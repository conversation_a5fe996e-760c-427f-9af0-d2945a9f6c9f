.container {
  margin-top: 24rpx;
  width: 100%;
  height: 100%;
  padding: 25rpx 24rpx 24rpx 20rpx;
  background-repeat: no-repeat;
  background-size: 100% auto;
}
.common-container {
  padding: 24rpx 24rpx 35rpx 24rpx;
}
/** 普卡会员 */
.common-container.theme-color-1 {
  background-image: url(https://resource.pagoda.com.cn/dsxcx/images/041791d9fa5b1a3b9b0e8adda946ab08.png);
  color: #F67248;
}
/** 银卡会员 */
.common-container.theme-color-2 {
  background-image: url(https://resource.pagoda.com.cn/dsxcx/images/2b89f3eaa52d5b5b8546526c27c93e27.png);
  color: #3882CC;
}
/** 金卡会员 */
.common-container.theme-color-3 {
  background-image: url(https://resource.pagoda.com.cn/dsxcx/images/afc7e637ac146e94ac1899a08db353d7.png);
  color: #F35909;
}
/** 钻卡会员 */
.common-container.theme-color-4 {
  background-image: url(https://resource.pagoda.com.cn/dsxcx/images/da0ab33531900bb3c3cf63d679bd0c37.png);
  color: #D1D0ED;
}
/** 绿卡会员 */
.common-container.theme-color-5 {
  background-image: url(https://resource.pagoda.com.cn/dsxcx/images/90528edacfa64d665b8f4a9fe1a7c3e9.png);
  color: #3BA62B;
}
/** 心享会员 */
.theme-color-vip {
  background-image: url(https://resource.pagoda.com.cn/dsxcx/images/d770ea46a2c62b7273956123958975c0.png);
  color: #FFDFB4;
}
.card-name .name, .card-name, .card-box .card-item, .card-name .button {
  display: flex;
  align-items: center;
}
.card-name {
  margin-bottom: 45rpx;
  justify-content: space-between;
}
.common-card-name {
  margin-bottom: 34rpx;
}
.card-name .name {
  font-size: 24rpx;
  height: 42rpx;
}
.card-name .common-name {
  margin-left: 5rpx;
}
.card-name .name image {
  width: 38rpx;
  height: 38rpx;
}
.card-name .name .title {
  margin: 0 13rpx 0 0;
  height: 42rpx;
  line-height: 42rpx;
  font-weight: bold;
  font-size: 32rpx;
}
.card-name .name .tips {
  width: 320rpx;
  height: 42rpx;
  line-height: 42rpx;
}
.card-name .button {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}
.card-name .button .arrow-right {
  margin: 0 6rpx 0 4rpx;
  width: 12rpx;
  height: 12rpx;
  transform: rotate(45deg);
}
.card-list {
  width: 100%;
  white-space: nowrap;
}
.card-list .card-box {
  width: 100%;
  overflow-x: scroll;
  overflow-y: hidden;
  display: flex;
  flex-direction: row;
}
.card-box .card-item {
  flex: 1;
}
.card-item .card-item-image {
  width: 64rpx;
  height: 64rpx;
}
.card-item .title-desc {
  margin-left: 10rpx;
}
.card-item .title {
  font-size: 28rpx;
  font-weight: bold;
}
.card-item .desc {
  font-size: 24rpx;
}
.container ::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}
.no-data-container {
  margin: 20rpx 0 24rpx 0;
  width: 702rpx;
  height: 284rpx;
}
.no-data-container image {
  width: 100%;
  height: 100%;
}