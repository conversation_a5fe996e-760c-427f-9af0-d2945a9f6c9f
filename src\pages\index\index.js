//index index.js
//获取应用实例
var app = getApp()
var commonObj = require('../../source/js/common').commonObj;
const locateMixin = require('../../mixins/locateMixin')
const taskService = require('../../utils/services/taskService')

const redDotTypes = [
  {type: 1, key: 'tryEat', tip: 'showTryEatTip'}
]
const act = require('../../utils/activity')
const sensors = require('../../utils/report/sensors')
const cartMinxins = require('../../mixins/cartMixin')
const util = require('../../utils/util.js')
const toTryEatPage = require('../../utils/services/toTryEatPage')
let { jumpH5Vip }  = require('../../utils/services/jumpBgxxVip');
import { tabBarStore, accountPageName } from '../../mixins/tabBarStore'
import { tickEntryMixins } from '../../mixins/ticketEntry'
const feedbackCommon = require.async('../../sourceSubPackage/feedback/index')
import { getTuringDeviceToken } from '../../service/userService'
import { defaultCustomerAvatar, defaultUnLoginAvatar } from '../../source/const/user';

const MY_SERVICE = {
  collectStore: {
    path: '/homeDelivery/pages/collectStore/index',
    sensors: {
      code: '*********',
      name: '收藏门店'
    }
  },
  selfService: {
    path: '/userB/pages/selfService/index',
    isNeedLogin: false
  },
  addressManage: {
    path: '/bgxxUser/pages/address/addressList/index?from=memberPage',
    sensors: {
      code: '*********',
      name: '地址管理'
    }
  },
  storeFeedback: {
    path: '/store/pages/storeFeedback/index',
    sensors: {
      code: '*********',
      name: '意见反馈'
    }
  },
  invoiceService: {
    path: '/userB/pages/invoice/invoiceService/index',
    sensors: {
      code: '*********',
      name: '点击发票服务'
    }
  },
  serviceHelp: {
    path: '/userB/pages/serviceHelp/index',
    sensors: {
      code: '*********',
      name: '自助服务'
    }
  },
  eduAuth: {
    path: '/userA/pages/eduAuth/main/index',
    sensors: {
      code: '*********',
      name: '学生有礼'
    }
  },
}

/**
 * 是否点击过小红点
 */
let isClickedCouponDot = false
Page({
  mixins: [ cartMinxins, locateMixin, tabBarStore(accountPageName), tickEntryMixins],
  data: {
    statusBarHeight: '0px',
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    defaultCustomerAvatar,  // 用户默认头像
    defaultUnLoginAvatar,  // 用户未登录默认头像
    isReady: false,
    balance: '', //果币余额；
    couponAmount: "", //优惠券数量
    integralBalance: '', //会员总积分
    detailShowFlag: false,
    hasPay: false, // 是否有待付款订单
    hasTake: false, // 是否有待自提订单
    userName: '',
    extraData: app.globalData.extraData,  // 跳转开卡小程序所需要的参数
    appId: app.globalData.appId,  // 跳转开卡小程序所需要的参数
    amount: 0,  // 红包总额
    integral: 0,  // 积分
    couponList: [], //  优惠券列表展示

    bannerList: [], // 轮播图列表
    notifyList: [], // 滚动消息通知列表
    notifyCurIndex: 0, // 滚动消息通知 当前显示索引
    taskList: [], // 任务列表
    specialGoodsList: [], // 好吃推荐列表
    modalVisible: false, // 对话框是否可见
    modalAnimationData: {}, // 对话框动画数据
    modalObj: {},
    swiperCurIndex: 0,
    scanCodeBuyToolVisible: false, // 扫码购 tool 是否可见 3 个
    orderSortList: [
      {
        orderIcon: 'https://resource.pagoda.com.cn/dsxcx/images/94a31b0d0b09440391e0f45ea0b200a4.png',
        label: '及时达',
        type: 'A',
        sensorsKey: 'customerCategoryOrder',
        count: 0,
        orderType: 10
      },
      {
        orderIcon: 'https://resource.pagoda.com.cn/dsxcx/images/5e2c5f147a6d5f440a07acf045cbc0cf.png',
        label: '次日达',
        type: 'E',
        sensorsKey: 'customerXxshopOrder',
        count: 0,
        orderType: 50
      },
      {
        orderIcon: 'https://resource.pagoda.com.cn/dsxcx/images/730e2fb7a2e76e232e6209d7a397dc44.png',
        label: '接龙',
        type: 'D',
        sensorsKey: 'customerRelayOrder',
        count: 0,
        orderType: 150
      },
      {
        orderIcon: 'https://resource.pagoda.com.cn/dsxcx/images/5f2a007b10584dcc52be573a04cd7885.png',
        label: '门店订单',
        type: 'C',
        sensorsKey: 'customerStoreOrder',
        count: 0
      },
      {
        orderIcon: 'https://resource.pagoda.com.cn/dsxcx/images/65d5e6642dc2ca707eea3c5abacf0cc2.png',
        label: '全国送',
        type: 'F',
        sensorsKey: 'customerNationalDeliveryOrder',
        count: 0,
        orderType: 130,
      },
      {
        orderIcon: 'https://resource.pagoda.com.cn/dsxcx/images/eca5caf165c674a171340b85321052eb.png',
        label: '评价送积分',
        type: 'P',
        sensorsKey: 'customerEvaluate',
        count: 0
      },
      {
        orderIcon: 'https://resource.pagoda.com.cn/dsxcx/images/898499894032d6596ad1c86c5091be9e.png',
        label: '订金',
        type: 'DING_JIN',
        sensorsKey: '',
        count: 0
      },
      {
        orderIcon: 'https://resource.pagoda.com.cn/dsxcx/images/ee6ee880c93e1d2e0eb7b0c66957fc09.png',
        label: '接收订单',
        type: 'GIFT',
        reportClick: true,
        sensorsKey: '1200_120007009',
        count: 0
      },
      {
        orderIcon: 'https://resource.pagoda.com.cn/dsxcx/images/da1bce11b7d5bcf87877ef8dc72ead57.png',
        label: '礼品卡',
        type: 'GIFTCRAD',
        sensorsKey: '',
        count: 0
      },
    ],
    isShowOrderBar: false, // 是否展示心享会员条幅（没有订单流传时展示）
    adSwiperCur: 0,     // 心享会员提示栏广告栏的轮播图当前下标
    superVipStatus: 'C',
    // 账户信息
    balanceInfo: {
      couponsAmount: 0,
      expireSoonNum: 0,
      integralAmount: 0,
      walletAmount: 0
    },
    vipStatusName: '加载中...', // 心享会员名
    // 是否点击过券包的小红点
    isClickedCouponDot: false,
    isShowFeedbackRedDot: false,
    showTaskMode: false //是否展示任务模块 2023年11月1号 00:00:00后不显示
  },
  _data : {
    urls : {
      '0': '/scancodeTobuy/pages/scan/index',
      '1': '/scancodeTobuy/pages/orderList/index'
    },
    tabBarCartIndex: 3,
    // 是否打开过次日达业务的个人中心页
    hasXmdxTabbar: false
  },
  onLoad: function() {
    this.setData({ statusBarHeight: `${(wx.getStorageSync('systemInfo') || { statusBarHeight: 0 }).statusBarHeight}px` })
    this.getBanner()
    getTuringDeviceToken()
  },
  onShow: function() {
    this.setData({
      isClickedCouponDot
    })
    this.refreshUserInfo()
    // 上报神策数据
    sensors.pageScreenView()
    // 更新红点
    // this.refreshRedDot()
    this.getOrderEvaluationTotal()
    this.getOrderCount()
    // this.getIsVegetablesOrder();
    wx.hideLoading()
    // 意见反馈红点
    this.checkfeedbackRodot()
  },
  // 定位完成触发的方法
  onLocateReady () {
    // 获取会员页的弹窗（新客礼包弹窗、广告弹窗等）
    this.getCurrPageDialog()
  },
  async checkfeedbackRodot() {
    const { setFeedbackList } = await feedbackCommon
    const { hasDot } = await setFeedbackList()
    this.setData({
      isShowFeedbackRedDot: hasDot,
    })
  },
  // 跳转修改个人信息页
  navigateModifyUserInfo: function() {
    //神策埋点
    sensors.track('MPClick', '1200_120000001')
    wx.reportAnalytics('vip_cardimgbtn');
    this.preventEvent();
    if (app.checkSignInsStatus()) {
      // 用户已登录
      wx.navigateTo({
        url: '/userB/pages/modifyUserInfo/index'
      })
    } else {
      app.signIn()
    }
  },
  // 跳转到查询优惠券页面
  navigateToCoupon: app.subProtocolValid('memberService', function() {
    this.preventEvent();
    if (app.checkSignInsStatus()) {
      // 用户已登录
      wx.reportAnalytics('coupon_click') // 首页-会员优惠券点击
      //神策埋点
      sensors.track('MPClick', '1200_120000005')
      this.showTicketSubscribe().then(() => wx.navigateTo({
        url: '/userA/pages/coupon/index',
      }))
      this.setData({
        isClickedCouponDot: true
      })
      isClickedCouponDot = true
    } else {
      app.signIn()
    }
  }),
  // 跳转到余额充值页面
  navigateToDeposit: app.subProtocolValid('memberService', function() {
    let that = this;
    that.preventEvent();
    if (app.checkSignInsStatus()) {
      // 用户已登录
      wx.reportAnalytics('balance_click') // 首页-账户余额·充值点击
      //神策埋点
      sensors.track('MPClick', '1200_120000006')
      wx.navigateTo({
        url: '/userA/pages/deposit/index',
      })
    } else {
      app.signIn()
    }
  }),
  // 跳转订单列表页
  navigateAllOrder: function(e) {
    let that = this;
    let id = e.currentTarget.dataset.id;
    let obj = JSON.stringify({
      orderStatus: e.currentTarget.dataset.orderstauts
    });
    var eventAnalytics = ['vip_allorder', 'vip_waitpay', 'vip_groupprocess', 'vip_waittake']
    that.preventEvent();
    if (app.checkSignInsStatus()) {
      // 用户已登录
      wx.reportAnalytics(eventAnalytics[id]) // 订单详情
      wx.navigateTo({
        url: '/fightGroups/pages/myAllOrder/index?indexObj=' + obj,
      })
    } else {
      app.signIn()
    }
  },
  // 新用户注册领取积分提醒，跳转兑吧
  navigateDuiba: app.subProtocolValid('memberService', function(e) {
    //from用来标识从哪里进入此方法，"0"标识从积分商城进入，"1"标识从更多好礼进入
    const from = e.currentTarget.dataset.from;
    //神策埋点
    this.duibaSensor(from);
    this.preventEvent();
    wx.navigateTo({
        url: '/h5/pages/duiba/index',
    })
  }),
  preventClick () {
    //
  },

  // 防止点击过快，导致页面重复跳转蒙层
  preventEvent: function() {
    this.setData({
      prevent: true
    });
    setTimeout(() => {
      this.setData({
        prevent: false
      });
    }, 400)
  },
  /**
   * @description - banner点击调整
   */
  onBannerTap: app.subProtocolValid('memberService', function(e) {
    const { item, index } = e.target.dataset
    //神策埋点
    if ( app.globalData.reportSensors ) {
      app.sensors.track('MPClick', {
        element_code: `12000900${index+1}`,
        element_name: `会员页广告位0${index + 1}`,
        element_content: '会员页广告位',
        screen_code: '1200',
        screen_name: '会员首页',
        banner_id: item.bannerID,
        banner_name: item.name,
      })
    }
    act.toActivityPage( item )
  }),
  // 获取订单评价总数
  getOrderEvaluationTotal () {
    const { userID } = wx.getStorageSync('user') || {}
    if (userID && userID !== -1) {
      const params = {
        customerID: userID
      }
      app.api.getNotEvaluatedOrderCount( params ).then(res => {
        const typeObj = res.data || {}
        let unCommentCount = 0
        Object.values(typeObj).forEach(number => unCommentCount += number)
        this.setData({
          'orderSortList[5].count': unCommentCount
        })
      })
    } else {
      this.setData({
        'orderSortList[5].count': 0
      })
    }
  },
  /**
   * 获取及时达（需要包括试吃订单）、次日达、全国送订单分别展示除交易完成和已取消状态外的其他状态的订单数量
   */
  async getOrderCount() {
    const { userID } = wx.getStorageSync('user') || {}
    if (!userID || userID === -1) {
      this.resetOrderCount()
      return
    }
    try {
      // 剔除门店订单和评价订单
      const orderCountList = this.data.orderSortList.filter(item => !!item.orderType)
      // 1.不同发货时间的全国送订单为混合单，增加混合单订单类型0 2.增加试吃订单类型90
      const orderType = [0, 90].concat(orderCountList.map(item => item.orderType))
      const params = {
        customerID: userID,
        orderType,
        orderStatus: [10, 20, 25, 30, 35, 40, 50, 60, 70, 80, 85],
      }
      const res = await app.api.getOrderStatusCount(params)
      if (!res || !res.data) {
        return
      }
      for (let index in orderCountList) {
        const orderType = orderCountList[index].orderType
        const idx = this.data.orderSortList.findIndex(item => item.orderType === orderType)
        const key = `orderSortList[${idx}].count`
        const { total } = res.data[orderType] || {}
        if (orderType === 10) {
          // 及时达增加试吃订单数量
          this.setData({
            [key]: (total || 0) + ((res.data['90'] || {}).total || 0)
          })
        } else {
          this.setData({
            [key]: total || 0
          })
        }
      }
    } catch (error) {}
  },
  /**
   * 重置订单数量
   */
  resetOrderCount() {
    const orderSortList = this.data.orderSortList
    orderSortList.filter(item => !!item.orderType).forEach(item => {
      const idx = orderSortList.findIndex(order => order.orderType === item.orderType)
      const key = `orderSortList[${idx}].count`
      this.setData({
        [key]: 0
      })
    })
  },
  // 神策上报点击事件
  trackClickEvent(params = {}) {
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', params)
    }
  },
  /**
   * @description - 获取滚动消息通知v2.4.1
   * @param {number} customerID - 用户id
   * @returns {void}
   */
  getNotifyMsg (customerID) {
    let vm = this
    let { storeID = -1 } = wx.getStorageSync('timelyCity') || {}
    let params = {
      customerID,
      storeID
    }
    app.api.getNotifyMsg(params).then((res) => {
      let { data } = res || {}
      this.setData({
        notifyList: data || [],
        notifyCurIndex: 0
      })
    }).catch(err => {})
  },
  /**
   * @description - 切换显示 滚动消息通知v2.4.1
   * @param {object} e - 原生组件对象
   * @returns {void}
   */
  onNotifychange (e) {
    let {current} = e.detail
    this.setData({
      notifyCurIndex: current
    })
  },
  /**
   * @description - 点击 滚动消息通知v2.4.1
   * @param {object} e - 原生组件对象
   * @returns {void}
   */
  onNotifyTap (e) {
    let { path, notifytype } = e.currentTarget.dataset
    //根据notifytype进行神策埋点
    this.notifySensor(notifytype);
    // 心享到期通知
    if (Number(notifytype) === 1) {
      jumpH5Vip()
    }
    // 积分失效通知、任务通知 等等
    if (!path) return
    wx.navigateTo({
      url: path
    })
  },
 /**
   * @description - 获取任务列表v2.4.1
   * @param {number} customerID - 用户id
   * @returns {void}
   */
  getTasks (customerID) {
    let { storeID = -1 } = wx.getStorageSync('timelyCity') || {}
    let params = {
      customerID,
      storeID
    }
    app.api.getTasks(params).then((res) => {
      let { data = [], systemTime = '' } = res
      this.setTaskModeStatus(systemTime)
      // 请求成功
      this.setData({
        taskList: data
      })
    }).catch(err => {
      this.setTaskModeStatus()
    })
  },
  /**
   * @description - 点击 单个任务v2.4.1
   * @param {object} e - 原生组件对象
   * @returns {void}
   */
  onTaskBtnTap: app.subProtocolValid('memberService', function(e) {
    const { status, path, type,index } = e.currentTarget.dataset;
    //神策埋点
    if(Number(index)===0)
    {
      sensors.track('MPClick', '1200_120004002')
    }else if(Number(index) ===1)
    {
      sensors.track('MPClick', '1200_120004003')
    }
    //根据任务状态和任务类型判断是否需要跳转小程序页面，若需要则跳转
    taskService.navigateToPage(status,type,path);
    //根据任务状态和任务类型判断是否需要跳转小程序tabbar页面，若需要则跳转
    taskService.navigateToTabBar(status,type,path);
    //根据任务状态和任务类型判断是否需要跳转其他小程序页面，若需要则跳转
    taskService.navigateToMiniProgram(status,type,path);
    //由于会员页跟任务中心页领取积分逻辑不同，在各自的页面完成不同逻辑
    //会员页：若该任务状态为领取积分，则跳转到任务中心页
    this.finishTask(status,type);
  }),
  //根据任务状态和上一个页面判断是领取积分操作还是跳转到任务中心的页面
  finishTask(status,type)
  {
    const tasks = [
      {status:'F', type:type}
    ]
    if(taskService.checkTaskNeedOperation(status,type,tasks))
    {
      //若from为0，则为会员页的完成任务状态，点击则跳转到任务中心页
      this.toTaskCenter();
    }
  },
   /**
 * @description - 跳转 任务中心v2.4.1
 * @param {object} e - 原生组件对象
 * @returns {void}
 */
  toTaskCenter: app.subProtocolValid('memberService', function(e) {
    //神策埋点
    sensors.track('MPClick', '1200_120004001')
    wx.navigateTo({
      url: '/userA/pages/taskCenter/index'
    })
  }),
  /**
   * @description - 获取banner广告图v2.4.1
   * @returns {void}
   */
  getBanner () {
    let user = wx.getStorageSync('user') || {}
    let timelyCity = wx.getStorageSync('timelyCity') || {}
    // 缺少城市
    if (!timelyCity || !timelyCity.cityID) return

    let params = {
      customerID: user.userID || -1,
      cityID: timelyCity.cityID,
      storeID: timelyCity.storeID || -1,
      pageSource: 'member'
    }
    app.api.getBannerList(params).then((res) => {
      let { data } = res
      // 请求成功
      data = data || []
      this.setData({
        bannerList: data,
        swiperCurIndex: 0
      })
    }).catch(err => {})
  },
  /**
   * @description - 跳转 H5差多少补多少页面v2.4.1
   * @returns {void}
   *  */
  toH5RefundInstruction () {
    wx.navigateTo({
      url: '/h5/pages/refundInstruction/index',
    })
  },
  // 打开对话框
  openModal() {
    this.setData({
      modalVisible: true
    })
    setTimeout(() => {
      let animation = wx.createAnimation({
        duration: 1000,
        timingFunction: 'ease'
      })
      animation.translateY( 0 ).step()
      this.setData({
        modalAnimationData: animation.export()
      })
    }, 300)
  },
  // 关闭对话框
  closeModal() {
    sensors.track('MPClick', '1200_120001002')
    setTimeout(() => {
      let animation = wx.createAnimation({
        duration: 1000,
        timingFunction: 'ease'
      })
      animation.translateY( 1200 ).step()
      this.setData({
        modalAnimationData: animation.export()
      })
    }, 300)
    setTimeout(() => {
      this.setData({
        modalVisible: false
      })
    }, 900)
  },

  getCurrPageDialog: util.throttle(async function() {
    // 优先弹新客礼包弹窗
    const hasCouponsLayer = await this.showNewUserCouponsLayer()
    // 没有新客礼包弹窗，则弹会员页弹窗
    if (!hasCouponsLayer) {
      this.getMemberPageDialog()
    }
  }, 5000),

  /**
   * 设置新客礼包弹窗
   * 用户已登录&&是电商新客才请求接口
   */
   async showNewUserCouponsLayer() {
    const { isEshopNewCustomer, userID: customerID } = wx.getStorageSync('user') || {}
    // 如果当前正在请求新客券，则不触发新请求（会员页/首页都会刷新）
    // 已经领取过，不再请求
    if (app.globalData.isRequestingEshopCoupons || app.globalData.isShowNewCouponDialog) {
      return false
    }
    if (!customerID || customerID === -1 || !isEshopNewCustomer) {
      return false
    }
    app.globalData.isRequestingEshopCoupons = true
    const data = await this.newUserCouponsFetchData(customerID)
    app.globalData.isRequestingEshopCoupons = false
    const { couponList = [] } = data || {}
    if (!!couponList.length) {
      this.setData({
        couponData: data
      })
      this.getMemberAccount(customerID) // 更新会员账户余额查询，积分，优惠券数量等
      commonObj.updatePopupShowTime() // 更新弹窗缓存时间
      app.globalData.isShowNewCouponDialog = true
      return true
    }
    app.globalData.isShowNewCouponDialog = true
    return false
  },

  /**
   * 仅获取数据：新客礼包
   */
   async newUserCouponsFetchData(customerID) {
    try {
      const res = await app.api.getEshopNewCustomerGift({ customerID }) || {}
      const data = res.data?.dialogCoupon || {}
      return data
    } catch (err) {
      console.log('newUserCouponsFetchData', err);
      return {}
    }
  },

  // 获取会员页弹窗
  getMemberPageDialog() {
    // 普通广告弹窗，4小时弹一次
    const isShowTime = commonObj.checkPopupIsShowTime(false)
    if(!isShowTime) return
    let { userID: customerID = -1 } = wx.getStorageSync('user') || {}
    let { cityName, cityID = -1, storeID = -1 } = wx.getStorageSync('timelyCity') || {}
    if ( !cityName ) {
      return
    }
    let params = {
      customerID,
      cityID,
      storeID
    }
    app.api.getMemberpageModal( params ).then( res => {
      // 第 2 种可能, 获取会员页弹窗数据, res.data = null, 运营管理台配置不显示会员页弹窗, 直接返回
      if( !res.data ) return
      let data = res.data
      this.setData({
        modalObj: data
      })
      commonObj.updatePopupShowTime()  // 更新弹窗缓存时间
      this.openModal()
    })
  },
  // 弹窗跳转
  modalNavigateTo(e) {
    //神策埋点
    sensors.track('MPClick', '1200_120001001')
    let { item } = e.currentTarget.dataset
    act.toActivityPage(item)
    this.closeModal()
  },

  navigateToH5Sensor(index){
    const bannerIndex = Number(index) + 1;
    let bIndex = '';
    const indexs = [
      {bannerIndex:1, bIndex:'一'},
      {bannerIndex:2, bIndex:'二'},
      {bannerIndex:3, bIndex:'三'},
      {bannerIndex:4, bIndex:'四'}
    ]
    indexs.some(item =>{
      if(item.bannerIndex === bannerIndex){
        bIndex = item.bIndex;
        return true;
      }
    });
    let name = `专区${bIndex}`
    let code = `12000500${Number(index) + 1}`;
    app.sensors.track('MPClick', {
      element_code: code,
      element_name: name,
      element_content: name,
      screen_code:'1800'
    })
  },
  // 活动页神策埋点
  topicPageSensor({ dataset: paramsDataset, currentTarget: { dataset: targetTarget } = {} }){
    const { sensorsKey } = paramsDataset || targetTarget || {}
    sensorsKey && sensors.track('MPClick', sensorsKey)
  },
  notifySensor(notifytype){
    if(notifytype === "1"){
      //如果消息通知类型为1
      sensors.track('MPClick', '1200_120003001')
    }else if(notifytype === "2"){
      //如果消息通知类型为2
      sensors.track('MPClick', '1200_120003002')
    }else if(notifytype === "3"){
      //如果消息通知类型为3
      sensors.track('MPClick', '1200_120003003')
    }
  },
  XxShopSensor(){
    sensors.track('MPClick', '1200_120000008')
  },
  duibaSensor(from){
    if(from === "0")
    {
      sensors.track('MPClick', '1200_120000004')
    }else if(from === "1"){
      sensors.track('MPClick', '1200_120005005')
    }
  },
  // 获取用户是否下过蔬菜订单
  getIsVegetablesOrder () {
    const { userID: customerID } = wx.getStorageSync('user') || {}
    // 未登录或退出的场景
    if ( !customerID ) {
      this.setData({ scanCodeBuyToolVisible: false })
      return
    }
    app.api.getIsVegetablesOrder({ customerID }).then( res => {
      const { data: scanCodeBuyToolVisible } = res || {}
      this.setData({ scanCodeBuyToolVisible })
    }).catch(() => { })
  },
  // 跳转扫码购 、扫码购订单 、周期购订单
  navigateTo ( e ) {
    const { index } = e.currentTarget.dataset
    const { urls } = this._data
    wx.navigateTo({
      url: urls[index]
    })
  },
  toOrderList(e) {
    // 点击上报神策
    sensors.track('MPClick', 'customerAllOrder')
    wx.navigateTo({
      url: '/userB/pages/orderList/index'
    })
  },

  /**
   * 订单tab点击
   */
  toOrderTab: app.subProtocolValid({ type: 'infoService', conditionFn(e) {
    const { type } = e.currentTarget.dataset

    //  只有 评价送积分 需要子协议拦截校验
    return ['P'].includes(type)
  }}, function(e) {
    const { type, sensorsKey, reportClick } = e.currentTarget.dataset
    // 点击上报神策
    sensorsKey && (
      reportClick
        ? sensors.clickReport(sensorsKey)
        : sensors.track('MPClick', sensorsKey)
    )
    if (type === 'GIFTCRAD') {
      if (!this.data.isLogin) {
        app.signIn()
        return
      }
      wx.navigateTo({
        url: '/giftCard/pages/home/<USER>'
      })
      return
    }

    wx.navigateTo({
      url: {
        DING_JIN: '/homeDelivery/pages/prepay/orderList/index',
        GIFT: '/userB/pages/giftOrderList/index',
        P: '/userB/pages/evaluation/myEvaluation/index'
      }[type] || ('/userB/pages/orderList/index?type=' + type),
    })
  }),
  // 展示心享会员条幅
  showOrderBar(e) {
    let { isShowOrderBar } = e.detail
    this.setData({
      isShowOrderBar
    })
  },
  // 遍历 redDotTypes更新红点
  refreshRedDot() {
    let nativeRedDot = wx.getStorageSync('nativeRedDot') || {}
    redDotTypes.forEach( ({ key, tip }) => {
      let { clickTip = true } =  nativeRedDot[key] || {}
      this.setData({
        [tip]: !clickTip
      })
    })
  },
  // 点击小图标后更新小红点
  updateRedDot (type) {
    let { key, tip } = redDotTypes.find( item => String(item.type) === String(type) )
    this.setData({
      [tip]: false
    })
    let nativeRedDot = wx.getStorageSync('nativeRedDot') || {}
    Object.assign(nativeRedDot[key], { clickTip: true })
    wx.setStorageSync('nativeRedDot', nativeRedDot)
  },
  // 切换Tab
  onTabItemTap() {
    let nativeRedDot = wx.getStorageSync('nativeRedDot') || {}
    Object.assign(nativeRedDot, { clickTab: true })
    wx.setStorageSync('nativeRedDot', nativeRedDot)
    wx.hideTabBarRedDotByName({
      name: accountPageName,
    })
  },
  // 跳转到 h5的试吃中心
  navigateToTryEat: app.subProtocolValid('infoService', function() {
    toTryEatPage.navigateTo({
      type: 1,
      complete: () => {
        this.topicPageSensor({ dataset: { sensorsKey: '1200_120000019' } })
      },
      callback: this.data.showTryEatTip ? this.updateRedDot(1) : null // 1:试吃活动
    })
  }),
  /**
   * 跳转拼团列表
   */
  navigateToGroupList() {
    wx.navigateTo({
      url: '/fightGroups/pages/fightGroups/index'
    })
  },
  forbidSwiperScroll() {},

  /**
   * 刷新用户信息
   */
  refreshUserInfo() {
    const userToken = wx.getStorageSync("token");
    const user = wx.getStorageSync('user');
    const { userID } = user || {}
    // 用户未登录
    if (!userToken || !userID) {
      this.setData({
        isLogin: false
      });
      this.resetUserInfo()
      return
    }
    this.setData({
      isLogin: true
    });
    // 登录用户获取并设置相应用户数据
    this.refreshUserNameAndImg()
    // 获取会员账户余额查询，积分，优惠券数量，会员等级
    this.getMemberAccount(userID)
    // 获取用户信息 用户等级、会员状态、
    this.getCustomerInfo(userID)
    // 获取滚动消息通知，获取任务列表
    // this.getNotifyMsg(userID)
    // this.getTasks(userID)
  },

  /**
   * 更新用户昵称和头像
   */
  refreshUserNameAndImg () {
    const { defaultCustomerAvatar } = this.data
    const { phoneNumber = '' } = wx.getStorageSync('user') || {};
    const { nickName = '', avatarUrl = '', isDefault } = wx.getStorageSync('userNameAndImg') || {}
    const phoneNum = phoneNumber.substring(0, 3) + '****' + phoneNumber.substring(7, 11)
    const userName = isDefault ? phoneNum : nickName || phoneNum
    // const imgUrl = isDefault ? defaultCustomerAvatar : avatarUrl || defaultCustomerAvatar
    this.setData({
      userName,
      imgUrl: avatarUrl || defaultCustomerAvatar
    })
  },

  /**
   * 重置用户信息
   */
  resetUserInfo () {
    const superVipStatus = 'C'
    this.updateNavColor(superVipStatus)
    this.setData({
      balanceInfo: {
        couponsAmount: 0,
        expireSoonNum: 0,
        integralAmount: 0,
        walletAmount: 0
      },
      userName: '',
      imgUrl: '',
      superVipStatus,
      couponPrice: null,
      notifyList: [], // 滚动消息通知列表
      taskList: [], // 任务列表
      specialGoodsList: [], // 好吃推荐列表
      bannerList: [],
      /**心享会员类型，H:尊享，YH:轻享，FH:飞享 */
      currentTypeTag:''
    })
  },

  /**
   * 会员账户余额查询，积分，优惠券数量，会员等级
   */
  getMemberAccount (userID) {
    const param = {
      customerID: userID,
      isNeedBalance: 'Y',
      isNeedIntegral: 'Y',
      isNeedCoupon: 'Y',
      isCountExpireSoon: 'Y',
      notOnlyApplicableChannels: [10000],
      showFruitGradeCouponCount: 'Y'
    }
    app.api.getUserAmount(param).then(({ data = {} }) => {
      const { walletAmount = 0, integralAmount = 0, couponsAmount = 0, expireSoonNum = 0 } = data
      const balanceInfo = {
        couponsAmount,
        expireSoonNum,
        integralAmount,
        walletAmount
      }
      this.setData({
        balanceInfo
      })
    })
  },
  updateNavColor(superVipStatus) {
    const color = { 'C': '#000000' }[superVipStatus] || '#ffffff'
    wx.setNavigationBarColor({
      frontColor: color,
      backgroundColor: color,
      animation: { duration: 300, timingFunc: 'linear' }
    })
  },
  /**
   * 获取用户信息
   */
  async getCustomerInfo (userId) {
    try {
      const { data } = await app.api.getMemberInfo({
        customerID: userId,
        optionalInfo: ['vip']
      })
      if (!data) {
        return
      }

      const {
        levelId,
        superVipStatus,
        certified,
      } = data

      let currentTypeTag = data.currentTypeTag || 'F'
      // 更新会员等级
      Object.assign(app.globalData, {
        superVipStatus
      })

      //  如果有试用心享会员的话
      if (superVipStatus === 'T') {
        currentTypeTag = superVipStatus
      }

      this.updateNavColor(superVipStatus)
      this.setData({
        currentTypeTag,
        isReady: true,
        levelId,
        superVipStatus,
        vipStatusName: { F: '心享会员', T: '试用心享会员' }[superVipStatus] || ''
      })
      // 将实名状态写入storage
      const user = wx.getStorageSync('user')
      Object.assign(user, {
        certified
      })
      wx.setStorageSync('user', user);
    } catch (error) {}
  },
  /**
   * 是否展示任务模块
   */
  setTaskModeStatus(systemTime = ''){
    const current = systemTime || new Date().getTime()
    const stopTime = new Date('2023-11-1 00:00:00').getTime()
    this.setData({
      showTaskMode: current < stopTime
    })
  },
  /**
   * 退出登陆
   */
  signOut () {
    app.signOut()
    this.resetUserInfo()
    this.refreshUserInfo()
    this.getBanner()
  },

  /**
   * 登录按钮点击
   */
  tapLoginBtn () {
    wx.reportAnalytics('vip_signin')
    app.signIn()
  },

  onPullDownRefresh() {
    wx.stopPullDownRefresh();
    this.refreshUserInfo();
    this.getBanner()
  },
  // 我的服务页跳转
  navigateToPage (e) {
    const { type } = e.currentTarget.dataset
    const { path, isNeedLogin = true, sensors } = MY_SERVICE[type]
    if (isNeedLogin && !app.checkSignInsStatus()) {
      app.signIn()
      return
    }
    wx.navigateTo({
      url: path,
    })
    if(sensors) {
      app.sensors.track('MPClick', {
        element_code: sensors.code,
        element_name: sensors.name,
        element_content: sensors.name,
        screen_code: '1200'
      })
    }
  }
})
