let app = getApp();
let coordtransform = require('../../../utils/coordUtil');
import { getStoreBusinessTime } from '../../../utils/util'
import { updateCollectStore } from '../../../service/userService'
Page({
  /**
   * 页面的初始数据
   */
  data: {
    storeID: "",
    storeName: "",
    distance: "",
    salesCount: "",
    openingTime: "",
    address: "",
    storePhone: "",
    credentialUrl: "",
    lon: "",
    lat: "",
    markers: [{
      iconPath: "../../source/images/icon_locationpoint_2x.png",
      id: 1,
      latitude: 23.099994,
      longitude: 113.324520,
      width: 50,
      height: 58,
      callout: {
        content: '导航到该店 GO',
        color: '#048e3f',
        fontSize: '28rpx',
        borderRadius: 10,
        borderColor:'#048e3f',
        borderWidth:'1rpx',
        display: 'ALWAYS',
        padding: '18rpx'
      },
      customCallout:{
        display:'ALWAYS',
        anchorY: 10,
        anchorX: 0,
      }
    }],
    fromTryEatStore: false
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let storeInfo = JSON.parse(decodeURIComponent(options.storeInfo))
    const {
      storeCode,
      distance,
      pageFromPath,
      hasActCode
    } = storeInfo
    this.getStoreDetail({storeCode: storeCode, isNeedScore: 'Y', isNeedSaleCount: 'Y'})
    this.setData({
      distance: distance,
      pageFromPath: pageFromPath,
      fromTryEatStore: pageFromPath === 'tryEatStore',
      hasActCode
    })
    // if (storeInfo.pageFromPath === "vegetables") {
    //   this.getStoreInfoById({storeId: storeInfo.storeID, isNeedScore: 'Y'})
    //   this.setData({ distance: storeInfo.distance })
    // } else {
    //   let {
    //     storeName,
    //     storePhone,
    //     phone,
    //     lon,
    //     lat,
    //     distance,
    //     salesCount,
    //     address,
    //     openingTime,
    //     credentialUrl,
    //     scoreNumber,
    //     pageFromPath,
    //     hasActCode,
    //     startTime='',
    //     endTime=''
    //   } = storeInfo
    //   const storeBusinessTime = getStoreBusinessTime({startTime,endTime,openingTime})
    //   this.setData({
    //     storeName,
    //     storePhone,
    //     phone,
    //     lon,
    //     lat,
    //     distance,
    //     salesCount,
    //     address,
    //     openingTime,
    //     credentialUrl,
    //     scoreNumber,
    //     'markers[0].longitude':lon,
    //     'markers[0].latitude':lat,
    //     isIphoneX: app.globalData.isIphoneX,
    //     fromTryEatStore: pageFromPath === 'tryEatStore',
    //     hasActCode: hasActCode || false,
    //     storeBusinessTime
    //   })
    // }

  },
    /**
   * 门店id获取门店详情
   * @param {object} params
   */
  async getStoreDetail(params) {
    try {
      let { userID } = wx.getStorageSync("user") || {}
      params.customerID = userID
      const res = await app.api.getStoreDetail(params)
      const {shortName, name, phone, lon, lat, salesCount, address, openingTime, credentialUrl,startTime='',endTime='', scoreNumber = '', isUserCollection, storeCode } = res.data
      // 接口返回的百度地图坐标转换成腾讯地图坐标
      const txLocation = coordtransform.bd09togcj02(lon, lat)
      const storeBusinessTime = getStoreBusinessTime({startTime,endTime,openingTime})
      this.setData({
        storeName: shortName || name,
        phone,
        salesCount,
        address,
        openingTime,
        credentialUrl,
        lon: txLocation[0],
        lat: txLocation[1],
        'markers[0].longitude': txLocation[0],
        'markers[0].latitude': txLocation[1],
        storeBusinessTime,
        scoreNumber,
        isUserCollection,
        storeCode
      })
    } catch (error) {}
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // this.nearbyStore()
  },
  // 点击浮标导航
  markertap() {
    let that = this,
      lat = that.data.lat,
      lon = that.data.lon,
      name = that.data.storeName,
      address = that.data.address;
    wx.openLocation({
      latitude: lat,
      longitude: lon,
      name: name,
      address: address
    })
  },
  // 门店资质页
  toStoreCredentials(e) {
    let jumpUrl = this.data.credentialUrl;
    wx.navigateTo({
      url: '/h5/pages/activityTemp/index?url=' + encodeURIComponent(jumpUrl)
    })
  },
  // 拨打门店电话
  callStore() {
    wx.makePhoneCall({
      phoneNumber: this.data.storePhone||this.data.phone  //仅为示例，并非真实的电话号码
    })
  },
  // 点击确认返回
  goBack() {
    wx.navigateBack({
      delta: 1
    })
  },
  async collectStore() {

    if (!app.checkSignInsStatus()) {
      app.signIn()
      return
    }
    const { isUserCollection, storeCode } = this.data
    const param = {
      storeCode: storeCode,
      type: isUserCollection === 'Y'? 0: 1
    }
    const result = await updateCollectStore(param)
    if (result) {
      this.setData({
        isUserCollection: isUserCollection === 'Y' ? 'N' : 'Y'
      })
    }
  }
})
