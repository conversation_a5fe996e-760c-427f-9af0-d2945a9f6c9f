const { helpCounpon: helpCounponApi } = require('../../api/activity.api.js');
import { throttle } from '../../../utils/util'
const detailMixin = require('../../mixins/details');
const navBar = require('../../mixins/navBar');
import { getObjectValue } from '../../../utils/util';
import styleConfig from '../../../utils/goodsStyleConfig'
const commonObj = require('../../../source/js/common').commonObj
import { inviterRules } from '../../source/js/rule';
import sensors from '../../../utils/report/sensors'
const statusList = [1,2,3] //进行中，领取成功，领取失败
const app = getApp();
const processList = [
  {
    icon:
      'https://resource.pagoda.com.cn/group1/M21/5D/79/CmiLkGE6xCeAcWwnAAABX8oOnwg768.png',
    text1: '选择',
    text2: '优惠券',
  },
  {
    icon:
      'https://resource.pagoda.com.cn/group1/M21/5D/18/CmiWa2E6xHKAay3qAAADGPC6WII590.png',
    text1: '邀好友',
    text2: '助力',
  },
  {
    icon:
      'https://resource.pagoda.com.cn/group1/M21/5D/79/CmiLkGE6xISAHRuQAAAD-myVdPQ505.png',
    text1: '人满',
    text2: '得券',
  },
  {
    icon:
      'https://resource.pagoda.com.cn/group1/M21/5D/79/CmiLkGE6xJSAfu3UAAACgQ83A1g096.png',
    text1: '下单',
    text2: '可用',
  },
];
const templateId = ['fG90Tsww_pb3k7nPZnjw1MrRZEaHvh6zqytXo8-rHp8','VmfCVxDyOFcE10fTju2UUNoLcHyI462EyTb4ieBH7gs']; //消息推送模板id
Page({
  mixins: [detailMixin, navBar],
  /**
   * 页面的初始数据
   */
  data: {
    styleConfig: styleConfig,
    bg_detail_top:
      'https://resource.pagoda.com.cn/group1/M21/5D/FB/CmiLkGE-wXGAJtnUAAPIPqMrqKc995.png',
    processList,
    tabIndex: 1,
    loading: true,
    hidePage: false,
    forward: false, //是否展示分享button
    isEligible: false, //用户省份是否符合条件
    actSuccess: false, //活动邀请人数是否足够
    actSubtitle: '', //活动状态描述
    checkRow: {}, //用户校验信息
    acceptPush: false, //用户是否选择了总是接受推送
    isStart: true, //活动已经开始助力
    startFailDesc: '活动开启失败！', //活动开启失败信息
    actStatus: '', //活动状态
    couponSendSuccess:false, //活动完成后发券是否成功
    buttonStatus:''//错误时候显示的按钮
  },
  async onLoad(options) {

    const { code = '', status = -1, ic = '' } = JSON.parse(
      options.pageParam.replace(new RegExp(/\\/g), '')
    );
    // console.log('解析后的详情-----', JSON.parse(
    //   options.pageParam.replace(new RegExp(/\\/g), '')
    // ));
    const { top = 26, height = 32 } = wx.getMenuButtonBoundingClientRect() || {}
    this.setData({
      activityCode: code,
      actStatus: status,
      addTop:top+height+12
    });
    this._data.inviteCode = ic;
    // 隐藏右上角分享按钮
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline'],
    });
  },
  onShow() {
    // 更新数据
    this.setDataOrUpdate()
  },
  /**
   * @description 初始化或者更新数据
   * */
   async setDataOrUpdate(){
    //检查是否登录
    this.checkAuthStatus();
    // 重置数据
    this.resetData()
    // (活动未开始才校验用户身份)
    const isCheckStatus = statusList.indexOf(Number(this.data.actStatus))===-1
    if(isCheckStatus){
      //检查用户是否可以助力
      await this.checkInviterAuth();
    }else{
      this.setData({
        isEligible: true,
        checkRow:{status:true}
      });
    }
    //开始助力活动 (活动未开始&&用户身份合法)
    if(isCheckStatus&&this.data.isEligible){
      await this.startInvite()
    }
    //获取详情
    await this.getActDeatil();
    //获取助力人列表
    await this.getInviteList();
    // 获取过期时间
    await this.getExpireTime()
    //检查用户是否接受消息推送
    await this.checkAuthAcceptPush();
    if(this.data.actSuccess){
      // 查询用户是否领券成功
      await this.checkGetCouponStatus();
    }
    //设置显示按钮和文案
    this.setPaperwork();
    //适用商品列表
    if(this.data.couponType !== 'A'){
      this.selectCategoryOrXxshop({ reset: true });
    }else{
      this.setData({
        tabIndex:2
      })
      // 请求更多活动列表
      this.getActRecordList();
    }
    const { code = '',  name = ''  } = this.data.actInfo
    sensors.pageScreenView({
      activityID: code,
      activityName: name
    })
  },
  /**
   * @description 重置数据
   * */
  resetData(){
    // 重置商品列表参数
    Object.assign(this._data.requestParams, {
      pageNum: 0,
      isRequesting: false,
    });
    this.setData({
      tabIndex:1,
      forward: false, //是否展示分享button
      isEligible: false, //用户省份是否符合条件
      actSuccess: false, //活动邀请人数是否足够
      actSubtitle: '', //活动状态描述
      checkRow: {}, //用户校验信息
      acceptPush: false, //用户是否选择了总是接受推送
      isStart: true, //活动已经开始助力
      startFailDesc: '活动开启失败！', //活动开启失败信息
      actList:[], //更多活动列表
      inviteList:[],//邀请人列表
      hasNextPage: true,//商品列表是否有下一页
      goodsList: [],//商品列表
    })
  },
  /**
   * @description 检查用户是否接受消息推送
   * */
  checkAuthAcceptPush() {
    let that = this;
    return new Promise((reslove,reject)=>{
      wx.getSetting({
        withSubscriptions: true,
        success(res) {
          // console.log("消息推送",res)
          const templateIdAct = getObjectValue(
            res,
            `subscriptionsSetting.itemSettings.${templateId[0]}`,
            'reject'
          );
          const templateIdDate = getObjectValue(
            res,
            `subscriptionsSetting.itemSettings.${templateId[1]}`,
            'reject'
          );
          that.setData({
            acceptPush: templateIdAct === 'accept' && templateIdDate === 'accept',
          });
          reslove('')
        },
        fail(){
          reject('')
        }
      });
    })

  },
  /**
   * @description 开始助力活动
   * */
  async startInvite() {
    try {
      const { userID = -1 } = wx.getStorageSync('user') || {};
      const { cityCode = -1 } = wx.getStorageSync('timelyCity') || {};
      const params = {
        customerID: String(userID),
        activityCode: this.data.activityCode,
        channel: '10001',
        regionCode: cityCode,
      };
      const { data = '' } = await helpCounponApi.startInvite(params);
      this._data.inviteCode = data;
      this.setData({
        actStatus:1,
        isStart: true,
        startFailDesc:''
      });
    } catch (error) {
      const { description = '' } = error;
      this.setData({
        isStart: false,
        startFailDesc: description,
      });

    }
  },
  /**
   * @description 检查用户是否有权限发起助力活动
   * */
  async checkInviterAuth() {
    try {
      const { userID = -1 } = wx.getStorageSync('user') || {};
      const { cityCode = -1 } = wx.getStorageSync('timelyCity') || {};
      const params = {
        customerID: String(userID),
        activityCode: this.data.activityCode,
        channel: '10001',
        regionCode: cityCode,
      };
      const {
        data = false,
        errorCode = 0,
        description = '',
      } = await helpCounponApi.checkInviterAuth(params);
      this.setData({
        isEligible: true,
        checkRow: { status: data, description, errorCode },
      });
    } catch (error) {

      const { description = '', errorCode = 0 } = error;
      this.setData({
        isEligible: false,
        checkRow: { status: false, description, errorCode },
      });
    }
  },
  //自定义左上角返回
  back() {
    const pages = getCurrentPages();
    const lastPage = pages[pages.length - 2] || '';
    if (lastPage) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/homeDelivery/index',
      });
    }
  },
  //添加购物车
  changeCount() {},
  // 转发时间
  optionSubscribeMessage(){
    // if(this.data.acceptPush) return
    const tmplIdArr = templateId;
    app.requestSubscribeMessage(
      {
        tmplIds: tmplIdArr,
      },
      () => {}
    );
    // 埋点
    const { code = '',  name = ''  } = this.data.actInfo
    sensors.track('MPClick', 'helpCouponDetailPage',{
      activityID: code,
      activityName: name
    })
  },
  /**
   * @description 转发
   * */
  onShareAppMessage() {
    const { avatarUrl= '' } = wx.getStorageSync('userNameAndImg') || {}
    const { userID = -1 } = wx.getStorageSync('user') || {};
    const params = {
      title: this.data.shareText||`帮我点一点,你也能领${this.data.actInfo.name||'券'}`,
      path:
        `/pages/homeDelivery/index?to=helpCouponDetail&pageParam=` +
        JSON.stringify({
          code: this.data.activityCode,
          ic: this._data.inviteCode,
          mid: userID,
          avatar: encodeURIComponent(avatarUrl),
        }),
      imageUrl: this.data.shareImg,
    };
    console.log('分享信息----', params);
    return params;
  },
  /**
   * @description 满足转发条件之前进行的操作
   * */
  clickForwardButton:throttle(function() {
    const { status = false, description = '',errorCode = 0 } = this.data.checkRow;
    // 检查用户状态
    if (!status) {
      const { toast = description } = inviterRules[Number(errorCode)] || {}
      wx.showToast({
        title: toast,
        icon: 'none',
        duration: 2000,
      });
      return;
    }
    // 检查活动是否开启
    if (!this.data.isStart) {
      wx.showToast({
        title: this.data.startFailDesc,
        icon: 'none',
        duration: 2000,
      });
      return;
    }
  },2000),
  /**
   * @description 获取助力人列表
   * @param { String } activityCode 活动编码
   * @param { String } customerID 会员id
   * */
  async getInviteList() {
    try {
      const { userID = -1 } = wx.getStorageSync('user') || {};
      const params = {
        customerID: String(userID),
        inviterId:String(userID),
        activityCode: this.data.activityCode,
        invitationCode: this._data.inviteCode
      };
      const { data = [] } = await helpCounponApi.getInvitees(params);
      const peopleNum =
        Number(this.data.actInfo.thresholdNum) - Number(data.length);

      this.setData({
        inviteList: data,
        peopleNum,
        actSuccess: peopleNum <= 0, // 如果邀请人数>需要人数
      });
    } catch (error) {
    }
  },
  /**
   * @description 设置按钮和文案信息
   * */
  setPaperwork() {

    const { status, errorCode = 0, description = '' } = this.data.checkRow;
    const isEligible = status; // 用户信息是否符合
    const actOngoing = this.data.actOngoing; //活动是否进行中(链接有效时间)
    const actSuccess = this.data.actSuccess; //助力人数是否已满
    const isStart = this.data.isStart; //活动是否已经开始
    const couponSendSuccess = this.data.couponSendSuccess; //是否发券成功
    const settingActStatus = this.data.settingActStatus;//活动是否开启(活动有效时间)
    const actStatus = this.data.actStatus;//活动状态(链接有效时间)
    const page = this.data.page; //来源页面
    //活动标题描述 默认
    let actDesc = `再找${this.data.peopleNum}位好友点一下，立即到账`;
    //活动已经结束
    !actOngoing && (actDesc = '已超时');
    // 开始活动校验报错
    !isStart && (actDesc = this.data.startFailDesc);
    // 用户校验信息报错显示校验信息
    if(errorCode !== 0){
      const { title = description} = inviterRules[Number(errorCode)] || {}
      actDesc = title
    }
    //活动完成 领到券
    if(actSuccess && couponSendSuccess){
      actDesc = '领取成功';
    }
    //活动完成 没领到券
    if(actSuccess && !couponSendSuccess){
      actDesc = '已完成';
    }

    //副标题 默认
    let actSubtitle = '';
    //活动已经结束
    !actOngoing && (actSubtitle = '好可惜，差一点就成功啦');
    // 用户校验信息报错显示校验信息
    if(errorCode !== 0){
      const { subTitle = ''} = inviterRules[Number(errorCode)] || {}
      actSubtitle = subTitle
    }
    //活动完成 领到券
    if(actSuccess && couponSendSuccess){
      actSubtitle = '优惠券已发至您的账户'
    }
    //活动完成 没领到券
    if(actSuccess && !couponSendSuccess){
      actSubtitle = '领取失败 活动太火爆了，优惠券已被抢光 详情可联系客服：400-181-1212';
    }

    // 按钮显示errorButton
    let buttonStatus = ''
    //(活动已经结束&还未成功)||活动开始失败||用户身份不符合||活动完成&未领到券-->
    if((!actOngoing&&!actSuccess)||!isStart||(!isEligible&&(Number(errorCode)!==30017||Number(errorCode)!==30018))||(actSuccess&&!couponSendSuccess)){
      buttonStatus = 'other'
    }
    if(!isEligible&&(Number(errorCode)===30017||Number(errorCode)===30018)){
      buttonStatus = 'record'
    }
    this.setData({
      loading: false,
      hidePage: true,
      buttonStatus,
      actDesc, //活动描述文案
      actSubtitle, //副标题 错误提示
      forward: isEligible && isStart, //用户身份符合&&活动已经开始&&接受消息推送
    });
    // 活动结束 管理台关闭等一系列操作跳转主页在此
    //活动进行中之后 不处理管理台关闭活动的操作
    if(statusList.indexOf(Number(actStatus))!==-1||actSuccess)return
    // 如果是从助力详情过来的活动开启或者不在进行中失败会跳主页
    if(!settingActStatus){
      this.setData({
        actDesc:'活动已结束',
        actSubtitle:'看看其他活动吧'
      })
      commonObj.showModal('提示', `活动已结束，看看其他活动吧~`, false, '我知道了', '', function (res) {
        if (res.confirm) {
          wx.reLaunch({
            url: '/activity/pages/helpCoupon/index',
          })
        }
      })
    }
  },
  // 去使用优惠券
  usePopup:throttle(function() {
    // let params = this.data.couponDetail
    // params.deliveryMode = this.data.deliveryMode
    // params.couponCode = this.data.couponCode
    // this.toUseCoupon(params)
    this.toUseCoupon()
  },2000),
  /**
   * @description 查询用户是否领券成功
   * @param { * }
   * */
  async checkGetCouponStatus() {
    try {
      const { userID = -1 } = wx.getStorageSync('user') || {};
      const params = {
        customerID: String(userID),
        invitationCode:this._data.inviteCode
      };
      const { data=[] } = await helpCounponApi.checkGetCouponStatus(params);
      this.setData({
        couponSendSuccess:data[0]&&data[0].personalCodes.length>0,
        // couponDetail:data[0]&&data[0].personalCodes.length>0?data[0].couponDetail:{},
        couponCode:data[0]&&data[0].personalCodes.length>0?data[0]&&data[0].personalCodes[0]:''
      })
    } catch (error) {
      this.setData({
        couponSendSuccess:false
      })
    }
  },
  // 跳转查看心享会员
  toRecord(){
    wx.navigateTo({
      url:'/activity/pages/helpRecord/index'
    })
  }

});
