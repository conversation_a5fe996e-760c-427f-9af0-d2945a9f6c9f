/* homeDelivery/pages/goodsDetail/index.wxss */
@import "templates/index";
@import "./skeleton/index.skeleton.wxss";
.contain{
  display: flex;
  flex-direction: column;
  height: 100rpx;
  background-color: #F5F5F5;
  z-index: 1
}
/* 商品轮播图 start */
.swiper-box,
swiper,
.video-item-box,
swiper-item {
  width:100%;
  height:750rpx;
  display: flex;
  flex-direction:column;
  justify-content: center;
  align-items: center;
}
.swiper-box {
  position: relative;
}
.sellout-box {
  position: absolute;
  left:0;
  right: 0;
  top:0;
  bottom: 0;
  pointer-events: none;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  background: rgba(255, 255, 255, 0.5);
}
.sellout-box__content{
  width: 150rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: rgba(34, 34, 34, 0.7);
  border-radius: 48rpx;
  color: #FFFFFF;
  font-size: 34rpx;
  text-align: center;
}
.video-item-box video, .video-image-box, .video-cover-box, .video-image {
  width: 100%;
  height: 100%;
}
.video-item-box .video-image-play {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.slide-image {
  width: 100%;
  height: 100%;
}
.carousel-index-box {
  position:absolute;
  width: 80rpx;
  height: 44rpx;
  border-radius: 25rpx;
  background-color:#929292;
  opacity: 0.6;
  bottom: 25rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}
.index-text {
  font-size: 28rpx;
  color: #ffffff;
}
/* 商品轮播图 end */

.group-wrapper {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.group-wrapper:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
/* 商品属性label */
.group-wrapper .group-title {
  width: 68rpx;
  line-height: 1.4;
  font-size: 24rpx;
  font-weight: bold;
  color: #222;
  flex-shrink: 0;
}
/*评价数量*/
.group-wrapper .group-title .num {
  color: #888;
}
.group-wrapper.group-extra-style {
  padding: 20rpx 24rpx 16rpx 24rpx;
  overflow: hidden;
}
.group-extra-style .group-title {
  width: auto;
  font-weight: 600;
}
.extra-evaluation  {
  border-bottom: 0;
}
.evaluation-summary {
  display: flex;
  justify-content: space-between;
  padding: 0 0 10rpx 0;
  /* border-bottom: 1rpx solid #f5f5f5; */
}
.goods-evaluation-item {
  margin-top: 26rpx;
}
.goods-evaluation-item-no-ai{
  --evaluation-item-bg: transparent;
  --evaluation-item-padding: 0rpx;
}
.extra-evaluation .btn-evaluation-detail {
  margin: 20rpx auto 30rpx;
}
.summary-right-wrap{
  display: flex;
  align-items: center;
}
.check-all{
  color: #888;
  font-size: 24rpx;
  font-weight: 400;
}
.fav-rate {
  color: rgba(0,140,60,1);
  font-size: 28rpx;
  font-weight: 400;
  margin-right: 8rpx;
}
.arrow {
  width: 20rpx;
  height: 20rpx;
}
.main-content-wrap {
  margin-left: 0;
}
.evaluation-content {
  margin-top: 15rpx;
}
.evaluation-img-wrap {
  margin: 10rpx 0 16rpx;
}
.group-wrapper .arrow-right {
  width: 20rpx;
  height: 20rpx;
  padding-left: 32rpx;
  flex-shrink: 0;
}
.goods-info{
  background-color: #fff;
  padding: 0 24rpx 20rpx 24rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
}
.goods-title{
  margin-right: 24rpx;
  flex: 1;
}
.goods-title{
  margin-right: 24rpx;
  font-size: 36rpx;
  line-height: 50rpx;
  color: #222;
  flex: 1;
}
.goods-title-wrap {
  display: flex;
  justify-content: space-between;
}
.goods-service-tips{
  margin-top: 10rpx;
  display: flex;
  align-items: center;
  border-radius: 8rpx;
  /* background: #F9F9F9; */
  font-size: 24rpx;
  color: #555555;
}
.goods-rank{
  margin-top: 10rpx;
}
.goods-service-tips-label{
  margin-right: 12rpx;
  width: 74rpx;
  height: 28rpx;
}
.goods-service-tips-content{
  margin-right: 1rpx;
  color: #91715D;
}
.goods-service-tips-content .no-point{
  display: none;
}

.goods-service-tips .service-tips-icon{
  width: 22rpx;
  height: 22rpx;
  color: #888;
}

.goods-level {
  --goods-level-margin: 8rpx;
  --goods-level-line-height: 30rpx;
  --m-goods-level-font-size: 26rpx;
  --m-goods-level-padding: 12rpx;
  vertical-align: middle;
  line-height: var(--goods-level-line-height);
}
.display-y-center {
  /* max-width: 538rpx; */
  font-size: 36rpx;
  line-height: 50rpx;
  font-weight: bold;
  color: #222;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal !important;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.goods-name{
  vertical-align: middle;
}
.goods-integra-wrap {
  padding: 10rpx 34rpx 10rpx;
  text-align: center;
  border-left: 2rpx dotted #dcdcdc;
}
.goods-subtitle{
  margin-top: 8rpx;
  font-size: 26rpx;
  line-height: 37rpx;
  color: #888888;
  overflow: hidden;
  /*超出2行显示省略号*/
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical
}
.goods-notice-info {
  margin-top: 8rpx;
  display: flex;
  align-items: center;
}
.b2c-goods-delivery-time {
  padding: 0 8rpx;
  height: 34rpx;
  font-size: 20rpx;
  line-height: 34rpx;
  color: #13AD56;
  background-color: #E6FEF0;
  border-radius: 8rpx;
}
.goods-integra{font-size: 40rpx;color: #FF7387;}
.goods-instruction{font-size: 20rpx;color: #888;}
/*卡片部分*/
.goods-detail-card {
  background-color: #F5F5F5;
  padding: 20rpx 16rpx 16rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.store-goods-priceInfo{
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
}
.goods-priceInfo{
  overflow: hidden;
}
.label-list{
  margin-bottom: 8rpx;
}
.group-invite-tips{
  margin-top: 15rpx;
}
.fix-price{position: fixed;top: 0;z-index: 1111;width: 100%;border-bottom:1rpx solid #f5f5f5;}
.price-1{color: #FF7D8C;font-weight: 600;display: flex;flex-direction: row;align-items: baseline;margin-right: 20rpx;}
.price-2{color: #888;font-size: 20rpx;text-decoration: line-through;margin-left: 8rpx;font-weight: 400;}
.f1{font-size:28rpx;margin-right: 3rpx;}
.f2{font-size:48rpx;}
.f3{font-size:34rpx;}
.price-info{font-size: 18rpx;color:#FF3753;background-color:rgba(255,55,83, 0.05);padding: 0 3rpx;}
.goods-detail{
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 24rpx;
}
.goods-detail .detail {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  margin-bottom: 2rpx;
  padding: 0 3rpx;
  font-size: 26rpx;
  line-height: 33rpx;
  text-align: left;
}
.goods-detail .detail:first-child .detail-1 {
  border-top-left-radius: 12rpx;
}
.goods-detail .detail:first-child .detail-2 {
  border-top-right-radius: 12rpx;
}
.goods-detail .detail:last-child .detail-1 {
  border-bottom-left-radius: 12rpx;
}
.goods-detail .detail:last-child .detail-2 {
  border-bottom-right-radius: 12rpx;
}
.goods-detail .detail:last-child {
  margin-bottom: 0;
}
.detail-1{
  width: 153rpx;
  padding: 14rpx 0;
  color: #888;
  padding-left: 25rpx;
  flex-shrink:0;
  background: #F5F5F5;
}
.detail-2{
  flex: 1;
  margin-left: 2rpx;
  padding: 14rpx 0 14rpx 25rpx;
  color: #222;
  background: #FAFAFA;
  font-weight: 400;
}
.c-img-box{margin-top: 20rpx;}
.c-img-box .pic-w{display: flex;flex-direction: column;}
.c-img-box {
  width: 100%;
  margin: 10rpx auto 0;
}
.icon-detailImg {
  width: 100%;
  margin: 0 auto;
}
.flex {display: flex; align-items: center;}
.flex1 {flex: 1;}
.mask {position: fixed;bottom: 0;left: 0;width: 100%;/* height: 100vh; 有蒙层，无height属性则无蒙层 */background-color: rgba(0, 0, 0, 0.5);}
.mask-true {height: 100vh; /* 有蒙层，无height属性则无蒙层 */ z-index: 1111;}

/* 品牌墙 */
.brandWall-box {
  margin: 0;
  width: 100%;
  font-size: 0;
}
.brandWall-pic {
  display: block;
  width: 100%;
  height: 100%;
}

.btn {
  color: #fff;
  background-color: #008C3C;
  height: 88rpx;
  width: 576rpx;
  line-height: 88rpx;
  font-size: 30rpx;
  border-radius: 100rpx;
  text-align: center;
  font-weight: 600;
}

.btn-group{
  position: fixed;
  right: 24rpx;
  top: 35rpx;
  z-index: 10;
}
.icon-home{
  margin-left: 40rpx;
  width: 56rpx;
  height: 56rpx;
}
.share_selectBox{
  background-color:#fff;
  min-height:316rpx;
  width:100%;
  position:fixed;
  bottom:-158px;
  z-index: 98;
  /* bottom:-316rpx; */
  /* transform: translateY(158) */
}
.deleteBtn{
  /* height:96rpx; */
  line-height: 96rpx;
  text-align: center;
  width:100%;
  font-size: 24rpx;
}
.shareBox{
  height:220rpx;
  padding:50rpx 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.shareIcon{
  height:120rpx;
  display: flex;
  text-align: center
}
.model_bottom{
  position: fixed;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 97;
}

.icon_group{
  position: relative;
  flex: 1;
}
.icon_group image{
  width:80rpx;
  height:80rpx;
}
.share-txt{
  font-size: 24rpx;
  color:#333;
}
.wx_friend{
  border-right:1rpx solid #f5f5f5;
}
.btn_wrapper{
  position: relative;
}
.wx_friend_loading{
  position: absolute;
  z-index: 10;
  width:80rpx;
  height:80rpx;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}
.wx_friend_loading image{
  animation: wxFriendLoading 0.5s linear infinite;
}
@keyframes wxFriendLoading{
  0%{
    transform: rotate(0deg);
  }
  50%{
    transform: rotate(180deg);
  }
  100%{
    transform: rotate(360deg);
  }
}
.btn_none{
  background-color:#fff;
  border-radius: 0;
  border:0;
  line-height:1.2;
}
.btn_none::after{
  border:none;
  border-radius: 0;
}
/* 海报预览模态框 */
.model_top {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  top: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 99;
}
.model-body{
  width:100%;
  position: fixed;
  bottom:0;
  padding:70rpx 0 40rpx;
  background-color: #FFFFFF;
  z-index:100
}
.container{
  text-align: center;
  display: flex;
  flex-direction: column;
}
.top-box{
  flex:1;
  box-shadow: 0rpx 0rpx 25rpx 1rpx rgba(0, 0, 0, 0.06);
  border-radius: 5rpx;
  margin-bottom: 45rpx;
}
.close_btn{
  padding: 10rpx;
  width: 30rpx;
  height: 30rpx;
  position: absolute;
  top: 26rpx;
  right: 26rpx;
}
.save_photo{
  width:520rpx;
  height:90rpx;
  line-height:90rpx;
  text-align:center;
  background-color:#008C3C;
  color:#fff;
  font-size:34rpx;
  border-radius:50rpx;
  margin-bottom:5rpx;
}
.share-tips{
  color:#666;
  font-size: 28rpx;
}
.myCanvas{
  position: absolute;
  z-index: 1111;
  left:-10000px;
  top:-10000px;
}

.vipIcon{
  width:64rpx;
  height:24rpx;
  margin-left:3rpx;
}
.lf{
  width:150rpx;
  text-align: center;
  font-size:26rpx;
  font-weight: 600;
}

.mid{
  font-size:24rpx;
  line-height: 36rpx;
  display: flex;
  flex:1;
  flex-direction: column;
  justify-content: center;
}
.saveMoney{
  width:94rpx;
  height:100rpx;
  background-color:#3D3938;
  border-top-right-radius:12rpx;
  border-bottom-right-radius:12rpx;
  color:#E8CC9A;
  font-size:24rpx;
  text-align:center;
  letter-spacing:1rpx;
  display:flex;
  justify-content:center;
  align-items:center;
}

.crownIcon{
  width:34rpx;
  height:28rpx;
  margin:0 16rpx;
}
.vipPrice{
  display: flex;
  font-size:24rpx;
  font-weight:600;
  color:#555;
  margin-left:8rpx;
}
.vipPrice .vipIcon{
  position:relative;
  bottom:-7rpx;
}

.symbol {
  margin:0 -0.15em;
}

.cardSymbol {
  margin-right: -0.45em;
}


/* 划线价格提示 */
.price-tips-box {
  padding: 40rpx 24rpx 24rpx;
  background: #fff;
}
.price-tips-title {
  height: 40rpx;
  line-height: 40rpx;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
}
.price-tips-content {
  line-height: 40rpx;
  font-size: 24rpx;
  color: #666;
}
.bottom-navbar {
  width: 100%;
  height: 110rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  background-color: #FFF;
  box-shadow: 0rpx 8rpx 30rpx 0rpx rgba(192, 195, 216, 0.5);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: content-box;
  transition: display .3s;
  z-index: 2;
}
.bottom-navbar.hide {
  display: none;
}
.bottom-navbar-cartcount,
.bottom-navbar-share {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}
.bottom-navbar-share {
  margin-left: 24rpx;
}
.bottom-navbar-cartcount {
  margin-left: 66rpx;
}
.navbar-cartcount-icon,
.navbar-share-icon {
  width: 54rpx;
  height: 54rpx;
  vertical-align: middle;
}
.navbar-cartcount-text,
.navbar-share-text {
 font-size: 22rpx;
 color: #222222;
}
.navbar-cart-count {
  min-width: 30rpx;
  width: auto;
  height: 30rpx;
  line-height: 30rpx;
  border-radius: 50%;
  font-size: 20rpx;
  color: #FFF;
  background: #F64742;
  position: absolute;
  top: -2rpx;
  right: 0;
  text-align: center;
}
.navbar-cart-count.bigger-cart-count {
  width: auto;
  height: 32rpx;
  padding: 0 6rpx;
  line-height: 32rpx;
  border-radius: 16rpx;
}
.bottom-navbar-addcart {
  display: flex;
  justify-content: flex-end;
  margin: 15rpx 18rpx 15rpx 0;
  flex: 1;
}
.add-to-cart {
  width: 404rpx;
  height: 80rpx;
  font-size: 32rpx;
  line-height: 80rpx;
  text-align: center;
  font-weight: bold;
  color: #FFFFFF;
  background: #00A34F;
  border-radius: 55rpx;
}
.navbar-addcart-showcount {
  background-color: #FFF;
}

/* 配送、服务、活动 */
.goods-tips-group {
  padding: 24rpx;
  margin-bottom: 16rpx;
}
.goods-tips-group .tips-item {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
}
.goods-tips-group .tips-item:last-child {
  margin-bottom: 0;
}
.goods-activity-title {
  align-self: flex-start;
}
/* 配送信息 */
.delivery-time-tips {
  display: flex;
  align-items: center;
}
/*及时达图标*/
.delivery-time-label {
  padding: 0 8rpx;
  /* height: 28rpx; */
  line-height: 30rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  background-color: #00A34F;
  background-image: linear-gradient(135deg, #65D667 0%, #3AC86E 100%);
  border-radius: 4rpx;
  flex-shrink: 0;
  font-weight: 500;
  margin-right: 6rpx;
}
.delivery-time-desc {
  font-size: 24rpx;
  color: #222;
  font-weight: 400;
}

/* 活动 */
.goods-activity-content {
  margin-top: -2rpx;
}
.goods-activity-item {
  display: flex;
  align-items: center;
}
.goods-activity-item:not(:first-child) {
  margin-top: 18rpx;
}
.goods-activity-label {
  display: flex;
  align-items: center;
  padding: 0 10rpx;
  height: 30rpx;
  font-size: 20rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.goods-activity-desc {
  font-size: 24rpx;
  line-height: 36rpx;
  color: #222222;
}

/* 规格选择栏 start */
.sku-selector{
  padding: 18rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #222222;
}
.sku-selector-content{
  line-height: 40rpx;
}
/* 规格选择栏 end */
.slogan-bar{
  width: 750rpx;
  height: 70rpx;
  font-size: 26rpx;
  color: #00A34F;
  display: flex;
  align-items: center;
  background: rgba(0, 163, 79, 0.12);
}
.national-fruit-store-icon{
  width: 152rpx;
  height: 32rpx;
  padding-left: 24rpx;
}
.tips{
  padding-left: 16rpx;
}

.b2c-delivery-box {
  position: fixed;
  left: 24rpx;
  bottom: 189rpx;
  width: 702rpx;
  height: 80rpx;
  background: rgba(54, 54, 54, 0.8);
  border-radius: 16rpx;
  color: #fff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx 0 21rpx;
}

.b2c-delivery-box .change-address-btn {
  width: 120rpx;
  height: 50rpx;
  background: #00A34F;
  border-radius: 8rpx;
  line-height: 50rpx;
  text-align: center;
}

.cart-animation{
  width: 40rpx;
  height: 40rpx;
  position: fixed;
  border-radius: 50%;
  overflow: hidden;
  display: none;
  left: 50%;
  top: 50%;
  z-index: 999;
  border: 1px solid rgba(0, 116, 255, 0.61);
  background: rgba(120, 188, 255, 0.2);
}

.cart-animation image {
  display: block;
  width: 100%;
  height: 100%;
}

/* 评论图片样式 */
.preview-img {
  margin: 0 8rpx 0 0;
  width: 130rpx !important;
  height: 130rpx !important;
  border-radius: 8rpx !important;
  box-sizing: border-box;
  overflow: hidden !important;
}
.preview-img:last-child {
  margin-right: 0 !important;
}
.preview-box {
  width: 100% !important;
}

.contain .goods-detail-info {
  overflow: hidden;
}

.contain .goods-detail-info.goods-detail-info--more {
  margin-bottom: 124rpx;
}
.gap {
  height: 1rpx;
}
.evaluation-summary .group-title {
  font-size: 32rpx;
}
.evaluation-goodsdetail{
  border-radius: 16rpx;
  overflow: hidden;
  width: 100%;
  font-size: 24rpx;
  color: #222;
}
.goods-detail-info .group-title {
  padding: 20rpx 24rpx 0 24rpx;
  width: auto;
  font-weight: bold;
  font-size: 32rpx;
  color: #222;
}
.evaluation-summary .group-title {
  color: #222;
}
/* 服务提示条 start */
.report-tips{
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  margin-top: 12rpx;
}
.report-service-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8rpx;
  border: 1rpx solid #888888;
  height: 48rpx;
  padding: 0 10rpx;
  line-height: 48rpx;
}
.report-service-bar-list {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
}
.report-service-bar-list .pop{
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
}
/*左侧区域*/
.report-service-bar-list .report-service-bar-left {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  font-size: 24rpx;
  color: #000000;
  flex: 1;
}
/*右侧区域*/
.report-service-bar-list .report-service-bar-right {
  width: 20rpx;
  height: auto;
}
/* 渐变效果 */
.report-service-bar-list .report-service-bar-right::before {
  content: '';
  position: absolute;
  right: 13rpx;
  top: -4rpx;
  width: 40rpx;
  height: 56rpx;
  background-image: linear-gradient(270deg, #FFFFFF 8%, #FFFFFF 52%);
  filter: blur(8rpx);
}
.report-service-bar-img {
  width: 88rpx;
  height: 30rpx;
  margin-right: 12rpx;
}
/*包裹右侧服务的元素*/
.report-service-bar-items {
  flex: 1;
  display: flex;
  flex-flow: wrap;
}
.report-service-bar-item {
  position: relative;
  margin-right: 8rpx;
}

.inspection-report{
  border-radius: 8rpx;
  display: flex;
  align-items: center;
}
.report-icon{
  width: 34rpx;
  height: auto;
  margin-right: 6rpx;
}
.report-font{
  color: #222222;
  font-size: 26rpx;
  white-space: nowrap;
}
.report-right{
  width: 20rpx;
  height: 20rpx;
}
/* 服务提示条 end */

.placeholder{
  position: absolute;
  height: 750rpx;
  width: 100%;
  z-index: 10;
}

.activityPriceNew-row {
  max-height: 142rpx;
  padding: 20rpx 23rpx;
  background-image: url(https://resource.pagoda.com.cn/dsxcx/images/d89d4ed3b1b0ae0a493da83717bec20d.png);
  background-size: 100% 100%;
}

.activityPriceNew-price {
  display: flex;
  color: #fff;
  align-items: baseline;
}

.price-symbol {
  font-size: 22rpx;
  color: #FFFFFF;
}

.price-amount {
  font-size: 46rpx;
  color: #FFFFFF;
  opacity: .8;
}

.new-price-label-wrap {
  width: 140rpx;
  height: 28rpx;
  display: inline-flex;
  margin-left: 9rpx;
  position: relative;
}

.new-price-label {
  position: absolute;
  top: -2rpx;
  display: inline-flex;
  border: 1rpx solid #FFFFFF;
  border-radius: 4rpx;
  font-size: 18rpx;
  color: #fff;
  align-items: center;
  background-color: #ff563b;
  overflow: hidden;
  line-height: 28rpx;
  white-space: nowrap;
}

.new-price-label > view:first-child {
  background-color: #fff;
  color: #FF0000;
  padding: 0rpx 10rpx;
  border-radius: 0 4rpx 4rpx 0;
}

.new-price-label > view:last-child {
  background-color: #ff563b;
  color: #fff;
  border-radius: 0 4rpx 4rpx 0;
  padding: 0rpx 10rpx;
}

.retailPrice-amount {
  height: 0;
  font-size: 24rpx;
  color: #FFFFFF;
  text-decoration-line: line-through;
  margin-left: 9rpx;
  transform: translateY(-4px);
}

.activityPriceNew-label-list {
  display: flex;
  margin-top: 14rpx;
}

.label-item {
  border: 1rpx solid #FFFFFF;
  border-radius: 4rpx;
  font-size: 18rpx;
  color: #FFFFFF;
  line-height: 26rpx;
  padding: 2rpx 8rpx;
  display: inline-block;
  margin-right: 8rpx;
  transform: rotateZ(360deg)
}
