<!-- pages/homeDelivery/components/metro/index.wxml -->
<wxs module='common' src="../../../../utils/common.wxs"></wxs>
<block>
  <view class="metro" wx:if="{{categoryMetro.length || functionMetro.length}}">
    <!-- 品类metro -->
    <view class="metroList">
      <view wx:for="{{categoryMetro}}" wx:key="index" id="{{item.openValue === 'allCate' ? 'allCate' : ''}}" class="metroItem" bindtap="navigateToCate" data-index="{{index}}" data-value="{{item.openValue}}" data-id="{{item.bannerID}}" data-name="{{item.name}}">
        <view class="itemPic">
          <view class="metroLabelBox" wx:if="{{item.labelCopy}}">
            <view class="metroLabelText">{{item.labelCopy}}</view>
          </view>
          <image class="itemPic-image" src="{{common.getImageUrl(picDomain, item.picUrl)}}" />
        </view>
        <view class="itemTitle">{{item.name}}</view>
      </view>
    </view>
    <!-- 品类metro -->

    <!-- 功能metro -->
    <view class="metroList {{ categoryMetro.length ? 'funcMetroList' : '' }}">
      <view
        wx:for="{{functionMetro}}"
        wx:key="index"
        style="{{ item.margin ? item.margin : '' }}"
        class="metroItem"
        bindtap="navigateToPage"
        data-index="{{index}}"
        data-mini="{{item.miniProgram}}"
        data-type="{{item.openType}}"
        data-value="{{item.openValue}}"
        data-id="{{item.bannerID}}"
        data-name="{{item.name}}">
        <view style="{{ item.size ? item.size : '' }}" class="itemPic funcMetro">
          <image class="itemPic-image" src="{{item.localPic ? item.localPic : common.getImageUrl(picDomain, item.picUrl)}}" />
        </view>
        <view class="itemTitle">{{item.name}}</view>
      </view>
    </view>
    <!-- 功能metro -->
  </view>
</block>
