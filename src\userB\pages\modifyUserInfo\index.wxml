<!--index.wxml-->
<view class="main">
	<!-- 上半部分容器 -->
	<view class="info-header-container">
		<!-- 头像 -->
		<view class="info-item">
		   <text class="info-item-label">头像</text>
		   <view class="info-item-value item-avatar">
         <image class="avatar-img" src="{{avatarUrl || defaultCustomerAvatar}}" />
				  <image src='/source/images/arrow-right-gary.png'></image>
          <button class="item-avatar-btn" open-type="chooseAvatar" bindchooseavatar="chooseavatar"></button>
		   </view>
		</view>
		<!-- 账号 -->
		<view class="info-item">
			<text class="info-item-label">昵称</text>
			<view class="info-item-value" bind:tap="toUpdateNickName">
				{{nickName}}
        <text style="color:#BFBFBF;" wx:if="{{!nickName}}">请输入您的昵称</text>
				<image src='/source/images/arrow-right-gary.png'></image>
			</view>
		</view>
	</view>

	<!-- 中部分容器 -->
	<view class="info-mid-container">
		<!-- 姓名 -->
		<!-- <view class="info-item">
			<text class="info-item-label">{{isIdentified ?'昵称':'实名认证'}}</text> -->
			<!-- 需要分多种情况考虑 1.未设置展示手机号 点击起输入框
			2.设置但未实名 点击跳输入框
			3.实名后不能跳输入框 -->
			<!-- <view class="{{ isIdentified ?'info-item-value':'info-item-value margin-right' }}">
				<view class="info-item-value">
				<input disabled="{{ isIdentified }}" class="name_input" type="text" placeholder="请输入" placeholder-style="color: #999999;font-size: 28rpx;" value="{{nickName}}" maxlength="12" bindinput="keyNameInput" />
				</view>
				<image wx:if="{{ !isIdentified }}" src='/source/images/arrow-right-gary.png'></image>
			</view>
		</view> -->
		<!-- 性别 -->
    <view class="info-item">
			<text class="info-item-label">我的账号</text>
			<view class="info-item-value">{{name}}</view>
		</view>
		<!-- 生日 -->
		<view class="info-item">
			<text class="info-item-label">我的生日</text>
			<view class="info-item-value" catchtap='selectBirthday'>
				<picker mode="date" value="{{birthday}}" start="1930-01-01" id="datePicker" disabled="{{disabledPicker}}" end="{{currentDate}}" bindchange="bindDateChange">
					<view>
						<view style="font-size:28rpx;">{{birthday}}</view>
					</view>
				</picker>
				<image src='/source/images/arrow-right-gary.png'></image>
			</view>
		</view>

    <view class="info-item">
			<text class="info-item-label">我的等级</text>
			<view class="info-item-value" catchtap='tapMemberInfo' data-urlType="13">
				{{levelName}}
				<image src='/source/images/arrow-right-gary.png'></image>
			</view>
		</view>
	</view>

	<!-- 下部分容器 只有一个我的等级 -->
	<!-- 我的等级 -->
	<view class="info-bot-container">
		<!-- <view class="info-item">
			<text class="info-item-label">我的等级</text>
			<view class="info-item-value" catchtap='tapMemberInfo' data-urlType="13">
				{{levelName}}
				<image src='/source/images/arrow-right-gary.png'></image>
			</view>
		</view> -->
	</view>
	<view class="{{saveBtnFlag?'btn-submit':'btn-submit cant-save'}}" bindtap="modifyUserInfo">保存</view>
</view>
<!-- 引导层 -->
<view class="mask" wx:if="{{taskFlag}}">
	<view class="guide_pic"><image src="../../source/images/guide_mask.png"></image></view>
	<!-- <view class="guide_word">完善此四项信息,即可领取积分喲~</view>	 -->
    <view class="know_pic" bindtap="cancelTask"><image src="../../source/images/i_know.png"></image></view>
</view>

<pagoda-popup
  visible="{{showPopup}}"
  round
  position="center"
  clickOverlayClose="{{false}}"
  bind:onBack="popupClose"
  width="600rpx"
  zIndex="1000"
>
  <view class="popup-content">
    <view class="popup-protocol">
      我们收集您的出生日期信息主要用于以下目的:为您在生日当天提供特别优惠和定制服务。我们将依据
      <text class="tips-link" bindtap="bindTap">《百果园用户隐私政策》</text>
      管理您的个人信息。
    </view>
    <picker mode="date" value="{{birthday}}" start="1930-01-01" end="{{currentDate}}" bindchange="bindDateChange">
      <view>
        <view class="popup-btn" bindtap="handleAgree">同意</view>
      </view>
    </picker>
    <view class="popup-btn-cancel" catch:tap="handleDisagree">不同意</view>
  </view>
</pagoda-popup>

<common-loading />
