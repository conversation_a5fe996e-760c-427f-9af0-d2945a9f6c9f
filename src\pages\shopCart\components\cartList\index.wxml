<view class="list">
  <view class="list__main">
    <!-- 吸顶区域 -->
    <view class="list__top">
      <!-- 填充间隔 -->
      <view class="list__top-fill"></view>
      <!-- 填充间隔 -->

      <!-- 头部区域 -->
      <view class="list__header">
        <view class="list__header-left">
          <!-- 单复选按钮 -->
          <radio-check checked="{{ isChecked }}" disabled="{{ goodsAmount === 0 }}" bindtap="checkChange" />
          <!-- 单复选按钮 -->
          <view style="color: {{ titleColor }}" class="header-title">{{ title }}</view>
          <view class="header-desc">{{ desc }}</view>
        </view>

        <view class="list__header-right">
          <view wx:if="{{ couponAmount }}" class="right-coupon" bindtap="clickCoupon">卡券({{ couponAmount }})</view>
          <view class="right-clear" bindtap="clickClear">清空</view>
        </view>
      </view>
      <!-- 头部区域 -->

      <!-- 凑单助手插槽 -->
      <view>
        <slot name="fixed"></slot>
      </view>
      <!-- 凑单助手插槽 -->
    </view>
    <!-- 吸顶区域 -->

    <view class="list__content {{signleMoudle ? 'list__content--radius' : ''}} {{ isChecked ? '' : 'list__content--repaint' }}">
      <!-- 主内容插槽 -->
      <slot></slot>
      <!-- 主内容插槽 -->

      <!-- 售罄区域 -->
      <view wx:if="{{ invalidAmount }}">
        <!-- 首个售罄商品插槽 -->
        <slot wx:if="{{ goodsAmount === 0 }}" name="first-invalid"></slot>
        <!-- 首个售罄商品插槽 -->

        <!-- 售罄商品操作区域 -->
        <view wx:if="{{ goodsAmount === 0 ? invalidAmount > 1 : invalidAmount >= 1 }}" class="invalid__toggle" bindtap="toggleShowInvalidGoods">
          <!-- 删除按钮 -->
          <view wx:if="{{ isShowInvalidGoodsList }}" class="invalid__clear" bindtap="clearInvalid">删除失效商品</view>
          <!-- 删除按钮 -->

          <!-- 操作按钮 -->
          <view class="invalid__amount">
            <block wx:if="{{ isShowInvalidGoodsList }}">点击收起</block>
            <block wx:else>{{ goodsAmount === 0 ? invalidAmount - 1 : invalidAmount }}份失效商品</block>
            <text class="direction {{ isShowInvalidGoodsList ? '' : 'direction--down' }}"></text>
          </view>
          <!-- 操作按钮 -->
        </view>
        <!-- 售罄商品操作区域 -->

        <!-- 售罄列表插槽 -->
        <slot wx:if="{{ isShowInvalidGoodsList }}" name="invalid"></slot>
        <!-- 售罄列表插槽 -->
      </view>
      <!-- 售罄区域 -->
    </view>
  </view>

  <!-- 结算栏插槽 -->
  <view class="list__submit {{isCartPage}} {{(isCartPage ? signleMoudle : popupSignleMoudle) ? 'list__submit--hide' : ''}}">
    <slot name="submit"></slot>
  </view>
  <!-- 结算栏插槽 -->
</view>

<!-- 确认模态对话框 -->
<confirm-modal
  isShowConfirmModal="{{ isShowClearModal }}"
  cancelText="取消"
  confirmText="确认"
  bindconfirm="clearConfirm">
  <view slot="content">{{ clearModalContent }}</view>
</confirm-modal>
<!-- 确认模态对话框 -->
