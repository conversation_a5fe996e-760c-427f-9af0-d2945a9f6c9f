import wxappMap from '../../../../service/wxappMap'
import coordtransform from '../../../../utils/coordUtil'
const { prdUrl } = require('../../../../utils/config')
const locateService = require('../../../../utils/services/locate')
const { LOCATE_STORAGE_ENUM } = locateService
import locateStore from '../../../../stores/module/locate'

const mapIconInfo = { // 地图控制点size
    width: 33,
    height: 38
}
const app = getApp()
Page({
    data: {
        controls: [],
        searchStr: '',
        cityName: '深圳市',
        mapLocation: null,
        poisList: [],
        searchList: [],
        loadSearch: false,
        noFoundImage: `${prdUrl.PAGODA_PIC_DOMAIN}/group1/M21/60/96/CmiWa2FVZd6AccnjAAAhYRqjPOo862.png`, // 公共缺省图
        addressfromWx:false
    },
    _data: {
        isClearInput: false
    },
    onLoad({
        locationInfo = null,
        addAddressInfo,
        useScene = '',
        addressfromWx = false
    }) {
        this.useScene = useScene
        Object.assign(this, JSON.parse(addAddressInfo))
        this.mapCtx = wx.createMapContext('addressMap')
        this.setLocation(JSON.parse(locationInfo))
        this.setData({addressfromWx})
    },
    /**
     * @desc 是否展示自定义的【获取位置信息提示】弹窗
     * @returns { Boolean } true-继续执行后续 false-中断执行后续
     */
    async checkShowCustomAuthModal () {
        const hasShown = wx.getStorageSync(LOCATE_STORAGE_ENUM.GET_LOCATE_TIP_SHOWED)
        // 没展示过
        if (!hasShown) {
            const result = await getApp().golbalModalPromise({
                titleText: '提示',
                contentText: '我们需要获取您的位置信息，为您展示附近的门店及商品信息',
                showCancel: true,
                cancelText: '手动输入',
                confirmText: '授权定位',
                isShowConfirmModal: true,
                isAsync: true,
                showCloseIcon: true
            })
            wx.setStorageSync(LOCATE_STORAGE_ENUM.GET_LOCATE_TIP_SHOWED, true)
            return result === 'confirm'
        }
        // 展示过，需要判断是否进行过授权
        const hasAuth = await locateStore.checkHasAuth()
        return hasAuth === true
    },
    async setLocation(locationInfo) {
        if (!locationInfo) {
            const continueRun = await this.checkShowCustomAuthModal()
            if (!continueRun) return
            const that = this
            // 定位当前位置
            wx.getLocation({
                type: 'gcj02',
                success(res) {
                    const {
                        latitude,
                        longitude
                    } = res
                    that.setMapLocation({
                        latitude,
                        longitude
                    })
                },
                fail() {
                    wx.showToast({
                        title: '定位当前位置失败',
                        icon: 'none'
                    });
                }
            })
        } else {
            // 当前编辑的地址
            this.setMapLocation(locationInfo)
        }
    },
    setMapLocation(location) {
        this.setData({
            mapLocation: location
        }, () => {
            this.setMapControls()
            this.centerLocation = JSON.parse(JSON.stringify(location))
            this.getNearAddress(this.centerLocation)
        })
    },
    /**
     * 设置地图控制点
     */
    setMapControls() {
        const selectQuery = wx.createSelectorQuery();
        selectQuery.select("#addressMap").boundingClientRect();
        selectQuery.exec((res) => {
            const {
                width: iconWidth,
                height: iconHeight
            } = mapIconInfo
            this.setData({
                controls: [{
                    id: 1,
                    iconPath: '../../../source/images/map_icon_location.png',
                    position: {
                        left: (res[0].width - iconWidth) / 2,
                        top: (res[0].height - iconHeight) / 2,
                        width: iconWidth,
                        height: iconHeight
                    }
                }]
            })
        });
    },
    async getNearAddress({
        latitude,
        longitude
    }) {
      try {
        const res = await wxappMap.reverseGeocoder({
          lat: latitude,
          lon: longitude,
          get_poi: 1,
          poi_options: 'policy=2',
        })
        this.dealNearResult(res)
      } catch(err) {
        wx.showToast({
          title: '获取附近地址失败',
          icon: 'none'
        });
      }
    },
    dealNearResult(res) {
        const {
            status = -1, result = null
        } = res || {}
        if (status === 0 && !!result) {
            const {
                pois = [], ad_info: {
                    city = ''
                } = {}
            } = result
            // 将初始当前传入定位的地址放在列表首位
            if (pois.length > 0) {
                let index = -1
                if (this.mapCityID !== -1) {
                    index = pois.findIndex(item => item.id === this.mapCityID)
                } else if (!!this.gisAddress) {
                    index = pois.findIndex(item => this.gisAddress.indexOf(item.title) > -1)
                }
                if (index > 0) {
                    const item = pois.splice(index, 1)[0]
                    pois.unshift(item)
                }
            }
            this.setData({
                cityName: city,
                poisList: pois
            })
        } else {
            this.setData({
                poisList: []
            })
        }
    },
    regionChange(res) {
        if (res.type === "end") {
            !!this.addressTimer && clearTimeout(this.addressTimer)
            const that = this
            this.addressTimer = setTimeout(() => {
                this.mapCtx.getCenterLocation({
                    success: function (res) {
                        const {
                            latitude,
                            longitude
                        } = res
                        that.centerLocation = {
                            latitude,
                            longitude
                        }
                        that.getNearAddress(that.centerLocation)
                    }
                })
            }, 200)
        }
    },
    inputSearch(e) {
        if (this._data.isClearInput) {
            return
        }
        this.setData({
            searchStr: e.detail.value,
        });
        if (!e.detail.value) {
            this.setData({
                searchList: []
            })
            this.refreshMapLocation()
        } else {
            this.searchRequest()
        }
    },
    bindFocus () {
        this._data.isClearInput = false
    },
    async searchRequest() {
        const that = this
        const {
            cityName,
            searchList
        } = this.data
        this.setData({
            loadSearch: !!searchList.length
        });
        !!this.addressTimer && clearTimeout(this.addressTimer)
        this.addressTimer = setTimeout( async () => {
          const res = await wxappMap.getSuggestion({
            keyword: this.data.searchStr,
            region: cityName,
            policy: 1,
          })
          const {
            data = []
          } = res || {}
          that.setData({
            loadSearch: true,
            searchList: data
          })
        }, 400)
    },
    clearSearch() {
        this.setData({
            searchStr: '',
            searchList: []
        })
        this._data.isClearInput = true
        this.refreshMapLocation()
    },
    refreshMapLocation() {
        this.addressTimer = setTimeout(() => {
            this.setData({
                mapLocation: this.centerLocation
            })
        }, 1000)
    },
    chooseCity() {
        wx.navigateTo({
            url: `/bgxxShop/pages/cityList/index?path=bgxxLocationAddress&cityId=${this.cityId || -1}`,
        })
    },
    changeCity(cityInfo) {
        const {
            name,
            location: {
                latitude,
                longitude
            },
            cityId
        } = cityInfo
        // 百度坐标转化为腾讯坐标
        const info = coordtransform.bd09togcj02(longitude, latitude)
            this.centerLocation = {
                latitude: info[1],
                longitude: info[0]
            }

        // apple真机上改变地图mapLocation时不会立马触发regionChange事件，所以延迟加载
        const time = !!this.data.searchStr ? 0 : 200
        this.addressTimer = setTimeout(() => {
            this.setData({
                cityName: name,
                mapLocation: this.centerLocation,
            })
            this.cityId = cityId
            if (!!this.data.searchStr) {
                this.searchRequest()
            }
        }, time);
    },
    async changeAddress(e) {
        const item = e.currentTarget.dataset.item
        const { location: { lat, lng } } = item
        // 腾讯坐标转化为百度坐标
        const info = coordtransform.gcj02tobd09(lng, lat)
        const checkParams = {
            cityName: this.data.cityName,
            lat: info[1],
            lon: info[0]
        }
        try {
            const isBGXX = this.useScene === 'bgxxConfirmOrder'
            const res = isBGXX ? await app.api.checkBgxxIsSupportVip(checkParams, { isLoading: true }) : await app.api.checkCity(checkParams)
            const resData = res.data || {}
            const {
              storeList = []
            } = resData
            const city = locateService.getLocateCity(resData)
            const {
              cityCode = '',
              code = '',
              deliveryCenterCode = '',
              cityID = '',
              cityName = '',
              name = '',
              id = ''
            } = city
            const cityInfo = { lon: info[0], lat: info[1], cityID: (cityID || id) || -1, cityName: (cityName || name) || '', storeID: -1, cityCode: code || cityCode, deliveryCenterCode  }
            if (isBGXX) {
              const { supportSuperVipShop = '' } = resData
                if (supportSuperVipShop === 'Y' && storeList.length > 0) {
                    const store = storeList.find(item => item.isSupportVipDelivery === 'Y')
                    if (store) {
                        this.backAddress(item, Object.assign(cityInfo, { storeID: store.storeID, storeName: store.storeName }))
                    } else {
                        this.noStoreTip()
                    }
                } else {
                    this.noStoreTip()
                }
            } else if (['homeDelivery', 'homeConfirmOrder', 'memberPage'].includes(this.useScene)) {
                // 这里调用locateService.storeInfo2City只是为了移除门店的cityInfo字段
                // 城市信息还是使用定位信息,即上面locateService.getLocateCity返回值
                const { store } = locateService.storeInfo2City(storeList[0] || null, city)
                if (store) {
                    store.storePhone = store.phone
                    Object.assign(cityInfo, { storeID: (store.storeID || store.id) || -1, storeInfo: store })
                }
                Object.assign(cityInfo, { supportBToCService: city.supportBToCService })
                this.backAddress(item, cityInfo)
            } else {
                this.backAddress(item, cityInfo)
            }
        } catch (e) {
            const { errorCode = -1, description = '请求出错' } = e || {}
            app.apiErrorDialog({
                errorCode,
                description
            })
        }
    },
    noStoreTip() {
        wx.showModal({
            title: '',
            content: '您所选收货地址附近无门店可配送哦，请重新选择',
            showCancel: false,
            confirmText: '知道了',
        })
    },
    backAddress(item, { lon, lat, cityID, cityName, storeID, storeName, storeInfo, supportBToCService, cityCode, deliveryCenterCode } ) {
        const { title = '', province = '', district = '', ad_info = {}, id } = item
        const pages = getCurrentPages()
        const params = {
            gisAddress: title,
            gisProvince: (province || ad_info.province) || '',
            gisDistrict: (district || ad_info.district) || '',
            lon,
            lat,
            cityID,
            cityName,
            storeID,
            mapCityID: id,
            storeInfo,
            storeName
        }
        if (cityCode) {
            Object.assign(params, {
                supportBToCService,
                cityCode,
                deliveryCenterCode
            })
        }
        pages[pages.length - 2].modifyAddress(params)
        wx.navigateBack()
    }
})
