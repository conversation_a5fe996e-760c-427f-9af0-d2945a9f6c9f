
<wxs module="fn">
  module.exports = {
    checkIsAd: function(adObj, item) {
      return item.adKey && adObj[item.adKey] && adObj[item.adKey].goodsType
    }
  }
</wxs>

<view class="waterfall-container {{isFixed ? 'fixed' : ''}}">
  <!-- "waterfall-container"这个节点做了dom监听，节点重新生成会导致监听失效，因此把if条件单独放在里面 -->

  <!-- 全国送提示 -->
  <view wx:if="{{ tabs[0].tabType === '4' }}" class="b2c-tips">
    <view class="left-dot"></view>
    当前地址附近暂无百果园门店，仅支持<text>快递到家</text>服务
    <view class="right-dot"></view>
  </view>
  <!-- 全国送提示 -->

  <view class="waterfall-content">
    <view class="waterfall-goods">
      <view class="goods-container">

        <!-- 第一列 -->
        <view class="col first-col">
          <!-- 脱离于商品循环之外，展示的招牌商品橱窗 -->
          <block wx:if="{{ addressInfo.supportStoreService }}">
            <super-fruit-showcase
              id="super-fruit-showcase"
            >
            </super-fruit-showcase>
          </block>
          <!-- 脱离于商品循环之外，展示的招牌商品橱窗 -->

          <!-- 左侧骨架屏 -->
          <template
            wx:if="{{ skeletonState.left && (isLoading && !tabs[0].goodsList[0].length) }}"
            is="waterfall-skeleton">
          </template>
          <!-- 左侧骨架屏 -->

          <block
            wx:for="{{ tabs[0].goodsList[0] }}"
            wx:for-index="idx"
            wx:for-item="goods"
            wx:key="idx">
            <template is="waterfall-item" data="{{ goods, adObj, picDomain, commonGoodsType, isLoading, minHeight, noGoodsImage, isOpenWaterfallFold }}"></template>
          </block>
        </view>
        <!-- 第一列 -->

        <!-- 第二列 -->
        <view class="col">
          <!-- 脱离于商品循环之外，展示的切分banner -->
          <block wx:if="{{ addressInfo.supportStoreService }}">
            <split-banner
              id="split-banner">
            </split-banner>
          </block>
          <!-- 脱离于商品循环之外，展示的切分banner -->

          <!-- 右侧骨架屏 -->
          <template
            wx:if="{{ skeletonState.right && (isLoading && !tabs[0].goodsList[1].length) }}"
            is="waterfall-skeleton">
          </template>
          <!-- 右侧骨架屏 -->

          <block
            wx:for="{{ tabs[0].goodsList[1] }}"
            wx:for-index="idx"
            wx:for-item="goods"
            wx:key="idx">
            <template is="waterfall-item" data="{{ goods, adObj, picDomain, commonGoodsType, isLoading, minHeight, noGoodsImage, isOpenWaterfallFold }}"></template>
          </block>
        </view>
        <!-- 第二列 -->
      </view>

      <!-- 商品缺省图 -->
      <view
        wx:if="{{ !tabs[0].goodsList[0].length && tabs[0].isLoadDone }}"
        class="noGoods-image">
        <no-goods
          noGoodsContent="暂无推荐内容"
          contentSize="28"
          contentColor="#888888"
          contentLineHeight="40"
          noGoodsIcon="{{noGoodsImage}}">
        </no-goods>
      </view>
      <!-- 加载提示 -->
      <view
        wx:else
        style="{{ isShowWaterfall ? '' : 'margin-top: 16rpx;' }}"
        class="bottom-tip">
        <!-- 正在加载 或 未展示瀑布流商品 -->
        <bounce-tips
          wx:if="{{ isLoading }}"
          loading="{{ true }}" />
        <text
          wx:elif="{{ tabs[0].isLoadDone }}"
          class="tip-text">没有更多啦~</text>
      </view>
    </view>

    <pagoda-sellout-item
      wx:if="{{ isShowWaterfallFold }}"
      sellOutNum="{{ selloutCount }}"
      isFruitSearch="{{ true }}"
      bind:isShow="selloutIsShow"
    />
  </view>
</view>

<template name="waterfall-skeleton">
  <view class="waterfall-skeleton">
    <view class="waterfall-avatar">
    </view>
    <view class="waterfall-name">
    </view>
    <view class="waterfall-desc">
    </view>

    <view class="waterfall-price">
    </view>
  </view>
</template>

<template name="waterfall-item">
  <view
    class="waterfall-item"
    wx:for="{{ goods }}"
    wx:key="goodsSn">

    <block wx:if="{{ item.adKey && adObj[item.adKey] }}">
      <!-- 榜单模块 -->
      <view
        wx:if="{{ fn.checkIsAd(adObj, item) && adObj[item.adKey].openType === '72' }}"
        class="goods-online-image"
        >
        <goods-rank-list
          adConfig="{{ adObj[item.adKey] }}"
          rankGoodsList="{{ adObj[item.adKey].rankGoodsList }}"
          >
        </goods-rank-list>
      </view>
      <!-- 榜单模块 -->

      <!-- 普通瀑布流广告位 -->
      <view
        wx:elif="{{ fn.checkIsAd(adObj, item) && adObj[item.adKey].goodsType === 'ad' }}"
        class="goods-advertis-image"
        data-item="{{ adObj[item.adKey] }}"
        data-index="{{ adObj[item.adKey].idx }}"
        >
        <image mode="aspectFill" src="{{picDomain + adObj[item.adKey].picUrl}}" bindtap="clickAdvertisement" data-data="{{ adObj[item.adKey] }}" />
      </view>
      <!-- 普通瀑布流广告位 -->
    </block>

    <!-- 商品公用组件 -->
    <block wx:elif="{{item.goodsSn}}">
      <common-goods
        wx:if="{{ item.isSellOutGoods ? isOpenWaterfallFold : true }}"
        id="goods_{{item.goodsSn}}"
        customClassName="home"
        goodsObj="{{item}}"
        sortMultiSpec
        multiSpecSingleSkuShowLayer
        isShowSubtitle="{{true}}"
        isShowCount="{{true}}"
        widthFill="{{true}}"
        heightFill="{{true}}"
        bind:updateCount="updateCartCount"
        bind:wxsAnimation="handlewxsAnimation"
        bind:toShowChoiceLayer="toShowChoiceLayer"
        componentRow="{{commonGoodsType}}"
        addSensorskey="tapWaterfallToCart"
        choiceSensorskey="tapWaterfallChoice"
        reportExtendData="{{ { Position: item.positionIndex } }}"
        show-rank-bar
      ></common-goods>
    </block>
    <!-- 商品公用组件 -->
  </view>
</template>
