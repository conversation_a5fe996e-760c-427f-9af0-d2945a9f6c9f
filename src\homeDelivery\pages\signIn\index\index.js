// pages/signIn/index.js
const commonObj = require('../../../../source/js/common').commonObj
const app = getApp()
const sensors = require('../../../../utils/report/sensors')
const loginMixin = require('../../../../mixins/loginMixin')
const util = require('../../../../utils/util')
import { defaultCustomerAvatar } from '../../../../source/const/user'
import {
  getEncryptKey
} from '../../../../service/encrypt';
import { protocolPopup } from '../components/protocolPopup/service'
import { loginType } from '../service/const'

Page({
  mixins: [loginMixin],
  /**
   * 页面的初始数据
   */
  data: {
    pagodaLogo: app.globalData.pagodaLogo, // 百果园logo
    showUserAuthorization: false,
    animationData: {},
    needLogin: false,
    phoneNumber: '',
    isRefreshLocation: true,
    pageName: 'signIn',  // 混入方法中使用
    isLogining: false, // 是否请求登录中
    isAgree:false, // 协议是否选中
    isOneClickLogin: false, // 是一键登录
    loginType: loginType.weixin
  },
  _data: {
    js_code: '',
    finishToRouter: '',
    tryGetWxsnsCount: 1,
    options: null,
    systemInfo: {},
    loginKey: '', // 登录key
    phoneNumber: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检测sessionKey是否可用
    // 根据用户授权控制页面显隐
    // wx.showLoading({
    //   title: '加载中',
    //   mask: true
    // })
    // 获取用户是否授权过获取用户信息
    // this.getUserSetting()
    // 获取用户的 openId，unionId
    // this.getUserWxSnsInfo()
    this.onLoadHandler(options)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    sensors.pageScreenView()
  },
  onLoadHandler(options) {
    this.getLoginInfo()
    // 获取isRefreshLocation
    const { isRefreshLocation = true, finishToRouter = '' } = options || {}
    this.setData({ isRefreshLocation: String(isRefreshLocation) !== 'false' })
    this._data.finishToRouter = finishToRouter // 完成登录要跳转的页面
    this._data.options = options
    this._data.systemInfo = wx.getStorageSync('systemInfo') || {}
  },

  getUserSetting(){
    const userNameAndImg = wx.getStorageSync('userNameAndImg') || {}
    // 用户头像昵称不存在，展示授权请求界面
    if (Object.keys( userNameAndImg ).length === 0 || userNameAndImg.isDefault) {
      this.showUserAuthorization(true)
    }
    else {
      this.showUserAuthorization(false)
    }
  },

  // 展示登录或者授权请求界面
  showUserAuthorization (isShow) {
    this.setData({
      showUserAuthorization: isShow
    })
    this.createShowAnimation()
  },

  // 每次登陆都重新获取用户个人信息
  getUserProfile: util.throttle(function(e) {
    // 低版本用户（微信7.0.9以下）不支持 wx.getUserProfile
    if (!wx.getUserProfile) {
      this.setUserNameAndImg()
      this.showUserAuthorization(false) // 展示登录界面
      return
    }
    // 每次调用该接口 wx.getUserProfile，均需用户确认，要保存起来
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        this.setUserNameAndImg(res.userInfo)
        this.reportSensors(true) // 上报神策埋点
      },
      fail: (res) => {
        console.log(res)
        this.setUserNameAndImg()
        this.reportSensors(false) // 上报神策埋点
      },
      complete: (res) => {
        this.showUserAuthorization(false) // 展示登录界面
      }
    })
  },1500),

  // 缓存头像昵称性别
  setUserNameAndImg (userInfo = {}) {
    let userNameAndImg = {
      nickName: '果宝宝',
      avatarUrl: defaultCustomerAvatar,
      isDefault: true
    }

    if(Object.keys(userInfo).length) {
      const { nickName, avatarUrl } = userInfo
      userNameAndImg = {
        nickName,
        avatarUrl,
        isDefault: false
      }
    }

    wx.setStorageSync('userNameAndImg', userNameAndImg)
  },
  async handleGetPhone() {
    if (!this.data.isAgree) {
      protocolPopup.show()
      const agree = await protocolPopup.waitProtocolPopup()
      if (!agree) return
      // 自动勾选
      this.radioTapHandle()
    }
    this.changeLoginStatus(true)
  },
  componentGetPhoneNumber(e) {
    this.getPhoneNumber(e.detail)
  },
  // 微信授权快速登录（获取用户微信绑定的手机号）
  async getPhoneNumber (e) {
    const that = this
    // iv和encryptedData如果为undefined，则表示表示用户拒绝了你的授权请求，并且不想登录┓( ´∀` )┏
    if (e.detail.iv && e.detail.encryptedData) {
      // 上报神策点击允许
      this.trackClickEvent({
        blockName: '授权手机号',
        blockCode: '02',
        element_name: '允许',
        element_code: '100102011'
      })
      const wxSnsInfo = wx.getStorageSync('wxSnsInfo') || {}
      if (!wxSnsInfo.openid) {
        const confirmRes = await app.showModalPromise({
          content: "登录失败，请稍后重试"
        })
        if (confirmRes) {
          this.onLoadHandler(this._data.options)
          this.changeLoginStatus(false)
          return
        }
      }
      try {
        const params = {
          "unionId": wxSnsInfo.unionid,
          "encryptedData": e.detail.encryptedData,
          'iv': e.detail.iv
        }
        that.signIn(params)
        return
      } catch(err) {
        console.log(err)
      }
      that.changeLoginStatus(false)
      return
    }
    that.changeLoginStatus(false)
    // 上报神策点击拒绝
    this.trackClickEvent({
      blockName: '授权手机号',
      blockCode: '02',
      element_name: '拒绝',
      element_code: '100102012'
    })
  },

  // 手机号登录（使用手机号验证码登录）
  goSignInWithPhoneNumber () {
    let that = this
    that.changeLoginStatus(true)
    // 上报神策点击立即注册
    that.trackClickEvent({
      blockCode: '00',
      element_name: '短信验证登录',
      element_code: '100100014'
    })
    wx.navigateTo({
      url: `/homeDelivery/pages/signIn/phoneNumber/index?isRefreshLocation=${that.data.isRefreshLocation}&finishToRouter=${that._data.finishToRouter}`,
      success() {
        setTimeout(() => {
          that.changeLoginStatus(false)
        }, 500)
      }
    })
  },

  // 获取凭证code（该凭证能换取用户登录态信息）
  getLoginCode () {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          resolve(res.code)
        },
        fail: () => {
          reject()
          console.log('获取code失败...')
        }
      })
    })
  },
  // 获取用户的 openId，unionId
  async getUserWxSnsInfo () {
    let that = this
    const code = await that.getLoginCode()
    const params = {
      "js_code": code
    }
    try {
      let res = await app.api.getWxSns(params)
      if (res.errorCode === 0 && res.data) {
        const { openId, unionId } = res.data || {}
        this.setWxSnsInfo(openId, unionId)
        return
      }
      throw new Error()
    } catch (error) {
      console.log(error)
      // 获取openid、unionId失败 则重新获取
      if (this._data.tryGetWxsnsCount > 1) {
        return
      }
      this.getUserWxSnsInfo()
      this._data.tryGetWxsnsCount++
    }
  },

  // 创建显示隐藏动画
  createShowAnimation () {
    let animation = wx.createAnimation({
      duration: 200,
      timingFunction: "ease",
      delay: 0
    })
    animation.opacity(1).step()
    this.setData({
      animationData: animation.export()
    })
  },
  signIn: util.throttle(function (options) {
    //  用户进行微信快捷登录，在登录后设置标记。用于获取协议版本跳过协议确认弹窗
    wx.setStorageSync('loginSuccessSetProtocol', true)
    this.toLogin({
      loginChannel: 1,
      ...options
    })
  },1500),
  // 标记是否在登录中的状态
  changeLoginStatus(isLogin) {
    this.setData({
      isLogining: isLogin
    })
  },
  /**
   * @description 取消授权头像、昵称，仍然可以登录
   */
  cancelLogin () {
    // wx.navigateBack()
    this.showUserAuthorization(false) // 展示登录界面
  },

  // 神策上报点击事件
  trackClickEvent( params = {} ) {
    sensors.clickReport(params)
  },

  // 用户授权上报神策
  reportSensors (isPermit) {
    // 用户允许授权
    if (isPermit) {
      this.trackClickEvent({
        blockName: '授权用户信息',
        blockCode: '01',
        element_name: '允许',
        element_code: '100101011'
      })
    }
    // 用户取消授权
    else {
      this.trackClickEvent({
        blockName: '授权用户信息',
        blockCode: '01',
        element_name: '取消',
        element_code: '100101012'
      })
    }
  },

  // 错误处理
  handleError(errorMsg, code) {
    let content = '系统繁忙，请稍后重试'
    if (errorMsg) {
      content = errorMsg
    }
    if (code === 55312) {
      this.setData({
        isOneClickLogin: false,
      })
    }
    // if (typeof code !== 'undefined') {
    //   content += '，code:' + code
    // }
    commonObj.showModal('提示', content, false, '我知道了', '', function (res) {
      if (res.confirm) {
        // wx.navigateBack()
      }
    })
  },
  radioTapHandle(){
    this.setData({
      isAgree:!this.data.isAgree
    })
    if (this.data.isAgree) {
      this.trackClickEvent({
        blockCode: '00',
        element_name: '勾选协议',
        element_code: '100100011'
      })
    }
  },
  navBack(){
   if(getCurrentPages().length === 1){ // 如果只有当前页的非tab页面的页面栈 返回的话就跳我的页
    wx.switchTab({
      url: '/pages/index/index',
    })
   }else{
      wx.navigateBack({
        delta: 1,
      })
   }
   this.trackClickEvent({
    blockCode: '00',
    element_name: '取消登录',
    element_code: '100100013'
  })
  },
  
  /**
   * @description 一键登录
   */
  toOneClickLogin: util.throttle(async function () {
    if (!this.data.isAgree) {
      protocolPopup.show()
      const agree = await protocolPopup.waitProtocolPopup()
      if (!agree) return
      // 自动勾选
      this.radioTapHandle()
    }
    //  用户进行微信快捷登录，在登录后设置标记。用于获取协议版本跳过协议确认弹窗
    wx.setStorageSync('loginSuccessSetProtocol', true)
    this.toLogin({
      loginChannel: 3,
      loginKey: this._data.loginKey,
      phoneNumber: this._data.phoneNumber
    })
    this.trackClickEvent({
      blockCode: '00',
      element_name: '一键登录',
      element_code: '100100012'
    })
  },1500),
  setWxSnsInfo(openId, unionId) {
    if (openId && unionId) {
      wx.setStorageSync('wxSnsInfo', {
        openid: openId,
        unionid: unionId
      })
      app.globalData.wxOpenId = openId
    }
  },
  /**
   * @description 获取登录信息
   */
  async getLoginInfo() {
    const lastIsManuallyLogout= wx.getStorageSync('isManuallyLogout')
    const code = await this.getLoginCode()
    const params = {
      "jscode": code
    }
    try {
      let res = await app.api.getLoginInfoRequest(params) 
      const decryptKey = await getEncryptKey('pwd')
      let decryptdata = JSON.parse(commonObj.Decrypt(res.data, decryptKey));
      console.log(decryptdata)
      const { memberId, loginKey, unionId, openId, phoneNumber } = decryptdata
      this.setWxSnsInfo(openId, unionId)
      const isOneClickLogin = lastIsManuallyLogout ? false : Boolean(memberId)
      this.setData({
        loginType: isOneClickLogin ? loginType.direct : loginType.weixin,
        isOneClickLogin  // 如果上次为手动退出登录，则不一键登录，否则 能查到会员信息，则可以一键登录
      })
      this._data.loginKey = loginKey
      this._data.phoneNumber = phoneNumber
    } catch (error) {
      console.log(error)
      this.getUserWxSnsInfo()
    }
  }
})
