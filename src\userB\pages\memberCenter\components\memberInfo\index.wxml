<wxs module="filter" src="../../../../../utils/common.wxs"></wxs>
<view class="container {{!isLogin ? 'no-login-container' : ''}} {{showCode ? '' : 'show-code-container'}}" wx:if="{{!noData}}">
  <!-- 账户信息 -->
  <view class="vip-info" wx:if="{{isConnected}}">
    <view class="info" bindtap="navigateToPage" data-type="duiba">
      <view class="info-detail">
        <text wx:if="{{isLogin}}" class="info-detail-value">{{filter.parseInt(balanceInfo.integralAmount)}}</text>
        <text class="info-detail-novalue" wx:else>**</text>
      </view>
      <view class="menber-info">积分商城</view>
    </view>
    <view class="info" bindtap="navigateToPage" data-type="coupon">
      <view class="info-detail">
        <view wx:if="{{isLogin}}" class="info-detail-value">
          <text>{{balanceInfo.couponsAmount}}</text>
          <image class="new-customer" wx:if="{{showNewCustomerTip}}" src="https://resource.pagoda.com.cn/dsxcx/images/6d1cf289a165e9c7d01f50da0e9719a7.png" />
        </view>
        <text class="info-detail-novalue" wx:else>**</text>
      </view>
      <view class="menber-info">卡券</view>
    </view>
    <view class="info" bindtap="navigateToPage" data-type="deposit">
      <view class="info-detail">
        <text wx:if="{{isLogin}}" class="info-detail-value">{{filter.formatPrice(balanceInfo.walletAmount) || 0}}</text>
        <text class="info-detail-novalue" wx:else>**</text>
      </view>
      <view class="menber-info">钱包·充值</view>
    </view>
  </view>
  <!-- 会员码 -->
  <view class="member-code" wx:if="{{showCode && isLogin}}">
    <view class="code">
      <image class="canvas-image" wx:if="{{memberCode}}" src="{{memberCode}}" />
      <view class="payway-container" wx:if="{{isConnected}}">
        <view class="payway-button {{!canUseOfflinePay ? 'disabled' : ''}}" bindtap="callOfflinePay">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/95a92b53c8b4e51db8a5bae2cbae840a.png" class="icon-wepay" />
          <text>微信</text>
          <text>支付</text>
        </view>
      </view>
    </view>
    <view class="refresh">
      <view class="touch-zone" bindtap="refreshMemberCode"></view>
      <image class="{{ isRefreshing ? 'image--rotate' : '' }}" src="https://resource.pagoda.com.cn/dsxcx/images/********************************.png" />
      <text>每30秒自动更新，请在店内消费时使用</text>
    </view>
  </view>
  <!-- 控制会员码的展示和隐藏，暂时写死不展示了 -->
  <view wx:if="{{false}}" bindtap="controlMemberCode">
    <view class="retract" wx:if="{{showCode}}">
      <image src="https://resource.pagoda.com.cn/dsxcx/images/d22d581d74541959236d041c654c7fad.png" />
      <text>收起会员码</text>
    </view>
    <view class="open" wx:else>
      <image src="https://resource.pagoda.com.cn/dsxcx/images/8bdcce12a5b93efa5df9f766bf583ee9.png" />
    </view>
  </view>
  <!-- 未登录 -->
  <view wx:if="{{!isLogin}}" class="no-login" bindtap="navigateLogin">
    <view>请登录后查看</view>
  </view>
  <!-- 会员码canvas -->
  <canvas class="canvas-code" canvas-id="canvasbarcode" />
</view>
<view class="no-data-container" wx:else>
  <image src="https://resource.pagoda.com.cn/dsxcx/images/f004098801310a91d6e07a2a1cd9f124.png" />
</view>
