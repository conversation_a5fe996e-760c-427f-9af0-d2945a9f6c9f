########
# 调试脚本
# - name: 读取环境变量
#   script:
#   - node -v
#   - node -e "console.log('当前运行环境：' + process.env.ENV)"
########
# 神策上传start
.sensor_stages: &sensor_stages
  # 步骤一 打印一下环境变量
  - name: 读取环境变量
    script:
    - node -v
  - name: 读取环境变量
    script:
    - node -e "console.log('当前运行环境：' + process.env.RUNTIME_ENV)"

  # 步骤二 执行shell
  - name: 执行shell
    script:
      - |
        str=$(git diff --name-only HEAD~1 --stat)

        # 定义一个数组变量，包含所有需要检查的路径
        paths=(
          "src/componentsSubPackage/commonUtils/sensors/"
          "src/utils/report/utils/"
          ".coding-ci.yml"
        )

        # 初始化一个变量，用于标记是否发现变更
        found_change=false

        # 遍历数组中的每个路径
        for path in ${paths[@]}; do
          if [[ $str == *"$path"* ]]; then
            found_change=true
            break
          fi
        done

        # 根据是否发现变更来决定是否触发构建
        if $found_change; then
          echo "指定文件夹发生了变更，触发构建"
          npm run uploadSensor
        else
          echo "指定文件无变更，不触发构建"
        fi

.sensor_pipeline_test: &sensor_pipeline_test
  env:
    RUNTIME_ENV: test_exc
  # 执行内容
  stages: *sensor_stages

.sensor_pipeline_uat: &sensor_pipeline_uat
  # 推送/合并动作触发时，执行脚本
  env:
    RUNTIME_ENV: uat_exc
  # 执行内容
  stages: *sensor_stages

.sensor_pipeline_prod: &sensor_pipeline_prod
  # 推送/合并动作触发时，执行脚本
  env:
    RUNTIME_ENV: exc
  # 执行内容
  stages: *sensor_stages
# 神策上传end

# 小程序构建start
.mp_build_stages: &mp_build_stages
  - name: 安装依赖
    script:
    - yarn install
  - name: 执行构建程序
    script:
    - yarn build-mp:${ENV}

.mp_build_pipeline_test: &mp_build_pipeline_test
  env:
    ENV: test
  stages: *mp_build_stages

.mp_build_pipeline_uat: &mp_build_pipeline_uat
  env:
    ENV: uat
  stages: *mp_build_stages

.mp_build_pipeline_prod: &mp_build_pipeline_prod
  env:
    ENV: prod
  stages: *mp_build_stages
# 小程序构建end

# dev_auto_upload:
#   <<: *pipeline_test

# 指定多条pipeline
cd:
  push:
    # - *sensor_pipeline_test
    - docker:
        image: node:18
      <<: *mp_build_pipeline_uat

# 指定各个分支的合并触发
test:
  push:
    - *sensor_pipeline_test
    - docker:
        image: node:18
      <<: *mp_build_pipeline_test

staging:
  push:
    - *sensor_pipeline_uat
    - docker:
        image: node:18
      <<: *mp_build_pipeline_uat

production:
  push:
    - *sensor_pipeline_prod
    - docker:
        image: node:18
      <<: *mp_build_pipeline_prod
