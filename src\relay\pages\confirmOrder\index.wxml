<wxs module='common' src="../../../utils/common.wxs"></wxs>
<wxs module='invoice' src="../../../utils/invoice.wxs"></wxs>
<view class="container">
  <view class="header-background"></view>
  <scroll-view scroll-y>
    <view class="confirm-order-box">
      <!-- 门店信息 -->
      <view bindtap="switchStore" class="store-info">
        <view class="name row-ellipsis"><view>提货门店：{{store.shortName || store.name}}</view><image style="width: 20rpx;height: 20rpx;" src="../../../source/images/arrow-right-gary.png"></image></view>
        <view class="address row-ellipsis">{{store.address}}</view>
        <view class="time">门店营业时间：{{store.storeBusinessTime}}</view>
        <view class="tips">助力环保，人人有责，提货请自备购物袋或到店购买哦~</view>
      </view>
      <!-- 商品信息 -->
      <view wx:for="{{ groupGoodsList }}" wx:key="index" wx:for-item="groupGoods" class="relay-activity-goods item-margin">
        <view class="title">
          <text>门店接龙{{ (groupGoodsList && groupGoodsList.length > 1) ? (index + 1) : '' }}</text>
          <text class="self-take-date">提货时间：{{ groupGoods.pickupTimeStr }}</text>
        </view>
        <view wx:for="{{ groupGoods.goodsList }}" wx:key="goodsSn" wx:for-index="goodIndex">
          <goods-item
            goodsObj="{{item}}"
            cart-goods-item="{{goodsCode2CartItem[item.goodsSn]}}"
            activityCode="{{groupGoods.activityId}}"
            bind:change="onCartNumChange"></goods-item>
        </view>
      </view>
      <!-- 失效商品信息 -->
      <view class="relay-activity-goods item-margin" wx:if="{{unAvailableGoosList && unAvailableGoosList.length > 0}}">
        <view class="invalid title">
          <text>失效</text>
          <text>因活动库存不足等而导致失效的商品</text>
        </view>
        <view wx:for="{{unAvailableGoosList}}" wx:key="goodsSn">
          <goods-item goodsObj="{{item}}" disabled></goods-item>
        </view>
      </view>
      <!-- 商品总额 -->
      <view class="total-price item-margin item-flex">
        <view class="title">商品金额</view>
        <view class="price"><text class="rmb-size">￥</text>{{common.formatPrice(payAmount)}}</view>
      </view>
      <!-- 支付方式 -->
      <view class="pay-type item-margin">
        <view class="pay-type-tip">支付方式</view>
        <paymentWaySelect
          selectPagodaPay="{{selectPagodaPay}}"
          selectWxPay="{{selectWxPay}}"
          selectUnionPay="{{selectUnionPay}}"
          mainBalance="{{balanceInfo.count}}"
          rechargeText="{{rechargeText}}"
          mainBalanceIsNotEnough="{{!balanceInfo.enough}}"
          bindsetWaySelect="setWaySelectHandle"
          bindswitchPagodaPayChange="switchPagodaPayChangeHandle"
        />
      </view>
      <!-- 根据支付方式展示文案 -->
      <view class="pay-mode-text">
        {{invoice.getVoucherText(selectWxPay || selectUnionPay, selectPagodaPay, false, store, 'selfTake')}}
      </view>
      <view class="padding-bottom safe-area-inset-bottom"></view>
    </view>
  </scroll-view>

  <!-- 立即支付 -->
  <view class="footer-box safe-area-inset-bottom">
    <view class="confirm-pay">
      <view class="pay-amount">
        <view>实付金额</view>
        <text>￥{{common.formatPrice(payAmount)}}</text>
      </view>
      <view class="pay-button {{payAmount <= 0 ? 'button-disabled' : ''}}" bindtap="submitForm">立即支付</view>
    </view>
  </view>
</view>

<request-subscribe title="打开提醒，获得订单变更通知" show="{{subscribe.show}}" tmpl-ids="{{subscribe.tmplIds}}" bind:close="onSubscribeClose" />

<!-- 验证码弹窗组件 -->
<pagoda-popup
  model:visible="{{visibleSMS}}"
  showLeftArrow="{{true}}"
  showClose="{{false}}"
  round="{{true}}"
  z-index="{{1000}}"
  clickOverlayClose="{{false}}"
  position="bottom"
  title="更换支付方式"
  head-class="sms-head-class"
  title-class="sms-title-class"
  height="600rpx"
  bind:onBack="onBack">
    <sms-validate
      model:visible="{{visibleSMS}}"
      bind:validated="validated"
    />
</pagoda-popup>

<common-loading />
<captcha id="comp-captcha"/>
