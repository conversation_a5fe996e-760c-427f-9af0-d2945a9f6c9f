const sensors = require('../../../utils/report/sensors')
const { prdUrl } = require('../../../utils/config');
const util = require('../../../utils/util');
const GOODSIMAGE = require('../../../source/const/goodsImage');
import { checkIsFansGray } from '../../../utils/gray/sseGray';
const app = getApp();
Page({
  data: {
    couponList: [],
    unAvailableCouponList: [],
    noCouponImage: GOODSIMAGE.NO_ACT, // 优惠券缺省图
    vipCardInfo:{},
    mouthCardSaveMoney:0,
    useLimitCouponKey:'',
    availableListKey:[],
    applyCardSeleted:false,
    defaultBetterCode:'',//最优券code,不算运费券
    packageBestCouponBatchNum:'',//券包内最佳券
    couponDiscountNum:0,
    vipPriceDiscountNum:0,
    totalDiscountNum:0,
    isVipCoupon: true
  },
  _data: {
    couponRules: [], // 优惠券同类型互斥规则 比如：["C,K,F,D", "M"] C,K,F,D类型优惠券不能同时选中
    // isGrayUser: false,
    // C,1：满减 K,2：满折 F,4：立折 D,3：立减 M,5：免运费
    newCouponRules: ["1,2,3,4","5"], // 以前规则是后端返回的，现在写死在前端。免运券+其他任一一张券
  },
  onLoad () {
    this.setInitCouponData()
    this.setVipCardInfo()
  },
  /**
   * @description 设置初始优惠券数据
   */
  setInitCouponData(val) {
    const couponInfo = val || this.invokeLastPageMethod('getCouponInfo')
    let {
      couponRules = [],
      couponList = [],
      unAvailableCouponList = [],
      selectedCouponList = []
    } = couponInfo
    // 灰度用户取新的规则
    couponRules = this._data.newCouponRules
    couponList.forEach(item => {
      if (!selectedCouponList || !selectedCouponList.length) {
        item.isSelected = 0
      } else {
        const selectItem = selectedCouponList.find(obj => obj.couponCode === item.couponCode)
        if (selectItem) {
          item.isSelected = 1
        } else {
          item.isSelected = 0
        }
      }
    })
    let permuteInfo = [...util.permute(['A','I','W']),...util.permute(['A','I']),...util.permute(['A','W']),'A']
    this.setData({
      couponList,
      unAvailableCouponList:util.filterChannelSeparation(unAvailableCouponList,'channelSeparation',permuteInfo),
    })
    this._data.couponRules = couponRules
  },
  /**
   * @description 设置月卡信息
   */
  setVipCardInfo(){
    let {
      vipCardInfo,
      mouthCardSaveMoney,
      packageBestCouponBatchNum
    } = this.invokeLastPageMethod('selectVipCouponPageGetVipCarInfo')
    this.setData({
      vipCardInfo,
      mouthCardSaveMoney,
      packageBestCouponBatchNum,
      saveMoney: mouthCardSaveMoney
    })
  },
  /**
   * @description 重置优惠券信息
   */
  resetCouponList(){
    this.setData({
      couponList: [],
      unAvailableCouponList: [],
    })
  },
  /**
   * @description 优惠券信息----转换字段到与确认订单页一致
   * @param {*} couponInfo
   */
  convertCoupon(couponInfo) {
    const {
      availableList = [],
      currentChoose = [],
      unavailableList = [],
      defaultBetter = [],
      rules,
    } = couponInfo || {}
    const convertCouponInfo = {
      couponList: availableList,
      unAvailableCouponList: unavailableList,
      selectedCouponList: currentChoose,
      defaultBetterCoupon: defaultBetter,
      couponRules:rules
    }
    this.resetCouponList()
    this.setInitCouponData(convertCouponInfo)
    this.getDefaultBetterCoupon(convertCouponInfo)
    this.decideIsVipCoupon(currentChoose)
  },
  /**
   * @description 获取defaultBetterCoupon
   */
  getDefaultBetterCoupon(val){
    const couponInfo = val || this.invokeLastPageMethod('getCouponInfo')
    let {
      defaultBetterCoupon = []
    } = couponInfo
    if(!defaultBetterCoupon.length) return
    const filterCouponArr = defaultBetterCoupon.filter(item => Number(item.couponWay) !== 5)
    let defaultBetterCode = ''
    if(filterCouponArr.length){
      defaultBetterCode = filterCouponArr[0].couponCode || ''
    }
    this.setData({
      defaultBetterCode
    })
  },

  /**
   * @description 调用上级确认订单页方法
   */
  invokeLastPageMethod(methodName, ...args) {
    const pages = getCurrentPages()
    if (!pages || pages.length < 2) {
      return
    }
    const prevPage = pages[pages.length - 2]
    return prevPage[methodName] ? prevPage[methodName](...args) : {}
  },

  /**
   * @description 取消选中，设置选中
   */
  selectChange(e){
    const { couponCode, isSelected, couponWay: initCouponWay } = e.detail || {}
    const list = this.data.couponList
    if (!!isSelected) {
      // 取消选中
      const index = list.findIndex(item => String(item.couponCode) === String(couponCode))
      if (index !== -1) {
          this.setData({
            [`couponList[${index}].isSelected`]: 0
          })
      }
    } else {
      // 选中
      const currRules = this._data.couponRules.find(rules => rules.includes(initCouponWay))
      list.forEach(item => {
        const { couponCode: itemCouponCode, couponWay: itemInitCouponWay } = item
        if (String(itemCouponCode) === String(couponCode)) {
            item.isSelected = 1
        } else {
          if (currRules) {
            // 如果当前优惠券类型在互斥规则里面，且被选中应该被取消选中
            if (currRules.includes(itemInitCouponWay) && item.isSelected) {
              item.isSelected = 0
            }
          }
        }
      })
      this.setData({
        couponList: list
      })
    }
    this.countCouponDiscountNum(list)
    const sensorsData = {
      element_code: !!isSelected ? '118302002' : '118302001',
      element_name: !!isSelected ? "取消用券" : '勾选用券',
      element_content: !!isSelected ? "取消用券" : '勾选用券',
    }
    this.reportData(sensorsData)
  },
  /**
   * @description 展示月卡权益详情
   */
  showBenfitsDetail(e){
    const { url = '' } = e.detail
    if(url){
      this.setData({
        popupBenefits:{
          show:true,
          url
        }
      })
    }
  },
  /**
   * @description 关闭月卡权益详情
   */
  closePopupBenefits(){
    this.setData({
      popupBenefits:{
        show:false,
        url:''
      }
    })
  },
  /**
   * @description 选择飞享卡
   */
  chooseMouthCard(val) {
    const {
      isSelected,
      activityCode,
      giftCouponInfo,
      chooseRenewType,
      refresh,
      ruleLadderId = ''
      } = val.detail;
    Object.assign(this._data, {
      openVipCardType: chooseRenewType,
      cardActivityCode: activityCode,
      giftCouponInfo,
      ruleLadderId
    })
    this.setData({
      applyCardSeleted: isSelected,
    });
    if (refresh) {
      this.orderSettle({selected: isSelected})
    }
    const sensorsData = {
      element_code: isSelected ? '118301001' : '118301002',
      element_name: isSelected ? "勾选开通" : '取消开通',
      element_content: isSelected? "勾选开通" : '取消开通',
    }
    this.reportData(sensorsData)
  },
  /**
   * @description 选择飞享卡
   */
  openCard(){
    const { couponList,applyCardSeleted } = this.data
    if(!applyCardSeleted){
      wx.navigateBack()
      return
    }
    const selectedCouponList = couponList.filter(item => !!item.isSelected)
    this.invokeLastPageMethod('setSelectOpenVipCoupon',selectedCouponList)
    wx.navigateBack()
    let orderCouponNameList = [],orderCouponNumberList = []
    selectedCouponList.forEach(item =>{
      item.couponName && orderCouponNameList.push(item.couponName)
      item.batchNum && orderCouponNumberList.push(item.batchNum)
    })
    const sensorsData = {
      element_code: '118303001',
      element_name: "确认",
      element_content: "确认",
      openXinxiangType_ConfirmOrder:'月卡',
      orderCouponName: orderCouponNameList.join(','),
      orderCouponNumber: orderCouponNumberList.join(',')
    }
    this.reportData(sensorsData)
  },
  /**
   * @description 左上角返回上一页
   */
  backConfirmOrder(){
    const { applyCardSeleted = false } = this.data
    applyCardSeleted && this.invokeLastPageMethod('changeVipCardSelected',{ selected: false})
    wx.navigateBack()
  },
  /**
   * @desc 请求订单确认接口获取订单信息
   */
  async orderSettle({selected = false}) {
    try {
      const settleParams = this.invokeLastPageMethod('buildSettleParams',{
        vipCardPageSelected: selected ? 'Y' : 'N',
        matchingTheBestCoupon: 'Y'
      })
      // 查询灰度信息
      await this.setGrayScaleUser(settleParams)
      const { isFruitFansGray } = this.data
      const res = await app.api[isFruitFansGray ? 'orderSettleGrayScale' : 'orderSettle'](settleParams)
      const {
        couponInfo = {},
        vipPriceDiscount = 0,
        couponValue = 0
      } = res.data || {};
      this.convertCoupon(couponInfo);
      this.setDiscountNum({vipPriceDiscount,couponValue})
    } catch (error) {
      this.convertCoupon({});
    }
  },
  /**
   * @desc 设置优惠金额
   */
  setDiscountNum({vipPriceDiscount = 0,couponValue = 0}){
    const { applyCardSeleted, mouthCardSaveMoney } = this.data
    const totalDiscountNum = Number(vipPriceDiscount) + Number(couponValue)
    this.setData({
      vipPriceDiscountNum: Number(vipPriceDiscount),
      couponDiscountNum: Number(couponValue),
      totalDiscountNum,
      saveMoney: applyCardSeleted ? totalDiscountNum : mouthCardSaveMoney
    })
  },
  /**
   * @description 判断选中券是否都是心享券
   */
  decideIsVipCoupon(list = []){
    const isBgxxVipCoupon = list.every(item => item.isOnlyVip === 'Y')
    this.setData({
      isVipCoupon: isBgxxVipCoupon
    })
  },
  /**
   * @description 计算选中的优惠券金额
   */
  countCouponDiscountNum(list = []){
    const currentChooseList = list.filter(item => item.isSelected === 1 && item.couponWay !== '5')
    const couponDiscountNum = currentChooseList.reduce((sum,item)=>{
      return sum + (item.couponMoney || 0)
    },0)
    const { vipPriceDiscountNum = 0 } = this.data
    const totalDiscountNum = Number(vipPriceDiscountNum) + Number(couponDiscountNum)
    this.setData({
      couponDiscountNum: Number(couponDiscountNum),
      totalDiscountNum
    })
    this.decideIsVipCoupon(currentChooseList)
  },
  /**
   * @description 上报神策数据
   */
  reportData(extraData){
    app.sensors.track('MPClick', {
      screen_code: '1183',
      screen_name: "开通心享",
      ...extraData
    });
  },
  /**
   * 是否为果粉价灰度会员
   */
  async setGrayScaleUser(settleParams) {
    const { cityCode, storeCode } = settleParams || {}
    const isFansGray = await checkIsFansGray(cityCode, storeCode)
    this.setData({
      isFruitFansGray: isFansGray
    })
  },
})
