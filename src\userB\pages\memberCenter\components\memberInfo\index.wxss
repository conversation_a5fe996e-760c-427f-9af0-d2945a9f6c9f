.container {
  width: 100%;
  height: 370rpx;
  padding-top: 6rpx;
  background: #fff;
  border-radius: 16rpx;
  position: relative;
}
.show-code-container {
  height: auto;
}
.info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
}
.member-code {
  margin-top: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color:#333;
}
.code {
  width: 100%;
  height: 164rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.code .canvas-image {
  width: 600rpx; 
  height: 150rpx;
  display: block;
}
.canvas-code {
  width: 600rpx; 
  height: 150rpx;
  position: fixed; 
  top: -99999rpx; 
  left: -99999rpx; 
  z-index: -1; 
}
.refresh {
  margin: 12rpx 72rpx 24rpx 0;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #888;
  position: relative;
}

.touch-zone {
  width: 60rpx;
  height: 60rpx;
  position: absolute;
  left: -15rpx;
}

.refresh image {
  width: 30rpx;
  height: 30rpx;
  margin-right: 7rpx;
}

.refresh image.image--rotate {
  transform: rotate(360deg);
  transition: 1s;
}

.vip-info {
  width: 690rpx;
  height: 106rpx;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  border-radius: 8rpx;
  margin: 0 6rpx;
}
.info-detail {
  display: flex;
  align-items: center;
  position: relative;
  color: #00A34F;
}
.info-detail-novalue {
  font-size: 34rpx;
  margin-right: 9rpx;
}
.info-detail-value {
  font-size: 34rpx;
  font-weight: 600;
  color: #222222;
  position: relative;
}
.info-detail-value .new-customer {
  width: 80rpx;
  height: 22rpx;
  position: absolute;
  left: 22rpx;
  top: -16rpx;
}
.menber-info {
  font-size: 24rpx;
  color: #8C8C8C;
}
.payway-container {
  margin: 72rpx 0 0 24rpx;
}
.payway-button {
  padding-bottom: 12rpx;
  background: #00A34F;
  border-radius: 12rpx 0 0 12rpx;
  text-align: center;
  overflow: hidden;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
}
.icon-wepay {
  margin: 26rpx 32rpx 17rpx 32rpx;
  width: 50rpx;
  height: 46rpx;
  display: block;
}
.payway-button text {
  display: block;
}
.payway-button text:last-child {
  margin-bottom: 12rpx;
}
.payway-button.disabled {
  background: #b9b9b9;
  color: #fff;
}
.retract {
  width: 186rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  background: #E9FFF2;
  border-radius: 8rpx 8rpx 0 0;
  font-size: 24rpx;
  color: #00A34F;
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}
.retract image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 5rpx;
}
.open {
  width: 100%;
  height: 90rpx;
}
.open image {
  margin-bottom: 22rpx;
  width: 99rpx;
  height: 99rpx;
  position: absolute;
  left: 50%;
  bottom: -80rpx;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.no-login {
  margin: 92rpx auto 124rpx auto;
  width: 368rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #00A34F;
  border-radius: 44rpx;
  font-weight: bold;
  font-size: 34rpx;
  color: #fff;
}
.no-login-container {
  height: 418rpx;
}
.no-data-container {
  width: 702rpx;
  height: 422rpx;
}
.no-data-container image {
  width: 100%;
  height: 100%;
}
