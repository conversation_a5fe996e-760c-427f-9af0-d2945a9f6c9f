<wxs module="addressDeal" src="../../../../../utils/addressDeal.wxs"></wxs>
<wxs module="common" src="../../../../../utils/common.wxs"></wxs>
<view class="address-set">
  <user-protocol />
  <view wx:if="{{ hasSelectedLocation }}" class="navigation">
    <map id="map" class="map" longitude="{{markers[0].longitude}}" latitude="{{markers[0].latitude}}" scale="18" markers="{{markers}}" enable-scroll="{{ false }}">
      <cover-view wx:if="{{ formData.gisAddress }}" slot="callout">
        <cover-view marker-id="1">
          <cover-view class="flex callout-container">
            <cover-view class="flex callout_float-box">{{ addressDeal.concatAddress(formData, 1) }}<view style="display: inline-block; color: #fff">■</view></cover-view>
          </cover-view>
        </cover-view>
      </cover-view>
    </map>
  </view>
  <view class="address-info {{ hasSelectedLocation ? 'has-map' : 'has-gap'}}">
    <view class="info-item">
      <text class="info-label">收货地址</text>
      <view class="select-address" bindtap="chooseAddress">
        <view class="{{formData.gisAddress ? 'input-address' : 'input-style'}}">
          {{formData.gisAddress ? addressDeal.concatAddress(formData,1) : '点击选择收货地址'}}
        </view>
      </view>
      <view bindtap="inputWxAddress" class="wx_input-box">
        <image class="wx_icon-img" src="https://resource.pagoda.com.cn/group1/M21/8E/CE/CmiWa2J46jiAdqbtAAALZHuO2Ds438.png" />
        微信导入
      </view>
    </view>
    <view class="info-item">
      <text class="info-label">门牌号</text>
      <input placeholder="例：1号楼101室" placeholder-class="input-style" maxlength="30" value="{{formData.address}}" cursor-spacing="270" bindinput="inputDoorNum" data-type="address" bindfocus="onInputFocus" bindblur="onInputBlur" />
      <view
        data-icon="address"
        catchtap="clearBtnClick">
        <image class="clear-circle-btn {{formData.address && isFocusAddress ? '' : 'clear-circle-btn-hidden'}}" src="/source/images/icon-delete.png"></image>
      </view>
    </view>
    <view class="info-item">
      <text class="info-label">收货人</text>
      <input placeholder="填写收货人姓名" placeholder-class="input-style" maxlength="15" value="{{formData.name}}" cursor-spacing="226" bindinput="inputName" data-type="name" bindfocus="onInputFocus" bindblur="onInputBlur" />
      <view
        data-icon="name"
        catchtap="clearBtnClick">
        <image class="clear-circle-btn {{formData.name && isFocusName ? '' : 'clear-circle-btn-hidden'}}" src="/source/images/icon-delete.png"></image>
      </view>
    </view>
    <view class="info-item">
      <text class="info-label">手机号</text>
      <view class="area-phone">
        <input placeholder="填写收货人手机号码" placeholder-class="input-style" type="number" value="{{ (editeStatus === 'edite' && !isEditPhone) ? common.formatPhoneNumber(formData.phoneNumber, true) : formData.phoneNumber }}" cursor-spacing="182" data-type="phone" bindfocus="onInputFocus" bindblur="onInputBlur" bindinput="inputPhoneNumber" maxlength="13" />
        <view wx:if="{{isFocusPhone && preFillPhoneNumber}}" class="edit-phone" bindtap="setLoginPhone">{{ common.formatPhoneNumber_344(preFillPhoneNumber) }}</view>
      </view>
      <view
        data-icon="phoneNumber"
        catchtap="clearBtnClick">
        <image class="clear-circle-btn {{formData.phoneNumber && isFocusPhone ? '' : 'clear-circle-btn-hidden'}}" src="/source/images/icon-delete.png"></image>
      </view>
    </view>
    <view class="info-item">
      <text class="info-label">标签</text>
      <view class="label-list">
        <view wx:for="{{labelList}}" wx:key="labelName" data-item="{{item}}" class="label-item {{item.labelName === formData.label ? 'actived-label' : ''}}" style="{{item.labelName === formData.label ? 'background: ' + addressDeal.labelType(item.labelName).background : ''}}" bindtap="changeLabel">
          <text style="color: {{item.labelName === formData.label ? addressDeal.labelType(item.labelName).fontColor: ''}}">{{item.labelName}}</text>
        </view>
      </view>
    </view>
    <view class="info-item switch-item">
      <text class="info-label">设为默认地址</text>
      <pdm-switch class="pdm-switch" checked="{{formData.isDefault === 'Y'}}" disabled="{{false}}" bindchange="defaultChange"></pdm-switch>
    </view>
  </view>
  <view class="save-btn" bindtap="save">保存地址</view>
  <slot></slot>
  <view class="state_text-box">您的收货人信息将会加密保存，且只用于商品物流配送。</view>
</view>