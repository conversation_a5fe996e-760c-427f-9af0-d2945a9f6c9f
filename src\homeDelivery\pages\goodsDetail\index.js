// homeDelivery/pages/goodsDetail/index.js
import { createStoreBindings } from 'mobx-miniprogram-bindings'
import fruitCartStore from '../../../stores/module/fruitCart'
import ShareCard from './shareCard'
var commonObj = require('../../../source/js/common.js').commonObj;
var app = getApp()
const cartMinxins = require('../../../mixins/cartMixin')
const sensors = require('../../../utils/report/sensors')
const util = require('../../../utils/util')
const txSensor = require('../../../utils/report/txSensor')
const locateMixin = require('../../../mixins/locateMixin')
var emitter = require('../../../source/js/emitter')
var { CLOSE_LOCATE_CHECK_CITY_DIALOG, NEXT_PAGE_LOAD } = require('../../../mixins/locateAfterCheckCity')
const { getCustomerInfo, checkIsShowActivityPrice, getRetailOrMemberPrice } = require('../../../source/js/requestData/activityPrice')
const { MULTI_ENUM } = require('../../../utils/goods/multiSpec')
const config = require('../../../utils/config')
const { DELIVERY_ICON } = require('../../../source/const/goodsImage')
import { getSaleReportInfo }  from '../../../utils/report/getSaleReportInfo'
import { FruitGoods } from '../../../service/fruitGoods';
import { sleep } from '../../../utils/time';
import { showFruitCutSpecDesc } from '~/service/fruitGoodsUtil';
import { handleGoodsLabel } from '~/utils/goods/label';
import userStore from '~/stores/userStore';
import { defaultCustomerAvatar } from '../../../source/const/user';
import { evaluationNum2Text } from '../../../utils/format';
import { getPromiseObj } from '../../../utils/promise';
const { deliveryCenterCodeEshopTransDm } = require('../../../utils/deliveryCenterUtil')

/**金额展示类型枚举 */
const priceDisplayTypeEnum = {
  /**新人特价 */
  activityPrice_new: 1,
  /**门店特价 */
  activityPrice_store: 2,
  /**运营特价 */
  activityPrice_operation: 3,
  /**普通价格 */
  normal: 4,
}
/**
 * 需要曝光的模块节点
 */
const exposeNodes = [
  //  口感
  [
    '.goods-taste',
    '营养成分',
    '131010',
  ],
  [
    '.ai-summarize',
    'AI总结',
    '131011',
    '131011001',
  ],
  //  评价
  [
    '.evaluation-wrap',
    '评价',
    '131011',
  ],
  //  商品信息
  [
    '.goods-detail-info--simple',
    '商品信息',
    '131012',
  ],
  //  商品详情
  [
    '.goods-detail-info--more',
    '商品详情',
    '131013',
  ],
  //  价格提示
  [
    '.price-tips-box',
    '价格提示',
    '131014',
  ]
].map(i => ({ selector: i[0], blockName: i[1], blockCode: i[2], element_code: i[3] }))

Page({

  mixins: [cartMinxins, locateMixin],
  /**
   * 页面的初始数据
   */
  data: {
    goodsObj: {},
    picChange: '/source/images/icon_label_tick2.png',
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    getUrl: commonObj.PAGODA_DSN_DOMAIN,
    shopCart: [],
    isShow: false,
    isClearShop: false,
    isShowList: false,
    isShowCart: true,
    isShowOrderMask: true,
    isShowCartList: true,
    cartAllPrice: ['0', '00'],
    extraData: app.globalData.extraData, // 跳转开卡小程序所需要的参数
    appId: app.globalData.appId, // 跳转开卡小程序所需要的参数
    // v1.6
    isShare:false, //是否显示分享弹框
    isThumbnail: false, //是否显示缩略图弹框
    isIphoneX: app.globalData.isIphoneX,
    addToCartData: {
      picUrl: commonObj.PAGODA_PIC_DOMAIN,
      hide_good_box: true
    },
    tryEatReport: {},  //试吃精选报告
    isShowTryEatReport: false, // 是否展示试吃达人模块
    line: 2, // 评价文字收起时展示的行数
    allTotal: '', // 全部评价总数
    goodsCommentTotalInfo: {
      allTotalNum: 0,
      praiseTotal: 0,
      imageTotal: 0,
    },
    goodsCommentInfo: '',
    defaultCustomerPictuer: defaultCustomerAvatar, // 用户默认头像
    previewSensorsParams: {
      'element_code': '131000008',
      'element_name': '评论图片',
      'element_content': '评论图片',
      'screen_code': '1310'
    },
    systemTime: null,
    fromShareTimeLine: false, //判断是否是从朋友圈进入
    isShowServiceBar: false, // 是否展示服务弹窗
    currentView: '',
    autoplay: true,
    curentCarouselIndex: 0, // 当前轮播index
    isMuted: true, //是否静音播放
    isVideo: false, // 是否为视频
    networkType: '', // 当前网络状态
    isPlaying: false, // 视频是否正在播放中
    isAutoPlay: true, // banner 当前页面在wifi环境下只要没有手动播放,既会走自动播放流程
    curr_id: '',
    canUseSharePic: true, // 表示分享图是否绘制完成
    fromShareDetail: false, // 是否从小程序分享进入的商品详情页
    detectionReportFileList:[], //质检报告列表
    navBarHeight: 60,

    // 及时达图标
    DELIVERY_ICON,
    // 是否预渲染
    preRender: true,
    // 商品详情是否已请求
    goodsDetailfetched: false,
    showPlaceHolder: true,
    placeHolderPic: '',
    // 是否获取到商品信息
    haveGoodsInfo: false,
    showDesc: false,
    showMinPrice: false,
    /**价格展示类型 */
    priceDisplayType: priceDisplayTypeEnum.normal,
    priceDisplayTypeEnum,
    // 分享卡片样式 normal(普通)、special(特价)、newUser(新人特价)
    priceType: 'normal',
    // 自定义分享卡片背景图
    customBgImg: ''
  },
  _data:{
    pageFrom: '',
    carouselImgLoadNum: 0,
    fruitGoodsClass: {},
    options: {},
    goodsReqParams: { // 初始商品请求参数
      goodsSn: '', // 商品编码
      takeawayAttr: '', // 商品属性 及时达/B2C
      goodsID: '' // 原始电商商品id
    },
    sharePic: {}, // 存储分享卡片
    hasClickBackBtn: false, // 是否有点击过返回按钮
    /**
     * @type ShareCard
     */
    shareCard: null // 分享卡片类
  },
  async preRender(){
    const { source, data: preRenderObj } = await new Promise(resolve => {
      const eventChannel = this.getOpenerEventChannel()
      this._data.getBasicInfoResolve = resolve
      ;(eventChannel && eventChannel.emit) ? eventChannel.emit('getBasicInfo', resolve) : resolve({})
    })
    // console.log('preRenderObj', preRenderObj);
    // 没有传商品基础信息，则展示骨架屏
    if (!preRenderObj) {
      source === 'goodsResult' || this.setData({
        preRender: false
      })
      return
    }
    this.setData({
      placeHolderPic: preRenderObj.headPic
    })
    this.handleGoodsData({
      systemTime: new Date().getTime(),
      data: preRenderObj,
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setModelStyle()
    this.preRender()
    const afterCityCheck = {
      onClose: () => {
        this._data.afterCityCheck.resolve()
      }
    }
    afterCityCheck.promise = new Promise(resolve => (afterCityCheck.resolve = resolve))
    this._data.afterCityCheck = afterCityCheck
    this._data.evaluationPromise = getPromiseObj()
    this.storeBindings = createStoreBindings(this, {
      store: fruitCartStore,
      fields: {
        /**
         * @returns { { goodsSn: { count: number } } }
         */
        goodsCountObj: () => fruitCartStore.goodsCountObj
      },
      actions: ['setFruitGoodsCount']
    })
    this.initPageParams(options)
    emitter.on(CLOSE_LOCATE_CHECK_CITY_DIALOG, this._data.afterCityCheck.onClose)
    emitter.emit(NEXT_PAGE_LOAD)
    this._data.hasClickBackBtn = false
    // 实例化创建分享卡片海报类
    this._data.shareCard = new ShareCard(this)
    // 增加截屏事件
    // 朋友圈单页模式不监听
    if (app.globalData.scene !== 1154) {
      wx.onUserCaptureScreen(() => this._data.shareCard.captureScreenEvent())
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 监听网络条件切换
    wx.onNetworkStatusChange((res) => {
      if (res.isConnected) {
        if (this.data.isPlaying) {
          if (this.data.networkType === 'wifi' && res.networkType !== 'wifi') {
            wx.showToast({
              title: '当前已切换到非WiFi环境播放，请注意流量消耗',
              icon: 'none'
            });
          }
        }
      }
      this.setData({
        networkType: res.networkType
      })
    })
  },

  /**
   * 初始页面参数
   */
  initPageParams(options) {
    // 目前这个options只用在查询商品详情和上报神策埋点
    // 如果用在其他地方,需要考虑异地分享商品下架后
    // 查询到另一个商品详情的情况
    this._data.options = options || {}
    // 卡片分享商品详情/海报分享商品详情,在及时达品类页,首页的forwardNavigate会传此参数
    this._data.options.fromShareDetail = Number(this._data.options.fromShareDetail) === 1
    const { homeDeliveryObj, paySuccessObj, fromShareDetail } = this._data.options
    let goodsTakeawayAttr = '及时达'
    if (homeDeliveryObj || paySuccessObj) {
      const { goodsID, takeawayAttr, goodsSn } = JSON.parse(decodeURIComponent(homeDeliveryObj) || paySuccessObj)
      goodsTakeawayAttr = takeawayAttr || '及时达'
      Object.assign(this._data.goodsReqParams, {
        goodsID,
        takeawayAttr: goodsTakeawayAttr,
        goodsSn
      })
    }
    this._data.pageFrom = options.pageFrom
    this.setData({
      fromShareDetail,
      fromShareTimeLine: app.globalData.scene === 1154,
      isNeedB2CBusiness: goodsTakeawayAttr === 'B2C'
    })
    if (goodsTakeawayAttr === 'B2C') {
      // B2C商品，只要有城市，就显示内容
      this.setData({
        'mainViews.noStore': 'content'
      })
    }
  },

  setModelStyle() {
    var that = this;
    if (wx.getSystemInfo) {
      wx.getSystemInfo({
        success: function (res) {
          // canvas绘制的海报高宽比（高：1334，宽：750）
          const posterRatio = 1334 / 750
          // 弹窗显示海报的缩略图的缩小倍数
          const scale = 0.5
          // 海报的宽度 = 屏幕宽度
          const posterWidth = res.screenWidth * 2
          // 海报的高度 = 海报的宽度 x 海报高宽比
          const posterHeight = parseInt(posterWidth * posterRatio);
          // 海报缩略图的宽度
          const smallPosterWidth = parseInt(posterWidth * scale);
          // 海报缩略图的高度
          const smallPosterHeight = parseInt(posterHeight * scale);
          // 模态框的宽度 = 屏幕宽度
          const modelWidth = res.screenWidth * 2;
          // 模态框的高度 = 模态框的宽度的 1.4倍（是UI决定的）
          const modelHeight = parseInt(modelWidth * 1.40);
          // 获取屏幕高度
          const height = res.windowHeight
          that.setData({
            modelWidth,
            modelHeight,
            smallPosterWidth,
            smallPosterHeight,
            height
          })
        }
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const { superVipStatus } = app.globalData
    this.setData({
      autoplay: true,
      superVipStatus,// 心享会员状态
    })
    this.refreshCustomerInfo()
  },
  onHide () {
    this.setData({
      // 进入下一页时，此时页面的轮播图还是会自动轮播，故禁止掉
      autoplay: false
    })
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this._data.hasClickBackBtn = true
    this.clearObserverExposeNode()
    !!this.videoObserve && this.videoObserve.disconnect()
    emitter.off(CLOSE_LOCATE_CHECK_CITY_DIALOG, this._data.afterCityCheck.onClose)
    this.storeBindings.destroyStoreBindings()
    // 销毁截屏事件
    if (this._data.shareCard && this._data.shareCard.captureScreenEvent) {
      wx.offUserCaptureScreen(() => this._data.shareCard.captureScreenEvent())
    }
  },
  /**
   * 浏览页面上报神策
   */
  reportPageShow() {
    const { goodsObj = {} } = this.data
    sensors.goodsDetailShow(goodsObj)
  },

  triggerShareMenu (bool) {
    const menus = ['shareAppMessage','shareTimeline']
    bool ? wx.showShareMenu({
      withShareTicket: false,
      menus
    }) : wx.hideShareMenu({
      menus
    })
    this.setData({
      canUseSharePic: bool
    })
  },

  /**
     * @description 刷新用户登录状态，心享会员身份
     */
  refreshCustomerInfo () {
    const { IS_LOGIN = false, IS_VIP_CUSTOMER = false } = getCustomerInfo() || {}
    this.setData({
      IS_LOGIN,
      IS_VIP_CUSTOMER
    })
  },

  // 定位完成触发的方法
  onLocateReady () {
    if (this.data.currentView !== 'content') {
      return
    }
    this.setCityInfo()
    this.updateUserInfo()
    this.getGoodsDeatilInfo()
  },

  /**
   * 设置城市地址等信息
   */
  setCityInfo() {
    const { isSupportVip } = wx.getStorageSync('timelyCity') || {}
    this.setData({
      isSupportVip: isSupportVip || false,
    })
  },

  /**
   * @desc 更新用户信息
   */
  updateUserInfo() {
    const isLogin = app.checkSignInsStatus()
    this.setData({
      isLogin
    })
  },

  // 轮播图切换事件
  changeCarouselIndex (e) {
    const { current, source } =  e.detail

    // 轮播页切换到其他页面再切回来，会出现轮播图疯狂抖动，原因是current一直变化导致轮播图一直切换，通过打印可知此时source是空的
    // [source字段值](https://developers.weixin.qq.com/miniprogram/dev/component/swiper.html)
    source !== '' && this.setData({
      curentCarouselIndex: current
    })
    // 暂停视频
    const { isPlaying, curr_id, goodsObj } = this.data
    if (isPlaying) {
      this.bannerVideoContext[curr_id].pause()
    }
    // 区分是视频还是图片
    if(goodsObj.goodsDetailHeadVideoList && goodsObj.goodsDetailHeadVideoList.length){
      const isVideo = current <= goodsObj.goodsDetailHeadVideoList.length - 1 ? true : false
      this.setData({ isVideo })
    }
  },
  async getGoodsDetailFromCos({ storeCode = '', cityCode = '', deliveryCenterCode = '' }){
    this._data.fruitGoodsClass = new FruitGoods({
      storeCode,
      cityCode,
      deliveryCenterCode
    })
    const { goodsSn, goodsID, takeawayAttr } = this._data.goodsReqParams
    const params = Object.assign({ takeawayAttr }, goodsSn ? { goodsSn } : { goodsID })
    const [goodsDetail] = await this._data.fruitGoodsClass.getComplateGoodsList([params])
    return util.deepClone(goodsDetail)
  },

  /**
   * 获取商品信息
   */
  async getGoodsDeatilInfo() {
    const { storeCode, cityCode, deliveryCenterCode, cityID, storeID } = wx.getStorageSync('timelyCity') || {}
    const { goodsSn, goodsID, takeawayAttr } = this._data.goodsReqParams
    // const goodsDetail = await this.getGoodsDetailFromCos({ storeCode, cityCode, deliveryCenterCode })
    // console.log('goodsDetail', goodsDetail);
    // const { saleType, spuNumber } = goodsDetail
    // const { goodsID, takeawayAttr } = this._data.goodsReqParams
    // let goodsSn = this._data.goodsReqParams.goodsSn
    // if (goodsID) goodsSn = this._data.fruitGoodsClass.convertGoodsIdToGoodsSn(goodsID)
    // try {
    //   const result = await app.api.getGoodsDetail({
    //     deliveryCenterCode,
    //     saleType: String(saleType),
    //     takeawayAttr,
    //     spuNumber,
    //     goodsSn
    //   })
    //   Object.assign(result.data, goodsDetail)
    //   // console.log('result.data', result.data)
    //   this.handleGoodsData({...result, setDetectionReportFileList:true})
    //   this.createSharePic(result.data)
    //   // 上报埋点
		// 	this.reportPageShow({
		// 		goodsName: result.data.goodsName,
		// 		goodsSn: result.data.goodsSn,
		// 	})
    // } catch (error) {
    //   console.error(error)
    //   commonObj.showModal('提示', '商品太火爆了，休息一下再试试吧', false, '我知道了', '', () => {
    //     wx.navigateBack({
    //       delta: 1,
    //       fail() {
    //         wx.switchTab({ url: '/pages/homeDelivery/index' });
    //       }
    //     });
		// 	})
		// 	this.reportPageShow({
		// 		goodsSn
		// 	})
    // }
    /******** */
    const params = {
      customerID: app.globalData.customerID || -1,
      goodsSn,
      eshopGoodsId: goodsID,
      storeCode,
      cityCode,
      cityID,
      storeID,
      deliveryCenterCode,
      takeawayAttr: takeawayAttr || '',
      isNeedMergeByGroup: true,
      isNeedMergeMultiSpec: true,
      otherCityShare: this.data.fromShareDetail && cityCode !== app.globalData.optionStore.cityCode
    }
    app.api.getGoodsDetail(params).then(res => {
      this._data.getBasicInfoResolve({ source: 'goodsResult', data: null })
      this.setData({
        goodsDetailfetched: true
      })
      this.handleGoodsData({...res,setDetectionReportFileList:true})
			this.createSharePic(res.data)
			// 上报埋点
			this.reportPageShow({
				goodsName: res.data.goodsName,
				goodsSn: res.data.goodsSn,
			})
    }).catch((e) => {
      console.error(e)
      commonObj.showModal('提示', '商品太火爆了，休息一下再试试吧', false, '我知道了', '', () => {
        wx.navigateBack({
          delta: 1,
          fail() {
            wx.switchTab({ url: '/pages/homeDelivery/index' });
          }
        });
			})
			this.reportPageShow({
				goodsSn
			})
    })
  },

  /**
   * 处理商品数据
   */
  handleGoodsData(res) {
    this.refreshCustomerInfo()
    const { IS_VIP_CUSTOMER, goodsDetailfetched } = this.data
    const { systemTime, data } = res || {}
    // 通过分享途径进入的商详页，如果查不到商品 则提示
    if (!data || !Object.keys(data).length) {
      app.showModalPromise({
        content: '该商品不存在',
        showCancel: false,
        confirmText: '回到首页',
      }).then(() => {
        wx.switchTab({ url: '/pages/homeDelivery/index' })
      })
      return
    }
    const goodsObj = data || {}
    this.handleSpecDesc(goodsObj)
    this.showMinPriceTip(goodsObj)
    const { setDetectionReportFileList = false } = res
    if(setDetectionReportFileList){
      const { detectionReportFileList = [] } = data
      this.setData({
        detectionReportFileList
      })
    }
    const { headPic, spuNumber, takeawayAttr, goodsId, activityList, heartPrice, activityPrice, isSupportCoupon, memberPrice = 0 } = goodsObj
    if (headPic) {
      data.headPic = headPic.includes('http') ? headPic : `${this.data.picUrl}${headPic}`
    }
    const { supportBToCService } = wx.getStorageSync('timelyCity') || {}
    if (goodsDetailfetched && takeawayAttr === 'B2C' && (!supportBToCService || !goodsId)) {
      // b2c商品无法配送 1.城市不支持b2c服务 2.无goodsId，表示不在配送区域
      goodsObj.b2cUnableDelivery = true
    }
    if (!!activityList && !!activityList.length) {
      // 心享会员，心享价 <= 特价， 不参与特价活动，剔除掉特价活动提示
      if (IS_VIP_CUSTOMER && heartPrice && activityPrice && heartPrice <= activityPrice ) {
        goodsObj.activityList = activityList.filter(item => item.key !== 'activityPrice')
        if (isSupportCoupon !== 'Y') {
          goodsObj.activityList.push({
            name: '不可用券',
            desc: '该商品不可用券',
            color: '#888888',
            borderColor: '#888888'
          });
        }
      }
    }
    // 多规格商品合并库存
    const specList = goodsObj.specificationGoodsList || []
    const stockNumSum = specList.length ? specList.reduce((total, item) => total + item.stockNum, 0) : goodsObj.stockNum // 多规格商品库存合并
    const isShowDeliveryTip = this.getIsShowDeliveryTip(goodsObj)
    let deliveryTip = ''
    if (isShowDeliveryTip) {
      const {storeInfo = {}  } = wx.getStorageSync('timelyCity') || {}
      deliveryTip = this.getDeliveryTime(storeInfo, systemTime)
    }
    Object.assign(goodsObj, {
      isShowDeliveryTip,
      deliveryTip
    })
    // 区分服务提示icon显示
    goodsObj.showMoneyIcon = '../../source/images/tips_m.png'
    if(goodsObj.specialServiceList && goodsObj.specialServiceList.some(item=>item.label === '快递到家')){
      goodsObj.showMoneyIcon = '../../source/images/tips_i.png'
    }
    if (this.data.fromShareDetail) {
      goodsObj.shareGoods = true
    }
    // 会员价异常的商品设置为失效
    if (Number(memberPrice) === 0) {
      goodsObj.stockNum = 0
    }

    // 检查商品是否有特价
    this.setPriceDisplayType(goodsObj)
    // 检查特价后再展示页面

    // 5.1.3及时达添加标签
    const labelList = handleGoodsLabel({
      goodsObj,
      isShowPurchaseLimit: true,
      isNeedActivityLabel: this.data.priceDisplayType === priceDisplayTypeEnum.activityPrice_new ? false : true,
      isNeedActivityNewLabel: false,
      needFilterLabelTypes: ['storeRecommend'],
      showLabelNum: Infinity // 商详页展示全部标签
    })
    this.setData({
      goodsObj,
      labelList,
      stockNumSum,
      systemTime,
      cloneGoodsObj: {...goodsObj},
      haveGoodsInfo: Object.keys(goodsObj).length > 0
    })

    const evaluationPromise = this.data.goodsDetailfetched
      ? this.getEvaluationList({takeawayAttr, spuNumber})
      : Promise.resolve()

    this.data.goodsDetailfetched && evaluationPromise.then(() => {
      this._data.evaluationPromise.resolve()
    })
    
    //智慧零售商品页浏览埋点,2表示商品页浏览埋点
    txSensor.txGoodsSensor(data,2)
    // 存在多规格时获取已选规格
    this.setGoodsSkuNService()
    this.data.goodsDetailfetched && wx.nextTick(() => {
      // 获取试吃精选报告
      this.selectComponent('#tryEatModule').getTryEatReport().then(() => {
        this.observerExposeNode()
      })
    })
    const { goodsDetailHeadVideoList = [], detailHeadPicList = [] } = this.data.goodsObj
    const idx = detailHeadPicList.findIndex(pic => pic.includes(headPic))
    // console.log('idx', idx + goodsDetailHeadVideoList.length);
    this.setData({
      curentCarouselIndex: idx + goodsDetailHeadVideoList.length,
      preRender: true
    })
    this.setBannerVideoContext(goodsDetailHeadVideoList)
  },

  handleSpecDesc (resData) {
    this.setData({
      showDesc: showFruitCutSpecDesc(resData)
    })
  },
  /**
   * @desc 多规格商品详情中，当前展示的规格价格为所有规格最低价时，价格旁展示【起】字
   * 果粉版本，取身份价
   * @param
   */
  showMinPriceTip(goodsObj) {
    const getShowPrice = (goodsObj) => {
      const {activityPrice, memberPrice, retailPrice} = goodsObj
      const showPrice = getRetailOrMemberPrice({ retailPrice, memberPrice })
      return activityPrice || showPrice
    }
    const { isMultiSpec = 0, specificationGoodsList = [] } = goodsObj
    let show = false

    if (isMultiSpec) {
      const curGoodsObjShowPrice = getShowPrice(goodsObj)
      let minPrice
      specificationGoodsList.forEach(item => {
        const showPrice = getShowPrice(item)
        if (!minPrice || minPrice > showPrice) {
          minPrice = showPrice
        }
      })
      show = curGoodsObjShowPrice <= minPrice
    }
    this.setData({
      showMinPrice: show
    })
  },

  /**
   * 监听需要曝光的节点
   */
  async observerExposeNode() {
    this.clearObserverExposeNode()

    const { goodsSn, goodsName } = this.data.goodsObj

    // 等评价数据加载完成再进行监听
    // 防止加载出dom之前就创建了监听
    // 导致无法触发回调
    await this._data.evaluationPromise.promise

    const observerList = exposeNodes.map(nodeInfo => {
      const {
        selector,
        blockName,
        blockCode,
        element_code,
      } = nodeInfo

      const observer = this.createIntersectionObserver().relativeToViewport({
        bottom: 0,
      })

      observer.observe(selector, ({ intersectionRatio }) => {
        if (this.observerMap[blockCode] || intersectionRatio <= 0) {
          return
        }

        this.observerMap[blockCode] = true
        sensors.exposureReport({
          element_code,
          blockName,
          blockCode,
          SKU_ID: goodsSn,
          SKU_Name: goodsName,
        })
        observer.disconnect()
      })

      return observer
    })

    this.observerMap = {}
    this.observerList = observerList
  },

  /**
   * 清空监听
   */
  clearObserverExposeNode() {
    if (this.observerList && Array.isArray(this.observerList)) {
      this.observerList.forEach(observer => {
        observer.disconnect()
      })
      this.observerMap = null
      this.observerList = null
    }
  },

  /**
   * @desc 设置金额展示类型
   */
  setPriceDisplayType (goodsObj) {
    const { IS_LOGIN, IS_VIP_CUSTOMER } = this.data
    const { isNewAct, activitySource } = goodsObj
    const isShowActivityPrice = checkIsShowActivityPrice({ goodsObj, IS_LOGIN, IS_VIP_CUSTOMER }) || false

    let type = priceDisplayTypeEnum.normal

    if (isNewAct) {
      type = priceDisplayTypeEnum.activityPrice_new
    } else if (isShowActivityPrice) {
      type = activitySource === 'S' ? priceDisplayTypeEnum.activityPrice_store : priceDisplayTypeEnum.activityPrice_operation
    }

    this.setData({
      priceDisplayType: type,
    })
  },

  /**
   * 商品是否展示29分钟达配送提示
   */
  getIsShowDeliveryTip(goodsObj) {
    const { takeawayAttr = '', eshopDeliveryType = '' } = goodsObj || {}
    // 及时达非自提专享商品
    return takeawayAttr === '及时达' && !(eshopDeliveryType && Number(eshopDeliveryType) === 1)
  },

  /**
   * @desc 生成分享卡片
   * 之前是请求完成之后就把所有sku商品进行绘制，这样会造成卡顿
   * 现在只会绘制当前展示的sku商品，其他规格则在切换了规格且隐藏了一品多规弹窗之后再进行绘制
   * 同时未绘制完成时，禁用分享按钮
   */
  async createSharePic (goodsObj = {}) {
    // 如果已经销毁了页面，不绘制分享卡片
    if (this._data.hasClickBackBtn) {
      return
    }
    console.log('createSharePic begin');
    if (this._data.sharePic.hasOwnProperty(goodsObj.goodsSn)) return
    this.triggerShareMenu(false)
    await this._data.shareCard.drawSharePic(goodsObj)
    this.triggerShareMenu(true)
    // const { specificationGoodsList } = goodsObj
    // let goodsList = [goodsObj]
    // if (!!specificationGoodsList && specificationGoodsList.length > 1) {
    //   goodsList = specificationGoodsList
    // }

    // // 多规格商品，用同一个canvas绘制多个分享卡片，需等一个canvas执行完后再执行下一个，否则canvas绘制错乱
    // var fn = async function (i, count) {
    //   const goodsSn = goodsList[i].goodsSn
    //   !that._data.sharePic.hasOwnProperty(goodsSn) && await that.drawSharePic(goodsList[i])
    //   ++i < count && fn(i, count)
    // }
    // fn(0, goodsList.length)
  },

  toEvaluationDetail({ tagIndex } = { tagIndex: 0 }) {
    const { goodsObj = {}, stockNumSum } = this.data
    const { spuNumber, takeawayAttr } = goodsObj
    const nextPagePromise = wx.navigateTo({
      url: `/userB/pages/evaluation/evaluationDetail/index?spuNumber=${spuNumber}&takeawayAttr=${takeawayAttr}&tagIndex=${tagIndex}`
    })
    nextPagePromise.then(nextPage => nextPage.eventChannel.emit('getGoodObj', { goodsObj, stockNumSum }))
  },

  onAiSummarizeTagTap({ detail }) {
    if (this.data.fromShareTimeLine) {
      return
    }
    this.toEvaluationDetail({
      tagIndex: detail.index,
    })
  },

  // v2.4 跳转到评价详情页
  navigateToEvaluationDetail () {
    if (this.data.fromShareTimeLine) {
      return
    }
    this.toEvaluationDetail()
    const {
      goodsObj = {},
    } = this.data
    const {
      goodsSn,
      goodsName,
    } = goodsObj
    sensors.clickReport({
      element_code: '131000009',
      element_name: '查看全部评价',
      element_content: '查看全部评价',
      screen_code: '1310',
      screen_name: '及时达商品详情页',
      SKU_ID: goodsSn,
      SKU_Name: goodsName || ''
    })
  },
  // v2.4 获取商品评价数据
  getEvaluationList({takeawayAttr, spuNumber}) {
    return app.api.getGoodsFirstComment2({
      customerID: app.globalData.customerID || -1,
      spuNumber,
      takeawayAttr: takeawayAttr || '及时达'
    }, false).then((res) => {
      const { goodsCommentList = [], goodsCommentTotalInfo = {} } = res.data || {}
      if (!goodsCommentList || !goodsCommentList.length) {
        this.setData({
          goodsCommentInfo: null
        })
        return
      }
      const { allTotalNum = 0 } = goodsCommentTotalInfo || {}
      this.setData({
        allTotal: evaluationNum2Text(allTotalNum),
        goodsCommentTotalInfo,
        goodsCommentInfo: {
          ...goodsCommentList[0],
          // 这里展示的评价不展示优质评价样式
          isQuality: 0
        }
      })
    }).catch(() => ({}))
  },
  // 去购物车
  toGoodsCart () {
    sensors.clickReport('goodsDetailToCart')

    //  从群众中来，到群众中去
    if (String(this._data.pageFrom) === 'cart') {
      wx.navigateBack({
        delta: 1
      })
    } else {
      util.toSubShopCart()
    }

  },

  // 打开服务弹窗
  showServiceBar () {},

  showTips() { //显示模板
    this.setData({
      isShowServiceBar: true
    })
    if (app.globalData.reportSensors) {
      const { goodsSn, goodsName } = this.data.goodsObj

      sensors.clickReport({
        element_code: 131000002,
        element_name: '百果园服务说明',
        element_id: '',
        element_content: '百果园服务说明',
        screen_code:'1310',
        screen_name: '及时达商品详情页',
        SKU_ID: goodsSn,
        SKU_Name: goodsName || ''
      })
    }
    wx.reportAnalytics('goodsbrandclick')  //商品品牌信息点击 v1.4
  },
  hideTips() { //隐藏模板
    if (app.globalData.reportSensors) {
      const { goodsSn, goodsName } = this.data.goodsObj

      sensors.clickReport({
        element_code: 131010001,
        element_name: '我知道了',
        element_id: '',
        element_content: '我知道了',
        screen_code:'1310',
        screen_name: '及时达商品详情页',
        SKU_ID: goodsSn,
        SKU_Name: goodsName || ''
      })
    }
  },
  eventprevent() {},

  /**
   * 分享弹窗点击事件
   * @param {Object} e 事件
   */
  showModel(e) {
    this._data.shareCard.showModel(e)
  },
  /**
   * 关闭分享弹窗事件
   * @param {Object} e 事件
   */
  closeModel(e) {
    this._data.shareCard.closeModel(e)
  },
  /**
   * 分享卡片事件
   */
  onShareAppMessage() {
    return this._data.shareCard.onShareAppMessage()
  },
  /**
   * 分享朋友圈事件
   */
  onShareTimeline () {
    return this._data.shareCard.onShareTimeline()
  },

  // 海报保存到本地
  savePoster() {
    this._data.shareCard.savePoster()
  },

  /**
   * 空白函数 禁止滚动
   */
  preventTouchMove() {},

  /**
   * 特价商品倒计时
   */
  handleCountComplete: util.throttle(function () {
    this.getGoodsDeatilInfo()
  }),

  operateCartToShowChoiceLayer () {
    if (this.data.fromShareTimeLine) {
      wx.showToast({
        title: '请前往小程序使用完整服务',
        icon: 'none'
      })
      return
    }
    const { goodsObj= {} } = this.data
    const commodityNumObj = this.data.goodsCountObj[goodsObj.goodsSn] || ''

    sensors.clickReport({
      blockName: '底部操作栏',
      blockCode: '131015',
      element_code: 131000001,
      element_name: '加入购物车',
    })

    sensors.clickReport({
      element_code: 131000011,
      element_name: '一品多规弹窗',
      element_content: '一品多规弹窗',
      screen_code: 1310,
      screen_name:'及时达商品详情页',
      commodityNum:commodityNumObj ? commodityNumObj.count : 0,
      ...getSaleReportInfo({goodsObj,referrer:1})
    })
    const { specificationGoodsList = [] } = goodsObj || {}
    // 会员价异常的商品设置为失效
    specificationGoodsList.forEach(goods => {
      const { memberPrice = 0 } = goods || {}
      if (Number(memberPrice) === 0) {
        Object.assign(goods, {
          stockNum: 0
        })
      }
    })
    this.setData({
      goodsInfo: goodsObj,  // 触发加购弹窗初始化
      showLayer: true,
    })
  },

  startAnimation (e) {
    // this.startWXSAnimation(e.detail, this.data.goodsObj)
  },

  /**
   * 规格商品规格多服务获取
   */
  setGoodsSkuNService () {
    const { goodsSn, specDesc, specificationGoodsList } = this.data.goodsObj
    if (!specificationGoodsList || !specificationGoodsList.length) {
      return
    }
    // 找到当前规格
    const curSpecification = specificationGoodsList.find(el => el.goodsSn === goodsSn) || {}
    const hasService = curSpecification.serviceList && curSpecification.serviceList.length
    const { selectService = {} } = this._data
    // 有服务，未选择时展示未选服务
    // 无服务，展示''
    const serviceName = `${ hasService ? `，${(selectService.serviceName || '未选服务')}` : ''}`
    const skuLabel = `${specDesc}${serviceName}`
    this.setData({
      skuLabel
    })
  },
  /**
   * 切换服务
   */
  hanldeSwitchService: util.debounce(function (e) {
    this._data.selectService = e.detail
    this.setGoodsSkuNService()
  }, 300),
  /**
   * @desc 多拼商品, multiSpecValueJson 字段只在外层商品有，切换多规格时，需要合并到选中的规格上
   */
  handleMultiSpecValueJson (goodsObjData) {
    const { isMultiSpec = MULTI_ENUM.NOT_MULTI_GOODS, multiSpecValueJson } = this.data.goodsObj
    if (isMultiSpec === MULTI_ENUM.NOT_MULTI_GOODS || !multiSpecValueJson) return {}
    Object.assign(goodsObjData, {
      multiSpecValueJson
    })
  },
  /**
   * 切换商品规格时更新商品详情
   */
  handleSwitchSpec: util.debounce(function (e) {
    const { detail: goodsSn } = e
    const { specificationGoodsList = [], goodsSn: curGoodsSn } = this.data.goodsObj || {}
    if (goodsSn === curGoodsSn) return
    if (!specificationGoodsList || !specificationGoodsList.length) {
      return
    }
    const currSkuObj = specificationGoodsList.find(item => item.goodsSn === goodsSn)
    if (currSkuObj) {
      const data = {
        ...currSkuObj,
        specificationGoodsList: [...specificationGoodsList]
      }
      this.handleMultiSpecValueJson(data)
      const { goodsSn, takeawayAttr } = currSkuObj
      Object.assign(this._data.goodsReqParams, {
        takeawayAttr,
        goodsSn
      })
      const res = {
        data,
        systemTime: Date.now()
      }
      // 切换规格时，清空缓存的上一个规格的服务
      this._data.selectService = {}
      this.handleGoodsData(res)
    }
  }, 300),
  /**
   * @desc 切换一品多规弹窗展示
   */
  handleLayerTrigger (e) {
    if (e.detail) return
    // 隐藏后，绘制分享图
    this.createSharePic(this.data.goodsObj)
  },
  /**
   * 返回首页
   */
  navigateToHome(){
    wx.switchTab({
      url: '/pages/homeDelivery/index',
    })
  },
  /**
   * 返回上一页
   */
  goBack(){
    this._data.hasClickBackBtn = true
    wx.navigateBack({
      delta: 1,
      fail() {
        wx.switchTab({
          url: '/pages/homeDelivery/index'
        })
      }
    })
  },
  /**
   * 切换地址
   */
  changeAddress() {
    if (this.data.fromShareTimeLine) return
    wx.navigateTo({
      url: '/bgxxUser/pages/address/addressList/index?from=fruitGoodsDetail&settleGoodsType=B2C'
    })
  },
  /**
   * 点击按钮视频播放
   */
  videoPlay(e) {
    this.setData({
      isMuted: false,
      curr_id: e.currentTarget.dataset.id,
      isAutoPlay: false
    })
    this.play()
    // 非wifi条件下流量提示
    if (['wifi', 'none'].indexOf(this.data.networkType) === -1) {
      wx.showToast({
        title: '当前非WiFi环境播放，请注意流量消耗',
        icon: 'none'
      });
    }
  },
  /**
   * myVideo 当前视频控制
   */
  play() {
    setTimeout(() => {
      this.bannerVideoContext[this.data.curr_id].play()
    }, 1000)
  },
  /**
   * 当开始/继续播放时触发play事件
   */
  videoPlaying() {
    this.setData({
      isPlaying: true
    })
  },
  /**
   * 当暂停播放时触发 pause 事件
   */
  videoPause() {
    this.setData({
      isPlaying: false
    })
  },
  /**
   * 当播放到末尾时触发 ended 事件
   */
  videoEnded(e) {
    const { goodsObj, isAutoPlay, networkType } = this.data
    const videoLen = (goodsObj.goodsDetailHeadVideoList || []).length
    if (videoLen > 1 && isAutoPlay && networkType === 'wifi') {
      if (e.currentTarget.dataset.id === 'video_banner_0') {
        this.setData({
          curentCarouselIndex: 1,
          curr_id: 'video_banner_1',
        })
        this.play()
      } else if (e.currentTarget.dataset.id === 'video_banner_1') {
        this.setData({
          curentCarouselIndex: 0,
          curr_id: ''
        })
      }
    } else {
      this.setData({
        curr_id: ''
      })
    }
    this.setData({
      isPlaying: false
    })
  },
  /**
   * 播放出现异常
   */
  videoPlayError(e) {
    if (this.data.networkType === 'none') {
      wx.showToast({
        title: '播放失败，请检查网络连接是否正常',
        icon: 'none'
      })
    }
  },
  /**
   * 设置视频信息
   */
  setBannerVideoContext(list = []) {
    // wifi条件下视频自动播放
    wx.getNetworkType({
      success: (info) => {
        if (info.networkType === 'wifi') {
          if ((list || []).length >= 1) {
            this.setData({
              curr_id: 'video_banner_0',
              networkType: info.networkType
            })
            this.play()
          }
        }
      }
    })
    this.bannerVideoContext = {};
    if (!!list && list.length > 0) {
      for (const index of list.keys()) {
        Object.assign(this.bannerVideoContext, {
          [`video_banner_${index}`]: wx.createVideoContext(`video_banner_${index}`)
        })
      }
      this.setData({
        isVideo: true
      })
      this.playBannerValid()
    }
  },
  /**
   * banner视频不可见视窗内停止播放检测
   */
  playBannerValid() {
    this.videoObserve = wx.createIntersectionObserver(this)
    this.videoObserve.relativeTo().observe('.swiper-box', res => {
      if (res.intersectionRatio <= 0 && this.data.isPlaying && this.data.curr_id.includes('video_banner')) {
        this.bannerVideoContext[this.data.curr_id].pause()
      }
    })
  },
  /**
   * 跳转商品质检报告页
   */
  toH5page(){
    const { deliveryCenterCode } = wx.getStorageSync('timelyCity') || {}
    const { spuNumber } = this.data.goodsObj
    const url = `${config.baseUrl.H5_WEB_DOMAIN}/previewPDF/${deliveryCenterCodeEshopTransDm(deliveryCenterCode)}/${spuNumber}`;
    const pageUrl = encodeURIComponent(url)
    wx.navigateTo({
      url: `/h5/pages/commonLink/index?pageUrl=${pageUrl}`
    })

    sensors.clickReport({
      blockName: '商品信息',
      blockCode: '131012',
      element_name: '查看质检报告',
      element_code: '131012001'
    })
  },
  /**
   * 获取配送送达时间
   * @param {Object} storeInfo 商店信息
   * @param {String} storeInfo.startTime 营业开始时间
   * @param {String} storeInfo.endTime 门店关门时间
   * @param {Number} systemTime 当前时间时间戳，用于比较门店是否超过营业时间
   */
  getDeliveryTime(storeInfo, systemTime) {
    const SPEED = 29
    const tips = `最快${SPEED}分钟达`
    // 获取店铺信息
    if (!storeInfo || !systemTime) {
      return tips
    }
    // 获取门店营业时间
    const { startTime, endTime } = storeInfo
    // 如果某个为空 无法判断营业时间范围
    if (!startTime || !endTime) {
      return tips
    }
    // 系统的当前时间
    const currentTime = new Date(systemTime)
    // 当前时
    const currentHour = currentTime.getHours()
    // 当前分
    const currentMin = currentTime.getMinutes()
    // 以下精确到秒的时间戳
    // 计算当前时间时分时间戳（仅需要精确到分）
    const currentDayTimeStamp = currentHour * 3600 + currentMin * 60
    // 门店开始时间时
    const startHour = startTime ? parseFloat(startTime.split(':')[0]) : 0
    // 门店开始时间分
    const startMin = startTime ? parseFloat(startTime.split(':')[1]) : 0
    // 门店结束时间时
    const endHour = endTime ? parseFloat(endTime.split(':')[0]) : 0
    // 门店结束时间分
    const endMin = endTime ? parseFloat(endTime.split(':')[1]) : 0
    // 是否跨天
    const isCrossDay = endHour < startHour
    // 计算范围时间戳
    const dayStartTimeStamp = startHour * 3600 + startMin * 60
    const dayEndTimeStamp = (isCrossDay ? endHour + 24 : endHour) * 3600 + endMin * 60

    // 如果当前时间在营业时间范围外 则直接返回固定文字
    if (currentDayTimeStamp < dayStartTimeStamp || currentDayTimeStamp > dayEndTimeStamp) {
      return tips
    }
    // 否则返回具体时间，具体时间可能跨天 则要展示次日标识
    const arriveTime = new Date(systemTime + SPEED * 60 * 1000)
    const arriveStartTime = `${arriveTime.getHours()}`.padStart(2, 0)
    const arriveEndTime = `${arriveTime.getMinutes()}`.padStart(2, 0)
    // 跨天
    if (isCrossDay && currentDayTimeStamp > 23 * 3600 + (60 - SPEED) * 60) {
      return `最快明天${arriveStartTime}:${arriveEndTime}前送达`
    }
    return `最快今天${arriveStartTime}:${arriveEndTime}前送达`
  },

  /**
   * 导航栏高度事件触发
   * @param {Object} ev
   */
  setNavBarHeight(ev) {
    this.setData({
      navBarHeight: ev.detail
    })
  },
  carouselImgLoad(ev) {
    // console.log('carouselImgLoad', ev.currentTarget.dataset.url, this.data.placeHolderPic)
    if (!this.data.placeHolderPic.includes(ev.currentTarget.dataset.url)) {
      this._data.carouselImgLoadNum += 1
    }
    this.hidePlaceHolderPic()
  },
  carouselImgError(ev) {
    // console.log('carouselImgError', ev.currentTarget.dataset.url)
    this._data.carouselImgLoadNum += 1
    this.carouselImgAllLoaded()
  },
  async hidePlaceHolderPic() {
    if (!this.data.showPlaceHolder) return
    // 真机上会闪，加延时
    await sleep(120)
    this.setData({
      showPlaceHolder: false
    })
    await sleep(500)
    this.setData({
      curentCarouselIndex: 0
    })
  },
  carouselImgAllLoaded() {
    const hasHeadPicList = Array.isArray(this.data.detailHeadPicList) && this.data.detailHeadPicList.length
    if (this._data.carouselImgLoadNum === hasHeadPicList) {
      this.hidePlaceHolderPic()
    }
  },

  /**
   * 分享卡片生成成功
   * @param {Object} e 事件
   */
  onShareCardGenerated(e) {
    // 如果已经销毁了页面，不做处理
    if (this._data.hasClickBackBtn) {
      return
    }
    this._data.shareCard.onShareCardGenerated(e)
  }
})
