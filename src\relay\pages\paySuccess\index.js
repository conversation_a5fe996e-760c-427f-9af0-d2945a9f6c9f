const app = getApp()
const { handlePickupTime } = require('../common/orderGoods')
const { relayPaySuccessMap } = require('../../sensorReportData')
const sensors = require('../../../utils/report/sensors')
const { updateUserDeviceInfo } = require('../../../service/userService')
import { drawSharePic,getCanvasLocalImage} from '../common/shareRelay'
import { pageTransfer } from '~/utils/route/pageTransfer'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    storeName: '', // 门店名称
    qrCode:'', // 企微头像二维码
    lineText:[], // 二维码模块文案
    pickupTimeStr: '', // 提货日期
    orderNo:'',//订单号
    sharePic:'',
    navBarBgColor: '#28BC4F', // 导航栏背景颜色
    navBarColor: '#fff', // 导航栏字体颜色
    backFilter: 1, // filter brightness
  },
  _data: {
    storeInfo: {}, // 门店信息
    storeImageStatus: {},
    drawStatus:false,
    recordsList:[], //活动记录列表
    goodTotalAmount: 0,//接龙数量
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad () {
    const storeInfo = pageTransfer.receive()
    if (!storeInfo) {
      return
    }
    const { storeName = '', storeCode = '', pickupStart, pickupEnd } = storeInfo
    const pickupTimeStr = handlePickupTime(pickupStart, pickupEnd)
    this._data.storeInfo = storeInfo
    this.setData({
      storeName,
      pickupTimeStr
    })
    this.getManagerCodeByStoreCode(storeCode)
    // 上报本次支付成功的设备信息
    updateUserDeviceInfo()
  },
  onReady() {
    this.getOrderRecordsNums()
  },

  onShow() {
    // 上报神策埋点
    const sensorReporData = relayPaySuccessMap.RELAY_PAY_SUCCESS_VIEW
    if (sensorReporData) {
      //  上报页面浏览事件
      sensors.pageScreenView({
        ...sensorReporData,
        mp_JieLongID: this._data.storeInfo.activityCode
      })
    }
  },
  /**
   * 去看看，跳转接龙首页
   */
  goHome() {
    wx.redirectTo({
      url: '/relay/pages/home/<USER>'
    })
  },


  /**
   * 跳转订单详情
   */
  openOrderDetail() {
    const { storeInfo } = this._data
    const {
      mainOrderNo, subOrderNos = [], isNotMixOrder
    } = storeInfo
    // 如果不是混合单 或者 只有一个子订单，则直接跳转到订单详情
    if (isNotMixOrder || (subOrderNos.length === 1 && subOrderNos[0] === mainOrderNo)) {
      const orderObj = {
        orderNo: mainOrderNo || '',
      }
      wx.redirectTo({
        url: '/relay/pages/orderDetail/index?orderObj=' + JSON.stringify(orderObj)
      })
    } else {
      // 跳转到接龙订单列表
      wx.redirectTo({
        url: '/userB/pages/orderList/index?type=D'
      })
    }
    // 上报神策埋点
    const sensorReporData = relayPaySuccessMap.RELAY_TO_ORDER_DETAIL
    sensorReporData && sensors.trackClickEvent(sensorReporData)
  },

  /**
   * 获取门店社群二维码
   */
  async getManagerCodeByStoreCode(storeCode) {
    const result = await app.api.getManagerCodeByStoreCode({storeCode})
    const { qr_code = '', lineText } =  result.data
    this.setData({
      qrCode: qr_code,
      lineText
    })
  },
  /**
   * 获取订单记录和数量
   */
  async getOrderRecordsNums(){
    const { storeInfo = {} } = this._data
    const { mainOrderNo, activityCode = '', goodsCount: initialNum = '' } = storeInfo
    const { userID: customerID = -1 } = wx.getStorageSync('user') || {};
    const params = {
      activityCode,
      count:3,
      nextStartTime:'',
      customerID
    }
    try {
      const { data: {recordsList = [],actTotalAmountArray = {} } = {}} = await app.api.getOrderRecordsNums(params)
      // 过滤掉当前订单
      const list = recordsList.filter(item => item.orderNo !== mainOrderNo).slice(0, 2)
      this._data.recordsList = list
      this._data.goodTotalAmount = actTotalAmountArray[activityCode] || initialNum
      this.getShareImgHandle()
    } catch (error) {
      this._data.recordsList = []
      this._data.goodTotalAmount = initialNum
    }
  },
  /**
   * 商品头图
   */
   getDetailHeadPic: function() {
    const { shareImg = ''} = this._data.storeInfo
    return getCanvasLocalImage(shareImg, 'goodPic')
  },
  /**
   * 转换头像
   */
  getAvatarImg: function(){
    const { avatarUrl } = wx.getStorageSync('userNameAndImg') || {}
    return getCanvasLocalImage(avatarUrl||'https://resource.pagoda.com.cn/group1/M21/54/64/CmiLkGD5TyKASB85AAAn3XJoLPU505.png', 'avatarImg')
  },
  /**
   * 转换背景
   */
  getBgImg: function(){
    return getCanvasLocalImage('https://resource.pagoda.com.cn/dsxcx/images/bcdb3e7e7cd78bd8f096339c0ddd0b39.png', 'bgPic')
  },
  /**
   * 绘制分享图片钱缓存本地图片
   */
  getShareImgHandle(){
    const that = this
    const p = [
      that.getAvatarImg(),//用户头像
      that.getDetailHeadPic(),//商品头图
      that.getBgImg()//背景
    ]
    that._data.recordsList.forEach((item,index)=>{
      p.push(getCanvasLocalImage(item.userInfo.icon||'',  `recordAvatar${index}`))
    })
    Promise.all(p).then(res => {
      // 缓存本地图片
      res.forEach(item=>{
        that._data.storeImageStatus[item.key] = item.path
      })
      const { goodsCount:goodsAmount = '' } = this._data.storeInfo
      const params = {
        storeImageStatus:that._data.storeImageStatus,
        goodsAmount,
        goodTotalAmount: that._data.goodTotalAmount,
        recordsList: that._data.recordsList
      }
      // 绘制分享图
      drawSharePic(params)
      // 保存图片
      setTimeout(() => {
        that.canvasToTempFilePath()
      }, 300)
    }).catch( error => {
      console.log("绘制分享图片失败",error)
      // 绘制失败，重新绘制一次
      if(!this._data.drawStatus){
        that.getShareImgHandle()
        this._data.drawStatus = true
      }
    })
  },
  /**
   * 保存图片
   */
   canvasToTempFilePath () {
    const that = this
    wx.canvasToTempFilePath({
      x: 0,
      y: 0,
      width: 496,
      height: 397,
      quality: 1,
      canvasId: 'sharePicCanvas',
      success: function (res) {
        that.setData({
          sharePic:res.tempFilePath
        })
      },
      fail: function (res) {
      }
    })
  },
  /**
   * 分享
   * */
   onShareAppMessage() {
    // 上报神策埋点
    const sensorReporData = relayPaySuccessMap.RELAY_TO_SHARE_ORDER
    sensorReporData && sensors.trackClickEvent(sensorReporData)

    const { activityCode, storeCode } = this._data.storeInfo
    const params = {
      title: `这是我买的商品，店长记得帮我留一下哟~ `,
      path:`/pages/homeDelivery/index?to=relayDetail&activityCode=${
        activityCode
      }&storeCode=${
        storeCode
      }&fromSharePage=paySuccess`,
      imageUrl: this.data.sharePic,
    };
    return params;
  },
  // 子组件事件：获取自定义导航栏高度
  getNavBarHeight(ev) {
    // 这里的高度单位是px
    this.setData({
      navBarHeight: ev.detail,
    });
  },
  onPageScroll: function (ev) {
    this.setNavBarStyle(ev);
  },
  setNavBarStyle(ev) {
    const { scrollTop } = ev;
    const { navBarHeight } = this.data;
    const ratio = Math.min(scrollTop / (navBarHeight / 2), 1);
    if (this.data.ratio === ratio) return;
    if (ratio <= 0) {
      // 没有滚动距离
      this.setData({
        ratio,
        navBarBgColor: '#28BC4F',
        navBarColor: '#fff',
        backFilter: 1,
      });
    } else {
      this.setData({
        ratio,
        navBarBgColor: `rgba(255,255,255, ${ratio})`,
        navBarColor: '#222222',
        backFilter: 1 - ratio,
      });
    }
  },
  //自定义左上角返回
  back() {
    const pages = getCurrentPages();
    const lastPage = pages[pages.length - 2] || '';
    if (lastPage) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/homeDelivery/index',
      });
    }
  }
})
