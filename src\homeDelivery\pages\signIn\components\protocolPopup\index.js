
import { storeBindingsBehavior } from 'mobx-miniprogram-bindings'
import { protocolPopup, protocolPopupStore } from './service'
import { loginType } from '../../service/const'
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    loginType: {
      type: String,
      value: loginType.weixin
    }
  },
  behaviors:[storeBindingsBehavior],
  storeBindings: {
    store: protocolPopupStore,
    fields: {
      showPopup: 'show',
    },
  },
  observers: {
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    popupClose() {
      protocolPopup.hide(false)
    },
    handleAgree() {
      protocolPopup.hide(true)
    },
    tapBtn() {
      protocolPopup.hide(true)
    },
    handleDisagree(){
      protocolPopup.hide(false)
    },
    getPhoneNumber(e) {
      this.triggerEvent('getPhoneNumber', e)
      // const agree = e.detail.iv && e.detail.encryptedData
    }
  }
})
