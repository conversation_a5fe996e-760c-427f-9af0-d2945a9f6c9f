<wxs module="common" src="../../utils/common.wxs"></wxs>
<view class="big-box {{overLimit ? 'big-box-over-limit' : ''}}" wx:if="{{visible}}">
  <view class="{{cartLimit && cartLimit > 0 ? 'special-content':'special-content-single'}}">
    <text class="special-label">特价</text>
    <text wx:if="{{price !== ''}}" class="special-label-price">¥{{common.formatPrice(price)}}</text>
  </view>
  <view class="limit-content" wx:if="{{cartLimit && cartLimit > 0}}">
    <text wx:if="{{overLimit}}" class="limit-how-many">x{{cartLimit}}</text>
    <text wx:else class="limit-how-many">限{{cartLimit}}份</text>
  </view>
</view>
