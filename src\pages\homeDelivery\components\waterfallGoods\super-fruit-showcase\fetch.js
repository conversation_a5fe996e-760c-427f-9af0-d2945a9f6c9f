import {
  FruitSubject,
  moduleTypeEnum,
} from "~/service/fruitSubject";
import userStore from "~/stores/userStore";
import { getCustomerInfo } from '~/source/js/requestData/activityPrice';
import compareVersion from '~/utils/util'
import config from "~/utils/config";
const appVersion = config.appVersion
const app = getApp();

async function fetchSubjectId() {
  try {
    const { userID } = wx.getStorageSync('user') || {}
    const { cityID, storeID } = wx.getStorageSync('timelyCity') || {}
    const { data } = await app.api.getSignboardShow({
      customerID: userID || '-1',
      cityID: cityID || -1,
      storeID: Number(storeID) || -1
    })
    return data
  } catch (error) {
    console.log('fetchSubjectId err: ', error)
    return ''
  }
}

/**
 * @typedef togetherModule
 * @property {any[]} goodsList
 * @property {string} goodsType
 * @property {number} modulePriority
 * @property {string} type
 * @property {object} [signatureImageSet] 有招牌套图信息时才有这个字段
 * @property {string} signatureImageSet.carouselBottomImg 轮播图底图
 * @property {string} signatureImageSet.carouselImg 轮播图
 */

/**
 * @desc 招牌果品橱窗广告位-获取综合专题商品
 */
export async function fetchSubjectGooods(isFruitFans = userStore.isFruitFans) {
  const res = {
    adConfig: {},
    goodsList: []
  }
  try {
    const data = await fetchSubjectId()
    const { IS_VIP_CUSTOMER } = getCustomerInfo()
    if (!data) {
      return res
    }

    const subjectId = data.value
    // 8011691
    const fruitSubject = new FruitSubject({ activityID: subjectId, isNeedMerge: true })
    // const [subjectDetail, subjectConfig] = await Promise.all([
    //   fruitSubject.getDetail(),
    //   fruitSubject.getSubjectConfig(),
    // ])
    const subjectDetail = await fruitSubject.getDetail()
    const subjectConfig = await fruitSubject.getSubjectConfig()
    // 这里拿到的 togetherModuleList 每一项都包含商品列表
    /** @type {{togetherModuleList: togetherModule[]}} */
    let {
      /**综合专题详情模块数据 */
      togetherModuleList: hasGoodsDetailList,
      navigationList = [],
    } = subjectDetail
    let navPriority = []
    if (navigationList && navigationList.length > 3) {
      navPriority = navigationList.map((module) => module.priority)
    }
    let {
      togetherModuleList: configList,
    } = subjectConfig

    hasGoodsDetailList = fruitSubject.filterSignatureImageModule(hasGoodsDetailList)
    configList =  fruitSubject.filterSignatureImageModule(configList)
    if (!Array.isArray(hasGoodsDetailList)) return []
    // 指定获取综合专题的【有配置招牌套图信息】的【商品列表】模块中的商品；未配置招牌套图的商品列表则跳过不获取
    const filterList = filterStockNumLogic(hasGoodsDetailList, configList)
    // 最多可抓取并展示6个商品，超出部分不展示

    res.adConfig = data
    res.goodsList = filterStock(filterList).slice(0, 6).map(item => {
      const {goods, signatureImageSet, modulePriority} = item
      const {
        newCarouselBottomImg: carouselBottomImg,
        newCarouselImg: carouselImg
      } = signatureImageSet || {}
      const { activityPrice, retailPrice, memberPrice, goodsSn, goodsName, headPic } = goods
       // 寻找比商品楼层高的下个导航栏再往前找
       const nextNavIndex = navPriority.findIndex((v) => v > modulePriority)
       const curNavPriority = [-1,0].includes(nextNavIndex) ? modulePriority : navPriority[nextNavIndex - 1]
       const price = (function() {
        if (userStore.isFruitFansGray) {
          return (IS_VIP_CUSTOMER || isFruitFans) ? memberPrice : retailPrice
        } else {
          return memberPrice
        }
       })()
      return {
        modulePriority: curNavPriority,
        activityID: subjectId,
        goodsSn,
        goodsName,
        headPic,
        activityPrice,
        memberPrice: price,
        carouselBottomImg,
        carouselImg
      }
    })
  } catch (error) {
    console.log('fetchSubjectGooods err: ', error)
  }

  return res
}

/**
 * @desc
 * 一个楼层的商品列表只抓取一个商品,按照专题商品列表的顺序依次抓取首个有库存的商品;无有库存的商品，则展示首个售罄商品
 * 但是问题来了，综合专题的getDetail是聚合一品多规的，如果配置的多个sku，都属于一个一品多规，就会聚合到最前面的sku上
 * 举个例子：
 * a,b,c同属于一个一品多规，管理台配置综合专题时，1楼层配置a,2楼层配置b，3楼层配置c
 * 则在getDetail之后，只有1楼层有商品a，且聚合了bc，2、3楼层没有对应的b,c商品了
 *
 * 由于需求是从每个楼层取sku商品，聚合后的商品，和之前的商品对比，会存在不一致
 * 所以增加判断，当前楼层配置的sku，在当前楼层的聚合后的商品信息中，就说明这个商品是在聚合后的楼层里的
 * @param {togetherModule[]} togetherModuleList
 * @param {object} configList
 */
function filterStockNumLogic(togetherModuleList, configList) {
  const allGoodsMap = addAllGoodsMap(togetherModuleList)
  return configList.reduce((acc, config) => {
    const type = config.type

    //  图片模块
    if (type === moduleTypeEnum.图片) {
      const {
        modulePriority,
        activityPicList,
        signatureImageSetList,
      } = config

      if (!Array.isArray(activityPicList) ||!activityPicList.length) {
        return acc
      }

      const pic = activityPicList[0]
      /**商品信息 */
      const goodsInfo = pic.goodsInfo
      /**支持版本号 */
      const supportAppVersion = pic.supportAppVersion

      if (!Array.isArray(signatureImageSetList) || !signatureImageSetList.length) {
        return acc
      }

      if (!goodsInfo) {
        return acc
      }

      signatureImageSetList.sort((a, b) => {
        // isVip为true的排在前面
        return b.isVip - a.isVip
      })

      const signatureImageSet = signatureImageSetList[0]

      if (!signatureImageSet || !signatureImageSet.newCarouselBottomImg || !goodsInfo) {
        return acc
      }

      const curFloorMap = allGoodsMap.get(modulePriority)
      if (!curFloorMap) {
        return acc
      }

      const goodsDetail = curFloorMap.get(goodsInfo.eshopGoodsId)

      return [...acc,
        {
          goods: goodsDetail,
          signatureImageSet,
          modulePriority,
          type: type,
      }]
    }
    //  商品列表模块
    else {
      const {
        goodsList,
        signatureImageSet,
        modulePriority,
      } = config

      if (!signatureImageSet || !signatureImageSet.newCarouselBottomImg || !goodsList || !goodsList.length) {
        return acc
      }

      const curFloorMap = allGoodsMap.get(modulePriority)
      if (!curFloorMap) {
        return acc
      }
      const [hasStock, noStock] = goodsList.reduce((goodsResult, goods) => {
        // const goodsDetail = curFloorMap.get(goods.goodsId)
        const goodsDetail = curFloorMap.get(goods.eshopGoodsId)
        if (!goodsDetail) return goodsResult
        goodsResult[goodsDetail.stockNum ? 0 : 1].push(goodsDetail)
        return goodsResult
      }, [[], []])
      // 找不到则说明都是售罄，直接取售罄第一个
      if (hasStock.length) {
        return [...acc,
          {
            goods: hasStock[0],
            signatureImageSet,
            modulePriority,
            type: type,
          }]
      }
      return [...acc,
      {
        goods: noStock[0],
        signatureImageSet,
        modulePriority,
        type: type,
      }]
    }
  }, [])
}

/**
 * @desc 把所有楼层的商品添加到一个map中
 * @param {togetherModule[]} togetherModuleList
 * @return { Map<floorIndex, Map<goodsSn, Object>> }
 */
function addAllGoodsMap(togetherModuleList) {
  const allGoodsMap = new Map()
  togetherModuleList.forEach(module => {
    //  图片模块
    if (module.type === moduleTypeEnum.图片) {
      const {
        modulePriority,
        activityPicList,
        signatureImageSetList,
      } = module

      if (!Array.isArray(activityPicList) || !activityPicList.length) {
        return
      }

      const pic = activityPicList[0]
      const goodsInfo = pic.goodsInfo

      if (!goodsInfo) {
        return
      }

      if (!Array.isArray(signatureImageSetList) || !signatureImageSetList.length) {
        return
      }

      const curFloorMap = new Map()
      addFloorGoods(goodsInfo, curFloorMap)
      allGoodsMap.set(modulePriority, curFloorMap)
    }
    //  商品列表模块
    else {
      const { goodsList, modulePriority, signatureImageSet } = module
      if (!goodsList.length || !signatureImageSet) return
      const curFloorMap = new Map()
      goodsList.forEach(goods => addFloorGoods(goods, curFloorMap))
      allGoodsMap.set(modulePriority, curFloorMap)
    }
  })
  return allGoodsMap
}

/**
 * @desc 把商品添加到楼层商品map中
 * @param {object} goods
 * @param {Map} curFloorMap
 */
function addFloorGoods(goods, curFloorMap) {
  // 综合专题用的是电商id
  const { specificationGoodsList, eshopGoodsId } = goods

  // 没有规格商品列表，直接添加
  if (!Array.isArray(specificationGoodsList) || !specificationGoodsList.length) {
    curFloorMap.set(eshopGoodsId, goods)
    return
  }

  // 有规格商品列表，添加规格商品
  specificationGoodsList.forEach(spec => {
    curFloorMap.set(spec.eshopGoodsId, spec)
  })
}

/**
 * @desc 售罄沉底
 */
function filterStock (list) {
  const [hasStock, noStock] = list.reduce((acc, cur) => {
    const { goods } = cur
    acc[goods.stockNum ? 0 : 1].push(cur)
    return acc
  }, [[], []])
  return [...hasStock, ...noStock]
}
