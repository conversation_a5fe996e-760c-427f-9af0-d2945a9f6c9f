const commonObj = require('../../../source/js/common').commonObj;
const orderListMixin = require('./orderListMixin')
const app = getApp();
const sensors = require('../../../utils/report/sensors')
const { wrapSensorReportFn } = require('../../../utils/report/index')
// const txSensor = require('../../utils/report/txSensor')
//  倒计时混入
const countDownMixin = require('./countDownMixin')
//  倒计时混入
const updateOrderMixin = require('./updateOrderMixin')
const orderBtn = require('../../../common/orderBtn')
const { refundInfo } = require('../orderLogic/refundInfo')
const {
  orderHeadIconMap
} = require('../../../utils/config')
var coordtransform = require('../../../utils/coordUtil')
const { handleReceiveTime, mapSnapShotDetail, addGoodsSnMap } = require('../orderLogic/orderGoods')

const orderMixin = require('../orderLogic/orderMixin')
// orderMixin里使用
const { genClickSensor } = require('../../../utils/report/index')
const { clickParamsMap } = require('./sensorClickParams')

const { createStoreBindings } = require('mobx-miniprogram-bindings')
const swiperStore = require('./store/index')
const util = require('../../../utils/util');
const mouthCardIcon = "https://fastdfs-prod-1251596386.cos.ap-guangzhou.myqcloud.com/eshop/c63abe0eca6452552c25b38d4fa5437d.png"
const yearCardIcon = "https://fastdfs-prod-1251596386.cos.ap-guangzhou.myqcloud.com/eshop/8a63cbbfed7165e8bc1ce410d6b0a4a2.png"
const yearCardType = ['Y','XY']
/**
 * @typedef TLoadOption
 * @property { boolean } loadMore 加载更多内容，为true时根据时间戳加载下一页内容，为false时清空时间戳加载第一页内容
 * @property { number } count 加载数量
 */

/**
 * @typedef TRequestPayload 请求函数载体
 * @property { number } count 加载数量
 * @property { string } timeStamp 分页依据时间戳
 */

/**
 * @typedef { Promise<Array<any>> } TRequestResult 请求函数返回结果
 */

Page({

  mixins: [orderListMixin, orderMixin, countDownMixin, updateOrderMixin],
  data: {
    //  A为及时达订单，B为拼团订单，C为门店订单, D为接龙订单, E次日达订单, F全国送订单,G试吃订单
    orderClass: "A",
    //  是否已登录
    isLogin: true,
    //  正在请求
    loading: false,

    //  全国送使用的订单数组
    realTimeOrderList: [],
    //  全国送使用的订单数组
    b2cOrderList: [],
    //  拼团使用的订单数组
    fightGroupOrderList: [],
    //  门店使用的订单数组
    storeOrderList: [],
    //  接龙使用的订单数组
    chainsOrderList: [],

    showCustomConfirmModal: false,

    //  及时达使用的分页时间戳
    timelyUpdate: '',
    //  全国送使用的分页时间戳
    b2cUpdate: '',
    //  拼团列表使用的分页时间戳
    lastFightGroupUpdate: '',
    //  门店列表使用的分页时间戳
    lastStoreUpdate: '',
    //  接龙列表使用的分页时间戳
    relayTimeUpdate: '',

    countFlag: true,

    headPicList: [], // 商品头图列表
    spuNumberList: [], // spu编码集合
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    // picUrl: 'https://resource.pagoda.com.cn',
    defaultHeadPic: 'https://resource.pagoda.com.cn/group1/M21/8C/51/CmiLkGJnsu-AKE_SAAAIGee3pVE401.png', // 无商品头图时展示默认图片
    orderFlowState: {
      'WAIT_PAY': '待付款',
      'PAYED': '已付款',
      'STOCKING': '备货中',
      'SENDING': '配送中',
      'WAIT_PICKUP': '待自提',
      'CANCELED': '已取消',
      'RECEIVED': '已收货',
      // 'PICKED_UP': '已自提',
      'PICKED_UP': '交易成功',
      'COMPLAINED': '已投诉',
      'REJECT': '已拒收',
      'REVIEWING': '退款审核中',
      'NOT_REFUND': '退款失败',
      'REFUNDED': '已退款',
      'GROUPBUY_PROCESS': '待成团'
    },
    status: {
      'WAIT_PAY': '待付款',
      'GROUPBUY_PROCESS': '待成团',
      'WAIT_PICKUP': '待提货',
      // 'PICKED_UP': '已提货',
      'PICKED_UP': '交易成功',
      'CANCELED': '已取消',
      'REFUNDED': '已退款',
      'COMPLAINED': '已投诉',
      'REVIEWING': '退款审核中',
      'NOT_REFUND': '退款失败'
    },
    storeStatus: {
      'REFUNDED': '已退款',
      'COMPLAINED': '已投诉',
      'REVIEWING': '退款审核中',
      'NOT_REFUND': '退款失败'
    },
    complaintStatus: { // 投诉订单状态 黄色样式标识
      'REFUNDED': '已退款',
      'COMPLAINED': '已投诉',
      'REVIEWING': '退款审核中',
      'NOT_REFUND': '退款失败'
    },
    isShare: false,
    listItem: {},
    getUrl: commonObj.PAGODA_DSN_DOMAIN,
    paymentDialog: false,
    pagodaMoney: true,
    orderCancelParams: null, // 取消订单（未超时）, 保存请求接口的参数（待合并弹窗取消原因）
    tabList: [{
        type: 'A',
        label: '及时达',
        sensorsKey: 'categoryOrderTab'
      },
      {
        type: 'E',
        label: '次日达',
        sensorsKey: 'xxshopOrderTab'
      },
      {
        type: 'D',
        label: '接龙',
        sensorsKey: 'chainsOrderTab'
      },
      {
        type: 'C',
        label: '门店订单',
        sensorsKey: 'storeOrderTab'
      },
      {
        type: 'F',
        label: '全国送',
        sensorsKey: 'nationalDeliveryOrderTab'
      }
    ],
    orderHeadIconMap,
    isCycleBuy: false,
    groupBtnClass: {
      payImmediatelyBtn: 'wait-pay',
      confirmReceiveBtn: 'confirm-receive'
    },
    showCancelTips:false, //是否显示取消订单失败提示,
    showB2cConfirmModal: false, // b2c未全部发货状态下确认收货弹窗
    b2cConfirmModalText: '1、如商品延迟发货，请您耐心等待几天哦~ \n 2、如商品无法发出，请您先确认收货后申请退款。',
    minHeight: 0,
    orderListHeight: 0,
    // 是否展示三无退提示弹窗
    showSanwuTip: false,
    // 是否是通过扫码退方式进入该页面
    isScan: false,
    // 随单充值信息
    rechargePayInfo: null,
  },
  _data: {
    offlineOrderListFinshed: false,
    curOrderInfo: {}, // 在立即付款按钮处理事件里赋值
    iconOrderClassMap: {
      'A': '及时达',
      'F': 'b2c',
      'G': '试吃自提'
    },
    getOrderListReq: {},
    isCombineOrder: false,
    posterPic: {}, // 存储分享海报
    isOrderList: true
  },

  onLoad: function (options) {
    this.storeBindings = createStoreBindings(this, {
      store: swiperStore,
      fields: {
        swiperIndex: 'swiperIndex',
        swiperDuration: 'swiperDuration'
      },
      actions:{
        updateSwiperActive: 'updateSwiperActive',
        stopSwiperAnimate: 'stopSwiperAnimate',
        startSwiperAnimate: 'startSwiperAnimate'
      }
    })
    const timelyCity = wx.getStorageSync('timelyCity') || {};
    var that = this;
    this.setData({
      userInfo: wx.getStorageSync("userNameAndImg") || {},
      timelyCity,
      isScan: !!options.isScan
    })

    //  页面首次进入时指定了类型
    this.setSwiperIndex(options.type)
  },

  async onShow () {
    //  正在锁定重新加载（打开拨号，点击导航地图等操作无需刷新列表）
    if (this.data.lockOnShowReload) {
      this.setData({ lockOnShowReload: false })
      return
    }

    sensors.pageScreenView()
    const user = wx.getStorageSync('user')
    this.setData({
      user,
      tabList: this.data.tabList,
      showCustomConfirmModal: false
    })

    // 判断是否登录
    if (app.checkSignInsStatus()) {
      this.setData({
        isLogin: true
      })
    } else {
      this.setData({
        isLogin: false
      })
      return
    }

    //  从其他页面返回，需要更新单个订单
    if (this.data.mix_updateOrder_singleUpdate) {
      this.mix_updateOrder_backToListHandle()
    }
    //  从其他页面进入/返回
    else {
      let newOrderClass
      // 如果从心享订单支付中或支付成功页面或首页多个待自提商品进入,则显示次日达页面
      const prePageName = app.globalData.prePageName

      //  次日达相关页面返回
      if (['payLoading', 'paySuccess', 'selfOrderModule'].includes(prePageName)) {
        newOrderClass = 'E'
      }
      //  拼团相关页面返回
      if (app.globalData.formWxPublicToOrderList) {
        newOrderClass = 'B'
      }

      //  页面返回时指定了指定订单类型，进行swiper更新
      if (newOrderClass) {
        this.setSwiperIndex(newOrderClass)
      }

      //  加载当前列表的数据
      this.getList()
    }
  },

  /**
   * 设置swiper无动画的index更新
   * @param { string } orderClass 订单类型
   */
  setSwiperIndex(orderClass = 'A') {
    this.setData({
      userInfo: wx.getStorageSync("userNameAndImg") || {},
      orderClass: orderClass
    })
    const index = this.data.tabList.findIndex(item => item.type === orderClass);
    this.stopSwiperAnimate()

    wx.nextTick(() => {
      this.updateSwiperActive(index)
      this.startSwiperAnimate()
    })
  },

  clickSensorParamsFn: genClickSensor(clickParamsMap),

  onHide: function () {
    app.globalData.prePageName = ''

    this.setData({
      paymentDialog: false,
      isShare: false,
      isThumbnail: false,
      shareImage: '',
      orderCancelIsShow: false,
      payPopFlag: false,
      tipPopFlag: false,
      tipConfirmFlag: false,
      pickedUpFlag: false,
      showCancelTips:false
    })

    app.globalData.formWxPublicToOrderList = false
  },

  /**
   * 页面卸载时
   */
  onUnload: function () {
    this.storeBindings.destroyStoreBindings()
    app.globalData.prePageName = '';
    app.globalData.formWxPublicToOrderList = false
  },

  /**
   * 点击nav
   * @param {*} e
   */
  bindOrderTap(e) {
    const {
      status: orderClass,
      sensorskey,
      index,
      isclicktab
    } = e.currentTarget.dataset

    if (orderClass === this.data.orderClass) {
      return
    }

    this.switchOrderType({
      orderClass: orderClass,
      sensorskey,
      index,
      isclicktab
    })
  },

  /**
   * 切换订单类型
   * @param { Object } option
   * @param { string } option.orderClass 订单类型
   * @param { string } option.sensorskey 神策键名
   * @param { number } option.index 当前点击tab下标
   * @param { boolean } option.isclicktab 订单类型切换是否为点击tab切换
   */
  switchOrderType({
    orderClass,
    sensorskey,
    index,
    isclicktab
  }) {
    // 未增加全国送埋点
    if (orderClass !== 'F') {
      sensorskey && sensors.track('MPClick', sensorskey)
    }

    this.setData({
      //  当前列表留5条数据，是为了后续滚动回来能够看到数据。
      [this.getCurrentListName()]: this.getCurrentList().slice(0, 5),
      orderClass
    })

    if (!this.data.isLogin) {
      return
    }

    //  点击tab切换时，不需要swiper的滑动动画
    if (isclicktab) {
      this.stopSwiperAnimate();
    }

    wx.nextTick(() => {
      this.updateSwiperActive(index)
      //  点击tab切换结束后，将swiper的滑动动画还原
      if (isclicktab) {
        this.startSwiperAnimate()
      }

      //  点击tab或滑动都需要滚动到顶部
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      })

      this.getList(false)
    });
  },

  /**
   * 根据订单类型获取对应列表数据并渲染
   * @param { boolean } fullPull 为true时，根据已有长度拉取全部数据。为false时，仅拉取前10条初始数据
   */
  async getList(fullPull = true) {
    //  列表切换时，清空上一个列表的倒计时定时器
    this.mix_countDown_clearCountDownArr()
    const orderList = this.getCurrentList()

    //  只有拉取初始数据时需要打开loading
    this.setLoading(!orderList.length)

    let count = 10

    //  拉取全部数据
    if (fullPull) {
      count = orderList.length < 10 ? 10 : orderList.length
    }

    const option = {
      loadMore: false,
      count
    }

    this.renderCurrentList(option)
  },

  /**
   * 页面上拉触底事件，加载更多内容
   * @returns
   */
  async onReachBottom() {
    if (this.data.loading) return;
    this.setLoading()

    const option = {
      loadMore: true
    }

    this.renderCurrentList(option)
  },

  /**
   * 下拉刷新回调，清空时间戳及列表数据。并重新拉取数据
   */
  async onPullDownRefresh () {
    this.mix_countDown_clearCountDownArr()
    wx.stopPullDownRefresh()
    this.setLoading()

    const option = {
      loadMore: false
    }

    this.renderCurrentList(option)
  },

  /**
   * 获取当前订单类型列表名称
   * @returns { string }
   */
  getCurrentListName() {
    const orderListNameMap = {
      A: 'realTimeOrderList',
      E: 'mix_bgxx_orderList',
      F: 'b2cOrderList',
      B: 'fightGroupOrderList',
      C: 'storeOrderList',
      D: 'chainsOrderList'
    }
    return orderListNameMap[this.data.orderClass]
  },

  /**
   * 获取当前订单类型列表
   * @returns { string }
   */
  getCurrentList() {
    return this.data[this.getCurrentListName()]
  },

  /**
   * 根据订单类型，渲染当前订单列表
   * @param { TLoadOption } option
   */
  async renderCurrentList(option) {

    const orderListFnMap = {
      A: this.getTimeOrderList,
      E: this.mix_bgxx_getOrderList,
      F: this.getB2cOrderList,
      B: this.getFightOrderList,
      C: this.getStoreOrderList,
      D: this.getChainsOrderList,
    }
    await orderListFnMap[this.data.orderClass](option)

    this.hideLoading()
    //  重新设置swiper高度
    this.refreshRectSize()
  },

  /**
   * 获取当前订单类型的数据请求函数
   * @returns { (requestPayload: TRequestPayload) => TRequestResult }
   */
  getCurrentListAjaxFn() {
    const orderListAjaxFnMap = {
      A: params => this.getOrderListData({ ...params, orderType: [0, 10, 90] }),
      E: this.mix_bgxx_getBgxxOrderListData,
      F: params => this.getOrderListData({ ...params, orderType: [0, 130] }),
      B: this.getFightOrderListData,
      C: this.getStoreOrderListData,
      D: this.getChainsOrderListData
    }
    return orderListAjaxFnMap[this.data.orderClass]
  },

  /**
   * 打开loading相关操作
   */
  setLoading(showLoading = true) {
    if (showLoading) {
      wx.showLoading({
        title: '加载中'
      })
    }

    this.setData({
      loading: true
    })
  },

  /**
   * 关闭loading相关操作
   */
  hideLoading() {
    wx.hideLoading()

    this.setData({
      loading: false
    })
  },
  /**
   * 获取及时达列表
   * @param { TLoadOption } param0
   * http://op.pagoda.com.cn/#/technical-doc?docId=869&anchor=%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AF-0
   * @returns
   */
  async getTimeOrderList ({
    loadMore = false,
    count = 10
  }) {
    if (!this.hasLogin()) {
      return
    }

    const params = {
      orderType: [0, 10, 90],
      count,
      timeStamp: loadMore ? this.data.timelyUpdate : ''
    }

    const handleResult = await this.getOrderListData(params)

    if (!handleResult.length) {
      return;
    }

    const result = loadMore ? this.data.realTimeOrderList : []
    result.push(...handleResult)

    this.setData({
      realTimeOrderList: result,
      timelyUpdate: result[result.length - 1].createTime
    })
  },

  /**
   * 请求订单列表数据（及时达/全国送共用）
   * @param { TRequestPayload } otherParams
   * @returns { TRequestResult }
   */
  async getOrderListData({ count, timeStamp, orderType }) {
    const params = {
      customerID: this.data.user.userID,
      sort: "desc",
      customerType: 1,
      optionalInfo: [
        'batchPayment',
        'store',
        'comment',
        'tradeRefundInfo',
        'itemSelectService',
        'invoice',
        'checkNoReasonRefund',
        'consumables',
        'extend',
        'relation'
      ],
      saleModel: 1,
      orderChannel: 5,
      count,
      orderType
    }

    //  存在分页时间戳时才需增加以下参数
    if (timeStamp) {
      params.action = 'next'
      params.searchOrderTime = timeStamp
    }

    const result = await app.api.getOrderList(params)
    const { data, systemTime } = result

    if (!data || !data.length) {
      return [];
    }

    await mapSnapShotDetail(data)

    if (this.data.orderClass === 'A') {
      this.markConsumablesOrder(data)
    }

    //  数据处理逻辑
    const handleResult = this.reduceOrder(data, 'orderNo').reduce((prev, order) => {
      const {
        payment = {},
        orderType = {},
        orderStatus = {},
        expressInfo,
        endDispatchTime,
        startDispatchTime,
        eshopInfo = {},
        dispatchType = {},
        paymentStatus = {},
        vipCardInfo = {},
        relation = [],
      } = order
      const goodsSnItemMap = {}
      addGoodsSnMap(order.items, goodsSnItemMap)
      // // 匹配发货时间
      order.expectedReceiveTime = handleReceiveTime(
        expressInfo,
        goodsSnItemMap
      );

      const isEshopOrder = !!Object.keys(eshopInfo).length
      const {
        totalPrice = 0,
        logicalExpirationTime = '' // 支付过期时间
      } = payment

      this.genGoodsInfo(order)
      // 月卡金额,未支付订单不做计算,赋值0
      let vipCardMoney = 0
      const isNotPay = paymentStatus.desc === '未支付'
      // 月卡信息 也算作一件商品
      if(vipCardInfo.type){
        const headPic = yearCardType.includes(vipCardInfo.type) ? yearCardIcon : mouthCardIcon
        order.items.push({
          headPic,
          quantity:1
        })
        vipCardMoney = isNotPay ? 0 : vipCardInfo.actualAmount
      }
      // 随单充值信息
      const relationInfo = relation.find(item => item.relationType === 3)
      let sum = 0;
      order.items.forEach(item => sum += parseInt(item.quantity))
      const showCountDown = (() => {
        // 老电商下的自提单不展示倒计时
        if (isEshopOrder && dispatchType.desc === '自提配送') return false
        return paymentStatus.desc === '未支付' && logicalExpirationTime && orderStatus.orderStatusDesc !== '已取消'
      })();
      const isTryEat = orderType.desc === '试吃自提'
      const rechargeInfo = relationInfo
        ? JSON.parse(relationInfo.extData)
        : {}
      // 已支付的情况,totalPrice中不会有充值金额
      // 需要加上rechargeInfo中的actualAmount
      const rechargeAmount = isNotPay || !relationInfo ? 0 : rechargeInfo.actualAmount
      Object.assign(order, {
        // 格式化时间
        showTime: this.formatMiddleOrderTime(order),
        dispatchTimeRange: this.formatDispatchTime({
          start: startDispatchTime,
          end: endDispatchTime,
          isTryEat,
        }),
        rechargeInfo: relationInfo ? rechargeInfo : void 0,
        originPayAmount: totalPrice,
        payAmount: String((totalPrice / 100).toFixed(2)).split('.'), //获取支付价格
        totalCount: sum,
        showCountDown,
        totalPriceAddVipCardMoney: totalPrice + vipCardMoney + rechargeAmount,
        orderClassIcon: isTryEat ? this._data.iconOrderClassMap['G'] : this._data.iconOrderClassMap[this.data.orderClass],
      }, orderType.desc ? {
        btnList: orderBtn.genBtnList({
          orderInfo: order,
          orderType: orderType.desc,
          orderStatus: order.orderStatus.orderStatusDesc
        }),
        refundInfo: (() => {
          const info = refundInfo(order)
          const { status = '', noReasonRefundMoney = '' } = info
          // 退款成功状态需要加退款金额
          if (status === '已退款') Object.assign(info, {status: `${status} ￥${Number((noReasonRefundMoney / 100).toFixed(2))}`})
          // 退款关闭 且 可以三无退
          if (status === '退款关闭' && (order.canRefund || order.canNoReasonRefund)) {
            info.status = ''
          }
          return info
        })()
      } : {})

      //  此订单需要展示倒计时
      if (showCountDown) {
        this.mix_countDown_setOrderCountDown({
          order,
          orderNo: order.orderNo,
          expireTime: logicalExpirationTime,
          systemTime
        }, () => {
          //  及时达/全国送，订单倒计时结束逻辑
          this.countDownFinish(this.data.orderClass, order)
        })
      }

      prev.push(order)
      return prev
    }, [])
    return handleResult
  },

  /**
   * 获取全国送列表
   * @param { TLoadOption } param0
   * http://op.pagoda.com.cn/#/technical-doc?docId=869&anchor=%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AF-0
   */
   async getB2cOrderList ({
    loadMore = false,
    count = 10
   }) {
    if (!this.hasLogin()) {
      return
    }

    const params = {
      orderType: [0, 130],
      count,
      timeStamp: loadMore ? this.data.b2cUpdate : ''
    }

    const handleResult = await this.getOrderListData(params)

    if (!handleResult.length) {
      return;
    }

    const result = loadMore ? this.data.b2cOrderList : []
    result.push(...handleResult)

    this.setData({
      b2cOrderList: result,
      b2cUpdate: result[result.length - 1].createTime
    })
  },

  /**
   * 标记耗材商品订单
   * （商品为纯耗材时，不允许评价）
   * @param { Array<TOrder> } orderList 订单列表
   */
  markConsumablesOrder(orderList) {
    orderList.forEach(order => {
      //  耗材商品列表
      const consumablesList = order.items.filter(item => {
        const combinationGoodsJson = item.combinationGoodsJson

        //  只有一个投入品
        if (combinationGoodsJson && combinationGoodsJson.length === 1) {
          const basicGoodsSn = combinationGoodsJson[0].basicGoodsSn || ''

          //  basicGoodsSn为5开头，为耗材商品
          return basicGoodsSn.startsWith('5')
        }
        return false
      })

      //  纯耗材商品订单不允许评价
      if (order.items.length === consumablesList.length) {
        order.isCanComment = false
      }
    })
  },

  // 生成商品列表
  // 混合单 mixOrderList
  // 其他单 items
  genGoodsInfo (orderInfo) {
    const { mixOrderList, items } = orderInfo
    const goodsInfoList = []
    if (Array.isArray(mixOrderList) && mixOrderList.length) {
      mixOrderList.forEach(mix => {
        goodsInfoList.push(...mix.items)
      })
      orderInfo.items = goodsInfoList
      delete orderInfo.mixOrderList
    }
  },

  /**
   * 获取拼团订单列表数据
   * @param { TRequestPayload } param0
   * @returns { TRequestResult }
   */
  getFightOrderListData({ count, timeStamp }) {
    let createTime
    let action

    if (timeStamp) {
      createTime = timeStamp
      action = 'up'
    } else {
      createTime = '-1'
      action = 'down'
    }

    const params = {
      //  http://wiki.pagoda.com.cn/pages/viewpage.action?pageId=7637430
      url: '/api/v1/wxmini/order/list',
      data: {
        type: 'C',
        customerID: this.data.user.userID,
        orderStatus: 1,
        orderShowType: 'GROUPBUY',
        action,
        createTime,
        count
      }
    }

    return new Promise(resolve => {
      commonObj.requestData(params, result => {
        const { data: { data } } = result

        if (data && data.length) {
          data.forEach((item) => { //待付款和待成团计时、格式化价格
            item.goodsList[0].groupPrice = String((item.goodsList[0].groupPrice / 100).toFixed(2)).split('.');
            item.goodsList[0].price = String((item.goodsList[0].price / 100).toFixed(2)).split('.');
            item.goodsList[0].originalPrice = item.goodsList[0].originalPrice ? (item.goodsList[0].originalPrice / 100).toFixed(2) : '';
          })

          const handleResult = this.reduceOrder(data, 'payOrderID');

          resolve(handleResult)
        } else {
          resolve([])
        }
      })
    })
  },

  /**
   * 获取拼团订单列表
   * @param { TLoadOption } param0
   * @returns
   */
  async getFightOrderList ({
    loadMore = false,
    count = 10
  }) {
    const createTime = loadMore ? this.data.lastFightGroupUpdate : ''

    if (!this.hasLogin()) {
      return
    }

    const handleResult = await this.getFightOrderListData({
      timeStamp: createTime,
      count
    })

    if (!handleResult.length) {
      return
    }

    const result = loadMore ? this.data.fightGroupOrderList : []
    result.push(...handleResult)

    this.setData({
      fightGroupOrderList: result,
      lastFightGroupUpdate: result[result.length - 1].createTime,
    })
  },

  /**
   * 获取门店订单列表数据
   * @param { TRequestPayload } param0
   * @returns { TRequestResult }
   */
  async getStoreOrderListData({ timeStamp }) {
    const { data: result } = await app.api.getOfflineOrderList({
      customerID: this.data.user.userID,
      lastTime: timeStamp,
    })

    return result
  },

  /**
   * 获取门店订单列表
   * @param { TLoadOption } param0
   * @returns
   */
  async getStoreOrderList ({
    loadMore = false,
    count = 10
  }) {
    const lastUpdate = loadMore ? this.data.lastStoreUpdate : ''

    if (this._data.offlineOrderListFinshed || !this.hasLogin()) {
      return
    }

    const result = await this.getStoreOrderListData({
      count,
      timeStamp: lastUpdate
    })

    const orderListLen = result.orderList.length

    this._data.offlineOrderListFinshed = orderListLen < count

    if (!orderListLen) {
      return
    }

    const { storeOrderList, headPicList } = this.data

    storeOrderList.push(...result.orderList)
    headPicList.push(...result.headPicList)

    this.setData({
      storeOrderList,
      headPicList,
      lastStoreUpdate: result.orderList[result.orderList.length - 1].tradeTime
    })
  },

  /**
   * 请求接龙订单列表数据，处理数据逻辑并返回最终结果
   * @param { TRequestPayload } param0
   * @returns { TRequestResult }
   */
  async getChainsOrderListData({ count, timeStamp }) {
    const params = {
      customerID: this.data.user.userID,
      sort: "desc",
      customerType: 1,
      optionalInfo: ['batchPayment', 'store', 'comment', 'tradeRefundInfo', 'itemSelectService', 'invoice', 'checkNoReasonRefund', 'preferential'],
      saleModel: 1,
      orderType: [150],
      orderChannel: 5,
      count
    }

    //  存在分页时间戳时才需增加以下参数
    if (timeStamp) {
      params.action = 'next'
      params.searchOrderTime = timeStamp
    }

    const result = await app.api.getRelayOrderList(params)
    const { data, systemTime } = result

    if (!data || !data.length) {
      return [];
    }

    //  数据逻辑处理
    const handleResult = this.reduceOrder(data, 'orderNo').reduce((prev, order) => {
      const {
        payment = {},
        orderStatus = {},
        items,
        paymentStatus = {}
      } = order
      // 支付过期时间
      const { logicalExpirationTime = '', totalPrice } = payment

      let sum = 0
      items.forEach(item => sum += parseInt(item.quantity))

      const showCountDown = paymentStatus.desc === '未支付' && logicalExpirationTime && orderStatus.orderStatusDesc !== '已取消'

      Object.assign(order, {
        showCountDown,
        totalCount: sum,
        originPayAmount: totalPrice,
        refundInfo: (() => {
          const info = refundInfo(order)
          const { status = '', noReasonRefundMoney = '' } = info
          // 退款成功状态需要加退款金额
          if (status === '已退款') Object.assign(info, {status: `${status} ￥${Number((noReasonRefundMoney / 100).toFixed(2))}`})
          // 退款关闭 且 可以三无退
          if (status === '退款关闭' && (order.canRefund || order.canNoReasonRefund)) {
            info.status = ''
          }
          return info
        })()
      })


      // 倒计时
      if (showCountDown) {
        this.mix_countDown_setOrderCountDown({
          order,
          orderNo: order.orderNo,
          expireTime: logicalExpirationTime,
          systemTime
        }, () => {
          //  接龙订单倒计时结束取消订单，记录需要更新的订单
          this.mix_updateOrder_recordOrder(order)

          //  接龙订单倒计时结束逻辑
          this.relayCountDownFinish(order)
        })
      }

      prev.push(order)
      return prev
    }, [])

    //  返回最终处理结果
    return handleResult
  },

  /**
   * 获取接龙订单列表
   * @param { TLoadOption } param0
   * @returns
   */
  async getChainsOrderList({
    loadMore = false,
    count = 10
  }) {

    if (!this.hasLogin()) {
      return
    }

    const params = {
      timeStamp: loadMore ? this.data.relayTimeUpdate : '',
      count
    }

    const handleResult = await this.getChainsOrderListData(params)

    if (!handleResult.length) {
      return
    }

    const result = loadMore ? this.data.chainsOrderList : []
    result.push(...handleResult)

    this.setData({
      chainsOrderList: result.map(v => {
        (v.items || []).forEach(item => {
          item.headPic = util.toHttps(item.headPic)
        })
        return v
      }),
      relayTimeUpdate: result[result.length - 1].createTime
    })
  },
  //判断是否登录
  hasLogin: function () {
    return app.checkSignInsStatus()
  },


  /**
   * 跳转订单详情（次日达在次日达混入中处理）
   * @param {*} e
   */
  toOrderDetail: function (e) {
    //  在安卓下有bug：快速点击支付按钮，导致穿透了订单列表，跳转了订单详情
    if (this.afterPayDialogClose) {
      setTimeout(() => {
        this.afterPayDialogClose = false
      }, 1000)
      return
    }
    let {
      sensorskey,
      orderid: goodsOrderID,
      payid: payOrderID,
      paystatus: payStatus,
      orderticket: orderTicket,
      goodsid: goodsID,
      orderinfo: orderInfo
    } = e.currentTarget.dataset
    const orderClass = this.data.orderClass

    // 及时达订单 b2c订单
    if (['A', 'F'].includes(orderClass)) {
      // 目前只有及时达的埋点
      sensorskey = 'categoryOrderListToDetail'
      const { orderNo, payment = {}, paymentStatus = {} } = orderInfo
      const myAllOrderObj = {
        isAllOrder: true,
        goodsOrderID: orderNo,
        payOrderID: payment.tradeNo,
        payStatus: paymentStatus.desc,
        type: orderClass
      }
      wx.navigateTo({
        url: '/userB/pages/allOrderDetail/index?myAllOrderObj=' + JSON.stringify(myAllOrderObj)
      })
    }
    //  拼团订单
    else if (orderClass === "B") {
      var myAllOrderObj = {
        isAllOrder: true,
        goodsOrderID,
        payOrderID,
        payStatus
      }
      wx.navigateTo({
        url: '/fightGroups/pages/PTorderDetail/index?myAllOrderObj=' + JSON.stringify(myAllOrderObj)
      })
    }
    //  门店订单
    else if (orderClass === "C") {
      // 如果是瞬间退 则需要保留之前的埋点逻辑
      if (this.data.isScan) {
        this.trackClickEvent({
          element_code: 160000004,
          element_name: '订单',
          element_id: '',
          element_content: '订单',
          screen_code: '1600',
        })
      }
      const { isDmOrder } = orderInfo
      var myAllOrderObj = {
        isAllOrder: true,
        goodsOrderID: orderTicket,
        type: orderClass,
        isDmOrder: Number(isDmOrder)
      }
      wx.navigateTo({
        url: '/userB/pages/allOrderDetail/index?myAllOrderObj=' + JSON.stringify(myAllOrderObj) + (this.data.isScan ? '&isScan=1' : ''),
        ...(myAllOrderObj.isDmOrder ? {
          events: {
            hideSanwuRefundBtn: ({ orderTicket }) => {
              const orderIndex = this.data.storeOrderList.findIndex(v => v.orderTicket === orderTicket)
              orderIndex >=0 && this.setData({
                [`storeOrderList[${orderIndex}].canNoReasonRefund`]: false
              })
            },
          }
        } : {})
      })
    }
    // 接龙订单
    else if (orderClass === 'D'){
      const orderObj = {
        orderNo: goodsID || ''
      }
      wx.navigateTo({
        url: '/relay/pages/orderDetail/index?orderObj=' + JSON.stringify(orderObj)
      })
    }

    //  订单查看详情，记录需要更新的订单
    this.mix_updateOrder_recordOrder(orderInfo)
    // 如果是扫码退方式进入 则此处不上报埋点
    if (this.data.isScan) {
      return
    }
    sensorskey && sensors.track('MPClick', sensorskey)
  },

  /**
   * 按钮事件处理
   * @param {*} event
   */
  handleBtn (event) {
    const { type: btnType, orderinfo: orderInfo } = event.currentTarget.dataset

    console.log('按钮点击回调方法名', `${btnType}Handle`)

    const btnFn = this[`${btnType}Handle`]

    const prevHandle = {
      //  订单取消按钮前置处理函数
      cancelBtn: this.btnPrevHandle,
      //  订单三无退按钮前置处理函数
      sanwuBtn: this.btnPrevHandle,
      //  订单评价按钮前置处理函数
      commentBtn: this.btnPrevHandle
    }[btnType]

    const fnList = [prevHandle, btnFn]

    fnList.forEach(fn => fn && fn(orderInfo))
  },

  /**
   * 按钮前置处理函数
   * @param {*} orderInfo
   */
  btnPrevHandle(orderInfo) {
    //  记录需要更新的订单
    this.mix_updateOrder_recordOrder(orderInfo)
  },

  // 及时达自提联系门店
  contactStoreBtnHandle (orderInfo) {
    console.log('orderInfo', orderInfo);
    const {
      storePhone, phone, telephone, mobilephone
    } = orderInfo.store || {}
    const phoneNumber = storePhone || phone || telephone || mobilephone || '4001811212'

    this.setData({ lockOnShowReload: true })

    wx.makePhoneCall({
      phoneNumber
    })
  },

  // 门店地图
  storeMapBtnHandle (orderInfo) {
    console.log('orderInfo', orderInfo);
    const store = orderInfo.store || {}
    if (Object.keys(store).length) {
      const { longitude = 0, latitude = 0, shortName, storeName, address } = store
      let x = coordtransform.bd09togcj02(parseFloat(longitude), parseFloat(latitude))

      this.setData({ lockOnShowReload: true })
      wx.openLocation({
        latitude: x[1],
        longitude: x[0],
        name: shortName || storeName,
        address
      })
    } else {
      commonObj.showModal('提示', '找不到门店坐标', false, '我知道了')
    }
  },

  // 联系店家
  bindOwnerTap: function (e) {
    var phone = e.currentTarget.dataset.phone;
    // 神策上报联系店家
    // 如果是瞬间退 则埋点值存在差异
    const reportInfo = this.data.isScan ? {
      'element_code': '160000002',
      'element_name': '联系店家',
      'element_content': '联系店家',
      'screen_code': '1600',
    } : {
      'element_code': '140003002',
      'element_name': '联系店家',
      'element_content': '联系店家',
      'screen_code': '1400',
    }
    this.trackClickEvent(reportInfo)
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone
      })
    }
  },
  //拼团 重新购买 or 再次购买
  buyAgain:  app.subProtocolValid('shop', function (e) {
    let goodsid = e.currentTarget.dataset.goodsid || ''
    let activityid = e.currentTarget.dataset.activityid || ''
    wx.reportAnalytics('groupinfo_buyagainbtn');
    let timelyCity = wx.getStorageSync('timelyCity') || {};
    let store = timelyCity.storeInfo
    // 神策数据上报
    if (e.currentTarget.dataset.buytype === 'A') {
      // 再来一单
      this.trackClickEvent({
        'element_code': '140002007',
        'element_name': '再来一单',
        'element_content': '再来一单',
        'screen_code': '1400',
      })
    } else if (e.currentTarget.dataset.buytype === 'B') {
      // 重新购买
      this.trackClickEvent({
        'element_code': '140002004',
        'element_name': '重新购买',
        'element_content': '重新购买',
        'screen_code': '1400',
      })
    } else if (e.currentTarget.dataset.buytype === 'C') {
      // 再次购买
      this.trackClickEvent({
        'element_code': '140002006',
        'element_name': '再次购买',
        'element_content': '再次购买',
        'screen_code': '1400',
      })
    }
    if (store) {
      // 此时 this.data.activityID 是undefined
      let obj = JSON.stringify({
        goodsID: goodsid,
        activityID: activityid
      })
      wx.redirectTo({
        url: '/fightGroups/pages/goodsDetail/index?PTorderDetailObj=' + obj,
      })
    } else {
      commonObj.showModal('提示', '您附近没有自提门店，无法参与拼团购买活动', false, '我知道了', '', '')
    }
    this.preventEvent();
  }),


  // 蒙层
  preventEvent: function () {
    this.setData({
      prevent: true
    });
    setTimeout(() => {
      this.setData({
        prevent: false
      });
    }, 400)
  },
  //去重
  reduceOrder: function (orderList, key) {
    var hash = {};
    var result = [];
    orderList.forEach(order => {
      if (!hash[order[key]]) {
        hash[order[key]] = true
        result.push(order)
      }
    })
    return result
  },
  // 立即登录
  loginRight: function () {
    app.signIn()
  },
  stopNavigate: function () { //此方法完全是用于阻止页面跳转
    // 神策上报邀请好友
    this.trackClickEvent({
      'element_code': '140002003',
      'element_name': '邀请好友',
      'element_content': '邀请好友',
      'screen_code': '1400',
    })
  },
  // 神策上报点击拼团立即支付
  sensorPayFightGroup() {
    this.trackClickEvent({
      'element_code': '140002002',
      'element_name': '立即付款',
      'element_content': '立即付款',
      'screen_code': '1400',
    })
  },


  // 三无退跳转路径
  sanwuBtnHandle: app.subProtocolValid('shop', function (orderInfo) {
    let sensorskey = ''    // 自定义key
    const orderClass = this.data.orderClass
    if (['A', 'F'].includes(orderClass)) {
      // 如果是全国送，且状态为待收货
      if (orderClass === 'F' && orderInfo.orderStatus.orderStatusDesc === '待收货') {
        // 弹窗提示 请先确认收货后才能进行三无退货哦
        this.setData({
          showSanwuTip: true
        })
        return
      }
      sensorskey = 'categoryOrderListToDetail'
      const { orderNo, payment = {}, paymentStatus = {} } = orderInfo
      const myAllOrderObj = {
        isAllOrder: true,
        goodsOrderID: orderNo,
        payOrderID: payment.tradeNo,
        payStatus: paymentStatus.desc,
        type: orderClass
      }
      wx.navigateTo({
        url: '/userB/pages/allOrderDetail/index?myAllOrderObj=' + JSON.stringify(myAllOrderObj)
      })
    } else {
      const { isDm, orderticket: orderTicket } = orderInfo.currentTarget.dataset
      // 门店订单区分是旧订单还是新订单 如果是旧订单 则一单一退
      // 新订单则直接进入商品详情页 再去一单一单退
      if (isDm) {
        var myAllOrderObj = {
          isAllOrder: true,
          goodsOrderID: orderTicket,
          type: 'C',
          isDmOrder: 1
        }
        wx.navigateTo({
          url: '/userB/pages/allOrderDetail/index?myAllOrderObj=' + JSON.stringify(myAllOrderObj)
        })
        return
      }
    }
    // 三无退货上报神策
    sensorskey && sensors.track('MPClick', sensorskey)
    // 3.2
    // 目前有b2c订单和门店订单在列表展示三无退按钮
    // b2c订单通过 data-orderInfo 判断
    // 门店订单通过 orderClass 判断
    // orderClass => C 门店订单
    // 3.2 列表没有及时达和拼团的三无退
    // if (this.data.orderClass == 'A') {
    //   wx.reportAnalytics('callowner_btn_realTime')
    //   // 神策上报及时达退款
    //   this.trackClickEvent({
    //     'element_code': '140001007',
    //     'element_name': '不好吃瞬间退款',
    //     'element_content': '不好吃瞬间退款',
    //     'screen_code': '1400',
    //   })
    // } else if (this.data.orderClass == 'B') {
    //   wx.reportAnalytics('callowner_btn_fightGroup')
    //   // 神策上报拼团退款
    //   this.trackClickEvent({
    //     'element_code': '140002005',
    //     'element_name': '申请退款',
    //     'element_content': '申请退款',
    //     'screen_code': '1400',
    //   })
    // }
    // if (this.data.orderClass === 'C') {
    //   // 门店订单
    //   commonObj.showModal('如何三无退货', '请下载百果园App或到门店扫码进行三无退货', false)
    //   wx.reportAnalytics('callowner_btn_store')
    // } else if (orderInfo) {
    //   // 列表只有b2c，走在线客服
    //   const modalContent = sanwuModal['b2c']
    //   this.setData({
    //     modalContent,
    //     showCustomConfirmModal: true
    //   })
    // }
  }),

  /**
   * 隐藏三无退提示弹窗
   */
  toggleSanwuTip() {
    this.setData({
      showSanwuTip: false
    })
  },
  // 关闭自定义弹窗
  customConfirmModalCancel () {
    this.setData({
      showCustomConfirmModal: false
    })
  },

  /**
   * @description - 成功取消订单，更新列表
   *  */
  orderCancelSuccess() {
    // 取消订单成功
    wx.hideLoading();
    wx.showToast({
      title: '订单取消成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        this.refreshOrder()
      }
    })
  },

  /**
   * 倒计时结束刷新订单列表
   * @param {*} timeout
   * @returns
   */
  refreshOrder () {
    // 目前只针对及时达和全国送、接龙订单
    if (!['A', 'F', 'D'].includes(this.data.orderClass)) {
      return
    }

    setTimeout(() => {
      const pageStack = getCurrentPages()
      const lastPage = pageStack[pageStack.length - 1]

      // 延时执行时要判断是否在当前页面
      if (lastPage && lastPage.route === 'userB/pages/orderList/index') {
        this.getList()
      }
    }, 0)
  },

  /**
   * @description - 弹窗选择-“取消订单”原因,（支付成功，才需要弹出选择原因，取消订单)
   * @param {object} params - 订单取消选项
   * @returns {undefined}
   */
  onOrderCancelSelected(e) {
    //  次日达取消订单
    if (String(this.data.orderClass) === 'E') {
      this.bgxxOrderCancelSelected(e)
    }
    //  及时达/全国送取消订单
    else {
      const { orderCancelParams } = this.data
      const { paramMap } = orderCancelParams
      const { cancelReason = '', cancelID = '' } = e.detail
      // 增加多一项取消原因
      if (paramMap && paramMap.eshopRefund) { // 旧电商订单
        Object.assign(orderCancelParams.paramMap.eshopRefund, {
          cancelID,
          subReason: cancelReason
        })
      } else {
        Object.assign(orderCancelParams, {
          reason: cancelReason
        })
      }
      this.orderCancelPaiedSuccessFetch(orderCancelParams)
    }
  },
  /**
   * @description - 弹窗关闭-“取消订单”原因
   */
  onOrderCancelClosed() {
    if (String(this.data.orderClass) === 'E') {
      // 素生鲜次日达订单
      this.bgxxOrderCancelClosed()
    } else {
      this.setData({
        orderCancelIsShow: false,
        orderCancelParams: null
      })
    }
  },

  // 提货时间格式化
  // 2021-03-22 16:17:33 - 2021-03-22 17:17:33
  // 2021.03.22周三/16:17:33-17:17:33
  formatDispatchTime({ start, end, isTryEat }) {
    // const day = '周' + weekList[new Date(start).getDay()]
    const [startDate, startTime] = String(start).split(' ')
    const [endDate, endTime] = String(end).split(' ')
    return `${
      isTryEat
        ? [startDate, endDate].map(date => date.replace(/^(\d{4})-(\d{2})-(\d{2})$/, '$2.$3')).join('-')
        : startDate
    } ${[startTime, endTime].map(time => time.replace(/(\d{2}):(\d{2}):(\d{2})/, '$1:$2')).join('-')}`
  },
  // 中台订单，订单列表左上角时间：未支付 => 创建时间；已支付 => 支付时间
  formatMiddleOrderTime(orderInfo) {
    const {
      createTime,
      payment = {},
      orderStatus = {}
    } = orderInfo
    let showTime = ''
    const {
      orderStatusDesc
    } = orderStatus
    const {
      tradeTime
    } = payment || {}
    if (!orderStatusDesc || !tradeTime || orderStatusDesc === '待付款') {
      showTime = createTime
    } else {
      showTime = tradeTime
    }
    return String(showTime).split(' ')[0]
  },
  navigateToLogisticsDetail (info) {
    const { orderNo, isAllSent } = info
    wx.navigateTo({
      url: `/homeDelivery/pages/logistics/index?expressInfo=${JSON.stringify({
        orderNo,
        isAllSent
      })}`
    })
  },
  toHomePageShop() {
    this.data.orderClass === 'E' ?wx.navigateTo({
      url: '/pages/xxshop/index/index'
    }) : wx.switchTab({
      url: '/pages/homeDelivery/index'
    });
  },
  /**
   * 接龙订单支付
   */
  handleRelayBtn(e) {
    const { orderinfo: orderInfo } = e.currentTarget.dataset
    // 获取账户余额
    this.getWalletAmount(orderInfo.payment.totalPrice)
    this.setData({
      paymentDialog: true,
      hideUnionPay: false,
      target: {
        payAmount: Number((orderInfo.payment.totalPrice / 100).toFixed(2))
      }
    })
    this._data.curOrderInfo = orderInfo
  },
  /**
   * 取消接龙未支付的订单
   */
  async handleCancelRelayOrder(e) {
    const { orderinfo: orderInfo } = e.currentTarget.dataset

    //  取消订单
    const showModalRes = await app.showModalPromise({
      content: `您精挑细选的商品真的要取消吗？`,
      showCancel: true,
      confirmText: '残忍取消',
      cancelText: '考虑一下',
    })
    //  点击取消
    if (!showModalRes) {
      return
    }
    // 点击确定
    wx.showLoading({
      title: '取消中',
      mask: 'true'
    })

    //  接龙订单手动取消订单，记录需要更新的订单
    this.mix_updateOrder_recordOrder(orderInfo)
    try {
      await this.cancelUnPayRelayOrder(100, orderInfo)
      wx.hideLoading()
      // 请求成功
      this.orderCancelSuccess()
    } catch (errorInfo) {
      wx.hideLoading()
      const errorType = Object.prototype.toString.call(errorInfo)
      if (/error/i.test(errorType)) {
      } else {
        commonObj.showModal('提示', errorInfo.description, false, '我知道了')
      }
    }
  },
  /**
   * 取消接龙未支付的订单
   */
   async cancelUnPayRelayOrder(operate, orderInfo) {
    const { attachOrderNo, orderNo } = orderInfo
    const user = wx.getStorageSync('user')
    const params = {
      customerID: user.userID,
      // 优先使用父订单号
      subOrderNo: (attachOrderNo && attachOrderNo !== '-') ? attachOrderNo : orderNo,
      operate,
      paramMap: {
        operatorCode: user.userID,
        launchType: '2'
      }
    }
    try {
      //  调用取消订单接口
      await app.api.cancelUnpaidOrder(params)
      return Promise.resolve()
    } catch (error) {
      //  订单异常情况
      return Promise.reject(error)
    }
  },
  /**
   * 接龙订单倒计时结束
   */
  relayCountDownFinish(orderInfo) {
    wx.showToast({
      title: '超过15分钟未支付，订单已取消',
      icon: 'none',
      duration: 2000,
      mask: true,
      success : () => {
        // 目前的解决方案是倒计时结束后主动调用未支付取消订单接口
        // 取消之后刷新列表
        setTimeout(() => {
          this.cancelUnpaidOrderAfterCountDown(orderInfo)
        }, 2000)
      }
    })
  },

  /**
   * 左右滑动切换tab
   */
   switchTab (e) {
    const { current, source } = e.detail

    if (source && source === "touch") {
      const tab = this.data.tabList[current]
      this.switchOrderType({
        orderClass: tab.type,
        sensorskey: tab.sensorskey,
        index: current
      })
    }
  },

  /**
   * 重新设置swiper尺寸
   */
  refreshRectSize() {
    wx.nextTick(() => {
      const swiperIndex = this.data.swiperIndex;
      const query = wx.createSelectorQuery().in(this);
      query.selectAll('.order-list').boundingClientRect(res => {
        if (res.length) {
          //  列表区域节点高度
          const orderListHeight = res[swiperIndex].height
          //  列表区域可展示的最小高度
          const listAreaHeight = 500
          //  最终高度
          const lastHeight = Math.max(orderListHeight, listAreaHeight)

          this.setData({
            //  50为底部间距
            orderListHeight: Math.ceil(lastHeight) + 50
          });
        }
      }).exec();
    });
  },

  /**
   * 门店点击评价按钮
   * @param {*} e
   */
  storeOrderComment(e) {
    this.btnPrevHandle(e.target.dataset.orderinfo)
    this.commentBtnHandle(e)
  },

  payrightNow: util.throttle(wrapSensorReportFn(async function () {
    if (this.isHandlePay) {
      return
    }
    this.isHandlePay = true
    const { orderClass } = this.data
    await this.fetchPayApi()
    // 点击上报
    const sensorInfo = this.clickSensorParamsFn(orderClass === 'D' ? '接龙付款':'付款')
    this.isHandlePay = false

    this.afterPayDialogClose = true
    return sensorInfo
  }), 2000),

  /**
   * 接龙发票按钮处理函数
   * @param {*} e
   */
  relayInvoiceBtnHandle(e) {
    const { orderinfo: orderInfo, type } = e.target.dataset

    if (type === 'detail') {
      this.invoiceDetailBtnHandle(orderInfo)
    } else {
      this.invoiceBtnHandle(orderInfo)
    }
  },
  /**
   * 从扫码退方式进入后 支持添加小票号
   */
  addOrder: function() {
    // 如果没有门店订单是单独的埋点
    const hasStoreOrder = this.data.storeOrderList.length > 0
    // 按照旧逻辑 没有门店订单时上报与有门店订单上报的字段有些许差异
    const reportInfo = hasStoreOrder ? {
      element_code: 160001001,
      element_name: '添加小票号',
      element_id: '',
      element_content: '添加小票号',
      screen_code: '1600',
    } : {
      element_code: 160000005,
      element_name: '添加小票号',
      element_id: '',
      element_content: '添加小票号',
      screen_code: '1600',
    }
    this.trackClickEvent(reportInfo)
    // 跳转到添加小票号页面
    wx.navigateTo({
      url: '/store/pages/addOrder/index',
    });
  },
  /**
   * 是否是短时间(30min)内申请的三无退
   * @param {String} timeLimit 下单时间
   * @returns {Boolean} 是否是短时间内的订单
   */
  hasShortTime(timeLimit) {
    const refundContent = '请您先品尝果品，不满意可30分钟后三无退货';
    if (!timeLimit || !this.countRefundTime(timeLimit)) {
      wx.showToast({
        title: refundContent,
        icon: 'none',
        duration: 3000
      })
      return true
    }
    return false
  },
  // 计算时间
  countRefundTime(timeLimit) {
    let curTime = Date.parse(new Date())
    timeLimit = timeLimit.replace(/-/g, '/')
    let timeGap = parseInt(curTime - new Date(timeLimit).getTime());
    return timeGap > 0
  }
})
