const fs = require('fs')
const https = require('https')
const { cosInstance } = require('./utils/cos/readCos.js')
const { COS_GLOBAL, cosAuthorizationOptions } = require('./utils/cos/const/cosConfig')
const sensorsJson = require('../../src/componentsSubPackage/commonUtils/sensors/sensorsReport.js')
const sensorsPageJson = require('../../src/utils/report/utils/sensorsPageConfigMap.js')

const objEnv = getEnvInfo()

function main() {
  const files = [{
    cosDirectory: 'gray/wxapp',
    filePath: './sensorsReport.json',
    fileName: 'sensorsReport.json',
    fileContent: sensorsJson
  }, {
    cosDirectory: 'gray/wxapp',
    filePath: './sensorsPageConfigMap.json',
    fileName: 'sensorsPageConfigMap.json',
    fileContent: sensorsPageJson
  }]
  uploadFiles(files)
}

/**
 * 在Node环境中上传文件到COS
 * @param {{ cosDirectory: string, filePath: string, fileName: string, fileContent: object }} arrFiles 文件列表
 */
async function uploadFiles(arrFiles) {
  const uploadSuccess = []
  const uploadFail = []
  try {
    let dirName = ''
    if (objEnv.env === 'test_exc') {
      dirName = '/test'
    }
    if (objEnv.env === 'uat_exc') {
      dirName = '/uat'
    }
    for (let index = 0; index < arrFiles.length; index++) {
      const fileInfo = arrFiles[index]
      const cosDirectory = fileInfo.cosDirectory + dirName
      // 压缩
      const miniJson = JSON.stringify(fileInfo.fileContent)
      // 保存到根目录
      await new Promise(resolve => {
        fs.writeFile(fileInfo.fileName, miniJson, async (err) => {
          if (err) throw err
          // 开始上传到COS
          const res = await cosInstance(COS_GLOBAL).uploadFile({
            cosDirectory,
            filename: fileInfo.fileName,
            filePath: fileInfo.filePath
          })
          if (!res.Location) {
            uploadFail.push({
              fileName: fileInfo.fileName,
              assetUrl: '',
              cosDirectory
            })
            resolve()
            return
          }
          const filePath = res.Location.match(/.com\/(\S*)/)[1]
          uploadSuccess.push({
            fileName: fileInfo.fileName,
            assetUrl: objEnv.globalDomain + '/' +  filePath,
            cosDirectory
          })
          resolve()
        })
      })
    }
    sendUploadStatusMsg({
      isSuccess: uploadSuccess.length > 0,
      errorText: '文件转换JSON失败或者上传失败，请检查格式是否正确并手动上传到COS',
      uploadSuccess,
      uploadFail,
      totalCount: arrFiles.length
    })
  } catch (e) {
    sendUploadStatusMsg({
      isSuccess: false,
      errorText: '文件转换JSON失败或者上传失败，请检查格式是否正确并手动上传到COS' + e.message,
      uploadSuccess: [],
      uploadFail: [],
      totalCount: arrFiles.length
    })
    console.log(e)
  }
}

/**
 * 发送中转站
 * @param {{ isSuccess: boolean, errorText: string, cosDirectory: string, fileName: string, assetUrl: string }}} param0 发送的状态
 */
function sendUploadStatusMsg({
  isSuccess = true,
  errorText,
  uploadSuccess = [],
  uploadFail = [],
  totalCount = 0
}) {
  const className = isSuccess ? 'info' : 'warning'
  let msgContent = `${objEnv.envName}埋点文件<font color=\"${className}\">更新到COS${
    isSuccess ? '成功' : '失败'
  }</font>，请相关同事注意：\n`

  // 如果失败 则拼接失败原因
  if (!isSuccess) {
    msgContent += `> 全部失败原因：<font color=\"${className}\">${errorText}</font>
    > 现网地址：<font color=\"${className}\">[查看](https://eshop-cos.prod.pagoda.com.cn/gray/wxapp/sensorsReport.json)</font>`
  } else {
    msgContent += `> 成功数量：<font color=\"${className}\">${uploadSuccess.length}/${totalCount}</font>` +
    `${uploadFail.length ? `> 失败原因：<font color=\"${className}\">${errorText}</font>` : ''}
    > 访问地址：
    ${uploadSuccess.map((item, index) => `> ${index + 1}. <font color=\"${className}\">[${item.cosDirectory}/${item.fileName}](${item.assetUrl})</font>`).join('\n')}`
  }
  // 调用企微发送
  console.log(msgContent)
  qywxRobot({
    msgtype: 'markdown',
    markdown: {
      content: msgContent,
    },
  })
}

/**
 * 将指定的信息通过企微机器人发送信息
 * @param {Record<string, any>} postData 发送的格式/内容
 */
async function qywxRobot(postData) {
  // 测试群聊机器人 https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=048a623f-5917-452a-b575-3941f46e8d80
  // uniapp团队机器人 https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=15526209-2d60-4e1c-bfd5-87672910da72
  try {
    const options = {
      hostname: 'qyapi.weixin.qq.com',
      port: 443,
      path: '/cgi-bin/webhook/send?key=1b2664cc-2a08-4314-8b66-238cd301c4fc',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    }
    const data = await makeRequest(options, postData)
    console.log(data)
    if (!data.errCode) {
      console.log('qywxRobot', data)
    }
  } catch (error) {
    console.log('qywxRobot err: ', error)
  }
}

/**
 * 使用给定的选项和postData进行请求
 * @param {Object} options - 请求的选项
 * @param {Object} postData - 要在请求中发布的数据
 * @return {Promise} 一个Promise，用于解析响应中的数据
 */
function makeRequest(options, postData) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = ''

      res.on('data', (chunk) => {
        data += chunk
      })

      res.on('end', () => {
        resolve(isJSON() ? JSON.parse(data || '{}') : data)
      })
    })

    req.on('error', (error) => {
      console.error('上传异常', error, error.message)
      reject(error)
    })

    if (postData) {
      req.write(JSON.stringify(postData))
    }

    req.end()
  })
}

/**
 * 判断是否是JSON字符串
 * @param {String} str 字符串内容
 * @returns {Boolean} 是否是JSON字符串
 */
function isJSON(str) {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * 获取当前环境信息
 * @returns { { env: string, envName: string, globalDomain: string }} 环境信息
 */
function getEnvInfo() {
  const env = process.env.RUNTIME_ENV || 'test_exc'
  let envName = '测试环境'
  switch(env) {
    case 'draft_exc':
      envName = '本地环境'
      break
    case 'dev_exc':
      envName = '开发环境'
      break
    case 'test_exc':
      envName = '测试环境'
      break
    case 'drill_exc':
      envName = '压测环境'
      break
    case 'uat_exc':
      envName = 'UAT环境'
      break
    case 'exc':
      envName = '生产环境'
      break
    default:
      envName = '测试环境'
      break
  }
  return {
    env,
    envName,
    globalDomain: cosAuthorizationOptions[env].globalDomain,
  }
}

main()
