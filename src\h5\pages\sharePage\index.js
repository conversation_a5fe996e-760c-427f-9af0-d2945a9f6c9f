var commonObj = require('../../../source/js/common.js').commonObj;

import { 
  fillText,
  circleImg,
  getImageInfo
} from '../../../utils/services/canvasUtil'

const sensors = require('../../../utils/report/sensors')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    animationShare: '',//分享、发海报弹框
    posterUrl: '', // 海报url
    shareContent: '', // 分享内容
    firstSubscribe: true, // 是否首次弹出订阅消息弹窗
  },
  _data: {
    nvatarImgPath: '', // 用户头像的本地路径
    picUrl: commonObj.PAGODA_DSN_DOMAIN,
    codeImg: '', // 小程序码图片
    downLoadNum: 0, // 需要转换本地图片的数量
    userID: '', // 用户id
    codeType: '', //1分享小程序，2分享公众号二维码
    posterbgUrl: '', // 海报背景图
    shareTitle: '', // 分享标题
    shareImg: '', // 分享卡片
    navigateDesc: '',//跳转h5路径标识
    h5Url: ''//跳转h5路径
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('options', options)
    //把h5传过来的参数保存下来
    this._data.options = options;
    this.setData({
      posterHeight: 667, // 海报高度
      posterWidth: 375, // 海报宽度
      imgHeight: 667*2/1.6, // 展示图片高度
      imgWidth: 375*2/1.6 // 展示图片宽度
    })
    wx.showLoading({
      title: '正在生成海报'
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.reportData()
    //显示分享、发海报弹框
    this.utilShare();
    // 获取头像
    this.getAvatarImg();
    // 获取海报配置信息
    this.getShareBanerConfig();
    
    this.getWxappCode()

  },
  reportData () {
    sensors.pageShow('sharePagePageShow')
  },
  // 获取海报配置信息
  getShareBanerConfig () {
    const { poster_url: posterbgUrl, show_text: shareContent, card_title: shareTitle, card_cover: shareImg,navigate_desc:navigateDesc,h5_url:h5Url} = this._data.options || {}
    this._data.shareTitle = shareTitle // 分享标题
    this._data.shareImg = shareImg // 分享卡片
    this._data.navigateDesc = navigateDesc //跳转标识
    this._data.h5Url = h5Url;//跳转h5的路径
    this.getImageInfo(posterbgUrl, 'posterbgUrl');
    this.setData({
      shareContent
    })
  },
  // 获取小程序码或公众号码
  getWxappCode: function(){
    // 显示小程序码
    const {navigateDesc}  = this._data
    let that = this,
      pageUrl = 'pages@homeDelivery@index',
      sceneUrl = encodeURIComponent(`to=h5&pageKey=${navigateDesc}`),
      widthUrl = 200,
      isHyalineUrl = false;
    const posterUrl = `${that._data.picUrl}/api/v1/wechat/wxa/wxacodeunlimit/get/${sceneUrl}/${pageUrl}/${widthUrl}/${isHyalineUrl}`;
    this.getImageInfo(posterUrl, 'codeImg')
  },
  // 分享弹框
  utilShare: function (){
    var that =  this;
    var animation = wx.createAnimation({
      duration:500,
      timingFunction:"linear",
      delay:0
    })
    that.animation = animation;
    animation.translateY(-150).step();
    that.setData({animationShare:animation.export()});

    setTimeout(function(){
      that.setData({animationShare:animation})
    }.bind(that),500)
  },
  // 转换头像
  getAvatarImg:function(){
    const userInfo = wx.getStorageSync('userNameAndImg') || {};
    this._data.nickName = userInfo.nickName || ''
    // 用户微信没有头像取默认头像，解决绘制海报出现异常的问题
    const avatarDefault = 'https://resource.pagoda.com.cn/group1/M21/54/64/CmiLkGD5TyKASB85AAAn3XJoLPU505.png'
    const avatar = userInfo && userInfo.avatarUrl ? userInfo.avatarUrl : avatarDefault
    this.getImageInfo(avatar, 'nvatarImgPath');
  },
  // 获取图片的本地路径
  getImageInfo:function(netUrl, urlKey) {
    console.log('urlKey', urlKey)
    console.log('netUrl',netUrl)
    var that = this;
    getImageInfo(netUrl).then( res => {
      console.log('getImageInfo', res)
      that._data[urlKey] = res.path
      that._data.downLoadNum++ 
      // 当图片转化本地路径完成时绘制
      if (that._data.downLoadNum === 3) {
        that.drawPoster();
      }
    }).catch( err => {
      console.log(err)
    })
  },
  // 绘制canvas图
  drawPoster:function() {
    const canvasCtx = wx.createCanvasContext('posterCanvas');
    canvasCtx.scale(2, 2);
    // 绘制背景图片
    canvasCtx.drawImage(this._data.posterbgUrl, 0, 0, this.data.posterWidth, this.data.posterHeight);
    //绘制头像
    canvasCtx.drawImage(this._data.nvatarImgPath, 18,28,47,47);
    // 绘制小程序码
    console.log('codeImg',this._data.codeImg);
    circleImg(canvasCtx, this._data.codeImg, 277, 573, 40)
    // 绘制用户名
    canvasCtx.beginPath();
    // fillText(canvasCtx, {
    //   text: this._data.nickName,
    //   x: 75,
    //   y: 40
    // })
    //绘制标题
    // this.fillText(canvasCtx, {
    //   text: '送你一个现金红包',
    //   x: 75,
    //   y: 74
    // })
    canvasCtx.draw(false, this.canvasToImgPath)
  },
  // canvas转化图片
  canvasToImgPath () {
    let that = this
    wx.canvasToTempFilePath({
      x: 0,
      y: 0,
      width: that.data.posterWidth * 2,
      height: that.data.posterHeight * 2,
      quality:1,
      destWidth: that.data.posterWidth * 2,
      destHeight: that.data.posterHeight * 2,
      canvasId: 'posterCanvas',
      success: function (res) {
        that.setData({
          posterUrl: res.tempFilePath
        })
        wx.hideLoading()
      },
      fail: function (res) {
        wx.hideLoading()
      }
    })
  },
  // 海报保存到本地
  savePoster: function () {
    var that = this;
    if(wx.showShareImageMenu) {
      wx.showShareImageMenu({
        path: this.data.posterUrl,
        fail(err) {
          let { errMsg } = err
          console.log(err, '错误信息')
          if(errMsg === "showShareImageMenu:fail auth deny") {
            commonObj.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了', '', function (res) {
              wx.openSetting()
            })
          }
        },
      })
    } else {
      wx.saveImageToPhotosAlbum({
        filePath: this.data.posterUrl,
        success: (res) => {
          wx.hideLoading()
          wx.showToast({ title: '保存成功，快去分享吧~', icon: 'none' });
          setTimeout(() => {
            wx.navigateBack()
          }, 1000)
        },
        fail: (err) => {
          console.log(err)
          this.setData({ status: 'auth denied' })
          wx.hideLoading()
          commonObj.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了','',function(res){
            if (res.confirm && that.data.status === 'auth denied') { 
              wx.openSetting()
            }
          })
        }
      })
    }
    sensors.track('MPClick', 'sendToPoster') 
  },
  onShareAppMessage(e) {
    const { userID } = wx.getStorageSync('user') || {}
    if (e.from === 'button') {
      sensors.track('MPClick', 'shareToFriend') 
    }
    return {
      title: this._data.shareTitle,
      path: `pages/homeDelivery/index?to=commonh5&pageParam=`+ JSON.stringify({
        pageUrl: this._data.h5Url
      }),
      imageUrl: this._data.shareImg,
    }
  },
  // 订阅消息
  subscribeMes() {
    const { tmplIds } = this._data.options || {}
    const { firstSubscribe } = this.data
    if (!tmplIds || !tmplIds.length) {
      this.savePoster()
    } else {
      if (!firstSubscribe) {
        this.savePoster()
      } else {
        app.requestSubscribeMessage({
          tmplIds: typeof tmplIds === 'string' ? JSON.parse(tmplIds) : tmplIds
        },(res) => {
          console.log('message', res)
          this.savePoster()
          this.setData({
            firstSubscribe: false
          })
        })
      }
    }
  }
})