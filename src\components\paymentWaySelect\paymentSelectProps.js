export const paymentSelectProps = {
  // 余额支付选中状态
  selectPagodaPay: {
    type: Boolean,
    default: true
  },
  // 微信支付选中状态
  selectWxPay: {
    type: Boolean,
    default: false
  },
  // 云闪付选中状态
  selectUnionPay: {
    type: Boolean,
    default: false
  },
  disableUnionPay: {
    type: Boolean,
    default: false
  },
  // 全国送隐藏云闪付
  hideUnionPay: {
    type: Boolean,
    default: false
  },
  mainBalance: {
    type: Number,
    default: 0
  },
  rechargeText: {
    type: String,
    default: ''
  },
  // 余额不足
  mainBalanceIsNotEnough: {
    type: Boolean,
    default: false
  },
  // 禁用钱包支付
  forbidPdPay: {
    type: Boolean,
    default: false
  },
  // 禁用支付
  disablePay: {
    type: Boolean,
    default: false
  },
  /**
     * 随单充支付信息
     */
  rechargePayInfo: {
    type: Object,
    default: {}
  },
}