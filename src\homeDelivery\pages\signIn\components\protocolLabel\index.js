import {clickReport} from "~/utils/report/sensors"
import { protocolKeyMap, protocolLinkList } from "../../service/protocol"
const preInfoMap = {
  normal: {
    text: '我已阅读并同意',
    color: '#555'
  },
  popup: {
    text: '为了更好地保障您的合法权益，请您阅读并同意以下协议意', 
    color:'#222'
  }
}
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    isPopup: {
      type: Boolean,
      value: false
    }
  },
  observers: {
    'isPopup': function (val) {
      this.setData({
        preInfo: val ? preInfoMap.popup : preInfoMap.normal
      })
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    protocolLinkList,
    preInfo: preInfoMap.normal
  },

  /**
   * 组件的方法列表
   */
  methods: {
    bindTap(event) {
      console.log('event', event)
      const { key, url } = event.currentTarget.dataset
      wx.navigateTo({
        url
      })
      if (key === protocolKeyMap.refund) {
        clickReport({
          'element_code': '100100004',
          'element_name': '百果园用户服务条款',
          'element_content': '阅读用户服务',
          'screen_code': '1001',
        })
      }
    }
  }
})
