<!--userA/pages/couponGoods/index.wxml-->
<import src="/pages/template/index.wxml" />
<wxs module="common" src="../../../utils/common.wxs"></wxs>

<main-page currentView="content" />
<view class="main-container">
  <scroll-view scroll-y="{{true}}" style="height:100vh" lower-threshold="600" bindscrolltolower="onReachBottomEvent">
    <view class="content">
      <!-- 顶部核销码 -->
      <view class="pop-down" wx:if="{{showQrcode}}">
        <view class="coupon-label">线下核销</view>
        <view class="coupon-card">
          <view class="coupon-name">{{couponName}}</view>
          <view class="code">
            <block wx:if="{{effectTimeStr}}">
              <view class="no-effective-bgm">
                <view class="no-effective-slogn">未生效</view>
              </view>
            </block>
            <image mode="widthFix" class="canvas-img" src="{{tempFilePath}}" />
          </view>
          <view class='coupon-code'>{{qrCouponCode}}</view>
          <!-- <view class="coupon-tips">门店结账时请出示此条码</view> -->
          <view class="btn-switch" bindtap="toSwitch">
            <block wx:if="{{ (isExchangeCard && !!couponObj.ruleDescription) || !isExchangeCard }}">
              <text>{{btnSwitch ? '收起': '展开'}}说明</text>
              <image class="arrow {{btnSwitch ? 'up' : 'down'}}" src="{{FOLD_ICON}}" />
            </block>
          </view>
          <view wx:if="{{ isExchangeCard || couponObj.ruleDescription }}" hidden="{{!btnSwitch}}" class="use-info">
            <text class="use-info-item">{{ couponObj.ruleDescription }}</text>
          </view>
          <view wx:else hidden="{{!btnSwitch}}" class="use-info">
            <view class="use-info-item" wx:for="{{useableCondition}}" wx:key="index">{{index + 1}}、{{item}}</view>
          </view>
        </view>
      </view>
      <!-- 如果仅线上可用 则需要展示小卡券信息 -->
      <view wx:if="{{!showQrcode && showGoods}}" class="coupon-small-card">
        <block wx:if="{{effectTimeStr}}">
          <view class="no-effective-bgm limit-height">
            <view class="no-effective-slogn">未生效</view>
          </view>
        </block>
        <coupon coupon="{{couponObj}}" type="normal" color="deep"></coupon>
      </view>
      <block wx:if="{{showGoods}}">
        <view class="sticky">
          <!-- 线上券保持原有导航栏固定样式 -->
          <!-- 头部导航栏 -->
            <view class="nav-header" wx:if="{{showQrcode}}">线上下单</view>
            <view class="price-gap">
              <view class="price-gap-item {{curIndex === index ? 'active': ''}} {{ item.hide ? 'hide' : '' }}" wx:for="{{priceGapList}}" wx:key="index" data-index="{{index}}" bindtap="getGapGoods">
                {{item.label}}
              </view>
            </view>
        </view>
        <!-- 适用商品模块 -->
        <view class="goods-block" wx:if="{{isSupport}}">
          <block wx:for="{{goodsList}}"  wx:key="index">
            <common-goods
              wx:if="{{ item.isSellOutGoods ? isOpenGoodsFold : true }}"
              id="goods_{{index}}"
              class="goods-item"
              componentRow="double"
              goodsObj="{{item}}"
              isShowCount="{{true}}"
              needFilterLabelTypes="{{['storeRecommend']}}"
              bindcomplete="handleCountComplete"
              bind:wxsAnimation="handlewxsAnimation"
              bind:toShowChoiceLayer="toShowChoiceLayer"
              choiceSensorskey="categoryCouponGoodsChoice"
              addSensorskey="categoryCouponGoodsAddCart"
          ></common-goods>
          </block>
          <view class="bottom-tip {{isLoading ? '' : 'hide'}}">
            <bounce-tips loading="{{true}}" />
          </view>
          <!-- 售罄商品折叠展示 -->
          <pagoda-sellout-item
            wx:if="{{ isShowGoodsFold }}"
            class="pagoda-sellout-item"
            sellOutNum="{{ selloutCount }}"
            bind:isShow="selloutIsShow"
          />
          <view class="bottom-tip {{isLoading || hasNextPage ? 'hide' : ''}}">
            <text class="tip-text">没有更多啦~</text>
          </view>
        </view>
        <view wx:elif="{{ !isLoading }}" class="no-goods-block">
          <image class="no-goods-img" src="https://resource.pagoda.com.cn/group1/M00/33/76/CmiU8F-ibLKAJUdVAAB8dFZR3-g465.png" />
          <view class="no-goods-text">抱歉，无符合要求的商品</view>
        </view>
      </block>
    </view>
  </scroll-view>

  <!-- 购物车结算栏 -->
  <cart-popup
    wx:if="{{ isLoadedCouponInfo }}"
    customSubmitBody="{{ true }}"
    isBackPrevPage="{{ isBackPrevPage }}"
    orderType="{{ ORDER_TYPE.TIMELY }}">
    <view class="submit-body" slot="submit-body">
      <!-- 如果是兑换卡 -->
      <view wx:if="{{isExchangeCard}}" class="amount-info">
        <view class="strong">
          {{isExchangeCardGoodsInCart ? '购物车有适用商品' : '请加购适用商品' }}
        </view>
        <view wx:if="{{effectTimeStr}}" class="text-tips">兑换卡生效后可使用</view>
        <view wx:else class="text-tips">可使用兑换卡</view>
      </view>
      <!-- 如果是优惠券/代金券 -->
      <view wx:else class="amount-info">
        <view wx:if="{{cartEnoughAmount < limitValue}}" class="amount-desc">
          再购<text class="text-highlight">{{common.formatPrice(limitValue - cartEnoughAmount)}}</text>元
        </view>
        <view wx:else>
          已满<text class="text-highlight">{{common.formatPrice(limitValue)}}</text>元
        </view>
        <view wx:if="{{effectTimeStr}}" class="text-tips">{{ couponObj.couponWay === '6' ? '代金' : '优惠' }}券生效后可使用</view>
        <view wx:else class="text-tips">可使用{{ couponObj.couponWay === '6' ? '代金' : '优惠' }}券</view>
      </view>
      <!-- 购物车数量 -->
      <view class="add-cart-btn" bindtap="toCartPage">去结算</view>
    </view>
  </cart-popup>
  <!-- 购物车结算栏 -->
</view>

<!-- 蒙层 -->
<view class="prevent-screen" hidden="{{!prevent}}"></view>
<!-- <template is="add-to-cart-animation" data="{{...addToCartData}}"></template> -->
<!--cart-animation-ball id="goodsCartAnimationBall"></cart-animation-ball -->

<!-- sku加购弹层 -->
<add-goods-layer
  showLayer="{{showLayer}}"
  goodsInfo="{{goodsInfo}}"
  bind:updateCarList="updateCarList"
></add-goods-layer>

<confirm-modal id="globalModal" globalModalInfo="{{globalLocateModalInfo}}"></confirm-modal>

<canvas class="canvas-view" canvas-id="barcode1" id="barcode1" style="width: 750px; height: 303.3px;"></canvas>

<common-loading />