@import "/pages/template/index";
@import "./template/index";
/* 短信验证样式 */
@import '/components/SMS-validate/sms-popup-common.wxss';
/* 及时达新UI样式 */
@import './css/orderTemplate.wxss';

Page{
  background-color: #fff;
}
.detail {
  position:relative;
}

.order-container {
  background-color: #f4f4f5;
  min-height: 100vh;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 124rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 124rpx);
  position: relative;
}

.show-map {
  padding-top: 24rpx;
}

/* 顶部导航条 */
.nav-back{
  width: 44rpx;
  height: 44rpx;
}

.nav-back image{
  width: 100%;
  height: 100%;
}

/* 及时达 待付款状态 */
.top {
  height: 164rpx;
  border-bottom: 1px solid rgb(240, 240, 240);
  background-color: #fff;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.top-status {
  font-size: 34rpx;
  font-weight: 500;
  padding-left: 24rpx;
  padding-bottom: 8rpx;
}
.top-label {
  margin-left: 12rpx;
  width: 120rpx;
  height: 40rpx;
  vertical-align: middle;
}
.rest-time,.tips {
  margin-left: 24rpx;
  font-size: 24rpx;
  max-width: 432rpx;
  align-self: flex-start;
}
.package-tips{
  height: 56rpx;
  background: #FEF6E7;
  color: #FF7900;
  line-height: 30rpx;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
}
.icon-right {
  width: 12rpx;
  height: 20rpx;
  vertical-align: middle;
  margin-top: -5rpx;
}
.icon-remind {
  width: 22rpx;
  height: 22rpx;
  vertical-align: middle;
  margin-left: 4rpx;
}

/*自提码  */
.take-code {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #fff;
  border-bottom: 1rpx solid #f2f2f2;
}
canvas.barcode-canvas {
  width: 600rpx;
  min-width: 300px;
  height: 175rpx;
}
.member-code {
  background-color: #fff;
  display: flex;
  justify-content: center;
  margin: 24rpx 0 18rpx 0;
}
.code-num {
  color: rgb(93, 157, 235);
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 18rpx;
}
/* 多单配送、送达时间 */
.delivery-item {
  position: relative;
  display: flex;
  align-items: center;
  height: 140rpx;
  padding: 0 24rpx;
  font-size: 26rpx;
  color: #555;
  border-bottom: 1rpx solid #f2f2f2;
}

.delivery-item .title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  line-height: 28rpx;
  color: #222;
  margin-bottom: 18rpx;
}
.timely-info-content{
  display: flex;
  align-items: center;
}
.timely-info-content .icon-tips {
  margin-left: 4rpx;
  width: 22rpx;
  height: 22rpx;
}
.timely-label{
  background: #15AD56;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: #FFFFFF;
  text-align: center;
  margin-left: 16rpx;
  line-height: 34rpx;
  height: 34rpx;
  padding: 0 6rpx;
}
.timely-label-text{
  font-size: 22rpx;
  line-height: 34rpx;
  margin-left: 8rpx;
}
.delivery-item .delivery-sign {
  margin-left: 12rpx;
  padding: 0 4rpx;
  height: 28rpx;
  line-height: 28rpx;
  font-size: 22rpx;
  color: #FFFFFF;
  background-color: #15AD56;
  border-radius: 4rpx;
}
.delivery-item .delivery-overtime {
  margin-left: 12rpx;
  display: flex;
  align-items: center;
  font-size: 22rpx;
}
.delivery-overtime .icon-tips {
  margin-left: 4rpx;
  width: 22rpx;
  height: 22rpx;
}

.delivery-item image{
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

/* 三无退货 */
.refunds-wrap {
  padding: 24rpx;

}
.refunds-title {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #222222;
}
.refunds-info {
  font-size: 24rpx;
  color: #FFA006;
}

/*地址详情  */
.border{
  /* height:184rpx; */
  /* margin-top:20rpx; */
  background-color:#fff;
  padding-bottom: 4rpx;
  box-sizing: border-box;
  background: linear-gradient(-34deg, #ffadb0 15%, #fff 15%, #fff 20%, #008C3C 20%, #008C3C 35%, #fff 35%, #fff 40%, #ffadb0 40%, #ffadb0 55%, #fff 55%, #fff 60%, #008C3C 60%, #008C3C 75%, #fff 75%, #fff 80%, #ffadb0 80%, #ffadb0 95%, #fff 95%, #fff 100%);
  background-size: 50px 50px;
}
.contact-detail{
  height:172rpx;
  padding:0 24rpx;
  background-color:#fff;
  display:flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #888;
}
.icon-address {
  width: 48rpx;
  height: 50rpx;
  margin-right: 20rpx;
}
.icon-call {
  width: 36rpx;
  height: 36rpx;
}
.flex-1 {
  flex: 1;
}
.userName {
  color: #222;
  margin-right: 12rpx;
}
.userTel {
  color: #888;
}
.store-call-wrap {
  font-size: 20rpx;
  padding: 0 14rpx 0 28rpx;
  border-left: 1rpx solid #DCDCDC;
}
.invoice-title {
  font-weight: 600;
}
.address{
  margin-top:12rpx;
  color: #555;
}

/*商品列表  */
.info-title {
  font-size: 28rpx;
  color: #222;
  padding: 25rpx 24rpx;
  font-weight: bold;
  border-top: 20rpx solid #f5f5f5;
  border-bottom: 1rpx solid #f2f2f2;
}
.goods-box{
  background-color:#fff;
}
.goods{
  /* height:238rpx; */
  padding:24rpx;
  border-bottom: 1px solid #f2f2f2;
  display:flex;
  align-content: center;
}
.goods:last-child{
  border-bottom: none;
}

/*门店商品列表  */
.store-goods{
  color: #222;
  border-bottom:1rpx solid #f2f2f2;
}
.goods-top{
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx 24rpx 0 24rpx;
  color: #222;
}
.store-goods-pic{
  margin-right: 12rpx;
  width:160rpx;
  height:160rpx;
}
.store-goods-msg {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-right: 12rpx;
  width: 360rpx;
}
.store-goods-name {
  font-size: 26rpx;
  color: #000000;
}
.store-goods-spec {
  font-size: 24rpx;
  color: #555555;
}
.store-goods-single-price {
  margin-right: 20rpx;
}
.store-goods-discount{
  width: 100%;
  flex: 0 0 100%;
  margin-top: 10rpx;
  padding-left: 172rpx;
}
.store-goods-discount-body{
  display: flex;
  padding: 14rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  background: #F5F5F5;
  vertical-align: baseline;
}
.store-goods-discount-text{
  height: 34rpx;
  line-height: 34rpx;
  font-size: 22rpx;
  color: #888;
}
.store-goods-discount-left{
  width: 100%;
  flex: 1 1 100%;
  min-width: 0;
}
.store-goods-discount-right{
  flex: 0 0 auto;
  text-align: right;
  align-self: flex-end;
}
.store-goods-discount-final{
  color: #FF542D;
}
.store-goods-price {
  flex: 1;
  font-size: 30rpx;
  text-align: right;
  font-weight: 600;
  color: #000000;
}
.actual-pay {
  margin-top: 6rpx;
  font-size: 24rpx;
  color: #00A34F;
}
/* 门店信息 */
.store-info-wrap {
  max-height: 221rpx;
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx 20rpx 24rpx;
  background-color: #fff;
}
.icon-store {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}
.store-info {
  display: flex;
  align-items: center;
  flex: 1;
  font-size: 26rpx;
  color: #555;
  overflow: hidden;
}
.store-name-text {
  font-size: 28rpx;
  color: #222;
}
.store-name-text .receiver-name {
  max-width: 304rpx;
  overflow: hidden;
}
.store-name-text .receiver-mobile {
  display: inline-block;
  margin-left: 12rpx;
  color: #888888;
}
.store-sub-name {
  color: #555555;
  font-size: 24rpx;
  line-height: 33rpx;
}
.store-address {
  margin: 10rpx 0;
}
.receiver-address {
  margin: 12rpx 0;
  color: #222222;
  font-size: 26rpx;
  line-height: 37rpx;
}
.store-info-r {
  font-size: 20rpx;
  margin-left: 24rpx;
}
.icon-wrap {
  padding: 10rpx 12rpx;
  border-top: 1rpx solid #f2f2f2;
}
.store-info-r>.icon-wrap:first-child{
  border-top: none;
}
.overflow-hidden{
  overflow: hidden;
}

/*退款  */
.refunded{
  /* height: 460rpx; */
  margin-top: 1.5rpx;
  background-color: #fff;
  padding: 15rpx 0;
}
/*总计  */
/* .pay-info{
  margin-bottom: 120rpx;
} */
.pay-info,.total-price{background-color:#fff;padding:15rpx 0;}
.total{
  height:460rpx;
  margin-top:20rpx;
  background-color:#fff;
  padding:15rpx 0;
}
.price-com{
  display:flex;
  justify-content: space-between;
  font-size:28rpx;
  margin: 0 12rpx;
  padding: 10rpx 12rpx;
  line-height:40rpx;
  color: #2B2B2B;
}
.price-com--highlight{
  background-color: #FFFBF2;
  border-radius: 12rpx;
}
.price-com--highlight > .detail-pack-left::after{
  content: '';
  width: 30rpx;
  height: 24rpx;
  background-size: auto 100%;
  background-repeat: no-repeat;
  background-position: right center;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADQAAAAwCAMAAABpN6nPAAABU1BMVEX/2oUAAAD/2oT/2oP/5LH/2oP/463/24f/2YH/5LD/5rL/47D/24b/45z/26T/5LL/5bP/2YH/24j/3IL/57P/5K7/5LP/5rC5fRj/3Iv/3pT/46//3I3/2oH/2oT/4qv/3pb/24b/5LH/35n/24j/4qj/3I//3ZH/35v/2oj/463/4aL/4aa7fxv/4aT/35f/353/3JH/4q3/4qq2eBH/4KD/5LO4fBa3ehT/4J68gh7/3ZD/57HDiir/5aX/45z+3pm1dg7+4Kjcr1/SoEX83qL83Z//4pf+3ZT/4ZD+2ovovnHMlzrHkDD/6LX/5qv/5KH+3p3xy4Pgsl/bqlD83qX42Jz/34751IXvx3fkum/WplDBhyW9gyD/5qj3047xyn3Zq1n83qT72pj00pLxzo7rwmzPmz//4aH825z414/00Yz2zn30zHfkuWPUokyzdAxzM+FaAAAAGHRSTlP+AI/9/Pz09PTWkI45CQf189bVtDk5tLSwufErAAADlElEQVRIx42Wd3PaMBiHVVKyu5dTCgRaoDHYBFBqQ9jQsJMwmr269/j+f/UnyQvHuePRnV+948E63SVncgs8Xl66/8YiCt6wBXi0uL+0/JjNQ7qzfC86M/eW7zDp7rPo7dvRQDTAwcYMXhGTT+9CWgncDswOhldukSfzbCvLgWyWRawsCGDJBigjyYazUTEw/4SsyLwbDu9Gw+FsFhkcK6IjIvq7dSNdIUthhvyuf3IADTuRT0VQLH5ulN7xRF4i84Yk1c7eHxQD9bAH9Wjxc1P5p0BizJMcR/5QkHRagFaP5VzUc7ufmhKVpMIHWVRIjBN+W9AkRauVTmS5HpuiHog1ClRSJK3wNixKhpRjkiQput48KKZStpJKFT/1a5qCJqScIaU4MSEBWhoVVRSwAJyTAoUipJiom9KOKSm6crKnQmOoqXCD4jWGtBPjRZWoHFvC4fUGLAA1dlpDyZIgACYlQWpHwg+a0EYMNTWpqn9rqJooOyqKTEoKtvpUV6wubeyxIt5j1zRaGhrDkAyrV6KShd7b28rvNRwOPR9/T5rSlsnFx1Ndt88/Kpd7SE3o18uLpDlKtvJ44pEffiv3zs0jKnrp40/7bjRtnFSH+XyeDULazG+CPAvD8sg6okL7A2odVjkuD1/ngRhkkkW1fDmwrsM+q34+uqjih5kEmPTaQXX/8kyXXGjKqFx9vekYJCLY1pWiuaUenCnIy2mq+z2XRMf7VdcQpGnW999Th6LQZiXtniFpF+sv2wPdlvSzH5V19wxZd9OuHDsP96fSvjZCnntYp1QxD/e1IgaeYzmka7TTR+a9a8pVq319AtI1MpUJtW4h4zFA7GIGPE/wdXRu3PtVOpMQTSxbSnAyWMYGarw14X8UtJnO8AJ7OgYhxYFIbOkYDqQvrXginnCAFAMkvh13gGx7G5VEHxdIB11srT4axp7EX7CA5wuOsWmNdbxo0uqiw8vAjuSFJ4dH7D/ur+01zy4R5TXAI5bY93Wt1DXrVhRtsuZJtzOm+uSw69m8WTpW6JfOxg3SK84GW0BkyNeOSsrvQxRQcsILZEEMO+uczmBwuGHlU84CeRjhMxEsHrAYpDNpdoidiyhmHhJfhEQ8IaEb6hEfWV0gLkKAxQjxZmGV3PKF/HOhmZnzh3zsM+dRyO+fC4aCZj0IWHTnCHN+f+jRXf5B5VsMzgVnAWOLPv5BBVZ9DxZncRYf+FbZ/H+uU20Dsoxj5QAAAABJRU5ErkJggg==);
}
.detail-pack-left {
  display: flex;
  align-items: center;
}
.detail-pack-icon{
  margin-left: 4rpx;
  width: 21rpx;
  height: 21rpx;
  vertical-align: middle;
}
.order-info-wrap {
  background-color: #f5f5f5;
  border-radius: 14rpx;
  padding: 24rpx;
  margin: 24rpx;
}
.order-info {
  font-size: 28rpx;
  margin-top: 8rpx;
  color: #222;
}
.order-info--heightlight {
  color: #FF0C0C;
}
.order-info .title {
  margin-right: 24rpx;
}
.btn-copy {
  color: #008C3C;
  margin-left: 24rpx;
}
.display-between {
  display: flex;
  justify-content: space-between;
}
.super-vip {
  margin:25rpx 0 20rpx;
  height:116rpx;
  padding: 0 32rpx;
}
.vip-img {
  width:172rpx;
  height:32rpx;
}
.vip-content{
  font-size: 24rpx;
}
.to-detail {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  font-size: 22rpx;
}
.arrow-right-img {
  height:14rpx;
  width: 8rpx;
  margin-left: 10rpx;
}
.refund{
  padding-bottom: 24rpx;
  margin-bottom:18rpx;
}
.price-com2{
  font-size: 28rpx;
  color:#222;
  padding-bottom: 10rpx;
  text-align: right;
}
.info-wrap {
  margin:0 24rpx;
  border-bottom:  1rpx solid #f2f2f2;
}
.total-pay{
  color:#222;
}
.differ-text {
  color: #FFA006;
  font-size: 24rpx;
  padding-bottom: 20rpx;
  text-align: right;
}
.total-money{
  color:#008C3C;
  font-size: 34rpx;
  font-weight: 500;
}
.color-complain {
  color: #FFA006;
}
.big-money{
  font-size: 48rpx;
  font-weight: 500;
}

/*页脚按钮组  */
.footer-btn{
  /* min-height: 100rpx;  */
  width: 100%;
  /* padding-top:20rpx;     */
  margin:20rpx 0;
  background-color:#fff;
  text-align: right;
  margin-bottom: 0;
  position: fixed;
  z-index: 1111;
  bottom: 0;
  box-shadow: 0 -4rpx 8rpx 0 rgba(0,0,0,0.03);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.footer-btn--hide{
  display: none;
}

.footer-btn button{
  padding:0 24rpx;
  margin-top:20rpx;
  height:56rpx;
  line-height: 56rpx;
  margin-right:20rpx;
  border-radius: 30rpx;
  margin-bottom: 10rpx;
  min-width: 156rpx;
}
.footer-btn button[plain]{
  border-color:#888;
  color:#222;
}

.footer-btn button[plain].footer-btn-disabled{
  background-color: #CCC;
  color: #FFF;
  border-color: #CCC;
}

.footer-btn-list{
  width: 100%;
  background-color:#fff;
  text-align: right;
  position: fixed;
  z-index: 99;
  bottom: 0;
  display: flex;
  justify-content: flex-end;
  box-shadow: 0 -4rpx 8rpx 0 rgba(0,0,0,0.03);
}
.ipx.footer-btn-list{
  padding-bottom: 64rpx;
}
.footer-btn-list > view{
  padding:10rpx 24rpx;
  font-size: 26rpx;
  color: #222;
  border:1rpx solid #888;
  /* margin-left: 20rpx; */
  border-radius: 30rpx;
  min-width:152rpx;
  text-align:center;
  margin: 22rpx 20rpx 22rpx 0rpx;
}

.pay-timer-btn {
  white-space: nowrap;
}

.footer-btn-list view.pay-btn,
.footer-btn-list view.confirm-receive {
  flex: 0 0 auto;
  font-size: 26rpx;
  color: #fff;
  background-color: #00A34F;
  border: 1px solid #00A34F;
}
.footer-btn-list view.pay-btn::after,
.footer-btn-list view.confirm-receive::after{
  border-color: #00A34F;
}
.store-footer{
  height: 96rpx;
  line-height: 96rpx;
  padding: 0;
  display: flex;
  justify-content:space-between;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
}
.store-footer button{
  margin-top: 20rpx;
}
.detail-mask {
  height:100%;
  width:100%;
  position:fixed;
  background-color:#000;
  top:0px;
  opacity:0.4;
  z-index: 1;
}
.xinxiang-detail {
  width:100%;
  position:fixed;
  bottom:0px;
  z-index:99999;
  background-color:#fff;
  animation: myfirst linear 0.4s;
  /* animation-fill-mode: forwards; */
  border-radius: 32rpx 32rpx 0 0;
  height: 615rpx;
}
@-webkit-keyframes myfirst{
  from {
    height: 0px;
  }
  to {
    height: 615rpx;
  }
}
@keyframes myfirst{
  from {
    height: 0px;
  }
  to {
    height: 615rpx;
  }
}
.xinxiang-detail-title {
  color: #222222;
  font-size: 34rpx;
  font-size: 500;
  width: 100%;
  text-align: center;
  padding-top: 40rpx;
}
.close-img {
  width: 30rpx;
  height: 30rpx;
  padding: 10rpx;
  position: absolute;
  top: 30rpx;
  right: 28rpx;
}
.xinxiang-detail-item {
  color: #2B2B2B;
  padding: 0 24rpx 20rpx 24rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.small-hongbao{position:fixed ;bottom: 180rpx;right: 24rpx;opacity: 0.8}
.hongbao-btn{
  width: 100rpx;
  height: 100rpx;
}
.hongbao-btn-inner {
  width: 100%;
  height: 100%;
}
.hongbao-btn-inner[plain] {
  border:none;
}

.membrane{position: absolute;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.6);}
.membrane-contain{width: 100%;height: 100%;display: flex;flex-direction: column;align-items: center;}
.contain{width: 530rpx;height: 625rpx;border-radius: 24rpx;background-color: #fff;margin-top: 206rpx;display: flex;flex-direction: column;align-items: center;}
.contain-img{width:418rpx;height: 278rpx;margin:35rpx 0 15rpx;}
.contain-f1{color: #ff3b30;font-size: 38rpx;font-weight: 600;margin-bottom: 20rpx;}
.contain-f2{font-size: 24rpx;color: #666;}
.contain-btn{width: 425rpx;height: 88rpx;border-radius: 100rpx;background-color: #fa465d;color: #fff;margin-top: 45rpx;line-height: 88rpx;}
.contain-close{width: 64rpx;height: 64rpx;margin-top: 50rpx;}
/* .refunded-btn button{
  color: #008C3C;
} */
.fr{
  float: right;
}
.fl{
  float: left;
}
.space-between{
  display: flex;
  justify-content: space-between;
  width: 100%;
}

/* 新加波浪 */
.highTop{
  background-size: 100% 100%;
  position: relative;
}
/* 下载app */
.download{
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120rpx;
  margin-top: 24rpx;
  background: #FFF;
}
.download__txt{
  color: #333;
  font-size: 24rpx;
  font-weight: 400;
}

/* 为了适配iphonex */
.iphoneXBt{
  border-bottom: 64rpx #fff solid;
  height: 164rpx;
}


/* 支付方式选择 */
.dialog{
  position: fixed;
  z-index: 1999;
  width: 100%;
  height: 100vh;
  top: 0;
  background-color: rgba(0,0,0,0.8);
}
.payment-wrapper{
  position: absolute;
  bottom: 0;
  background-color: #FFFFFF;
  width: 100%;
}

.payment-way {
  background-color: #fff;
}

.pay-item {
  display: flex;
  align-items: center;
  height: 110rpx;
  background-color: #fff;
  padding: 0 24rpx;
  font-size: 32rpx;
  font-family:PingFangSC-Regular;
}

.recharge {
  font-size: 28rpx;
}
.disable{
  opacity: 0.5;
}

.icon-pagodapay,
.icon-wechat {
  width: 44rpx;
  height: 44rpx;
  padding-right: 12rpx;
}

.pay-desc {
  flex: 1;
}

.remaining {
  font-size: 28rpx;
  color: #666;
  padding-left: 12rpx;
}

.pay-item radio {
  width: 40rpx;
  height: 40rpx;
}

.payment-header{
  display: flex;
  align-items: center;
  height: 96rpx;
  background-color: #fff;
}
.payment-header-title{
  color: #333;
  font-size: 30rpx;
  flex: 1;
  text-align: center;
  padding-right: 48rpx;
}
.icon-close{
  width: 48rpx;
  height: 48rpx;
  transform:translateX(24rpx);
}
.payment-wrapper-amount{
  height: 228rpx;
  background-color: #fff;
}
.light{
  color: #999;
  font-size: 26rpx;
  padding-top: 40rpx;
  padding-bottom: 24rpx;
  text-align: center;
}
.money-num{
  color: #333;
  font-size: 80rpx;
  text-align: center;
  padding-bottom: 46rpx;
}

.payment-wrapper-button{
  margin: 200rpx auto 42rpx;
  width: 702rpx;
  height: 90rpx;
  text-align: center;
  line-height: 90rpx;
  color: #fff;
  background-color: rgba(21, 173, 86, 1);
  border-radius: 45rpx;
}

.adaption{
  height: 180rpx;
}
.adaption.adaption-gift-order{
  height: 15rpx;
}

/* 2.3版本 支付引导 */
.t-bottom{
  font-size: 24rpx;
  /* width: 220rpx; */
  padding: 4rpx 16rpx;
  color: #008C3C;
  background-color: #E5F3EB;
  border-radius: 8rpx;
  display: inline-block;
  /* display: flex; */
  /* align-items: center; */
}
.green-right-icon{
  width: 12rpx;
  height: 12rpx;
  margin-left: 8rpx;
  margin-bottom: 2rpx;
}
/* v2.4开具发票 */
.invoiced-btn{
  border: 0;
}
.invoice-btn{
  color: #00A34F;
  font-size: 26rpx;
  background-color: white;
  border: 1px solid #00A34F;
}
.invoice-btn::after{
  border: none;
}


/* 餐盒费 */
.detail-pack-left {
  display: flex;
  align-items: center;
}
.detail-pack-icon{
  margin-left: 8rpx;
  width: 24rpx;
  height: 24rpx;
  vertical-align: middle;
}
/* 餐盒费明细 */
.pack-list-item{
  font-size: 28rpx;
  margin-top: 16rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx;
}
.pack-list {
  width: 100%;
  max-height: calc(60vh - 209rpx);
  display: flex;
  flex-direction: column;
}
.pack-item-desc {
  max-width: 560rpx;
}
.pack-count{
  margin-top: 19rpx;
  height: 90rpx;
  height: 78rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  padding: 0 24rpx 0 24rpx;
}
.pack-count-value{
  font-size: 56rpx;
  font-weight: bold;
  line-height: 78rpx;
  color: #008C3D;
}
.pack-item-value {
  min-width: 80rpx;
  text-align: right;
}
.pack-explain {
  margin-top: 16rpx;
  padding: 0 24rpx 0 24rpx;
}
.pack-explain .pack-explain-desc {
  font-size: 28rpx;
  font-weight: bold;
  line-height: 40rpx;
  color: #222222;
}
.pack-explain .pack-explain-value {
  display: block;
  margin-top: 8rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  color: #555555;
}
.pack-explain .pack-explain-value.point {
  position: relative;
  padding-left: 20rpx;
}
.pack-explain .pack-explain-value.point::before {
  position: absolute;
  content: '';
  top: 16rpx;
  left: 0;
  width: 8rpx;
  height: 8rpx;
  background-color: #008C3D;
  border-radius: 50%;
}

/* b2c */
.b2c_tips_font-color{
  color: #888888;
  font-weight: normal;
  font-size: 24rpx;
  line-height: 34rpx;
}
/* 底部撑高 */
.page-bottom{
  height: 126rpx;
  background-color: #f5f5f5;
  margin-top: constant(safe-area-inset-bottom);
  margin-top: env(safe-area-inset-bottom);
}

.vertical_divider{
  width: 1rpx;
  height: 70rpx;
  background-color: #f5f5f5;
}
.flex{
  display: flex;
}
.radius-16 {
  border-radius: 16rpx;
}
.mg-b{
  margin-bottom: 24rpx;
}
.b2c_box_32{
  padding: 32rpx;
  background: #fff;
}
.b2c-detail {
  position: relative;
  background-color: #f5f5f5;
  z-index: 1;
}


.close-tip-btn {
  width: 702rpx;
  height: 96rpx;
  background: #008C3D;
  border-radius: 48rpx;
  text-align: center;
  line-height: 96rpx;
  color: #FFFFFF;
  font-size: 32rpx;
  margin: 32rpx auto;
}

.tip-desc {
  color: #555555;
  font-size: 28rpx;
  margin: 0 40rpx;
}

.tip-desc:not(:first-child) {
  margin-top: 30rpx;
}

button.confirm-btn{
  padding: 33rpx 70rpx;
  font-size: 32rpx;
  line-height: 36rpx;
  border: none;
  position: relative;
  width: 280rpx;
}

button.confirm-btn:last-child{
  color: rgba(0, 140, 60, 1);
}

button.confirm-btn:last-child:after {
  position: absolute;
  top: 0;
  right: 0;
  content: "";
  width: 2rpx;
  height: 100%;
  background-color: rgba(221, 221, 221, 1);
  transform: scaleX(0.5);
}

.b2c-btn-confirm {
  border-radius: 0 34rpx 34rpx 0;
}

.barcode-canvas {
  position: fixed;
  top: -1000px;
  left: -1000px;
}

.member-code image {
  width: 690rpx;
  min-width: 300px;
  height: 175rpx;
}

.store-order-more-btn {
  height: 56rpx;
  display: inline-block;

  margin: 22rpx 0rpx !important;
  min-width: unset !important;
  padding: unset !important;
  border: unset !important;
}

.map-tips {
  font-size: 26rpx;
  display: flex;
  position: absolute;
  right: 36rpx;
  bottom: 14rpx;
}

.map-arrow {
  margin-left: 10rpx;
}

.map-arrow::before, .map-arrow::after {
  content: " ";
  display: block;
  width: 20rpx;
  height: 20rpx;
  background-size: 100%;
  background-image: url(https://resource.pagoda.com.cn/group1/M21/8F/BC/CmiWa2J83WuAWPeSAAAA4VbKHqA021.png)
}

.map-arrow::after {
  margin-top: -10rpx;
}

/* 祝福卡弹窗 */
.bless-popup{
  font-size: 28rpx;
  color: #222222;
  margin: 0 40rpx;
  min-height: 636rpx;
}
.bless-receiver{
  display: flex;
  justify-content: flex-start;
  margin-top: 24rpx;
}
.bless-content{
  padding: 30rpx 0 40rpx;
}
.bless-sender{
  display: flex;
  justify-content: flex-end;
}
.pay-mode-box{
  width: 750rpx;
  /* height: 88rpx; */
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx 24rpx;
  margin-top: 24rpx;
}
.pay-mode-text{
  width: 547rpx;
  height: 40rpx;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 28rpx;
  color: #222222;
}
.wx-switch-input {
  transform: translateX(25%) scale(.75);
}
.confirm-title {
  line-height: 44rpx;
  font-size: 28rpx;
  color: #222222;
  text-align: left;
}
.confirm-title text {
  color:#0070FF;
  text-decoration: underline;
}

.thrid-pay-discount {
  border-radius: 16rpx;
  background-color: #fff;
  font-size: 28rpx;
  margin: 24rpx 0;
  padding: 24rpx 32rpx;
}

.thrid-pay-discount text {
  color: #48A5FF;
}

/* 门店订单配送 - start */
.address-section {
  border-bottom: 1rpx solid #f2f2f2;
  background-color: #FFF;
}
.address-section-info {
  padding: 24rpx 32rpx;
}
.info-title.info-title-address {
  display: flex;
  justify-content: space-between;
  border: none;
}
.info-title-address-action{
  color: #8C8C8C;
  font-size: 26rpx;
  font-weight: normal;
  padding-right: 20rpx;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkBAMAAAATLoWrAAAAJFBMVEUAAACtra2tra2tra2vr6+tra2tra2urq6urq6tra2wsLCtra18zUE/AAAAC3RSTlMABvlsXInmW4jeNw9lbSQAAABKSURBVCjPY4ABRgEGdCBsiC7CGKQqgK5IexO6spW7d6MrE9HejaHMbZAoE8eirIxSZZjuwHQGtRRpDDZFkASAmUwwExOeJIeZMAF61jp8FxjDGwAAAABJRU5ErkJggg==);
  background-size: 20rpx 20rpx;
  background-repeat: no-repeat;
  background-position: center right;
}
.info-title-address-action-selected{
  color: #00A34F;
}
.address-section-submit{
  /* 25 + 7 = 32 */
  padding: 7rpx 0 32rpx 0;
  text-align: center;
}
.address-section-submit-btn{
  display: inline-block;
  height: 2.5em;
  line-height: 2.5;
  padding: 0 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #FFFFFF;
  background-color: #00A34F;
  border-radius: 1.25em;
}
.offline-delivery-map-section{
  --delivery-map-background: transparent;
  margin-top: -32rpx;
  border-bottom: 20rpx solid #f2f2f2;
}
.offline-delivery-tag{
  display: inline-block;
  height: 1.875em;
  line-height: 1.875;
  padding: 0 15rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #FF9A00;
  background-color: #FFEED5;
  border-radius: 8rpx;
}
/* 门店订单配送 - 时间弹窗 - 从确认订单抄过来的 - start */
.head-class{
  background: #F5F5F5;
  border-top-right-radius: 20rpx;
  border-top-left-radius: 20rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f5f5f5;
}
.title-class{
  font-size: 32rpx!important;
  height: 96rpx!important;
  line-height: 96rpx!important;
  font-weight: 500!important;
}
/* 门店订单配送 - 时间弹窗 - 从确认订单抄过来的 - end */
.offline-delivery-address-title{
  font-size: 32rpx;
  color: #222;
  text-align: center;
  line-height: 44rpx;
}
/* 门店订单配送 - end */
