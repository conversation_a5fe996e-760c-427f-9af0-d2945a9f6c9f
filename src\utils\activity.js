
const commonObj = require('../source/js/common').commonObj

const {
  FruitSubject
} = require('../service/fruitSubject')
const {
  FreshSubject
} = require('../service/freshSubject')

// 统一配置活动跳转
const app = getApp()
const openTypes = [
  { openType: '1', key: 'deposit' }, // 果币充值
  { openType: '5', key: 'noMean' }, // 不跳转无意义
  { openType: '3', key: 'goodsDetail' }, // 商品详情
  { openType: '13', key: 'deposit' }, // 充值页
  { openType: '17', key: 'activity' }, // h5活动页
  { openType: '19', key: 'colligateSubActivity' }, // 综合专题活动模板
  { openType: '20', key: 'wxappPage' }, // 小程序路径
  { openType: '24', key: 'groupFightDetails' }, // 拼团商品详情
  { openType: '33', key: 'vipShopGoods'}, // 心享商品
  { openType: '49', key: 'vipShopGoods'}, // 生鲜商品详情
  { openType: '34', key: 'vipShopPath'}, // 心享商城页面路径
  { openType: '35', key: 'thridWxapp'}, // 三方小程序
  { openType: '46', key: 'relateWxapp'}, // 关联小程序
  { openType: '52', key: 'xxcolligateSubActivity'}, // 心享综合专题活动
  { openType: '48', key: 'livePlayer'}, // 直播
  { openType: '54', key: 'tryEatPage' }, // 试吃活动首页
  { openType: '55', key: 'limitActivity' }, // 限时特价
  { openType: '58', key: 'xxShopCategory' }, // 次日达分类
  { openType: '61', key: 'nationalDelivery'}, // 全国送
  { openType: '62', key: 'nationalDeliveryGoodsDetail'}, // 全国送商品详情
  { openType: '63', key: 'xxGoodsDetail'}, // 4.3 新增次日达商品详情
  { openType: '66', key: 'wxappPage' }, // 拼团首页
  { openType: '67', key: 'relayActivity' }, // 接龙首页
  { openType: '68', key: 'xxShopSubactivity' }, // 次日达综合专题活动
  { openType: '73', key: 'goodsWikiImage' }, // 及时达商品
]
const freshOpenTypes = [
  { openType: '0', key: 'noMean' }, // 不跳转无意义
  { openType: '1', key: 'xxGoodsDetail' }, // 新增次日达商品详情
  { openType: '2', key: 'xxShopSubactivity' }, // 心享综合专题活动
  { openType: '3', key: 'wxappPage' }, // 原生页面
  { openType: '4', key: 'thridWxapp' }, // 原生页面
  { openType: '5', key: 'activity' }, // 原生页面
  { openType: '9', key: 'xxShopSubactivity' }, // 心享综合专题活动

]
const { tabBarPage } = require('./config')

let activity = {
  noMean({ openValue }) {
    if (openValue === '') {
      return false
    }
  },
  groupFightDetails ({ openValue }) {
    // 拼团活动
    let topicObj = {
      activityID: openValue,
      goodsID:""
    }
    wx.navigateTo({
      url: "/fightGroups/pages/goodsDetail/index?topicObj=" +  JSON.stringify(topicObj)
    })
  },
  goodsDetail ({ openValue, goodsSn,takeawayAttr } ) {
    const homeDeliveryObj = {
      takeawayAttr
    }
    if (goodsSn) {
      homeDeliveryObj.goodsSn = goodsSn
    } else if (openValue) {
      homeDeliveryObj.goodsID = openValue
    }
    wx.navigateTo({
      url: `/homeDelivery/pages/goodsDetail/index?homeDeliveryObj=${JSON.stringify(homeDeliveryObj)}`
    })
  },
  activity: app.subProtocolValid('memberService', function ({ openValue }) {
    if (openValue.indexOf("http") > -1) {
      let url = ''
      // h5路径
      if (openValue.indexOf('x.pagoda.com.cn') > -1) {
        url = `/h5/pages/activityTemp/index?url=${encodeURIComponent(openValue)}`
      } else {
        let pageParam = {
          pageUrl: encodeURIComponent(openValue)
        }
        url = `/h5/pages/commonh5/index?pageParam=${JSON.stringify(pageParam)}`
      }
      wx.navigateTo({
        url: url
      })
    } else {
      // 小程序路径
      this.wxappPage({openValue})
    }
  }),
  colligateSubActivity ({ openValue }) {
    wx.navigateTo({
      url: `/pages/topic/index?homeDeliveryObj=${JSON.stringify({activityID: openValue })}`,
    })
    new FruitSubject({ activityID: openValue, isNeedMerge: true }).preRequest()
  },
  wxappPage ({ openValue }) {
    const url = openValue.startsWith('/') ? openValue : '/' + openValue
    if (tabBarPage.includes(url)) {
      wx.switchTab({
        url
      })
    } else if (openValue === 'pages/homeDelivery/index?to=duiba'){
      //跳兑吧积分商城首页
      wx.navigateTo({
        url: '/h5/pages/duiba/index',
      })
    } else {
      wx.navigateTo({
        url
      })
    }
  },
  // vipWxapp () {
  //   wx.navigateToMiniProgram({
  //     appId: 'wxc08e66e4f11eb4e1'
  //   })
  // },
  vipShopGoods({ openValue }) {
    app.globalData.bgxxOptions = {
      toDetail: JSON.stringify({ goodsID: openValue })
    }
    wx.navigateTo({
      url: '/pages/xxshop/index/index'
    })
  },
  vipShopPath ({ openValue }) {
    wx.navigateToMiniProgram({
      appId: 'wxc08e66e4f11eb4e1',
      path: '/' + openValue
    })
  },
  livePlayer({ openValue }) {
    wx.navigateTo({
      url: `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${openValue}`,
    })
  },
  xxcolligateSubActivity({ openValue }) {
    wx.navigateTo({
      url: `/bgxxShop/pages/subjectActivity/index?activityID=${openValue}`
    })
    new FreshSubject({ activityID: openValue }).preRequest()

  },
  relateWxapp(options) {
    // miniType 1：心享小程序  2：百果园+ 3 百果园礼品卡
    let appId = ''
    if (options.miniType === '1') {
      appId = 'wxc08e66e4f11eb4e1'
    } else if (options.miniType === '3') {
      appId = 'wx64168858244cb3d1'
    }
    if (appId) {
      wx.navigateToMiniProgram({
        path: options.openValue,
        appId: appId
      })
      return
    }
    this.wxappPage({ openValue: options.openValue })
  },
  thridWxapp (options) {
    if (!options.miniProgram) {
      return
    }
    wx.navigateToMiniProgram({
      path: options.openValue,
      appId: options.miniProgram
    })
  },
  nationalDelivery() {
    wx.navigateTo({
      url: '/homeDelivery/pages/nationalDelivery/index'
    })
  },
  nationalDeliveryGoodsDetail({ openValue }) {
    wx.navigateTo({
      url: `/homeDelivery/pages/goodsDetail/index?homeDeliveryObj=${JSON.stringify({ goodsSn: openValue,takeawayAttr: 'B2C'})}`
    })
  },
  deposit () {
    // 跳转充值页
    if (app.checkSignInsStatus()) {
      // 用户已登录
      wx.navigateTo({
        url: '/userA/pages/deposit/index'
      })
    } else {
      app.signIn()
    }
  },
  fightGroup () {
    wx.navigateTo({
      url: '/fightGroups/pages/fightGroups/index'
    })
  },
  limitActivity () {
    wx.navigateTo({
      url: '/homeDelivery/pages/limitActivity/index'
    })
  },
  relayActivity () {
    wx.navigateTo({
      url: '/relay/pages/home/<USER>'
    })
  },
  tryEatPage () {
    wx.navigateTo({
      url: `/h5/pages/commonh5/index?pageParam=${JSON.stringify({ pageUrl: encodeURIComponent(`${commonObj.H5_WEB_DOMAIN}/tryEat/index`) })}`
    })
  },
  // 跳转次日达综合专题活动
  xxShopSubactivity ({ openValue }) {
    wx.navigateTo({
      url: `/bgxxShop/pages/subjectActivity/index?activityID=${openValue}&isFromHomeDelivery=true`
    })
    console.time('xxShopSubactivity')
    new FreshSubject({ activityID: openValue }).preRequest()
  },
  // 跳转次日达商品详情
  xxGoodsDetail({ openValue, goodsID, bannerType }) {

    const obj = {}
    // 次日达广告位openvalue存储的是eshopGoodsId
    if (bannerType === 'freshBanner') {
      obj.goodsID = openValue
    } else {
      openValue && (obj.goodsSn = openValue)
      goodsID && (obj.goodsID = goodsID)
    }
    wx.navigateTo({
      url: `/bgxxShop/pages/goodDetail/index?toDetail=${JSON.stringify(obj)}`
    })
  },
  // 跳转次日达分类
  xxShopCategory({ openValue }) {
    wx.navigateTo({
      url: `/bgxxShop/pages/category/index?categoryCode=${openValue}`
    })
  },
  // 跳及时达wiki图片
  goodsWikiImage({ bannerImageList }) {
    if (!Array.isArray(bannerImageList)) {
      return
    }
    wx.navigateTo({
      url: `/homeDelivery/pages/wikiImage/index?bannerImageList=${
        encodeURIComponent(
          JSON.stringify(
            bannerImageList
              .map(function (v) {
                return v.picUrl
              })
          )
        )
      }`
    })
  },
}
function toActivityPage (options) {
  console.log(options)
  let key = ''
  const { openType } = options
  openTypes.forEach( item => {
    if (String(item.openType) === String(openType)) {
      key = item.key
    }
  })
  activity[key] && activity[key](options)
}
// 次日达广告位跳转
function freshtoActivityPage (options) {
  console.log(options)
  let key = ''
  const { openType } = options
  freshOpenTypes.forEach( item => {
    if (String(item.openType) === String(openType)) {
      key = item.key
    }
  })
  activity[key] && activity[key](options)
}
module.exports = {
  toActivityPage,
  freshtoActivityPage
}
