<wxs module="common" src="../../utils/common.wxs"></wxs>
<wxs module="fn">
  module.exports = {
    /**
     * 微信支付禁用
     */
    getWxpayDisabled: function(mainBalanceIsNotEnough, selectPagodaPay, disablePay) {
      if (disablePay) {
         return true
       }
       return false
      // return !mainBalanceIsNotEnough && selectPagodaPay
    },
    /**
     * 余额支付禁用
     */
     getPagodapayDisabled: function(mainBalanceIsNotEnough, mainBalance, disablePay, rechargePayInfo) {
       if (disablePay) {
         return true
       }
       if (rechargePayInfo && rechargePayInfo.balancePayAmount) {
        return false
       }
      return mainBalance === 0
    }
  }
</wxs>
<view class="payway-select">
  <!-- ['payment-item-m', 'payment-item-w'] -->
  <block wx:for="{{payWayList}}" wx:key="item">
    <template is="{{item}}" data="{{ rechargePayInfo, mainBalanceIsNotEnough,selectPagodaPay,mainBalance, rechargeText, forbidPdPay, disablePay, selectWxPay, selectUnionPay, disableUnionPay, unionPayConfig, hideUnionPay }}"></template>
  </block>
</view>

<!-- 会员钱包支付 -->
<template name="payment-item-m">
  <view class="pay-item pagoda-pay-item {{mainBalanceIsNotEnough? 'recharge': ''}} {{(forbidPdPay || disablePay)? 'forbidPdPay' : ''}}">
    <view class="flex align-center">
      <image src="/source/images/icon_pagodapay.png" class="icon-pagodapay {{fn.getPagodapayDisabled(mainBalanceIsNotEnough, mainBalance, disablePay, rechargePayInfo)? 'disable': ''}}"></image>
      <view class="pay-desc">
        <view class="{{fn.getPagodapayDisabled(mainBalanceIsNotEnough, mainBalance, disablePay, rechargePayInfo) ? 'disable': ''}}">
          <text class="">会员钱包</text>

          <block wx:if="{{ rechargePayInfo && rechargePayInfo.totalBalance }}">
            <text class="remaining">(余额:￥{{common.formatPrice(rechargePayInfo.totalBalance)}})</text>
          </block>
          <block wx:else>
            <text class="remaining" wx:if="{{!forbidPdPay}}">(余额:￥{{common.formatPrice(mainBalance)}})</text>
          </block>
        </view>
        <!--
        <navigator url="/userA/pages/deposit/index" class="t-bottom" wx:if="{{(mainBalance !== 0 || !forbidPdPay) && rechargeText}}">
          <text>{{rechargeText}}</text>
          <image src="/source/images/right_arrow_pink.png" class="red-right-icon"></image>
        </navigator>
        -->
        <!-- 文字提示后续有需要再扩展 -->
        <!-- <text class="forbid-text" wx:if="{{forbidPdPay}}">升级轻享年卡不可使用钱包支付</text> -->
      </view>
    </view>
    <view class="flex align-center">
      <!-- 选择随单充支付时的钱包支付 -->
      <block wx:if="{{ rechargePayInfo && rechargePayInfo.thirdPayAmount }}">
        <view wx:if="{{ rechargePayInfo.balancePayAmount }}" style="color: #222;font-size: 26rpx;margin-right: 16rpx;">
          支付¥{{common.formatPrice(rechargePayInfo.balancePayAmount)}}
        </view>
        <radio-check checked="{{ rechargePayInfo.balancePayAmount ? true : false }}" disabled="{{ true }}" />
      </block>
      <!-- 选择随单充支付时的钱包支付 -->

      <block wx:else>
        <view wx:if="{{mainBalanceIsNotEnough && mainBalance !== 0 && selectPagodaPay}}" style="color: #222;font-size: 26rpx;margin-right: 16rpx;">
          支付¥{{common.formatPrice(mainBalance)}}
        </view>
        <!-- 只有混合支付的时候才展示开关 -->
        <radio-check checked="{{selectPagodaPay}}" bindchange="pdpay" wx:if="{{!mainBalanceIsNotEnough || mainBalance === 0}}" disabled="{{mainBalance === 0 || forbidPdPay || disablePay}}" />
        <pdm-switch checked="{{selectPagodaPay}}" wx:else disabled="{{mainBalance === 0 || forbidPdPay || disablePay}}" bindchange="switchChange">
        </pdm-switch>
      </block>
    </view>
  </view>
</template>

<!-- 微信支付 -->
<template name="payment-item-w">
  <view class="pay-item {{disablePay ? 'forbidPdPay' : ''}}" data-type="wechat" catchtap="selectThirdPay">
    <image src="/source/images/icon_wechat.png" class="icon-wechat {{fn.getWxpayDisabled(mainBalanceIsNotEnough, selectPagodaPay, disablePay) ? 'disable' : ''}}"></image>
    <view class="pay-desc {{fn.getWxpayDisabled(mainBalanceIsNotEnough, selectPagodaPay, disablePay) ? 'disable' : ''}}">
      <text class="">微信支付</text>
    </view>
    <!-- 余额足的时候，如果果币支付开启，微信支付禁用状态 -->
    <radio-check checked="{{selectWxPay}}" disabled="{{fn.getWxpayDisabled(mainBalanceIsNotEnough, selectPagodaPay, disablePay)}}" />
  </view>
</template>

<!-- 云闪付支付 -->
<template name="payment-item-u">
  <view wx:if="{{!(unionPayConfig.hide || hideUnionPay)}}" class="pay-item {{disablePay ? 'forbidPdPay' : ''}}" data-type="unionpay" catchtap="selectThirdPay">
    <image src="/source/images/icon_unionpay.png" class="icon-unionpay {{fn.getWxpayDisabled(mainBalanceIsNotEnough, selectPagodaPay, disableUnionPay || disablePay) ? 'disable' : ''}}"></image>
    <view class="pay-desc {{fn.getWxpayDisabled(mainBalanceIsNotEnough, selectPagodaPay, disableUnionPay || disablePay) ? 'disable' : ''}}">
      <text class="pay-name">云闪付</text>
      <view wx:if="{{unionPayConfig.discountText}}" class="t-bottom" catch:tap="showUnionPayDiscountRule">
        <view>{{unionPayConfig.discountText}}</view>
        <image wx:if="{{unionPayConfig.discountRule}}" src="/source/images/icon_21_qm_red.png" class="red-right-icon red-right-icon-big"></image>
      </view>
    </view>
    <!-- 余额足的时候，如果果币支付开启，云闪付禁用状态 -->
    <radio-check checked="{{selectUnionPay && !disableUnionPay}}" disabled="{{fn.getWxpayDisabled(mainBalanceIsNotEnough, selectPagodaPay, disableUnionPay || disablePay)}}" />
  </view>
</template>
