.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  font-size: 34rpx;
  z-index: 999;
}
.title-box {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 999;
  padding-right: 190rpx;
}
.back-box {
  width: 60rpx;
  height: 60rpx;
  flex: 0 0 60rpx;
  position: absolute;
  top: 50%;
  left: 20rpx;
  margin-top: -30rpx;
  display: flex;
  align-items: center;
  z-index: 999;
}
.back {
  width: 18rpx;
  height: 34rpx;
  z-index: 999;
  display: none;
}
.content-box {
  flex: 1 1 100%;
}
.back-box + .content-box, .content-box:first-child>.slot-content+.title{
  padding-left: var(--padding-left, 90px);
}
.title {
  font-family:PingFangSC-Medium,PingFang SC;
  font-size: 36rpx;
  z-index: 999;
  display: none;
  text-align: center;
  white-space: nowrap;
}
.slot-back:empty + .back,
.slot-content:empty + .title{
  display: block;
}
