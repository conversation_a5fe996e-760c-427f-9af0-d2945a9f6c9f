// pages/confirmOrder/index.js
import {
  fruitOrderPay,
} from '../../../utils/services/fruitOrderPay';
import { getBlessCardInfo, clearBlessCardInfo } from '../../service/blessLogic';
import { getServerTimeStamp } from '../../../utils/time';
import fruitCartStore from '../../../stores/module/fruitCart';
import { b2cOrderSubmitTemplateIds, timelyOrderSubmitTemplateIds, reportTmplIds } from '../../../mixins/ticketEntry';
import { getCouponMatchGoodsMap } from '../../../utils/goods/exchangeCard';
import { getGoodsShowPrice, getMaxDiscountUuid, getMemberPrice } from '../../../source/js/requestData/activityPrice';
import { exchangeControl } from '../../../stores/module/exchangeControl';
import { getTimelyDistributor, recentBuy } from '../../../service/userService';
import { isCombinationGoods } from '../../../utils/goods/goodsData';
import { handleVipCouponList } from './business/coupon';
import { getVipPrice } from '../../../source/js/requestData/activityPrice';
import { checkIsFansGray } from '../../../utils/gray/sseGray';
import { getPromiseObj } from '../../../utils/promise';
import { pickActGoodsFields } from '~/service/fruitAct/activity';
import { getCartGoodsUniqCode, reMatchNewUserGoodsList, removeGoodsNewAct } from './business/goods';
import { handleSettleSuccPrompt } from './business/settlePrompt';
import { getSaleReportInfo } from '~/utils/report/getSaleReportInfo';
const { commonObj } = require('../../../source/js/common');
const GRAY_SCALE_ENUM = require('../../../source/const/grayScaleEnum');
const util = require('../../../utils/util');
const sensors = require('../../../utils/report/sensors');
const txSensor = require('../../../utils/report/txSensor');
const navigateToH5Mixin = require('../../../mixins/navigateToH5Mixin');
const config = require('../../../utils/config');
const { formatTime: formatTimeUtils, splitTrans, textTransf } = require('../../../utils/deliveryTime');
const SMSMixin = require('../../../mixins/SMSMixin');
const log = require('../../../utils/log.js');
import { getOrderInlineRechargeData } from './components/order-inline-recharge/fetch';
import { getOrderInlineRechargeInfo } from '../../../utils/order';

const selfTakeTitle = '选择自提时间';
const deliveryToDoorTitle = '选择送达时间';
const popupTitleMap = {
  selfTake: selfTakeTitle,
  deliveryToDoor: deliveryToDoorTitle,
};

const BLESS_CARD_SELECT_ENUM = {
  TRUE: 'Y',
  FALSE: 'N',
};

// 开卡类型
const APPLY_CARD_TYPE_ENUM = {
  MONTH_CARD: 'M',
  RENEW_MONTH_CARD: 'XM',
  YEAR_CARD: 'Y',
  RENEW_YEAR_CARD: 'XY',
};
const YearCardType = ['XY','Y']
const MonthCardType = ['XM','M']
// 电商优惠券类型字段枚举
const ESHOP_COUPON_WAY_ENUM = {
  MAN_JIAN: '1', // 满减
  LI_JIAN: '3', // 立减
  MAN_ZHE: '2', // 满折
  LI_ZHE: '4', // 立折
  NO_FREIGHT: '5', // 免运
};

// 中台优惠券类型字段枚举
const MIDDLE_COUPON_WAY_ENUM = {
  MAN_JIAN: 'C', // 满减
  LI_JIAN: 'D', // 立减
  MAN_ZHE: 'K', // 满折
  LI_ZHE: 'F', // 立折
  NO_FREIGHT: 'M', // 免运
};

const app = getApp();
Page({
  mixins: [navigateToH5Mixin, SMSMixin],
  /**
   * 页面的初始数据
   */
  data: {
    goodList: [],
    noDeliveryTimeInfo: '',
    seletTimePopupName: deliveryToDoorTitle,
    multiArray: [[], []],
    multiIndex: [0, 0],
    currSelectDeliveryInfo: {}, // 当前选择的配送时间
    paraGoodsList: [],
    settlementGoodsList: [],
    storeInfo: null, // 门店信息
    deliveryWay: 'deliveryToDoor', // 配送方式 selfTake 自提 deliveryToDoor 配送上门 默认为deliveryToDoor
    showAddrNotice: false, // 是否显示小行的配送地址信息
    b2cGoodsList: [], // b2c商品列表
    inTimeGoodsList: [], // 及时达商品
    invalidGoodsList: [], // 失效商品list
    inTimeOrderNote: '', // 订单备注
    couponList: [], // 可使用的优惠券
    unAvailableCouponList: [], // 不可使用的优惠券
    selectedCouponList: [], // 选择的优惠券
    couponModifyFlag: false, // 是否跟换选择过优惠券
    payAmount: 0, // 合计(支付价格)
    totalFreight: 0, // 支付总运费
    goodsTotalAmount: 0, // 商品总金额(以扣除了特价优惠)
    showm: true,
    selectPagodaPay: true, // 会员钱包支付
    selectWxPay: false, // 微信支付
    selectUnionPay: false, // 云闪付
    disableUnionPay: false, // 禁用云闪付
    hideUnionPay: true, // 隐藏云闪付
    discountAmount: 0, //优惠金额 v.2.3.2
    topTips: '',
    isShowPopup: false, // 底部弹窗开关
    showPackDetail: false, // 是否显示餐盒费明细
    showPackingFee: false, // 是否显示包装费说明
    mainBalance: 0,
    showFreightTip: false, // 是否显示运费说明
    showExceedDistanceTip: false, // 是否展示收获地址超过当前位置1km提示
    addMargin: 0,
    orderDeliveryWay: [
      {
        // 可下单使用方式
        label: '配送上门',
        key: 'deliveryToDoor',
      },
      {
        label: '门店自提',
        key: 'selfTake',
      },
    ],
    orderDeliveryAddressInfo: {
      selfTake: null, // 门店自提: 门店信息
      deliveryToDoor: null, // 配送上门: 地址信息
    },
    visible: false, // 是否显示弹窗
    blessCardSelected: false,
    blessCardContent: '',
    blessCardSender: '',
    blessCardReceiver: '',
    formattedBlessCardContent: '',
    forbidPdPay: false,
    vipFree: -1,
    vipCardInfo: {}, //月卡信息
    scrollId: '',
    showMouthCardMode: false, //是否展示飞享月卡提示条
    mouthCardSaveMoney: 0,
    openCardContent: {},
    payAmountShouldRolling: false,
    vipCardIsSeleted: false, // 是否勾选开通心享卡
    applyVipCardTipsOptions: {
      showModal: false,
    },
    showMouthSaveBox: false,
    useLimitCouponKey: '',
    bestCouponIsVipCoupon: false,
    isSelectVoucher: false,// 未用代金券
    popupBenefits:{ //是否展示月卡权益详情弹窗
      show:false,
      url:''
    },
    canUseCouponAmount:0, // 能够参与优惠券抵扣的费用：商品金额 + 餐盒费 + 包装费 + 贺卡费，这里不计入运费
    availableListKey:[],
    // 订阅消息相关
    subscribe: {
      // 是否展示订阅
      show: false,
      // 模板列表
      tmplIds: []
    },
    showSelectVipCoupon:'',//开通心享后的可用券描述
    packageBestCouponBatchNum:'',//月卡券包内本单最佳券批次号
    payAmountVirtual: 0, //虚拟勾选后支付价格
    integralSelected: false, // 是否选择积分兑换
    packAmountAfterDiscount: 0, // 优惠后的包装费金额
    discountList: [], // 优惠信息列表
    fruitFans: 0, // 是否为果粉（0:非果粉 1:果粉）
    /**
     * 是否展示超重弹窗
     */
    showOverWeightPopup: false,
    showDiscountDetail: false,
    showDiscountDetailArrow: false,
    /**充值活动数据 */
    rechargeData: [],
    /**是否选择了随单充活动 */
    isSelectRechargeAct: false,
    /**当前选择的随单充活动详情 */
    rechargeAct: {},
    /**当前随单充对应支付信息 */
    rechargePayInfo: {},
  },
  _data: {
    firstSettle: true,
    timelyGoodsTotalPrice: 0, // 及时达商品总金额
    deliveryTimeSplit: [],
    couponRules: [], // 优惠券类型互斥规则
    isRefreshOrder: false,
    cityInfo: {
      cityID: '',
      storeID: '',
      storeCode: '',
      storeName: '',
      cityCode: '',
      cityName: '',
      deliveryCenterCode: '',
    },
    isCanSubmit: true,
    isShowInvaildMessage: true, // 是否显示商品失效提示
    uuid: '', // 结算标示
    settleGoodsType: 'both', // 结算商品类型，both：全国送 + 及时达 timely：及时达 B2C：全国送
    isShowDeliveryWayNoMatch: true, // 是否展示结算方式与结算商品属性不匹配提示（配送上门：含有自提专享,无法购买，门店自提：含有配送专享，无法购买）
    isFirstScroll: true, // 是否首次滚动页面
    isHandleSelfTakeStoreDistance: true, // 门店自提时，是否需要判断用户当前定位附近最近的门店逻辑（进入确认订单页只处理一次）
    lastUuid: '', // 上次结算接口返回的uuid
    blessCardRelated: false, // 与是否选择贺卡相关；勾选和取消贺卡时，要传上一次结算的uuid，其余时间不用
    isCombineOrder: false,
    applyCardSeleted: false, // 是否勾选开卡
    firstSetMouthCardMode: true, //是否展示月卡浮窗
    showVipCardTipsNums: 0, // 展示提示弹窗次数，进入页面后只展示一次
    isPageShowSensors: true,
    storePhone: '',
    ViewVipRegion: false,
    VipSaveMoney: false,
    subOrderList: [],
    isFirstSettle: 'Y', // 为Y，则匹配最优优惠券和代金券
    isNeedMatchExchangeCard: 'Y', // 为Y，则匹配兑换卡
    currentChooseVoucher: [], // 当前选中的代金券
    cardActivityCode: '', // 参与续费活动的编码
    giftCouponInfo: {}, // 续费送券详情
    openVipCardType:'',//开通会员类型,
    settleResUniqCode: '',
    ruleLadderId:'', //礼包id
    isSelectVipCouponSettle: false, // 开卡页面返回的结算不匹配最优优惠券和代金券
    virtualSettleData:{}, //虚拟勾选结算返回的数据
    payAmountCompareCurrentVirtual: '',
    isReportSubmitClick: true, //是否上报点击立即支付
    confirmOrderPromise: {},
    currentNewActUuid: '',
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function (options) {
    this.setModelStyle();
    this.initData(options);
    this.setSettleGoodsType();
    await this.initAddressInfo()
    this.getCurrLocToAddressDistance();
    this.orderSettle();
  },
  onUnload() {
    app.event.off('depositSuccess');
    wx.removeStorageSync('deliveryToDoorSelectInfo')
    wx.removeStorageSync('selfTakeSelectInfo')
  },
  /**
   * 是否为果粉价灰度会员
   */
  async setGrayScaleUser(settleParams) {
    const { cityCode, storeCode } = settleParams || {}
    const isFansGray = await checkIsFansGray(cityCode, storeCode)
    this.setData({
      isFruitFansGray: isFansGray
    })
  },
  /**
   * 设置页面初始数据
   */
  initData(options) {
    const selectedAddressInfo = wx.getStorageSync('selectedAddress') || {};
    const settlementGoodsList = options.homeDeliveryObj
      ? JSON.parse(options.homeDeliveryObj).goodsList
      : [];
    if (fruitCartStore.actPriceNewMaxDiscountUuid) {
      this._data.currentNewActUuid = fruitCartStore.actPriceNewMaxDiscountUuid
    }
    Object.assign(selectedAddressInfo, {
      labelStyle: this.getLabelStyle(selectedAddressInfo.label),
    });
    this.setData({
      settlementGoodsList,
      isIphoneX: app.globalData.isIphoneX,
    });
    const differenceAmountStart = options.homeDeliveryObj
      ? JSON.parse(options.homeDeliveryObj).differenceAmountStart
      : 0;
    /**及时达商品是否超重和限制的最大重量 */
    const timelyGoodsIsOverWeight = fruitCartStore ? fruitCartStore.timelyGoodsIsOverWeight : false
    const timelyGoodsMaxWeight = fruitCartStore ? fruitCartStore.timelyGoodsMaxWeight : 20
    const timelyGoodsObj = (settlementGoodsList || []).find(val => val.takeawayAttr === '及时达') || {}
    // 根据购物车传过来的数据判断，未达起送金额，默认为自提模式；及时达商品超重也默认为自提
    if (differenceAmountStart > 0 || (timelyGoodsIsOverWeight && Object.keys(timelyGoodsObj).length)) {
      this.setData({
        deliveryWay: 'selfTake',
        timelyGoodsIsOverWeight,
        timelyGoodsMaxWeight
      });
      timelyGoodsIsOverWeight && sensors.exposureReport('1302_130210001')
    }
    this.initCityInfo(wx.getStorageSync('timelyCity'));
  },

  initCityInfo(cityInfo) {
    const {
      storeID,
      cityID,
      cityCode,
      storeCode,
      deliveryCenterCode,
      storeName,
      cityName
    } = cityInfo || {};
    Object.assign(this._data.cityInfo, {
      cityID,
      storeID,
      cityCode,
      storeCode,
      deliveryCenterCode,
      storeName,
      cityName
    });
  },

  async initAddressInfo() {
    const selectedAddressInfo = wx.getStorageSync('selectedAddress') || {};
    if (selectedAddressInfo.isDefault === 'Y') {
      this.setData({
        'orderDeliveryAddressInfo.deliveryToDoor':Object.assign({}, selectedAddressInfo, {
          labelStyle: this.getLabelStyle(selectedAddressInfo.label),
        })
      });
    } else {
      if (this._data.settleGoodsType !== 'B2C') await this.getDeliveryAddressAroundStore()
    }
  },

  /**
   * 获取门店附近的用户的收货地址
   */
  async getDeliveryAddressAroundStore(){
    const { storeInfo } = wx.getStorageSync('timelyCity') || {}
    const { lat, lon } = storeInfo || {}
    try {
      const { data } = await app.api.getAddressByDistance({
        customerID: app.globalData.customerID,
        lat: String(lat),
        lon: String(lon)
      })
      this.setData({
        'orderDeliveryAddressInfo.deliveryToDoor': Object.keys(data).length ? data : null
      });
    } catch (error) {
      console.log('getDeliveryAddressAroundStore err', error);
      this.setData({
        'orderDeliveryAddressInfo.deliveryToDoor': null
      });
    }
  },

  /**
   * 设置scroll-view容器的高度
   */
  setModelStyle() {
    const that = this;
    wx.getSystemInfo({
      success: function ({ screenHeight, statusBarHeight, safeArea }) {
        const { top, height } = wx.getMenuButtonBoundingClientRect();
        const safeAreaBottom = screenHeight - safeArea.bottom;
        const titleHeight = height + (top - statusBarHeight) * 2;
        that.setData({
          safeAreaBottom,
          scrollViewHeight: screenHeight - statusBarHeight - titleHeight,
        });
      },
    });
  },

  /**
   * 获取底部地址提示条的高度
   */
  getAddHeight() {
    const query = wx.createSelectorQuery();
    query.select('.border-top').boundingClientRect();
    query.exec((res) => {
      if (Array.isArray(res) && res[0]) {
        this.setPageBottom(res[0].height);
      }
    });
  },

  // 根据底部的地址提示条动态展示页面底部高度
  setPageBottom(height) {
    this.setData({
      addMargin: height,
    });
  },

  /**
   * 获取页面主内容高度
   */
  getContainerHeight() {
    const query = wx.createSelectorQuery();
    query.select('.confirm-box').boundingClientRect();
    query.exec((res) => {
      if (Array.isArray(res) && res[0]) {
        this.setPageOverHeight(res[0].height);
      }
    });
  },

  /**
   * pageOverOneScreen: 页面主内容高度是否超过一屏 + 100 （100为滚动界点，超出100展示底部地址提示条）
   * @param {*} height 页面主内容高度
   */
  setPageOverHeight(height) {
    const { scrollViewHeight = 0 } = this.data;
    this._data.pageOverOneScreen = height - scrollViewHeight > 100;
  },
  /**
   * @desc 设置月卡提示条
   */
  setMouthCardOverHeight(type) {
    if(type !== APPLY_CARD_TYPE_ENUM.MONTH_CARD) return
    const cardHeight = 270;
    const { scrollViewHeight = 0 } = this.data;
    const page = wx.createSelectorQuery();
    page.select('.confirm-box').boundingClientRect();
    page.exec((res) => {
      if (Array.isArray(res) && res[0]) {
        this.setData({
          showMouthCardMode: res[0].height - cardHeight - scrollViewHeight > 0,
        });
      }
    });
  },
  /**
   * @desc 滚动到飞享月卡
   */
  scrollBottom() {
    this.setData({
      scrollId: 'mouthcard',
      showMouthCardMode: false,
    });
  },
  /**
   * 设置结算商品类型
   */
  setSettleGoodsType() {
    const { settlementGoodsList = [] } = this.data;
    const inTimelyGoods = settlementGoodsList.find(
      (item) => item.takeawayAttr === '及时达'
    );
    const inB2CGoods = settlementGoodsList.find(
      (item) => item.takeawayAttr === 'B2C'
    );
    this.setData({
      hideUnionPay: Boolean(inB2CGoods),
    })
    if (inTimelyGoods && inB2CGoods) {
      return;
    }
    if (inTimelyGoods) {
      this._data.settleGoodsType = 'timely';
      return;
    }
    this._data.settleGoodsType = 'B2C';
  },

  onShow() {
    this._data.isPageShowSensors = true;
    this.setVipMemberData();
    // 充值成功 需要刷新订单信息
    app.event.on('depositSuccess', (e) => {
      this._data.isRefreshOrder = true;
      app.event.off('depositSuccess');
    });
    // 选择地址、选择门店后需要重新结算
    if (this._data.isRefreshOrder) {
      this._data.isFirstSettle = 'N'
      this.orderSettle();
      this._data.isRefreshOrder = false;
    }
    setTimeout(() => {
      this.setBlessContent();
      this.getAddHeight();
      this.getContainerHeight();
    }, 500);
  },
  onHide() {
    this._data.isPageShowSensors = false;
  },
  setBlessContent() {
    const { content, reveiver, sender } = getBlessCardInfo();
    let formattedBlessCardContent = '';
    const colonReg = /[:：]/g;
    const dashReg = /[--]/g;
    if (reveiver) {
      if (colonReg.test(reveiver[reveiver.length - 1])) {
        formattedBlessCardContent += reveiver;
      } else {
        formattedBlessCardContent += `${reveiver}：`;
      }
    }
    formattedBlessCardContent += content;
    if (sender) {
      if (dashReg.test(sender.slice(0, 2))) {
        formattedBlessCardContent += sender;
      } else {
        formattedBlessCardContent += `--${sender}`;
      }
    }
    this.setData({
      blessCardContent: content,
      blessCardReceiver: reveiver,
      blessCardSender: sender,
      formattedBlessCardContent,
    });
  },

  /**
   * 设置心享会员相关数据
   */
  setVipMemberData() {
    const { superVipStatus } = app.globalData;
    this.setData({
      superVipStatus, // 心享会员类型
    });
  },
  // 选择飞享卡
  chooseMouthCard(val) {
    const {
      isSelected,
      refresh,
      chooseRenewType,
      activityCode,
      giftCouponInfo,
      buryingPointType = '',
      ruleLadderId = ''
     } = val.detail;
    Object.assign(this._data, {
      applyCardSeleted: isSelected,
      openVipCardType: chooseRenewType,
      cardActivityCode: activityCode,
      giftCouponInfo,
      ruleLadderId
    })
    this.setData({
      vipCardIsSeleted: isSelected,
    });
    if (refresh) {
      // 选择月卡，匹配最优代金券和优惠券
      this._data.isFirstSettle = 'Y'
      this._data.isNeedMatchExchangeCard = 'Y'
      if(this._data.isSelectVipCouponSettle){
        this._data.isFirstSettle = 'N'
        this._data.isSelectVipCouponSettle = false
      }
      this.orderSettle({
        optionOrigin: 'mouthCardSelected'
      });
    }
    // 埋点
    let sensorsKey = '';
    if (MonthCardType.includes(buryingPointType) ) {
      sensorsKey = isSelected
        ? 'selectedMouthCardModeForMonth'
        : 'cancelMouthCardModeForMonth';
    } else if(YearCardType.includes(buryingPointType)) {
      sensorsKey = isSelected
        ? 'selectedMouthCardModeForYear'
        : 'cancelMouthCardModeForYear';
    }
    sensorsKey && sensors.track('MPClick', sensorsKey);
  },
  /**
   * @desc 获取开卡内容
   */
  async getVipCardInfo(params) {
    const { customerID = -1 } = app.globalData;
    const { list = [], notShowReason = '' } = this.data.vipCardInfo
    const { storeCode: homeStoreCode = '' } = this._data.cityInfo
    const { storeCode: currentStoreCode = '' } = this.data.storeInfo
    try {
      // 已经获取过vipCardInfo不再请求
      if (!list.length && !notShowReason) {
        const { data = {} } = await app.api.getVipCardInfo({
          customerID,
          uniqueOrder: '',
          storeCode: currentStoreCode || homeStoreCode
        });
        this.setData({
          vipCardInfo: data,
        });
      }
      const { list:packageList = []  } = this.data.vipCardInfo
      const initialVipInfo = packageList.length ? packageList[0] : {}
      const { show = false } = this.data.vipCardInfo
      const {
        couponList = [],
        money: mouthCardMoney = 0,
        type = '',
      } = initialVipInfo;
      // 监听开通月卡提示条
      if (this._data.firstSetMouthCardMode) {
        this.setMouthCardOverHeight(type);
        this._data.firstSetMouthCardMode = false;
      }
      this.getMouthCardSaveMoney({
        ...params,
        mouthCoupon: couponList
      });
      // 埋点
      const { mouthCardSaveMoney = 0 } = this.data;
      const ViewVipRegion = show && !!couponList.length; //是否展示心享专区
      const VipSaveMoney = mouthCardSaveMoney >= mouthCardMoney && type === APPLY_CARD_TYPE_ENUM.MONTH_CARD; //是否展示一单回本
      this._data.ViewVipRegion = ViewVipRegion;
      this._data.VipSaveMoney = VipSaveMoney;
      this.sensorsPageShowMouthCard({
        ViewVipRegion,
        VipSaveMoney,
      });
    } catch (error) {
      console.log('getVipCardInfo error', error);
      this.sensorsPageShowMouthCard();
      this.setData({
        vipCardInfo: {},
      });
    }
  },
  /**
   * @desc 上报页面浏览事件
   * @param { Boolean } ViewVipRegion //是否展示心享专区
   * @param { Boolean } ViewVipRegion //是否展示一单回本
   */
  sensorsPageShowMouthCard({
    ViewVipRegion = false,
    VipSaveMoney = false,
  } = {}) {
    if (this._data.isPageShowSensors) {
      this._data.isPageShowSensors = false;
      const { showSelectVipCoupon = '',couponList = [], superVipStatus = '', vipCardIsSeleted = false} = this.data
      sensors.pageScreenView({
        ViewVipRegion,
        VipSaveMoney,
        monthCardGuide: !couponList.length && superVipStatus === 'C' && showSelectVipCoupon && !vipCardIsSeleted ? '有':'无'
      })
    }
  },
  /**
   * 获取当前定位地址距离收获地址的距离
   */
  async getCurrLocToAddressDistance() {
    try {
      if (this._data.settleGoodsType === 'B2C') {
        // 全国送不展示超距离提示
        return;
      }
      const userCurrLoca = wx.getStorageSync('userCurrLoca') || null;
      const { orderDeliveryAddressInfo } = this.data;
      if (!userCurrLoca || !orderDeliveryAddressInfo.deliveryToDoor) {
        return;
      }
      const {
        location: { lat: fromLat, lng },
      } = userCurrLoca;
      const { lon, lat } = orderDeliveryAddressInfo.deliveryToDoor;
      const distance = await commonObj.deliveryDistance(
        { lat: fromLat, lon: lng, isNeedTransform: false },
        { lat, lon }
      );
      this.setData({
        showExceedDistanceTip: distance > 1000,
      });
    } catch (error) {}
  },

  pageScroll(e) {
    // 获取滚动条当前位置
    if (this._data.isFirstScroll) {
      this.getContainerHeight(); // 初始化计算的主体内容高度可能不准（页面元素未完全加载完），这里再计算一次
      this._data.isFirstScroll = false;
    }
    const { pageOverOneScreen } = this._data;
    const { orderDeliveryAddressInfo, deliveryWay, showAddrNotice } = this.data;
    const { scrollTop = 0 } = e.detail || {};
    if (orderDeliveryAddressInfo[deliveryWay] && pageOverOneScreen) {
      if (scrollTop > 100) {
        !showAddrNotice &&
          this.setData({
            showAddrNotice: true,
          });
      } else {
        showAddrNotice &&
          this.setData({
            showAddrNotice: false,
          });
      }
    }
    if (pageOverOneScreen && scrollTop > 100) {
      this.setData({
        showMouthCardMode: false,
      });
    }
  },

  /**
   * 心享会员状态变更触发
   */
  superVipStatusChange() {
    this.orderSettle();
  },

  /**
   * @desc 请求订单确认接口获取订单信息
   */
  async orderSettle(params = {}) {
    try {
      this._data.isCanSubmit = false;
      if (this.data.deliveryWay === 'selfTake') {
        await this.setInitSelfTakeStore();
      }
      const settleParams = this.buildSettleParams(params);
      // 查询灰度信息
      await this.setGrayScaleUser(settleParams)
      const { isFruitFansGray } = this.data
      const res = await app.api[isFruitFansGray ? 'orderSettleGrayScale' : 'orderSettle'](settleParams)
      this.handelOrderSettleData({res, params})
    } catch (error) {
      console.log('orderSettle1', error);
      app.monitor.report({
        label: 'fruit.settle',
        errMsg: error
      })
      this.sensorsPageShowMouthCard('error');
      this.setSettleErrorData()
      if (error.statusCode === 429) {
        wx.showModal({
          content: '活动太火爆了，请稍后重试！',
          showCancel: false,
          confirmText: '我知道了',
          success: (res) => {
            if (res.confirm) {
              wx.navigateBack();
            }
          },
        });
        return;
      }
      this.clearNoUseStoreAddress(error);
      const { errorCode = '', description = '' } = error || {};
      if (['35407', '450047'].includes(String(errorCode))) {
        if (String(errorCode) === '35407') {
          this.handleAddressNoDeliveryStore();
        } else {
          this.handleSelectIntegralError({content: '活动信息已更新'})
        }
      } else if (
        ['450208', '450209', '450207', '440401', '80001'].includes(
          String(errorCode)
        ) &&
        this._data.applyCardSeleted
      ) {
        // 开卡报错，禁用勾选
        let lockDesc = '';
        if (String(errorCode) === '80001') {
          lockDesc =
            '其他订单已开通心享会员，不可重复开通;若未开通成功请检查订单是否完成支付';
        }
        await app.showModalPromise({ content: lockDesc || description });
        this.cancelApplyCard({
          status: false,
          refresh: true,
        });
        // 450209特殊处理，以不勾选的参数重新结算，并且刷新开卡信息
        // vipCardInfo赋值为空，settle之后会刷新开卡信息
        if(String(errorCode) === '450209'){
          this.setData({
            vipCardInfo:{}
          })
        }
      } else {
        app.apiErrorDialog(error);
      }
    }
    this._data.isCanSubmit = true;
  },
  /**
   * @desc 处理orderSettle返回信息
   */
  async handelOrderSettleData({res = {}, params = {}}){
    const {
      pickerChange = true,
      optionOrigin = ''
    } = params;
    const {
      actInvalidCode,
      toastContent,
      goodsInvalidCode,
      couponInfo = {},
      vipFree,
      deliveryTimeSplit,
      uuid,
    } = res.data || {};
    const storeChange = await this.checkStoreChange(res.data);
    const { goon, hasShowNewActModal } = await handleSettleSuccPrompt({ 
      actInvalidCode, 
      goodsInvalidCode, 
      toastContent,
      isShowGoodsInvaildMessage: this._data.isShowInvaildMessage
    })
    if (!goon) return
    // 处理兑换卡
    this.setExchangeCardInfo(res.data);
    this.setSettleGoodsInfo(res.data);
    this.cacheSettleUniqCode(res.data)
    // 灰度用户单独处理优惠券、代金券
    this.setVoucherInfo(res.data);
    this.setGrayCoupon(couponInfo);
    this.handleSettlePriceInfo(res.data, optionOrigin);
    this.setData({ vipFree: vipFree || 0 });
    this.setDeliveryTime({
      pickerChange,
      deliveryTimeSplit,
    });
    const showed = await this.setOrderSettleTip({
      data: res.data,
      storeChange,
      hasShowNewActModal,
      optionOrigin,
    });
    Object.assign(this._data, {
      isShowInvaildMessage: true,
      isShowDeliveryWayNoMatch: true,
      lastUuid: uuid,
    });

    let rechargeData = []
    if (this._data.settleGoodsType === 'timely') {
      rechargeData = await getOrderInlineRechargeData(
        res.data.mainBalance,
        res.data.payAmount,
        res.data.store
      )
    }

    //  展示随单充活动时，不执行随单开通心享后续逻辑。并清空随单开通心享参数
    if (rechargeData.length) {
      //  随单充活动未曾曝光
      if (!this.rechargeDataExposure) {
        this.rechargeDataExposure = true
        const activityCodes = rechargeData.map(i => i.activityCode)

        sensors.exposureReport('1302_130211001', {
          activity_ID: [...new Set(activityCodes)]
        })
      }
      this.setData({
        rechargeData: rechargeData,
      })
      this.resetMonthCardState()
    } else {
      this.setData({
        rechargeData: [],
      })
      this.unSelectOrderInlineRecharge()
      // 飞享卡模块
      this.getVipCardInfo(res.data);
    }
    setTimeout(() => {
      this.getAddHeight();
    }, 500);
    this.setPayAmountRolling(res.data);
    this._data.firstSettle = false
  },
  /**
   * @desc 虚拟勾选结算
   */
  async orderVirtualSettle(){
    try {
      const params = {
        vipCardPageSelected : 'Y',
        matchingTheBestCoupon : 'Y'
      }
      const settleParams = this.buildSettleParams(params);
      const { isFruitFansGray } = this.data
      const res = await app.api[isFruitFansGray ? 'orderSettleGrayScale' : 'orderSettle'](settleParams)
      Object.assign(this._data,{
        virtualSettleData: res
      })
      const {
        data:{
          payAmount: payAmountVirtual = 0
        } = {}
      } = this._data.virtualSettleData
      const { payAmount = 0 } = this.data
      this.setData({
        payAmountVirtual
      })
      this._data.payAmountCompareCurrentVirtual = Number(payAmount) - Number(payAmountVirtual)
    } catch (error) {
      console.log('orderVirtualSettle error', error);
      Object.assign(this._data,{
        virtualSettleData: {}
      })
      this._data.payAmountCompareCurrentVirtual = ''
    }
    return this._data.virtualSettleData
  },
  /**
   * @desc 结算出错设置数据
   */
  setSettleErrorData(){
    this.setData({
      showSelectVipCoupon: ''
    })
  },
  /**
   * @desc 关闭月卡区域
   */
  cancelApplyCard(params) {
    const monthCardComp = this.selectComponent('#mouthcard');
    monthCardComp.clickIcon(params);
  },
  /**
   * @desc 开通/取消月卡
   */
  changeVipCardSelected({ selected = true }){
    const monthCardComp = this.selectComponent('#mouthcard');
    monthCardComp.changeVipCardSelected({selected});
  },
  /**
   * @desc 构建开卡参数
   */
  genApplyCardInfo({isSubmit = false, vipCardPageSelected = ''}) {
    if (this.data.isSelectRechargeAct) {
      return {}
    }

    const { applyCardSeleted: curretPageApplyCardSeleted = false, giftCouponInfo = {}, settleResUniqCode } = this._data;
    // 获取当前开通状态
    const applyCardSeleted = this.getVipCardSelectedStatus(vipCardPageSelected, curretPageApplyCardSeleted)
    const { list = [], payMemberEndTime = '', couponExpireInfo } = this.data.vipCardInfo
    if (!Array.isArray(list)) return {}
    // 获取当前选中couponInfo
    const couponInfo = this.getVipCardCouponInfo(list,vipCardPageSelected)
    if (!couponInfo) return {}
    const {
      couponList = [],
      uniqueOrder = '',
      cardSetPackageValue, // 开卡套餐值
      money, // 开卡金额，为0时不可勾选，所以这里不给默认值
      type = APPLY_CARD_TYPE_ENUM.MONTH_CARD,
    } = couponInfo;
    const applyYearCard = [APPLY_CARD_TYPE_ENUM.YEAR_CARD, APPLY_CARD_TYPE_ENUM.RENEW_YEAR_CARD].includes(type);
    const { startTime, endTime } = couponExpireInfo[applyYearCard ? 'year' : 'month']
    const applyCardInfo = {
      setPackageAmount: money,
      applyCardType: type,
      uniqCode: isSubmit ? settleResUniqCode : uniqueOrder,
      setPackageValue: cardSetPackageValue,
      // batchNumList 为券包信息+加赠活动送券信息
      batchNumList: [...couponList.map((coupon) => {
        const { amount, batchNum } = coupon;
        return {
          batchNum,
          type: '1', // 券包为1
          number: amount,
          effectiveDatetime: startTime,
          expirationDatetime: endTime,
        };
      }), ...(() => {
        const { batchNum, startTime, expireTime } = giftCouponInfo
        // 加赠券不传时间
        return batchNum ? [{
          batchNum,
          type: '2', // 年卡&&加赠活动为2
          number: 1,
          effectiveDatetime: startTime,
          expirationDatetime: expireTime
        }] : []
      })()],
      cardActivityCode: this._data.cardActivityCode,
      ruleLadderId: this._data.ruleLadderId
    };
    return applyCardSeleted ? applyCardInfo : {};
  },
  /**
   * @desc 获取勾选状态
   */
  getVipCardSelectedStatus(vipCardPageSelected, curretPageApplyCardSeleted){
    return vipCardPageSelected ? vipCardPageSelected === 'Y' : curretPageApplyCardSeleted
  },
  /**
   * @desc 获取参数couponInfo
   */
  getVipCardCouponInfo(list, vipCardPageSelected){
    if(vipCardPageSelected){
      return list.find(el => el.type === (vipCardPageSelected === 'Y' ? APPLY_CARD_TYPE_ENUM.MONTH_CARD : ''))
    }
    return list.find(el => el.type === this._data.openVipCardType)
  },
  /**
   * 设置初始化自提门店信息
   */
  async setInitSelfTakeStore() {
    if (!this._data.isHandleSelfTakeStoreDistance) {
      return;
    }
    // 只有全国送商品
    if (this._data.settleGoodsType === 'B2C') {
      return;
    }
    this._data.isHandleSelfTakeStoreDistance = false;
    const { store } = await this.checkSelfTakeStoreDistance();
    this.setData({
      'orderDeliveryAddressInfo.selfTake': store,
    });
  },

  /**
   * 判断当前首页门店，是否为用户当前定位附近最近的支持自提的门店
   */
  async checkSelfTakeStoreDistance() {
    // 无当前定位地址附近最近的门店
    const nearStore = wx.getStorageSync('currLocaNearSelfTakeStore') || {};
    const {
      storeCode: nearStoreCode,
      shortName: nearShortName,
      storeName: nearStoreName,
      lat: nearLat,
      lon: nearLon,
    } = nearStore;
    const homeStore = this.getHomeStoreInfo();
    if (!nearStoreCode) {
      return {
        store: homeStore,
      };
    }
    const { storeCode, lat, lon } = homeStore || {};
    if (!storeCode) {
      return {
        store: homeStore,
      };
    }
    // 定位地址附近最近的门店与首页门店为同一个门店
    if (String(nearStoreCode) === String(storeCode)) {
      return {
        store: homeStore,
      };
    }
    const userCurrLoca = wx.getStorageSync('userCurrLoca') || null;
    if (!userCurrLoca) {
      return {
        store: homeStore,
      };
    }
    try {
      const {
        location: { lat: fromLat, lng },
      } = userCurrLoca;
      // 分别计算当前定位地址距离首页门店，最近自提门店的距离
      const promises = [
        commonObj.deliveryDistance(
          {
            lat: fromLat,
            lon: lng,
            isNeedTransform: false,
          },
          {
            lat,
            lon,
          }
        ),
        commonObj.deliveryDistance(
          {
            lat: fromLat,
            lon: lng,
            isNeedTransform: false,
          },
          {
            lat: nearLat,
            lon: nearLon,
          }
        ),
      ];
      const [homeDistance, nearDistance] = await Promise.all(promises);
      if (homeDistance > nearDistance) {
        const modalRes = await app.showModalPromise({
          content: `当前门店距离您${
            homeDistance < 1000 ? `${homeDistance}米` : '大于1公里'
          }，是否为您切换最近门店${nearShortName || nearStoreName || ''}`,
          showCancel: true,
          confirmText: '切换门店',
          cancelText: '暂不切换',
        });
        if (modalRes) {
          return {
            store: nearStore,
          };
        }
      }
    } catch (error) {}
    return {
      store: homeStore,
    };
  },

  /**
   * 获取首页门店信息
   */
  getHomeStoreInfo() {
    const {
      storeID,
      storeInfo,
      cityCode,
      storeCode,
      cityID,
      deliveryCenterCode,
      cityName,
    } = wx.getStorageSync('timelyCity') || {};
    const {
      isSupportTake = '',
      lat,
      lon,
      shortName,
      openingTime,
      address,
      storeName,
      startTime,
      endTime,
    } = storeInfo || {};
    if (!storeID || !storeInfo) {
      return null;
    }
    if (!isSupportTake || isSupportTake.toUpperCase() !== 'Y') {
      return null;
    }
    return {
      openingTime,
      cityID,
      lat,
      lon,
      address,
      storeID,
      storeCode,
      shortName,
      storeName,
      cityCode,
      deliveryCenterCode,
      cityName,
      startTime,
      endTime,
    };
  },

  /**
   * 收货地址附近无可配送的门店
   */
  async handleAddressNoDeliveryStore() {
    const modalRes = await app.showModalPromise({
      content: '附近没有可配送的门店，您可以切换为门店自提，或者换个门店试试',
      showCancel: true,
      confirmText: '去自提',
      cancelText: '换个地址',
    });
    if (modalRes) {
      this.setDeliveryWay('selfTake');
    } else {
      this.openAddressList();
    }
  },

  /**
   * 构造结算需要的参数
   */
  buildSettleParams(params) {
    const {
      pickerChange = true,
      vipCardPageSelected = '', //selectVipCoupon 页面传参 Y/N
      matchingTheBestCoupon = '', //selectVipCoupon 页面传参 Y/N
    } = params || {};
    const {
      deliveryWay,
      settlementGoodsList,
      currSelectDeliveryInfo,
      orderDeliveryAddressInfo,
      blessCardSelected,
      selectedCouponList,
      integralSelected = false,
      inTimeGoodsList,
      integralExchangeInfo: { activityCode = '', integral = 0, integralAmount = 0 } = {},
      vipCardIsSeleted
    } = this.data;
    const {
      isFirstSettle, // 为Y 则匹配最优优惠券和代金券
      isNeedMatchExchangeCard, // 为Y 则匹配兑换卡
      currentChooseVoucher = []
    } = this._data
    const goodsList = vipCardIsSeleted ? reMatchNewUserGoodsList({
      settlementGoodsList,
      inTimeGoodsList
    }) : settlementGoodsList
    const { lat, lon, addressId } =
      orderDeliveryAddressInfo[deliveryWay] ||
      wx.getStorageSync('timelyCity') ||
      {};
    const settleParams = {
      deliveryWay: 1,
      customerID: app.globalData.customerID || -1,
      goodsList: goodsList,
      location: {
        lat,
        lon,
      },
      blessCardSelected: '', // 是否选择贺卡
    };
    settleParams.assetInfo = {}
    settleParams.isFirst = isFirstSettle
    settleParams.needMatchExchangeCard = isNeedMatchExchangeCard
    const curCouponList = selectedCouponList.map(item => {
      return {
        couponId: item.couponCode,
        discountAmount: item.couponMoney,
        type: 1
      }
    })
    const curVoucherList = currentChooseVoucher.map(item => {
      return {
        couponId: item.couponCode,
        discountAmount: item.couponMoney,
        type: 2
      }
    })
    // 非首次结算才会传用户所选
    if (isFirstSettle !== 'Y') {
      settleParams.assetInfo.couponList = [...curCouponList,...curVoucherList]
    }
    // 不需要默认匹配的兑换卡才会传用户所选
    if (isNeedMatchExchangeCard !== 'Y') {
      const curExchangeCardList = this.handleUserSelectedExchangeCard()
      settleParams.assetInfo.exchangeCardList = [...curExchangeCardList]
    }
    // 勾选参与积分兑换活动
    if (integralSelected && activityCode) {
      settleParams.assetInfo.integralExchange = [
        {
          activityCode,
          integral,
          integralAmount
        }
      ]
    }
    // selectVipCoupon页面 matchingTheBestCoupon为Y重新匹配最佳券
    if(matchingTheBestCoupon === 'Y'){
      settleParams.isFirst = 'Y'
      settleParams.assetInfo = {}
    }
    if (deliveryWay === 'deliveryToDoor') {
      const { cityID, cityCode, storeCode, deliveryCenterCode, cityName } =
        this._data.cityInfo;
      const { lastUuid, blessCardRelated } = this._data;
      Object.assign(settleParams, {
        deliveryWay: 2,
        cityID,
        storeCode,
        cityCode,
        deliveryCenterCode,
        // 这个城市名用来做配送信息城市名称展示用的
        // 但是接口里又通过城市id查了城市名
        // 或者通过门店信息查了城市名
        // 所以这个参数是多余的
        cityName,
      });
      // 如果是重新选择了配送时间，则需要传入开始配送时间
      if (!pickerChange && !!currSelectDeliveryInfo.deliveryTimeBegin) {
        settleParams.deliveryBeginTime =
          currSelectDeliveryInfo.deliveryTimeBegin;
      }
      if (blessCardRelated) {
        Object.assign(settleParams, {
          // uuid: lastUuid,
          blessCardSelected:
            BLESS_CARD_SELECT_ENUM[String(blessCardSelected).toUpperCase()],
        });
      }
      if (!blessCardSelected) {
        this._data.blessCardRelated = false;
      }
      addressId && (settleParams.addressId = addressId);
    } else {
      if (orderDeliveryAddressInfo.selfTake) {
        // 有门店信息
        const { cityID, cityCode, storeCode, deliveryCenterCode } =
          orderDeliveryAddressInfo.selfTake;
        Object.assign(settleParams, {
          cityID,
          storeCode,
          cityCode,
          deliveryCenterCode,
        });
      } else {
        // 无门店，不传门店，城市信息取首页
        const { cityID, cityCode, deliveryCenterCode } =
          wx.getStorageSync('timelyCity') || {};
        Object.assign(settleParams, {
          cityID,
          cityCode,
          deliveryCenterCode,
        });
      }
    }
    // 开卡相关
    const applyCardInfo = this.genApplyCardInfo({isSubmit: false, vipCardPageSelected});
    Object.assign(settleParams, applyCardInfo);
    return settleParams;
  },

  /**
   * 配送上门如果门店不支持配送，结算会匹配新的配送门店，这里检查结算回来的门店是否发生更改
   */
  async checkStoreChange(data) {
    const { deliveryWay } = this.data;
    // 门店自提不校验
    if (deliveryWay === 'selfTake') {
      return false;
    }
    // 只有全国送商品不校验
    if (this._data.settleGoodsType === 'B2C') {
      return false;
    }
    const { store } = data || {};
    if (!store) {
      return false;
    }
    const { id, storeCode:number, shortName, storeName, name } = store || {};
    const { storeCode, storeName: preStoreName } = this._data.cityInfo;
    if (!number || String(number) === String(storeCode)) {
      return false;
    }
    await app.showModalPromise({
      content: `${preStoreName || ''}无法配送上门，已为您切换到${
        shortName || storeName || name
      }`,
      showCancel: false,
      confirmText: '我知道了',
    });
    // 更改存储门店信息
    Object.assign(this._data.cityInfo, {
      storeID: id,
      storeCode: number,
    });
    return true;
  },

  /**
   * 设置结算商品数据
   */
  setSettleGoodsInfo(data) {
    const { inTimeGoodsList, b2cGoodsList, unAvailableGoosList, fruitFans } = data;
    this.setData({
      paraGoodsList: inTimeGoodsList.concat(b2cGoodsList),
      inTimeGoodsList,
      b2cGoodsList,
      invalidGoodsList: unAvailableGoosList,
      fruitFans
    });
  },
  /**
   * @desc 计算月卡节省费用(商品+优惠券)
   * params包含了settle返回的结果
   */
  getMouthCardSaveMoney(params) {
    const {
      couponInfo = {},
      mouthCoupon = [], // getVipCardInfo接口返回的券列表
      canUseCouponAmount = 0, //可用券商品金额
      vipPriceDiscount = 0, //心享价格节省金额
    } = params;
    if(!mouthCoupon.length) return
    // 商品心享价优惠金额
    const goodSaveMoney = Number(vipPriceDiscount)
    // console.log('商品心享价优惠金额',goodSaveMoney)
    // 心享优惠券优惠金额
    let couponSaveMoney = 0;

    const totalAmount = Number(canUseCouponAmount);
    // console.log("可用券的商品金额",totalAmount)
    // 自身可用不可用券都得计算
    const { availableList = [], unavailableList = [] } = couponInfo;
    // 剔除掉未生效的券，获取已有可用券
    const selfCouponList = availableList.concat(unavailableList.reduce((acc, cur) => {
      const {startTime} = cur
      if (!startTime) return acc
      if (new Date(startTime).getTime() > getServerTimeStamp()) return acc
      return [...acc, cur]
    }, []));
    const superVipStatus = this.data.vipCardIsSeleted ? 'F' : app.globalData.superVipStatus
    const isVip = superVipStatus !== 'C'
    // 贺卡选择信息
    Object.assign(params, {
      blessCardSelected: this.data.blessCardSelected,
      isFruitFansGray: this.data.isFruitFansGray,
      isVip
    })
    //已有心享券节省金额
    const { best: vipBestCoupon } = handleVipCouponList(selfCouponList, params)
    // 飞享卡优惠券节省金额
    const { best: newMouthBestCoupon, newCouponList: newMouthCoupon } = handleVipCouponList(mouthCoupon, params)
    // 取vipCouponList最大值
    const vipCouponSaveMoney = vipBestCoupon.mouthCardSaveMoney || 0

    // 取mouthSaveMoney最大值
    const newMouthCouponSaveMoney = newMouthBestCoupon.mouthCardSaveMoney || 0

    // 取较大优惠券
    couponSaveMoney =
      newMouthCouponSaveMoney > vipCouponSaveMoney
        ? newMouthCouponSaveMoney
        : vipCouponSaveMoney;

    // 设置选择月卡优惠券文案
    const expectedCoupon = newMouthCouponSaveMoney > vipCouponSaveMoney ? newMouthBestCoupon : vipBestCoupon
    this.getVipVoucherPackage({expectedCoupon,totalAmount: expectedCoupon.canUseCouponTotalHeartMoney,selfCouponList})

    // 获取月卡全包最佳可用券
    const packageBestCouponBatchNum = newMouthBestCoupon.batchNum || ''

    // 最优券----取defaultBetter优惠券的batchNum
    let useLimitCouponKey = '';
    let useLimitCouponMoney = 0;
    let bestCouponIsVipCoupon = false
    if(couponInfo.defaultBetter && couponInfo.defaultBetter.length){
      const allBestCoupon = couponInfo.defaultBetter.filter(item => item.couponWay !== '5')
      if(allBestCoupon.length){
        const allBestCouponObj = allBestCoupon[0]
        useLimitCouponKey = allBestCouponObj.batchNum || ''
        useLimitCouponMoney = allBestCouponObj.couponMoney || 0
      }
    }
    // 月卡券包包括最优券
    const couponPackageKeys = newMouthCoupon.map(item => { return item.batchNum })
    const hasBestPackageCoupon = useLimitCouponKey && couponPackageKeys.includes(useLimitCouponKey)
    // 最优券是否开通月卡后可使用的心享券
    if(couponSaveMoney && (hasBestPackageCoupon || (couponSaveMoney > useLimitCouponMoney)) ){
      bestCouponIsVipCoupon = true
    }

    const mouthCardSaveMoney =
      Number(goodSaveMoney) +
      Number(couponSaveMoney)

    // 本单可用券批次号
    const availableListKey = availableList.map(item=>{
      return item.batchNum
    })
    this.setData({
      useLimitCouponKey,
      bestCouponIsVipCoupon,
      mouthCardSaveMoney,
      availableListKey,
      packageBestCouponBatchNum,
      vipCardInfo: {
        ...this.data.vipCardInfo,
        heartPriceSaveMoney: Number(goodSaveMoney),
        vipCouponSaveMoney: Number(couponSaveMoney)
      },
    });
  },
  /**
   * @desc 设置最佳优惠券相关文案
   */
  getVipVoucherPackage({expectedCoupon = {}, totalAmount = 0, selfCouponList = []}){
    const { couponWay = '', couponValue = '' } = expectedCoupon
    const { list = [] } = this.data.vipCardInfo
    if(list.length !== 1) return
    let desc = ''
    // 满减
    if ([1, 3].includes(Number(couponWay))) {
      const vipPrice = Number(couponValue / 100)
      desc = `开通会员可用${vipPrice}元券`
    }
    // 满折
    else if ([2, 4].includes(Number(couponWay))) {
      const discountNum = Number(couponValue / 10)
      desc = `开通会员可用${discountNum}折券`
    }
    // 是否有可用免运券
    const hasVipFreightCouponList = this.getCanUseVipFreightCoupon(selfCouponList)
    if(hasVipFreightCouponList.length && !desc){
      desc = '开通会员可用免运券'
    }
    if(!totalAmount){
      desc = ''
    }
    this.setData({
      showSelectVipCoupon: desc
    })
  },
  /**
   * @desc 返回本单可用心享免运券
   * 门槛： 商品金额+餐盒费
   */
  getCanUseVipFreightCoupon(selfCouponList = []){
    const {
      totalFreight = 0,
      goodsTotalAmount = 0,
      blessCardAmount = 0,
      packAmount = 0,
      totalBoxAmount = 0,
      vipFree = 0,
      blessCardSelected = false
    } = this.data
    const useCouponTotalAmount = Number(goodsTotalAmount) - Number(vipFree) + Number(totalBoxAmount)
    const freeFreightCouponList = totalFreight !== 0 ? selfCouponList.filter(
      (item) =>
        item.couponWay === '5' &&
        item.isOnlyVip === 'Y' &&
        (item.limitAmount === 0 || item.limitValue <= useCouponTotalAmount)
    ): [];
    return freeFreightCouponList
  },
  /**
   * @desc 更新开通月卡提示文案
   */
  updateOpenCardInfo(val) {
    const { openSub = '', fixOpenTitle = '',showModeMoney } = val.detail;
    this.setData({
      openCardContent: {
        fixOpenTitle,
        openSub,
        showModeMoney,
      }
    });
  },
  /**
   * @desc 设置支付方式，开通年卡时，不允许钱包支付
   * lack 余额不足支付
   * mainBalance 主钱包余额
   */
  setPayWay({ mainBalance, lack, payAmount, optionOrigin }) {
    this.setData({
      isMixPay: false,
    })
    const isFromSelectCard = optionOrigin === 'mouthCardSelected';
    const {
      selectWxPay: cacheSelectWxPay,
      selectUnionPay: cacheSelectUnionPay,
      vipCardIsSeleted,
      isSelectVoucher,
      isSelectExchangeCard
    } = this.data;
    // 代金券刚好抵消支付金额
    if (payAmount <= 0 && (isSelectVoucher || isSelectExchangeCard)) {
      this.setData({
        selectPagodaPay: false,
        selectWxPay: false,
        selectUnionPay: false,
        disableUnionPay: false,
        disablePay: true,
      })
      return
    } else {
      this.setData({
        disablePay: false,
      })
    }
    cacheSelectUnionPay && vipCardIsSeleted && wx.showToast({
      title: '此服务不支持云闪付支付',
      icon: 'error',
    })
    // 余额为0 则不支持余额支付
    if (mainBalance === 0) {
      this.setData(Object.assign({
        selectPagodaPay: false,
      }, isFromSelectCard && !vipCardIsSeleted ? {
        selectWxPay: cacheSelectWxPay,
        selectUnionPay: cacheSelectUnionPay,
        disableUnionPay: false,
      } : {
        selectWxPay: true,
        selectUnionPay: false,
        disableUnionPay: vipCardIsSeleted,
      }))
      return
    }
    // 余额不足，则支持混合支付
    if (lack) {
      this.setData(Object.assign({
        isMixPay: true,
        selectPagodaPay: true,
      }, isFromSelectCard && !vipCardIsSeleted ? {
        selectWxPay: cacheSelectWxPay,
        selectUnionPay: cacheSelectUnionPay,
        disableUnionPay: false,
      } : {
        selectWxPay: true,
        selectUnionPay: false,
        disableUnionPay: vipCardIsSeleted,
      }))
      return
    }
    // 余额足，则默认余额支付
    const selectPagodaPay = app.globalData.reqPayType === 0 ? true : false; // 默认余额支付，然后看余额是否足够支付
    this.setData(Object.assign({
      selectPagodaPay: selectPagodaPay,
    }, isFromSelectCard && !vipCardIsSeleted ? {
      selectWxPay: cacheSelectWxPay && !selectPagodaPay,
      selectUnionPay: cacheSelectUnionPay && !selectPagodaPay,
      disableUnionPay: false,
    } : {
      selectWxPay: !selectPagodaPay,
      selectUnionPay: false,
      disableUnionPay: vipCardIsSeleted,
    }));
  },

  /**
   * 设置结算提示，优先级如下：
   * 1.优先门店变更弹窗
   * 2.按照结算方式区别：配送上门，失效是否有自提专享商品；门店自提，失效是否有全国送/配送专享商品（只需要提示一次）
   * 3.结算商品库存/下架失效等提示(接口返回的invaildMessage)
   */
  async setOrderSettleTip({ data, storeChange, optionOrigin, hasShowNewActModal }) {
    if (storeChange || hasShowNewActModal || optionOrigin === 'mouthCardSelected') {
      return;
    }
    const { isShowDeliveryWayNoMatch, isShowInvaildMessage } = this._data;
    const { deliveryWay, orderDeliveryAddressInfo } = this.data;
    const { invaildMessage, unAvailableGoosList } = data || {};
    let isInDeliveryWayTip = false;
    if (isShowDeliveryWayNoMatch) {
      if (Array.isArray(unAvailableGoosList) && !!unAvailableGoosList.length) {
        switch (deliveryWay) {
          case 'selfTake':
            isInDeliveryWayTip = await this.setSelfTakeSettleTip({
              unAvailableGoosList,
            });
            break;
          case 'deliveryToDoor':
            isInDeliveryWayTip = await this.setDeliverySettleTip({
              unAvailableGoosList,
            });
            break;
          default:
        }
      }
    }
    // 有配送方式提示，不展示商品库存不足提示
    if (isInDeliveryWayTip) {
      return;
    }
    if (!invaildMessage || !orderDeliveryAddressInfo[deliveryWay]) {
      return;
    }
    if (isShowInvaildMessage) {
      this.showInvalidMessage(invaildMessage);
      return true;
    }
  },
  customModalConfirm(event) {
    const selectConfirm = event.detail === 'confirm';
    const { applyVipCardTipsOptions } = this.data;
    if (selectConfirm && !applyVipCardTipsOptions.betterHasVipCard) {
      wx.navigateTo({
        url: '/userA/pages/selectCoupon/index',
      });
    }
    this.setData({
      applyVipCardTipsOptions: Object.assign(applyVipCardTipsOptions, {
        showModal: false,
      }),
    });
  },
  /**
   * @desc 勾选开通心享后，如果有选择优惠券或商品有心享价，实付金额增加滚动动效
   */
  setPayAmountRolling(settleRes) {
    console.log('this._data.firstSettle', this._data.firstSettle);
    this.setData({
      payAmountShouldRolling: !this._data.firstSettle,
    });
    this._data.firstSettle = false
  },

  /**
   * 自提模式下，查看失效商品是否有全国送/配送专享商品, 优先全国送提示
   */
  async setSelfTakeSettleTip({ unAvailableGoosList }) {
    let deliveryGoods = null;
    for (const item of unAvailableGoosList) {
      const { takeawayAttr, isDeliveryOnly } = item || {};
      if (takeawayAttr.toUpperCase() === 'B2C') {
        deliveryGoods = item;
        break;
      } else if (!!isDeliveryOnly) {
        !deliveryGoods && (deliveryGoods = item);
      }
    }
    if (!!deliveryGoods) {
      const that = this;
      const res = await app.showModalPromise({
        content: `${
          deliveryGoods.takeawayAttr.toUpperCase() === 'B2C'
            ? '全国送商品仅限快递送到家，不能自提'
            : '配送专享商品不支持门店自提哦'
        }`,
        showCancel: true,
        confirmText: '切回配送',
        cancelText: '我知道了',
      })
      if (res) {
        const { timelyGoodsIsOverWeight, timelyGoodsMaxWeight } = that.data
        if (timelyGoodsIsOverWeight) {
          wx.showToast({
            title: `本单商品重量已超过${timelyGoodsMaxWeight}kg，仅支持到店自提服务`,
            icon: 'none',
            duration: 2000
          })
          sensors.exposureReport('1302_130210005')
          return
        }
        that.setDeliveryWay('deliveryToDoor');
      }
      return true;
    }
    return false;
  },

  /**
   * 配送模式下，查看失效商品是否有自提专享商品
   */
  async setDeliverySettleTip({ unAvailableGoosList }) {
    const that = this;
    const isInSelfTakeOnlyGoods = !!unAvailableGoosList.find(
      (item) => item.eshopDeliveryType && Number(item.eshopDeliveryType) === 1
    );
    if (isInSelfTakeOnlyGoods) {
      const res = await app.showModalPromise({
        content: '自提专享商品不支持配送上门哦',
        showCancel: true,
        confirmText: '切回自提',
        cancelText: '我知道了',
      })
      if (res) {
        that.setDeliveryWay('selfTake');
      }
      return true;
    }
    return false;
  },

  /**
   * 处理结算价格信息
   */
  handleSettlePriceInfo(data, optionOrigin) {
    console.log('handleSettlePriceInfo')
    const {
      packAmount,
      wholeFreight,
      totalFreight,
      payAmount,
      mainBalance,
      vipFree,
      couponFreight,
      supportSuperVip,
      goodsTotalAmount,
      originGoodsTotalAmount,
      goodsAmountObj,
      vipPriceDiscount: vipDiscount,
      store,
      discountAmount,
      newActDiscountAmount,
      totalBoxAmount,
      couponValue,
      uuid,
      orderAmount,
      freightTip,
      timelyGoodsTotalPrice,
      existReparationActivity,
      notice,
      blessCardAmount,
      canUseCouponAmount,
      canUseCouponGoodsMemberPrice,
      totalGoodsHeartMoney,
      timelyGoodsTotalWeight,
      differenceAmountStart,
      baseFreight,
      currentBaseFreight,
      secondFreight,
      voucherCouponValue = 0,
      exchangeCardValueTotal = 0,
      integralInfo = {},
      integralExchangeList = [],
      fansFree = 0
    } = data;
    const { superVipStatus, integralSelected, integralExchangeInfo: { integralAmount = 0 } = {}, timelyGoodsMaxWeight } = this.data;
    // 包装费优惠金额
    let packAmountDiscount = 0
    if (integralSelected && integralAmount > 0) {
      packAmountDiscount = integralAmount
    }
    const lack = Number(payAmount) > Number(mainBalance);
    const totalCouponMoney = Number(couponValue) + Number(couponFreight);
    const vipPriceDiscount =
      this._data.applyCardSeleted || superVipStatus !== 'C'
        ? Number(vipDiscount)
        : 0;
    const freightDiscount = Number(wholeFreight) - Number(totalFreight);
    const totalDiscount =
      Number(discountAmount) +
      Number(newActDiscountAmount) +
      totalCouponMoney +
      vipPriceDiscount +
      freightDiscount +
      Number(voucherCouponValue) +
      Number(exchangeCardValueTotal) +
      Number(packAmountDiscount) +
      Number(fansFree);
    this.setPayWayHandle = () => this.setPayWay({ mainBalance, lack, payAmount, optionOrigin });
    this.setPayWayHandle()
    this.setData({
      /**三方支付金额（存在支付金额为0的情况） */
      thirdPayAmount: Math.max(Number(payAmount) - Number(mainBalance), 0),
      orderAmount,
      lack,
      mainBalance,
      packAmount: Number(packAmount),
      blessCardAmount,
      totalFreight: Number(totalFreight),
      wholeFreight: Number(wholeFreight),
      supportSuperVip: supportSuperVip === 'Y',
      payAmount: payAmount || 0,
      couponFreight,
      goodsTotalAmount: Number(goodsTotalAmount),
      originGoodsTotalAmount: Number(originGoodsTotalAmount),
      goodsAmountObj,
      storeInfo: store,
      discountAmount,
      newActDiscountAmount,
      totalBoxAmount: Number(totalBoxAmount),
      normalCouponMoney: couponValue,
      vipFree,
      vipPriceDiscount,
      totalCouponMoney,
      totalGoodsHeartMoney,
      canUseCouponGoodsMemberPrice,
      freightTip,
      existReparationActivity,
      topTips: (notice || {}).content,
      totalDiscount,
      freightDiscount,
      timelyGoodsTotalWeight,
      differenceAmountStart,
      baseFreight, // 基础配送费
      currentBaseFreight, // 满减后的基础配送费
      secondFreight, // 续重运费
      canUseCouponAmount,
      fansFree, // 果粉优惠
      // 这里重新判断一下及时达结算商品是否超重
      timelyGoodsIsOverWeight: timelyGoodsTotalWeight > timelyGoodsMaxWeight * 1000,
    });
    Object.assign(this._data, {
      timelyGoodsTotalPrice,
      uuid
    });
    this.data.lack && this.rechargeGuide();
    this.handleIntegralExchange({integralInfo, integralExchangeList})
    this.handleDiscountList()
  },
  /**
   * 设置优惠券信息
   * @param {*} couponInfo
   */
  async setGrayCoupon(couponInfo) {
    const {
      availableList = [],
      currentChoose = [],
      unavailableList = [],
      defaultBetter = [],
      rules,
    } = couponInfo || {};
    // 选择的优惠券是否均为心享专享优惠券
    const isBgxxVipCoupon = currentChoose.every(
      (item) => item.isOnlyVip === 'Y'
    );
    const defaultBetterMap = defaultBetter.map(item => item.couponCode)
    const currentChooseMap = currentChoose.map(item => item.couponCode)
    const isBetterSelectCoupon = defaultBetterMap.sort().join('') === currentChooseMap.sort().join('')

    this.setData({
      isBetterSelectCoupon: isBetterSelectCoupon && Boolean(currentChoose.length),
      isBgxxVipCoupon,
      couponList: availableList,
      unAvailableCouponList: unavailableList,
      selectedCouponList: currentChoose,
      defaultBetterCoupon: defaultBetter
    });
    this._data.couponRules = rules;
  },

  /**
   * 设置时间切割
   */
  setDeliveryTime({ pickerChange, deliveryTimeSplit }) {
    if (!deliveryTimeSplit || !deliveryTimeSplit.length) {
      if (this.data.inTimeGoodsList.length > 0) {
        this.setData({
          noDeliveryTimeInfo: '今日已截单，请明日再下单',
        });
      }
      return;
    }
    if (pickerChange) {
      this._data.deliveryTimeSplit = deliveryTimeSplit;
      const multiArray = [[], []];
      deliveryTimeSplit.forEach((item) => {
        const { dateDesc } = item;
        multiArray[0].push({
          dateDesc,
        });
      });
      multiArray[1] = this.splitTrans(
        (deliveryTimeSplit[0] || {}).splitList || []
      );
      // 取缓存位置的时间段，比较开始时间和结束时间以及时间段内的运费是否相等，相等则保留用户的选择，不相等则清空取最近的时间
      const userSelectInfo = this.data.deliveryWay === 'deliveryToDoor' ? wx.getStorageSync('deliveryToDoorSelectInfo') : wx.getStorageSync('selfTakeSelectInfo')
      // multiIndex [0,0] 上次用户选择的索引值
      // currSelectDeliveryInfo 时间项内容
      const { multiIndex: selectIndex = [], currSelectDeliveryInfo: selectDeliveryInfo = {} } = userSelectInfo || {}
      let newSelectDeliveryInfo = {}
      // selectIndex[1]表示时间段选择第几个（用户是否已选才有值） 且 选择了第一个可配送天
      if (selectIndex[1] && selectIndex[0] === 0) {
        //
        newSelectDeliveryInfo = multiArray[1][selectIndex[1]]
      } else if (selectIndex[1] >= 0 && selectIndex[0] === 1) {
        multiArray[1] = this.splitTrans(
          (deliveryTimeSplit[1] || {}).splitList || []
        )
        newSelectDeliveryInfo = multiArray[1][selectIndex[1]]
      }
      const { deliveryTimeBegin: oldTimeBegin, deliveryTimeEnd: oldTimeEnd, totalFreight: oldTotalFreight = 0 } = selectDeliveryInfo || {}
      const { deliveryTimeBegin: newTimeBegin, deliveryTimeEnd: newTimeEnd, totalFreight: newTotalFreight = 0 } = newSelectDeliveryInfo || {}
      const { totalFreight: curTotalFreight } = this.data
      const sameTime = oldTimeBegin && newTimeBegin && oldTimeBegin === newTimeBegin && oldTimeEnd && newTimeEnd && oldTimeEnd === newTimeEnd && oldTotalFreight === newTotalFreight && newTotalFreight === curTotalFreight
      if (sameTime) {
        this.setData({
          multiIndex: selectIndex,
          currSelectDeliveryInfo: newSelectDeliveryInfo
        })
      } else {
        // 如果出现不一致，就取当前时间
        multiArray[1] = this.splitTrans(
          (deliveryTimeSplit[0] || {}).splitList || []
        )
        this.setData({
          multiIndex: [0, 0],
          currSelectDeliveryInfo: multiArray[1][0]
        })
      }
      this.setData({
        noDeliveryTimeInfo: '',
        multiArray: this.textTransf(multiArray)
      })
    }
  },

  /**
   * 设置订单备注
   */
  inTimeOrderNoteChange() {
    wx.navigateTo({
      url:
        '/homeDelivery/pages/orderNote/index?paramObj=' +
        JSON.stringify({
          orderNote: this.data.inTimeOrderNote,
          noteType: 'inTimeOrderNote',
        }),
    });
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '130200008',
        element_name: '备注',
        element_id: '',
        element_content: '及时达订单备注',
        screen_code: '1302',
      });
    }
  },

  /**
   * @desc 弹层显示失效商品信息
   */
  showInvalidMessage(invaildMessage) {
    wx.showModal({
      content: invaildMessage,
      showCancel: false,
      confirmText: '我知道了',
      success: (res) => {
        if (res.confirm) {
          if (app.globalData.reportSensors) {
            app.sensors.track('MPClick', {
              element_code: '130201001',
              element_name: '我知道了',
              element_id: '',
              element_content: '库存不足提示',
              screen_code: '1302',
            });
          }
        }
      },
    });
  },
  /**
   * @desc 获取月卡信息，结算信息
   * @return {object} 优惠券信息
   */
  selectVipCouponPageGetVipCarInfo(){
    const {
      vipCardInfo = {},
      mouthCardSaveMoney = 0,
      defaultBetterCoupon = [],
      packageBestCouponBatchNum = ''
     } = this.data
    return {
      vipCardInfo,
      mouthCardSaveMoney,
      defaultBetterCoupon,
      packageBestCouponBatchNum
    }
  },
  /**
   * @desc 获取可用优惠券，不可用优惠券以及选中的优惠券，给选择优惠券页使用
   * @return {object} 优惠券信息
   */
  getCouponInfo() {
    const { couponList, unAvailableCouponList, selectedCouponList } = this.data;
    return {
      couponRules: this._data.couponRules,
      couponList,
      unAvailableCouponList,
      selectedCouponList: [...(selectedCouponList || [])],
    };
  },
  setGraySelectedCoupon(list) {
    const selectedCouponList = list || [];
    const preSelectedCouponList = this.data.selectedCouponList || [];
    const preSelectedCouponIds = preSelectedCouponList.map(
      (item) => item.couponCode
    ); // 上次选择的优惠券集合
    const curSelectedCouponIds = selectedCouponList.map(
      (item) => item.couponCode
    ); // 当前选择的优惠券
    const isChange = preSelectedCouponIds.sort().join() !== curSelectedCouponIds.sort().join()
    // 没有手动改变优惠券选择，则不用重新结算
    if (!isChange) {
      return
    }
    this._data.isFirstSettle = 'N'
    this.setData({
      selectedCouponList
    })
    this.orderSettle();
  },
  /**
   * @desc 设置选中的优惠券，给选择优惠券页使用，选择不使用优惠券则传null
   * @param {Array} list 选择的优惠券
   */
  setSelectedCoupon(list) {
    this.setGraySelectedCoupon(list)
  },
  /**
   * @desc selectVipCoupon页面用
   * @param {Array} list 选择的优惠券
   */
  setSelectOpenVipCoupon(list) {
    const selectedCouponList = list || [];
    this.setData({
      selectedCouponList
    })
    this._data.isSelectVipCouponSettle = true
    this.changeVipCardSelected({selected: true})
  },

  /**
   * @desc   设置选中的地址，给地址页使用
   * @param  {object} addressInfo 地址信息
   */
  setSelectedAddress(addressInfo, showModal = false) {
    Object.assign(addressInfo, {
      labelStyle: this.getLabelStyle(addressInfo.label),
    });
    //  切换地址后，也清除心享月卡勾选。好让正确结算
    this.resetMonthCardState()
    this.setData({
      'orderDeliveryAddressInfo.deliveryToDoor': addressInfo,
    });
    this.initCityInfo(addressInfo);
    this.getCurrLocToAddressDistance();
    Object.assign(this._data, {
      isRefreshOrder: true,
      isShowInvaildMessage: !showModal, // 地址列表切换地址如何有弹窗提示，则下单页面不在提示
    });
  },

  /**
   * 获取地址标签的样式值
   */
  getLabelStyle(label) {
    if (!label) return;
    const labelsObj =
      config.addressLabels.find((item) => item.labelName === label) || {};
    return `color: #${labelsObj.labelNameColor};border: 1rpx solid #${labelsObj.labelNameColor};`;
  },

  /**
   * 选择器选择事件触发的方法
   */
  bindMultiPickerColumnChange(e) {
    const { column = 0, value = 0 } = e.detail;
    const { multiArray } = this.data;
    if (!column) {
      // 如果改变的是第一列，需要更新第二列对应数据
      multiArray[1] = this.splitTrans(
        this._data.deliveryTimeSplit[value].splitList || []
      );
      this.setData({
        multiArray: this.textTransf(multiArray),
      });
    }
    this.reportDeliveryTime({
      type: 'click',
    });
  },

  /**
   * 选择器点击确认切换
   */
  bindMultiPickerChange(e) {
    const { multiArray, currSelectDeliveryInfo, deliveryWay } = this.data;
    const [firstIndex, secondIndex] = e.detail.value;
    const {
      deliveryTimeBegin: oldDeliveryTimeBegin,
      deliveryTimeEnd: oldDeliveryTimeEnd,
    } = currSelectDeliveryInfo;
    const { deliveryTimeBegin = '', deliveryTimeEnd = '' } =
      multiArray[1][secondIndex] || {};
    this.setData({
      currSelectDeliveryInfo: multiArray[1][secondIndex] || {},
    });
    // 配送上门切换配送时间，重新结算（运费重新计算）
    if (deliveryWay === 'deliveryToDoor') {
      this._data.isShowInvaildMessage = false;

      this._data.isFirstSettle = 'N'
      this.orderSettle({
        pickerChange: false
      });
    }
    this.reportDeliveryTime({
      type: 'switch',
      mp_from: `${oldDeliveryTimeBegin}-${oldDeliveryTimeEnd}`,
      mp_to: `${deliveryTimeBegin}-${deliveryTimeEnd}`,
    });
  },

  /**
   * 上报配送时间操作
   */
  reportDeliveryTime(params) {
    if (app.globalData.reportSensors) {
      const { mp_from, mp_to, type } = params;
      if (type === 'click') {
        app.sensors.track('MPClick', {
          element_code: '130200006',
          element_name: '配送时间',
          element_id: '',
          element_content: '配送时间',
          screen_code: '1302',
        });
      } else {
        app.sensors.track('MPSwitch', {
          mp_screen_name: '及时达确认订单页',
          mp_switchObject: '配送时间',
          mp_from,
          mp_to,
        });
      }
    }
  },
  closeMouthSave() {
    this.setData({
      showMouthSaveBox: false,
    });
  },

  /**
   * 重置随单开通心享状态
   */
  resetMonthCardState () {
    this.setData({
      vipCardInfo: {},
      vipCardIsSeleted: false,
    })
    Object.assign(this._data, {
      isSelectVipCouponSettle: false,
      applyCardSeleted: false,
      openVipCardType: '',
      cardActivityCode: '',
      giftCouponInfo: {},
      ruleLadderId: ''
    })

  },
  /**
   * @desc 选择开通心享月卡
   * */
  openMouthCardFromMouthSave(){
    const monthCardComp = this.selectComponent('#mouthcard');
    monthCardComp.changeVipCardSelected({
      selected: true,
      refresh: false,
    });
    const { virtualSettleData = {} } = this._data
    this.handelOrderSettleData({
      res: virtualSettleData,
      params:{
        optionOrigin: 'mouthCardSelected'
      }
    })
  },
  /**
   * @desc 暂不开通心享月卡
   * */
  cancelMouthCardFromMouthSave: util.throttle(function(){
    this.submitForm()
  },2000),
  // 提示开通月卡
  async submitTipOpenMouthCard() {
    let result = true;
    const {
      mouthCardSaveMoney = 0,
      payAmount: payAmountCurrent = 0,
      vipCardInfo:{
        list = [],
      } = {} } = this.data
    const {
      applyCardSeleted,
      ViewVipRegion,
      showVipCardTipsNums = 0
    } = this._data
    // 没有展示月卡区域 || 已选择月卡 || 月卡某块类型不是月卡是年卡，不进行后续校验 || 已经弹过一次
    const { type = '', money = 0 } = list[0] || {}
    const senceNotTrue = !ViewVipRegion || applyCardSeleted || list.length > 1 || type !== APPLY_CARD_TYPE_ENUM.MONTH_CARD || showVipCardTipsNums > 0
    if(senceNotTrue) return result
    // 当前订单开通省金额 ≥ 开卡金额
    if(mouthCardSaveMoney >= money){
      const { data: virtualSettleData = {} } = await this.orderVirtualSettle()
      const { payAmount: payAmountVirtual = 0 } = virtualSettleData
      // 开通月卡时机支付金额 ≥ 当前实际支付金额
      const openPopup = payAmountCurrent && payAmountVirtual && Number(payAmountVirtual) <= Number(payAmountCurrent)
      if(openPopup) {
        result = false
      }
    }
    // 展示开通月卡弹窗
    if(!result){
      this.setData({
        showMouthSaveBox:true,
      })
      this._data.showVipCardTipsNums++;
      sensors.track('Exposure', {
        'blockName': '一单回本弹窗',
        'blockCode': 130207
      })
      this._data.isReportSubmitClick = false
      this.reportClickConfirmOrder();
    }
    return result;
  },
  // 提交表单之前判断是否提示开卡
  submitFormBefore: util.throttle(async function(){
    if (!this._data.isCanSubmit) return;
    const { timelyGoodsIsOverWeight, timelyGoodsMaxWeight, deliveryWay } = this.data
    if (timelyGoodsIsOverWeight) {
      if (deliveryWay === 'selfTake') {
        sensors.exposureReport('1302_130210002')
        this.setData({
          showOverWeightPopup: true,
        })

        const closeType = await (() => {
          let resolve
          const promise = new Promise(r => resolve = r)
          this.overWeightCloseCallback = function(closeType) {
            resolve(closeType)
          }
          return promise
        })()

        //  关闭弹窗，拒绝
        if (closeType === 'close') {
          sensors.clickReport('1302_130210004')
          return
        }
        sensors.clickReport('1302_130210003')
      } else {
        sensors.exposureReport('1302_130210006')
        const modalResDelivery = await app.showModalPromise({
          title: '温馨提示',
          content: `本单商品重量已超过${timelyGoodsMaxWeight}kg，仅支持门店自提服务，如需配送上门，请返回购物车修改商品数量，分开下单~`,
          showCancel: true,
          confirmText: '回购物车',
          cancelText: '门店自提',
        })
        if (modalResDelivery) {
          wx.switchTab({
            url: '/pages/shopCart/index'
          })
          sensors.clickReport('1302_130210007')
        } else {
          this.setDeliveryWay('selfTake')
          sensors.clickReport('1302_130210008')
        }
        return
      }
    }

    //  没有充值活动时，才能判断是否提示开卡
    if (!this.data.rechargeData.length) {
      this._data.isReportSubmitClick = true
      const resultTip = await this.submitTipOpenMouthCard();
      if (!resultTip) return;
    }

    // 校验月卡是否选择 & 没有选择是否能一单回本
    this.submitForm()
  }, 2000),
  // 提交表单
  submitForm: util.throttle(async function () {
    if (!this._data.isCanSubmit) return;
    const status = this.checkOrderParamsInvalid();
    if (!status) {
      return;
    }
    this._data.isReportSubmitClick && this.reportClickConfirmOrder();
    const that = this;
    const confirmHandle = async () => {
      that._data.isCanSubmit = false;
      // 触发订阅弹窗
      this.setData({
        'subscribe.show': true,
        'subscribe.tmplIds': that.getSubscribeMessageTmplIds()
      })
      const promisewithResolve = getPromiseObj()
      this._data.confirmOrderPromise = promisewithResolve
      // 在这使用promise的形式进行等待,
      // 避免使用云闪付半屏弹窗时,
      // 由静默订阅消息唤起this.confirmOrder
      // 导致无法使用半屏弹窗
      await promisewithResolve.promise
      this.confirmOrder()
    };

    const {
      payAmount,
      isSelectVoucher,
      isSelectExchangeCard,
      selectPagodaPay,
      selectWxPay,
      selectUnionPay,
      isSelectRechargeAct,
    } = this.data;

    //  选择了充值活动
    if (isSelectRechargeAct) {
      confirmHandle();
      return
    }

    // 代金券0元支付
    if (Number(payAmount) === 0 && isSelectVoucher) {
      confirmHandle();
      return
    }
    // 使用了兑换卡
    if (Number(payAmount) === 0 && isSelectExchangeCard) {
      confirmHandle();
      return
    }

    //  仅钱包支付
    if (selectPagodaPay && !(selectWxPay || selectUnionPay)) {

      // 校验是否需要进入验证码环节
      const isNeedValidate = await that.checkPayDevice();

      if (!isNeedValidate) {
        // 缓存一下提交订单的请求
        that._data.tmpConfirmHandle = confirmHandle;
        // 弹出验证码输入框
        that.showSmsValidate()
        return;
      }
      commonObj.showModal(
        '提示',
        `确认使用会员钱包支付${Number((payAmount / 100).toFixed(2))}元吗？`,
        true,
        '确认',
        '取消',
        function (res) {
          if (res.confirm) {
            // 提交订单
            confirmHandle();
          }
        }
      );
    } else {
      confirmHandle();
    }
  }, 2000),
  /**
   * 订阅消息关闭事件
   */
  onSubscribeClose({ detail }) {
    // 关闭订阅弹窗
    this.setData({ 'subscribe.show': false })
    const { resolve } = this._data.confirmOrderPromise
    resolve && resolve()
    // 上报到触达域
    detail && reportTmplIds(detail.resultStatus)
  },

  /**
   * 获取订阅消息模版id
   */
  getSubscribeMessageTmplIds() {
    const { inTimeGoodsList } = this.data
    // v4.2增加，全国送的订阅消息，订阅消息分别为：发货通知 优惠券到期提醒
    if (!inTimeGoodsList || !inTimeGoodsList.length) {
      return b2cOrderSubmitTemplateIds
    }
    // v4.2改版，订阅消息分别为：订单进度提醒 差额退 优惠券到期提醒
    return timelyOrderSubmitTemplateIds
  },

  /**
   * 上报点击下单按钮
   */
  reportClickConfirmOrder() {
    if (app.globalData.reportSensors) {
      const {
        deliveryWay = 'deliveryToDoor',
        totalDiscount = 0,
        payAmount = 0,
        inTimeGoodsList = [],
        vipPriceDiscount = 0,
        totalCouponMoney = 0,
        totalGoodsHeartMoney = 0,
        canUseCouponGoodsMemberPrice = 0,
        b2cGoodsList = [],
        isBgxxVipCoupon = false,
        vipCardIsSeleted = false,
        blessCardSelected = false,
        mouthCardSaveMoney = 0,
        superVipStatus = 'C',
        isBetterSelectCoupon = false,
        isSelectVoucher = false,
        voucherCouponValue = 0,
        selectedCouponList = [],
        vipCardInfo:{
          show = false,
          list = [],
          monthCardLeftDay = '',
          yearCardLeftDay = ''
        } = {}
      } = this.data
      const { openVipCardType, payAmountCompareCurrentVirtual = '' } = this._data
      const { money = 0 , type = APPLY_CARD_TYPE_ENUM.YEAR_CARD } = list.length === 1 ? list[0] : {}
      const commodityNum = inTimeGoodsList.concat(b2cGoodsList).reduce((sum, cur) => {
          return sum + (cur.count || 0)
        }, 0)
      let OrderType = ''
      if(b2cGoodsList.length && !inTimeGoodsList.length){
        OrderType = '全国送'
      }else if(b2cGoodsList.length && inTimeGoodsList.length){
        OrderType = '及时达到家'
      }else{
        OrderType = deliveryWay === 'deliveryToDoor' ? '及时达到家':'及时达自提'
      }
      const isVip = ['T','F'].includes(superVipStatus)
      const saveMoneyAboveXinxiang = show && mouthCardSaveMoney > money && type === APPLY_CARD_TYPE_ENUM.MONTH_CARD
      // 订单心享节省金额
      let orderXinxiangSaveValue = 0
      // 可用券金额
      let useCouponOrderAmount = canUseCouponGoodsMemberPrice
      if(isVip || vipCardIsSeleted){
        useCouponOrderAmount = totalGoodsHeartMoney
        orderXinxiangSaveValue += util.formatPrice(vipPriceDiscount)
        if(isBgxxVipCoupon){
          orderXinxiangSaveValue += util.formatPrice(totalCouponMoney)
        }
      }
      let openXinxiangType_ConfirmOrder = '无'
      // 是否开通心享-开通类型
      if(vipCardIsSeleted){
        openXinxiangType_ConfirmOrder =  YearCardType.includes(openVipCardType) ? '年卡':'月卡'
      }
      const orderCouponNameList = [],orderCouponNumberList = []
      selectedCouponList.forEach(item =>{
        item.couponName && orderCouponNameList.push(item.couponName)
        item.batchNum && orderCouponNumberList.push(item.batchNum)
      })
      app.sensors.track('MPClick', {
        element_code: '130200009',
        element_name: '立即支付',
        element_id: '',
        element_content: '立即支付',
        screen_name:'及时达确认订单页',
        screen_code: '1302',
        scene_number: app.globalData.scene,
        screen_type:'及时达',
        OrderType,
        commodityNum,
        orderAmount:util.formatPrice(payAmount) + util.formatPrice(totalDiscount),
        useCouponOrderAmount:util.formatPrice(useCouponOrderAmount)||0,
        actualPaymentAmount:util.formatPrice(payAmount)||'',
        useOrdinaryCoupon:totalCouponMoney > 0 ? 1 : 0,
        xinXiangCoupon:isBgxxVipCoupon ? 1 : 0,
        coupondiscountamount:util.formatPrice(totalCouponMoney)||0,
        saveMoneyAboveXinxiang:saveMoneyAboveXinxiang ? 1 : 0,
        orderXinxiangSaveValue,
        is_gift_card:blessCardSelected ? 1 : 0,
        is_Optimalcoupon: isBetterSelectCoupon ? 1 : 0, //优惠券是否存在本单最优
        openXinxiangType_ConfirmOrder,
        isUse_CashCoupon: isSelectVoucher ? 1 : 0, //是否使用代金券
        cashCouponValue: util.formatPrice(voucherCouponValue),
        orderXinxiangGoodsSaveValue: util.formatPrice(vipPriceDiscount),
        is_open_xinxiang: openVipCardType === APPLY_CARD_TYPE_ENUM.MONTH_CARD ? 1 : 0,
        monthCardExpirationTime: monthCardLeftDay ,//月卡过期时间
        yearCardExpirationTime: yearCardLeftDay ,//年卡过期时间
        orderCouponName: orderCouponNameList.join(','),
        orderCouponNumber: orderCouponNumberList.join(','),
        openXinxiangAmountsaved: payAmountCompareCurrentVirtual !== '' ? util.formatPrice(payAmountCompareCurrentVirtual) : ''
      });
      app.event.emit('refreshNewUserInfo')
    }
  },
  /**
   * 确认下单
   */
  async confirmOrder() {
    try {
      const params = this.getConfirmOrderParams();
      wx.showLoading({
        title: '支付中...',
        mask: true,
      });
      // 下单退出登录，需要回到首页
      const { isFruitFansGray } = this.data
      const res = await app.api[isFruitFansGray ? 'submitOrderGrayScale' : 'submitOrder'](params, true, () => {
        this._data.isCanSubmit = true;
      })
      const result = res.data || {};
      this.payRequest(result);
      this.txSumitOrder(result);
      fruitCartStore.getCartList()
      // 提交订单成功后才清空祝福卡信息
      if (this.data.deliveryWay === 'deliveryToDoor') {
        setTimeout(() => {
          clearBlessCardInfo();
        }, 500);
      }
      recentBuy.collectRecentFruitSku({
        skuList: (this.data.inTimeGoodsList || []).filter(item => !isCombinationGoods(item.goodsName || '')).map(item => item.goodsSn),
        time: res.systemTime
      });
      this.submitOrderSuccessReport(result)
    } catch (error) {
      wx.hideLoading();
      this._data.isCanSubmit = true;
      if (error.statusCode === 429) {
        wx.showModal({
          content: '活动太火爆了，请稍后重试！',
          showCancel: false,
          confirmText: '我知道了',
          success: (res) => {
            if (res.confirm) {
              wx.navigateBack();
            }
          },
        });
        return;
      }
      this.handleConfirmOrderError(error);
    }
  },

  /**
   * 处理下单异常
   * @param {*} error
   */
  async handleConfirmOrderError(error) {
    console.log('handleConfirmOrderError', error);
    app.monitor.report({
      label: 'fruit.confirmOrder',
      errMsg: error
    })
    let { errorCode, description } = error || {};
    errorCode = Number(errorCode);
    if (errorCode === 35406) {
      // 35406:下单时出现了配送方式不能购买的商品（配送上门：含有自提专享，门店自提：含有配送专享），此时直接重新结算，结算提示切换配送方式
      this._data.isShowDeliveryWayNoMatch = true;
    } else if ([80001, 80003, 450209].includes(errorCode)) {
      await app.showModalPromise({
        content:
          errorCode === 80001
            ? '其他订单已开通心享会员，不可重复开通;若未开通成功请检查订单是否完成支付'
            : description,
      });
      this.cancelApplyCard({
        status: false,
        refresh: false,
      });
    } else if ([3006054, 450006].includes(errorCode)) {
      /**
       * 3006054
       * 有新人特价但不是新用户
       * 450006
       * 商品价格发生变化，请确认
       */
      await app.showModalPromise({
        content: description
      })
      const { settlementGoodsList, inTimeGoodsList } = this.data
      if (errorCode === 3006054) {
        const list = removeGoodsNewAct({ goodsList: settlementGoodsList })
        this.setData({
          settlementGoodsList: list
        })
      } 
      // 下单时不知道哪个特价活动失效，inTimeGoodsList还是上次结算的数据，不需要重新匹配，直接取身份价
      // else if (errorCode === 450006) {
      //   const list = reMatchNewUserGoodsList({
      //     settlementGoodsList,
      //     inTimeGoodsList
      //   })
      //   this.setData({
      //     settlementGoodsList: list
      //   })
      // }
    } else {
      this._data.isShowDeliveryWayNoMatch = false;
      await app.showModalPromise({
        content: description
      })
      this.clearNoUseStoreAddress(error);
      // 积分不足时，取消用户的选择
      if (errorCode === 450003) {
        this.initIntegralExchange()
      }
    }
    this._data.isShowInvaildMessage = false;
    this.orderSettle();
  },

  /**
   * 清空不能使用的门店地址
   */
  clearNoUseStoreAddress(error) {
    const { errorCode = '' } = error || {};
    if ([35002, 35402, 35403, 35407].includes(errorCode)) {
      // 门店不能使用
      const { deliveryWay } = this.data;
      const key = `orderDeliveryAddressInfo.${deliveryWay}`;
      this.setData({
        [key]: null,
      });
    }
  },

  /**
   * 组装下单需要的参数
   */
  getConfirmOrderParams() {
    let {
      payAmount,
      deliveryWay,
      totalFreight,
      orderDeliveryAddressInfo,
      inTimeGoodsList,
      b2cGoodsList,
      inTimeOrderNote,
      packAmount,
      wholeFreight,
      currSelectDeliveryInfo,
      selectedCouponList,
      orderAmount,
      superVipStatus,
      couponFreight,
      blessCardSelected,
      blessCardAmount,
      blessCardContent,
      blessCardReceiver,
      blessCardSender,
      timelyGoodsTotalWeight,
      baseFreight,
      currentBaseFreight,
      secondFreight,
      freightTip,
      currentChooseExchangeCard,
      integralSelected = false,
      integralExchangeInfo: { activityCode = '', integral = 0, integralAmount = 0 } = {},
      vipCardIsSeleted,
      isFruitFansGray,
      fruitFans
    } = this.data;
    const {
      uuid,
      cityInfo,
      timelyGoodsTotalPrice,
      currentChooseVoucher,
    } = this._data;
    const { cityID, cityCode, storeCode, deliveryCenterCode, storeID } =
      (deliveryWay === 'deliveryToDoor'
        ? cityInfo
        : orderDeliveryAddressInfo.selfTake) || {};
    const params = {
      timelyGoodsTotalPrice,
      payAmount,
      orderAmount,
      customerID: app.globalData.customerID || -1,
      storeCode,
      storeID,
      cityCode,
      cityID,
      deliveryCenterCode,
      deliveryWay: 1,
      uuid,
      costList: [],
      couponList: [],
      orderInfoList: [],
      parentDistributor: getTimelyDistributor(),
    };
    if (deliveryWay === 'deliveryToDoor') {
      // 配送上门
      Object.assign(params, {
        deliveryWay: 2,
        receiveAddrID: orderDeliveryAddressInfo.deliveryToDoor.addressId,
        timelyGoodsTotalWeight,
      });
      // 配送上门才有贺卡
      if (blessCardSelected) {
        Object.assign(params, {
          blessCard: {
            blessCardAmount,
            blessCardSender,
            blessCardContent,
            blessCardReceiver,
          },
        });
      }
    }
    if ((Number(baseFreight) || Number(secondFreight) > 0) && deliveryWay === 'deliveryToDoor') {
      const costListItem = {
        costCode: 'D',
        costAmount: Number(currentBaseFreight),
        standardAmount: Number(baseFreight),
        secondFreight: Number(secondFreight)
      }
      // 添加运费
      params.costList.push(costListItem);
    }
    if (!!selectedCouponList && !!selectedCouponList.length) {
      // 优惠券
      selectedCouponList.forEach((item) => {
        const { couponMoney, couponCode, couponWay } = item;
        const couponInfo = {
          discountAmount: couponMoney,
          couponId: couponCode
        };
        couponInfo.type = 1
        if (couponFreight && Number(couponWay) === 5) {
          // 非免运券不传，免运券需传订单类型，当前只有及时达，传10
          Object.assign(couponInfo, { orderType: 10 });
        }
        params.couponList.push(couponInfo);
      });
    }
    // 代金券
    if (currentChooseVoucher.length) {
      currentChooseVoucher.forEach( item => {
        const couponInfo = {
          discountAmount: item.couponMoney,
          couponId: item.couponCode,
          type: 2
        };
        params.couponList.push(couponInfo);
      })
    }
    // 兑换卡
    if (currentChooseExchangeCard.length) {
      currentChooseExchangeCard.forEach( item => {
        const couponInfo = {
          discountAmount: item.couponMoney,
          couponId: item.couponCode,
          assetBuyAmount: item.buyValue,
          type: 3
        };
        params.couponList.push(couponInfo);
      })
    }
    // 积分兑换信息
    if (integralSelected && activityCode) {
      params.integralExchange = [
        {
          activityCode,
          integral,
          integralAmount
        }
      ]
    }
    const goodsListMap = (list) => {
      return list.map((item) => {
        let {
          takeawayAttr,
          goodsSn,
          count,
          selectSpecsServiceList,
          activityQuantity,
          exceedActivityNum,
          heartPrice,
          memberPrice,
          goodsId,
          couponCode,
          isNewAct
        } = item;
        const { activityCode, activityPrice } = pickActGoodsFields({ goods: item, isNewUser: Boolean(isNewAct) })
        const exchangeCardOfGoods = currentChooseExchangeCard.find(val => val.couponCode === couponCode) || {}
        const params = {
          takeawayAttr,
          goodsSn,
          goodsId,
          quantity: count,
          serviceFeeList: selectSpecsServiceList.map((service) => {
            return Object.assign(
              {
                quantity: count,
                serviceSn: service.serviceSn,
              },{ salesPrice: service.schemePrice }
            );
          }),
          assetInfo: {
            assetTypeId: exchangeCardOfGoods.defineId,
            assetCode: exchangeCardOfGoods.couponCode,
            assetUseAmount: exchangeCardOfGoods.couponValue,
            assetBuyAmount: exchangeCardOfGoods.buyValue
          }
        };
        // 查看商品是否参与特价活动（会员/心享价 < 特价 不参与），用了兑换卡，不再享受特价
        const isVip = ['T', 'F'].indexOf(superVipStatus) > -1 || vipCardIsSeleted
        const {
          /**当前用户身份的心享价 */
          identityHeartPrice,
          /**当前用户身份的心享前购买价格 */
          purchasePrice
        } = getVipPrice(item, fruitFans, isVip)
        // 及时达商品才走果粉价逻辑
        isFruitFansGray = isFruitFansGray && takeawayAttr === '及时达'
        heartPrice = isFruitFansGray ? identityHeartPrice : heartPrice
        memberPrice = isFruitFansGray ? purchasePrice : memberPrice
        const currPrice = isVip ? heartPrice || memberPrice : memberPrice;
        if (activityCode && currPrice > activityPrice && !exchangeCardOfGoods.couponCode) {
          Object.assign(params, {
            activityQuantity: exceedActivityNum ? exceedActivityNum : activityQuantity,
            activityCode,
            isNewAct
          });
        }
        return params;
      });
    };
    if (!!inTimeGoodsList.length) {
      const { deliveryTimeBegin, deliveryTimeEnd } = currSelectDeliveryInfo;
      const timelyObj = {
        deliveryTimeBegin,
        deliveryTimeEnd,
        orderNote: inTimeOrderNote,
        orderTakeawayAttr: '及时达',
        dispatchLevel: deliveryWay === 'deliveryToDoor' ? '12' : 0, // 配送上门59分钟达：12 自提：0
        goodsList: goodsListMap(inTimeGoodsList),
      };
      params.orderInfoList.push(timelyObj);
    }
    if (!!b2cGoodsList.length) {
      const timelyObj = {
        orderTakeawayAttr: 'B2C',
        dispatchLevel: '0',
        goodsList: goodsListMap(b2cGoodsList),
      };
      params.orderInfoList.push(timelyObj);
    }

    // 3.5.1 接入腾讯有数卖货佣金需求，新增字段  存储有效期15天
    const txCpsId = app.getCacheQueryParams('txCps', 'txCpsId', 1296000000);
    txCpsId && (params.txCpsId = txCpsId);
    // 4.0 增加分销渠道 24h有效期
    const distributionChannel = app.getCacheQueryParams('utmSource', 'channel',86400000 );
    if (distributionChannel) {
      params.distributionChannel = distributionChannel
    }
    // 开卡相关
    const applyCardInfo = this.genApplyCardInfo({isSubmit: true});
    Object.assign(params, applyCardInfo);

    //  选择随单充活动时，增加入参
    if (this.data.isSelectRechargeAct) {
      const {
        activityCode,
        sequence,
        lowerLimit,
        giftAmount,
        gifts,
      } = this.data.rechargeAct;

      const {
        giveawayType,
        code,
        quantity,
        type,
      } = gifts[0]

      params.recharge = {
        activityCode: activityCode,
        sequence: sequence,
        lowerLimit: lowerLimit,
        giftAmount: giftAmount,
        gifts: {
          giveawayType: giveawayType,
          code: code,
          quantity: quantity,
          type: type,
        },
      }
    }

    /**
     * 订单交易合并，对入参做兼容处理，灰度用户走新接口，非灰度走老接口
     */
    // if (isGrayScale) {
      // 灰度用户
      params.packAmount = packAmount;
      return params;
    // }
    // 非灰度用户
    // 添加包装费
    // if (!!Number(packAmount)) {
    //   params.costList.push({
    //     costCode: 'P',
    //     costAmount: packAmount,
    //     standardAmount: packAmount,
    //   });
    // }
    // return params;
  },
  async getSubOrderList(subOrderNos) {
    try {
      const orderNos = subOrderNos
      const result = await app.api.getSubOrderList({
        orderNos,
      });
      return result.data.map( item => {
        return {
          orderNo: item.orderNo,
          totalPrice: item.totalPrice,
          actualAmount: item.actualAmount,
          createTime: item.createTime
        }
      })
    } catch(err) {
      return []
    }
  },
  /**
   * @description 上报子单埋点
   * @param {*} param
   */
  async reportTxSubOrder({ subOrderNos, type }) {
    let subOrderList = this._data
    if (!subOrderList.length) {
      subOrderList = await this.getSubOrderList(subOrderNos)
    }
    subOrderList.forEach(item => {

    const param = {
      type,
      orderNumber: item.orderNo || '',
      amount: item.totalPrice,
      payAmount: item.actualAmount,
      createTime: item.createTime || '',
    };
    if (type === 1) {
      param.cancelPayTime = Date.now()
    } else if (type === 2) {
      param.payTime = Date.now()
    }
    console.log(param)
    txSensor.txOrderSensor(param);
    })
  },
  /**
   * 智慧零售提交订单
   */
  txSumitOrder(data) {
    try {
      const { payAmount, mainOrderNo, createTime, subOrderNos = [] } = data;
      if (subOrderNos.length > 1 || (subOrderNos.length === 1 && subOrderNos[0] !== mainOrderNo)) {
        this.reportTxSubOrder({ subOrderNos, type: 0 })
        return
      }
      const param = {
        type: 0,
        orderNumber: mainOrderNo || '',
        amount: this.data.orderAmount,
        payAmount,
        createTime: createTime || Date.now(),
      };
      txSensor.txOrderSensor(param);
    } catch(err) {}
  },

  /**
   * 检查当前下单参数是否正常
   */
  checkOrderParamsInvalid() {
    const {
      paraGoodsList,
      currSelectDeliveryInfo,
      inTimeGoodsList,
      orderDeliveryAddressInfo,
      deliveryWay,
      blessCardSelected,
      blessCardContent,
    } = this.data;
    // 有及时达商品，需要校验配送时间
    if (
      inTimeGoodsList.length > 0 &&
      !currSelectDeliveryInfo.deliveryTimeBegin
    ) {
      wx.showToast({
        title: `请选择${deliveryWay === 'selfTake' ? '自提' : '配送'}时间`,
        icon: 'none',
      });
      return false;
    }
    if (!orderDeliveryAddressInfo[deliveryWay]) {
      wx.showToast({
        title: `请选择${deliveryWay === 'selfTake' ? '自提门店' : '收货地址'}`,
        icon: 'none',
        duration: 2000,
      });
      return false;
    }
    if (!paraGoodsList.length) {
      wx.showToast({
        title: '无有效商品，请重新选择',
        icon: 'none',
        duration: 2000,
      });
      return false;
    }
    if (
      deliveryWay === 'deliveryToDoor' &&
      blessCardSelected &&
      !blessCardContent
    ) {
      wx.showToast({
        title: '祝福卡还没填写哦~',
        icon: 'none',
        duration: 2000,
      });
      return false;
    }
    return true;
  },

  /**
   * 支付请求
   */
  async payRequest(orderData) {
    // 神策防黑产埋点
    sensors.safeGuardSensor('pay', {
      orderNo: orderData.mainOrderNo,
      orderType: 'FRUIT',
    });
    const {
      selectPagodaPay,
      selectWxPay,
      selectUnionPay,
      mainBalance,
      isSelectVoucher,
      isSelectExchangeCard,
      isSelectRechargeAct,
      rechargePayInfo,
    } = this.data;
    const {
      payAmount,
      tradeNo,
      mainOrderNo,
    } = orderData;

    const payInfo = {
      usePagodaPay: selectPagodaPay,
      useWxPay: selectWxPay,
      useUnionPay: selectUnionPay,
      paymentAmount: payAmount,
      tradeNo,
      mainOrderNo,
      mainBalance,
      isSelectVoucher,
      isSelectExchangeCard,
    };

    //  选择随单充活动时，增加入参
    if (isSelectRechargeAct) {
      payInfo.rechargePayInfo = rechargePayInfo;
    }
    const succCb = this.paySuccessCallback.bind(this, orderData);
    const failCb = this.payFailCallback.bind(this, orderData);
    const extraInfo = {
      succCb,
      failCb
    };
    fruitOrderPay.handlePay(payInfo, extraInfo);
  },

  payFailCallback(orderData, failReason) {
    app.remarkNewUserOrderNo(orderData.mainOrderNo)
    console.log('failReason', orderData, failReason);
    const { payType, err, payMessage } = failReason;
    // 存在支付失败提示文案
    if (payMessage) {
      wx.showModal({
        content: payMessage,
        showCancel: false,
        complete: (res) => {
          this.openOrderDetail(orderData);
        }
      })
      return
    }
    if (fruitOrderPay.isThirdPay(payType)) {
      console.log('支付失败回调');
      this.openOrderDetail(orderData);
      this.reportTxCanalePay(orderData);
      log.error('useWXPayFailed', err);
    } else if (fruitOrderPay.isPagodaPay(payType)) {
      wx.showToast({
        title: '支付失败，请重试',
        duration: 1500,
        icon: 'none',
      });
      setTimeout(() => {
        this.openOrderDetail(orderData);
      }, 1500);
    }
  },

  /**
   * 上报智慧零售取消支付
   */
  reportTxCanalePay(orderData) {
    const { mainOrderNo, payAmount, createTime, subOrderNos = [] } = orderData || {};
    if (subOrderNos.length > 1|| (subOrderNos.length === 1 && subOrderNos[0] !== mainOrderNo)) {
      this.reportTxSubOrder({ subOrderNos, type: 1 })
      return
    }
    const param = {
      type: 1,
      orderNumber: mainOrderNo || '',
      amount: this.data.orderAmount,
      payAmount,
      cancelPayTime: Date.now(),
      createTime,
    };
    txSensor.txOrderSensor(param);
  },

  paySuccessCallback(orderData, payData) {
    app.remarkNewUserOrderNo(orderData.mainOrderNo)
    const { payType, data } = payData;
    // 代金券0元支付直接进支付成功页
    if(payType === 'zeroPay') {
      const { mainOrderNo, subOrderNos } = orderData
      const isMix = subOrderNos.length > 1 || (subOrderNos.length === 1 && subOrderNos[0] !== mainOrderNo)
      const { inTimeGoodsList, storeInfo: { storeCode, name: storeName } , deliveryWay } = this.data
      wx.redirectTo({
        url:
          `/homeDelivery/pages/paySuccess/index?paySuccessObj=${orderData.mainOrderNo}&subOrderNo=${orderData.mainOrderNo}&storeCode=${storeCode}&storeName=${storeName}&isMix=${isMix}&selectMonthCard=${this._data.applyCardSeleted}&isTimelyOrder=${inTimeGoodsList.length}&deliveryWay=${deliveryWay}&timelyGoodsIsOverWeight=${this.data.timelyGoodsIsOverWeight ? 'Y' : 'N'}`
      });
      return
    }
    this.openWaitPay(orderData, data);

    this.reportPay({
      data: orderData,
    });
  },

  /**
   * 跳转支付结果查询页
   */
  openWaitPay(orderData, payData) {
    // console.log('payData', payData);
    const { mainOrderNo, subOrderNos } = orderData;
    const { payNo, payNos } = payData;
    const { b2cGoodsList, inTimeGoodsList, storeInfo, deliveryWay } = this.data;
    const { phone: storePhone, name: storeName } = storeInfo;
    const { storeCode } = this.getConfirmOrderParams(); // 传入下单的门店code
    const orderDetailObj = {
      customerID: app.globalData.customerID || -1,
      paymentOrderID: mainOrderNo,
      subOrderNo: Array.isArray(subOrderNos) ? subOrderNos[0] : '',
      payNo,
      payNos,
      orderType: 'timelyOrder',
      isTimelyOrder: inTimeGoodsList.length,
      deliveryWay,
      needShowCode: !(b2cGoodsList.length && !inTimeGoodsList.length), // 如果全国送列表存在，及时达列表不在 就不用请求接口了
      storeCode,
      storeName,
      storePhone,
      selectMonthCard: this._data.applyCardSeleted,
      // 混合单：及时达+全国送，及时达+月卡，全国送+全国送
      // 及时达+月卡时，提交订单响应subOrderNos只会返回及时达子单
      isMix: this.data.isSelectRechargeAct ? false : subOrderNos.length > 1 || (subOrderNos.length === 1 && subOrderNos[0] !== mainOrderNo),
      timelyGoodsIsOverWeight: this.data.timelyGoodsIsOverWeight ? 'Y' : 'N',
      isRecharge: this.data.isSelectRechargeAct ? 1 : 0,
    };
    wx.redirectTo({
      //支付成功跳
      url:
        '/userA/pages/waitPay/index?orderDetailObj=' +
        JSON.stringify(orderDetailObj),
    });
  },

  /**
   * 跳转订单详情
   */
  openOrderDetail(orderData) {
    const { mainOrderNo = '', subOrderNos } = orderData;
    const orderNo = Array.isArray(subOrderNos) ? subOrderNos[0] : mainOrderNo;
    const myAllOrderObj = {
      goodsOrderID: orderNo,
    };
    wx.redirectTo({
      url:
        '/userB/pages/allOrderDetail/index?myAllOrderObj=' +
        JSON.stringify(myAllOrderObj),
    });
  },

  /**
   * 支付上报
   */
  reportPay(params) {
    const { data } = params;
    const { mainOrderNo, payAmount, createTime, orderAmount, subOrderNos } = data || {};
    // 混合单
    if (subOrderNos.length > 1 || (subOrderNos.length === 1 && subOrderNos[0] !== mainOrderNo)) {
      this.reportTxSubOrder({ subOrderNos, type: 2 })
      return
    }
    const txPayParams = {
      orderNumber: mainOrderNo,
      totalFee: parseFloat(payAmount || 0),
      createTime,
      orderAmount,
    };
    // 智慧零售支付埋点
    this.reportTXPay(txPayParams);
  },

  /**
   * 上报智慧零售支付成功
   */
  reportTXPay(obj) {
    const { orderNumber, totalFee, createTime } = obj;

    const param = {
      type: 2,
      orderNumber: orderNumber,
      amount: this.data.orderAmount,
      payAmount: totalFee,
      payTime: Date.now(),
      createTime: createTime || Date.now(),
    };
    txSensor.txOrderSensor(param);
  },

  /**
   * 跳转选择收获地址
   */
  openAddressList() {
    const {
      orderDeliveryAddressInfo: { deliveryToDoor },
      storeInfo,
    } = this.data;
    let url = `/bgxxUser/pages/address/addressList/index?from=homeConfirmOrder&settleGoodsType=${this._data.settleGoodsType}`;
    if (deliveryToDoor) {
      const { id = '' } = storeInfo || {};
      url += `&addressId=${deliveryToDoor.addressId}&storeID=${id}`;
    } else {
      const {
        cityInfo: { storeID = '' },
      } = this._data;
      url += `&storeID=${storeID}`;
    }
    wx.navigateTo({
      url,
    });
    this.addrsensors(deliveryToDoor);
  },

  /**
   * 神策上报选择地址
   */
  addrsensors(selectedAddressInfo) {
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: selectedAddressInfo ? 130200002 : 130200003,
        element_name: selectedAddressInfo ? '配送地址栏' : '选择收货地址',
        element_id: '',
        element_content: selectedAddressInfo ? '配送地址栏' : '选择收货地址',
        screen_code: '1302',
      });
    }
  },

  /**
   * 跳转选择优惠券
   */
  toSelectedCoupon() {
    const {
      couponList = [],
      superVipStatus = '',
      showSelectVipCoupon = '',
      vipCardIsSeleted = false
    } = this.data
    const url = !couponList.length && superVipStatus === 'C' && showSelectVipCoupon && !vipCardIsSeleted
     ? '/homeDelivery/pages/selectVipCoupon/index' : '/userA/pages/selectCoupon/index'
    wx.navigateTo({
      url,
    });
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: 130200005,
        element_name: '优惠券',
        element_id: '',
        element_content: '优惠券',
        screen_code: '1302',
      });
    }
  },
  /**
   * 获取下单充值引导活动信息
   */
  rechargeGuide() {
    const { storeID } = this._data.cityInfo;
    const param = {
      storeID,
      amount: this.data.payAmount,
      customerID: app.globalData.customerID || -1,
    };
    app.api
      .rechargeGuide(param)
      .then((res) => {
        const rechargeText = (res.data || {}).depositText || '';
        this.setData({ rechargeText });
      })
      .catch((res) => {
        console.error('res', res);
      });
  },

  showGoodsList(e) {
    const goodListName = e.currentTarget.dataset.goodsList;
    this.setData({
      isShowPopup: true,
      goodList: this.data[goodListName],
    });
  },

  /**
   * 打开餐盒费明细
   */
  showOrHidePackDetail() {
    this.setData({
      showPackDetail: true,
    });
  },

  // 关闭餐盒费明细
  closePackListPopu() {
    this.setData({
      showPackDetail: false,
    });
  },

  /**
   * 打开包装费说明
   */
  showPackingFeePopup() {
    this.setData({
      showPackingFee: true,
    });
  },

  /**
   * 关闭包装费说明
   */
  closePackingFeePopup() {
    this.setData({
      showPackingFee: false,
    });
  },

  /**
   * 关闭运费说明弹窗
   */
  closeFreightTipPopup() {
    this.setData({
      showFreightTip: false,
    });
  },

  /**
   * 打开运费说明弹窗
   */
  showFreightTipPopup() {
    this.setData({
      showFreightTip: true,
    });
  },

  /**
   * 切换配送方式
   */
  changeDeliveryWay(e) {
    const { status } = e.currentTarget.dataset || {};
    const { timelyGoodsIsOverWeight, timelyGoodsMaxWeight } = this.data
    if (timelyGoodsIsOverWeight && status === 'deliveryToDoor') {
      this.setData({
        showOverWeightPopup: true,
      })
      sensors.exposureReport('1302_130210005')
      return
    }
    this.setDeliveryWay(status);
    setTimeout(() => {
      this.getAddHeight();
    }, 500);
    if (status === 'deliveryToDoor') {
      sensors.track('Click', '1302_130200013')
    } else {
      sensors.track('Click', '1302_130200014')
    }
  },

  /**
   * 关闭超重提示弹窗
   */
  onOverWeightClose(event) {
    this.setData({
      showOverWeightPopup: false,
    })

    if (typeof this.overWeightCloseCallback === 'function') {
      this.overWeightCloseCallback(event.detail)
      delete this.overWeightCloseCallback
    }
  },

  /**
   * 联系门店
   */
  callStore() {
    wx.makePhoneCall({
      phoneNumber: this.data.storeInfo.phone
    })
  },

  /**
   * 设置配送方式
   */
  setDeliveryWay(status) {
    this.setData({
      deliveryWay: status,
    });
    //  切换配送/自提时，清空随单心享选择情况
    this.resetMonthCardState()
    this.initIntegralExchange()
    this.changeOrderSettle();
    this.changeSeletTimePopupName(status);
  },

  /**
   * 切换配送方式结算
   */
  changeOrderSettle: util.debounce(
    function () {
      // 切换自提、配送方式之后，会重新匹配最优代金券、优惠券
      this._data.isFirstSettle = 'Y'
      this._data.isNeedMatchExchangeCard = 'Y'
      this.orderSettle();
    },
    400,
    true
  ),

  /**
   * 切换门店地址信息
   */
  choiceAddressInfo() {
    const { deliveryWay } = this.data;
    switch (deliveryWay) {
      case 'selfTake':
        this.openStoreList();
        break;
      case 'deliveryToDoor':
        this.openAddressList();
        break;
      default:
    }
  },
  /**
   * 切换选择门店
   */
  openStoreList() {
    let url =
      '/fightGroups/pages/selfExtractStore/index?formPage=selfTakeConfirmOrder';
    const {
      orderDeliveryAddressInfo: {
        selfTake
      } = {}
    } = this.data;
    if (selfTake) {
      url += `&storeInfo=${JSON.stringify(selfTake)}`;
    }
    wx.navigateTo({
      url,
    });
  },
  /**
   * @desc 设置自提门店数据，提供给选择提货门店使用
   * @param  {object} storeInfo 门店信息
   */
  setTakeStoreInfo(storeInfo) {
    //  切换门店后，也清除心享月卡勾选。好让正确结算
    this.resetMonthCardState()
    this.setData({
      'orderDeliveryAddressInfo.selfTake': storeInfo,
    });
    Object.assign(this._data, {
      isRefreshOrder: true,
    });
  },
  /**
   * 时间选择
   * @param {*} e
   */
  pick(e) {
    const {
      detail: { current },
    } = e;
    const { deliveryWay, totalFreight } = this.data;
    const { totalFreight: selectTotalFreight = 0 } =
      this.data.multiArray[1][current[1]] || {};
    this.setData({
      currSelectDeliveryInfo: this.data.multiArray[1][current[1]],
      multiIndex: current,
      visible: false,
    });
    // 把用户选择的时间段存起来
    const selectInfo = {
      multiIndex: current,
      currSelectDeliveryInfo: this.data.multiArray[1][current[1]]
    }
    const selectInfoItem = deliveryWay === 'deliveryToDoor' ? 'deliveryToDoorSelectInfo' : 'selfTakeSelectInfo'
    wx.setStorageSync(selectInfoItem, selectInfo)
    // 配送上门切换配送时间运费发生变化，重新结算（运费重新计算）
    if (
      deliveryWay === 'deliveryToDoor' &&
      Number(selectTotalFreight) !== Number(totalFreight)
    ) {
      this._data.isFirstSettle = 'N'
      this.orderSettle({
        pickerChange: false
      });
    }
  },
  /**
   * 日期选择
   * @param {*} e
   */
  changeDate(e) {
    const {
      detail: { current },
    } = e;
    const { multiArray } = this.data;
    // if (!column) {
    // 如果改变的是日期，需要更新第二列对应数据
    multiArray[1] = this.splitTrans(
      this._data.deliveryTimeSplit[current[0]].splitList || []
    );
    this.setData({
      multiArray: this.textTransf(multiArray),
    });
    // }
    this.reportDeliveryTime({
      type: 'click',
    });
  },
  openTimePickerHandle() {
    if (this.data.noDeliveryTimeInfo) return;
    this.setData({
      visible: true,
    });
  },
  /**
   * 字符切分
   * @param {*} ary
   */
  splitTrans(ary) {
    return splitTrans(ary)
  },
  /**
   *  标点符号替换
   * @param {*} multArray
   */
  textTransf(multArray) {
    return textTransf(multArray)
  },
  changeSeletTimePopupName(type) {
    this.setData({
      seletTimePopupName: popupTitleMap[type] || deliveryToDoorTitle,
    });
  },
  // 选择祝福卡
  handleSelectBlessCard() {
    const { blessCardSelected } = this.data;
    this.setData({
      blessCardSelected: !blessCardSelected,
    });
    Object.assign(this._data, {
      blessCardRelated: true,
    });

    this._data.isFirstSettle = 'N'
    this.orderSettle();
  },
  navigateToBless() {
    wx.navigateTo({
      url: '/homeDelivery/pages/blessCard/index',
    });
  },
  // 余额支付开关
  switchPagodaPayChangeHandle(e) {
    console.log(e)
    const { selectPagodaPay } = e.detail
    this.setData({
      selectPagodaPay,
    })
    this.checkIsMixPay()
  },
  // 设置微信支付
  setWaySelectHandle(e) {
    const { selectWxPay, selectUnionPay } = e.detail
    this.setData({
      selectWxPay,
      selectUnionPay,
    });
    this.checkIsMixPay()
  },

  /**
   * 检查是否混合支付
   */
  checkIsMixPay() {
    const {
      selectPagodaPay,
      selectWxPay,
      selectUnionPay,
    } = this.data;

    this.setData({
      isMixPay: selectPagodaPay && (selectWxPay || selectUnionPay),
    });
  },

    /**
   * @description 设置代金券
   *
   */


  async setVoucherInfo(data) {
    const {
      voucherCouponInfo,
      voucherCouponValue // 代金券优惠金额
    } = data
    const {
      availableList, // 可用
      defaultBetter, // 默认最佳
      unavailableList, // 不可用
      currentChoose // 当前选择
    } = voucherCouponInfo
    // console.log(voucherCouponInfo)
    // console.log(Boolean(availableList.length))
    // console.log(defaultBetter.length && !currentChoose.length,)
    const defaultBetterMap = defaultBetter.map(item => item.couponCode)
    const currentChooseMap = currentChoose.map(item => item.couponCode)
    const isBetterSelectVoucher = defaultBetterMap.sort().join('') === currentChooseMap.sort().join('')
    this._data.availableList = availableList
    this._data.unavailableList = unavailableList
    this._data.currentChooseVoucher = currentChoose
    this.setData({
      isBetterSelectVoucher: isBetterSelectVoucher && Boolean(currentChoose.length), // 最佳选择和当前选择相等则
      isHasAvailableVoucher: Boolean(availableList.length), // 有可用代金券
      isNoVoucher: Boolean(!availableList.length && !unavailableList.length), // 无代金券
      isSelectVoucher: Boolean(currentChoose.length), // 选择了优惠券
      voucherCouponValue, // 代金券优惠金额
      availableCount: availableList.length, // 代金券可用张数
    })
  },
    /**
   * 选择代金券
   */
  selectedVoucher() {
    const {
      availableList,
      unavailableList,
      currentChooseVoucher,
      applyCardSeleted
    } = this._data
    const {
      voucherCouponValue,
      payAmount,
      isNoVoucher,
      vipCardInfo = {},
      forbidPdPay
    } = this.data
    // if (isNoVoucher) {
    //   return
    // }
    let payAmountNoVoucher = Number(voucherCouponValue) + Number(payAmount)
    const selectedCouponList = currentChooseVoucher.map( item => item.couponCode)
    // 如果选择了年卡，则计入代金券可使用价格范围内
    if (applyCardSeleted && forbidPdPay) {
      const applyVipCardMoney = vipCardInfo.money || 0; // 开通心享卡费用
      payAmountNoVoucher -= Number(applyVipCardMoney);
    }


    wx.navigateTo({
      url: '/userA/pages/selectVoucher/index',
      success: (res) => {
        res.eventChannel.emit('voucherInfo', {
          voucherCouponInfo: {
            availableList: availableList,
            unavailableList: unavailableList,
            selectedCouponList: selectedCouponList
          },
          payAmount: payAmountNoVoucher  // 传递未使用代金券的支付金额
        })
      },
      events: {
        confirmSelectVouchers: (res) => {
          this.setSelectedVoucher(res.data)
        }
      }
    })
    app.sensors.track('MPClick', {
      element_code: 130209001,
      element_name: '选择代金券',
      element_id: '',
      element_content: '选择代金券',
      screen_code: '1302',
    });
  },
  /**
   * @description 选择优惠券回显
   */
  setSelectedVoucher(voucherList = []) {
    console.log('setSelectedVoucher', voucherList)
    const nextSelectedVoucherMap = voucherList.map(item => item.couponCode)
    const { currentChooseVoucher } = this._data
    const curSelectedVoucherMap = currentChooseVoucher.map(item => item.couponCode)
    const isChange = nextSelectedVoucherMap.sort().join() !== curSelectedVoucherMap.sort().join()
    // 没有手动改变代金券选择，则不用重新结算
    if (!isChange) {
      return
    }
    this._data.isFirstSettle = 'N'
    this._data.currentChooseVoucher = voucherList
    console.log('选择的券对象列表',voucherList, this._data.isFirstSettle )
    this.orderSettle();
  },
  /**
   * @description 展示月卡权益详情
   */
  showBenfitsDetail(e){
    const { url = '' } = e.detail
    if(url){
      this.setData({
        popupBenefits:{
          show:true,
          url
        }
      })
    }
  },
  /**
   * @description 关闭月卡权益详情
   */
  closePopupBenefits(){
    this.setData({
      popupBenefits:{
        show:false,
        url:''
      }
    })
  },
  cacheSettleUniqCode(resData){
    const { newUniqCode = '' } = resData
    this._data.settleResUniqCode = newUniqCode
  },
  /**
   * 处理兑换卡信息
   */
  setExchangeCardInfo(data) {
    const { exchangeCardInfo, exchangeCardValueTotal } = data || {}
    const { availableList = [], unavailableList = [], currentChoose = [] } = exchangeCardInfo || {}
    this.setData({
      exchangeCardInfo,
      isHasAvailableExchangeCard: Boolean(availableList.length), // 有可用兑换卡
      isNoExchangeCard: Boolean(!availableList.length && !unavailableList.length), // 无兑换卡
      isSelectExchangeCard: Boolean(currentChoose.length), // 选择了兑换卡
      availableExchangeCardCount: availableList.length, // 兑换卡可用张数
      exchangeCardValueTotal, // 兑换卡优惠金额
      currentChooseExchangeCard: currentChoose, // 当前选中的兑换卡
    })
  },
  /**
   * 选择兑换卡
   */
  selectedExchangeCard() {
    const { exchangeCardInfo = {}, exchangeCardMaxUseNum } = this.data
    const { availableList = [], unavailableList = [], currentChoose = [] } = exchangeCardInfo || {}
    const selectedExchangeCardList = currentChoose
    const { flatGoodsList } = this.getCouponMatchGoodsInfo(availableList)

    wx.navigateTo({
      url: '/userA/pages/selectExchangeCard/index',
      success: (res) => {
        res.eventChannel.emit('exchangeInfo', {
          exchangeCardInfo: {
            availableList,
            unavailableList,
            selectedExchangeCardList,
            flatGoodsList,
            exchangeCardMaxUseNum,
            currentNewActUuid: this._data.currentNewActUuid,
          }
        })
      },
      events: {
        confirmSelectExchangeCard: (res) => {
          this.setSelectedExchangeCard(res.data)
        }
      }
    })
  },
  /**
   * 处理选中的兑换卡
   */
  setSelectedExchangeCard(data) {
    const inTimeGoodsList = this.data.inTimeGoodsList
    const { currentChooseExchangeCard } = this.data
    const selectedExchangeCardList = data.selectedExchangeCardList
    const curSelectedExchangeCardMap = currentChooseExchangeCard.map(item => item.couponCode)
    const nextSelectedExchangeCardMap = selectedExchangeCardList.map(item => item.couponCode)
    const isChange = nextSelectedExchangeCardMap.sort().join() !== curSelectedExchangeCardMap.sort().join()
    // 没有手动改变兑换卡选择，则不用重新结算
    if (!isChange) {
      return
    }

    const couponBindGoodsMap = data.couponBindGoodsMap
    const exchangeCard4UuidMap = {};
    for (const value of couponBindGoodsMap.values()) {
      if (exchangeCard4UuidMap[value.uuid]) {
        exchangeCard4UuidMap[value.uuid]++
      } else {
        exchangeCard4UuidMap[value.uuid] = 1
      }
    }
    const _exchangeCard4UuidMap = Object.assign({}, exchangeCard4UuidMap)

    /**之前的新人特价uuid */
    const beforeCurrentNewActUuid = this._data.currentNewActUuid
    const goodsList = inTimeGoodsList.filter(goods => {
      const exchangeCardCount = exchangeCard4UuidMap[goods.uuid]
      //  商品已使用兑换卡
      if (exchangeCardCount) {
        let count = goods.count

        //  根据匹配成兑换卡的数量进行递减
        for (let i = 1; i <= exchangeCardCount; i++) {
          if (count === 0) {
            break
          }
          count--
          exchangeCard4UuidMap[goods.uuid]--
        }

        if (count === 0) {
          return false
        } else {
          return true
        }
      }
      return true
    })
    /**最大折扣商品uuid */
    const maxDiscountUuid = getMaxDiscountUuid(goodsList)
    /**顺位的新人特价商品 */
    const currentNewActUuid = maxDiscountUuid[0]
    const settlementGoodsList = this.data.settlementGoodsList
    this._data.currentNewActUuid = currentNewActUuid

    settlementGoodsList.forEach(goods => {
      /**该商品的uuid */
      const uuid = getCartGoodsUniqCode({ goods })
      const goodsInfo = inTimeGoodsList.find(goods => goods.uuid === uuid)
      /**当前商品为新人特价商品 */
      const hasActivityPriceNew = currentNewActUuid === uuid
      /**有同sku不同服务的商品使用了新人特价，不允许使用特价 */
      const hasActivityPriceNewSameSku = !hasActivityPriceNew && currentNewActUuid?.includes(goods.goodsSn)

      //  该商品之前是新人特价商品，移除新人特价属性
      if (uuid === beforeCurrentNewActUuid) {
        delete goods.isNewAct
      }

      //  该商品变为新人特价商品，增加新人特价属性及更新活动编码
      if (uuid === currentNewActUuid) {
        goods.isNewAct = true
      }

      //  特价活动编码 = 新人特价活动
      goods.activityCode = (hasActivityPriceNew ? goodsInfo.activityCodeNew : goodsInfo.activityCode) || ''

      //  全是兑换卡商品 和 与新人特价商品同sku不同服务的商品，不允许使用特价活动
      if (_exchangeCard4UuidMap[uuid] === goods.count || hasActivityPriceNewSameSku) {
        delete goods.activityCode
      }
    })

    this.setData({
      settlementGoodsList
    })

    this._data.isNeedMatchExchangeCard = 'N'
    this._data.isFirstSettle = 'Y'
    this.setData({
      currentChooseExchangeCard: selectedExchangeCardList
    })
    this.orderSettle()
  },
  /**
   * 获取兑换卡匹配商品信息
   * @returns
   */
  getCouponMatchGoodsInfo(couponList) {
    const { inTimeGoodsList = [] } = this.data
    /**
     * 将相同onlyId的商品放入同一个数组
     * 因为存在同goodsSn，但服务不同的情况。此时会返回多个商品行
     */
    const onlyIdGoodsListMap = inTimeGoodsList.reduce((prev, current) => {
      const onlyId = util.getGoodsOnlyId(current)

      if (prev[onlyId]) {
        prev[onlyId].push(current)
      } else {
        prev[onlyId] = [current]
      }
      return prev
    }, {})

    /**
     * 将每个goodsSn收集为goodsList并进行排序
     */
    const sortGoodsList = Object.keys(onlyIdGoodsListMap).reduce((prev, onlyId) => {
      const goodsList = onlyIdGoodsListMap[onlyId]
      const goods = goodsList[0]

      prev.push(goods)

      return prev
    }, []).sort((goods1, goods2) => {
      return new Date(goods1.addCartTime) - new Date(goods2.addCartTime)
    })

    // 勾选了开通心享时，暂时修改会员属性不为C即可
    const superVipStatus = this.data.vipCardIsSeleted ? 'F' : app.globalData.superVipStatus
    const isVip = superVipStatus !== 'C'

    // 将商品信息按每行count为1的方式进行打平
    const flatGoodsList = sortGoodsList.reduce((prev, current) => {
      const onlyId = util.getGoodsOnlyId(current)
      /**相同onlyId的商品列表 */
      const goodsList = onlyIdGoodsListMap[onlyId]
      /**该goodsSn已加购过的商品数量 */
      let beforeCount = 0

      goodsList.forEach(goods => {
        const count = goods.count

        for (let i = 1; i <= count; i++) {
          beforeCount += 1
          /**此处打平无需考虑新人特价价格 */
          const price = getGoodsShowPrice(goods, beforeCount, isVip, void 0)

          prev.push({
            ...goods,
            price,
            count: 1
          })
        }
      })

      return prev
    }, [])

    return {
      flatGoodsList,
      ...getCouponMatchGoodsMap({
        flatGoodsList,
        couponList,
        isVip
      })
    }
  },
  /**
   * 整理用户手动选择兑换卡后，结算接口兑换卡相关的入参信息
   */
  handleUserSelectedExchangeCard() {
    const { currentChooseExchangeCard = [] } = this.data
    const { coupon2Goods } = this.getCouponMatchGoodsInfo(currentChooseExchangeCard)
    const curExchangeCardList = currentChooseExchangeCard.map(item => {
      const goods = coupon2Goods[item.couponCode] || {}
      const { selectSpecsServiceList = [], goodsSn } = goods || {}
      const price = getMemberPrice(goods)
      let goodsIdentifier = ''
      if (selectSpecsServiceList && selectSpecsServiceList.length > 0) {
        const serviceSn = selectSpecsServiceList[0].serviceSn || ''
        goodsIdentifier = `${goodsSn}_${serviceSn}_${price}`
      } else {
        goodsIdentifier = `${goodsSn}_${price}`
      }
      return {
        couponId: item.couponCode,
        type: 3,
        goodsIdentifier
      }
    })
    return curExchangeCardList || []
  },
  /**
   * 提交订单成功后埋点上报
   */
  submitOrderSuccessReport(result) {
    try {
      const params = this.getConfirmOrderParams()
      const { orderInfoList = [], deliveryWay, orderAmount, payAmount } = params || {}
      const { goodsList = [], orderTakeawayAttr } = orderInfoList[0] || {}
      const { mainOrderNo, subOrderNos = [] } = result || {}
      const isSplitOrder = (subOrderNos.length > 1 || subOrderNos[0] !== mainOrderNo) ? 1 : 0
      const skuNum = goodsList.reduce((sum, cur) => {
        return sum + (cur.quantity || 0)
      }, 0)
      const {
        couponType = [],
        couponBatchNumList = [],
        couponNameList = [],
        orderType,
        way
      }
      = this.getReportInfoList({orderTakeawayAttr, deliveryWay}) || {}
      const {
        totalGoodsHeartMoney = 0,
        canUseCouponGoodsMemberPrice = 0,
        vipCardIsSeleted = false,
        blessCardSelected = false,
        superVipStatus = 'C',
        inTimeGoodsList = []
      } = this.data
      // 可用券金额
      let useCouponOrderAmount = canUseCouponGoodsMemberPrice
      const isVip = ['T','F'].includes(superVipStatus)
      if(isVip || vipCardIsSeleted){
        useCouponOrderAmount = totalGoodsHeartMoney
      }
      const SKU_ID = []
      const SKU_Name = []
      const spuId = []
      inTimeGoodsList.forEach(item => {
        const { goodsSn, goodsName, spuNumber } = item || {}
        SKU_ID.push(goodsSn)
        SKU_Name.push(goodsName)
        spuId.push(spuNumber)
      })
      const reportData = {
        mainOrderNo,
        OrderType: orderType,
        DeliveryWay: way,
        sku_cnt: skuNum,
        orderAmount: util.formatPrice(orderAmount),
        actualPaymentAmount: util.formatPrice(payAmount),
        couponType,
        couponBatchNumList,
        couponNameList,
        // 可用券金额
        useCouponOrderAmount: util.formatPrice(useCouponOrderAmount) || 0,
        // 是否选择了祝福卡
        is_gift_card: blessCardSelected ? 1 : 0,
        SKU_ID,
        SKU_Name,
        spuId,
        // 是否拆单
        isSplitOrder
      }
      sensors.track('SubmitOrder', reportData)
      
      // 订单详情埋点上报
      const arrOrderDetailReportData = []
      inTimeGoodsList.forEach(item => {
        const { goodsSn, goodsName, selectSpecsServiceList = [] } = item || {}
        const reportInfo = getSaleReportInfo({ goodsObj: item })
        const { goods_label = [] } = reportInfo || {}
        arrOrderDetailReportData.push({
          mainOrderNo,
          OrderType: orderType,
          SKU_ID: goodsSn,
          SKU_Name: goodsName,
          choosableService: selectSpecsServiceList.map(service => service.serviceName),
          goods_label,
          isSplitOrder
        })
      })
      // 上报埋点
      arrOrderDetailReportData.forEach(item => sensors.track('SubmitOrderDetail', item))
    } catch(e) {
      console.error(e)
    }
  },
  /**
   * 整合需要上报的信息
   */
  getReportInfoList(obj) {
    const couponType = []
    const couponBatchNumList = []
    const couponNameList = []
    const { selectedCouponList = [], currentChooseExchangeCard = [] } = this.data
    const { orderTakeawayAttr, deliveryWay } = obj || {}
    const { currentChooseVoucher = [] } = this._data
    const orderType = orderTakeawayAttr === 'B2C' ? '全国送' : (deliveryWay === 2 ? '及时达到家' : '及时达自提')
    const way = orderTakeawayAttr === 'B2C' ? '快递' : (deliveryWay === 2 ? '配送上门' : '自提')
    const couponList = selectedCouponList.concat(currentChooseVoucher, currentChooseExchangeCard) || []
    couponList.forEach(item => {
      const { defineId, batchNum, couponName } = item || {}
      defineId && couponType.push(String(defineId))
      batchNum && couponBatchNumList.push(batchNum)
      couponName && couponNameList.push(couponName)
    })
    return {
      couponType,
      couponBatchNumList,
      couponNameList,
      orderType,
      way
    }
  },
  /**
   * 选择积分兑换
   */
  handleSelectIntegral() {
    const { integralSelected, notSelectedIntegral } = this.data
    if (notSelectedIntegral) {
      wx.showToast({
        title: '可用积分不足',
        icon: 'none',
        duration: 2000
      })
      return
    }
    this.setData({
      integralSelected: !integralSelected
    })
    this._data.isFirstSettle = 'N'
    this.orderSettle()
  },
  /**
   * 处理积分兑换活动
   */
  handleIntegralExchange({integralInfo = {}, integralExchangeList = []}) {
    const { packAmount = 0, integralSelected } = this.data
    const { availableIntegral = 0 } = integralInfo
    const { activityCode = '', matchGoods = [] } = integralExchangeList.find(
      val => val.activityType === 'T013' && val.activitySubtype === 'S027') || {}
    if (!matchGoods.length) return
    let totalIntegral = 0
    let totalIntegralAmount = 0
    matchGoods.forEach(good => {
      const { integral = 0, integralAmount = 0 } = good || {}
      totalIntegral += integral
      totalIntegralAmount += integralAmount
    })
    // 优惠后的包装费金额
    const packAmountAfterDiscount = (packAmount - totalIntegralAmount) || 0
    this.setData({
      userIntegral: availableIntegral,
      integralExchangeInfo: {
        activityCode,
        integral: totalIntegral,
        integralAmount: totalIntegralAmount
      },
      notSelectedIntegral: availableIntegral < totalIntegral,
      packAmountAfterDiscount
    })
    if (integralSelected && availableIntegral < totalIntegral) {
      this.handleSelectIntegralError({content: '可用积分不足'})
    }
  },
  /**
   * 处理勾选积分兑换活动出现异常
   */
  async handleSelectIntegralError({content}) {
    const modalRes = await app.showModalPromise({
      content,
      showCancel: false,
      confirmText: '知道了'
    });
    if (modalRes) {
      this.initIntegralExchange()
      this.orderSettle()
    }
  },
  /**
   * 初始化积分兑换活动信息
   */
  initIntegralExchange() {
    const { integralSelected, integralExchangeInfo: { integral = 0 } = {} } = this.data
    if (integralSelected || integral > 0) {
      this.setData({
        integralSelected: false,
        integralExchangeInfo: {
          activityCode: '',
          integral: 0,
          integralAmount: 0
        }
      })
    }
  },
  /**
   * 处理优惠信息的展示（特价优惠、果粉优惠、心享优惠）
   */
  handleDiscountList() {
    const { fansFree, vipPriceDiscount, discountAmount, newActDiscountAmount } = this.data
    const discountList = []
    const array = [
      {
        name: '新人价',
        value: newActDiscountAmount
      },
      {
        name: '特价',
        value: discountAmount
      },
      {
        name: '果粉',
        value: fansFree
      },
      {
        name: '心享',
        value: vipPriceDiscount
      }
    ]
    array.forEach(item => {
      if (item.value > 0) {
        discountList.push({
          text: `已享${item.name}优惠: ￥${util.formatPrice(item.value)}`
        })
      }
    })
    this.setData({
      discountList,
      showDiscountDetailArrow: Boolean(discountList.length)
    })
  },
  tapGoodsAmount() {
    const { isFruitFansGray, discountList } = this.data
    if (!isFruitFansGray || !discountList.length) return
    this.setData({
      showDiscountDetail: !this.data.showDiscountDetail
    })
  },

  /**
   * 跳转更多充值活动
   */
  moreRecharge() {
    wx.navigateTo({
      url: '/userA/pages/deposit/index',
      events: {
        rechargeSuccess: () => {
          //  充值成功后，重新结算
          this._data.isRefreshOrder = true
        }
      }
    })
  },

  /**
   * 选择随单充活动
   */
  selectOrderInlineRecharge(event) {
    const {
      act = {},
    } = event.detail || {}

    const {
      /** 充值门槛 */
      lowerLimit,
      /** 赠送金额 */
      giftAmount,
    } = act

    const {
      /** 主账户余额 */
      mainBalance,
    } = this.data
    /** 订单金额 */
    const payAmount = Number(this.data.payAmount)

    const rechargePayInfo = getOrderInlineRechargeInfo({
      mainBalance: mainBalance,
      payAmount: payAmount,
      lowerLimit: lowerLimit,
      giftAmount: giftAmount,
    })

    //  选择了随单充活动后
    this.setData({
      //  必须选择钱包支付
      selectPagodaPay: true,
      //  必须选择微信支付
      selectWxPay: true,
      //  不能云闪付钱包支付
      disableUnionPay: true,
      //  无需禁用支付方式
      disablePay: false,

      isSelectRechargeAct: true,
      rechargeAct: act,
      rechargePayInfo: rechargePayInfo,
    })

    sensors.clickReport('1302_130211002', {
      value: lowerLimit / 100,
    })
  },

  /**
   * 取消选择随单充活动
   */
  unSelectOrderInlineRecharge(event) {
    this.setData({
      isSelectRechargeAct: false,
      rechargeAct: {},
      rechargePayInfo: {},
    })
    if (this.setPayWayHandle) {
      this.setPayWayHandle()
    }

    if (event) {
      sensors.clickReport('1302_130211003')
    }
  },
});
