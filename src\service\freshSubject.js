
const { deepClone } = require('../utils/util.js')
const {
  FreshGoods
} = require('./freshGoods.js')
const { deliveryCenterCodeDmTransEshop } = require('../utils/deliveryCenterUtil')
const cos = require('./cosInstance');
const { fetchJson } = require('~/utils/readAndSaveFile/index.js');
class SubjectConfig {
  constructor({ activityID, isOnlyGoodsModule = false }) {
    this.activityID = activityID;
    this.isOnlyGoodsModule = isOnlyGoodsModule
    const { deliveryCenterCode, cityCode, storeCode } = getApp().globalData.bgxxCityInfo || {};
    const { userID } = wx.getStorageSync('user') || {};
    const { unionid = '' } = wx.getStorageSync('wxSnsInfo') || {};
    this.deliveryCenterCode = deliveryCenterCode
    this.cityCode = cityCode
    this.storeCode = storeCode
    this.userID = userID
    this.unionid = unionid
  }
  async getModule() {
    // 获取综合专题配置
    const subjectModuleConfig = await this.getSubjectConfig()
    if (!subjectModuleConfig) {
      console.error('未查询到活动')
      return {}
    }
    if (this.isOnlyGoodsModule) {
      this.filterGoodsAllModule(subjectModuleConfig)
    }
    let configGoodsList = []

    let isRequestModule = false
    subjectModuleConfig.togetherModuleList.forEach( (module) => {
      // 图片模块跳转商品详情的时候也需要查询商品信息
      if (module.type === '1') { // 图片模块
        isRequestModule || (isRequestModule = true)
        // module.activityPicList = this.filterPicModule(module)
        const picConfigGoodsList = this.filterPicModuleGoods(module)
        configGoodsList = configGoodsList.concat(picConfigGoodsList)
      } else if (['2','3','6'].includes(module.type)) { // 商品模块
        module.goodsList = this.filterGoodsModule(module)
        configGoodsList = configGoodsList.concat(module.goodsList)
      } else if (module.type === '4') { // 优惠券模块
        isRequestModule || (isRequestModule = true)
      }
    })
    if(this.isOnlyGoodsModule) {
      isRequestModule = false
    }
    const options = configGoodsList.map(item => ({
      goodsSn: String(item.goodsSn || ''),
      eshopGoodsId: String(item.goodsId || ''),
    })).filter( item => item.goodsSn || item.eshopGoodsId )
    const freshGoods = new FreshGoods({ storeCode: this.storeCode, cityCode: this.cityCode, deliveryCenterCode: this.deliveryCenterCode })
    const [
      complateGoodsMap,
      moduleMap
    ] = await Promise.all([
      freshGoods.getGoodsComplateInfoMap(options, { emptySpringStock: true, emptyFrozenStock: true, filterSaleStatus: true, filterGift: true }),
      isRequestModule ? this.getCouponModule(): {}
    ])

    const { couponModuleMap = {}, picModuleMap = {}} = moduleMap
    return {
      picModuleMap,
      couponModuleMap,
      complateGoodsMap,
      subjectModuleConfig,
      freshGoods
    }
  }
      /**
   * @description  过滤出为小程序配置的图片模块
   * @param {*} module
   * @returns
   */
  filterPicModule(activityPicList) {
    if(!activityPicList.length) {
      return []
    }
    const list = []
    activityPicList.forEach(item => {
      // 过滤非小程序渠道
      if (item.platJump && item.platJump !== '2') {
        return
      }
      // 全国送过滤其他城市
      if (item.openType === '62' && item.deptCode) {
        const { cityCode } = wx.getStorageSync('timelyCity')
        if (item.deptCode !== cityCode) {
          return
        }
      }
      // 及时达商品过滤其他配送中心
      if (item.openType === '3' && item.deptCode) {
        const { deliveryCenterCode } = wx.getStorageSync('timelyCity')
        if (item.deptCode !== deliveryCenterCodeDmTransEshop(deliveryCenterCode)) {
          return
        }
      }
      // 次日达商品过滤其他配送中心
      if (item.openType === '63' && item.deptCode && item.deptCode !== deliveryCenterCodeDmTransEshop(this.deliveryCenterCode)) {
        return
      }
      list.push(item)
    });
    return list
  }
  /**
   * @description 过滤出商品模块
   * @param {*} subjectModuleConfig
   */
  filterGoodsAllModule(subjectModuleConfig) {
    subjectModuleConfig.togetherModuleList = subjectModuleConfig.togetherModuleList.filter( module => ['2','3','6'].includes(module.type))
  }
  /**
   * @description 获取优惠券模块
   * @returns
   */
  async getCouponModule() {
    const params = {
      customerID: this.userID || -1,
      unionid: this.unionid || '',
      activityID: Number(this.activityID),
      deliveryCenterCode: this.deliveryCenterCode
    };

    try {
      const result = await getApp().api.getSubjectCouponModule(params);
      const couponModuleMap = {};
      const picModuleMap = {}
      const { couponModuleList, picModule } = result.data
      couponModuleList.forEach(item => {
        couponModuleMap[item.moduleNumber] = [item];
      });
      for( const key in picModule) {
        picModuleMap[key] = this.filterPicModule(picModule[key] || [])
      }

      return {
        couponModuleMap,
        picModuleMap
      };
    } catch (err) {
      return {};
    }
  }
  /**
   * @description 从cos拉取综合专题数据
   * @returns
   */
  async getSubjectConfig() {
    try {
      const cosUrl = cos.completeNoAuthUrl(`/activity/${this.activityID}.json`)
      const result = await fetchJson(cosUrl)
      // console.log(result)
      if (!result) {
        return;
      }
      return result
    } catch (err) {
      console.error('getSubjectConfig:', err)
      return;
    }
  }
  /**
   * @description 按配置配送中心过滤
   * @param {*} module
   * @returns
   */
  filterGoodsModule(module) {
    return module.goodsList.filter(item => {
      return item.deptCode ? item.deptCode === deliveryCenterCodeDmTransEshop(this.deliveryCenterCode) : true
    })
  }
  /**
   * @description 去除图片模块的商品信息，用于查询商品详情
   * @param {*} module
   * @returns
   */
  filterPicModuleGoods(module) {
    const goodsList = []
    module.activityPicList.forEach(picItem => {
      const { openType, openValue } = picItem;
      if (['63'].includes(openType)) {
        goodsList.push({
          goodsSn: openValue,
          // goodsId: openType === "3" ? openValue : '',
          // takeawayAttr: openType === "62" ? "B2C" : "及时达"
        });
      }
    });
    return goodsList
  }
}

class SubjectProcessor {
  constructor({
    subjectModuleConfig,
    complateGoodsMap,
    freshGoods,
    couponModuleMap,
    picModuleMap
  }) {
    this.subjectModuleConfig = subjectModuleConfig;
    this.complateGoodsMap = complateGoodsMap;
    this.freshGoods = freshGoods;
    this.couponModuleMap = couponModuleMap;
    this.picModuleMap = picModuleMap
    this.navigationIndex = -1;
    this.navigationList = [];
  }
  // Main method to process all modules
  processModules() {
    const subjectModuleConfig = this.subjectModuleConfig
    if (!subjectModuleConfig) {
      return {}
    }
    const togetherModuleList = subjectModuleConfig.togetherModuleList
    subjectModuleConfig.togetherModuleList.forEach((module, index) => {
      const { type } = module;

      switch (type) {
        case '1':
          this.processImageModule(module);
          break;
        case '2':
        case '3':
        case '6':
          this.processGoodsModule(module);
          break;
        case '4':
          this.processCouponModule(module);
          break;
        case '7':
          this.processNavigationModule(module, index);
          break;
        // Handle other module types if needed
        default:
          break;
      }
    });
    // Add navigation module to the appropriate position
    if (this.navigationIndex > -1) {
      togetherModuleList.splice(this.navigationIndex, 0, {
        type: '999',
        navigationList: this.navigationList
      });
    }
    subjectModuleConfig.togetherModuleList = togetherModuleList
    subjectModuleConfig.navigationList = this.navigationList
    subjectModuleConfig.activityID = subjectModuleConfig.id
    subjectModuleConfig.cardSharePic = subjectModuleConfig.cardSharePic?.picUrl
    return subjectModuleConfig;
  }
  processImageModule(module) {
    const { windowWidth: screenW = 750 } = wx.getStorageSync('systemInfo') || {};
    module.activityPicList = this.picModuleMap[module.modulePriority] || []
    module.activityPicList.forEach(picItem => {
      const { openType, openValue, hotZone } = picItem;
      if (['63'].includes(openType)) {
        if (hotZone && JSON.parse(hotZone)[0]) {
          picItem.hotZoneStyle = JSON.parse(hotZone)[0]
          const {w, h, x, y} = picItem.hotZoneStyle
          const { maxWidth } = picItem
          Object.assign(picItem.hotZoneStyle, {
            w: screenW*(w/maxWidth),
            h: screenW*(h/maxWidth),
            x: screenW*(x/maxWidth),
            y: screenW*(y/maxWidth)
          })
        }
        picItem.goodsInfo = this.complateGoodsMap.get(String(openValue));
      }
    });
  }

  // Separate method for processing "商品模块" (Goods module)
  processGoodsModule(module) {
    const sellList = [];
    const sellOutList = [];
    module.goodsList.forEach(goods => {
      const goodsSn = goods.goodsSn
      const curGoods = this.complateGoodsMap.get(String(goodsSn));
      if (curGoods) {
        const cloneGoods = this.forMartGoods(curGoods);
        if (cloneGoods.stock !== 0 && cloneGoods.saleStatus === 1) {
          sellList.push(cloneGoods);
        } else {
          sellOutList.push(cloneGoods);
        }
      }
    });
    module.goodsList = sellList.concat(sellOutList);
  }
  forMartGoods(goods) {
    if (typeof goods !== 'object') {
      return
    }
    const newGoods = deepClone(goods)
    delete newGoods.resourceList
    return newGoods
  }
  processCouponModule(module) {
    const curActivityCouponList = module.activityCouponList.filter(item => this.couponModuleMap[item.moduleNumber]);
    const { moduleNumber } = curActivityCouponList[0] || {};
    module.couponList = this.couponModuleMap[moduleNumber] || [];
  }
  processNavigationModule(module, index) {
    if (this.navigationIndex < 0) {
      this.navigationIndex = index;
    }
    if (module.navigationList) {
      this.navigationList.push(module.navigationList[0]);
    }
  }
}

class FreshSubject {
  constructor({activityID, isOnlyGoodsModule = false}) {
    this.activityID = typeof activityID === 'string' ? Number(activityID) : activityID
    this.isOnlyGoodsModule = isOnlyGoodsModule
    this.freshGoods = null
  }
  /**
   * @description 综合专题使用，通过预请求获取详情
   */
  async getDetailWithPreRequest() {
    const cache = FreshSubject._requestCache[this.activityID]
    if (cache) {
      FreshSubject._requestCache = {}
      return cache
    }
    const result = await this.getDetail();
    return result;
  }
  /**
   * @description 预请求，广告位点击跳转综合专题前请求
   */
  async preRequest() {
    FreshSubject._requestCache[this.activityID] = this.getDetail()
  }
  /**
   * @description 获取综合专题详情
   */
  async getDetail() {
    const subjectConfig = new SubjectConfig({ activityID: this.activityID, isOnlyGoodsModule: this.isOnlyGoodsModule})
    const {
      picModuleMap,
      couponModuleMap,
      complateGoodsMap,
      subjectModuleConfig,
      freshGoods
    } = await subjectConfig.getModule()
    this.freshGoods = freshGoods
    const subjectProcessor = new SubjectProcessor({
      subjectModuleConfig, complateGoodsMap, freshGoods, couponModuleMap, picModuleMap
    })
    const subjectModule = await subjectProcessor.processModules()
    return subjectModule
  }
  /**
   * @description 返回综合专题商品列表
   */
  async getGoodsModule() {
    const subjectModule = await this.getDetail()
    if (!subjectModule.togetherModuleList) {
      return {
        navPriority: [],
        goodsModuleList: [],
      }
    }
    const ModuleList = subjectModule.togetherModuleList.filter((module) => {
      return ['2','3','6'].includes(module.type)
    })
    let navPriority = []
    if (subjectModule.navigationList && subjectModule.navigationList.length > 3) {
      navPriority = subjectModule.navigationList.map((module) => module.priority)
    }


    return {
      navPriority,
      goodsModuleList: ModuleList,
    }
  }
}
FreshSubject._requestCache = {};
module.exports = {
  FreshSubject
}

