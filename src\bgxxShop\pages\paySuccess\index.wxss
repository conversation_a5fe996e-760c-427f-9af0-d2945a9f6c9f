/* pages/mall/paySuccess/index.wxss */
@import "../../template/suspenseCart/index.wxss";
.pay-success {
  background: #F5F5F5;
  width: 100%;
  height: 100%;
}

.content-box {
  width: 100%;
  max-height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.t-wap {
  /* background: #FF6F80; */
  /* background-image:url('https://resource.pagoda.com.cn/group1/M21/56/47/CmiWa2ELoM2AER-eAAMwbtc1mjE122.png'); */
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.pay-success-text {
  margin-top: 48rpx;
  color: #FFF;
  text-align: center;
  position: relative;
  padding: 0 56rpx;
}
.pay-success-tip {
  font-size: 48rpx;
  font-weight: 600;
  display: flex;
}
.suggest-goods-price {
  color: var(--bgxxThemeColor);
  font-size: 32rpx;
  line-height: 32rpx;
  font-weight: Medium;
  margin-top: 24rpx;
}

.pay-success-tip-text {
  margin-top: 4rpx;
  font-size: 22rpx;
  font-size: 400;
  display: flex;
}

.period-purchase {
  margin-top: 8rpx;
  display: flex;
  justify-content: center;
}

.period-purchase text {
  font-size: 24rpx;
  color: rgba(153, 153, 153, 1);
}

.btn-wap {
  display: flex;
  justify-content: center;
  margin: 32rpx 0 24rpx;
}

.vip_wrapper{
  --tips-bg-color: transparent;
  --tips-bg-img: linear-gradient(-64deg, #0C8248 7%, #016E52 100%), linear-gradient(180deg, #FBEDDA 10%, #F0CF93 100%);
  --show-left-icon: none;
  --tips-text-color: #FFDFB4;
  --right-icon-url: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAA0CAMAAAD7TUujAAAAaVBMVEUAAAD/x4P/zoT/0ID/yIT/yIT/yIT/yov/x4P/x4T/x4P/yIT/x4P/yYT/yIX/yIX/yIT/x4X/yYT/yoT/yIX/yYn/yIT/yIT/xoP/x4T/yIT/x4T/yYX/x4X/yYX/yYT/xof/zIX/x4OrdNJdAAAAInRSTlMA/A4HuqBxE/by7ObUwa2Ue01EOC0a3dzLyaaJhm1kVSQiwjRJwgAAAKdJREFUSMft1UcWgzAQBFEFEMY2jhicQ9//kGZ0AmoBK2at/5R73FIzVEgpIHCXmgqMj7W8CjJHKc9EKEzsgKiaLBwQNxNlHC8u1ywcEFsTeyB+WHw7Ew8g2t7Ek4iNiQMQZy7WJhIQp0FILyJqm+MNxHElL/+hoo8Y0CXRTdNjpRc32dNo6fguP2/6gegXpSFAYwYHGYxKGsaRxj1tKLRl4aa41Pz1BxV8EK8web4nAAAAAElFTkSuQmCC);
}
.vip_wrapper-left-icon{
  width: 26rpx;
  height: 26rpx;
  margin-right: 12rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAA0CAMAAAD/uJueAAAAdVBMVEUAAAD/37T/4Lb/4rf/37T/4LX/4bX/4bX/47f/4LT/4LX/4bb/4rf/4LX/4LX/4LX/4LX/4bn/4LX/4LX/4bX/4bb/4rb/4LX/4LT/4LX/4LT/4bb/4rb/37T/4LT/4LX/4bX/4bb/4LX/4LX/4LT/4rb/37S+69dKAAAAJnRSTlMA/AQS99KBSwznkice28Gyowjgn2xFNu/LrYpYGvLWmjoieLS0MTCw36EAAAGuSURBVEjHlZTpmqIwEEVvFlYRhQbcdeyZqfd/xDYdvk5bMZKcXxhyKLlVAfvhjAT+5zsQretVrFBsLkQQgkhHSd3YEpEgEBlpd1RLxp9W2L0gw+OiP70VzrXdT7Nifx2asHGqvp/rlFmqioCgtlZgilnU+eu3uJoHMuVHOpS+MWhTgivOab3G5mb5WWHO+rlF3ZYJTPGd8mAMrgjBnbszNp4hCDvfyZqQYfZq3G+auNPOuX14BtG/CcDUskKCtjYr38jmbstcE3NySAxedV2Xrr9rfrvAqHmJasQPEk3LnPWU8cr7FSR+UW7Zjh2vcVBgSC8elkgHH+b4Gfp0rnGe8bcLnKS9c9ibKwRYVdbx0l2FDIkTveT0uBWkJuEXqfEOlb2YK4W3FORRYIEbn5Mblri71Fha4dQ+n5XPx9ISZU+/6EtEMJBwRQbEoK7Gscb1HGNIe+bdgY6hudDMpcEC7OQI+kAsxaywxi9NmpuuuABqq9SPy1gm+8cmxCMzo2QSCRzNZ/qYUgSFUQqklGl6or5BEhuiDRxxc2bnK4GRaEQaSmuVqHQiQyKyCnblC4GAfZC4U7RKAAAAAElFTkSuQmCC) no-repeat;
  background-size: auto 100%;
}
.maybe-like-bg{
  width: 192rpx;
  height: 60rpx;
}

.btn-order-view {
  width: 200rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #fff;
  position: relative;
}

.btn-order-view::after{
  content: '';
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid #FFF;
  transform: scale(.5) translate3d(-50%, -50%, 0);
  border-radius: 96rpx;
}

.btn-order-view + .btn-order-view {
  margin-left: 40rpx;
}

.suggest-title {
  font-size: 26rpx;
  font-weight: bold;
  width: 192rpx;
  height: 60rpx;
  margin: 24rpx 279rpx 0rpx;
  padding: 16rpx 44rpx 7rpx;
}

.suggest-tittle-l {
  width: 6rpx;
  height: 22rpx;
  margin-left: 30rpx;
  background-color: #a8d568;
  border-radius: 100rpx;
  display: inline-block;
  margin-right: 12rpx;
}

.suggest-goods-list {
  display: flex;
  flex-direction: row;
  align-content: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-left: 32rpx;
  padding-bottom: 32rpx;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
}

.suggest-goods {
  margin-top: 32rpx;
  width: 340rpx;
  margin-left: 23.333rpx;
  background: #fff;
}

.suggest-goods-pic {
  width: 340rpx;
  height: 340rpx;
}

.suggest-goods-detail {
  height: 182rpx;
  margin-left: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16rpx 0 20rpx 0;
}

.suggest-goods-name {
  font-size: 28rpx;
  font-weight: Regular;
  color: #2b2b2b;
  line-height: 28rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.suggest-goods-unit {
  font-size: 22rpx;
  font-weight: Regular;
  color: #666;
  margin-top: 8rpx;
  line-height: 22rpx;
}

.xxj {
  height: 24rpx;
  width: 64rpx;
  margin-left: 4rpx;
}

.xxPrice {
  color: #333;
  margin-top: 4rpx;
  font-size: 32rpx;
  line-height: 32rpx;
  font-weight: Medium;
}

/* 运营管理台  广告位图 */
.banner-box {
  width: 100%;
  padding: 16rpx 24rpx 4rpx;
}

.banner-img {
  width: 100%;
  height: auto;
}

.suggest-goods-list .goods-preview-double {
  width: calc(50% - 24rpx);
  padding: 16rpx 16rpx 0 0;
}
.buttomTips, .buttomTips-image{
  width: 421rpx;
  height: 139rpx;
  margin: 0 auto;
}
.text-dash{
  width: 100%;
  height: 1rpx;
  background-image: linear-gradient(to right, #fff 0%, #fff 50%, transparent 50%);
  background-size: 12px 100%;
  background-repeat: repeat-x;
  margin-top: 28rpx;
}
.right-icon{
  position: absolute;
  width: 83rpx;
  height: 103rpx;
  top: 0;
  right: 56rpx
}
