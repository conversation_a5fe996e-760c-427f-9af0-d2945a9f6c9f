<wxs module="scrollView" src="./scrollView.wxs"></wxs>
<wxs module="fn">
  module.exports = {
    /**
     * 获取页面状态样式名称
     * @param { boolean } activity 大促
     * @param { boolean } newCustomer 新客专区
     */
    getPageStatusClassName: function(activity, activityBgObj, newCustomer) {
      var classNames = []
      var imageType = activityBgObj.imageType
      var pic1 = activityBgObj.pic1
      var pic2 = activityBgObj.pic2

      if (imageType === '0' ? pic1 : pic1 && pic2) {
        classNames.push('activity')
      }

      if (newCustomer) {
        classNames.push('newCustomer')
      }

      return classNames
    },
  }
</wxs>

<import src="./skeleton/index.skeleton.wxml"/>

<custom-tab-bar
  wx:if="{{currentView !== 'skeletonScreen'}}"
  isGrayTheme="{{ isGrayTheme }}"
  tab-index="0"
  id="custom-tab-bar"
  showBack2Top="{{ !hideBackTop }}"
  bind:goBack="handleBackTop"
   />

<main-page currentView="{{currentView}}">
  <template is="skeleton" wx:if="{{currentView === 'skeletonScreen'}}" />
  <view class="homeDelivery-container" hidden="{{currentView === 'skeletonScreen'}}"  bindtouchstart="homePageTouchStart" bindtouchend="homePageTouchEnd">

    <!-- 吸顶后的头部（在页面滚动距离至搜索栏时，会进行吸顶） -->
    <fix-nav-top
      id="fix-nav-top"
      searchText="{{ searchGoodsPlaceholder }}"
      locateInfo="{{ addressInfo }}"
      data-height="{{ navBarHeight }}"
    />
    <!-- 吸顶后的头部 -->

    <!-- 瀑布流tabbar吸顶
    1. scroll-view里面使用 position: fixed; 失效
    2. scroll-view里面使用 position: sticky; 页面高度发生变化时，ios某些机型下tabbar发生抖动
    3. 因此把瀑布流tabbar放在scroll-view外面吸顶
    -->

    <!-- scroll-view开启refresher-enabled之后，内部的fixed会失效。所以需要两个nav-top -->
    <!-- fix-nav-top 用于吸顶；relative-nav-top跟随页面滚动 -->
    <scroll-view
      refresher-enabled
      scroll-y="{{canScrollY}}"
      scroll-into-view="{{scrollIntoView}}"
      scroll-with-animation="{{scrollAnimation}}"
      refresher-threshold="{{refresher.threshold}}"
      refresher-triggered="{{refresher.triggered}}"
      refresher-default-style="none"
      scroll-top="{{pageScrollViewTop}}"
      bindscroll="{{scrollView.onScroll}}"
      bindrefresherpulling="{{scrollView.onContentPull}}"
      bindrefresherrestore="{{scrollView.onRestore}}"
      bindrefresherabort="{{scrollView.onRestore}}"
      bindrefresherrefresh="{{scrollView.onRefresh}}"
      bindscrolltolower="onReachBottom"
      class="scroll-box {{showMetroUserGuide ? '' : 'safe-bottom'}}"
      lower-threshold="700"
      bindscrolltolower="onScrollViewBottom"

      data-searchbartop="{{ searchBarInfo.top }}"
    >
      <view class="scroll-flag" id="scroll-top-0"></view>
      <!-- 自定义下拉刷新动画 -->
      <view slot="refresher" class="custom-refresh-zone tips-hide" data-threshold="{{refresher.threshold}}">
        <view class="custom-refresh-zone-tips">继续下拉刷新页面</view>
        <view class="custom-refresh-zone-tips">释放刷新页面</view>
        <view class="custom-refresh-zone-tips-loading">加载中</view>
        <view class="custom-refresh-zone-img frames-animate"></view>
      </view>
      <!-- 自定义下拉刷新动画 -->

      <!-- 大促活动展示需要在不展示新人特价的情况下才展示 -->
      <view
        class="inner-container {{ isGrayTheme ? 'gray-theme' : '' }} {{ fn.getPageStatusClassName(!isShowNewGoodsArea, activityBgObj, specialAreaCouponList.length) }} {{ isShowNewGoodsArea ? 'new-user-area' : '' }}">

        <!-- 未进行吸顶时的页面头部 -->
        <relative-nav-top
          id="relative-nav-top"
          searchText="{{searchGoodsPlaceholder}}"
          couponNum="{{couponNum}}"
          locateInfo="{{addressInfo}}"
          data-height="{{navBarHeight}}"
          activityBgObj="{{activityBgObj}}"
          isShowNewGoodsArea="{{isShowNewGoodsArea}}"
          bind:getNavBarHeight="getNavBarHeight"
          bind:closeLocateTips="closeDefaultLocateTips"
          bind:openSetting="onTapOpenSetting"
          bind:hideAddModle ="closeAddGandle"
          ></relative-nav-top>
        <!-- 未进行吸顶时的页面头部 -->
        
        <!-- 新人专享商品列表 -->
        <view wx:if="{{ isShowNewGoodsArea }}" class="new-goods">
          <!-- 搜索栏 -->
          <view id="search-bar">
            <search-bar wx:if="{{ !unSupportB2CAndStore && !supportB2cButNoContent }}" no-border placeholder="{{ searchGoodsPlaceholder }}"></search-bar>
          </view>
          <!-- 新人专享商品列表 -->
          <new-user-goods
            id="new-user-goods"
            couponList="{{ specialAreaCouponList }}"
            bindtoShowChoiceLayer="toShowChoiceLayer"
          ></new-user-goods>
        </view>

        <!-- 首页内容模块 -->
        <view class="content-modal {{ addressInfo.supportStoreService ? '' : 'content-modal--gray' }}">
          <!-- 没有展示新人特价时展示 -->
          <block wx:if="{{ !isShowNewGoodsArea }}">
            <!-- 搜索栏 -->
            <view id="search-bar">
              <search-bar wx:if="{{ !unSupportB2CAndStore && !supportB2cButNoContent }}" placeholder="{{ searchGoodsPlaceholder }}"></search-bar>
            </view>
            <!-- 搜索栏 -->

            <!-- 新人专享 -->
            <new-user-coupon wx:if="{{ addressInfo.supportStoreService && isLoadNewGoods }}" couponList="{{specialAreaCouponList}}" isLogin="{{isLogin}}" bindclickcoupon="newUserCouponClick"></new-user-coupon>
            <!-- 新人专享 -->
          </block>
          <!-- 商品模块 -->
          <view class="goods-modal">
            <!-- metro -->
            <view class="{{ isShowNewGoodsArea ? 'top-gap' : '' }}">
              <metro-list locateInfo="{{addressInfo}}" customerID="{{userInfo.customerID}}" id="metro-list" bind:registerRefreshComp="registerRefreshComp"></metro-list>
            </view>
            <!-- metro -->
            <!-- 订单流转条幅 -->
            <view class="order-bar" style="display: {{isShowOrderBar ? 'block' : 'none'}}">
              <order-bar
                showTip
                autoplay="{{ false }}"
                bind:showOrderBar="showOrderBar"
                id="order-bar"
                bind:registerRefreshComp="registerRefreshComp"></order-bar>
            </view>
            <!-- 订单流转条幅 -->

            <!-- 次日达入口 -->
            <fresh-entry
              id="fresh-entry"
              userInfo="{{ userInfo }}"
              addressInfo="{{ addressInfo }}" />
            <!-- 次日达入口 -->

            <!-- 接龙入口 -->
            <relay-entry
              id="relay-entry"
              addressInfo="{{ addressInfo }}" />
            <!-- 接龙入口 -->
          </view>
        </view>
      </view>

      <!-- 瀑布流顶部标记 -->
      <view class="scroll-flag waterfall" id="scroll-flag-waterfall" style="transform:translateY({{-(navBarHeight - 2)}}px);"></view>
      <!-- 瀑布流 -->
      <waterfall-goods
        wx:if="{{supportB2COrStore}}"
        id="waterfallGoods"
        userInfo="{{userInfo}}"
        addressInfo="{{addressInfo}}"
        distanceToTop="{{navBarHeight}}"
        bindinited="waterfallTabInited"
        bindscrolltop="scrollIntoView"
        bindfixed="waterfallTabbarFixed"
        bindtoShowChoiceLayer="toShowChoiceLayer"
        bind:registerRefreshComp="registerRefreshComp"
        bind:updateCartCount="updateCartCount"
      ></waterfall-goods>

      <!-- 缺省模块 -->
      <view class="containor-default" wx:if="{{ supportB2cButNoContent || unSupportB2CAndStore }}" style="margin-top: calc((100vh - {{navBarHeight}}px - {{tabbarHeight}}px) * 0.1)">
        <!-- 全国送有服务无商品 -->
        <block wx:if="{{ supportB2cButNoContent }}">
          <view class="default-image">
            <image src="{{defaultImgUrl}}" />
          </view>
          <view class="default-tips">当前地址附近暂无百果园门店</view>
          <view class="default-btn" bind:tap="navigateToLocate">换个地址</view>
        </block>
        <!-- 全国送有服务无商品 -->

        <!-- 全国送无服务 -->
        <block wx:else>
          <no-store customMargin></no-store>
        </block>
        <!-- 全国送无服务 -->
      </view>
      <!-- 缺省模块 -->
    </scroll-view>

    <!-- 直播 -->
      <floating
        wx:if="{{!showMetroUserGuide}}"
        bind:registerRefreshComp="registerRefreshComp"
        isGrayTheme="{{ isGrayTheme }}"
        id="floating"
        userInfo="{{userInfo}}"
        addressInfo="{{addressInfo}}"
        pageScrolling="{{isPageScrolling}}"
      ></floating>

  </view>

  <!-- 蒙层 -->
  <view class="prevent-screen" hidden="{{!prevent}}"></view>
</main-page>

<!-- 广告弹窗&红包雨弹窗 -->
<page-dialog isGrayTheme="{{ isGrayTheme }}" fetchDialog="{{fetchDialogData}}" isShowDialog="{{isShowDialog}}" userInfo="{{userInfo}}" addressInfo="{{addressInfo}}" bind:hideBounce="closePageDialog" bind:loadedData="pageDialogLoaded"></page-dialog>

<!-- 新注册用户礼包弹窗 -->
<new-customer-layer isGrayTheme="{{ isGrayTheme }}" coupon="{{couponData}}" bind:closeNewCustomerLayer="closeNewCustomerLayer"></new-customer-layer>

<!-- sku加购弹层 -->
<add-goods-layer showLayer="{{showLayer}}" goodsInfo="{{goodsInfo}}" is-tabbar bind:updateCount="updateCartCount"></add-goods-layer>

<!-- 自定义modal弹窗 -->
<confirm-modal
    contentText="{{storeCanDeliveryTipsWord}}"
    isShowConfirmModal="{{ showCustomConfirmModal}}"
    cancelText="继续选购"
    confirmText="换个门店"
    clickMaskToClose="{{false}}"
    bind:confirm="handleChoice"
    >
  </confirm-modal>


<confirm-modal id="globalModal" globalModalInfo="{{globalLocateModalInfo}}"></confirm-modal>

<common-loading />
