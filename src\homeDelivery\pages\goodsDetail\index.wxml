<!--homeDelivery/pages/goodsDetail/index.wxml-->
<import src="templates/index" />
<import src="./skeleton/index.skeleton.wxml"/>
<wxs module="common" src="../../../utils/common.wxs"></wxs>

<main-page currentView="{{currentView}}">
  <goods-detail-nav-bar bind:home="navigateToHome" bind:back="goBack" bind:navBarHeight="setNavBarHeight"></goods-detail-nav-bar>
  <template is="skeleton" wx:if="{{!preRender}}" />
  <view wx:else>
    <scroll-view class='contain' scroll-y style='height:{{height - navBarHeight}}px'>
      <!-- 商品轮播图、商品信息 -->
      <view>
        <!-- 商品轮播图 -->
        <view class='swiper-box'>
          <view class="sellout-box" wx:if="{{!goodsObj.stockNum}}">
            <view class="sellout-box__content">售罄</view>
          </view>
          <view class="placeholder" wx:if="{{preRender && showPlaceHolder}}">
            <!-- 保持与commonGoods一样 -->
            <image class="video-image" src="{{common.formatWebp(common.getImageUrl(picUrl, placeHolderPic), 60)}}" mode="aspectFit" />
            <!-- <image class="video-image" src='{{placeHolderPic}}' /> -->
            <!-- <image class="video-image" src='https://resource.pagoda.com.cn/dsxcx/images/e52fd7a15acc1dd3207423bf997d6fe7.png' /> -->
          </view>
          <swiper current="{{curentCarouselIndex}}" indicator-dots="{{false}}" duration="500" interval="3000" circular autoplay="{{false}}" indicator-color="rgb(245,245,245)" indicator-active-color="#008C3C" bindchange="changeCarouselIndex">
            <swiper-item wx:for="{{goodsObj.goodsDetailHeadVideoList}}" wx:key="videoUrl">
              <view class='video-item-box'>
                <!-- 视频播放层 -->
                <video custom-cache='{{false}}' muted="{{isMuted}}" id="video_banner_{{index}}" data-id="video_banner_{{index}}" wx:if="{{curr_id == 'video_banner_' + index}}" show-center-play-btn='{{false}}' play-btn-position='center' show-fullscreen-btn="{{false}}" show-mute-btn='{{true}}' enable-progress-gesture='{{false}}' controls show-progress='{{false}}' src='{{common.getImageUrl(picUrl, item.videoUrl)}}' bindplay="videoPlaying" bindpause="videoPause" bindended='videoEnded' binderror='videoPlayError' bindwaiting='videoPlayError' class='video'></video>
                <!-- 视频图片层 -->
                <view wx:else class="video-image-box" data-id="video_banner_{{index}}" catchtap='videoPlay'>
                  <view class="video-cover-box">
                    <image class="video-image" src="{{common.getImageUrl(picUrl, item.videoCoverUrl)}}"></image>
                  </view>
                  <!-- 视频按钮 -->
                  <image class="video-image-play" src="https://resource.pagoda.com.cn/group1/M21/60/A5/CmiLkGFS3LWAVLZcAAANtqegI2c355.png" mode="scaleToFill"></image>
                </view>
              </view>
            </swiper-item>
            <block wx:for="{{goodsObj.detailHeadPicList}}" wx:key="index">
              <swiper-item>
                <image class="slide-image" src="{{common.getImageUrl(picUrl,item)}}" bind:load="carouselImgLoad" bind:error="carouselImgError" data-url="{{item}}" mode='aspectFit' />
              </swiper-item>
            </block>
          </swiper>
          <view class="carousel-index-box" wx:if="{{goodsObj.detailHeadPicList.length || goodsObj.goodsDetailHeadVideoList.length}}">
            <text class="index-text">{{curentCarouselIndex + 1}}/{{goodsObj.detailHeadPicList.length + goodsObj.goodsDetailHeadVideoList.length}}</text>
          </view>
        </view>
      </view>
      <!-- 门店特价 -->
      <count-down
        wx:if="{{ priceDisplayType === priceDisplayTypeEnum.activityPrice_store }}"
        activityEndTime="{{goodsObj.activityEndTime}}"
        systemTime="{{systemTime}}"
        type="{{20}}"
        bindcomplete="handleCountComplete"
      >
        <view class="store-goods-priceInfo">
          <goods-price goodsObj="{{goodsObj}}" customClassName="store-activity"></goods-price>
          <fruit-goods-price goodsObj="{{goodsObj}}" currentMode="GOODS_DETAIL_STORE_ACT" />
          <template is="limitCount" data="{{...goodsObj, limitCountClass: 'store-activity'}}"></template>
        </view>
      </count-down>


      <view class="goods-detail-card">
        <view
          wx:if="{{ priceDisplayType === priceDisplayTypeEnum.activityPrice_new }}"
          class="activityPriceNew-row">
          <view class="activityPriceNew-price">
            <view style="height:0;">
              <!-- 新人价购买价格 -->
              <text class="price-symbol">￥</text>
              <text class="price-amount">{{ common.formatPrice(goodsObj.activityPrice) }}</text>
              <!-- 新人价购买价格 -->
            </view>
            <view class="new-price-label-wrap">
              <!-- 新人价标签 -->
              <view class="new-price-label"><view>新人价</view><view>限1份</view></view>
              <!-- 新人价标签 -->
            </view>
            <!-- 零售价购买价格 -->
            <view class="retailPrice-amount">￥{{ common.formatPrice(goodsObj.retailPrice) }}</view>
            <!-- 零售价购买价格 -->
          </view>
          <view wx:if="{{ labelList.length }}" class="activityPriceNew-label-list">
            <view wx:for="{{ labelList }}" wx:key="index" class="label-item">{{ item.content }}</view>
          </view>
        </view>

        <!-- 商品信息 -->
        <main-desc wx:if="{{ priceDisplayType !== priceDisplayTypeEnum.activityPrice_new }}" mainDesc="{{goodsObj.mainDescription}}"></main-desc>
        <view class='goods-info' style="{{goodsObj.mainDescription ? 'border-radius: 0 0 16rpx;' : 'padding-top: 16rpx'}}">
          <!-- 商品价格 -->
          <view class='goods-priceInfo' wx:if="{{ priceDisplayType === priceDisplayTypeEnum.activityPrice_operation || priceDisplayType === priceDisplayTypeEnum.normal }}">
            <goods-price goodsObj="{{goodsObj}}" customClassName="detail" direction="horizontal" showMinPriceTip="{{showMinPrice}}"></goods-price>
            <fruit-goods-price goodsObj="{{goodsObj}}" showMinPriceTip="{{showMinPrice}}" currentMode="GOODS_DETAIL" />
            <view class="label-list" wx:if="{{labelList.length}}">
              <label-list labelList="{{labelList}}" size="{{labelSize}}"></label-list>
            </view>
          </view>
          <view wx:if="{{goodsDetailfetched}}">
            <groupInviteTips custom-class="group-invite-tips" goodsObj="{{goodsObj}}" />
          </view>
          <!-- 商品标题，副标题 -->
          <view class='goods-title-wrap'>
            <view class='goods-title'>
              <view class="display-y-center">
                <goods-level class="goods-level" text="{{goodsObj.goodsLevel}}" size="middle"></goods-level>
                <text class="goods-name">{{ common.goodsLevelFormat(goodsObj.goodsName) }} {{common.goodsLevelFormat(showDesc ? goodsObj.specDesc : '')}}</text>
              </view>
              <view class='goods-subtitle' wx:if="{{goodsObj.subtitle}}">{{ goodsObj.subtitle }}</view>
            </view>
          </view>
          <view class='goods-service-tips' bindtap="showTips">
            <image class="goods-service-tips-label" src="https://resource.pagoda.com.cn/dsxcx/images/7d14af1bc5a2cd53026ee022e57ae463.png" mode="widthFix" />
            <view class='goods-service-tips-content'>
              <text wx:for="{{goodsObj.specialServiceList}}" wx:key="index"><text class="{{index === 0 ? 'no-point' : ''}}"> · </text>{{item.label}}</text>
            </view>
            <image class="service-tips-icon" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkBAMAAAATLoWrAAAAJFBMVEUAAAC5mYW6m4W7mYS6mYW6mYS7mYe6moW5mYW6moa5mYa5mYX1uPwfAAAAC3RSTlMA+Vxs5olsiN6JN5ZoSqgAAABCSURBVCjPY8ADmA0whAJFMRRJb0RXtmL3bnRlTNK7MZSpjBRlUCFMjYQVSWIqSsBQJD7YFK2AOBwzmWAmJsJJDgEAaA006aiPk9UAAAAASUVORK5CYII=" />
          </view>
          <view wx:if="{{goodsObj.goodsSn}}" class="goods-rank">
            <goods-rank goods-sn="{{goodsObj.goodsSn}}" scene="goodsDetail" />
          </view>
        </view>

        <!-- 配送、活动 -->
        <!-- 只有全国送才展示 -->
        <view class="goods-tips-group group-wrapper" wx:if="{{goodsObj.b2cDeliveryTime}}">
          <!-- 全国送商品展示配送提示 -->
          <view class="delivery-time tips-item" wx:if="{{goodsObj.b2cDeliveryTime}}">
            <view class="group-title">配送</view>
            <view class="delivery-time-tips">
              <view class="delivery-time-label">快递到家</view>
              <view class="delivery-time-desc">现在下单，预计{{goodsObj.b2cDeliveryTime}}</view>
            </view>
          </view>
          <!-- 活动 改为 限制 -->
          <view class="goods-activity tips-item" wx:if="{{goodsObj.activityList.length}}">
            <view class="goods-activity-title group-title">限制</view>
            <view class="goods-activity-content">
              <view class="goods-activity-item" wx:for="{{goodsObj.activityList}}" wx:key="index">
                <view class="goods-activity-desc">{{item.desc}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 规格选择 v5.0.0隐藏 -->
        <!-- <view wx:if="{{goodsObj.specificationGoodsList.length}}" class="sku-selector group-wrapper" catchtap="operateCartToShowChoiceLayer" data-goodsobj="{{goodsObj}}">
          <view class="flex">
            <text class="group-title">已选</text>
            <text class="sku-selector-content text-hidden-line2">{{skuLabel}}</text>
          </view>
          <image  class="arrow-right" src="/homeDelivery/source/images/icon_right_arrow_gary.png"></image>
        </view> -->

        <!-- 口感修改为营养成分 -->
        <!-- <view class="goods-taste">
          <goods-taste imgUrl="{{goodsObj.tasteImageList[0] || ''}}"></goods-taste>
        </view> -->

        <view class="goods-taste">
          <goods-nutrient src="{{goodsObj.tasteImageList[0] || '' }}"></goods-nutrient>
        </view>

        <!-- 达人试吃 -->
        <view class="group-wrapper">
          <tryEat-module id="tryEatModule" goodsID="{{goodsObj.eshopGoodsId}}"></tryEat-module>
        </view>

        <!-- 商品评价 -->
        <view class="evaluation-goodsdetail">
          <view wx:if="{{goodsCommentInfo}}" bindtap="navigateToEvaluationDetail" class="extra-evaluation group-wrapper group-extra-style">
            <view class="evaluation-summary">
              <text class="group-title">全部评价({{ allTotal }})</text>
              <view class="summary-right-wrap">
                <text class="check-all">查看全部</text>
                <view><image class="arrow" src="/homeDelivery/source/images/icon_right_arrow_gary.png"></image></view>
              </view>
            </view>
            <view class="ai-summarize">
              <ai-summarize
                content="{{goodsObj.aiSummary}}"
                ellipsis
                with-empty-padding="{{false}}"
                comment-total-info="{{goodsCommentTotalInfo}}"
                bind:tapTag="onAiSummarizeTagTap">
              </ai-summarize>
            </view>
            <view class="goods-evaluation-item {{goodsObj.aiSummary ? '' : 'goods-evaluation-item-no-ai'}}">
              <evaluation-item comment-info="{{goodsCommentInfo}}" />
            </view>
          </view>
        </view>

        <!-- 水果百科 -->
        <fruit-wiki wx:if="{{goodsObj.bannerObj}}" banner-obj="{{goodsObj.bannerObj}}" />

        <!-- 商品详情信息 -->
        <view class="group-wrapper goods-detail-info goods-detail-info--simple">
          <view class='group-title'>商品信息</view>
          <view class='goods-detail'>
            <view class='detail-info'>
              <!-- 有质检报告&&标准份才展示 -->
              <view class='detail' wx:if="{{detectionReportFileList.length&&goodsObj.saleType === 1}}">
                <view class='detail-1'>质检</view>
                <view class='detail-2' bindtap="toH5page">
                  <view class="inspection-report">
                    <image class="report-icon" src="../../source/images/tips_aq.png" mode="widthFix"></image>
                    <view class="report-font">全面检测</view>
                    <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' class='report-right' />
                  </view>
                </view>
              </view>
              <view class='detail' wx:if="{{goodsObj.placeOrigin}}">
                <view class='detail-1'>产地</view>
                <view class='detail-2'>{{goodsObj.placeOrigin}}</view>
              </view>
              <view class='detail' wx:if="{{goodsObj.specDesc}}">
                <view class='detail-1'>规格</view>
                <view class='detail-2'>{{common.goodsLevelFormat(goodsObj.specDesc)}}</view>
              </view>
              <view class='detail' wx:if="{{goodsObj.dataDesc}}">
                <view class='detail-1'>数据说明</view>
                <view class='detail-2'>{{goodsObj.dataDesc}}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 商品详情信息 -->
        <view class="group-wrapper goods-detail-info goods-detail-info--more">
          <view class='group-title'>商品详情</view>
          <!-- 图片展示 -->
          <view class='c-img-box'>
            <view class='pic-w' wx:key="index" wx:for="{{goodsObj.detailPicList}}">
              <image class='icon-detailImg' src='{{common.getImageUrl(picUrl,item)}}' mode='widthFix' lazy-load="true"></image>
            </view>
          </view>
          <!-- 品牌墙 -->
          <view class="brandWall-box">
            <!-- <image src="https://resource.pagoda.com.cn/dsxcx/images/3e9389db8ae1a3f41116e03c38783f11.jpg?width=718&height=820" class="brandWall-pic" mode='widthFix'/>
            <image src="https://resource.pagoda.com.cn/dsxcx/images/922fc06848e1a4c9638527e2bccf9d0c.jpg?width=718&height=820" class="brandWall-pic" mode='widthFix'/>
            <image src="https://resource.pagoda.com.cn/dsxcx/images/cbf69d55f208764bd2cdfaf89efe13c6.jpg?width=718&height=820" class="brandWall-pic" mode='widthFix'/> -->
            <image src="https://resource.pagoda.com.cn/dsxcx/images/c15d9fea4894e08bf169b9dd497ae197.jpg?width=750&height=958" class="brandWall-pic" mode='widthFix'/>
          </view>
          <!-- 划线价格提示 -->
          <view class="price-tips-box" wx:if="{{goodsObj.priceDescription}}">
            <view class="price-tips-title">价格提示</view>
            <view class="price-tips-content">{{goodsObj.priceDescription}}</view>
          </view>
        </view>
        <!-- 留空的节点 为了兼容底部内容自然撑开 -->
        <view class="gap"></view>
      </view>
    </scroll-view>
    <block>
      <view class="b2c-delivery-box" wx:if="{{goodsObj.b2cUnableDelivery}}">
        <view>商品不支持配送至当前地址</view>
        <view class="change-address-btn" catchtap="changeAddress">更换地址</view>
      </view>
      <!-- 底部按钮: 2.2改版-->
      <view class="bottom-navbar {{visibleCouponPop ? 'hide' : ''}}">
        <!-- 分享按钮 -->
        <view class="bottom-navbar-share">
          <image class="navbar-share-icon" catchtap="showModel" data-type="share" src="/homeDelivery/source/images/home_button_Share_change.png" />
          <view class="navbar-share-text">分享</view>
        </view>
        <!-- 购物车按钮 -->
        <view class="bottom-navbar-cartcount" catchtap="toGoodsCart">
          <view wx:if="{{cartCount > 0}}" class="navbar-cart-count {{cartCount > 99 ? 'bigger-cart-count' : ''}}" style="{{cartCount>10 ?'right:-8rpx;':''}}">{{cartCount > 99 ? '99+' : cartCount}}</view>
          <!-- suspension-cart为需要计算位置的购物车类名，所以请勿修改 -->
          <image class="navbar-cartcount-icon suspension-cart" src="/homeDelivery/source/images/home_button_shoppingcart.png" />
          <view class="navbar-cartcount-text">购物车</view>
        </view>
        <!-- 加入购物车按钮 -->
        <view class="bottom-navbar-addcart" wx:if="{{haveGoodsInfo && goodsDetailfetched}}" style="pointer-event: {{goodsDetailfetched ? 'auto' : 'none'}}">
          <!--不支持B2C-->
          <view wx:if="{{goodsObj.b2cUnableDelivery}}" class="add-to-cart bottom-navbar-notadd">
            加入购物车
          </view>
          <operate-cart
            wx:else
            style="{{ priceDisplayType === priceDisplayTypeEnum.activityPrice_new ? '--background-image: linear-gradient(92deg, #FF4545 6%, #FF7A27 98%);' : '' }}"
            goodsCount="{{goodsCountObj[goodsObj.goodsSn].count}}"
            initStyle="{{goodsCountObj[goodsObj.goodsSn].count ? 'subAndAdd' : 'button'}}"
            goodsObj="{{cloneGoodsObj}}"
            showTipAndNoAdd="{{true}}"
            fromShareTimeLine="{{fromShareTimeLine}}"
            bind:updateCount="updateCartCount"
            bind:startAnimation="startAnimation"
            bind:operateCartToShowChoiceLayer="operateCartToShowChoiceLayer"
            addCartBtnSensorsKey="goodsDetailAddCartBtn"
            addSensorskey="goodsDetailAddCart"
            subSensorskey="goodsDetailSubCart" >
          </operate-cart>
        </view>
      </view>
    </block>
  </view>


</main-page>
<!-- 分享选项框 -->
<view wx:if="{{isShare}}" class="bounce-model" catchtouchmove="preventTouchMove">
  <view class="model_bottom" data-type="share" catchtap='closeModel'></view>
  <view class="share_selectBox" animation="{{animationShare}}">
    <view class="shareBox">
      <view class="shareIcon">
        <view class="icon_group wx_friend" >
          <view class="btn_wrapper">
            <view class="wx_friend_loading" wx:if="{{!canUseSharePic}}"><image src="/source/images/share_loading.png" /></view>
            <button open-type="share" class="btn_none"><image src="/source/images/icon_tencent_wechat.png"></image></button>
          </view>
          <view class="share-txt">分享给好友</view>
        </view>
        <view class="icon_group wx_moment" data-type="thumbnail" catchtap='showModel'>
          <!-- <view class="wx_friend_loading" wx:if="{{!canUseSharePic}}"><image src="/source/images/share_loading.png" /></view> -->
          <image src="/source/images/icon_tencent_moments.png"></image>
          <view class="share-txt">发海报</view>
        </view>
      </view>
    </view>
    <view class="deleteBtn safe-area-inset-bottom" data-type="share" catchtap='closeModel'>取消</view>
  </view>
</view>
<!-- 海报模态框 -->
<view wx:if="{{isThumbnail}}" class="poster-model" catchtouchmove="preventTouchMove">
  <view class="model_top" data-type="thumbnail" catchtap='closeModel'></view>
  <view class="model-body">
    <view class="container">
      <view class="top-box">
        <image class="close_btn" src="/source/images/layer_btn_close.png" data-type="thumbnail" catchtap='closeModel'></image>
        <image class="thumbnail" src="{{shareImage}}" style="width:{{smallPosterWidth}}rpx;height:{{smallPosterHeight}}rpx;"></image>
      </view>
      <view>
        <view class="save_photo" bindtap='savePoster'>保存图片</view>
        <text class="share-tips">分享海报时可到相册选取图片</text>
      </view>
    </view>
  </view>
</view>
<!--canvas 绘图-->
<canvas canvas-id="posterCanvas" class="myCanvas" style="width: 750px; height: 1380px;"></canvas>
<canvas id="picCanvas" type="2d" class="myCanvas" style="width: 992px; height: 794px;"></canvas>
<canvas id="QRCodeCanvas" type="2d" class="myCanvas" style="width: 400px; height: 400px;"></canvas>
<!-- <template is="add-to-cart-animation" data="{{...addToCartData}}"></template> -->
<!--cart-animation-ball id="goodsCartAnimationBall"></cart-animation-ball -->
<!-- sku加购弹层 -->
<add-goods-layer
  showLayer="{{showLayer}}"
  goodsInfo="{{goodsInfo}}"
  defaultGoodsSn="{{goodsInfo.goodsSn}}"
  bind:updateCount="updateCartCount"
  bind:switchSpec="handleSwitchSpec"
  bind:switchService="hanldeSwitchService"
  bind:layerTrigger="handleLayerTrigger"
  shareGoods="{{fromShareDetail}}"
></add-goods-layer>
<!-- 服务弹窗 -->
<service-bar isShowPopup="{{isShowServiceBar}}" serviceDescList="{{goodsObj.specialServiceList}}" bind:hideServicePopup="hideTips"></service-bar>

<share-card-generator 
  id="shareCardGenerator"
  class="myCanvas"
  bizType="{{ goodsObj.takeawayAttr === 'B2C' ? 'b2c' : 'delivery' }}" 
  priceType="{{ priceType }}" 
  goodsInfo="{{ goodsObj }}"
  customBgImg="{{ customBgImg }}"
  bind:generated="onShareCardGenerated">
</share-card-generator>

<confirm-modal id="globalModal" globalModalInfo="{{globalLocateModalInfo}}"></confirm-modal>

<common-loading />
