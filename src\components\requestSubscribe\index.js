import { getPromiseObj } from "../../utils/promise"

const getSubscriptionsSetting = async () => {
  const { subscriptionsSetting = {} } = await new Promise(complete => wx.getSetting({ withSubscriptions: true, complete }))
  const { mainSwitch = false, itemSettings = {} } = subscriptionsSetting
  return { mainSwitch, itemSettings }
}

Component({
  options: {
    styleIsolation: 'apply-shared',
    multipleSlots: true,
    pureDataPattern: /^_/
  },
  properties: {
    title: {
      type: String,
      value: '打开提醒，获得优惠券过期提醒'
    },
    tmplIds: {
      type: Array,
      value: []
    },
    show: {
      type: Boolean,
      value: false
    },
    customBg: {
      type: Boolean,
      value: false
    },
    customNav: {
      type: Boolean,
      value: false,
    },
    withTabBar: {
      type: Boolean,
      value: false
    }
  },
  observers: {
    show(show) {
      show && this.requestSubscribeMessage()
    }
  },
  data: {
    maskShow: false,
    // 需要显示的模板id,可能有的模板id已经不再提示了
    showTmplIds: [],
    _showResolve: null
  },
  lifetimes: {
    created() {
      this._data = {
        cacheByPageShow: false,
        subscriptionsSettingCache: null,
      }
    },
    attached() {
      if (this._data.cacheByPageShow) {
        return
      }
      this._data.subscriptionsSettingCache = getSubscriptionsSetting()
    }
  },
  pageLifetimes: {
    show() {
      this._data.cacheByPageShow = true
      this._data.subscriptionsSettingCache = getSubscriptionsSetting()
    }
  },
  methods: {
    noop() {},
    async close({ showTabbar, detail = { acceptAll: false, rejectAll: true, resultStatus: {}, selectNeverAskAgain: false } } = {}) {
      this.setData({ maskShow: false })
      showTabbar && await new Promise(complete => wx.showTabBar({ complete })) // 恢复tabbar显示
      this.triggerEvent('close', { show: false, ...detail })
      const { _showResolve } = this.data
      if (_showResolve) _showResolve({ show: false, ...detail })
    },
    async getSubscriptionsSetting() {
      const { mainSwitch, itemSettings } = await (this._data.subscriptionsSettingCache || getSubscriptionsSetting())
      const tmplIds = this.data.tmplIds
      const showTmplIds = tmplIds.filter(id => !(id in itemSettings))
      return { mainSwitch, showTmplIds, itemSettings }
    },
    /**
     * 获取订阅结果
     * @param { Object } options
     * @param { boolean } options.mainSwitch 订阅消息总开关
     * @param { Record<string, 'accept'| 'reject'| 'ban'| 'filter'> } options.result 本次发起订阅消息的结果
     * @param { Record<string, 'accept' | 'reject' | 'ban'> } options.itemSettings 获取到设置中的结果
     */
    getSubscribeResult({ mainSwitch, result, itemSettings }) {
      const tmplIds = this.data.tmplIds
      const resultStatus = tmplIds.reduce((status, id) => {
        status[id] = result[id] === 'accept'
        return status
      }, {})
      return mainSwitch ? {
        // 所有都同意
        acceptAll: !tmplIds.find(id => !resultStatus[id]),
        // 所有都拒绝
        rejectAll: !tmplIds.find(id => resultStatus[id]),
        // 是否勾选了不再询问(如果有某个id不在itemSettings里,说明没勾选不再询问)
        selectNeverAskAgain: !tmplIds.some(id => !(id in itemSettings)),
        resultStatus
      } : { acceptAll: false, rejectAll: true, resultStatus, selectNeverAskAgain: false }
    },
    requestSubscribeMessage() {
      let maskShow = true
      const maskShowPromise = this.getSubscriptionsSetting().then(({ mainSwitch, showTmplIds, itemSettings }) => {
        // 如果订阅消息都在"不再提醒"里,就不显示遮罩
        if (!(maskShow = maskShow && mainSwitch && showTmplIds.length)) {
          // 不显示遮罩的情况下,直接执行this.close()结束流程即可
          // 无需关心后续的wx.requestSubscribeMessage流程
          this.close({
            detail: this.getSubscribeResult({
              mainSwitch,
              itemSettings,
              // 这里直接使用itemSettings模拟wx.requestSubscribeMessage的结果
              // 可能存在部分模板消息被ban/filter,但是getSetting结果未及时更新的情况
              result: itemSettings
            })
          })
        } else {
          setTimeout(async () => {
            maskShow && this.data.withTabBar && await new Promise(complete => wx.hideTabBar({ complete }))
            this.setData({ maskShow, showTmplIds })
          }, 300) // 默认300ms后弹出授权弹窗
        }
        return maskShow
      })
      // 在这写wx.requestSubscribeMessage,
      // 是因为微信限制了wx.requestSubscribeMessage一定需要在同步流程里执行
      // 否则无法正常发起订阅授权
      wx.requestSubscribeMessage({
        tmplIds: this.data.tmplIds,
        // 未弹出成功授权回调
        fail: (reason) => {
          console.log('fail', reason);
          maskShowPromise.then((maskShow) => {
            maskShow && this.close()
          })
        },
        // 授权结果回调
        success: async (result) => {
          maskShowPromise.then((maskShow) => {
            // 如果maskShow是false,说明之前getSubscriptionsSetting的时候,已经结束了流程
            // 无需执行this.requestSubscribeResult
            maskShow && this.requestSubscribeResult(result)
          })
        },
        complete() {
          // 已经完成requestSubscribe流程
          // 还没弹出提示就不弹了
          // 主要用在fail和不再提示的情况
          maskShow = false
        }
      })
    },
    async requestSubscribeResult(result) {
      // 尽快关闭遮罩
      this.setData({ maskShow: false })
      // 检查是否勾选不再询问
      const getSetting = getSubscriptionsSetting()
      this._data.subscriptionsSettingCache = getSetting
      const { mainSwitch, itemSettings } = await getSetting
      this.close({
        detail: this.getSubscribeResult({ mainSwitch, result, itemSettings }),
        showTabbar: this.data.withTabBar
      })
    },
    /**
     * promise 返回值与 triggerEvent close 保持一致
     */
    showPromise() {
      const { resolve, promise } = getPromiseObj()
      this.setData({
        _showResolve: resolve
      })
      this.requestSubscribeMessage()
      return promise
    }
  }
})
