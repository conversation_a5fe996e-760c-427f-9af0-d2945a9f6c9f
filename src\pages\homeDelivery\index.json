{"navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "white", "onReachBottomDistance": 200, "usingComponents": {"common-goods": "../../components/goods/commonGoods/index", "main-page": "../../components/base/main-page/index", "order-bar": "../../components/orderBar/index", "add-goods-layer": "../../componentsSubPackage/goods/addGoodsLayer/index", "metro-list": "./components/metro/index", "relative-nav-top": "./components/relativeNavTop/index", "fix-nav-top": "./components/fixNavTop/index", "waterfall-goods": "./components/waterfallGoods/index", "new-user-coupon": "./components/newUserCoupon/index", "floating": "./components/floating/index", "new-customer-layer": "../../components/newCustomerLayer/index", "page-dialog": "./components/pageDialog/index", "confirm-modal": "../../components/confirmModal/index", "search-bar": "./components/searchBar/index", "fresh-entry": "./components/fresh-entry/index", "relay-entry": "./components/relay-entry/index", "no-store": "../../components/base/no-store/index", "new-user-goods": "../../componentsSubPackage/homeDelivery/newUserGoods/index"}, "navigationStyle": "custom", "componentPlaceholder": {"new-user-goods": "view", "add-goods-layer": "view"}}