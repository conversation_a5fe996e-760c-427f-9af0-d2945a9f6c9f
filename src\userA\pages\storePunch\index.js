let commonObj = require('../../../source/js/common').commonObj;
let groupImg = require('../../source/image-base64/group_image').bg;
let coordtransform = require('../../../utils/coordUtil.js');  //定位引入
import wxappMap from '../../../service/wxappMap'
import { ORDER_TYPE } from '../../../source/const/order';
import { getAllInfoFruitGoodsList } from '../../../utils/goods/goodsData';
let app = getApp();

const cartMinxins = require('../../../mixins/cartMixin')
const sensors = require('../../../utils/report/sensors')
const locateService = require('../../../utils/services/locate')

Page({
  mixins: [cartMinxins],

  /**
   * 页面的初始数据
   */
  data: {
    ORDER_TYPE,
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    groupImg: groupImg,
    hasStore: false, //有无门店
    isShowCart: false,
    isShowList: false,
    isClearShop: false,
    isIphoneX: '',
    cartFlag: false,
    superVipStatus: app.globalData.superVipStatus,
  },

  onLoad: function(options){
    this.setData({opts: options})
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    const {cityID = -1, cityName = '', storeID = 0, storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
    this.setData({storeID : storeID || -1})
    if (app.checkSignInsStatus()) {
      let hasPunch = this.data.opts.isPunch === 'no' ? false : true
      let customerID = wx.getStorageSync('user').userID;
      let { nickName: userName } = wx.getStorageSync('userNameAndImg') || {}
      this.setData({
        customerID,
        userName,
        cityID,
        isIphoneX: app.globalData.isIphoneX,
      })
      if (hasPunch){
        this.getStorePunchInfo()
      }else{
        this.nearbyStoreLoc()
      }
    } else {
      app.signIn()
    }
    // 浏览页面上报神策
    sensors.pageScreenView()
  },

  // 获取当前位置
  async nearbyStoreLoc(){
    try {
      const { lat, lon } = app.globalData.curLocation || {}
      if (lat && lon) {
        this.toReverseGeocoder(lat, lon)
        return
      }
      const locationInfo = await locateService.getGeoLocation()
      this.toReverseGeocoder(locationInfo.latitude, locationInfo.longitude)
    } catch(err) {
      this.setData({userLocateClose:true})
      locateService.handleWXLocationErr(err.errMsg)
    }
  },
  async toReverseGeocoder({
    lat,
    lon
  }) {
    const res = await wxappMap.reverseGeocoder({
      lat,
      lon
    })
    if (res && res.status === 0 && res.result) {
      let ad_location = res.result.location || {};
      let storeLocation = coordtransform.gcj02tobd09(ad_location.lng, ad_location.lat);
      this.getStorePunchInfo(storeLocation)
    }
  },
  // 门店打卡信息
  getStorePunchInfo(location){
    if(this.data.cityID !== 0 && !this.data.cityID){
      return
    }
    this.getStoreGoods()
    this.getPunchInfo(location || [])
  },

  /**
   * 获取门店商品
   */
  async getStoreGoods() {
    try {
      const { cityCode, storeCode, deliveryCenterCode } = wx.getStorageSync('timelyCity') || {}
      if (!storeCode) {
        return
      }
      const params = {
        pageNum: 1,
        pageSize: 10,
        deliveryCenterCode,
        storeCode,
      }

      const res = await app.api.getStoreGoods(params)
      const goodsSimpleList = res.data

      const { goodsList, soldOutGoodsList } = await getAllInfoFruitGoodsList({
        goodsSimpleList,
        storeCode,
        cityCode,
        deliveryCenterCode
      })
      const allGoodsList = [...goodsList, ...soldOutGoodsList]
      const maxLen = allGoodsList.length <= 3 ? 2 : 4
      this.setData({
        goodsList: allGoodsList.slice(0, maxLen)
      })
    } catch (error) {}
  },

  getPunchInfo(lnglat) {
    const that = this
    app.api.getStorePunchInfo({
      customerID: that.data.customerID,
      cityID: that.data.cityID,
      lon: lnglat[0],
      lat: lnglat[1],
      storeID: that.data.storeID,
    }).then((res) => {
      let storeID
      let {
        storeRewardIntegral,
        storeSignInStatus,
        isAllowSignIn,
        currentDistance,
        todaySignInStore,
        nearbyStore,
        lessKilo = true
      } = res.data
      if (currentDistance >= 1000) {
        lessKilo = false;
        currentDistance = parseFloat(currentDistance / 1000).toFixed(1)
      }
      if (todaySignInStore) {
        storeID = todaySignInStore.storeID
      } else if (nearbyStore) {
        storeID = nearbyStore.storeID
      }
      that.setData({
        isLoading: false,
        storeRewardIntegral, // 门店签到奖励积分
        storeSignInStatus, // 门店签到状态 0未签到 1已签到
        isAllowSignIn, // 当前位置是否允许签到 Y：允许  N：不允许
        currentDistance, // 距您当前的距离， 单位: 米
        todaySignInStore: todaySignInStore || {}, // 今日签到门店实体，为空则说明今日未签到
        nearbyStore: nearbyStore || {}, // 附近门店
        lessKilo, //小于1000米
        storeID
      })
    })
  },

  // 门店打卡
  punchCard (){
    let that = this;
    if(that.data.isPunchLoading) return;
    that.setData({ isPunchLoading: true })
    app.api.goPunchCard(that.data.customerID, that.data.nearbyStore.storeID).then((res) => {
      let data = res.data
      that.setData({
        isPunchLoading: false,
        storeCode: data,
        showAwardPop: true
      })
      that.getStorePunchInfo()
      if (app.globalData.reportSensors) {
        app.sensors.track("MPClick", {
          element_code: 121902001,
          element_name: '门店打卡',
          element_content: '门店打卡',
          screen_code: '1219',
        })
      }
    }).catch((res) => {
      that.setData({
        isPunchLoading: false
      })
      wx.showToast({
        title: res.errorMsg,
        icon: 'none',
        duration: 2000
      })
    })
  },

  // 关闭弹窗
  closeModelPop (e){
    let that = this;
    let type = e.currentTarget.dataset.type;
    switch (type) {
      case 'storeAccept':
        that.setData({ showAwardPop: false})
        break;
      case 'openLocate':  // 未开户门店定位
        that.setData({
          userLocateClose: false,
        })
        break;
    }
  },

  // 跳转到及时达首页
  skipToDelivery() {
    wx.switchTab({
      url: '/pages/homeDelivery/index',
    })
  },

  //跳转地图定位页面
  toStoreMapPage: function (e) {
    let that = this
    let nearbyStore = that.data.nearbyStore
    let x = coordtransform.bd09togcj02(parseFloat(nearbyStore.lon), parseFloat(nearbyStore.lat))
    wx.openLocation({
      latitude: x[1],
      longitude: x[0],
      name: nearbyStore.storeName,
    })
    if (app.globalData.reportSensors) {
      app.sensors.track("MPClick", {
        element_code: 121902002,
        element_name: '门店导航',
        element_content: '门店导航',
        screen_code: '1219',
      })
    }
  },
  // 空白函数 禁止滚动
  preventTouchMove (e) {
  },
  eventprevent() {
    // console.log("mask 显示隐藏事件，阻止冒泡这个空方法不能删")
  },
  // 保存 formID
  submitFormId(e) {}
})
