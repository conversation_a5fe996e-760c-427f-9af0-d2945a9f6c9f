import { getPathName } from '~/utils/file'
import COSHelper from '~/utils/services/COSHelper'
import sensors from '../../../../utils/report/sensors'
const app = getApp()
const util = require('../../../../utils/util')
const contactMixin = require('../common/contactMixin')

/**@type COSHelper | null */
let cosHelperDSKHD = null

function instanceCOSHelper() {
  const { userID: customerID } = wx.getStorageSync('user') || {}
  return new COSHelper({
    authSource: 'dskhd',
    cosType: 'realNameChange',
    params: {
      customerID
    }
  })
}

Page({
  mixins: [contactMixin],
  /**
   * 页面的初始数据
   */
  data: {
    // 实名验证状态，VALID: 生效，FAIL: 无效
    validStatus: '',
    // 实名认证状态，"FINISH": 认证完成，"ERROR": 认证失败
    verifyStatus: '',
    // 每日实名认证失败的限制次数
    failLimit: 3,
    // 每日实名认证失败次数
    failCount: 0,
    // 客服填写认证失败的原因
    verifyResult: '',
    // 页面类型 result/bound/binding/otherBound/otherBinding
    // result: 通过，bound: 身份证被认证，binding: 未认证，在绑定审核中或者绑定审核失败的结果
    // 分别是什么意思？ result: 当前身份证没有被认证，返回的结果显示，有可能成功，有可能失败（侧重一次性认证）
    // binding: 当前账号申请换绑中或者申请失败（侧重当显示前登录账号）根据reviewStatus来区分具体是换绑中(W)还是失败(R)
    // bound: 当前账号申请换绑成功（侧重显示当前登录账号的换绑成功信息）
    // otherBound: 这个身份证号被其他号绑了（侧重显示其他账号）
    // otherBinding: 这个身份证号被其他账号换绑申请中（侧重显示其他账号）
    pageType: 'result',
    // 当前认证账号
    currentPhoneNumber: '',
    // 已被认证账号
    certifiedPhoneNumber: '',
    // 如果是换绑 则会使用这些字段
    // WAITING("W", "待审核"),
    // APPROVED("A", "审核通过"),
    // REJECTED("R", "审核驳回")
    reviewStatus: '',
    // 换绑驳回原因
    reviewNote: '',
    // 审核接口返回的脱敏身份证号
    identityCardNum: '',
    // 审核返回的脱敏姓名
    realName: '',
    // 审核返回的附件获取的base64
    base64Attachments: [],
    // 是否有错误
    hasError: false
  },
  _data: {
    // 用户上个页面手动填写的姓名
    name: '',
    // 用户上个页面手动填写的身份证号
    identityCardNum: '',
    // 返回重新申请按钮是否替换当前路由wx.redirectTo来跳转
    isDirectBack: false,
    // 是否通过当前页面跳转到换绑页并提交审核中了，目的是为了后续返回的时候能多返回一层，毕竟申请页不需要了
    isChecking: false
  },
  onShow() {
    // 浏览页面上报神策
    sensors.pageScreenView()
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function (options) {
    // 如果是来自消息推送
    if (options.fromSubscribe) {
      const { memberId, reviewStatus, reviewNote = '' } = options
      const { userID: customerID } = wx.getStorageSync('user') || {}
      // 如果推送的不是当前登录的id
      if (memberId !== customerID) {
        wx.showModal({
          title: '提示',
          content: '非当前登录用户的消息，请切换登录用户查看审核结果',
          showCancel: false,
          confirmText: '我知道了',
          success: function () {
            wx.switchTab({
              url: '/pages/homeDelivery/index',
            })
          },
        })
        this.setData({
          reviewStatus: 'W',
          hasError: true
        })
        return
      }
      this.setData({
        pageType: reviewStatus === 'A' ? 'bound' : 'binding',
        reviewStatus,
        reviewNote
      })
      this.getVerifyChangeRecord()
      return
    }
    // 如果是来自页面传参
    if (options.params) {
      const params = JSON.parse(decodeURIComponent(options.params)) || {}
      const { pageType = 'result', validStatus, verifyStatus, reviewStatus = '', failCount = 0, failLimit = 0, verifyResult = '', readIdentityCardNum = 0, name, isDirectBack = false } = params
      this.setData({
        validStatus,
        verifyStatus,
        failCount,
        failLimit,
        verifyResult,
        pageType,
        name,
        reviewStatus
      })
      this._data.name = name
      let identityCardNum = ''
      if (readIdentityCardNum) {
        // 从上一页面获取值
        const eventChannel = this.getOpenerEventChannel()
        identityCardNum = await new Promise((resolve) => {
          eventChannel.once('getIDCardNumber', ({ IDCardNumber }) => {
            resolve(IDCardNumber)
          })
        })
      }
      this._data.identityCardNum = identityCardNum
      this._data.isDirectBack = isDirectBack
      // 如果不是认证结果 需要查询审核结果
      if (pageType !== 'result') {
        // 如果是换绑 需要手动查询已被认证账号
        if (identityCardNum) {
          this.setPhoneNumber(identityCardNum)
        }
        // 如果审核中 则需要展示审核情况
        if (pageType === 'binding') {
          this.getVerifyChangeRecord()
        }
      }
    }
  },
  /**
   * 根据输入的身份证号码查询已被认证的脱敏账号
   * @param {string} identityCardNum 身份证号
   */
  async setPhoneNumber(identityCardNum) {
    // 当前认证账号
    const { phoneNumber } = wx.getStorageSync('user') || {}
    const currentPhoneNumber = phoneNumber ? (phoneNumber.substring(0, 3) + '****' + phoneNumber.substring(7, 11)) : ''
    // 根据身份证号查询已被认证账号
    const { data } = await app.api.getVerifyByIdentityCardNum({
      identityCardNum: util.base64Encode(identityCardNum)
    })
    // 变更数据
    const changeData = {
      currentPhoneNumber
    }
    // 如果查到当前身份证号对应的脱敏手机号
    if (data.identity) {
      changeData.certifiedPhoneNumber = data.identity
    } else {
      changeData.certifiedPhoneNumber = '***********'
    }
    this.setData(changeData)
  },
  /**
   * 继续退款按钮
   */
  continueAction() {
    this.navBack()
    sensors.track('Click', '11607_1160701001')
  },
  /**
   * 更换实名信息
   */
  changeVerify() {
    this.navBack()
    sensors.track('Click', '11607_1160702003')
  },
  /**
   * 重新认证
   */
  reVerify() {
    this.navBack()
    sensors.track('Click', '11607_1160702004')
  },
  /**
   * 返回上一页
   */
  navBack() {
    // 有可能是通过三无退申请页直接进入的 这里需要判断是直接返回还是redirect
    if (this._data.isDirectBack) {
      wx.redirectTo({
        url: '/userB/pages/certification/applyCertification/index?storePhone=' + this.data.storePhone
      })
      return
    }
    // 否则直接返回
    // 如果是换绑了，审核中，此时返回2级
    const delta = this._data.isChecking ? 2 : 1
    wx.navigateBack({
      delta
    })
    // 如果存在触发事件 则使用
    const eventChannel = this.getOpenerEventChannel()
    if (eventChannel && eventChannel.emit) {
      eventChannel.emit('navBack')
      // 如果是需要返回2层 则通过上一页面自动返回
      if (delta === 2) {
        eventChannel.emit('autoClose')
      }
    }
  },
  /**
   * 审核拒绝后重新提交
   */
  redirectApply() {
    wx.redirectTo({
      url: '/userB/pages/certification/applyCertification/index?storePhone=' + this.data.storePhone
    })
    sensors.track('Click', '11607_1160704002')
  },
  /**
   * 换绑
   */
  changeBind() {
    const params = JSON.stringify({
      name: this._data.name,
      readIdentityCardNum: 1,
      storePhone: this.data.storePhone,
      currentPhoneNumber: this.data.currentPhoneNumber,
      certifiedPhoneNumber: this.data.certifiedPhoneNumber,
    })
    const nextPagePromise = wx.navigateTo({
      url: '/userB/pages/certification/applyChangeBind/index?params=' + params,
      events: {
        /**
         * 换绑页可能会提交换绑申请，如果提交成功了，返回到当前页面并刷新为审核中/审核完成的状态
         */
        reload: () => {
          this.setData({
            pageType: 'binding',
            reviewStatus: 'W'
          })
          // 表示换绑申请已经提交，需要等待审核，返回时会返回2层
          this._data.isChecking = true
          this.getVerifyChangeRecord()
          // 如果此时用于返回上一页，需要触发事件让上一页自动关闭
          const eventChannel = this.getOpenerEventChannel()
          if (eventChannel && eventChannel.emit) {
            eventChannel.emit('autoClose')
          }
        }
      }
    })
    /**
     * 获取当前页面输入的身份证号
     * 因为通过地址栏传参会导致身份证号通过神策上报，为了保护用户隐私，不传输，这里仅在本地获取
     */
    nextPagePromise.then(nextPage => nextPage.eventChannel.emit('getIDCardNumber', { IDCardNumber: this._data.identityCardNum }))
    // 申请换绑埋点
    sensors.track('Click', '11607_1160702002')
  },
  /**
   * 获取换绑记录并显示在页面
   */
  async getVerifyChangeRecord() {
    const { userID: customerID } = wx.getStorageSync('user') || {}
    if (!customerID) {
      app.signIn()
      return
    }
    const { data } = await app.api.getVerifyChangeRecord({
      customerID
    }).catch(err => {
      console.log('err', err)
      return { data: null }
    })
    if (!data) {
      wx.showToast({
        title: '暂无换绑记录，请稍后再试',
        icon: 'none'
      })
      this.setData({
        hasError: true
      })
      return
    }
    const { reviewStatus, reviewNote, identity: currentPhoneNumber, oldIdentity: certifiedPhoneNumber, identityCardNum, realName, attachments } = data
    this.setData({
      reviewStatus,
      reviewNote,
      currentPhoneNumber,
      certifiedPhoneNumber,
      identityCardNum,
      realName,
      // 审核通过A
      pageType: reviewStatus === 'A' ? 'bound' : 'binding',
    })
    // 根据附件获取base64
    if (!attachments || !attachments.length) {
      return
    }
    // 上传到COS
    if (!cosHelperDSKHD) {
      cosHelperDSKHD = instanceCOSHelper()
    }
    const res = await Promise.all(attachments.map(item => {
      const key = getPathName(item)
      return cosHelperDSKHD.getObject(key)
    })).catch(err => {
      wx.showToast({
        title: '资源下载不成功，请检查网络后重试',
        icon: 'none'
      })
      cosHelperDSKHD = null
      console.error(err)
      return []
    })
    // 图片前缀
    const PREFIX = 'data:image/png;base64,'
    this.setData({
      // 截掉前48位
      base64Attachments: res.map(item => PREFIX + item.Body.slice(42))
    })
  },
  /**
   * 查看大图
   */
  previewImage(e) {
    const { index } = e.currentTarget.dataset
    wx.previewImage({
      current: this.data.base64Attachments[index],
      urls: this.data.base64Attachments
    })
  },
  /**
   * 页面关闭时销毁私有读取对象
   */
  onUnload() {
    if (cosHelperDSKHD) {
      cosHelperDSKHD = null
    }
  }
})
