import storeBindings from '../../store/storeBindings'
import { baseUrl } from '../../../../../utils/config'
import ShareCard from './shareCard'
import { relayCartStore } from '../../../home/<USER>/inedx'
import { sleep } from '~/utils/time'
import { toJS } from 'mobx-miniprogram'
import { relayAddSuccessReport } from '../../../../../utils/report/getSaleReportInfo'
const { relayGoodsMap, relayScreenMap } = require('../../../../sensorReportData')
const { handlePickupTime } = require('../../../common/orderGoods')
const sensors = require('../../../../../utils/report/sensors')
const app = getApp()
const SCROLL_HEAD_SELECTOR = '#goods-detail-head'
const SCROLL_VIEW_SELECTOR = '#goods-detail-scroll'

Component({
  options: {
    styleIsolation: 'apply-shared'
  },
  behaviors: [storeBindings({
    fields: {
      cartNamespace: store => store.cartNamespace,
      useCacheSaleCount: store => store.detail.useCacheSaleCount,
      hasShareMenu: store => store.hasShareMenu,
      notStartStatus: store => store.isNotStartStatus,
      pageOptions: store => store.options,
      show: store => store.goodsDetail.show,
      activityDetail: store => store.detail,
      // 自提时间字符串
      pickupTimeStr: store => { 
        const { pickupEnd, pickupStart } = store.detail
        return handlePickupTime(pickupStart, pickupEnd)
      },
      goodsPrice: ({ goodsDetail: { goodsCode }, code2Price }) => code2Price[goodsCode] || {},
      // 还没有goodsCode的时候,会找不到详情(下面两个都是)
      // 活动商品项
      goodsItem: ({ goodsDetail: { goodsCode }, code2Goods }) => code2Goods[goodsCode] || {},
      // 活动商品项对应的商品详情
      goodsDetail: ({ goodsDetail: { goodsCode }, code2Detail }) => code2Detail[goodsCode] || {},
      showStepper: store => store.isVaildStatus
    },
    actions: [
      'updateCart',
      'getGoodsHistory',
      'getCartNum',
      'getDisableStatus',
      'getState',
      'sensorsBaseData',
      'sensorsTrackShow',
      'startWaitCountDown',
      'checkFromShareTimeLine',
      'checkWithStore',
      'checkShareGoodsDetail',
    ]
  })],
  data: {
    isThumbnail:false, //  是否显示缩略图弹框
    canUseSharePic: true, // 表示分享图是否绘制完成
    cartNum: 1,
    scroll: {
      viewId: '',
      fillContent: false,
      toTop: true, // 是否scrollTop === 0的情况
      outView: false, // 滚动内容区的stepper是否超出可视区域
    },
    contentHeight: {
      imageMin: 0,
      image: 0,
      scroll: 0,
      time: 0 // 修改contentHeight的时候,修改下time,触发wxs监听变化
    },
    historyUser: [],
    picUrl: baseUrl.PAGODA_PIC_DOMAIN,
    disabled: { status: false, text: '' },
    isShare: false, //是否显示分享弹框
    waitStartText: '',
    waitStartTimer: 0,
    submitLoading: false,
  },
  _data:{
    /**
     * @type ShareCard
     */
    shareCard: null
  },
  observers: {
    show(show) {
      const data = {
        'scroll.viewId': show ? '' : 'goods-detail-scroll-top',
        ...(show ? {
          // 显示的时候,重置加购数
          cartNum: 1,
          historyUser: []
        } : {})
      }
      if (show) {
        this.startWaitCountDown({
          context: this,
          key: 'waitStartText',
          timerKey: 'waitStartTimer',
        })
        // 显示后等组件ready再做各种dom操作
        this._data.ready.promise.then(() => {
          this.getContentHeight()
          this.createScrollOb()
          this.contentStepperOb()
          this.updateDisabled()
        })
        this.getGoodsHistoryUser()
        const { goodsName, goodsCode } = this.data.goodsItem
        sensors.pageScreenView({
          ...relayGoodsMap.SHOW_DETAIL,
          ...this.sensorsBaseData(),
          SKU_Name: goodsName,
          SKU_ID: goodsCode,
          screen_code: 11503,
          screen_name: '接龙商品详情',
          ...(this.data.activityCode ? {
            '$referrer': 'relay/pages/home/<USER>',
            'referringScreenName': relayScreenMap.home.screen_name,
            'referringScreenCode': relayScreenMap.home.screen_code
          } : {
            '$referrer': 'relay/pages/detail/index',
            'referringScreenName': relayScreenMap.detail.screen_name,
            'referringScreenCode': relayScreenMap.detail.screen_code
          })
        })
      } else {
        // this.sensorsTrackShow()
        this.clearTimer()
      }
      this.setData(data)
    }
  },
  methods: {
    clearTimer() {
      clearTimeout(this.data.waitStartTimer)
    },
    // 空白函数 禁止滚动
    preventTouchMove() {},

    /**
     * 分享弹窗点击事件
     * @param {Object} e 事件
     */
    showModel(e) {
      this._data.shareCard.showModel(e)
    },

    /**
     * 关闭分享弹窗事件
     * @param {Object} e 事件
     */
    closeModel(e) {
      this._data.shareCard.closeModel(e)
    },
    setModelStyle() {
      var that = this;
      if (!!wx.getSystemInfo) {
        wx.getSystemInfo({
          success: function (res) {
            // canvas绘制的海报高宽比（高：1334，宽：750）
            const posterRatio = 1334 / 750
            // 弹窗显示海报的缩略图的缩小倍数
            const scale = 0.5
            // 海报的宽度 = 屏幕宽度
            const posterWidth = res.screenWidth * 2
            // 海报的高度 = 海报的宽度 x 海报高宽比
            const posterHeight = parseInt(posterWidth * posterRatio);
            // 海报缩略图的宽度
            const smallPosterWidth = parseInt(posterWidth * scale);
            // 海报缩略图的高度
            const smallPosterHeight = parseInt(posterHeight * scale);
            // 模态框的宽度 = 屏幕宽度
            const modelWidth = res.screenWidth * 2;
            // 模态框的高度 = 模态框的宽度的 1.4倍（是UI决定的）
            const modelHeight = parseInt(modelWidth * 1.40);
            // 获取屏幕高度
            const height = res.windowHeight
            that.setData({
              modelWidth,
              modelHeight,
              smallPosterWidth,
              smallPosterHeight,
              height
            })
          }
        });
      }
    },
    triggerShareMenu (bool) {
      const menus = ['shareAppMessage','shareTimeline']
      bool ? wx.showShareMenu({
        withShareTicket: false,
        menus
      }) : wx.hideShareMenu({
        menus
      })
      this.setData({
        canUseSharePic: bool
      })
    },

    async getGoodsHistoryUser() {
      const historyId = ++this._data.historyId
      const historyUser = await this.getGoodsHistory({ goodsCode: this.data.goodsItem.goodsCode })
      historyId === this._data.historyId && this.setData({ historyUser })
    },
    onClose() {
      //  关闭商品弹窗之后清除分享弹框
      this.setData({isShare:false})
      this.setState({
        'goodsDetail.show': false
      })
      sleep(301).then(() => relayCartStore.updateGoodsDetailShow({ activityCode: '' }))
    },
    showServicePopup() {
      const specialServiceList = this.data.goodsDetail.specialServiceList
      specialServiceList && specialServiceList.length && this.setState({
        serverPopup: {
          show: true,
          descList: specialServiceList
        }
      })
    },
    getContentHeight() {
      Promise.all(['#goods-detail-img-mini', '#goods-detail-img', SCROLL_VIEW_SELECTOR]
        .map(selector => new Promise(resolve =>
          this.createSelectorQuery()
          .select(selector)
          .fields({ size: true }, ({ height }) => resolve(height))
          .exec())))
        .then(([imageMin, image, scroll]) => this.setData({
          contentHeight: { imageMin, image, scroll, time: Date.now() }
        }))
    },
    /**
     * @description 创建滚动到顶部/底部ob
     */
    createScrollOb() {
      const scrollOb = this.createIntersectionObserver()
      scrollOb.relativeTo(SCROLL_VIEW_SELECTOR).observe('#goods-detail-scroll-top', ({ intersectionRatio }) => {
        const toTop = Boolean(intersectionRatio)
        this.data.show && (toTop || sensors.trackClickEvent(relayGoodsMap.CHECK_DETAIL))
        this.setData({
          'scroll.toTop': toTop
        })
      })
      this._data.domObs.push(scrollOb)
    },
    /**
     * @description wxs中滚动回调更新fillContent
     */
    updateFillContent({ fillContent }) {
      fillContent === this.data.scroll.fillContent || this.setData({
        'scroll.fillContent': fillContent
      })
    },
    /**
     * @description 创建滚动内容区域detail-stepper的ob
     */
    contentStepperOb() {
      const stepperOb = this.createIntersectionObserver()
      stepperOb.relativeTo(SCROLL_HEAD_SELECTOR, {
        // 防止#goods-detail-scroll-stepper-bottom
        // 超出SCROLL_HEAD_SELECTOR上边界后被隐藏
        top: Number.MAX_SAFE_INTEGER
      }).observe('#goods-detail-scroll-stepper-bottom', ({ intersectionRatio }) => this.setData({
        // 前提条件: 滚动内容超过滚动容器
        'scroll.outView': Boolean(this.data.scroll.fillContent && intersectionRatio)
      }))
      this._data.domObs.push(stepperOb)
    },
    onCartChange({ detail: { type, cartNum } }) {
      sensors.trackClickEvent({
        ...{
          add: relayGoodsMap.DETAIL_ADD,
          remove: relayGoodsMap.DETAIL_REMOVE
        }[type],
        ...this.sensorsBaseData({ goodsCode: this.data.goodsItem.goodsCode })
      })
      this.setData({
        cartNum
      })
    },
    onStepperLimit({ detail: { type } }) {
      wx.showToast({
        icon: 'none',
        title: {
          min: '数量不能再少啦',
          max: '已达购买上限'
        }[type]
      })
    },
    addCart: app.subProtocolValid('shop', function () {
      if (this.checkFromShareTimeLine() || this.data.disabled.status) { return }
      // 检查加购数
      const goodsItem = this.data.goodsItem
      const { goodsCode, purchaseAmount, stockNum, limitAmount } = goodsItem
      const cartNum = this.data.cartNum
      const sumCartNum = cartNum + this.getCartNum({ goodsCode })
      const maxBuyLimit = ('limitAmount' in goodsItem) && (sumCartNum + purchaseAmount) > limitAmount
      const stockLimit = sumCartNum > stockNum
      const maxAddLimit = sumCartNum > 999 // 最多加购999件
      if (maxBuyLimit || stockLimit || maxAddLimit) {
        return this.onStepperLimit({ detail: { type: 'max' } })
      }
      sensors.trackClickEvent({
        ...relayGoodsMap.DETAIL_CONFIRM_ADD,
        ...this.sensorsBaseData({ goodsCode })
      })
      this.updateCart({ goodsCode: this.data.goodsItem.goodsCode, variation: this.data.cartNum })
      this.onClose()
    }),
    updateDisabled() {
      this.setData({
        disabled: this.getDisableStatus({ goodsCode: this.data.goodsItem.goodsCode })
      })
    },
    async submitNow() {
      // 有其他的请求在进行中 或者 当前请求正在进行中
      if (this.checkFromShareTimeLine() || relayCartStore.itemSubmit.loading || this.data.submitLoading) {
        return
      }
      this.setData({ submitLoading: true })
      const { cartNamespace, goodsItem, cartNum, goodsDetail, goodsPrice, activityDetail } = this.data
      const [pickupDateKey, activityCode] = cartNamespace
      sensors.clickReport(relayGoodsMap.DETAIL_BUY_NOW_CLICK)
      relayAddSuccessReport({
        goodsObj: Object.assign({}, goodsDetail, {
          goodsPrice: goodsPrice.cartPrice,
        }) || {},
        count: 1,
        activity: activityDetail,
      })
      // 无需考虑商品限购等情况
      // 因为限购了会在updateDisabled里将购买按钮隐藏
      await relayCartStore.submitOrder({
        screenData: relayScreenMap.goodsDetail,
        onLogout: () => {
          this._data.toLogin = true
        },
        cartList: [{
          pickupDateKey,
          cartList: [toJS(goodsItem)],
          countMap: { [goodsItem.goodsCode]: { checked: true, fromBuyNow: true, count: cartNum, activityCode } },
        }],
      })
      this.setData({ submitLoading: false })
    },
  },
  lifetimes: {
    attached() {
      const ready = {}
      ready.promise = new Promise(resolve => (ready.resolve = resolve))
      this._data = {
        domObs: [],
        ready,
        historyId: 0
      }
    },
    ready() {
      // 实例化创建分享卡片海报类
      this._data.shareCard = new ShareCard(this)
      this._data.ready.resolve()
      // 如果是从朋友圈分享过来,就不监听截图
      if (!this.checkFromShareTimeLine({ showToast: false })) {
        // 增加截屏事件
        wx.onUserCaptureScreen(() => this._data.shareCard.captureScreenEvent())
      }
    },
    detached() {
      this._data.domObs.forEach(ob => ob.disconnect())
      this.clearTimer()
      // 销毁截屏事件
      if (this._data.shareCard && this._data.shareCard.captureScreenEvent) {
        wx.offUserCaptureScreen(() => this._data.shareCard.captureScreenEvent())
      }
    }
  },
  pageLifetimes: {
    show() {
      // 登录后刷新页面数据
      if (this._data.toLogin && app.checkSignInsStatus()) {
        relayCartStore.refreshCartInfo() && this.updateDisabled()
      }
      this._data.toLogin = false
    },
  },
})
