# 商品详情页分享卡片性能优化方案

## 问题分析

原始问题：商品详情页进入后，查询商品信息后通过`this._data.shareCard.drawSharePic(goodsObj)`生成canvas，并通过`wx.canvasToTempFilePath`绘制canvas生成临时路径，在某些设备上存在3s左右的延迟。

## 性能瓶颈识别

通过代码分析，发现主要性能瓶颈包括：

1. **网络图片加载**：需要下载商品图片、背景图、图标等多个网络资源
2. **Canvas绘制复杂度**：复杂的canvas绘制操作，包括图片绘制、文字渲染、样式计算
3. **wx.canvasToTempFilePath转换**：将canvas转换为临时文件的过程较慢
4. **同步等待时间**：使用了较长的setTimeout延迟（2000ms）
5. **重复计算**：样式配置的深拷贝和重复计算

## 优化方案

### 1. 性能监控系统

在关键方法中添加了详细的性能监控日志：

#### shareCard.js 优化点：
- `drawSharePic()`: 总体流程监控
- `getCustomShareBG()`: 自定义背景获取监控
- `getCoverBgImg()`: 背景图缓存监控
- `generateShareCard()`: 分享卡片生成监控

#### shareCardGenerator.js 优化点：
- `generateShareCard()`: 组件生成流程监控
- `init()`: 组件初始化监控
- `getMergedStyleInfo()`: 样式合并监控
- `generateTemplate()`: 模板生成监控

#### index.js 优化点：
- `createSharePic()`: 主流程监控和错误处理

### 2. 图片加载优化

```javascript
// 优化前：串行加载图片
const bgImageUrl = await self.getCoverBgImg(canvas, bgPath, isB2C ? 'b2cCard' : 'deliveryCard')
const navHeadUrl = await createCanvasImage(canvas, headPic)

// 优化后：并行加载图片 + 缓存机制
- 添加了图片缓存机制，避免重复下载
- 使用Promise.all并行加载多个图片资源
- 添加图片加载失败重试机制
```

### 3. Canvas性能优化

```javascript
// 优化前：
quality: 1,
setTimeout(() => {}, that.data.isIphone ? 0 : 100)

// 优化后：
quality: 0.8, // 降低质量以提升性能
fileType: 'jpg', // 使用jpg格式减少文件大小
setTimeout(() => {}, that.data.isIphone ? 0 : 50) // 减少延迟时间
```

### 4. 延迟时间优化

```javascript
// 优化前：
this.generateShareCard(hasSuccessBuild ? 200 : 2000)

// 优化后：
this.generateShareCard(hasSuccessBuild ? 100 : 1000) // 减少50%延迟时间
const optimizedDelay = Math.min(delay, 800) // 最大延迟不超过800ms
```

### 5. 样式计算优化

```javascript
// 优化前：深拷贝
const mergedStyle = JSON.parse(JSON.stringify(defaultStyleInfo))

// 优化后：浅拷贝 + 选择性深拷贝
const mergedStyle = { ...defaultStyleInfo }
// 只对需要的嵌套对象进行深拷贝
```

### 6. 错误处理和容错机制

- 添加了完整的try-catch错误处理
- 图片加载失败时的重试机制
- 组件未找到时的容错处理
- 系统信息获取失败时的默认值处理

## 性能监控输出示例

```
[GoodsDetail Performance] createSharePic开始 - goodsSn: ABC123, 时间: 1640995200000
[ShareCard Performance] drawSharePic开始 - goodsSn: ABC123, 时间: 1640995200000
[ShareCard Performance] 开始获取自定义分享背景图
[ShareCard Performance] 使用缓存的自定义背景图 - spuNumber: 12345, 有缓存: true, 耗时: 2ms
[ShareCard Performance] 开始生成自定义分享卡片 - 耗时: 5ms
[ShareCardGenerator Performance] 开始生成分享图片 - goodsSn: ABC123, 时间: 1640995200005
[ShareCardGenerator Performance] 获取canvas组件完成 - 耗时: 1ms
[ShareCardGenerator Performance] 样式配置合并完成 - 总耗时: 3ms
[ShareCardGenerator Performance] 自定义模板生成完成 - 耗时: 2ms
[ShareCardGenerator Performance] Canvas渲染完成 - 耗时: 800ms
[ShareCardGenerator Performance] 图片导出完成 - 导出耗时: 200ms
[ShareCardGenerator Performance] 分享图片生成完成 - 总耗时: 1010ms
[GoodsDetail Performance] createSharePic完成 - 总耗时: 1015ms
```

## 预期性能提升

1. **延迟时间减少**：从最大2000ms减少到800ms，减少60%
2. **图片质量优化**：quality从1.0降到0.8，文件大小减少约30%
3. **缓存机制**：重复访问同类商品时，背景图加载时间从几百ms减少到2-5ms
4. **样式计算优化**：样式合并时间减少约50%
5. **错误恢复**：添加重试机制，提高成功率

## 使用方法

优化后的代码会自动输出性能监控日志，开发者可以通过控制台查看各个阶段的耗时：

1. 在开发者工具中打开控制台
2. 进入商品详情页
3. 查看以`[ShareCard Performance]`、`[ShareCardGenerator Performance]`、`[GoodsDetail Performance]`开头的日志
4. 分析各个阶段的耗时，识别性能瓶颈

## 进一步优化建议

1. **图片预加载**：可以在商品列表页面预加载常用的背景图和图标
2. **WebP格式**：支持WebP格式的设备可以使用WebP图片减少文件大小
3. **离屏Canvas**：使用离屏Canvas进行预渲染
4. **分片渲染**：对于复杂的分享卡片，可以考虑分片渲染
5. **CDN优化**：确保图片资源使用CDN加速
