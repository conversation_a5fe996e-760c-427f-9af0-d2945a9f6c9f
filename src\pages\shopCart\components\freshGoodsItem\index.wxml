<wxs module="common" src="../../../../utils/common.wxs" />
<view
  class="goods-wrap {{isTouchMove ? 'touch-move-active' : ''}}"
  bindtouchstart="touchstart"
  bindtouchend="touchend"
  bindtouchmove="touchmove">
  <block wx:if="{{ isLoad }}">
    <view class="goods-row" catchtap="goDetail">
      <view class="goods-content">
        <!-- 售罄遮罩 -->
        <view class="goods-spec-choose--disabled-mask" wx:if="{{isInvalid || showNoMatch}}"></view>
        <!-- 售罄遮罩 -->
        <!-- 商品左侧 -->
        <view class="content-left">
          <!-- 售罄状态下的按钮 -->
          <view class="goods-check" wx:if="{{isInvalid || showNoMatch}}">
            <radio-check class="radio-button" checked="{{false}}" disabled="{{ true }}" />
          </view>
          <!-- 售罄状态下的按钮 -->
          <!-- 普通状态下的按钮 -->
          <view
            wx:else
            class="goods-check"
            catchtap="checkSingle"
            data-goods="{{goodsInfo}}"
          >
            <radio-check class="radio-button" checked="{{goodsInfo.isSelected === 'Y'}}" />
          </view>
          <!-- 普通状态下的按钮 -->

          <!-- 商品头图 -->
          <view class="goods-img {{isInvalid || showNoMatch ?'img-sell-out':''}} {{matchExchange || isInvalid ? '' : 'img-no-match-exchange'}}">
            <image class="goods-img-body" src="{{common.minifyImg(goodsInfo.headPic, 140, 140)}}" />
            <!-- 商品冷冻标签 -->
            <pagoda-storage-tag wx:if="{{goodsStorageTypeObj[goodsInfo.goodsSn] || goodsInfo.storageType}}" parentWidth="{{140}}" storageType="{{goodsStorageTypeObj[goodsInfo.goodsSn] || goodsInfo.storageType}}" top="{{4}}" right="{{4}}"/>
            <!-- 商品冷冻标签 -->
          </view>
          <!-- 商品头图 -->
        </view>
        <!-- 商品左侧 -->
        <!-- 商品右侧 -->
        <view class="content-right">
          <view class="info-top">
            <!-- 商品名称 -->
            <view class="goods-name">
              <text wx:if="{{goodsInfo.isNewExclusive === 'Y' && !isInvalid}}" class="new-exclusive">新客专享</text>
              <view wx:if="{{goodsInfo.activityInfo}}" class="exchange-label">换购</view>
              <view class="goods-name-text">{{common.goodsLevelFormat(goodsInfo.name || goodsInfo.shortName)}}</view>
            </view>
            <!-- 商品名称 -->

            <!-- 商品规格 -->
            <view class="goods-spec-desc" wx:if="{{goodsInfo.spec}}">{{common.goodsLevelFormat(goodsInfo.spec)}}</view>
            <!-- 商品规格 -->

            <!-- 商品标签 -->
            <view class="goods-label-list" wx:if="{{!isInvalid}}">
              <goods-label goods="{{goodsInfo}}" />
            </view>
            <!-- 商品标签 -->
          </view>

          <view class="info-bottom">
            <!-- 商品金额 -->
            <!-- 换购活动(别名: 一元购/加价购) 或 次日达特价活动的价格展示 -->
            <!-- 特价活动时,加购数没超过限购数, 才显示特价 和 商品售价 -->
            <view wx:if="{{goodsInfo.activityInfo || (goodsInfo.specialInfo && goodsInfo.specialInfo.maxEffectNum >= goodsInfo.count)}}" class="goods-price">
              <view class="bid-price">{{
                common.formatPrice(
                  goodsInfo.activityInfo
                    ? goodsInfo.activityInfo.price
                    : goodsInfo.specialInfo.price
                )
              }}</view>
              <!-- 换购品 或 次日达特价加购项,使用商品售价做展示 -->
              <view class="bid-price line-price">{{common.formatPrice(goodsInfo.bidPrice)}}</view>
            </view>
            <view wx:else class="goods-price">
              <block wx:if="{{(goodsInfo.vipPrice || goodsInfo.vipPrice == 0) && isVip}}">
                <view class="heart-price">{{common.formatPrice(goodsInfo.vipPrice)}}</view>
                <image class="heart-price-icon" mode="aspectFit" src="https://resource.pagoda.com.cn/eshop/e0f1d7b28bec6391a618a465672b54c0" />
                <!-- 心享专享 或者 换购或者特价商品需要显示划线价 超过特价限制不用显示划线价 -->
                <view wx:if="{{(goodsInfo.buyerType !== 'V' || goodsInfo.activityInfo || goodsInfo.specialInfo) && !promotion.overLimit}}" class="bid-price line-price">{{common.formatPrice(goodsInfo.bidPrice)}}</view>
              </block>
              <view wx:else class="bid-price">{{common.formatPrice(goodsInfo.bidPrice)}}</view>
              <view wx:if="{{promotion.overLimit}}" class="promotion-normal-count">x{{promotion.normalCount}}</view>
            </view>
            <!-- 商品金额 -->

            <!-- 操作区域 -->
            <view class="count" wx:if="{{!isInvalid}}">
              <!-- 减购按钮 -->
              <view class="btn-wrap">
                <view
                  class="btn-mask--left"
                  catchtap="changeCount"
                  data-type="2"></view>
                <image
                  class="subbigger"
                  src="/source/images/cart-substract-btn.png"
                />
              </view>
              <!-- 减购按钮 -->
              <!-- 自定义输入框 -->
              <view class="count-num-box" catchtap="preventClick">
                <input
                  class="count-num"
                  value="{{count}}"
                  type="number"
                  maxlength="4"
                  bindfocus="onFocus"
                  bindblur="setCount"
                />
              </view>
              <!-- 自定义输入框 -->
              <!-- 加购按钮 -->
              <view class="btn-wrap">
                <view
                  class="btn-mask--left"
                  catchtap="changeCount"
                  data-type="1"></view>
                <image
                  class="subbigger"
                  src="/source/images/cart-add-btn.png"
                />
              </view>
              <!-- 加购按钮 -->
            </view>
            <!-- 操作区域 -->
          </view>
          <view wx:if="{{promotion.overLimit}}" class="promotion-over-limit">特价限{{promotion.maxEffectNum}}份，超出部分以原价进行购买</view>
        </view>
        <!-- 商品右侧 -->
      </view>
    </view>
    <!-- 商品删除按钮 -->
    <view
     class="goods-del"
     catchtouchend="onDelHandle"
     data-index="{{goodsIndex}}"
     data-goods="{{goodsInfo}}"
     wx:if="{{!isInvalid}}">删除</view>
  </block>
  <!-- 商品加载中骨架图 -->
	<image wx:else class="loading-item" src="/source/images/cart_bg.png" />
</view>
