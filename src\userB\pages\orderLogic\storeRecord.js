/**
 * v5.0.6
 * 因为现有的 generateBtnList 方法是同步的，这两个按钮逻辑是异步的
 * 如果要改的话，涉及多个页面，范围有点大
 * 所以两个按钮的逻辑暂时先分开写，后面再有需求，再合并到 generateBtnList
 */

import { baseUrl } from "~/utils/config"
const CryptoJS = require('../../../source/js/crypto-min');
import { clickReport } from '../../../utils/report/sensors'
import { clickParamsMap } from '../../pages/allOrderDetail/sensorClickParams'
import { FRUIT_SERVICES, SALE_TYPE_ENUM } from "~/utils/goods/enum";

const liveConfig = {
  status: ['备货中', '已备货'],
  btn: {
    btnType: 'liveBtn',
    btnName: '门店吧台直播',
  }
}

const recordConfig = {
  status: ['备货中', '已备货', '交易成功'],
  btn: {
    btnType: 'recordBtn',
    btnName: '果品开切视频',
  }
}

/**
 * @desc 是否及时达配送
 */
function isTimelyDelivery(orderInfo) {
  const { orderType, dispatchType } = orderInfo
  return orderType.desc === '及时达' && dispatchType.desc === '商家配送'
}

function hasStoreInfo(orderInfo) {
  const { store } = orderInfo
  return store && store.storeCode
}

function hasUserInfo() {
  const { userID } = wx.getStorageSync('user') || {}
  return String(userID) !== '-1'
}

/**
 * 销售类型为果切或有服务项
 */
function isFruitCutOrHasService(orderInfo) {
  const { items } = orderInfo
  const isFruitCut = Array.isArray(items) && items.some(item => item.saleType === SALE_TYPE_ENUM.果切)
  const serviceNames = Object.keys(FRUIT_SERVICES)
  const hasService = items.some(({ itemServiceList }) => {
    if (Array.isArray(itemServiceList) && itemServiceList.length) {
      return itemServiceList.some(s => s.itemConsumablesName && serviceNames.includes(s.itemConsumablesName))
    }
    return false
  })
  return isFruitCut || hasService
}

function orderMatched(orderInfo) {
  return [
    isTimelyDelivery,
    hasStoreInfo,
    hasUserInfo,
    isFruitCutOrHasService
  ].every(fn => fn(orderInfo))
}

function isInStatus(orderInfo, status) {
  const { orderStatus } = orderInfo
  return status.includes(orderStatus.orderStatusDesc)
}

function addRecordBtn(orderInfo) {
  const { status, btn } = recordConfig
  if (!isInStatus(orderInfo, status)) return
  return btn
}

function addLiveBtn(orderInfo) {
  const { status, btn } = liveConfig
  if (!isInStatus(orderInfo, status)) return
  return btn
}

export async function addBtn(orderInfo) {
  const { orderNo, store } = orderInfo
  const { userID } = wx.getStorageSync('user') || {}
  const isMatched = orderMatched(orderInfo)
  if (!isMatched) return
  // return {
  //   url: 'https://cmgw-vpc.lechange.com:8890/flv/LCO/8C02CE2PAZFD37F/3/1/20240415153505/********************************.flv?proto=https',
  //   btn: {
  //     btnType: 'liveBtn',
  //     btnName: '果品开切直播',
  //   }
  // }
  try {
    const { data } = await getApp().api.getLiveOrRecord({
      orderNo,
      storeCode: store.storeCode,
      customerID: String(userID)
    })
    console.log('getLiveOrRecord', data)
    const { url, isLive, web } = data
    return {
      url,
      web,
      btn: isLive ? addLiveBtn(orderInfo) : addRecordBtn(orderInfo),
      // url: 'https://cmgw-vpc.lechange.com:8890/flv/LCO/8C02CE2PAZFD37F/3/1/20240415153505/********************************.flv?proto=https',
      // btn: {
      //   btnType: 'liveBtn',
      //   btnName: '果品开切直播',
      // }
    }
  } catch (error) {
    console.log('getLiveOrRecord err', error)
  }
}

export function recordBtnHandle(orderInfo) {
  vedioHandle.call(this, orderInfo, recordConfig.btn.btnType)
  clickReport(clickParamsMap[recordConfig.btn.btnName])
}

export function liveBtnHandle(orderInfo) {
  vedioHandle.call(this, orderInfo, liveConfig.btn.btnType)
  clickReport(clickParamsMap[liveConfig.btn.btnName])
}

function vedioHandle(orderInfo, type) {
  // 添加btn时已经判断过store字段，这里不再判断
  const { orderNo, store } = orderInfo
  const { mclzUrl, dskhdWebUrl } = this._data
  if (!mclzUrl) return
  const encodedWord = CryptoJS.enc.Utf8.parse(mclzUrl)
  const base64Url = CryptoJS.enc.Base64.stringify(encodedWord)
  const decodeWebWord = CryptoJS.enc.Base64.parse(dskhdWebUrl)
  const decodeWebUrl = CryptoJS.enc.Utf8.stringify(decodeWebWord)
  console.log('decodeWebUrl', decodeWebUrl)
  // http://*************:5173
  // ${baseUrl.DSKHD_WEB_DOMAIN}
  // const tempUrl = 'https://cmgw-vpc.lechange.com:8890/flv/LCO/8C02CE2PAZFD37F/3/1/20240415153505/********************************.flv?proto=https'
  const pageUrl = `${decodeWebUrl}/#/store-vedio-record?mclzUrl=${base64Url}&orderNo=${orderNo}&orderStoreCode=${store.storeCode}&isLive=${type === liveConfig.btn.btnType ? 1 : 0}`
  // const pageUrl = 'http://*************:5173/#/store-vedio-record?mclzUrl=aHR0cHM6Ly90ZXN0LS1haXh1bmRpYW4tLWNvcy0xMzE3MjA0MzA4LmNvcy5hcC1ndWFuZ3pob3UubXlxY2xvdWQuY29tL2FpeHVuZGlhbi9tY2x6L3ZpZGVvL3Rlc3QvYXBwcm92ZWQvMDAyNS8yNTE5NTM2OTkyMTI2NDcxNC8yMDI0MDYxMi1mNDZjMjNiMzAyMDY0ZmFiODBhYzA3YmMwMmE1OTJlZjMubXA0P3Etc2lnbi1hbGdvcml0aG09c2hhMSZxLWFrPUFLSURuVklHWm9RV281U2xjVmYzWTFTbXc4UzNheUdDSUtNSyZxLXNpZ24tdGltZT0xNzE4MTczMTQ3OzE3MTgxNzQwNDcmcS1rZXktdGltZT0xNzE4MTczMTQ3OzE3MTgxNzQwNDcmcS1oZWFkZXItbGlzdD1ob3N0JnEtdXJsLXBhcmFtLWxpc3Q9JnEtc2lnbmF0dXJlPWRmYTQzMTFlNzJmM2FhM2Y0NmQ1N2MxMzRiZWU1YzI1MzEzZTk2OWU=&orderNo=25195369921264714&orderStoreCode=0025&isLive=1&user_id=1911232179&user_token=8A6C073229F62D0963DA486A05B3542F3D7674943D1180E83C4F554A8483F7A4&phoneNumber=186****3841&unionid=ok8R8vzXwx6D4A7ZQGzkeicZsCGk&city_id=3&city_name=%E6%83%A0%E5%B7%9E%E5%B8%82&store_id=362&store_name=%E6%83%A0%E5%B7%9E%E7%A2%A7%E6%B0%B4%E6%B9%BE%E5%BA%97&version=5.0.5&cityCode=441300&storeCode=0701&deliveryCenterCode=szpszx&lat=22.53640098079008&lon=113.95354249750291&curLocation={%22lat%22:22.53125705295139,%22lon%22:113.94730170355903}&wechatUnionID=ok8R8vzXwx6D4A7ZQGzkeicZsCGk&is_login=%E6%98%AF&Terminal=%E7%99%BE%E6%9E%9C%E5%9B%AD+%E5%B0%8F%E7%A8%8B%E5%BA%8F&storeAddress=%E7%95%99%E5%AD%A6%E7%94%9F%E5%88%9B%E4%B8%9A%E5%A4%A7%E5%8E%A6&traceId=1718172351537-9724751-033dbc279c8cbf6-10423776_1718172351&$url_path=h5/pages/commonh5/index&screen_name=%E5%B0%8F%E7%A8%8B%E5%BA%8FH5%E9%A1%B5&screen_code=11401&GroupNewUsers=%E5%90%A6&QuicklyDeliveryUsers=%E6%98%AF&NextDeliveryNewUsers=%E6%98%AF&wecomIsGuideFriend_a=%E9%9D%9E%E5%A5%BD%E5%8F%8B&wecomInGroupchat_a=%E6%9C%AA%E5%8A%A0%E7%BE%A4&memberlevelId_levelName=1_%E6%99%AE%E5%8D%A1%E4%BC%9A%E5%91%98&$referrer=userB/pages/allOrderDetail/index&referringScreenName=%E5%8F%8A%E6%97%B6%E8%BE%BE%E8%AE%A2%E5%8D%95%E8%AF%A6%E6%83%85%E9%A1%B5&referringScreenCode=1401&$latest_scene=1089&'
  wx.navigateTo({ url: `/h5/pages/commonh5/index?pageParam=${JSON.stringify({ pageUrl: encodeURIComponent(pageUrl) })}` })
}
