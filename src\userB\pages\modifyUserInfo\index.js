// 会员修改信息页 index.js
var commonObj = require('../../../source/js/common').commonObj;
const sensors = require('../../../utils/report/sensors')
import teenagerProtocol from '../../../mixins/teenager'
import { checkSensitiveWord } from '../../../utils/services/tools';
import { forMatDate } from '../../../utils/deliveryTime'
import { navigateToCommonH5Page } from '../../../mixins/navigateToH5Mixin'
var app = getApp()
const levelObj = {
  1: '普卡会员',
  2: '银卡会员',
  3: '金卡会员',
  4: '钻卡会员',
  5: '绿卡会员'
}
import config  from '../../../utils/config'
const h5Domain = config.baseUrl.H5_WEB_DOMAIN
let isShowPopup = false
import { updateNickAndIcon } from '../../../service/userService'
import COSHelper from '~/utils/services/COSHelper'
const CryptoJS = require('~/source/js/crypto-min')

import { COS_UPLOAD_TYPE_ENUM } from '~/source/const/cosConfig'
import { defaultCustomerAvatar } from '~/source/const/user';
function instanceCOSHelper() {
  const { userID: customerID } = wx.getStorageSync('user') || {}
  return new COSHelper({
    authSource: 'dskhd',
    cosType: 'avatar',
    params: {
      customerID
    }
  })
}
function getENcryptCustomerId(data, key = config.userCosEncryptKey, iv = null) {
  if (!data) {
    return ''
  }
  const encryptKey = CryptoJS.enc.Utf8.parse(key)
  const encryptedStr = CryptoJS.AES.encrypt(data, encryptKey, {
    iv: iv,
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
    format: CryptoJS.format.Hex
  });
  return encryptedStr.toString()
}
Page({

  /**
   * 页面的初始数据
   */
  data: {
    name: '',
    nickName: '',
    sex: '',
    avatarUrl: '',
    genderList: ['女', '男'],
    genderIndex: -1,
    birthday: '',
    closeShowFlag: false,
    isIdentified: false, // 是否实名认证
    levelName: '', // 会员等级
    showGenderMask: false, // 性别选择弹层
    familySize: '', // 家庭成员数量
    infoObj: {}, // 原始数据的对象
    taskFlag: false, // 蒙层标识
    inputNameFlag: false, // 姓名输入框标识
    inputMemberFlag: false, // 家庭成员输入框标识
    colorFlag: false, // 输入框完成颜色标识
    memberArray: ['1', '2', '3', '4', '5', '6', '7', '8', '9'],
    defaultCustomerAvatar,  // 用户默认头像
    showPopup: false,
    disabledPicker: !isShowPopup,
  },
  _data: {
    tpl_nickName: '',
    noNameFlag:false,
    initMemberInfo: {},
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

    var that = this;

    var user = wx.getStorageSync('user');
    that.getMemberInfo(user.userID, user.phoneNumber);
    this._data.userID = user.userID
    const pages = getCurrentPages();
    const prevpage = pages[pages.length - 2] || {};
    console.log(prevpage.route)
    if (prevpage.route === 'userA/pages/taskCenter/index') {
      // if (prevpage.route === 'pages/index/index') {
      that.setData({
        taskFlag: true
      })
      setTimeout(() => {
        that.setData({
          taskFlag: false
        })
      }, 3000)
    }
    isShowPopup = false
  },
  onShow: function () {
    //  上报页面浏览事件
    sensors.pageScreenView()
    // var defaultN = this.data.defaultName;
    // if (defaultN != '') {
    //   this.setData({ disable: false })
    // }
  },
  // 选择日期
  bindDateChange: function (e) {
    this.setData({
      birthday: e.detail.value
    })
    this.saveBtnFlag()
  },
  handleAgree() {
    this.setData({
      showPopup: false,
      disabledPicker: false,
    })
    isShowPopup = true
    // this.openPicker()
  },
  handleDisagree() {
    this.setData({
      showPopup: false,
    })
  },
  // 选择性别
  bindGenderChange(e) {
    let index = e.detail.value
    let genderList = this.data.genderList
    this.setData({
      genderIndex: index,
      sex: genderList[index]
    })
    this.saveBtnFlag()
  },
  // 姓名隐藏逻辑
  // hideName(str) {
  //    if(str.length > 2) {
  //      let first = str.slice(0,1)
  //      let remain = str.slice(1)
  //      let tempStr = ''
  //      let i = 0
  //      for (i=0;i<=remain.length-1;i++){
  //         tempStr += '*'
  //      }
  //      let newName = first + tempStr
  //      return newName
  //    } else {
  //      return str
  //    }
  // },
  // 获取会员信息
  async getMemberInfo(userId) {
    const { data, systemTime } = await app.api.getMemberInfo({
      customerID: userId
    })

    const {
      levelId,
      birthday,
      identity,
      sex,
      familySize,
      certified,
      realName,
      superVipStatus,
      icon = '',
    } = data

    let nickName = data.nickName

    const name = identity.substring(0, 3) + '****' + identity.substring(7, 11)
    const genderIndex = this.data.genderList.indexOf(sex)
    const isIdentified = certified === 1

    // if (isIdentified) {
    //   nickName = realName
    // }

    if (data.nickName ==='') {
      this._data.noNameFlag = true
    } else {
      this._data.noNameFlag = false
    }
    // const {avatarUrl=''} = wx.getStorageSync('userNameAndImg') || {}
    this._data.tpl_nickName = nickName

    this.setData({
      infoObj: data,
      isIdentified,
      name,
      nickName,
      familySize,
      avatarUrl: icon.indexOf('thirdwx.qlogo.cn') === -1 && icon.indexOf('https') > -1 ? icon : '',
      sex: sex || '女',
      genderIndex,
      birthday: birthday || '1985-01-01',
      levelName: levelObj[levelId],
      levelId,
      isBgxxVip: superVipStatus === 'F' || superVipStatus === 'T',
      currentDate: forMatDate(systemTime) || '2030-12-31'
    })
    this._data.initMemberInfo = {
      icon,
      birthday,
      nickName,
    }
  },
  ...teenagerProtocol,
  // 修改会员信息， 姓名，生日，性别
  async modifyUserInfo() {
    const that = this
    if (!that.data.saveBtnFlag) return

    const {
      nickName,
      birthday,
      avatarUrl,
    } = that.data

    // const res = await checkSensitiveWord({
    //   loadingText: '校验中',
    //   defaultToastText: '实名认证内容含有敏感信息，请修改后发布',
    //   text: nickName,
    //   bizType: 1
    // }).catch(e => {
    //   console.log(e)
    // })
    // // 存在敏感词则res为false
    // if (!res) {
    //   return
    // }
    wx.showLoading({
      title: '加载中',
      mask: 'true'
    })


    const user = wx.getStorageSync('user');
    const {
      icon: initIcon,
      birthday: initBirthaday,
      nickName: initNickname,
    } = this._data.initMemberInfo
    const data = {
      customerID: user.userID,
      bizHead: {
        channel: '10001'
      },
      memberId: user.userID
    }
    if (initIcon !== avatarUrl) {
      data.icon = avatarUrl
    }
    if (initBirthaday !== birthday) {
      data.birthday = birthday
      const agree = await this.teenagerProtocol(that.data.birthday.split('-'))
      if (!agree) {
        return this.rejectSignOut(app)
      }
    }
    if (initNickname !== nickName) {
      data.nickName = nickName
    }

    try {
      console.log(data)
      await app.api.modifyMemberInfo(data)
      updateNickAndIcon(nickName, avatarUrl)
      wx.showLoading({
        title: '保存成功',
      })
      setTimeout(function () {
        wx.navigateBack({
          delta: 1,
          fail() {
            wx.reLaunch({
              url: '/pages/homeDelivery/index'
            })
          }
        })
      }, 500)
    } catch (error) {
      console.log(error)
      wx.hideLoading();
        wx.showToast({
          icon:'none',
          title: error.description || '保存失败，请重新保存',
          duration:1500,
      })
    }
  },
  // 保存按钮标识确认
  saveBtnFlag() {
    let that = this
    let {
      nickName,
      sex,
      birthday,
      familySize,
      avatarUrl,
    } = that.data
    let infoObj = that.data.infoObj
    let checkStatusFlag = infoObj.nickName === nickName && infoObj.sex === sex && infoObj.birthday === birthday && infoObj.familySize === familySize && infoObj.avatarUrl === avatarUrl
    if (infoObj.nickName ==='') {
      if (checkStatusFlag) {
        that.setData({
          saveBtnFlag: false
        })
      } else {
        that.setData({
          saveBtnFlag: true
        })
      }
    } else {
      if (checkStatusFlag || String(nickName) ==='') {
        that.setData({
          saveBtnFlag: false
        })
      } else {
        that.setData({
          saveBtnFlag: true
        })
      }
    }

  },
  // 跳转会员俱乐部页
  tapMemberInfo: function (e) {
    if (app.checkSignInsStatus()) {
      navigateToCommonH5Page(e)
    } else {
      app.signIn()
    }
  },
  // 去往实名认证页
  toRealNameCheck() {
    // 暂借调试
    // wx.navigateTo({
    //   url: '/userB/pages/noReasonRefund/index', // 特权页
    // })
  },
  // 取消蒙层
  cancelTask() {
    this.setData({
      taskFlag: false
    })
  },
  // 判断姓名输入
  keyNameInput(e) {
    // let regNum = /\d/g; // 验证数字逻辑
    let regSpace = /\s/g;
    let regEn = /[`~!@#$%^&*()\-+=<>?:"{}|,.\/;]/g,
      regCn = /[·！#￥（）：；“”‘、，|《。》？、【】[\]]/img;
    if (regEn.test(e.detail.value) || regCn.test(e.detail.value) || regSpace.test(e.detail.value)) {
      // return false;
      console.log(regEn.test(e.detail.value),1)
      console.log(regCn.test(e.detail.value),2)
      console.log(regSpace.test(e.detail.value),3)
      let nickName = e.detail.value.replace(/\s/g, "").replace(regEn, '').replace(regCn, '')
      wx.showToast({
        title: '支持字符:数字,中文,英文,下划线',
        icon: 'none',
        during: 500
      })
      this.setData({
        nickName,
      })
    } else {
      this.setData({
        nickName: e.detail.value,
      })
    }
    if (e.detail.value.length === 10) {
      this._data.tpl_nickName = e.detail.value
    }
    if (e.detail.value.length > 10) {
      wx.showToast({
        title: '输入昵称长度不符合规范',
        icon: 'none',
        during: 500
      })
      this.setData({
        nickName: this._data.tpl_nickName
      })
    }
    this.saveBtnFlag()
  },
  // 修改家庭成员人数
  bindPickerChange: function (e) {
    console.log('picker发送选择改变，携带值为', e.detail.value)
    const memberArray = this.data.memberArray
    let index = e.detail.value
    this.setData({
      index,
      familySize: memberArray[index]
    })
    this.saveBtnFlag()
  },
  //
  bindTap() {
    wx.navigateTo({
      url: `/h5/pages/commonLink/index?pageUrl=${h5Domain}/app/privacyInfo&query=${encodeURIComponent('close=N')}`,
    })
  },
  async chooseavatar(e) {
    const { avatarUrl } = e.detail;
    const cosHelperDSKHD = instanceCOSHelper()
    await cosHelperDSKHD._refreshTempKey()
    const arrFileInfo = [{
      path: avatarUrl,
      replaceFile: true,
      saveName: `${getENcryptCustomerId(this._data.userID)}/avatar.png`,
    }]
    const res = await cosHelperDSKHD.uploadTempFilePath({
      arrFileInfo,
      uploadType: COS_UPLOAD_TYPE_ENUM.AVATAR,
    }).catch(err => {
      return []
    })
    console.log(res)
    const arrImgs = res.map(item => item.result && item.result.Location ? ('https://' + item.result.Location) : '')
    this.setData({
      'avatarUrl': arrImgs[0] + '?time=' + Date.now()
    });
    this.saveBtnFlag()
  },
  toUpdateNickName() {
    wx.navigateTo({
      url: `/userB/pages/updateNickName/index?nickName=${this.data.nickName || ''}`,
    })
  },
  // 修改昵称保存触发
  updateValue(e) {
    console.log(e)
    this.setData({
      nickName: e
    })
    this.saveBtnFlag()
  },
  selectBirthday(e) {
    console.log(e)
    if (!isShowPopup) {
      this.setData({
        showPopup: true,
      })
      return
    }
    // this.handleAgree()
  }
})
