// h5/pages/toMiniprogram/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    btnText: '前往领取',
    descText: '恭喜您已成功兑换礼品，快去带它回家吧！',
    posterUrl: 'https://resource.pagoda.com.cn/group1/M21/6F/C3/CmiWa2HJXr-AUPqtAAYn4MNSJsM965.jpg'
  },
  _data: {
    options: {}
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this._data.options = options
  },
  /**
   * 跳转印鸽小程序
   */
  toNavigateHandle() {
    const { path, appletId } = this._data.options || {}
    wx.navigateToMiniProgram({
      appId: appletId,
      path: decodeURIComponent(path)
    })
  }
})