const util = require('../../../../../utils/util');
const { fillText, drawGoodsPicRadius } = require('../../../../../utils/services/canvasUtil');
const {
  roundRectImg,
  fillRoundRect,
  measureText,
} = require('../../../../../utils/services/2DCanvasUtil');
var commonObj = require('../../../../../source/js/common.js').commonObj;
const {
  WHITE_MONEY,
  SHARE_BALANCE,
  STORE_NAME_LEFT,
  STORE_NAME_RIGHT,
  SOLITAIRE_SHARE_POSTER,
  SOLITAIRE_SHARE_LCON,
  AVATAR_DEFAULT
} = require('../../../../../source/const/goodsImage');
const sensors = require('../../../../../utils/report/sensors')
const { generateShareAttr, ShareScene, ShareWay } = require('../../../../../utils/report/setup')
const { relayGoodsMap } = require('../../../../sensorReportData')
const { handlePickupTime } = require('../../../common/orderGoods')
import { YN2Boolean } from '~/utils/YNBoolean';
import { getWxCodeTempFilePath }  from '../../../../../service/weChat'
import { checkUserIsDistributor, isDistributorFromCache } from '../../../../../service/userService';
const app = getApp();
const getDistributorsQRCode = require.async('../../../../../sourceSubPackage/qrcode/distributorsQRCode')

export default class ShareCard {
  /**
   * 构造函数 接受商详页传入的data
   * @param {Object} that 页面操作的this对象
   */
  constructor(that) {
    this.that = that;
    this.initDefaultData();
  }
  /**
   * 初始化默认数据
   */
  initDefaultData() {
    const data = {
      // 海报临时地址 tempFilePath 用于显示界面中的canvas
      shareImage: '',
      // 是否是iPhone机型
      isIphone: app.globalData.isIphone,

      getUrl: commonObj.PAGODA_DSN_DOMAIN,

      isLogin: true

    };
    const _data = {
      // 存储分享海报临时路径 格式： {[goodsSn]: tempFilePath}
      posterPic: {},
      // 存储下载好的图片临时路径缓存对象
      posterImage: {},
    };
    Object.assign(this.that.data, data);
    Object.assign(this.that._data, _data);
    this.updateUserInfo();
  }
  /**
   * 绘制分享海报 canvas图
   */
  async drawPoster() {
    const self = this;
    const that = this.that;
    const { goodsSn } = that.data.goodsDetail || {};
    // 如果截屏时 不在具体的商品详情页不生成海报
    if (!goodsSn || !that.data.show) {
      return
    }
    const { posterPic = {} } = that._data;
    // 若海报已经生成过了，不用重新生成
    if (posterPic.hasOwnProperty(goodsSn)) {
      this.showSharePoster(posterPic[goodsSn]);
      return;
    }
    wx.showLoading({
      title: '正在生成海报',
    });
    try {
      const { storeName } = wx.getStorageSync('timelyCity') || {};
      //  接龙价格
      const { code2Price } = that.getState()
      const { goodsCode } = that.data.goodsItem;
      let {  goodsPricePer } = that.data.goodsItem;
      let goodsPrice = code2Price[goodsCode].originPrice
      const {
        goodsName,
        goodsLevel,
        specDesc,
        subtitle: goodsWeight,
        // takeawayAttr,
      } = that.data.goodsDetail || {};
      goodsPrice = util.formatPrice(goodsPrice);
      goodsPricePer = util.formatPrice(goodsPricePer);

      // canvas绘制文字和图片
      const canvasCtx = wx.createCanvasContext('posterCanvas', that);
      canvasCtx.scale(2, 2);

      // 获取生成海报的图片资源
      await this.getPosterImagePath();
      // 图片资源
      const {
        bgImagePath,
        nativeImagePath,
        codeImagePath,
        WHITE_MONEY,
        SHARE_BALANCE,
        STORE_NAME_LEFT,
        STORE_NAME_RIGHT,
        SOLITAIRE_SHARE_LCON,
        AVATAR_DEFAULT,
        avatarUrl
      } = that._data.posterImage || {};

      const marginLeft = 32;
      // 绘制背景图 750*1304 这里缩放一半
      const WIDTH = 375;
      const HEIGHT = 652;
      canvasCtx.drawImage(bgImagePath, 0, 0, WIDTH, HEIGHT);

      // 绘制商品图片
      // canvasCtx.drawImage(nativeImagePath, 19, 58, 338, 338);
      drawGoodsPicRadius({canvasCtx, imagePath: nativeImagePath})

      //  绘制接龙专享
      canvasCtx.drawImage(SOLITAIRE_SHARE_LCON, 28, 67, 72, 24);

      // 绘制商品标题
      const maxWidth = 290
      let fillTop = 458
      const showGoodsLevel = goodsLevel ? (goodsLevel + '-') : ''
      fillTop = fillText(canvasCtx, {
        text: showGoodsLevel + goodsName + specDesc,
        x: marginLeft,
        y: fillTop,
        width: maxWidth - 130,
        MaxLineNumber: 2,
        fontSize: 18,
        bolder: true,
        color: '#222222',
        lineHeight: 23,
      });
      fillTop += 23 + 4 // 23 字体行高
      // 绘制小标题
      fillText(canvasCtx, {
        text: goodsWeight,
        x: marginLeft,
        y: 510,
        width: maxWidth - 130,
        MaxLineNumber: 2,
        fontSize: 12,
        color: '#999999',
      });

      // 价格绘制的Y坐标
      let priceTextY = 405;
      // 绘制提货时间
      const { activityDetail = {} } = that.data
      const { pickupEnd, pickupStart } = activityDetail
      const pickupTimeStr = handlePickupTime(pickupStart, pickupEnd, false)
      if (pickupTimeStr) {
        const pickupTimeStrText = pickupTimeStr + '可提货'
        const pickupTimeW = measureText(canvasCtx, pickupTimeStrText, '10px') + 10
        // 矩形填充
        fillRoundRect(
          canvasCtx,
          marginLeft,
          404,
          pickupTimeW,
          14,
          2,
          '#FEECEC'
        );
        fillText(canvasCtx, {
          text: pickupTimeStrText,
          x: marginLeft + 5,
          y: 405,
          width: maxWidth - 130,
          MaxLineNumber: 2,
          fontSize: 10,
          color: '#FF0C0C',
        })
        // 如果绘制了提货时间 则需要调整价格绘制的Y坐标
        priceTextY = 422;
      }
      // 绘制第一个价格：接龙售价
      let priceTextX = marginLeft;
      const salePrice = goodsPricePer;
      priceTextX += 6;
      fillText(canvasCtx, {
        text: '¥',
        x: priceTextX - 6,
        y: priceTextY + 8,
        fontSize: 18,
        bolder: true,
        color: '#FF7387',
      });
      fillText(canvasCtx, {
        text: salePrice,
        x: priceTextX+4,
        y: priceTextY,
        fontSize: 28,
        bolder: true,
        color: '#FF7387',
      });
      priceTextX += measureText(canvasCtx, salePrice, '28px') + 9.5;

      // 绘制第二个价格：划线价/心享价 活动门店
      // const memberPriceW = measureText(canvasCtx, `¥${goodsPrice}`, '15px');
      // 划线价（原售价）
      fillText(canvasCtx, {
        text: `¥${goodsPrice}`,
        x: priceTextX,
        y: priceTextY + 11,
        fontSize: 15,
        color: '#888888',
        lineThrough: true,
      });
      const { globalLaunchOptions: { query: { isCarryStore = 'Y' } = {} } = {} } = app.globalData
      // 如果有门店名称 则需要展示
      if (storeName && YN2Boolean(isCarryStore)) {
        // 部分安卓机型会导致右侧显示不完整 被截取的情况 所以这里增加7的左右padding以完全展示文字
        const offsetX = that.data.isIphone ? 0 : 7
        // 活动门店
        const storeNameWordlimit = storeName.length > 15 ? storeName.substring( 0,15 )+ '...' : storeName
        const storeNameW = Math.min(
          measureText(canvasCtx, `活动门店：${storeNameWordlimit}等多家门店`, '12px') + 2 * offsetX,
          maxWidth
        );
        // 矩形填充
        fillRoundRect(
          canvasCtx,
          WIDTH / 2 - storeNameW / 2,
          561,
          storeNameW,
          23,
          0,
          '#FEF6E7'
        );
        // 门店文字
        fillText(canvasCtx, {
          text: `活动门店：${storeNameWordlimit}等多家门店`,
          x: WIDTH / 2 - storeNameW / 2 + offsetX,
          y: 566,
          fontSize: 12,
          color: '#FF522C',
          width: maxWidth,
          MaxLineNumber: 1,
        });
        // 门店左右切图
        canvasCtx.drawImage(
          STORE_NAME_LEFT,
          WIDTH / 2 - storeNameW / 2 - 12,
          561,
          12,
          23
        );
        canvasCtx.drawImage(
          STORE_NAME_RIGHT,
          WIDTH / 2 + storeNameW / 2,
          561,
          12,
          23
        );
      }

      // 绘制小程序码
      canvasCtx.drawImage(codeImagePath, 228, 401, 120, 120);

      // 绘制codeText字
      fillText(canvasCtx, {
        text: '长按图片去接龙',
        x: 228 + 16,
        y: 401 + 120 + 10,
        color: '#888888',
        fontSize: 12,
        lineHeight: 10,
        MaxLineNumber: 1,
      });
      canvasCtx.setTextBaseline('top');

      // canvas 画布转为图片
      canvasCtx.draw(false, function () {
        // 判断绘制方法，判断手机类型 因为安卓手机在某种情况下会导致绘图失败，没有按照原设定参数绘制
        setTimeout(
          () => {
            wx.canvasToTempFilePath(
              {
                x: 0,
                y: 0,
                width: 750,
                height: 1304,
                quality: 0.4,
                destWidth: 750 * 1.5,
                destHeight: 1304 * 1.5,
                canvasId: 'posterCanvas',
                success: function (res) {
                  console.log(
                    `isPhone: ${that.data.isIphone}, drawposter: ${res.tempFilePath}`
                  );
                  that._data.posterPic = Object.assign(that._data.posterPic, {
                    [goodsSn]: res.tempFilePath,
                  });
                  self.showSharePoster(res.tempFilePath);
                  wx.hideLoading();
                },
                fail: function (res) {
                  wx.hideLoading();
                  wx.showLoading({
                    title: '生成海报出错啦，请稍后重试',
                  });
                },
              },
              that
            );
          },
          that.data.isIphone ? 0 : 100
        );
      });
    } catch (error) {
      console.error(error);
      wx.hideLoading();
      wx.showToast({
        title: '生成海报出错啦，请稍后重试',
      });
    }
  }
  /**
   * 分享弹窗点击事件
   * @param {Object} e 事件
   */
  showModel(e) {
    const self = this;
    const that = this.that;
    if (that.data.fromShareTimeLine) {
      wx.showToast({
      title: '请前往小程序使用完整服务',
        icon: 'none',
      });
      return;
    }
    if (!util.isEmpty(that.data.goodsDetail)) {
      const type = e.currentTarget.dataset.type;
      if (type === 'share') {
        that.setData({ isShare: true });
        self.utilShare();
      } else if (type === 'thumbnail') {
        app.subProtocolValid('shop', function () {
          that.setData({ isShare: false });
          if (!wx.showShareImageMenu) {
            that.setData({ isThumbnail: true });
          }
          self.utilThumbnail();
          // 生成海报showModel
          self.drawPoster();
        })();
      }
    }
  }
  /**
   * 关闭分享弹窗事件
   * @param {Object} e 事件
   */
  closeModel(e) {
    wx.hideLoading();
    var that = this.that;
    var type = e.currentTarget.dataset.type;
    if (type === 'share') {
      that.setData({ isShare: false });
    } else if (type === 'thumbnail') {
      that.setData({
        isThumbnail: false,
       });
    }
  }
  // 获取生成海报的图片的本地路径
  getPosterImagePath() {
    const that = this.that;
    const { avatarUrl } = wx.getStorageSync('userNameAndImg') || {};
    return new Promise((resolve, reject) => {
      const p = [];
      const { goodsSn } = that.data.goodsDetail || {};
      const { headImg } = that.data.goodsItem || {};
      const { posterPic = {}, posterImage } = that._data;
      // 若海报已经生成过了，不用重新生成
      if (posterPic.hasOwnProperty(goodsSn)) {
        that.setData({
          shareImage: posterPic[goodsSn],
        });
        wx.hideLoading();
        return;
      }

      // 生成二维码
      const p1 = this.getCode();
      p.push(p1);

      // 获取商品头图（若已经获取过，不用重新获取）
      const p2 = this.getImageInfo(headImg, 'nativeImagePath');
      p.push(p2);

      // 获取海报背景图（若已经获取过，不用重新获取）
      if (!this.checkStorageImage('bgImagePath', posterImage)) {
        const p3 = this.getImageInfo(SOLITAIRE_SHARE_POSTER, 'bgImagePath');
        p.push(p3);
      }

      // 获取门店切图（若已经获取过，不用重新获取）
      if (!this.checkStorageImage('STORE_NAME_RIGHT', posterImage)) {
        const p3 = this.getImageInfo(STORE_NAME_LEFT, 'STORE_NAME_LEFT');
        const p4 = this.getImageInfo(STORE_NAME_RIGHT, 'STORE_NAME_RIGHT');
        p.push(p3, p4);
      }
      // 获取白色闪电 白色金币 太平切图（若已经获取过，不用重新获取）
      if (!this.checkStorageImage('WHITE_MONEY', posterImage)) {
        const p5 = this.getImageInfo(WHITE_MONEY, 'WHITE_MONEY');
        const p6 = this.getImageInfo(SHARE_BALANCE, 'SHARE_BALANCE');
        p.push(p5, p6);
      }
      // 获取接龙专享图标 猴果果默认头像（若已经获取过，不用重新获取）
      if(!this.checkStorageImage('SOLITAIRE_SHARE_LCON', posterImage)) {
        const p7 = this.getImageInfo(SOLITAIRE_SHARE_LCON, 'SOLITAIRE_SHARE_LCON')
        const p8 = this.getImageInfo(AVATAR_DEFAULT,'AVATAR_DEFAULT')
        p.push(p7,p8)
      }
      if(avatarUrl){
        const p9 = this.getImageInfo(avatarUrl, 'avatarUrl')
        p.push(p9)
      }


      Promise.all(p)
        .then((res) => {
          resolve();
        })
        .catch((err) => {
          console.error(err);
          reject();
        });
    });
  }
  // 检查 storage 缓存中是否存储了 storageKey对应的图片的本地路径
  checkStorageImage(storageKey, storage) {
    return storage.hasOwnProperty(storageKey);
  }

  // 将商品图缓存
  getImageInfo(netUrl, storageKeyUrl) {
    const self = this;
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: netUrl, //请求的网络图片路径
        success: function (res) {
          //请求成功后将会生成一个本地路径即res.path,然后将该路径缓存到storageKeyUrl关键字中
          self.storageImage(res.path, storageKeyUrl);
          resolve();
        },
        fail: (res) => {
          reject(res)
        },
      });
    });
  }

  // 将获取到的图片的本地路径存储到 posterImage
  storageImage(path, key) {
    this.that._data.posterImage = Object.assign(this.that._data.posterImage, {
      [key]: path,
    });
  }
  async getDistributorsQRCode(options) {
    const {
      userID,
    } = options
    const {
      detail: { code: activityCode, name: activityName },
      // 因为打开了商品半屏弹窗才会走这里的逻辑,所以直接取goodsDetail里的商品编码
      goodsDetail: { goodsCode },
      code2Goods,
      store: { storeCode, name: storeName },
    } = this.that.getState()
    const goodsName = code2Goods[goodsCode].goodsName
    return getDistributorsQRCode.then(({ getDistributorsQRCode }) => getDistributorsQRCode({
      canvasId: 'QRCodeCanvas',
      query: {
        to: 'relayDetail',
        scene: 'relay',
        distributor: userID,
        activityCode,
        activityName,
        fromSharePage: 'goodsDetail',
        goodsCode,
        goodsName,
        // 社群分享不携带门店不会走这里的逻辑,所以直接带上storeCode和storeName
        storeCode,
        storeName,
      },
      context: this.that,
    }))
  }
  // 生成二维码
  async getCode() {
    var that = this.that;
    const isCarryStore = that.checkWithStore()
    const { userID = '' } = wx.getStorageSync('user') || {}
    const { activityCode, storeCode } = that.data.pageOptions

    const needDistributorShare = await (userID && isCarryStore ? checkUserIsDistributor() : Promise.resolve(false))

    const codeUrl = await (
      needDistributorShare
        ? this.getDistributorsQRCode({
          userID,
        })
        : getWxCodeTempFilePath({
        page: 'pages/homeDelivery/index',
        scene: `RAD@${activityCode}@${ YN2Boolean(isCarryStore) ? storeCode : '' }`,
        width: 200,
        isHyaline: false
      })
    )
    const self = this;
    return self.storageImage(codeUrl, 'codeImagePath');
  }
  mpShareReport() {
    const that = this.that;
    sensors.track('MPShare', {
      ...generateShareAttr(),
      ...relayGoodsMap.DETAIL_SHARE,
      ...that.sensorsBaseData({
        goodsCode: that.data.goodsItem.goodsCode,
        dataMode: 'share',
      }),
      shareScene: ShareScene.接龙商品,
      shareWay: ShareWay.分享海报,
    })
  }
  /**
   * 海报保存到本地
   */
  savePoster() {
    var that = this.that;
    wx.showLoading({
      title: '正在保存图片',
    });
    wx.saveImageToPhotosAlbum({
      filePath: this.data.shareImage,
      success: (res) => {
        this.mpShareReport()
        wx.hideLoading();
        wx.showToast({ title: '保存成功，快去分享吧~', icon: 'none' });
      },
      fail: (err) => {
        wx.hideLoading();
        console.log('saveImageToPhotosAlbum', err);
        commonObj.showModal(
          '提示',
          '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试',
          false,
          '我知道了',
          '',
          function (res) {
            if (res.confirm) {
              wx.openSetting();
            }
          }
        );
      },
    });
  }
  // 分享弹框
  utilShare() {
    var that = this.that;
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: 'linear',
      delay: 0,
    });
    that.animation = animation;
    animation.translateY(-158).step();
    that.setData({ animationShare: animation.export() });

    setTimeout(
      function () {
        that.setData({ animationShare: animation });
      }.bind(that),
      400
    );
  }
  // 缩略图弹框
  utilThumbnail() {
    var that = this.that;
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: 'linear',
      delay: 0,
    });
    that.animation = animation;
    animation.translateY(-parseInt(that.data.modelHeight / 2)).step();
    that.setData({ animationImage: animation.export() });

    setTimeout(
      function () {
        that.setData({ animationImage: animation });
      }.bind(that),
      300
    );
  }
  // 展示商品生成的海报
  showSharePoster(path) {
    if (!wx.showShareImageMenu) {
      this.that.setData({
        shareImage: path,
      });
    } else {
      wx.showShareImageMenu({
        path: path,
        style: 'v2',
        success: () => {
          this.mpShareReport()
          // wx.showToast({ title: '保存成功，快去分享吧~', icon: 'none' })
        },
        fail: async (err) => {
          if (err.errMsg === 'showShareImageMenu:fail cancel') {
            return
          }
          if (err.errMsg === 'showShareImageMenu:fail forbidden' && isDistributorFromCache()) {
            // 暂时不支持团长分佣二维码分享到朋友圈
            // 使用微信自带的toast
            return
          }
          const res = await app.showModalPromise({
            content: '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试',
            showCancel: false,
            confirmText: '我知道了',
          })
          if (res) wx.openSetting()
        }
      });
    }
  }
  /**
   * @desc 更新用户信息
   */
  updateUserInfo() {
    const isLogin = app.checkSignInsStatus()
    return{
      isLogin
    }
  }
  /**
   * 用户截屏事件
   */
  async captureScreenEvent() {
    this.drawPoster()
  }
}
