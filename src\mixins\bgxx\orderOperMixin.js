// 订单操作相关公用函数
// 使用场景： 订单详情页/订单列表页(./orderListMixin.js)
const app = getApp(),
  common = require('../../source/js/common').commonObj
const { checkPayDevice } = require('../../service/userService')
const { ORDER_TYPE } = require('../../source/const/order')
import shopCartStore from '../../stores/shopCartStore'
const sms = require.async('../../sourceSubPackage/commonUtils/sms/index')

module.exports = {
  data: {
    // 是否显示短信验证码输入框
    visibleSMS: false
  },
  _data: {
    tmpConfirmHandle: null
  },
  // 跳转支付loading
  redirectToPayLoading(paymentOrderID) {
    wx.redirectTo({
      url: `/bgxxShop/pages/payloading/index?paymentOrderID=${paymentOrderID}`,
    })
  },
  prevent() {
    // 就单纯的阻止事件冒泡函数
  },
  // 错误处理
  handleError(err) {
    const { description = '系统繁忙，请稍后重试', errorCode } = err
    app.apiErrorDialog({
      errorCode,
      description
    })
  },

  // 支付类型选项
  handlePayTypeChange({ detail }) {
    this.setData({
      payType: detail.value
    })
  },

  // 取消未支付订单
  chanelNoPayOrderRequest(userID, payOrderID, suc, fail, finallyFn) {
    app.api.bgxxChanelNoPayOrder(userID, payOrderID).then(res => {
      wx.showToast({
        title: '取消成功',
        icon: 'none',
        duration: 500
      })
      setTimeout((res) => {
        suc && suc(res)
      }, 500)
    }).catch(err => fail && fail(err)).finally(() => {
      finallyFn && finallyFn()
    })
  },

  // 申请售后
  toPostSale(payAmount, orderID, orderNum, channel = '', sensorData = {}) {
    wx.navigateTo({
      url: '/bgxxUser/pages/requestPostSale/index?orderInfo=' + JSON.stringify({
        payAmount,
        orderID,
        orderNum,
        refundChannel: channel.indexOf('gb') > -1 ? 1 : 2,
        sensorData
      })
    })
  },

  // 再次购买
  bgxxBuyAgain({isPay, goodsOrderID, payOrderID, goodsList, type} = {}) {
    const freshBuyAgainInfo = {
      goodsList: [],
      type
    }
    // type类型：1为需要根据旧电商商品id来查询商品编码，2为不需要
    const obj = {}
    const key = Number(type) === 1 ? 'goodsID' : 'goodsNumber'
    // 同一个sku去重，商品数量累加处理
    goodsList.forEach(v => obj[v[key]] = obj[v[key]] === void 0 ? Number(v.count) : Number(obj[v[key]]) + Number(v.count))
    Object.keys(obj).forEach(val => {
      freshBuyAgainInfo.goodsList.push({
        goodsSn: Number(type) === 2 ? String(val) : '',
        eshopGoodsId: Number(type) === 1 ? String(val) : '',
        count: obj[val]
      })
    })
    if (isPay) {
      app.globalData.payOrderID = goodsOrderID
      app.globalData.isPay = true
    } else {
      app.globalData.payOrderID = payOrderID
      app.globalData.isPay = false
    }
    app.globalData.freshBuyAgainInfo = freshBuyAgainInfo
    // 原次日达购物车改为及时达购物车
    wx.switchTab({
      url: '/pages/shopCart/index'
    })
  },
  // 获取支付公用信息 2020-04-02 15:21:51
  getPayInfo(timeExpire) {
    const { userID, phoneNumber } = this.data.user
    const { openid = '' } = wx.getStorageSync('wxSnsInfo') || {}
    const [date, time] = timeExpire.split(' ')
    const [year, month, day] = date.split('-')
    const [h, m, s] = time.split(':')
    return {
      userID,
      phoneNumber,
      timeExpire: `${year}${month}${day}${h}${m}${s}`,
      openID: openid,
    }
  },
  // 余额支付
  async payByGB(info = {}, suc, fail) {
    const confirmHandle = () => {
      wx.showLoading({ title: '支付中' })
      const { timeExpire, openID, payAmount, userID, payOrderNum, payOrderID} = info
      const params = {
        payTitle: '心享订单',
        desc: '心享商品',
        payAmount,
        timeExpire,
        openID
      }
      app.api.bgxxPayGB(userID, payOrderNum, params).then(res => {
        wx.hideLoading()
        suc && suc(res)
        this.redirectToPayLoading(payOrderID)
      }).catch(err => {
        wx.hideLoading()
        fail && fail(err)
      })
    }
    // 校验是否需要进入验证码环节
    const isNeedValidate = await checkPayDevice()

    if (!isNeedValidate) {
      // 缓存一下提交订单的请求
      this._data.tmpConfirmHandle = confirmHandle
      // 弹出验证码输入框
      this.showSmsValidate()
      return
    }
    // 不校验验证码走此处逻辑
    confirmHandle()
  },
  // 微信支付
  payByWechat(info = {}, suc, fail) {
    wx.showLoading({ title: '支付中' })
    const { timeExpire, openID, payOrderNum: payOrderNo, payAmount, userID, phoneNumber, payOrderID } = info
    const params = {
      channel: 'WX_MINI',
      subject: '心享订单',
      body: '心享商品',
      version: '*******',
      source: 'WX_XX',
      clientIP: 'ip',
      orderType: 'CONSUME_GOODS',
      payOrderNo,
      payAmount,
      customerID: userID,
      mobile: phoneNumber,
      timeExpire,
      openID
    }
    app.api.bgxxPayWechat(userID, {
      data: common.Encrypt(params, wx.getStorageSync('token'))
    }).then(res => {
      wx.hideLoading()
      const depositObj = JSON.parse(common.Decrypt(res.data, wx.getStorageSync('token')));
      wx.requestPayment({
        'timeStamp': depositObj.timeStamp,
        'nonceStr': depositObj.nonceStr,
        'package': `prepay_id=${depositObj.prepayId}`,
        'signType': 'MD5',
        'paySign': depositObj.signStr,
        'success': (res) => {
          suc && suc(res)
          this.redirectToPayLoading(payOrderID)
        },
        'fail': (res) => {
          const title = res.errMsg.indexOf('cancel') > -1 ? '支付取消' : '支付失败'
          wx.showToast({
            title,
            icon: 'loading',
            duration: 2000
          })
        },
        'complete': function (res) {
        }
      })
    }).catch(err => {
      wx.hideLoading()
      fail && fail(err)
    })
  },
  /**
   * 父组件接受到短信验证弹窗的值
   * @param {Boolean} e Event
   */
  validated(e) {
    // 通过校验直接允许支付
    if (e.detail.validated) {
      this.setData({
        visibleSMS: false
      })
      // 使用钱包支付
      if (this._data.tmpConfirmHandle) {
        this._data.tmpConfirmHandle()
        this._data.tmpConfirmHandle = null
      }
    }
  },
  /**
   * 父组件接受到返回事件
   * @param {Boolean} e Event
   */
  onBack(e) {
    // 用户点击返回
    if (e.detail.back) {
      this.setData({
        visibleSMS: false
      })
    }
  },
  async showSmsValidate() {
    const { CaptchaHandle } = await sms
    CaptchaHandle.selectComp(this)
    this.setData({
      visibleSMS: true,
    })
  },
}
