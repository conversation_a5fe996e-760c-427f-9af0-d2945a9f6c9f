const sensors = require('../../../utils/report/sensors')
const { prdUrl } = require('../../../utils/config');
const util = require('../../../utils/util');
const GOODSIMAGE = require('../../../source/const/goodsImage');
const app = getApp();
Page({
  data: {
    couponList: [],
    unAvailableCouponList: [],
    noCouponImage: GOODSIMAGE.NO_ACT, // 优惠券缺省图
  },
  _data: {
    couponRules: [], // 优惠券同类型互斥规则 比如：["C,K,F,D", "M"] C,K,F,D类型优惠券不能同时选中
    // isGrayUser: false,
    // C,1：满减 K,2：满折 F,4：立折 D,3：立减 M,5：免运费
    newCouponRules: ["1,2,3,4","5"] // 以前规则是后端返回的，现在写死在前端。免运券+其他任一一张券
  },
  onShow () {
    // 浏览页面上报神策
    sensors.pageScreenView()
    
    // this._data.isGrayUser = app.checkIsGray()
    if (app.checkSignInsStatus()) {
      this.setInitCouponData()
    } else {
      app.showSignInModal()
    }
  },
  /**
   * 设置初始优惠券数据
   */
  setInitCouponData() {
    let {
      couponRules = [],
      couponList = [],
      unAvailableCouponList = [],
      selectedCouponList = []
    } = this.invokeLastPageMethod('getCouponInfo') || {}
    // 灰度用户取新的规则
    couponRules = this._data.newCouponRules
    couponList.forEach(item => {
      if (!selectedCouponList || !selectedCouponList.length) {
        item.isSelected = 0
      } else {
        const selectItem = selectedCouponList.find(obj => obj.couponCode === item.couponCode)
        if (selectItem) {
          item.isSelected = 1
        } else {
          item.isSelected = 0
        }
      }
    })
    let permuteInfo = [...util.permute(['A','I','W']),...util.permute(['A','I']),...util.permute(['A','W']),'A']
    this.setData({
      couponList,
      unAvailableCouponList:util.filterChannelSeparation(unAvailableCouponList,'channelSeparation',permuteInfo),
    })
    this._data.couponRules = couponRules
  },

  invokeLastPageMethod(methodName, ...args) {
    const pages = getCurrentPages()
    if (!pages || pages.length < 2) {
      return
    }
    const prevPage = pages[pages.length - 2]
    return prevPage[methodName](...args)
  },

  /**
   * 返回确认订单页
   */
  couponPipe() {
    const { couponList } = this.data
    const selectedCouponList = couponList.filter(item => !!item.isSelected)
    this.backConfirmOrder(selectedCouponList)
  },

  /**
   * 不使用优惠券
   */
  noUseCoupon (){
    this.backConfirmOrder([])
  },

  backConfirmOrder(selectedCouponList) {
    this.invokeLastPageMethod('setSelectedCoupon', selectedCouponList)
    wx.navigateBack()
  },

  /**
   * 取消选中，设置选中
   */
  selectChange(e){
    let { couponCode, isSelected, initCouponWay: oldCouponWay, couponWay } = e.detail || {}
    // 灰度用户兼容新字段
    // let initCouponWay = this._data.isGrayUser ? couponWay: oldCouponWay
    let initCouponWay = couponWay

    const list = this.data.couponList
    if (!!isSelected) {
      // 取消选中
      const index = list.findIndex(item => String(item.couponCode) === String(couponCode))
      if (index !== -1) {
          this.setData({
            [`couponList[${index}].isSelected`]: 0
          })
      }
    } else {
      // 选中
      const currRules = this._data.couponRules.find(rules => rules.includes(initCouponWay))
      list.forEach(item => {
        const {couponCode: itemCouponCode, initCouponWay: oldCouponWay, couponWay } = item
        // let itemInitCouponWay = this._data.isGrayUser ? couponWay: oldCouponWay
        let itemInitCouponWay = couponWay
        if (String(itemCouponCode) === String(couponCode)) {
            item.isSelected = 1
        } else {
          if (currRules) {
            // 如果当前优惠券类型在互斥规则里面，且被选中应该被取消选中
            if (currRules.includes(itemInitCouponWay) && item.isSelected) {
              item.isSelected = 0
            }
          }
        }
      })
      this.setData({
        couponList: list
      })
    } 
  }
})