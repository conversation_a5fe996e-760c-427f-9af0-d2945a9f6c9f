/**
 * 环境变量处理
 * 没有使用其他构建工具，无法注入环境变量，所以只能手动添加环境变量文件，然后在构建完成之后删除
 */

const fs = require('fs/promises')
const path = require('path')

const envPath = path.resolve(__dirname, '../../src/env.js')
async function writeEnvFile(env) {
  // 写入失败直接抛错中断程序
  const content = `module.exports = { env: '${env}' }`
  await fs.writeFile(envPath, content)
  // try {
  //   const content = `module.exports = { env: '${env}' }`
  //   await fs.writeFile(envPath, content)
  // } catch (error) {
  //   console.log('writeEnvFile error', error)
  // }
}

async function removeEnvFile() {
  try {
    await fs.rm(envPath)
  } catch (error) {
    console.log('removeEnvFile error', error)
  }
}

function handleEnvFileAOP(fn, env) {
  return async function (...args) {
    await writeEnvFile(env)
    await fn.apply(this, args)
    await removeEnvFile()
  }
}

module.exports = {
  writeEnvFile,
  removeEnvFile,
  handleEnvFileAOP
}
