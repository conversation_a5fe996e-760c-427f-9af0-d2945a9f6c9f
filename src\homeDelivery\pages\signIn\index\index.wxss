/* pages/signIn/index.wxss */
.signin-warpper {
  height: 100%;
}
.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 168rpx;
  height: 168rpx;
  margin: 96rpx auto 43rpx auto;
}

.slogan {
  width: 460rpx;
  height: 31rpx;
}

.captions {
  padding: 80rpx 75rpx 24rpx;
  font-size: 26rpx;
  line-height: 40rpx;
  color: #7b7b7b;
}

.btn {
  box-sizing: border-box;
  line-height: 96rpx;
  /* width: 590rpx; */
  margin: 0 50rpx;
  background-color: #fff;
  border: 1rpx solid #00A34F;
  color: #00A34F;
  border-radius: 48rpx;
  font-size: 32rpx;
  text-align: center;
}

.btn.bg-green {
  background-color: #00A34F;
  border: none;
  color: #fff;
}

.btn-authorization {
  margin-top: 50rpx;
}

/* .chose-wrapper {
  opacity: 1;
  animation-name: opacitytrans;
  animation-delay: 0.5;
} */

.btn-signin-wrapper {
  margin-top: 100rpx;
}

.btn-signin-wrapper .btn + .btn {
  margin-top: 24rpx;
}

.icon {
  width: 36rpx;
  height: 36rpx;
  vertical-align: middle;
  margin-right: 10rpx;
}
/* 提示 */
.tips-wrapper {
  margin: 40rpx 40rpx 0;
  font-size: 24rpx;
  line-height: 30rpx;
  display: flex;
}

.tips-radio{
  position: relative;
}

.btn-cancel {
  margin-top: 30rpx;
  text-align: center;
}

button.btn[disabled] {
  color: #019B50;
  background-color: #fff;
}

button.bg-green[disabled] {
  color: #fff;
  background-color: #019B50;
}

radio .wx-radio-input{
  border-radius: 50%;/* 圆角 */
  width: 24rpx;
  height: 24rpx;
  transform:translateY(-3rpx);
}
/* 选中后的 背景样式 （红色背景 无边框 可根据UI需求自己修改） */
radio .wx-radio-input.wx-radio-input-checked{
  /* border: none; */
  /* background: red; */
}
/* 选中后的 对勾样式 （白色对勾 可根据UI需求自己修改） */
radio .wx-radio-input.wx-radio-input-checked::before{
  border-radius: 50%;/* 圆角 */
  width: 24rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
  height: 24rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
  line-height: 24rpx;
  text-align: center;
  font-size:24rpx;
  /* color:#fff; 对勾颜色 白色 */
  background: transparent;
  transform:translate(-50%, -50%) scale(1);
  -webkit-transform:translate(-50%, -50%) scale(1);
}
.sign-btn-row {
  margin-top: 40rpx;
  flex-direction: column;
}
.phone-number-sigin {
  width: 100%;
  position: fixed;
  bottom: 64rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}
.phone-number-sigin, .phone-number-img {
  display: flex;
  align-items: center;
  flex-direction: column;
}
.phone .line {
  display: inline-block;
  width: 210rpx;
  height: 1rpx;
  background: #ccc;
}
.phone .phone-text {
  margin: 0 14rpx;
  font-size: 28rpx;
  color: #888;
}
.phone-number-img image {
  margin: 29rpx 0 12rpx 0;
  width: 79rpx;
  height: 79rpx;
}
.phone-number-img text {
  font-size: 28rpx;
  color: #888;
}
