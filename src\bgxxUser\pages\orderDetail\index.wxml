<!--bgxxUser/pages/orderDetail/index.wxml-->
<wxs module="common" src="../../../utils/common.wxs"></wxs>
<main-page currentView="content" />
<view class='order-detail-page'>
  <nav-bar
    normalBack
    background="{{navBarBgColor}}"
    color="{{navBarColor}}"
    navBarTitle="订单详情"
    bindnavBarHeight="getNavBarHeight"
  >
    <view slot="back" class="nav-back" style="filter: brightness({{backFilter}})">
      <image src="/source/images/arrow_back.png" />
    </view>

  </nav-bar>
  <!-- <view slot="content" class="nav-title">订单详情</view> -->
  <view class='order' style="margin-top: -{{navBarHeight}}px">
    <!-- 交易状态 -->
    <view style="background-image:url('{{bg_detail_top}}');padding-top: {{navBarHeight-51}}px;" class='bg-detail-top flex-center'>
      <view class="bg-detail-top-cover"></view>
      <view class="order-tip">
        <view class='order-status' bindtap="showStatusList">
          <image wx:if="{{orderStatusInfo.statusIcon}}" src='{{orderStatusInfo.statusIcon}}' class='status-img'></image>
          <text class='status-describe'>{{orderStatusInfo.orderStatus}}</text>
          <image wx:if="{{orderStatusInfo.orderStatus}}" class="img-show-list" src="/source/images/arrow_back.png" />
        </view>
        <!-- <view class='time-font' wx:if="{{isShowOrderTime}}">付款剩余时间: {{min}}:{{second}}</view> -->
      </view>
      <view class='status-info'>{{orderStatusInfo.orderDesc}}</view>
    </view>
    <!-- 三无退货信息 -->
    <view class="refund-info mrb-16" wx:if="{{refundTips}}">
      <view class="flex a_t s_b">
        <view class="title">三无退货</view>
        <view bindtap="goRefundDetail" style="display: flex;align-items: center;">
          <text class="detail">查看详情</text>
          <image class="icon" src="../../source/images/icon_20_right_black.png"></image>
        </view>
      </view>
      <view class="tips">{{refundTips}}</view>
    </view>
    <!-- 缺货退款信息 -->
    <view class="refund-info mrb-16" wx:if="{{!!doubleRefundGoodsCount}}">
      <view class="flex a_t s_b">
        <view bindtap="openRefundDialog">
          <text class="title">缺货退款</text>
          <image class="refund-tips-icon" src="../../source/images/icon_remind_circle_gary.png" />
        </view>
        <view wx:if="{{ orderDetail.channel === 'gb' }}" bindtap="goBaldetail">
          <text class="detail">查看详情</text>
          <image class="icon" src="../../source/images/icon_20_right_black.png"></image>
        </view>
      </view>
      <view class="tips tips-con">非常抱歉，您订单中有<text class="tips">{{doubleRefundGoodsCount}}种</text>商品缺货，退款<text class="tips">￥{{common.formatPrice(doubleRefundAmount)}}</text>{{orderDetail.arrivedAccount ? '已' : '将'}}退回{{ orderDetail.channel === 'gb' ? '会员钱包' : '原支付账户' }}。</view>
    </view>
    <!-- 配送/自提 用户、地址信息 -->
    <view class='address-border mrb-24 radius-16 pdt-24 pdb-24'>
      <view class='address'>
        <!-- 自提订单信息 -->
        <view class='address-info' wx:if="{{deliveryWay == 1}}">
          <view class="shopping-bag-tip" wx:if="{{orderStatusInfo.orderFlowState!=='CANCELED'}}">
            <!-- <image src="../../source/images/icon_tips.png"></image> -->
            <text>助力环保，人人有责，提货请自备购物袋或到店购买哦~</text>
          </view>
          <!-- <cell
            leftText="自提点信息"
            rightHorAlign="left"
          >
            <view slot="right">
              <view class='address-store-name txt-ellipsis'>{{orderStoreInfo.storeName}}</view>
              <view class='address-detail'>{{orderStoreInfo.address}}</view>
              <view class='address-store-time' wx:if="{{orderStoreInfo.openingTime}}">营业时间：{{orderStoreInfo.openingTime}}</view>
            </view>
          </cell> -->
          <view class="store-item">
            <view class='address-store-name txt-ellipsis'>{{orderStoreInfo.storeName}}</view>
            <view class="item" bindtap='openStoreLoaction' data-storeinfo="1" >
              <image src="../../source/images/icon_nav.png"></image>
              <text>导航到店</text>
            </view>
          </view>
          <view class="store-item">
            <view class='address-detail'>{{orderStoreInfo.address}} <span wx:if="{{orderStoreInfo.storeBusinessTime}}">(营业时间：{{orderStoreInfo.storeBusinessTime}})</span></view>
            <view class="item" bindtap='connectStore'>
              <image src="../../source/images/icon_tel.png"></image>
              <text>联系商家</text>
            </view>
          </view>
          <!-- <view class='address-store-time' wx:if="{{orderStoreInfo.openingTime}}">营业时间：{{orderStoreInfo.openingTime}}</view> -->
          <!-- 预约时间 -->
          <cell
            wx:if="{{orderStatusInfo.deliveryTime}}"
            leftText="预约时间"
            rightHorAlign="left"
          >
            <view slot="right">
              <view style="font-weight: bold">{{orderStatusInfo.deliveryTime}}</view>
            </view>
          </cell>
        </view>
        <!-- 配送订单信息 -->
        <view class='address-info' wx:else>
          <!-- 收货信息 -->
          <!-- <cell
            leftText="收货人信息"
            rightHorAlign="left"
          >
            <view slot="right">
              <view style="font-weight: bold">
                <text style="margin-right: 24rpx">{{receiverAddressInfo.userName}}</text>
                <text >{{common.formatPhoneNumber_344(receiverAddressInfo.phoneNumber)}}</text>
              </view>
              <view class='user-address mrb-20'>{{receiverAddressInfo.address}}</view>
            </view>
          </cell> -->
          <view class="store-item">
            <view class="user-info txt-ellipsis">
              <text>{{receiverAddressInfo.userName}}</text>
              <text>{{common.formatPhoneNumber_344(receiverAddressInfo.phoneNumber)}}</text>
            </view>
            <view class="item" bindtap='openStoreLoaction' data-storeinfo="1" >
              <image src="../../source/images/icon_nav.png"></image>
              <text>导航到店</text>
            </view>
          </view>
          <view class="store-item">
            <view class='address-detail'>{{receiverAddressInfo.address}}</view>
            <view class="item" bindtap='connectStore'>
              <image src="../../source/images/icon_tel.png"></image>
              <text>联系商家</text>
            </view>
          </view>
          <!-- 配送信息 -->
          <cell
            leftText="配送信息"
            rightHorAlign="left"
          >
            <view slot="right" style="font-weight: bold">由{{orderStoreInfo.storeName}}送出</view>
          </cell>
          <!-- 预约时间 -->
          <cell
            wx:if="{{orderStatusInfo.deliverySection}}"
            leftText="预约时间"
            rightHorAlign="left"
          >
            <!-- 自提单预约时间取deliveryTime；配送单预约时间取deliverySection -->
            <view slot="right" style="font-weight: bold">
            {{orderStatusInfo.deliverySection}}
            <text style="font-weight: bold" wx:if="{{orderStatusInfo.deliveryTimeBegin && orderStatusInfo.deliveryTimeEnd}}">{{common.formatDeliveryTime(orderStatusInfo.deliveryTimeBegin)}}-{{common.formatDeliveryTime(orderStatusInfo.deliveryTimeEnd)}}</text>
            </view>
          </cell>
        </view>
      </view>
      <!-- 下方门店导航栏 -->
      <!-- <view class='address-operation flexs a_t s_e'>
        <view class="flexs a_t j_c" bindtap='connectStore'>
          <image class='icon'src="/source/images/IconTel.png"></image>
          <text>联系商家</text>
        </view>
        <view class="divider"></view>
        <view class="flexs a_t j_c" bindtap='openStoreLoaction' data-storeinfo="1" >
          <image class='icon'src="/source/images/IconNav.png"></image>
          <text>导航到店</text>
        </view>
      </view> -->
      <!-- 分隔线 -->
      <view class="address-divide flexs a_t s_a" wx:if="{{showDeliveryInfo}}">
        <view class="divide-circle left-circle"></view>
        <view class="divide-circle right-circle"></view>
        <view class="divide-line"></view>
      </view>
      <!-- 提货信息 -->
      <view class='delivery' wx:if="{{showDeliveryInfo}}">
        <view class='delivery-tips'>
          <view class="delivery-tips-font">
            <view class="delivery-tips-below">
              <view >*请于计划时间内到 <text >{{orderStoreInfo.storeName}}</text> 提货，如您未及时取走，系统将自动完结订单</view>
            </view>
          </view>
        </view>
        <view class='delivery-code'>
          <image wx:if='{{!!deliveryCanvasImage}}' src='{{deliveryCanvasImage}}'></image>
          <view class="code-info">
            <view class="code">提货码：{{pickUpOrderInfo.takeCode}}</view>
            <!-- <view class="code">流水号：{{pickUpOrderInfo.serialNumber}}</view> -->
          </view>
        </view>
      </view>
    </view>

    <!-- 商品信息 -->
    <!-- 现货 -->
    <view class="goods-list-wrapper mrb-16 radius-16" wx:if="{{goodsList.list.length > 0}}">
      <cell border>
        <view slot="left">
          <view class="order-info-title">{{orderGoodsType[goodsList.orderSubType]}}</view>
        </view>
        <view slot="right" class="flex a_t">
          <view class="delivery-time" wx:if="{{orderStatusInfo.orderStatus === '待付款' && deliveryWay == 1}}">提货时间：{{goodsList.deliveryTime}}</view>
          <view class="delivery-time" wx:if="{{orderStatusInfo.orderStatus === '待付款' && deliveryWay == 2 && !selectDeliveryTime}}">{{goodsList.deliverySection}}</view>
          <view class="time-picker" wx:if="{{selectDeliveryTime}}" data-item="{{goodsList.deliveryTimeRange}}" catchtap="pickerOnSaleDelivery">
            <text class="select-delivery-time">{{ goodsList.showDeliveryTime || '请选择送达时间'}}</text>
            <text class="time-arrow">></text>
          </view>
        </view>
      </cell>
      <goods-list border="{{false}}" needStorageType="{{false}}" list="{{goodsList.list}}" ignoreVipStatus="{{true}}" binditemtap="navigate" orderSubType="{{goodsList.orderSubType==='G'}}"></goods-list>
    </view>

    <!-- 预定 -->
    <!-- 和产品确认过，不会有周期购 -->
    <view class="goods-list-wrapper mrb-16 radius-16" wx:if="{{!isPay && preGoodsList.length > 0}}" wx:for="{{preGoodsList}}" wx:for-item="itemName" wx:key="itemName">
      <view class="goods-list-header cell">
        <text class="title">{{orderGoodsType[itemName.orderSubType]}}</text>
        <text class="delivery-time" wx:if="{{orderStatusInfo.orderStatus === '待付款' && deliveryWay == 1}}">提货时间：{{itemName.deliveryTime}}</text>
        <text class="delivery-time" wx:if="{{orderStatusInfo.orderStatus === '待付款' && deliveryWay == 2}}">{{itemName.orderSubType === 'H' ? '首次配送' : ''}}{{itemName.deliverySection}}</text>
        <!-- <view wx:if="{{selectDeliveryTime}}" class="time-picker" data-item="{{itemName.deliveryTimeRange}}" data-index="{{index}}" catchtap="pickerPreSaleDelivery">
          <text class="select-delivery-time">{{ itemName.showDeliveryTime || '请选择送达时间'}}</text>
          <text class="time-arrow">></text>
        </view> -->
      </view>
      <goods-list list="{{itemName.goodsList}}" needStorageType="{{false}}" ignoreVipStatus="{{true}}" binditemtap="navigate" orderSubType="{{itemName.orderSubType==='G'}}"></goods-list>
    </view>

    <!-- 金额信息 -->
    <view class="order-price mrb-16 radius-16 pdt-24 pdb-24">
      <!-- 商品总额 -->
      <cell
        leftText="商品总额"
        rightText="¥{{(orderPriceInfo.totalAmount || 0) * 1}}"
      ></cell>
      <!-- 包装费 -->
      <view class="packing-cost" wx:if="{{orderPriceInfo.packAmount > 0 }}">
        <view class="left-text" catchtap="showCostTips">
          <text>包装费</text>
          <image src="../../source/images/icon_remind_circle_gary.png"/>
        </view>
        <view class="right-text">
          <text>¥{{orderPriceInfo.packAmount*1}}</text>
        </view>
      </view>
      <!-- 配送费 -->
      <cell
        wx:if="{{deliveryWay == 2}}"
        leftText="配送费(总重{{orderPriceInfo.weightCount}}kg)"
        rightText="¥{{orderPriceInfo.freight*1}}"
      ></cell>
      <!-- 优惠券 -->
      <block wx:for="{{orderPriceInfo.couponList}}" wx:key="index">
        <cell
          leftText="{{item.couponName}}"
          rightText="-¥{{item.couponValue*1}}"
        ></cell>
      </block>
      <!-- 优惠金额 -->
      <cell
        wx:if="{{orderPriceInfo.newVipDiscountAmount}}"
        leftText="新客满折"
        rightText="-¥{{orderPriceInfo.newVipDiscountAmount*1}}"
      ></cell>
      <!-- 一元购优惠金额 -->
      <cell
        wx:if="{{showActivityAmount && orderPriceInfo.totalOneBuyActivityPrice}}"
        leftText="优惠活动"
        rightText="-¥{{orderPriceInfo.totalOneBuyActivityPrice*1}}"
      ></cell>
      <!-- 特价活动优惠金额 -->
      <cell
        wx:if="{{orderPriceInfo && orderPriceInfo.totalBargainGoodsCoupon}}"
        leftText="特价优惠"
        rightText="-¥{{orderPriceInfo.totalBargainGoodsCoupon * 1}}"
      ></cell>
      <!-- 实付金额 -->
      <cell>
        <!-- 主题色 app.wxss bgxxThemeColor: #FF1F3A; -->
        <view slot="right">
          <text style="font-weight: bold">实付：</text>
          <text class="actual-price">¥{{orderPriceInfo.payAmount || 0}}</text>
        </view>
      </cell>
    </view>
    <!-- vip省钱模块 -->
    <view
      wx:if="{{isSuperVip && orderPriceInfo.freeValue && orderStatusInfo.orderFlowState!=='CANCELED'}}"
      class="vip-section">
      <vip-section
        static-text
        tips="本单已省{{orderPriceInfo.freeValue}}元，了解更多省钱权益"
        btn="查看"
        bind:vipTipsClick="showVipDetail" />
    </view>
    <!-- 订单信息 -->
    <view class="order-info-list radius-16">

      <cell border>
        <view slot="left" class="order-info-title">订单信息</view>
      </cell>
      <view class='order-info'>
        <cell
          wx:if="{{isPay}}"
          leftText="订单编号"
          leftColor="#858996"
          rightHorAlign="left"
        >
          <view class="flex a_t" slot="right">
            <text >{{orderInfoAndTime.orderNum}}</text>
            <view class="order-info-operation" data-orderid="{{orderInfoAndTime.orderNum}}" bindtap='copyOrderId'>复制</view>
          </view>
        </cell>
        <cell
          leftText="下单时间"
          leftColor="#858996"
          rightText="{{orderInfoAndTime.createTime}}"
          rightHorAlign="left"
        ></cell>
        <cell
          wx:if="{{isPay}}"
          leftText="支付方式"
          leftColor="#858996"
          rightText="{{payChannel}}"
          rightHorAlign="left"
        ></cell>

        <cell
          wx:if="{{canInvoice && isGBPay}}"
          leftText="发票信息"
          leftColor="#858996"
          rightText="该订单使用钱包余额支付，不可开票"
          rightHorAlign="left"
        ></cell>
      </view>
    </view>
    <!-- 背书信息 -->
    <view class="buttomTips">
      <image src="https://resource.pagoda.com.cn/dsxcx/images/c8248a1d2b2d9f2c9f0651d5c71d7b53.png" mode="widthFix" />
    </view>
  </view>

  <view class="order-btn-group safe-area-inset-bottom">
    <!-- 更多按钮组件 -->
    <order-detail-more-btn
      wx:if="{{ leftBtnList && leftBtnList.length }}"
      menuList="{{ leftBtnList }}"
      >
    </order-detail-more-btn>
    <!-- 更多按钮组件 -->

    <!-- 旧按钮，在原型中无出现的情况，但防止出现线上意外，仍然保留 -->
    <block wx:if="{{orderStatusInfo.orderSubType !== 'I' && orderStatusInfo.orderSubType !== 'J'}}">
      <view
        class='order-btn'
        wx:if="{{isEachDetails === 'Y'}}"
        catchtap='toinstallmentDetail'>
        每期详情
      </view>
    </block>
    <!-- 旧按钮，在原型中无出现的情况，但防止出现线上意外，仍然保留 -->

    <!-- 循环按钮组 -->
    <block wx:for="{{ btnList }}" wx:key="btnType" wx:for-item="btnItem">
      <view data-type="{{ btnItem.btnType }}"
            data-orderInfo="{{item}}"
            class="order-btn {{ btnItem.class }}"
            catchtap="handleBtn">

            <block wx:if="{{ btnItem.btnType === 'payImmediatelyBtn' }}">
                立即支付{{min}}:{{second}}
            </block>
            <block wx:else>
                {{ btnItem.btnName }}
            </block>
      </view>
    </block>
    <!-- 循环按钮组 -->

  </view>
  <!-- 待付款支付弹窗 -->
  <view class='popup-mask' wx:if="{{hasPayPopup}}" catchtap='closePayPopup'>
    <view class='pay-popup' catchtap='prevent'>
      <view class="pay-top-menu">
        <view bindtap='closePayPopup'><image class="pay-top-menu-img" src='../../source/images/icon_close_zhifu.png'></image></view>
        选择支付方式
      </view>
      <view class='pay-detail-price'>
        <view class='pay-detail-info'>应付金额</view>
        <view class='pay-price'><label class='rmb'>¥</label>{{orderPriceInfo.payAmount}}</view>
      </view>
      <!-- 支付方式 -->
      <view class="cell pay-type mrb-90">
       <view class="pay-type-item">
        <view class="{{orderPriceInfo.balanceInfo.enough ? '' : 'disabled'}}" style="display: flex;align-items: center">
            <image src="../../source/images/icon_pagodapay.png" style="margin-right: 8rpx;width: 44rpx;height: 44rpx"></image>
            <text>会员钱包支付</text>
            <text class="balance">(余额:￥{{orderPriceInfo.balanceInfo.count}})</text>
          </view>
          <w-checkbox
            wx:if="{{orderPriceInfo.balanceInfo.enough}}"
            value="{{0}}"
            checked="{{payType == 0}}"
            bindchange="handlePayTypeChange"
          ></w-checkbox>
          <text wx:else class="disabled-text">余额不足</text>
        </view>
        <view class="pay-type-item">
          <view style="display: flex;align-items: center">
            <image src="../../source/images/icon_wechat.png" style="margin-right: 8rpx;width: 44rpx;height: 44rpx"></image>
            <text>微信支付</text>
          </view>
          <w-checkbox value="{{1}}" checked="{{payType == 1}}" bindchange="handlePayTypeChange"></w-checkbox>
        </view>
      </view>
      <!-- 支付按钮 -->
      <view class="pay-btn {{isIphoneX ? 'mrb-62' : ''}}" catchtap='payBtn'>确认支付</view>
      <view wx:if="{{isIphoneX}}" class="iphonex-height"></view>
    </view>
  </view>

  <!-- 取消提示弹窗 -->
  <view class="popup-mask popup-flex" wx:if="{{hasChanelPopup}}" catchtap="closePopup" data-modalFlag="chanel">
    <view class='popup' catchtap='prevent'>
      <view class="popup-content chanel-content">您精挑细选的商品真的要取消吗？</view>
      <view class='popup-btns-group'>
        <view class='popup-btn' catchtap="closePopup" data-modalFlag="chanel">考虑一下</view>
        <!-- <view class='popup-btn btn-primary' bindtap="{{isPay ? 'orderPayOrder' : 'chanelNoPayOrder'}}">残忍取消</view> -->
        <view class='popup-btn-cancel btn-primary' wx:if="{{isPay}}" bindtap='orderPayOrder'>
          <button form-type="submit" class="btn-reset">残忍取消</button>
        </view>
        <view class='popup-btn btn-primary bgxx-theme-color' wx:else bindtap='chanelNoPayOrder'>残忍取消</view>
      </view>
    </view>
  </view>

  <!-- 确认收货弹窗 -->
  <view class="popup-mask popup-flex" wx:if="{{hasConfirmPopup}}" catchtap="closePopup" data-modalFlag="confirm">
    <view class='popup' catchtap='prevent'>
      <view class="popup-content chanel-content">您确认已收到货品了吗？</view>
      <view class='popup-btns-group'>
        <view class='popup-btn-cancel br-2' catchtap="closePopup" data-modalFlag="confirm">取消</view>
        <view class='popup-btn-cancel btn-primary' bindtap='confirmOrder'>
          <button form-type="submit" class="btn-reset">确认</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 确认提货弹窗 -->
  <view class="popup-mask popup-flex" wx:if="{{hasPickedUp}}" catchtap="closePopup" data-modalFlag="pickup">
    <view class='popup' catchtap='prevent'>
      <view class="popup-content chanel-content">您确认已到门店完成提货了吗？</view>
      <view class='popup-btns-group'>
        <view class='popup-btn-cancel br-2' catchtap="closePopup" data-modalFlag="pickup">取消</view>
        <view class='popup-btn-cancel btn-primary' bindtap='confirmPickUp'>
          <button form-type="submit" class="btn-reset">确认</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 缺货退款弹窗 -->
  <view class="popup-mask popup-flex" wx:if="{{hasRefundPopup}}" catchtap="closePopup" data-modalFlag="refund">
    <view class='refund-popup' catchtap='prevent'>
      <view class="popup-refund-content refund-content">
        <text>{{doubleRefundToastContent}}</text>
      </view>
      <view class='popup-btns-singe'>
        <view class='popup-btn-know btn-primary' catchtap="closePopup" data-modalFlag="refund">我知道了</view>
      </view>
    </view>
  </view>
  <canvas canvas-id="barcode" class="barcode-canvas"/>
</view>
<!--自提/配送订单分享图-->
<canvas wx:if="{{showDeliveryInfo||showDeliveryPoster}}" style="width: 550px;height:{{shareCanvasHeight}}px" canvas-id="shareCanvas"/>
<view wx:if="{{showSharePop}}" class="share-pop">
  <view class="share-img-box" catchtap="closeSharePop">
    <view class="share-img-container" catchtap="preventEvent">
      <image class="share-img" src="{{shareImage}}" style="height:{{shareCanvasHeight}}rpx"></image>
    </view>
  </view>
  <view class="footer-box">
    <text class="share-tips">分享时可到相册选取图片</text>
    <view class="save-photo" bindtap='saveSharePoster'>保存图片</view>
  </view>
</view>
<order-cancel wx:if="{{orderCancelIsShow}}" bindselect="onOrderCancelSelected"   bindclose="onOrderCancelClosed" orderSource="bgxxOrder" orderID="{{orderDetail.goodsOrderID}}"></order-cancel>

<time-line title="订单状态" showTimeLine="{{showTimeLine}}">
  <block wx:for="{{orderStatusList}}" wx:key="index">
    <time-line-item text="{{item.state}}" time="{{item.date}}" showLine="{{index !== orderStatusList.length - 1}}" lineHeight="{{item.detail ? '90rpx' : '65rpx'}}">
      <image slot="image_slot" wx:if="{{index === 0}}" src="/source/images/red_dot.png"></image>
      <view slot="text">
        <view class="time-line-item-text" style="padding-bottom: {{item.detail ? '30rpx' : ''}}">
          <view>{{item.state}}</view>
          <view class="detail">{{item.detail}}</view>
        </view>
      </view>
    </time-line-item>
  </block>
</time-line>
<!-- 取消订单说明弹窗 -->
<view class="cost-description-box" wx:if="{{showCancelTips}}">
  <view class="cost-description-tips">
    <image src="https://resource.pagoda.com.cn/group1/M21/4B/39/CmiWa2C-zreARvYDAADJUcQOS9w784.png"></image>
    <view class="title">友情提示</view>
    <view class="tips">
      <text>订单已在处理中，请联系客服进行取消</text>
      <text>400-1811-212</text>
    </view>
    <view class="handle-btn">
      <view class="button" catchtap="showCancelTips" data-type="1">联系客服</view>
      <view class="button" catchtap="showCancelTips" data-type="2">我知道了</view>
    </view>
  </view>
</view>

<!-- 费用说明弹窗 -->
<popup
  popupTitle="包装费说明"
  isShowPopup="{{showCostBox}}"
  titleStyle="{{costBoxTitle}}"
  bindcloseShowPopup="showCostTips">
  <view class="cost-description">
    <view class="description">
      <text>说明</text>
      <text wx:if="{{deliveryWay == 2}}">本订单为保障商品的完整性和安全性，打包配送时将进行适当外部包装。根据国家相关法律法规，该部分包装物料需收取一定费用。如本单商品未使用外部包装物料，或您对包装费用存在异议等问题，可致电客服申请包装费用退款或咨询包装费用收取的相关问题。客服热线:400-181-1212。</text>
      <text wx:else>根据国家“限塑令”相关法律法规及政策要求，百果园有义务推广使用可降解塑料袋且不得免费提供给消费者。感谢您与百果园一起为生态环境、公众安全保驾护航。</text>
    </view>
    <view class="tips-item tips-item-total">
      <text>包装费</text>
      <text>￥{{orderPriceInfo.packAmount*1}}</text>
    </view>
  </view>
  <view class="close-btn safe-area-inset-bottom" bindtap="showCostTips">
    <view class="know">我知道了</view>
  </view>
</popup>

<!-- 心享会员节省明细弹窗 -->
<popup
  popupTitle="百果心享节省明细"
  isShowPopup="{{showVipDetail}}"
  title-class="vip-title"
  bindcloseShowPopup="showVipDetail">
  <view class="vip-detail">
    <view class="vip-section">
      <vip-section
        static-text
        btn="查看"
        tips="本单已省{{orderPriceInfo.freeValue}}元，了解更多省钱权益"
        bind:vipTipsClick="jumpVipPage"
      />
    </view>
    <view>
      <view class='xinxiang-detail-item' wx:for="{{orderPriceInfo.freeInfoList}}" wx:key="index">
        <text>{{item.freeName}}</text>
        <text>省{{common.formatPrice(item.freeValue)}}元</text>
      </view>
    </view>
  </view>
</popup>

<!-- 自定义时间选择组件 -->
<pagoda-popup
  wx:if="{{customPickerVisible}}"
  model:visible="{{customPickerVisible}}"
  position="bottom"
  title="请选择配送时间"
  round="{{true}}"
  head-class="head-class"
  title-class="title-class"
  z-index="{{1000}}"
  height="48vh"
  desc="详情描述">
  <pagoda-picker
    time-list="{{deliveryTimePickerArray}}"
    bindpick="handleCustomPickerPick"
    bindchangeDate="handleCustomerPickerChangeDate"
    currentIndex="{{deliveryTimePickerSeleted}}"
  />
</pagoda-popup>

<!-- 验证码弹窗组件 -->
<pagoda-popup
  model:visible="{{visibleSMS}}"
  showLeftArrow="{{true}}"
  showClose="{{false}}"
  round="{{true}}"
  z-index="{{2000}}"
  clickOverlayClose="{{false}}"
  position="bottom"
  title="更换支付方式"
  head-class="sms-head-class"
  title-class="sms-title-class"
  height="600rpx"
  bind:onBack="onBack">
    <sms-validate
      model:visible="{{visibleSMS}}"
      bind:validated="validated"
    />
</pagoda-popup>

<!-- 确认模态对话框 -->
<confirm-modal
    titleText="退款提示"
    confirmText="我知道了"
    showCancel="{{ false }}"
    isShowConfirmModal="{{ refundInfoModalVisible }}"
    bindconfirm="refundInfoModalConfirm">
    <view slot="content">
      <view class="ellipsis">
        1. 缺货商品将会按照实付部分退款
      </view>
      <view class="ellipsis">
        2. 退款金额将退回您的{{ orderDetail.channel === 'gb' ? '会员钱包' : '原支付账户' }}
      </view>
    </view>
</confirm-modal>
<!-- 确认模态对话框 -->

<common-loading />
<captcha id="comp-captcha"/>
