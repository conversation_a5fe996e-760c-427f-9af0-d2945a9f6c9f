const { getGiftCardHomePage, submitOrder, cancelUnpaidOrder, checkOrderIsCancel } = require('../../api/index')
import { debounce } from '../../../utils/util'
import { fruitOrderPay } from '../../../utils/services/fruitOrderPay'
const log = require('../../../utils/log.js')
const sensors = require('../../../utils/report/sensors')
import { pageTransfer } from '~/utils/route/pageTransfer';
const { protocolUrl } = require('../../../utils/config')
import {
  ErrorCode,
  RefreshErrorCode
} from '../../../sub-common/types/giftCard/errorCode/submitOrder'

Page({
  data: {
    /**主题标题 */
    title: '',
    /**主题id */
    themeId: '',

    /**卡面列表 */
    coverList: [],
    /**礼品卡列表 */
    cardList: [],
    /**主题库存数量 */
    themeStockNum: 0,

    /**选中的卡面索引 */
    selectedCoverIndex: 0,
    /**当前选中的索引id */
    currentCoverId: '',

    /**选中的卡索引 */
    selectedCardIndex: '',
    /**当前选中卡库存数量 */
    currentCardStockNum: 0,

    /**购买数量 */
    quantity: 1,
    /**是否同意购买须知 */
    isAgree: false,
    /**当前加购总金额 */
    totalPrice: 0,
  },

  onLoad() {
    const eventChannel = this.getOpenerEventChannel()

    eventChannel.on('initData', (data) => {
      this.initData(data)
    })
  },

  /**
   * 根据数据初始化页面
   * @param { Object } data
   * @param { Object } data.index 该主题选中的封面坐标
   * @param { Object } data.cardIndex 该主题选中的礼品卡坐标
   * @param { Object } data.theme 该主题数据
   * @param { String } data.theme.title 主题标题
   * @param { String } data.theme.themeId 主题id
   * @param { Array } data.theme.coverList 卡面列表
   * @param { Array } data.theme.cardList 礼品卡列表
   * @param { Number } data.theme.themeStockNum 主题库存数量
   */
  initData(data) {
    const {
      index,
      cardIndex,
      theme: {
        title,
        themeId,
        coverList,
        cardList,
        themeStockNum,
      }
    } = data

    this.setData({
      title,
      themeId,
      coverList,
      cardList,
      themeStockNum,
      selectedCoverIndex: index,
    })
    this.updateCoverId()

    //  该主题下有库存
    if (themeStockNum) {
      /**指定选择礼品卡 且 该礼品卡有库存 */
      const targetIndx = cardIndex && cardList[cardIndex] && cardList[cardIndex].stockNum

      //  指定了选择的礼品卡
      if (targetIndx) {
        this.selectCard(cardIndex)
      } else {
        const hasStockNumIndex = cardList.findIndex(item => item.stockNum)
        this.selectCard(hasStockNumIndex)
      }
    }
    //  该主题下无库存
    else {
      this.setData({
        quantity: 0,
        currentCardStockNum: 0,
      })
    }
  },

  /**
   * 更新购买金额
   * @param {*} e
   */
  updateTotalPrice() {
    const currentCard = this.data.cardList[this.data.selectedCardIndex]

    this.setData({
      totalPrice: (currentCard.price * this.data.quantity / 100),
    })
  },

  /**
   * 更新卡面滚动坐标
   * @param {*} e
   */
  updateCoverId() {
    this.setData({
      currentCoverId: `cover${this.data.selectedCoverIndex}`,
    })
  },

  /**
   * 选择卡面
   * @param {*} e 
   */
  selectCover(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      selectedCoverIndex: index
    })

    this.updateCoverId()
    sensors.clickReport({
      blockName: '购买电子礼品卡',
      element_name: '点击小图',
      element_code: '1161700001',
    })
  },

  /**
   * 卡面列表滚动事件
   * @param {*} e 
   */
  scrollCoverList: debounce(function () {
    sensors.clickReport({
      blockName: '购买电子礼品卡',
      element_name: '滑动小图',
      element_code: '1161700002',
    })
  }, 300),

  /**
   * 选择面额
   * @param {*} e 
   */
  selectCard(e) {
    const index = typeof e === 'number' ? e : e.currentTarget.dataset.index
    const currentCard = this.data.cardList[index]
    /**商品剩余库存 */
    const stockNum = Number(currentCard.stockNum)

    //  商品无库存
    if (!stockNum) {
      return
    }

    /**展示仅剩x件 */
    const showRemaining = stockNum && stockNum < 11

    this.setData({
      selectedCardIndex: index,
      currentCardStockNum: stockNum,
      showRemaining: showRemaining,
    })

    //  购买数量大于库存数量
    if (this.data.quantity > stockNum) {
      this.setData({
        quantity: stockNum,
      })
    }
    this.updateTotalPrice()

    //  点击选择面额埋点
    if (e?.currentTarget?.dataset) {
      sensors.clickReport({
        blockName: '购买电子礼品卡',
        element_name: '选择面额',
        element_code: '1161700003',
      })
    }

    if (showRemaining) {
      sensors.exposureReport({
        blockName: '购买电子礼品卡',
        element_name: '展示库存提示（选择面额）',
        element_code: '1161700007',
      })
    }
  },

  /**
   * 设置数量
   * @param {*} e 
   * @returns 
   */
  setCount(e) {
    const newCount = Number(e.detail.value || 0)
    const isValid = this.validateCount(newCount)

    if (!isValid) return

    this.setData({
      quantity: newCount,
    })
    this.updateTotalPrice()
  },

  /**
   * 校验数量是否合法
   * @param {*} quantity
   * @returns 
   */
  validateCount(quantity) {
    const newCount = Number(String(quantity).replace(/\s*/g, ''))
    const _quantity = this.data.quantity
    const currentCard = this.data.cardList[this.data.selectedCardIndex]
    let title = ''

    const reset = () => {
      this.setData({
        quantity: _quantity,
      })
    }
    //  经过格式化后的数量与原始数量相同，不做任何处理
    if ((newCount) === _quantity || !this.data.currentCardStockNum) {
      //  输入的内容可能携带空格，重新设置原始数据
      reset()
      return false
    }

    const conditions = [
      [
        () => newCount === 0 && _quantity === 1,
        '已经是最小数量了',
      ],
      [
        () => newCount > 99,
        '单次最多购买99张',
      ],
      [
        () => newCount > currentCard.stockNum,
        `仅剩${currentCard.stockNum}份了哦`,
        function () {
          sensors.exposureReport({
            blockName: '购买电子礼品卡',
            element_name: '库存不足提示（点击加购）',
            element_code: '1161700008',
          })
        }
      ],
      [
        () => !new RegExp(/^\d{1,}$/).test(newCount),
        '请输入正确的商品数量',
      ],
      [
        () => !Number(newCount),
        '数量必须大于0哦~',
      ],
    ]

    conditions.some(function (condition) {
      const [
        fn,
        msg,
        sensorFn,
      ] = condition

      if (fn && fn()) {
        title = msg
        reset()

        if (sensorFn) {
          sensorFn()
        }
        return true
      }
    })

    if (title) {
      wx.showToast({
        title: title,
        icon: 'none'
      })
      return false
    }
    return true
  },

  /**
   * 修改数量
   * @param {*} e 
   * @returns 
   */
  changeQuantity(e) {
    const type = e.currentTarget.dataset.type
    const quantity = this.data.quantity
    const newQuantity = type === 'add' ? quantity + 1 : quantity - 1
    const isValid = this.validateCount(newQuantity)

    if (!isValid) return

    this.setData({
      quantity: newQuantity,
    })
    this.updateTotalPrice()

    if (type === 'add') {
      sensors.clickReport({
        blockName: '购买电子礼品卡',
        element_name: '增加购买数量',
        element_code: '1161700005',
      })
    } else {
      sensors.clickReport({
        blockName: '购买电子礼品卡',
        element_name: '减少购买数量',
        element_code: '1161700006',
      })
    }
  },

  /**
   * 切换同意状态
   */
  handleAgree() {
    if (!this.data.themeStockNum) {
      return
    }

    this.setData({
      isAgree: !this.data.isAgree
    })
  },

  /**
   * 更新页面数据
   */
  async updatePage() {
    const { data } = await getGiftCardHomePage()
    const eventChannel = this.getOpenerEventChannel()
    // 通过emit的方式进行触发 将子页面/目标页面中的数据传递给当前页面
    eventChannel.emit('update', { data: data })

    const themeId = this.data.themeId
    const currentTheme = data.themeList.find(item => item.themeId === themeId)
    if (currentTheme) {
      this.initData({
        index: this.data.selectedCoverIndex,
        cardIndex: this.data.selectedCardIndex,
        theme: currentTheme,
      })
    }
  },

  beforeConfirmPay: debounce(function () {
    if (!this.data.themeStockNum) {
      return
    }
    sensors.clickReport({
      blockName: '购买电子礼品卡',
      element_name: '点击支付',
      element_code: '1161700009',
    })

    if (!this.data.isAgree) {
      wx.showToast({
        title: '请阅读并勾选礼品卡购买须知',
        icon: 'none'
      })

      sensors.exposureReport({
        blockName: '购买电子礼品卡',
        element_name: '未勾选协议提示',
        element_code: '1161700010',
      })
      return
    }

    this.confirmPay()
  }, 300),

  /**
   * 确认支付
   * @returns 
   */
  confirmPay: async function () {
    const app = getApp()
    if (this.isSubmiting) {
      return
    }

    try {
      this.isSubmiting = true
      wx.showLoading({
        title: '支付中...',
        mask: true
      })
      const currentCard = this.data.cardList[this.data.selectedCardIndex]
      const currentCover = this.data.coverList[this.data.selectedCoverIndex]
      const quantity = Number(this.data.quantity)

      const submitParams = {
        customerID: wx.getStorageSync('user')?.userID,
        themeId: this.data.themeId,
        coverUrl: currentCover.backgroundPicUrl,
        cardId: currentCard.cardId,
        count: quantity,
        totalAmount: this.data.totalPrice,
      }
      this.cardInfo = {
        name: `${this.data.title}-${currentCover.title}`,
        amount: currentCard.initBalance,
        cover: currentCover.backgroundPicUrl,
        count: quantity,
      }
      const res = await submitOrder(submitParams)
      const result = res.data || {}
      this.payRequest(result)
      this.submitOrderSuccessReport(result)
    }
    //  处理创建订单的异常
    catch (error) {
      const errMsg = error.errMsg || ''
      //  网络请求超时提示
      if (errMsg.includes('timeout') || errMsg.includes('超时')) {
        wx.showToast({
          title: '网络请求超时，请稍后重试',
          icon: 'none',
          duration: 1500
        })
      }

      /**数字状态码 */
      const errorCode = error?.errorCode
      /**字符串状态码 */
      const errorCodeStr = String(error?.errorCode || '')
      //  其他确认订单页会重新结算，但礼品卡购买页无需结算，只需要刷新页面，或返回首页

      //  状态码为2开头，需要页面刷新
      if (errorCodeStr.startsWith('2')) {
        //  卡面已下架
        if (errorCode === RefreshErrorCode.卡面已下架) {
          this.setData({
            selectedCoverIndex: 0,
          })
        }

        wx.showToast({
          title: error.description,
          icon: 'none',
          duration: 1500
        })

        this.updatePage()
      }
      //  状态码为3开头，需要返回首页
      else if (errorCodeStr.startsWith('3')) {
        await app.showModalPromise({
          content: error.description,
          showCancel: false,
          confirmText: '我知道了',
        })
        wx.navigateBack({
          delta: 1
        })
      } else {
        wx.showToast({
          title: error.description,
          icon: 'none',
          duration: 1500
        })
      }

      //  礼品卡库存不足
      if (errorCode === RefreshErrorCode.礼品卡库存不足) {
        sensors.exposureReport({
          blockName: '购买电子礼品卡',
          element_name: '库存不足提示（确认支付）',
          element_code: '1161700011',
        })
      }

      //  单日占用库存达上限 || 单日扣减库存不足
      if (errorCode === ErrorCode.单日占用库存达上限 || errorCode === ErrorCode.单日扣减库存不足) {
        sensors.exposureReport({
          blockName: '购买电子礼品卡',
          element_name: '购买数量超限',
          element_code: '1161700012',
        })
      }

      //  单日扣减库存达上限
      if (errorCode === ErrorCode.单日扣减库存达上限) {
        sensors.exposureReport({
          blockName: '购买电子礼品卡',
          element_name: '可购买数量为0',
          element_code: '1161700013',
        })
      }
    } finally {
      wx.hideLoading()
      this.isSubmiting = false
    }
  },

  /**
   * 支付请求
   * @param {*} orderData
   */
  payRequest(orderData) {
    const {
      selectPagodaPay,
      selectWxPay,
      selectUnionPay,
      balanceInfo,
    } = this.data;
    const {
      payAmount,
      mainOrderNo,
    } = orderData

    const payInfo = {
      usePagodaPay: false,
      useWxPay: true,
      useUnionPay: false,
      paymentAmount: payAmount,
      tradeNo: mainOrderNo,
      mainOrderNo,
      mainBalance: 0,
    }
    const succCb = this.openWaitPay.bind(this, orderData)
    const failCb = this.failCallback.bind(this, orderData)
    const extraInfo = {
      succCb,
      failCb
    }
    this.callPayStartTime = new Date().getTime()
    fruitOrderPay.handlePay(payInfo, extraInfo)
  },

  /**
   * 支付成功回调
   * @param {*} orderData
   */
  async openWaitPay(orderData, payData) {
    const {
      userID: customerID = -1
    } = wx.getStorageSync('user')
    const payTime = new Date().getTime() - this.callPayStartTime
    //  如果支付时间大于一分钟，订单在后端定时任务可能已自动取消，查询一下是否已取消
    if (payTime > 60000) {
      const {
        data: isCanceled,
      } = await checkOrderIsCancel({
        customerID: customerID,
        orderNo: orderData.mainOrderNo,
      })
      //  如果真的已取消了，引导用户重新购买
      if (isCanceled) {
        const app = getApp()
        app.showModalPromise({
          content: '支付超时，订单已取消，请重新购买。如您已支付，将稍后进行原路退款',
          showCancel: false,
          confirmText: '我知道了',
        })
        this.updatePage()
        return
      }
    }

    console.log(payData)
    const {
      mainOrderNo,
    } = orderData
    const {
      data: {
        payNo,
        payNos
      }
    } = payData

    const orderDetailObj = {
      payNo: payNo,
      payNos: payNos,
      mainOrderNo: mainOrderNo,
      cardInfo: this.cardInfo,
    }
    pageTransfer.send(orderDetailObj)
    // 支付成功跳转
    wx.redirectTo({
      url: '/giftCard/pages/waitPay/index',
    })
  },

  /**
   * 支付失败回调
   * @param {*} orderData
   */
  async failCallback(orderData, failReason) {
    await this.cancelUnpaidOrder(orderData.mainOrderNo)
    console.log('礼品卡支付失败', orderData, failReason);
    this.updatePage()
    wx.showToast({
      title: '支付取消',
      duration: 1500,
      icon: 'none'
    })

    sensors.exposureReport({
      blockName: '购买电子礼品卡',
      element_name: '支付取消',
      element_code: '1161700014',
    })
  },

  /**
   * 取消未支付的订单
   * @param {*} mainOrderNo
   */
  async cancelUnpaidOrder(mainOrderNo) {
    const { userID: customerID = -1 } = wx.getStorageSync('user')

    const params = {
      customerID,
      subOrderNo: mainOrderNo,
    }

    try {
      await cancelUnpaidOrder(params)
    } catch (error) {
      console.log('礼品卡待支付订单取消失败', error);
    }
  },

  /**
   * 提交订单成功后埋点上报
   * @param {*} orderData
   */
  submitOrderSuccessReport(orderData) {

  },

  /**
   * 查看章程
   */
  checkConstitution(e) {
    const { type } = e.currentTarget.dataset
    const pageUrl = encodeURIComponent(`${protocolUrl[type]}?close=${this.data.isAgree ? 'Y' : 'N'}`)
    wx.navigateTo({
      url: `/h5/pages/commonLink/index?pageUrl=${pageUrl}`
    })
  },
})
