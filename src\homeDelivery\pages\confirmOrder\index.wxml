<wxs module='m1'>
  var totalGoodsCount = function(list) {
    var count = 0
    for (var i = 0; i < list.length; i++) {
      count = count + Number(list[i].count)
    }
    return count
  }
  module.exports.totalGoodsCount = totalGoodsCount

  /**
    * 获取支付按钮的文案
    */
  module.exports.getPayBtnText = function(isSelectRechargeAct, isMixPay, selectPagodaPay, selectWxPay, selectUnionPay) {
    if (isSelectRechargeAct) {
      return '充值并支付'
    }

    if (isMixPay) {
      if (selectWxPay) {
        return '微信支付'
      }
      if (selectUnionPay) {
        return '云闪付支付'
      }
    } else {
      if (selectPagodaPay) {
        return '钱包支付'
      }
      if (selectWxPay) {
        return '微信支付'
      }
      if (selectUnionPay) {
        return '云闪付支付'
      }
    }

    return '立即支付'
  }
</wxs>
<wxs module='invoice' src="../../../utils/invoice.wxs"></wxs>
<wxs module="common" src="../../../utils/common.wxs"></wxs>

<view class='confirm-order' id="confirm-box-view">
  <view class="header-background"></view>
  <scroll-view
    scroll-y="{{!visibleSMS}}"
    bindscroll='pageScroll'
    style="height: {{scrollViewHeight}}px;"
    scroll-into-view="{{scrollId}}"
    scroll-with-animation="true"
    >
    <view class="cart-tips" wx:if="{{topTips}}">
      <view class="cart-tips-content text-hidden-line3">
        {{topTips}}
      </view>
    </view>
    <view class="confirm-box">
      <view class="delivery-info">
        <view class="delivery-way">
          <view class="way-list">
            <view wx:for="{{orderDeliveryWay}}"
              wx:key="key"
              data-status="{{item.key}}"
              catchtap="changeDeliveryWay"
              class="delivery-way-item way-{{index + 1}} {{item.key == deliveryWay ? 'actived-way-item' : ''}}">{{item.label}}</view>
            <view class="delivery-way-item-line {{deliveryWay === 'selfTake' ? 'item-line-take' : ''}}"></view>
          </view>
        </view>
        <view class="address-border">
          <block wx:if="{{orderDeliveryAddressInfo[deliveryWay]}}">
             <template wx:if="{{deliveryWay === 'deliveryToDoor'}}" is="delivery-address-info" data="{{address: orderDeliveryAddressInfo[deliveryWay], showExceedDistanceTip, storeInfo, isInTimeGoods: inTimeGoodsList.length !== 0}}"></template>
             <template wx:else is="take-store-info" data="{{storeInfo: orderDeliveryAddressInfo[deliveryWay]}}"></template>
          </block>
          <choice-address
            wx:else
            tips="{{deliveryWay === 'selfTake' ? '请选择自提门店' : '请选择收货地址'}}"
            bind:choiceAddressInfo="choiceAddressInfo">
          </choice-address>
        </view>
      </view>

      <!-- 及时达商品 -->
      <view class='order-list' wx:if='{{inTimeGoodsList.length !== 0}}'>
        <!-- 及时达商品头 -->
        <view class='delivery-time order-page-border column-header'>
          <view class="column-summary">
            <view class="column-title">
              <view class="column-type-status">
                <view class="column-type">及时达</view>
                <!--超时赔券-->
                <view wx:if="{{existReparationActivity && deliveryWay === 'deliveryToDoor'}}" class="column-tips" bindtap="navigateToCommonH5Page" data-urlType="1">超时赔券<image class="icon-tips" src="../../source/images/icon_qm_circle_black_20.png" /></view>
              </view>
              <!-- <picker mode="multiSelector" value="{{multiIndex}}" range="{{multiArray}}" range-key="dateDesc" bindchange="bindMultiPickerChange" bindcolumnchange="bindMultiPickerColumnChange"> -->
                <view class='time' bindtap="openTimePickerHandle">
                  <image wx:if="{{currSelectDeliveryInfo.atOnceDelivery && deliveryWay === 'deliveryToDoor'}}"  class="quickly-delivery-label" src="/homeDelivery/source/images/at-once-delivery.png" />
                  <!-- 无配送时间 -->
                  <text class="no-delivery-info" wx:if="{{noDeliveryTimeInfo}}">{{noDeliveryTimeInfo}}</text>
                  <!-- 有配送时间 -->
                  <text wx:else class='delivery-text'>{{common.exchangeText(currSelectDeliveryInfo.desc) || (deliveryWay === 'deliveryToDoor' ? '请选择配送时间' : '请选择自提时间')}}</text>
                  <image wx:if="{{!noDeliveryTimeInfo}}" src='/homeDelivery/source/images/icon_right_arrow_gary.png' class='delivery-arrow right-arrow'></image>
                </view>
              <!-- </picker> -->
            </view>

            <view wx:if="{{ timelyGoodsIsOverWeight }}" class="over-weight-tip" bindtap="callStore">
              商品重量已超20kg, 仅支持自提, 如需配送请联系门店
              <image src="https://resource.pagoda.com.cn/dsxcx/images/de27d84af4634979131127ba02955a83.png" />
            </view>
          </view>
        </view>
        <!-- 单个商品直接展示 -->
        <view wx:if="{{inTimeGoodsList.length ===1}}">
          <goods-item
            goodsObj="{{inTimeGoodsList[0]}}"
            applyVipCard="{{vipCardIsSeleted}}"
            fruitFans="{{fruitFans}}"
            isFruitFansGray="{{isFruitFansGray}}"
            currentChooseExchangeCard="{{currentChooseExchangeCard}}">
          </goods-item>
        </view>
        <!-- 多个商品轮播  -->
        <view wx:else class="goods-scroll-wrap">
          <scroll-view class="goods-scroll-box" scroll-x="true">
            <image class="goods-inner-img" wx:for="{{inTimeGoodsList}}" wx:key="index"  wx:for-item="item" src="{{item.headPic}}"></image>
          </scroll-view>
          <view class="goods-scroll-sideBar" bindtap="showGoodsList" data-goods-list="inTimeGoodsList">
            <view class="sideBar-text">共{{m1.totalGoodsCount(inTimeGoodsList)}}件</view>
            <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' class='right-arrow'></image>
          </view>
          <image class="img-shadow" src="/homeDelivery/source/images/img-shadow-bg.png"></image>
        </view>
        <!-- 添加备注  -->
        <view class='order-comment'>
          <view class='comment-title'>备注</view>
          <view class="comment-box" bindtap='inTimeOrderNoteChange'>
            <view class="comment-text">
              <view class="text-hidden">{{inTimeOrderNote || '选填，如有送礼或其他需求可填写备注'}}</view>
              <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' class='right-arrow'></image>
            </view>
          </view>
        </view>
        <block wx:if="{{deliveryWay === 'deliveryToDoor'}}">
          <!-- 贺卡模块，管理台贺卡金额不能设置为0元，所以为金额为0或undefined表示没有配置 -->
          <view class="bless-card-box" wx:if="{{blessCardAmount}}">
            <view class="bless-card-box__info">
              <view class="bless-card-box__info__title">祝福卡</view>
              <view class="bless-card-box__info__price">{{common.formatPrice(blessCardAmount)}}元/张</view>
            </view>
            <view catch:tap="handleSelectBlessCard" class="bless-card-box__check">
              <radio-check checked="{{blessCardSelected}}" ></radio-check>
            </view>
          </view>
          <!-- 贺卡模块，展示内容 -->
          <block wx:if="{{blessCardSelected}}">
            <view class="bless-card-content" bind:tap="navigateToBless">
              <view class="bless-card-content__text" wx:if="{{blessCardContent}}" style="color: #222">
                {{formattedBlessCardContent}}
              </view>
              <view class="bless-card-content__text" wx:else>
              给Ta捎上一句祝福吧~
              </view>
              <view class="bless-card-content__right-arrow" >
                <view class="bless-card-content__right-arrow__text" wx:if="{{!blessCardContent}}">去填写</view>
                <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' class='right-arrow'></image>
              </view>
            </view>
            <view style="padding-top: 24rpx"></view>
          </block>
        </block>
        <template is="goods-Amount-calculation" wx:if="{{inTimeGoodsList.length > 1 && b2cGoodsList.length !== 0 }}" data="{{...goodsAmountObj.inTimeGoods}}"></template>
      </view>

      <!-- b2c商品 -->
      <view class='order-list' wx:if='{{b2cGoodsList.length !== 0}}'>
        <view class='forward-hander order-page-border'>
          <text class='hander-txt'>全国送</text>
          <text class="b2c-delivery-time">大仓直发，快递到家</text>
        </view>
        <!-- 单个商品直接展示 -->
        <view wx:if="{{b2cGoodsList.length ===1}}">
          <goods-item goodsObj="{{b2cGoodsList[0]}}" applyVipCard="{{vipCardIsSeleted}}" fruitFans="{{fruitFans}}" isFruitFansGray="{{isFruitFansGray}}"></goods-item>
        </view>
        <!-- 多个商品轮播  -->
        <view wx:else class="goods-scroll-wrap">
          <scroll-view class="goods-scroll-box" scroll-x="true">
            <image class="goods-inner-img" wx:for="{{b2cGoodsList}}" wx:key="index"  wx:for-item="item" src="{{item.headPic}}"></image>
          </scroll-view>
          <view class="goods-scroll-sideBar" bindtap="showGoodsList" data-goods-list="b2cGoodsList">
            <view class="sideBar-text">共{{m1.totalGoodsCount(b2cGoodsList)}}件</view>
            <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' class='right-arrow'></image>
          </view>
          <image class="img-shadow" src="/homeDelivery/source/images/img-shadow-bg.png"></image>
        </view>
        <template is="goods-Amount-calculation" wx:if="{{b2cGoodsList.length > 1 && inTimeGoodsList.length !== 0 }}" data="{{...goodsAmountObj.b2cGoods}}"></template>
      </view>

      <!-- 失效商品 -->
      <view class='invalidgoods-box' wx:if="{{invalidGoodsList.length > 0}}">
        <view class='forward-hander order-page-border'>
          <text class='goods-items-title'>失效</text>
          <text class="goods-items-desc">因配送范围、库存等原因而导致失效</text>
        </view>
        <view wx:if="{{invalidGoodsList.length === 1}}">
          <goods-item goodsObj="{{invalidGoodsList[0]}}" applyVipCard="{{vipCardIsSeleted}}" fruitFans="{{fruitFans}}" isFruitFansGray="{{isFruitFansGray}}"></goods-item>
        </view>
        <!-- 多个商品轮播  -->
        <view wx:if="{{invalidGoodsList.length > 1}}" class="goods-scroll-wrap">
          <scroll-view class="goods-scroll-box" scroll-x="true">
            <view class="goods-scroll-item" wx:for="{{invalidGoodsList}}" wx:key="index"  wx:for-item="item">
              <view class="goods-unavailable-mask"></view>
              <image class="goods-inner-img" src="{{item.headPic}}"></image>
            </view>
          </scroll-view>
          <view class="goods-scroll-sideBar" bindtap="showGoodsList" data-goods-list="invalidGoodsList">
            <view class="sideBar-text">共{{m1.totalGoodsCount(invalidGoodsList)}}件</view>
            <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' class='right-arrow'></image>
          </view>
        </view>
      </view>
      <!-- 订单信息 -->
      <view class='consumer-details'>
        <view class='details-item' catch:tap="tapGoodsAmount">
          <text>商品金额</text>
          <view class="total-amount-detail">
            <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' wx:if="{{showDiscountDetailArrow}}" class='toggle-arrow' style="{{showDiscountDetail ? 'transform: rotate(-90deg);' : ''}}"></image>
            <text class="line-through-txt" wx:if="{{originGoodsTotalAmount !== goodsTotalAmount}}">¥{{common.formatPrice(originGoodsTotalAmount)}}</text>
            <text class="details-value"><text class="rmb-sign">¥</text>{{common.formatPrice(goodsTotalAmount)}}</text>
            <view wx:if="{{vipPriceDiscount && !isFruitFansGray}}" class="total-amount-tips">本单已享¥{{common.formatPrice(vipPriceDiscount)}}心享价优惠</view>
          </view>
        </view>
        <!-- 灰度用户展示特价优惠、果粉优惠、心享优惠 -->
         <!-- style="{{showDiscountDetail ? 'height: 76rpx' : ''}}" -->
        <view class="discount-box-toggle" wx:if="{{showDiscountDetail}}">
          <view wx:if="{{isFruitFansGray && discountList.length}}" class="discount-box">
            <view class="discount-item" wx:for="{{discountList}}" wx:key="index">{{item.text}}</view>
          </view>
        </view>
        <block wx:if="{{deliveryWay === 'deliveryToDoor' && blessCardSelected}}">
          <view class='details-item'>
            <view class="detail-pack-left">
              <text>祝福卡</text>
            </view>
            <text class="details-value"><text class="rmb-sign">¥</text>{{common.formatPrice(blessCardAmount)}}</text>
          </view>
        </block>
        <view class='details-item' wx:if="{{totalBoxAmount}}">
          <view class="detail-pack-left" bindtap="showOrHidePackDetail">
            <text>餐盒费</text>
            <image class="detail-pack-icon" data-show-pack="{{showPackDetail}}" src="../../source/images/icon_qm_circle_black_22.png" />
          </view>
          <text class="details-value"><text class="rmb-sign">¥</text>{{common.formatPrice(totalBoxAmount)}}</text>
        </view>
        <!--配送上门展示运费/包装费-->
        <block wx:if="{{deliveryWay === 'deliveryToDoor'}}">
          <view class='details-item' wx:if="{{packAmount>0}}">
            <view class="detail-pack-left" catchtap="showPackingFeePopup">
              <text>包装费</text>
              <image class="detail-pack-icon" src="../../source/images/icon_qm_circle_black_22.png" />
            </view>
            <view>
              <text class="details-value {{integralSelected ? 'line-through-txt' : ''}}"><text class="rmb-sign">¥</text>{{common.formatPrice(packAmount)}}</text>
              <text wx:if="{{integralSelected}}" class="details-value"><text class="rmb-sign">¥</text>{{common.formatPrice(packAmountAfterDiscount)}}</text>
            </view>
          </view>
          <view class="details-item integral-box" wx:if="{{integralExchangeInfo && integralExchangeInfo.integral > 0}}">
            <view>
              <text>可用{{integralExchangeInfo.integral}}积分抵{{common.formatPrice(integralExchangeInfo.integralAmount)}}元包装费（共{{userIntegral}}积分）</text>
            </view>
            <view catch:tap="handleSelectIntegral">
              <radio-check checked="{{integralSelected}}"></radio-check>
            </view>
          </view>
          <view class='details-item'>
            <view class="detail-pack-left" catchtap="showFreightTipPopup">
              <text>配送费</text>
              <image wx:if="{{freightTip && freightTip.length}}" class="detail-pack-icon" src="../../source/images/icon_qm_circle_black_22.png" />
            </view>
            <view>
              <text class="line-through-txt" wx:if="{{wholeFreight !== totalFreight}}">¥{{common.formatPrice(wholeFreight)}}</text>
              <text class="details-value"><text class="rmb-sign">¥</text>{{common.formatPrice(totalFreight)}}</text>
            </view>
          </view>
        </block>
        <!-- 兑换卡 -->
        <template is="exchange-card-bar" data="{{exchangeCardValueTotal,isHasAvailableExchangeCard,isSelectExchangeCard,availableExchangeCardCount,isNoExchangeCard}}"></template>
        <!-- 优惠券 -->
        <template is="coupon-bar" data="{{isBetterSelectCoupon,selectedCouponList, couponList, totalCouponMoney, unAvailableCouponList, couponModifyFlag, isBgxxVipCoupon, showSelectVipCoupon, superVipStatus}}"></template>
        <!-- 代金券 -->
        <template is="vouchers-bar" data="{{voucherCouponValue,isHasAvailableVoucher,isSelectVoucher,availableCount,isBetterSelectVoucher,isNoVoucher}}"></template>
      </view>

      <!-- 随单充值活动 -->
      <order-inline-recharge
        rechargeData="{{ rechargeData }}"
        bind:select="selectOrderInlineRecharge"
        bind:unSelect="unSelectOrderInlineRecharge"
        bind:moreClick="moreRecharge">
      </order-inline-recharge>
      <!-- 随单充值活动 -->

      <!-- 飞享月卡 -->
      <mouth-card
        wx:if="{{vipCardInfo.show}}"
        id="mouthcard"
        vipCardInfo="{{vipCardInfo}}"
        saveMoney="{{mouthCardSaveMoney}}"
        isDisabled="{{ !b2cGoodsList.length && !inTimeGoodsList.length}}"
        useKey="{{useLimitCouponKey}}"
        availableListKey="{{availableListKey}}"
        bind:chooseMouthCard="chooseMouthCard"
        bind:updateOpenCardInfo="updateOpenCardInfo"
        bind:showBenfitsDetail="showBenfitsDetail"
      ></mouth-card>

      <!-- 心享横幅 -->
      <view class="bgxx-box" wx:if="{{superVipStatus === 'C' && !vipCardInfo.show}}">
        <vip-section save-money="{{vipFree}}" sensors-data="{{({element_code:'130200012',element_name:'心享开卡',element_content:'心享开卡',screen_code:'1302',screen_name:'及时达确认订单页'})}}" />
      </view>

      <!-- 支付方式 -->
      <view class="payment-way" wx:if="{{mainBalance != -1}}">
        <paymentWaySelect
          selectPagodaPay="{{selectPagodaPay}}"
          selectWxPay="{{selectWxPay}}"
          selectUnionPay="{{selectUnionPay}}"
          disableUnionPay="{{disableUnionPay}}"
          hideUnionPay="{{hideUnionPay}}"
          mainBalance="{{mainBalance}}"
          rechargePayInfo="{{ rechargePayInfo }}"
          rechargeText="{{rechargeText}}"
          mainBalanceIsNotEnough="{{lack}}"
          forbidPdPay="{{forbidPdPay}}"
          disablePay="{{disablePay}}"
          bindsetWaySelect="setWaySelectHandle"
          bindswitchPagodaPayChange="switchPagodaPayChangeHandle"
         />
      </view>
      <!-- 根据支付方式展示对应的文案 -->
      <!-- <template is="invoice-text" data="{{selectPagodaPay, selectWxPay, deliveryWay, storeInfo, isSelectVoucher}}"></template> -->

      <view class="payment-text">
        {{invoice.getVoucherText(selectWxPay || selectUnionPay, selectPagodaPay, isSelectVoucher, storeInfo, deliveryWay)}}
      </view>
      <view class="padding-bottom safe-area-inset-bottom" style="margin-top: {{addMargin}}px"></view>
    </view>

  </scroll-view>
  <view class='flex-footer'>
    <!-- 开通飞享提示 -->
    <view class="footer-mouthcard" wx:if="{{showMouthCardMode && vipCardInfo.show && openCardContent.fixOpenTitle}}" bindtap="scrollBottom">
      <view class="mouthcard-content">
        <image class="vip-tips-icon-crown" mode="aspectFill" src='/source/images/savetip_img-icon.png' />
        <view class="mouthcard-mode">
          {{openCardContent.fixOpenTitle}}
          <text class="mouthcard-save">{{openCardContent.showModeMoney}}</text>
          {{openCardContent.openSub}}
        </view>
        <image class="vip-tips-icon-down" mode="aspectFill" src='https://resource.pagoda.com.cn/dsxcx/images/f324dbf623743fd51568e89e15d1226c' />
      </view>
    </view>
    <view class="border-top {{showAddrNotice ? '': 'show-addr-notice'}}" wx:if='{{orderDeliveryAddressInfo[deliveryWay]}}'>
      <view class='footer-info'>
        <text wx:if="{{deliveryWay === 'deliveryToDoor'}}">{{ (!inTimeGoodsList.length && b2cGoodsList.length) ? '快递到家: ' : '配送上门: ' }}{{orderDeliveryAddressInfo[deliveryWay].cityName}}{{orderDeliveryAddressInfo[deliveryWay].gisAddress}}{{orderDeliveryAddressInfo[deliveryWay].address}}</text>
        <text wx:else>门店自提：{{orderDeliveryAddressInfo[deliveryWay].address}}</text>
      </view>
    </view>

    <view class="footer-confirm safe-area-inset-bottom">
      <!-- 选择充值活动场景 -->
      <block wx:if="{{ isSelectRechargeAct }}">
        <view class="order-amount-detail">
            <view>
              <view class="total">
                <text class="one">待支付: </text>
                <view class="total-val">
                  <view class="total-val-unit">¥</view>
                  <rollingNumber currentNumber="{{ rechargePayInfo.thirdPayAmount }}" showRolling="{{payAmountShouldRolling}}"/>
                </view>

                <!-- 已优惠 -->
                <text
                  wx:if="{{ totalDiscount }}"
                  class="total-discount">已优惠 ¥{{common.formatPrice(totalDiscount)}}</text>
                <!-- 已优惠 -->
              </view>
            </view>

            <!-- 支付方式金额 -->
            <view class="pay-way-amount">
              <view wx:if="{{ payAmount > 0 }}">
                充值后钱包支付￥{{ common.formatPrice(rechargePayInfo.balancePayAmount) }}
              </view>
            </view>
            <!-- 支付方式金额 -->
          </view>
      </block>
      <!-- 选择充值活动场景 -->

      <!-- 常规场景 -->
      <block wx:else>
        <view class="order-amount-detail">
          <view>
            <view class="total">
              <text class="one">待支付: </text>
              <view class="total-val">
                <view class="total-val-unit">¥</view>

                <!-- 混合支付时，此处仅展示三方支付金额。非混合支付时，展示整笔订单金额 -->
                <rollingNumber currentNumber="{{ isMixPay ? thirdPayAmount : payAmount }}" showRolling="{{payAmountShouldRolling}}"/>
              </view>

              <!-- 已优惠 -->
              <text
                wx:if="{{ totalDiscount }}"
                class="total-discount">已优惠 ¥{{common.formatPrice(totalDiscount)}}</text>
              <!-- 已优惠 -->
            </view>
          </view>

          <!-- 支付方式金额 -->
          <block wx:if="{{ isMixPay }}">
            <view class="pay-way-amount">
              <view>
                钱包支付￥{{ common.formatPrice(mainBalance) }}
              </view>
            </view>
          </block>
          <!-- 支付方式金额 -->
        </view>
      </block>
      <!-- 常规场景 -->

      <!-- 选择充值活动场景-支付按钮 -->
      <block wx:if="{{ isSelectRechargeAct }}">
        <form
          name='pagoda'
          report-submit='true'
          class="confirm-form">
          <button
            class='confirm-button'
            bindtap="submitFormBefore">{{ m1.getPayBtnText(isSelectRechargeAct, isMixPay, selectPagodaPay, selectWxPay, selectUnionPay) }}</button>
        </form>
      </block>
      <!-- 选择充值活动场景-支付按钮 -->
      <!-- 常规场景-支付按钮 -->
      <block wx:else>
        <form
          wx:if="{{ (payAmount > 0 || isSelectVoucher || isSelectExchangeCard) && !noDeliveryTimeInfo }}"
          name='pagoda'
          report-submit='true'
          class="confirm-form">
          <!-- 禁用按钮条件： 支付金额小于等于0 && 超过截单时间 && 配送模式下未达起送金额 -->
          <button
            wx:if="{{ differenceAmountStart && differenceAmountStart > 0 }}"
            class='confirm-button button-disabled'>差¥{{common.formatPrice(differenceAmountStart)}}起送</button>
          <!-- 没有代金券且支付金额等于0 不可支付 -->
          <button
            wx:else
            class='confirm-button'
            bindtap="submitFormBefore"
            disable="{{ payAmount <= 0 && !isSelectVoucher }}">{{ m1.getPayBtnText(isSelectRechargeAct, isMixPay, selectPagodaPay, selectWxPay, selectUnionPay) }}</button>
        </form>
        <view class="disabled_btn" wx:else>
          <button wx:if="{{differenceAmountStart && differenceAmountStart > 0}}" class='confirm-button button-disabled'>差¥{{common.formatPrice(differenceAmountStart)}}起送</button>
          <button wx:else class='confirm-button button-disabled'>立即支付</button>
        </view>
      </block>
      <!-- 常规场景-支付按钮 -->
    </view>

  </view>

  <!-- 商品清单 wx:if="{{goodList.length > 1}}" -->
  <popup  popupTitle="共{{m1.totalGoodsCount(goodList)}}件商品" isShowPopup="{{isShowPopup}}">
    <scroll-view class="goods-list" scroll-y="true">
      <goods-item wx:for="{{goodList}}" wx:key="index" goodsObj="{{item}}" applyVipCard="{{vipCardIsSeleted}}" fruitFans="{{fruitFans}}" isFruitFansGray="{{isFruitFansGray}}" currentChooseExchangeCard="{{currentChooseExchangeCard}}"></goods-item>
    </scroll-view>
  </popup>

  <!-- 餐盒费明细 -->
  <popup popupTitle="餐盒费明细" isShowPopup="{{showPackDetail}}" selfClose="{{false}}" bindcloseShowPopup="closePackListPopu">
    <view class="pack-list-box">
      <view class="pack-explain">
        <view class="pack-explain-desc">说明</view>
        <text class="pack-explain-value">需要切块和取肉装盒等服务的水果，每份需要收取餐盒费，收费情况见明细。</text>
      </view>
      <scroll-view class="pack-list" scroll-y="true">
        <view class="pack-list-item" wx:for="{{paraGoodsList}}" wx:if="{{item.selectSpecsServiceList[0] && item.selectSpecsServiceList[0].schemePrice && item.isGoodsValid === 'Y'}}" wx:key="index">
          <view class="pack-item-desc text-hidden">{{item.goodsName}} {{item.selectSpecsServiceList[0].serviceName}}</view>
          <view class="pack-item-value">¥{{common.formatPrice(item.count * item.selectSpecsServiceList[0].schemePrice)}}</view>
        </view>
      </scroll-view>
      <view class="pack-count">
        <view class="pack-count-desc">合计</view>
        <view class="pack-count-value">¥{{common.formatPrice(totalBoxAmount)}}</view>
      </view>
      <view class="close-btn close-packlist" style="margin-bottom: {{safeAreaBottom ? safeAreaBottom + 'px' : '32rpx'}}" bindtap="closePackListPopu">我知道了</view>
    </view>
  </popup>

<!--  包装费说明 -->
  <popup popupTitle="包装费说明" showTitleLine="{{false}}" isShowPopup="{{showPackingFee}}" selfClose="{{false}}" bindcloseShowPopup="closePackingFeePopup">
    <view class="pack-explain-box">
      <view class="pack-explain pack-padding">
        <view class="pack-explain-desc">说明</view>
        <view class="pack-explain-value">本订单为保障商品的完整性和安全性，打包配送时将进行适当外部包装。根据国家相关法律法规，该部分包装物料需收取一定费用。如本单商品未使用外部包装物料，或您对包装费用存在异议等问题，可致电客服申请包装费用退款或咨询包装费用收取的相关问题。客服热线:400-181-1212。</view>
      </view>
      <view class="pack-list-item pack-padding">
        <view class="pack-item-desc">包装费</view>
        <view class="pack-item-value">¥{{common.formatPrice(packAmount)}}</view>
      </view>
      <view class="close-btn" style="margin-bottom: {{safeAreaBottom ? safeAreaBottom + 'px' : '32rpx'}}" catchtap="closePackingFeePopup">我知道了</view>
    </view>
  </popup>

  <popup popupTitle="及时达配送费说明" showTitleLine="{{false}}" isShowPopup="{{showFreightTip}}" selfClose="{{false}}" bindcloseShowPopup="closeFreightTipPopup">
    <view>
      <view class="tip-desc" wx:for="{{freightTip}}" wx:key="index"><text wx:if="{{freightTip.length > 1}}">{{index + 1}}、</text>{{item}}</view>
      <view class="close-tip-btn" style="margin-bottom: {{safeAreaBottom ? safeAreaBottom + 'px' : '32rpx'}}" catchtap="closeFreightTipPopup">我知道了</view>
    </view>
  </popup>
  <!-- 心享节省说明 -->
  <mouth-save
    bind:close="closeMouthSave"
    show="{{showMouthSaveBox}}"
    vipCardInfo="{{vipCardInfo}}"
    payAmountCurrent="{{payAmount}}"
    payAmountVirtual="{{payAmountVirtual}}"
    bind:openMouthCard="openMouthCardFromMouthSave"
    bind:cancelMouthCard="cancelMouthCardFromMouthSave"
  ></mouth-save>
  <!-- 月卡|年卡权益详情弹窗 -->
  <popup-benefits
    show="{{popupBenefits.show}}"
    url="{{popupBenefits.url}}"
    bind:closePopupBenefits="closePopupBenefits"
  ></popup-benefits>
</view>

<user-error-modal isShow="{{showUserErrorModal}}"></user-error-modal>

<!--配送收货地址信息-->
<template name="delivery-address-info">
  <view catchtap="openAddressList">
    <view class="info-main">
      <view class='info-address text-hidden-line2'>
        <view wx:if="{{address.label}}" class="address-label" style="{{address.labelStyle}}">{{address.label}}</view>
        <text>{{address.gisAddress}}{{address.address}}</text>
      </view>
      <image class="right-arrow" src='/homeDelivery/source/images/icon_right_arrow_gary.png'></image>
    </view>
    <view class="info-recipient">
      <view class="reci-name text-hidden">{{address.name}}</view>
      <view class="reci-phone">{{common.formatPhoneNumber(address.phoneNumber)}}</view>
      <view class="delivery-store"><text>(</text><text class="text-hidden">{{storeInfo.shortName}}配送</text><text>)</text></view>
    </view>
    <view class="order-tips" wx:if="{{showExceedDistanceTip}}">所选地址距您1km以上，请仔细核对</view>
  </view>
</template>

<!--自提门店信息-->
<template name="take-store-info">
  <view class='info-left' catchtap="openStoreList">
    <view class='info-title text-hidden'>{{storeInfo.shortName || storeInfo.storeName}}</view>
    <view class='info-main'>
      <view class='main-left'>{{storeInfo.address || ''}}</view>
      <view class='main-right info-right'>
        <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' class='right-arrow'></image>
      </view>
    </view>
    <view class="address-store" wx:if="{{storeInfo.openingTime}}">门店营业时间：{{common.getStoreBusinessTime(storeInfo.openingTime, storeInfo.startTime, storeInfo.endTime)}}</view>
    <view class="order-tips">
      助力环保，人人有责，提货请自备购物袋或到店购买哦~
    </view>
  </view>
</template>

<!-- 兑换卡 -->
<template name="exchange-card-bar">
  <view class="coupon-content" bindtap="selectedExchangeCard">
    <view class="align-items-center">
      <text class="coupon-label">兑换卡</text>
    </view>
    <view class="align-items-center">
      <text class="no-coupon" wx:if="{{isNoExchangeCard}}">暂无兑换卡</text>
      <text class="no-coupon" wx:elif="{{!isHasAvailableExchangeCard}}">暂无可用</text>
      <text class="coupon-val" wx:elif="{{!isSelectExchangeCard}}">{{availableExchangeCardCount}}张可用</text>
      <text class="coupon-val" wx:else="{{isSelectExchangeCard}}"><text class="rmb-sign">-¥</text>{{common.formatPrice(exchangeCardValueTotal)}}</text>
      <image src="/homeDelivery/source/images/icon_right_arrow_gary.png" class="right-arrow"></image>
    </view>
  </view>
</template>

<!-- 优惠券 -->
<template name="coupon-bar">
  <view class='coupon-content' bindtap="toSelectedCoupon">
    <view class="align-items-center">
      <text class="coupon-label">优惠券</text>
    </view>
    <view class="align-items-center">
      <text class="coupon-tips-max__vouchers" wx:if="{{isBetterSelectCoupon}}">已选最大优惠</text>
      <text class="no-coupon vip-coupon" wx:if="{{!couponList.length && superVipStatus === 'C' && showSelectVipCoupon && !vipCardIsSeleted}}">{{showSelectVipCoupon}}</text>
      <text class="no-coupon" wx:elif="{{!couponList.length && !unAvailableCouponList.length}}">暂无优惠券</text>
      <text class="no-coupon" wx:elif="{{!couponList.length}}">暂无可用</text>
      <text class="avail-coupon" wx:elif="{{!selectedCouponList.length}}">{{couponList.length}}张可用</text>
      <text class="coupon-val {{isBgxxVipCoupon ? 'vip-coupon' : ''}}" wx:else="{{selectedCouponList.length}}"><text class="rmb-sign">-¥</text>{{common.formatPrice(totalCouponMoney)}}</text>
      <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' class ='right-arrow'></image>
    </view>
  </view>
</template>

<!-- 优惠券 -->
<template name="vouchers-bar">
  <view class='vouchers-content' bindtap="selectedVoucher">
    <view class="align-items-center">
      <text class="coupon-label">代金券</text>
    </view>
    <view class="align-items-center">
      <text class="coupon-tips-max__vouchers" wx:if="{{isBetterSelectVoucher}}">已选最大优惠</text>
      <text class="no-vouchers" wx:if="{{isNoVoucher}}">暂无代金券</text>
      <text class="no-vouchers" wx:elif="{{!isHasAvailableVoucher}}">暂无可用</text>
      <text class="coupon-val" wx:elif="{{!isSelectVoucher}}">{{availableCount}}张可用</text>
      <text class="coupon-val" wx:else="{{isSelectVoucher}}"><text class="rmb-sign">-¥</text>{{common.formatPrice(voucherCouponValue)}}</text>
      <image src='/homeDelivery/source/images/icon_right_arrow_gary.png' class ='right-arrow'></image>
    </view>
  </view>
</template>

<!-- 小计 -->
<template name="goods-Amount-calculation">
  <view class="amount-calculation">
    <view class="calculate-title">小计:</view>
    <text class="line-through-value" wx:if="{{originGoodsTotalAmount !== goodsTotalAmount}}">¥{{common.formatPrice(originGoodsTotalAmount)}}</text>
    <text class="calculate-value"><text class="rmb-sign">¥</text>{{common.formatPrice(goodsTotalAmount)}}</text>
  </view>
</template>

<!-- 发票文案(用不上了,使用了getVoucherText来生成) -->
<template name="invoice-text">
  <!-- 只有果币支付 -->
  <view wx:if="{{!selectWxPay}}">
    <view class="payment-text" wx:if="{{selectPagodaPay && !isSelectVoucher}}">*使用钱包余额支付，不可开票</view>
    <view class="payment-text" wx:elif="{{!selectPagodaPay && isSelectVoucher }}">*使用代金券支付，不可开票</view>
    <view class="payment-text" wx:elif="{{selectPagodaPay && isSelectVoucher}}">*使用钱包余额和代金券支付，不可开票</view>
  </view>
  <!-- 有微信支付 -->
  <view wx:elif="{{selectWxPay}}">
    <view class="payment-text" wx:if="{{storeInfo && storeInfo.isSupportInvoice === 'N'}}">*该门店无法开具电子发票，可联系门店开具纸质发票</view>
    <view class="payment-text" wx:elif="{{deliveryWay == 'deliveryToDoor'}}">*收货后可在订单详情页中开具电子发票</view>
    <view class="payment-text" wx:elif="{{deliveryWay == 'selfTake'}}">*自提后可在订单详情页中开具电子发票</view>
  </view>
</template>

<!-- 自定义modal弹窗 -->
<confirm-modal
  contentText="{{applyVipCardTipsOptions.contentText}}"
  isShowConfirmModal="{{applyVipCardTipsOptions.showModal}}"
  showCancel="{{applyVipCardTipsOptions.showCancel}}"
  confirmText="{{applyVipCardTipsOptions.confirmText}}"
  clickMaskToClose="{{false}}"
  bind:confirm="customModalConfirm"
  >
</confirm-modal>

<!-- 自定义时间选择组件 -->
<pagoda-popup
  wx:if="{{visible}}"
  model:visible="{{visible}}"
  position="bottom"
  title="{{seletTimePopupName}}"
  round="{{true}}"
  head-class="head-class"
  title-class="title-class"
  z-index="{{1000}}"
  height="48vh"
  desc="详情描述">
    <pagoda-picker
    time-list="{{multiArray}}"
    bindpick="pick"
    bindchangeDate="changeDate"
    currentIndex="{{multiIndex}}"
    />
  </pagoda-popup>

<!-- 验证码弹窗组件 -->
<pagoda-popup
  model:visible="{{visibleSMS}}"
  showLeftArrow="{{true}}"
  showClose="{{false}}"
  round="{{true}}"
  z-index="{{1000}}"
  clickOverlayClose="{{false}}"
  position="bottom"
  title="更换支付方式"
  head-class="sms-head-class"
  title-class="sms-title-class"
  height="600rpx"
  bind:onBack="onBack">
    <sms-validate
      model:visible="{{visibleSMS}}"
      bind:validated="validated"
    />
</pagoda-popup>

<request-subscribe title="打开提醒，获得订单变更通知" show="{{subscribe.show}}" tmpl-ids="{{subscribe.tmplIds}}" bind:close="onSubscribeClose" />

<common-loading />

<captcha id="comp-captcha"/>

<over-weight-popup
  showPopup="{{ showOverWeightPopup }}"
  bindcallStore="callStore"
  bindclose="onOverWeightClose" />
