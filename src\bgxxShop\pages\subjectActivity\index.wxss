@import "../../template/suspenseCart/index.wxss";

.subject-activity {
  width: 100%;
  height: 100vh;
  -webkit-overflow-scrolling: touch;
  background-color: rgba(247, 247, 247, 1);
}

.subject-activity .content {
  width: 100%;
  /* max-height: 100%; */
  /* overflow-y: auto; */
  display: flex;
  flex-direction: column;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 20rpx;
}

#ob-reference {
  position: fixed;
  top: 0;
  width: 100%;
  height: 10rpx;
}

#fixNav {
  position: fixed;
  width: 100%;
  top: 0;
  overflow: hidden;
}

.content .subject-item {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.content .subject-item-pic {
  margin-top: 24rpx;
}

.subject-item .subject-image {
  width: 100%;
  height: auto;
}

.subject-image image {
  width: 100%;
  display: block;
  margin-top: -2rpx;
}

.subject-item .subject-goods {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.subject-item .subject-goods-double {
  justify-content: flex-start;
  margin: 12rpx 0rpx 16rpx 24rpx;
}

.subject-goods-double .goods-preview-double {
  width: calc(50% - 24rpx);
  padding: 16rpx 22rpx 0 0;
}

.subject-item .subject-goods-three {
  justify-content: flex-start;
  margin: 12rpx 0rpx 16rpx 24rpx;
}

.subject-goods-three .goods-preview-three {
  width: 29vw;
  padding: 16rpx 24rpx 0 0;
}

.goods-toggle-button {
  width: 544rpx;
  height: 72rpx;
  background: #FFFFFF;
  border-radius: 44rpx;
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
  margin: 20rpx auto;
}

.goods-toggle-text {
  font-size: 28rpx;
  font-weight: 400;
  color: #858996;
}

.goods-toggle-icon {
  width: 20rpx !important;
  height: 20rpx !important;
  margin-left: 10rpx;
}

.buttomTips {
  width: 421rpx;
  height: 139rpx;
  margin: 0 auto;
}

.buttomTips>image {
  width: 100%;
  height: 100%;
}

.mt5 {
  margin-top: 5rpx;
}

.sellout_goods-box {
  display: flex;
  flex-wrap: wrap;
}

.goods-preview-three .goods-preview-vertical-small {
  height: 442rpx;
}

.sellout_goods-box {
  width: 100%;
}

.sv-anchor {
  position: relative;
}

.inner-anchor {
  position: absolute;
  width: 100%;
  opacity: 0;
  /* top: -110rpx; */
  z-index: -1;
}

.start-anchor {
  /* height: 88rpx; */
  bottom: 0;
}

.end-anchor {
  height: 1rpx;
  bottom: 115rpx;
}
.no-data{flex:1;display:flex;flex-direction:column;justify-content: space-between;text-align: center;}
.icon-no-data {
  width: 441rpx;
  height: 309rpx;
  margin: 0px auto;
  margin-top: 155rpx;
}
.l-t{
  font-size:30rpx;
  font-weight:400;
  color:rgba(51,51,51,1);
  margin-top: 12rpx;
  text-align: center;
}

.back-btn{width:280rpx;height:88rpx;line-height: 88rpx;text-align: center;color:#fff;font-size: 30rpx;background-color:#008C3C;border-radius: 44rpx;margin:40rpx auto;}

.scroll-view-blank {
  position: absolute;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #f7f7f7;
  z-index: 1001;
}
.scroll-view-blank image {
  width: 100%;
}
