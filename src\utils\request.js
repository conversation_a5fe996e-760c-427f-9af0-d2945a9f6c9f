const commonObj = require('../source/js/common').commonObj
const requestLog = require('./requestLog/index')
const log = require('../utils/log.js')
const { checkIsQy } = require('../utils/util')
const { baseUrl, ENV,
  CLOUD_FLAG
} = require('./config')
const { isServerlesUrl , checkIsWhitelist } = require('./proxySlrRoute')
import userStore from '~/stores/userStore'
import {
  getEncryptKey,
  getAccessHeaders,
  updateTimeDiff
} from '../service/encrypt';
import { outLoginType } from '../source/const/globalConst'
import { getCustomerInfo, newUserOrNotLogin } from '../source/js/requestData/activityPrice'
// import {
//   getEncryptKey,
//   getAccessHeaders,
//   updateTimeDiff
// } from '../service/encrypt.source';
const tryCounts = 1 // 限流报错后重试次数

/**是否已切换过全局云环境 */
let isChangeGlobalCloud = false
const sleep = (timeountMS) => new Promise((resolve) => {
  setTimeout(resolve, timeountMS);
});
let globalCould = null
// 控制重复提交
const tryRequest = {
  tryTask: {},
  getTaskLength() {
    return Object.keys(this.tryTask).length
  },
  addTask(request) {
    const task = this.getTask(request);
    if (task) return
    const key = this.getKey(request)
    this.tryTask[key] = true;
  },
  getTask(request) {
    const key = this.getKey(request);
    return this.tryTask[key];
  },
  delTask(request) {
    const key = this.getKey(request);
    delete this.tryTask[key];
  },
  getKey(request) {
    const { method, url, data } = request;
    const sData = typeof data === 'string' ? JSON.parse(data || '{}') : data;
    return JSON.stringify({ method, url, sData });
  },
};
// 判断是serverless或电商接口
function isNewService(url) {
  return isServerlesUrl(url) || url.indexOf(`${ENV}.pagoda.com.cn`) > -1
}
function getService(domain) {
  if (!domain) {
    return 'tencent-'
  }
  // 使用正则表达式模式提取值
  const pattern = `\\.(\\w+\\-)${ENV}`;
  const regex  = new RegExp(pattern)
  const match = domain.match(regex );
  if (match) {
    return match[match.length -1 ]
  } else {
    return 'tencent-'
  }
}
function getSlrUrl(url) {
  if (!isServerlesUrl(url) ||  url.indexOf('http') > -1) {
    return url
  }
  if (url.indexOf('wxapp/') > -1) {
    return `${baseUrl.serverlessApi}${url}`
  }
  if (url.indexOf('dskhd/') > -1) {
    return `${baseUrl.dskhdSlrApi}${url}`
  }
}
// 获取baseurl，配置在common.js中，如果在api.js中配置了则此处不做处理。
async function getBaseUrl (options, header) {
  const { url = '', isTryNextCloud = false } = options
  // 切换云不用在此处拼接url
  // if (isTryNextCloud) {
  //   return url
  // }
  // 新的腾讯云环境，包含serverless和电商
  let {
    isGrayTime,
    domainService = '',
    noGrayDomainList
  } = await checkIsWhitelist(header)

  if (isNewService(url)) {
    const allUrl = getSlrUrl(url)
    // 灰度期间不切换云
    if(isTryNextCloud && !isGrayTime) {
      // 切换为除灰度服务名以外且非自身的另外服务名
      let curService = `.${domainService}${ENV}.pagoda.com.cn`, changeDomainList = [];
      noGrayDomainList.forEach( item => {
        if (allUrl.indexOf(item) > -1) {
          curService=  item
        } else {
          changeDomainList.push(item)
        }
      })
      // 切换全局云环境，只切换一次
      if (!isChangeGlobalCloud) {
        globalCould = getService(changeDomainList[0])
        isChangeGlobalCloud = true
      }
      return (changeDomainList[0] && curService) ? allUrl.replace(curService, changeDomainList[0]) : allUrl
    }

    // 如果为白名单用户
    if (globalCould !== null) {
      domainService = globalCould
    }
    const newServiceUrl =  allUrl.replace(`.${CLOUD_FLAG.tx}${ENV}.pagoda.com.cn`, `.${domainService}${ENV}.pagoda.com.cn`);
    return newServiceUrl
  } else if (url.indexOf('://') > -1) { // 已经拼接了域名
    return url
  }
  // 老逻辑
  if (globalCould !== null) {
    domainService = globalCould
  }
  const complateUrl = commonObj.PAGODA_DSN_DOMAIN + url
  const newComplateUrl = complateUrl.replace(`.${CLOUD_FLAG.tx}${ENV}.pagoda.com.cn`, `.${domainService}${ENV}.pagoda.com.cn`);
  return newComplateUrl
}
// 用于拼接非request中封装请求的url
async function getExtraUrl(url) {
  const header = await getHeaders({ url: url })
  return getBaseUrl({ url: url },header)
}
const isTryRequest = (function () {
  const retryObj = {}
  return function(options = {}) {
    // 测试环境不重试
    if (['test', 'staging'].includes(ENV)) {
      return
    }
    const { url } = options
    console.log(url)
    const path = url.split('pagoda.com.cn')[1]
    options.isTryNextCloud = true
    // 重试过了不再次重试
    console.log('retryObj',retryObj)
    if (retryObj[path]) {
      log.error('try request again')
      return false
    }
    retryObj[path] = 1
    return true
  }
})()

let userPhoneInfo = wx.getSystemInfoSync() || {}
const requestId = new Date().getTime().toString(16) + Math.random().toString(16).substring(2,10)
function getHeaders ({url = '', header = {}, data }) {
  const { unionid, openid } = wx.getStorageSync('wxSnsInfo') || {}
  const isQy = checkIsQy()
  let { userToken, userID, userType = 'wb', isEshopNewCustomer } = wx.getStorageSync('user') || {}
  const { cityID, cityCode, deliveryCenterCode, storeCode, storeID } = wx.getStorageSync('timelyCity') || {}
  // 如果没有userId，去请求参数取
  if (!userID && data) {
    // 请求参数里面的userID
    const customerID = data.customerID || data.customerId || ''
    if (customerID && String(customerID) !== "-1") {
      userID = customerID
    }
  }

  const userHeaderInfo = {
    'x-customerId': userID,
    'is-fruit-fans-gray': String(userStore.isFruitFansGray),
    'is-fruit-fans': String(userStore.isFruitFans),
    'is-new-user': String(newUserOrNotLogin()),
    'is-vip': String(getCustomerInfo().IS_VIP_CUSTOMER),
  }
  if (['wxapp/', 'api', 'localhost', 'dskhd/'].some(str => url.indexOf(str) > 0)) {
    let headers = {
      'appVersion': commonObj.appVersion,
      'X-DEFINED-appinfo': JSON.stringify({
        'channel': isQy ? 'uniAppWorkWx': 'miniprogram',
        'verName': commonObj.appVersion,
        'model': userPhoneInfo.model || '', // 手机品牌型号（如：Huawei Mate 10）
        'os': userPhoneInfo.system || '', // 系统版本(如：Android 7.0)
        'wxVersion': userPhoneInfo.version || '', // 微信版本号
        'unionID': unionid || '', // 用户unionID
        'openId': openid || '',
        'appVersion': commonObj.appVersion, // 小程序版本号
        'osType': 'wxMini'
      }),
      'x-appinfo': JSON.stringify({
        'x-app-version': commonObj.appVersion,
        'x-app-name': 'wxapp'
      }),
      'x-userinfo': JSON.stringify(userHeaderInfo),
      'x-fruit-cityinfo': JSON.stringify({
        'x-storeId': storeID,
        'x-storeCode': storeCode || '',
        'x-deliveryCenterCode': deliveryCenterCode,
        'x-cityId': cityID,
        'x-cityCode': cityCode
      }),
      'x-storeCode': storeCode || '',
      'x-deliveryCenterCode': deliveryCenterCode,
      'x-cityCode': cityCode,
      'x-appVersion': commonObj.appVersion,
      'x-defined-verinfo': isQy ? 'uniAppWorkWx': 'miniProgram', // miniProgram兼容电商老接口
      'userToken': userToken || '',
      'x-has-storeCode': storeCode && storeCode !== '0212' ? 'true' : 'false',
      'x-requestid': requestId,
      'x-member-type': userType
      // 'x-middle-server': 'true',
    }
    if (userID) {
      headers['x-memberId'] = userID
    }
    return headers
  }
  header['x-defined-verinfo'] =  isQy ? 'uniAppWorkWx': 'wx'
  return header
}

/**
 * 获取serverless接口请求权限检验请求头参数
 * @param {Object}
 * @returns {
 *  timeStamp, // 时间戳
 *  accessToken // 生成的加密串
 * }
 */
async function getServerlessAccessHeaders({ reqParams, reqUrl }) {
  // 非serverless接口不加
  if (!reqUrl || !isServerlesUrl(reqUrl)) {
    return {}
  }
  if (reqUrl.indexOf("/config/encrypt") > -1) {
    return {}
  }
  const result = await getAccessHeaders({ reqParams })
  return result
}

// 退出登录 loginOutBackHomePage 退出登录，是否回到首页
function signOut ({loginOutBackHomePage = false, logoutCause = outLoginType.deviceChangeLogout} = {}) {
  const app = getApp()
  if (!app.globalData.isShowSignoutModal) {
    return
  }
  app.globalData.isShowSignoutModal = false
  // 退出登录，直接清空登录态，不需要等待用户点击弹窗操作
  app.resetGlobalUserInfo()
  commonObj.showModal('提示', '您的登录已过期，是否重新登录？', true, '我知道了', '',  res => {
    if (res.confirm) {
      if (loginOutBackHomePage) {
        app.toSignIn({finishToRouter: 'homeDelivery'})
      } else {
        app.signIn()
      }
    } else if (res.cancel) {
      wx.reLaunch({
        url: '/pages/index/index'
      })
    }

  })
  app.signOut({
    logoutCause
  })
}
// 异常用户
function errHandle (res) {
  const app = getApp()
  wx.showModal({
    title: '提示', //弹框标题
    content: res.data.errorMsg,
    confirmText: '我知道了',
    showCancel: false,
    success: function (res) {
      app.signOut({
        logoutCause: outLoginType.accountErrorLogout
      })
      app.toSignIn({ finishToRouter: 'homeDelivery'})
    }
  })
}
// 参数加密
async function Encrypt(data, type) {
  if (!type) {
    return ''
  }
  let key = ''
  if (type === 'pwd') {
    key = await getEncryptKey('pwd', request)
  } else if (type === 'token') {
    key = wx.getStorageSync('token')
  }
  return commonObj.Encrypt(data, key)
}
function checkServer(res) {
  return [999, 500].includes(res.data.errorCode) || ( 500 <= res.statusCode && res.statusCode < 600)
}


function checkIsSuccess(res) {
  return res.data.errorCode === 0 || res.data.resultCode === 0
}

/**
 * 上报接口异常，除200外
 */
function reportApiError(req, res) {
  if (res.statusCode === 200) return
  const { url } = req
  getApp().monitor.report({
    type: 'request',
    content: {
      // 请求url
      url,
      // http状态码
      code: res.statusCode,
      // 响应
      // success里的响应是data,fail里的是errMsg
      data: res.data || res.errMsg
    }
  })
}

/**
 * 设置用户id
 * @param {*} data 
 */
function setCustomerID(data) {
  //  data为对象 且 传递了用户id 且 用户id为数字，转成字符串类型。防止部分后端接口返回了数字类型的的用户id导致serverless报错
  if (typeof data === 'object' && data.customerID && typeof data.customerID === 'number') {
    data.customerID = String(data.customerID)
  }
}

/**
 * 设置请求url
 * @param {*} options 
 * @param {*} header 
 */
async function setUrl(options, header) {
  if (options.isLocal && ENV !== 'prod') {
    return `http://localhost:3001/draft_exc${options.url}`
  } else {
    // 拼接域名
    const url = await getBaseUrl(options, header)
    return url
  }
}

/**
 * 获取请求参数
 * @param {*} data 
 * @param {*} encryptFlag 
 * @param {*} encryptType 
 * @returns 
 */
async function getReqData(data, encryptFlag, encryptType) {
  if (encryptFlag && encryptType) {
    const encryptData = await Encrypt(data, encryptType)
    return { data:  encryptData }
  } else {
    return data || {}
  }
}

/**
 * @param {*} options
 * type: Object
 * default:{isloading: true, method: 'POST'}
 */
function request (options = {}) {
  const requestPromise = new Promise(async (resolve, reject) => {
    // loginOutBackHomePage为true，表示剔出登录，需要返回首页   isEncryptRes 是否需要解密
    let {
      encryptFlag,
      encryptType,
      data,
      page,
      loginOutBackHomePage = false,
      isReturnLoginOutError = false,
      loginOutErrorHandle,
      url,
      isEncryptRes = false,
      isTryNextCloud = false, // 是否尝试下个云  setHttpUrl中用到了
      timeout = 15000
    } = options

    //  data为对象 且 传递了用户id 且 用户id为数字，转成字符串类型。防止部分后端接口返回了数字类型的的用户id导致serverless报错
    setCustomerID(data)

    // 设置请求头信息
    const header = getHeaders(options)

    options.url = await setUrl(options, header)

    // 设置serverless签名
    const accessHeaders = await getServerlessAccessHeaders({ reqParams: data, reqUrl: options.url });
    Object.assign(header, accessHeaders, options.header || {})
    let { isLoading = true, mask = false } = options
    if (isLoading) {
      wx.showLoading({
        title: '加载中',
        mask
      })
    }
    let requestTask = {}
    requestLog.print(options)
    // 处在重试过程中的接口，不在触发其他请求
    if (tryRequest.getTaskLength() && tryRequest.getTask(options) && !options.tryCount) {
      resolve({})
      return
    }
    const reqData = await getReqData(data, encryptFlag, encryptType)
    requestTask = wx.request({
      url: options.url,
      method: options.method || 'POST',
      data: reqData,
      header,
      timeout: timeout,
      success: async res => {
        if (isLoading) wx.hideLoading();


        // serverless调用中台接口挂了 或serverless接口本身挂了
        if(checkServer(res)) {
          console.error(res)
          if(isTryRequest(options)) {
            getApp().monitor.report({
              type: 'request',
              content: {
                url,
                resData: res.data
              }
            })
            log.error('try request log', {
              errorCode: res.data.errorCode,
              statusCode: res.statusCode,
              url
            })
            options.isTryNextCloud = true
            resolve(request(options))
            return
          }
          reject(res.data)
        } else if ([-1002,-12002].includes(res.data.errorCode)) {
          wx.hideLoading();
          const isTokenExpire = res.data?.error?.message === '自然过期'
          signOut({
            loginOutBackHomePage,
            logoutCause: isTokenExpire ? outLoginType.tokenExpire : outLoginType.deviceChangeLogout
          })
          // isReturnLoginOutError 业务模块是否要捕获踢出登录异常
          if (loginOutErrorHandle) {
            loginOutErrorHandle()
          }
          isReturnLoginOutError && reject(res.data)
        } else if ([52005, 41000].includes(res.data.errorCode)) {
          wx.hideLoading();
          if (page) {
            page.setData && page.setData({ showUserErrorModal: true })
          } else {
            errHandle(res)
          }
        } else if (checkIsSuccess(res)) {
          isEncryptRes && (res.data.isEncryptRes = true) // 用于判断是否需要解密
          const data = handleResponse(res.data)
          resolve(data)
        }  else if (res.statusCode === 429) {
          // 限流接口重试请求
          tryRequest.addTask(options)
          !options.tryCount && (options.tryCount = 0)
          options.tryCount ++
          if (options.tryCount > tryCounts) {
            tryRequest.delTask(options)
            return reject( options.tryCount > 999 ? res: res.data)
          }
          await sleep(3000)
          resolve(request(options))
        } else {
          reject(res.data)
        }
        // 如果是serverless接口返回并且有服务端时间则更新
        if (isServerlesUrl(options.url) && res.data.systemTime) {
          updateTimeDiff(res.data.systemTime)
        }
        // 上报接口异常，除200外
        reportApiError(options, res)
      },
      fail: error => {
        console.error(error)
        wx.hideLoading()

        /**请求超时 */
        const isTimeOut = error && error.errMsg === 'request:fail timeout'
        const isAbort = error && error.errMsg === 'request:fail abort'
        getNetworkStatus(type => {
          console.log(type)
          if (!isAbort) {
            // 排除无网络的情况，且非手动终止 服务挂了更换其他云
            if(type !== 'none' && isTryRequest(options)) {
              options.isTryNextCloud = true
              resolve(request(options))
              getApp().monitor.report({
                type: 'request fail',
                content: {
                  url,
                  resData: error
                }
              })
            } else if (type === 'none') {
              commonObj.showNoNetModal()
            }
          }
          // log.error(url, data, error, type)
          getApp().sensors.track("failedRequest", {
            interface: url,
            errorCode: JSON.stringify(error),
            param: JSON.stringify(data),
            networkType: type
          })
        })
        reportApiError(options, error)

        if (isTimeOut && options.timeoutReject) {
          reject(error)
        }
      }
    })

    requestPromise.requestTask = requestTask
  })
  return requestPromise
}

/**
 * 处理响应的结果，如果有加密，直接解密出结果
 */
function handleResponse(result) {
  const { isEncryptRes = false, data } = result || {}
  if (!isEncryptRes) {
    return result
  }
  try {
    const decryptData = JSON.parse(
      commonObj.Decrypt(data, wx.getStorageSync('token') || '')
    )
    // console.log('decryptData', decryptData);
    return Object.assign(result, {
      data: decryptData
    })
  } catch (error) {
    // console.table(error)
  }
  return result
}

function getNetworkStatus(callback) { //判断网络状态
  wx.getNetworkType({
    success: function(res) {
      callback(res.networkType);
    }
  })
}

function get (options) {
  options.method = 'GET'
  return request (options)
}
function post (options) {
  return request (options)
}
const req = {
  get,
  post,
  getExtraUrl
}
export default req
export {
  get,
  post,
  getExtraUrl
}
