const app = getApp()
import { navigateGroupH5 } from '../../../../../service/group'
import config from '../../../../../utils/config'
import act from '../../../../../utils/activity'
import sensors from '../../../../../utils/report/sensors'
/** 社群h5链接生产环境对应的二维码 */
const prdUrl = 'https://resource.pagoda.com.cn/dsxcx/images/0aaed9be52d576ad880e32f2529e412a.png'
/** 社群h5链接测试环境对应的二维码 */
const testUrl = 'https://resource.pagoda.com.cn/dsxcx/images/dd0a87f96337e009b52341ee739a0e8f.png'

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    memberObj: {
      type: Object,
      value: {}
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    // 展示的二维码
    qrcodeValue: '',
    // 是否支持长按识别
    showMenuByLongpress: false,
    // banner数据
    bannerObj: {},
    // banner背景图
    bannerUrl: '',
    // 是否展示banner模块
    showBanner: false,
    // 是否为心享会员
    isSuperVip: false,
    noData: true,
    // banner位二维码链接
    qrcodeImgSrc: '',
  },
  observers: {
    memberObj(memberObj) {
      if (memberObj && Object.keys(memberObj).length) {
        const qrcodeImgSrc = config.ENV === 'prod' ? prdUrl : testUrl
        const { superVipStatus } = memberObj || {}
        const isSuperVip = superVipStatus ? superVipStatus !== 'C' : false
        this.setData({
          isSuperVip,
          qrcodeImgSrc
        })
        this.getManagerCodeByStore()   
      }
    }
  },
  lifetimes: {
    created() {
      this._data = {
        initedRect: false
      }
    },
    detached() {
      this._data.bannerInfoObserver && this._data.bannerInfoObserver.disconnect()
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 获取门店社群二维码
     */
    async getManagerCodeByStore() {
      const city = wx.getStorageSync('timelyCity') || {}
      const { storeCode = '', cityID } = city || {}
      // 已加入社群的用户不调此接口
      const { isAddGroup = false } = this.data.memberObj || {}
      let storeGroupCode = ''
      if (storeCode && !isAddGroup) {
        const result = await app.api.getManagerCodeByStoreCode({storeCode})
        const { qr_code = '' } =  result.data || {}
        storeGroupCode = qr_code
      }
      const hasStore = !!storeCode && !!storeGroupCode
      const paramsObj = {
        cityId: cityID,
        hasStore,
        storeGroupCode
      }
      this.getBanner(paramsObj)
    },
    /**
     * 获取banner
     */
    async getBanner(paramsObj) {
      const { cityId, hasStore, storeGroupCode } = paramsObj
      const { memberId, isAddGroup, isQuitAddGroup } = this.data.memberObj || {}
      const params = {
        customerID: memberId,
        cityId,
        isAddGroup,
        isQuitAddGroup,
        hasStore
      }
      try {
        const { data } = await app.api.getBanner(params)
        const { image = {}, communityIdentity } = data || {}
        const { qrcodeValue, showMenuByLongpress } = this.handleShowCode(storeGroupCode, communityIdentity)
        const bannerUrl = config.baseUrl.PAGODA_PIC_DOMAIN + image.picUrl
        this.setData({
          bannerObj: data,
          bannerUrl,
          qrcodeValue,
          showMenuByLongpress,
          showBanner: !!data,
          noData: false
        })
        this.bannerExposureReport()
      } catch {
        this.setData({
          showBanner: false,
          noData: false
        })
      }
    },
    /**
     * 处理二维码的展示和是否支持长按识别
     * communityIdentity：1：从未加过社群&首页有门店 * 2：从未加过社群&首页无门店 * 3：加入后又退群&首页有门店 * 4：加入后又退群&首页无门店 * 5：已加入社群
     * 首页有门店但查不到门店社群二维码，按相应场景的无门店情况处理
     */
    handleShowCode(storeGroupCode, communityIdentity) {
      let qrcodeValue = ''
      if (String(communityIdentity) === '5') {
        return {
          qrcodeValue,
          showMenuByLongpress: false
        }
      }
      const showMenuByLongpress = ['1', '3'].includes(communityIdentity)
      qrcodeValue = showMenuByLongpress && storeGroupCode ? storeGroupCode : this.data.qrcodeImgSrc
      return {
        showMenuByLongpress,
        qrcodeValue
      }
    },
    /**
     * 跳转页面
     */
    navigateToPage() {
      const { communityIdentity } = this.data.bannerObj
      // 已加入社群的跳转配置的广告，其他场景跳转社群h5页面
      if (String(communityIdentity) === '5') {
        act.toActivityPage(this.data.bannerObj)
      } else {
        navigateGroupH5()
      }
      const reportInfo = this.getBannerReportInfo()
      sensors.track('ClickAdvertisement', '1810_181006002', reportInfo)
    },
    /**
     * 点击二维码埋点
     */
    qrcodeReport() {
      const reportInfo = this.getBannerReportInfo()
      sensors.track('ClickAdvertisement', '1810_181006001', reportInfo)
    },
    /**
     * 获取埋点信息
     */
    getBannerReportInfo(needBlock = false) {
      const { bannerType, id, name, openValue } = this.data.bannerObj || {}
      const reportObj = {
        'bannerType': bannerType,
        'banner_id': id,
        'banner_name': name,
        'Position': 0,
        'url': openValue
      }
      return needBlock ? Object.assign(reportObj, {
        'blockName': '社群模块',
        'blockCode': 181006
      }) : reportObj
    },
    /**
     * 监听banner位模块出现在视窗内
     */
    bannerInfoObserver() {
      let firstTime = true
      const reportInfo = this.getBannerReportInfo(true)
      this._data.bannerInfoObserver && this._data.bannerInfoObserver.disconnect()
      const bannerInfoObserver = wx.createIntersectionObserver(this)
      bannerInfoObserver.relativeToViewport().observe('#bannerInfo', res => {
        if (res.intersectionRatio !== 0 && firstTime) {
          sensors.track('ExposureAdvertisement', reportInfo)
          firstTime = false
        }
      })
      this._data.bannerInfoObserver = bannerInfoObserver
    },
    /**
     * 广告位曝光埋点
     */
    bannerExposureReport() {
      if(this._data.initedRect) {
        return
      }
      wx.nextTick(() => {
        this.bannerInfoObserver()
        this._data.initedRect = true
      })
    }
  }
})