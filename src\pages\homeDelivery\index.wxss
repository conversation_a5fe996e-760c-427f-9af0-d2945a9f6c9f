@import "./skeleton/index.skeleton.wxss";

page {
  background-color: #F8F8F8;
}

.gray-theme {
  filter: grayscale(1);
}

.prevent-screen {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000000;
}

.homeDelivery-container {
  position: relative;
  width: 100vw;
}
.scroll-box {
  height: 100vh;
  background-color: #F8F8F8;
  box-sizing: border-box;
  position: relative;
}
.safe-bottom {
  padding-bottom: calc(48px + constant(safe-area-inset-bottom));
  padding-bottom: calc(48px + env(safe-area-inset-bottom));
}
.inner-container {
  width: 100%;
  box-sizing: border-box;
  background-color: #00A34F;
  background-repeat:no-repeat;
  background-size: 100% auto;
}

.inner-container .content-modal {
  width: 100%;
  background-color: #f8f8f8;
  background-image: linear-gradient(173deg, #FFFFFF 0%, #fcfcfc00 54%);
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}

.inner-container.activity .content-modal {
  margin-top: -16rpx;
}

.content-modal--gray {
  background-image: unset !important;
  --search-box-background: #f8f8f8;
}

.inner-container .goods-modal {
  width: 100%;
  background-repeat:no-repeat;
  background-size: 100% auto;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.order-bar{
  background-color: #fff;
  margin: 20rpx 24rpx 0;
  border-radius: 12rpx;
}

/* 自定义下拉刷新动画 */
.custom-refresh-zone{
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}
.tips-hide .custom-refresh-zone-tips{
  display: none;
}
.custom-refresh-zone-tips,
.custom-refresh-zone-tips-loading{
  width: 8em;
  text-align: center;
  position: relative;
  margin-top: 27rpx;
  margin-right: 16rpx;
  font-size: 20rpx;
  color: #000;
}

.tips-more view:nth-child(1){
  display: block;
}

.tips-release view:nth-child(2){
  display: block;
}

.tips-more .custom-refresh-zone-tips-loading,
.tips-release .custom-refresh-zone-tips-loading{
  display: none;
}
.custom-refresh-zone-img{
  --frames-count: 71;
  --frames-duration: 3s;
  width: 74rpx;
  height: 62rpx;
  line-height: 0;
  box-sizing: content-box;
  background-image: url(https://resource.pagoda.com.cn/group1/M21/56/33/CmiWa2ELUraAOEPhAABNpK93cJo369.png);
  background-position-y: center;
}

.business .add-self-take {
  position: absolute;
  top: 0rpx;
  left: 20rpx;
  width: 70rpx;
  height: 64rpx;
}

/* 品质生鲜推荐模块 */

/* 添加到我的小程序提示 */
.add-app-tip {
  position: fixed;
  padding: 0 14rpx 0 19rpx;
  display: flex;
  align-items: center;
  height: 64rpx;
  line-height: 64rpx;
  font-size: 24rpx;
  color: #FFF;
  background-color: rgba(0,0,0,0.7);
  border-radius: 12rpx;
  box-sizing: border-box;
  z-index: 999999;
}
.add-app-tip:after {
  position: absolute;
  bottom: 100%;
  right: 100rpx;
  content: '';
  width: 0;
  height: 0;
  border-width: 0 14rpx 16rpx;
  border-style: solid;
  border-color: transparent transparent rgba(0,0,0,0.7);
}
.add-app-btn {
  margin-left: 12rpx;
  width: 115rpx;
  height: 42rpx;
  font-family: PingFangSC-Regular,PingFang SC;
  font-size: 24rpx;
  line-height: 42rpx;
  text-align: center;
  color: #005324;
  background: rgba(255,255,255,1);
  border-radius: 28rpx;
}
@keyframes backgroundGradient
  {
  from {
    background:rgba(0,0,0,0)
  }
  to {
    background:rgba(0,0,0,0.7)
  }
}
@-webkit-keyframes backgroundGradient /* Safari 和 Chrome */
  {
  from {
    background:rgba(0,0,0,0)
  }
  to {
    background:rgba(0,0,0,0.7)
  }
}

.containor-default .default-image{
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto 14rpx;
}
.containor-default .default-image image{
  width: 100%;
  height: 100%;
}
.containor-default .default-tips{
  font-size: 28rpx;
  color: #555555;
  line-height: 38rpx;
  margin: 0 auto 38rpx;
  text-align: center;
}
.containor-default .default-btn {
  width: 360rpx;
  background: #00A34F;
  border-radius: 48rpx;
  color: #fff;
  font-size: 32rpx;
  line-height: 2.8;
  text-align: center;
  margin: auto;
}

.icon-back-top image {
  width: 100%;
  height: 100%;
}
/* 瀑布流顶部标记 */
.scroll-flag.waterfall {
  margin-top: 16rpx;
  margin-bottom: -16rpx;
}
.close_icon-img{
  width: 38rpx;
  height: 38rpx;
  margin-right: 4rpx;
}
/* 瀑布流吸顶 */
.waterfall-tabbar {
  position: fixed;
  left: 0;
  width: 100%;
  z-index: 99;
  transform: translateX(100%);
}
.waterfall-tabbar.fixed {
  transform: translateX(0);
}

/* 开始新人商品 */
.new-user-area {
  background-image: linear-gradient(180deg, #FF4545 1%, #FF7A27 90%);
}
.new-goods {
  margin-top: -16rpx;
}
/* 结束新人商品 */