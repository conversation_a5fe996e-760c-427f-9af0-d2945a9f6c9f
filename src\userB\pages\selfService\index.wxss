/* pages/user/selfService/index.wxss */
Page {
  display: flex;
  flex-direction: column;
  width: 100%;
  background: #f5f5f5;
  color: #222222;
}

.contain {
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  align-items: center;
  padding-top: 2rpx;
  padding-bottom: 110rpx;
  padding-bottom: calc(110rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
  height: 100vh;
}
.version_num-text{
  font-size: 28rpx;
  color: #888888;
}
.self-service {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background-color: #fff;
  padding: 0 34rpx 0 40rpx;
  font-size: 32rpx;
  color: rgb(51, 51, 51);
  border-bottom: 1rpx solid #F5F5F5;
}

.service {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.footer {
  position: absolute;
  bottom: 24rpx;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.tip-2 {
  color: #008C3C;
  font-size: 28rpx;
  margin-bottom: 4rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.f-txt {
  margin: 0 12rpx;
}

.line {
  width: 70rpx;
  height: 1rpx;
  border-bottom: 1rpx solid #008C3C;
}

.dot {
  background: #008C3C;
  width: 6rpx;
  height: 6rpx;
  border-radius: 6rpx;
}

.tip-3 {
  color: rgb(153, 153, 153);
  font-size: 22rpx;
}

.btn-signOut {
  margin-top: 20rpx;
  background-color: #fff;
  border: 1px solid #F3F3F3;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  width: 100%;
  font-size: 32rpx;
  color: var(--themeColor);
  font-weight: bold;
  position: fixed;
  bottom: 0;
  left: 0;
  box-sizing: content-box;
}

.ipx-btn {
  padding-bottom: 64rpx;
  height: 160rpx;
}

.individuation_box {
  margin-top: 16rpx;
  width: 100%;
  flex: 1;
}

.recommend_text-box {
    display: flex;
    align-items: center;
    padding: 28rpx 34rpx 28rpx 40rpx;
    background: #fff;
    border-bottom: 1rpx solid #F5F5F5;
    justify-content: space-between;
}

.bgy_prcard-constitution {
  font-size: 32rpx;
  color: #222222;
  margin-bottom: 4rpx;
}

.open_state-text {
  font-size: 24rpx;
  color: #888888;
  line-height: 33rpx;
}

.self-service-logoff{
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #888888;
}
.logoff-text{
  margin-right: 8rpx;
}
.self-service image{
  width: 20rpx;
  height: 20rpx;
}