
<block wx:if="{{ dataResult.length }}">
  <view class="maybe-like-box {{ isDoubleStyle ? 'double-style' : 'single-style' }}">
    <view wx:if="{{ showRecommendText }}" class="maybe-like-title">为你推荐</view>

    <view class="maybe-like-goods-list">
      <view class="common-goods-box">
        <block wx:if="{{ orderType === ORDER_TYPE.FRESH }}">
          <view class="search-goods-list {{ isDoubleStyle ? 'need-pading':''}}">
            <!-- 双排 -->
            <block wx:if="{{ isDoubleStyle }}">
              <middle-goods-preview
                class="goods-preview-double fresh-goods"
                wx:for="{{dataResult}}"
                wx:key="index"
                goodsData="{{item}}"
                styleType="{{styleConfig.verticalSmall}}"
                goods-expose
                goods-expose-key="bgxxInputSearchGoodsExpose"
                goods-expose-data="{{({maybeLikeFrom: '搜索页'})}}"
                bindaddCart="freshAddCart"
                height-fill="{{true}}"
                goods-special
                bind:searchGoodsReportSensors="searchGoodsReportSensors"
                isSearchGoods="{{true}}"
                index="{{index}}"
                >
              </middle-goods-preview>
            </block>
            <!-- 单排 -->
            <block wx:else>
              <middle-goods-preview
                class="goods-preview-single fresh-goods"
                wx:for="{{dataResult}}"
                wx:key="index"
                goodsData="{{item}}"
                styleType="{{styleConfig.horizontalCard}}"
                goods-expose
                goods-expose-key="bgxxInputSearchGoodsExpose"
                goods-expose-data="{{({maybeLikeFrom: '搜索页'})}}"
                bindaddCart="freshAddCart"
                goods-special
                bind:searchGoodsReportSensors="searchGoodsReportSensors"
                isSearchGoods="{{true}}"
                index="{{index}}"
                >
              </middle-goods-preview>
            </block>
          </view>
        </block>
        <block wx:else>
          <view wx:for="{{dataResult}}" wx:key="goodsSn" class="common-goods">
            <common-goods
              id="goods_{{item.goodsSn}}"
              componentRow="{{commonGoodsRow}}"
              goodsObj="{{item}}"
              isShowCount="{{true}}"
              isShowCountDown="{{true}}"
              isShowSubtitle="{{true}}"
              bindcomplete="handleCountComplete"
              bind:toShowChoiceLayer="onSelectSpecService"
              addSensorskey="searchGoodsAddGoods"
              bindcomplete="handleRefreshLikeGoodsList"
              bind:searchGoodsReportSensors="searchGoodsReportSensors"
              serachIndex="{{index}}"
            ></common-goods>
          </view>
        </block>
      </view>
    </view>

    <view class="refund-tips">
      <image class="inner-image" src="{{ refundTipsIcon }}" />
    </view>
</view>
</block>
