// const homeDelivery = require('./homeDelivery')
// const fightGroups = require('./fightGroups')
// const unitary = require('./unitary')
// const user = require('./user')
// const bgxxShop = require('./bgxxShop')
// const category = require('./category')
// const pageShowGather = require('./pageShowGather')
// const nationalDelivery = require('./nationalDelivery')
// const helpCoupon = require('./helpCoupon')
const common_api = require('../../api/common.api')
const { ENV } = require('../config')
// const propsObj = {
//   ...homeDelivery,
//   ...fightGroups,
//   ...unitary,
//   ...user,
//   ...bgxxShop,
//   ...category,
//   ...pageShowGather,
//   ...nationalDelivery,
//   ...helpCoupon
// }
const { REPORT_JSON } = require('../../source/const/cosConfig')
const { _getApp } = require('../util')
import { parallelPromise } from '../promise'
import { EVENT_NAME_ENUM } from './const/event'
import sensorsBlockConfigMap from './sensorsBlockConfigMap'
const safeGuardSensorByServerless = ['refund', 'pay']

/**
 * @typedef Exposure 一般曝光
 * @property { string } blockName 所属模块
 * @property { string } blockCode 模块编号
 * @property { string } element_name 元素名称
 * @property { string } element_code 完整元素编号
*
* 以下为特定情况属性
* @property { string } element_content 元素状态
* @property { string } searchCategory 仅在搜索结果时分类曝光时上报，上报搜索关键词的系统分类名称
* @property { string } SKU_ID 仅在商详页上报
* @property { string } SKU_Name 仅在商详页上报
 */

/**
 * @typedef Click 点击事件曝光
 * @property { string } blockName 所属模块
 * @property { string } blockCode 模块编号
 * @property { string } element_name 元素名称
 * @property { string } element_code 完整元素编号
 *
 * 以下为特定情况属性
 * @property { string } element_content 元素状态
 * @property { string } searchUniqueID 搜索唯一值
 */

/**
 * @typedef ExposureAdvertisement 广告位曝光
 * @property { string } bannerType 所属模块
 * @property { string } banner_id 模块编号
 * @property { string } banner_name 广告名称
 * @property { string } Position 位置
 * @property { string } element_code 完整元素编号
 */

/**
 * @typedef ClickAdvertisement 广告位点击曝光
 * @property { string } bannerType 所属模块
 * @property { string } banner_id 模块编号
 * @property { string } banner_name 广告名称
 * @property { string } Position 位置
 * @property { string } element_code 完整元素编号
 *
 * 以下为特定情况属性
 * @property { string } url 跳转url，产品说不用上报
 * @property { '及时达普通弹窗' | '及时达普通活动弹窗' | '及时达活动红包弹窗' | '次日达普通弹窗' | '次日达红包弹窗' } subclass 目前只有及时达首页弹窗和次日达弹窗有子类型，取值取弹窗类型
 * @property { string } SKU_ID 仅在点击及时达首页的招牌果品橱窗广告位和次日达首页的全球甄选广告位时，需要用户点击的商品的SkuID
 * @property { string } SKU_Name 仅在点击及时达首页的招牌果品橱窗广告位和次日达首页的全球甄选广告位时，需要用户点击的商品的Sku名称
 */

const getReportData = parallelPromise(() => {
  return new Promise((resolve) => {
    wx.request({
      url: REPORT_JSON,
      method: 'GET',
      success(res) {
        resolve(res.data)
      },
      fail(err) {
        resolve({})
      }
    })
  })
}, {
  cache: true,
})

let reportData
const getPropData = (function () {
  let globalProprietaryData = null
  return async function() {
    if (globalProprietaryData) {
      return globalProprietaryData
    }
    let data
    if (ENV !== 'prod') {
      data = await (reportData || (reportData = require.async('../../componentsSubPackage/commonUtils/sensors/sensorsReport.js')))
    }
    if (!data) {
      data = await getReportData()
    }
    if (!data) {
      return {}
    }
    reportData = globalProprietaryData = data || {}
    return data
  }
})()


/**
 * @param { string } evenName 神策事件名称.
 * @param { string | AnyObject } props 神策对应的事件单一对象.
 * @param { AnyObject | null } [extendObj] 对于一些要额外传的变量对象添加.
 */
async function track(eventName, props, extendObj = null) {
  const app = _getApp()
  if (!app.globalData.reportSensors) { return }
  const trackData = {}
  // 若有数据，则不需要异步，因为 track function 有getCurrentPages方法
  // getCurrentPages若放在异步队列中执行，无法准确获取页面栈
  const propsObj = reportData ? reportData : await getPropData()
  // const propsObj = await getPropData()
  const data = (props && typeof props === 'string' ? propsObj[props] : props) || {}
  // 根据elementCode获取模块信息
  let blockObj = {}
  if (props && props.element_code && !props.blockName) {
    blockObj = sensorsBlockConfigMap[props.element_code] || {}
  }
  extendObj && Object.assign(trackData, extendObj, blockObj)
  data && Object.assign(data, trackData, {Terminal: '百果园+小程序'}) && (await app.sensors.track(eventName, data))


}

/**
 * @param { string | AnyObject } props 神策页面浏览的标识
 * @param { AnyObject } [extendObj] 对于一些要额外传的变量对象添加
 */
async function pageShow (props, extendObj = {}) {
  const app = _getApp()
  if (!app.globalData.reportSensors) { return }

  let data = props
  if (typeof props === 'string') {
    const propsObj = await getPropData()
    data = propsObj[props]
  }

  app.sensors.track('$MPViewScreen', Object.assign(data || {}, extendObj) || {})
}

/**
 * 上报页面浏览事件
 * @param {*} extendObj
 * @returns
 */
function pageScreenView (extendObj) {
  const app = _getApp()
  if (!app.globalData.reportSensors) { return }

  track(EVENT_NAME_ENUM.SCREENVIEW, null, extendObj)
}

/**
 * 上报页面浏览时长事件
 * @param {object} info
 * @param {number} info.duration 单位为ms
 */
function pageViewDuration (info) {
  const app = _getApp()
  if (!app.globalData.reportSensors) { return }
  const { duration } = info
  track(EVENT_NAME_ENUM.VIEW_DURATION, void 0, {
    duration: duration / 1000, // 转为s
  })
}

/**
 * 上报Click事件
 * @param { Click | string } sensorsKey
 */
function clickReport(sensorsKey, extendObj) {
  if (!_getApp().globalData.reportSensors) {
    return Promise.resolve()
  }
  return track('Click', sensorsKey, extendObj)
}

async function safeGuardSensor(type, extraParam = {}){
  let user = wx.getStorageSync('user') || {}
  let userCurrLoca = wx.getStorageSync('userCurrLoca') || {}
  let location = userCurrLoca.location || {}
  const byServerless = safeGuardSensorByServerless.includes(type) && extraParam && extraParam.orderNo
  const sensorOrderType = extraParam && extraParam.orderType
  console.log('safeGuardSensor', byServerless, sensorOrderType, type);
  let safeGuardParam = {
    lon: location.lng || '',
    lat: location.lat  || '',
    locationCity: userCurrLoca.cityName || '',
    cityName: userCurrLoca.cityName || '',
    locationDistrict: userCurrLoca.district || '',
    locationAddress: userCurrLoca.address || ''
  }
  if (extraParam) delete extraParam.ordertype
  Object.assign(
    safeGuardParam,
    extraParam,
    byServerless ? {
      customerID: user.userID || -1,
      type
    } : {
      customerId: user.userID || -1
    }
  )
  console.log('safeGuardParam', safeGuardParam)
  try {
    if (byServerless) {
      const apiMap = {
        'RELAY': 'safeGuardSensorForRelayByServerless',
        'FRUIT': 'safeGuardSensorByServerless'
      }
      const apiKey = apiMap[sensorOrderType] || 'safeGuardSensorByServerless'
      const res = await common_api[apiKey](safeGuardParam);
      console.log(res)
    } else {
      const res = await common_api.safeGuardSensor(type, safeGuardParam);
      console.log(res)
    }
  } catch(err) {
    console.log(err)
  }
}

// 神策上报点击事件
function trackClickEvent(params = {}) {
  const app = _getApp()
  if (app.globalData.reportSensors) {
    app.sensors.track('MPClick', params)
  }
}
/**
 * 此埋点已废弃，商品埋点使用了新的事件 ExposureGoods
 * 商品曝光埋点
 * @param { string } props 神策对应的事件单一对象
 * @param { Record<any, any> } [extendObj] 额外数据
 */
function goodsExpose(props, extendObj = {}) {
  new Promise(resolve => {
    wx.getStorage({
      key: 'userCurrLoca',
      success({ data }) {
        resolve((data || { cityName: '' }).cityName)
      },
      fail() { resolve('') }
    })
  }).then(city_name => {
    track('MPExpose', props, {
      ...(extendObj || {}),
      city_name
    })
  })
}

function goodsDetailShow(goodsDetail, props) {
  if (!_getApp().globalData.reportSensors) {
    return Promise.resolve()
  }
  const { stockNum, saleStatus, goodsSn, goodsName } = goodsDetail || {}
  return track(EVENT_NAME_ENUM.SCREENVIEW, null, Object.assign({}, props, goodsSn ? {
    SKU_ID: goodsSn,
    SKU_Name: goodsName,
    saleStatus: `门店${ saleStatus ? '上' : '下' }架`,
    availableQuantity: stockNum,
  } : {}))
}

/**
 * 上报一般曝光事件
 * @param { Exposure } data
 */
function exposureReport (data, extendObj = {}) {
  if (!_getApp().globalData.reportSensors) {
    return
  }
  track(EVENT_NAME_ENUM.EXPOSURE, data, extendObj)
}

/**
 * 广告位曝光埋点
 * @param { ExposureAdvertisement } data
 */
function adExposure (data) {
  if (!_getApp().globalData.reportSensors) {
    return
  }
  track('ExposureAdvertisement', data)
}

/**
 * 广告位点击埋点
 * @param { ClickAdvertisement } data
 */
function adClick (data) {
  if (!_getApp().globalData.reportSensors) {
    return
  }
  track('ClickAdvertisement', data)
}

module.exports = {
  MPClick: /** @type { const } */('MPClick'),
  /**页面浏览埋点 */
  SCREENVIEW: EVENT_NAME_ENUM.SCREENVIEW,
  track,
  pageShow,
  goodsDetailShow,
  pageScreenView,
  clickReport,
  trackClickEvent,
  safeGuardSensor,
  goodsExpose,
  exposureReport,
  adExposure,
  adClick,
  pageViewDuration
}
