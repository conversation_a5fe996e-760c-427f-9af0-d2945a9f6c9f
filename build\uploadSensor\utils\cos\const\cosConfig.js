/**
 * 注意：该文件属于机密，仅供内部使用，严禁上传到公网GitHub
 * create: 2024/2/21
 */
/**
 * 用于读写快照
 **/
exports.cosAuthorizationOptions = {
  draft_exc: {
    region: "ap-guangzhou",
    bucket: "serverless-cdn-test-1251596386",
    secretId: "AKIDEJ7eutc4F9nAF3bsqZ1DAqFNEo7AwRfj",
    secretKey: "pNfhTyC9qGQJwJPDl23oIWT9YhZ9SUFj",
    globalDomain: 'https://serverless-cdn-test-1251596386.cos.ap-guangzhou.myqcloud.com',
  },
  dev_exc: {
    region: "ap-guangzhou",
    bucket: "serverless-cdn-test-1251596386",
    secretId: "AKIDEJ7eutc4F9nAF3bsqZ1DAqFNEo7AwRfj",
    secretKey: "pNfhTyC9qGQJwJPDl23oIWT9YhZ9SUFj",
    globalDomain: 'https://serverless-cdn-test-1251596386.cos.ap-guangzhou.myqcloud.com',
  },
  // 测试环境换UAT了
  test_exc: {
    region: "ap-guangzhou",
    bucket: "serverless-cdn-uat-1251596386",
    secretId: "AKIDvgBQP7YjcEZeS0QaT5PSFm1ziAKDpBH7",
    secretKey: "bwzlHXXFgqjLgkX4KJ2UhdIhpfEWXHl0",
    globalDomain: 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com',
  },
  drill_exc: {
    region: "ap-guangzhou",
    bucket: "serverless-cdn-uat-1251596386",
    secretId: "AKIDvgBQP7YjcEZeS0QaT5PSFm1ziAKDpBH7",
    secretKey: "bwzlHXXFgqjLgkX4KJ2UhdIhpfEWXHl0",
    globalDomain: 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com',
  },
  uat_exc: {
    region: "ap-guangzhou",
    bucket: "serverless-cdn-uat-1251596386",
    secretId: "AKIDvgBQP7YjcEZeS0QaT5PSFm1ziAKDpBH7",
    secretKey: "bwzlHXXFgqjLgkX4KJ2UhdIhpfEWXHl0",
    globalDomain: 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com',
  },
  exc: {
    region: "ap-guangzhou",
    bucket: "serverless-cdn-prod-1251596386",
    secretId: "AKIDWx82f1OWNyJNAgusZz81e05uTlmW0D9o",
    secretKey: "QZR2MPBfsoTryoNeeOr40e29yWMwGJ6d",
    globalDomain: 'https://eshop-cos.prod.pagoda.com.cn'
  }
};

/**
 * @desc 权限配置
 * @link https://cloud.tencent.com/document/product/436/31923
 */

const COS_SENSOR = 'COS_SENSOR'
// 读取商品快照使用的cos
const COS_GLOBAL = 'COS_GLOBAL'
// 前端上传图片使用的cos
const COS_WXAPP = 'COS_WXAPP'
// 体验家ECM配置COS
const COS_ECM = 'COS_ECM'

exports.allowActions = {
  [COS_WXAPP]: [
    "name/cos:HeadObject",
    //简单上传操作
    "name/cos:PutObject",
    //表单上传对象
    "name/cos:PostObject",
  ],
  [COS_GLOBAL]: [
    "name/cos:HeadObject",
    "name/cos:GetObject",
    // 快照上传对象
    "name/cos:PostObject",
  ],
  [COS_SENSOR]: [
    "name/cos:GetObject"
  ],
  [COS_ECM]: [
    "name/cos:GetObject"
  ],
}

exports.COS_GLOBAL_CDNKEY = 's0nWg6h64Gq6j3'

// 导出枚举
exports.COS_GLOBAL = COS_GLOBAL
exports.COS_WXAPP = COS_WXAPP
exports.COS_SENSOR = COS_SENSOR
exports.COS_ECM = COS_ECM
