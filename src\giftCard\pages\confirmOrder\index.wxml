<wxs module="common" src="/utils/common.wxs" />

<view class="container-wrap">
  <scroll-view
    scroll-y
    class="scroll-view"
    >
    <view class="theme-container">
      <!-- 标题 -->
      <view class="title">{{ title }}</view>

      <!-- 大图展示区 -->
      <view class="cover-preview">
        <image
          class="preview-image"
          src="{{ coverList[selectedCoverIndex].backgroundPicUrl }}"
          mode="aspectFill"
          />
      </view>

      <!-- 卡面列表 -->
      <view class="card-list-wrap">
        <scroll-view
          class="cover-list"
          scroll-x
          scroll-into-view="{{ currentCoverId }}"
          scroll-with-animation
          scroll-into-view-offset="-16"
          bindscroll="scrollCoverList"
          >
          <view
            id="{{ 'cover' + index }}"
            wx:for="{{ coverList }}"
            wx:key="index"
            data-index="{{ index }}"
            bindtap="selectCover"
            class="cover-item {{ selectedCoverIndex === index ? 'active' : '' }}"
            >
            <view>
              <view
                wx:if="{{ selectedCoverIndex === index }}"
                class="cover-item-selected"></view>
              <image
                src="{{ item.backgroundPicUrl }}"
                mode="aspectFill"
                />
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 面额选择 -->
    <view class="section-title">选择面额</view>
      <view class="face-list-wrap">
        <view
            id="{{ 'card' + index }}"
            class="card-item {{ selectedCardIndex === index ? 'active' : '' }} {{ item.stockNum ? '' : 'disabled' }}"
            wx:for="{{ cardList }}"
            wx:key="cardId"
            data-index="{{ index }}"
            bindtap="selectCard">
          <view class="card-amount">{{ common.formatPrice(item.initBalance) }}</view>
          <view wx:if="{{ !item.stockNum }}" class="card-sale-out">已售罄</view>
        </view>
    </view>

    <!-- 购买数量 -->
    <view class="quantity-section">
      <view class="section-title section-title1">购买数量<text wx:if="{{ showRemaining }}">仅剩{{ currentCardStockNum }}份</text></view>
      <view class="quantity-control">
        <!-- 操作区域 -->
        <view class="count">
          <!-- 减购按钮 -->
          <view class="btn-wrap">
            <view
              class="btn-mask--left"
              catchtap="changeQuantity"
              data-type="minus"></view>
            <image
              class="subbigger"
              src="/source/images/cart-substract-btn.png"
            />
          </view>
          <!-- 减购按钮 -->
          <!-- 自定义输入框 -->
          <view class="count-num-box" catchtap="preventClick">
            <input
              class="count-num"
              value="{{ quantity }}"
              type="number"
              maxlength="4"
              disabled="{{ !themeStockNum }}"
              bindblur="setCount"
            />
          </view>
          <!-- 自定义输入框 -->
          <!-- 加购按钮 -->
          <view class="btn-wrap">
            <view
              class="btn-mask--right"
              catchtap="changeQuantity"
              data-type="add"></view>
            <image
              class="addbigger"
              src="/source/images/cart-add-btn.png"
            />
          </view>
          <!-- 加购按钮 -->
        </view>
        <!-- 操作区域 -->
      </view>
    </view>

    <!-- 购买须知 -->
    <view class="notice-section">
      <view
      class="section-title section-title2">购买须知</view>
      <view class="notice-content">
        <view>1、此礼品卡为电子卡(非实体卡面)，电子礼品卡自购买之日起五年有效。</view>
        <view>2、礼品卡可在百果园小程序、百果园App上充值进钱包余额，充值后余额可在百果园全国门店使用，单次最多可充值5张。</view>
        <view>3、同一用户单次购买礼品卡上限为99张，单日购买上限为999张。</view>
        <view>4、转赠功能:购买礼品卡后可转赠给好友且仅可转赠一次，赠送后不可再次转赠;若在赠送后24小时内未被好友领取，则该礼品卡将自动退回至购卡人礼品卡卡列表；礼品卡购买完成后，进入礼品卡订单列表，可分别进行赠送，为防诈骗，购买完成后请妥善保存，勿轻易转发他人。</view>
        <view>5、礼品卡为电子卡券，可为购买人提供预付卡发票。</view>
        <view>6、此礼品卡可多次使用，不可兑换现金，不找零，过期不退，购买后无法退款。</view>
        <view>7、对礼品卡使用有任何疑问可拨打全国服务/团购热线400-181-1212。</view>
        <view>8、更多信息可查看下方《百果园预付卡管理章程》。</view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部支付区域 -->
  <view class="footer">
    <view class="agreement" bindtap="handleAgree">
      <radio-check
        size="24rpx"
        disabled="{{ !themeStockNum }}"
        checked="{{ isAgree }}"></radio-check>
      <text class="agreement-text">我已阅读并知悉</text>
      <text catchtap="checkConstitution" data-type="prepaidCard" class="protocol-style">《百果园预付卡管理章程》</text>
    </view>
    <view
      class="pay-button {{ (themeStockNum && isAgree) ? 'active' : '' }}"
      bindtap="beforeConfirmPay">
      <block wx:if="{{ themeStockNum }}">
        <text>确认支付</text>
        <text class="pay-amount">¥{{ totalPrice }}</text>
      </block>
      <block wx:else>
        <text>已售罄</text>
      </block>
    </view>
  </view>
</view>

<common-loading />
