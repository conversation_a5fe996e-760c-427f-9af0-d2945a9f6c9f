const util = require('../../../utils/util')
const app = getApp()
import { COUPON_DEFAULT, FOLD_ICON, EXCHANGE_CARD } from '../../../source/const/goodsImage'
// eslint-disable-next-line no-unused-vars
const exchangeCardUtils = require('../../../utils/goods/exchangeCard')

Page({
  data: {
    /**可用优惠券列表 */
    availableList: [],
    /**不可用优惠券列表 */
    unavailableList: [],
    /**优惠券缺省图 */
    noCouponImage: COUPON_DEFAULT,
    foldIcon: FOLD_ICON,
    EXCHANGE_CARD,
    /**切换折叠详细信息栏的优惠券 */
    couponInfo: {},
    /**打平的商品数据 */
    flatGoodsList: [],
    /**最多选择多少张 */
    maxUseNum: 10,
  },

  onShow() {
    if (app.checkSignInsStatus()) {
      this.setInitCouponData()
    } else {
      app.showSignInModal()
    }
  },

  /**
   * 设置初始优惠券数据
   */
  setInitCouponData() {
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.on('exchangeInfo', (data) => {
      const exchangeCardInfo = data.exchangeCardInfo
      /**可用优惠券列表 */
      const availableList = exchangeCardInfo.availableList || []
      /**不可用优惠券列表 */
      const unavailableList = exchangeCardInfo.unavailableList || []
      /**传入的选择优惠券数组 */
      const selectedExchangeCardList = exchangeCardInfo.selectedExchangeCardList || []
      /**打平的商品数据 */
      const flatGoodsList = exchangeCardInfo.flatGoodsList || []
      /**最多选择多少张 */
      const exchangeCardMaxUseNum = exchangeCardInfo.exchangeCardMaxUseNum
      /**记录外层可能传入的新人特价商品uuid */
      this.currentNewActUuid = exchangeCardInfo.currentNewActUuid

      //  选择了优惠券的情况
      if (selectedExchangeCardList.length) {
        availableList.forEach(coupon => {
          coupon.isSelected = selectedExchangeCardList.some(_coupon => _coupon.couponCode === coupon.couponCode)
        })
      } else {
        availableList.forEach(coupon => coupon.isSelected = false)
      }

      const permuteInfo = [
        ...util.permute(['A', 'I', 'W']),
        ...util.permute(['A', 'I']),
        ...util.permute(['A', 'W']),
        'A'
      ]

      this.setData({
        availableList,
        unavailableList: util.filterChannelSeparation(unavailableList, 'channelSeparation', permuteInfo),
        flatGoodsList,
        maxUseNum: exchangeCardMaxUseNum || 10
      })

      if (selectedExchangeCardList.length) {
        //  执行算法给未选择的优惠券进行禁用处理
        const selectCouponList = availableList.filter(coupon => coupon.isSelected).map(coupon => coupon.couponCode)
        const {
          updateCouponHandle,
        } = this.getUpdateCouponHandle(selectCouponList)
        updateCouponHandle()
      }
    })
  },

  /**
   * 切换查看优惠券详细说明
   */
  toggleMore(e) {
    const { couponCode } = e.currentTarget.dataset.item || {}
    const { couponInfo } = this.data
    couponInfo[couponCode] = !couponInfo[couponCode]
    this.setData({
      couponInfo
    })
  },

  /**
   * 确定使用按钮 返回确认订单页
   */
  confirm() {
    const { availableList } = this.data
    const selectedExchangeCardList = availableList.filter((item) => item.isSelected)
    // 删除选择的属性再交出去
    selectedExchangeCardList.forEach(item => delete item.isSelected)
    this.backConfirmOrder(selectedExchangeCardList)
  },

  /**
   * 不使用优惠券
   */
  noUseCoupon() {
    this.backConfirmOrder([])
  },

  /**
   * 传参到调用者 并返回上一页
   * @param {Array<any>} selectedExchangeCardList 选择的兑换卡
   */
  backConfirmOrder(selectedExchangeCardList) {
    const eventChannel = this.getOpenerEventChannel()
    // 通过emit的方式进行触发 将子页面/目标页面中的数据传递给当前页面
    eventChannel.emit('confirmSelectExchangeCard', {
      data: {
        selectedExchangeCardList,
        couponBindGoodsMap: this.couponBindGoodsMap || new Map(),
      }
    })
    wx.navigateBack()
  },

  /**
   * 取消选中，设置选中
   */
  async selectChange(e) {
    const { couponCode, isSelected } = e.detail.value || {}
    const {
      availableList,
      maxUseNum,
    } = this.data

    /**已选中优惠券数量 */
    const selectedExchangeCardCount = availableList.filter((item) => item.isSelected).length

    if (!isSelected && (selectedExchangeCardCount && selectedExchangeCardCount + 1 > maxUseNum)) {
      wx.showToast({
        title: `最多选择${maxUseNum}张兑换卡`,
        icon: 'none',
        duration: 2000
      })
      return
    }

    /**当前点击优惠券下标 */
    const currentIndex = availableList.findIndex((item) => String(item.couponCode) === String(couponCode))
    /**此次选择后的值 */
    const afterValue = !isSelected

    const selectCouponList = (function() {
      const coupons = availableList.filter(coupon => coupon.isSelected).map(coupon => coupon.couponCode)

      //  如勾上，推入该优惠券编码
      if (afterValue) {
        coupons.push(couponCode)
      }
      //  如取消，从数组中移除该优惠券编码
      else {
        const index = coupons.findIndex(item => item === couponCode)
        coupons.splice(index, 1)
      }
      return coupons
    })()

    const {
      couponBindGoodsMap,
      updateCouponHandle,
    } = this.getUpdateCouponHandle(selectCouponList)

    const updateFn = () => {
      this.couponBindGoodsMap = couponBindGoodsMap

      if (currentIndex !== -1) {
        this.setData({
          [`availableList[${currentIndex}].isSelected`]: afterValue
        })
      }
      updateCouponHandle()
    }

    //  当前优惠券生效的商品是新人特价商品
    if (afterValue && couponBindGoodsMap.get(couponCode)?.uuid === this.currentNewActUuid) {
      const res = await app.showModalPromise({
        content: `兑换的商品已享受了新人特价，确定使用兑换卡吗`,
        showCancel: true,
        confirmText: '确定',
        cancelText: '取消',
      })

      res && updateFn()
    } else {
      updateFn()
    }
  },

  /**
   * 根据传入选择优惠券列表，计算出优惠券和商品的对应关系，并返回更新优惠券的选择状态的回调函数
   */
  getUpdateCouponHandle(selectCouponList) {
    const {
      flatGoodsList,
      availableList,
    } = this.data

    const { discountList } = exchangeCardUtils.getCouponMatchGoodsMap({
      flatGoodsList,
      couponList: availableList
    })

    /**
     * 不禁用的的优惠券Set
     * 进入该Set的优惠券，有两个情况
     * 1. 折扣更高，已经和商品绑定的优惠券
     * 2. 优惠券所属的商品没有被其他优惠券绑定
     */
    const unDisabledCouponSet = new Set()
    /**
     * 商品绑定优惠券map，记录该商品使用的优惠券
     */
    const goodsBindCouponMap = new Map()
    /**
     * 优惠券绑定商品map，记录该券对应的商品
     */
    const couponBindGoodsMap = new Map()

    const left = []
    const right = []
    discountList.forEach((current) => {
      if (selectCouponList.includes(current.coupon.couponCode)) {
        left.push(current)
      } else {
        right.push(current)
      }
    })

    /**@type { discountList } */
    const _discountList = [...left, ...right]
    for (let i = 0; i < _discountList.length; i++) {
      const discount = _discountList[i]
      const { goods, coupon, discountAmount } = discount

      if (!discountAmount) {
        continue
      }

      //  商品已绑定优惠券
      if (goodsBindCouponMap.has(goods)) {
        continue
      }
      //  优惠券已无需禁用
      if (unDisabledCouponSet.has(coupon.couponCode)) {
        continue;
      }

      //  如优惠券在选择状态 && 优惠券未绑定商品 && 商品未绑定优惠券
      if (
        selectCouponList.includes(coupon.couponCode) &&
        !unDisabledCouponSet.has(coupon.couponCode) &&
        !goodsBindCouponMap.has(goods)
      ) {
        unDisabledCouponSet.add(coupon.couponCode)
        goodsBindCouponMap.set(goods, coupon.couponCode)
        couponBindGoodsMap.set(coupon.couponCode, goods)

        continue
      }

      //  经过上述的筛选，还能走到这个位置的优惠券。说明优惠券的商品未被使用，且该优惠券也没有使用。允许勾选
      unDisabledCouponSet.add(coupon.couponCode);
    }

    return {
      couponBindGoodsMap,
      /**
       * 更新选中状态
       */
      updateCouponHandle: () => {
        /**无需禁用的的优惠券数组 */
        const unDisabledArr = [...unDisabledCouponSet]
        const availableList = this.data.availableList
        //  循环优惠券列表，更新优惠券禁用状态
        availableList.forEach((coupon, couponIndex) => {
          //  在unDisabledArr的券，取消禁用
          const inUnDisabled = unDisabledArr.includes(coupon.couponCode)
          const isDisabled = inUnDisabled ? false : true

          if (isDisabled) {
            this.setData({
              [`availableList[${couponIndex}].isSelected`]: false
            })
          }

          this.setData({
            [`availableList[${couponIndex}].isDisabled`]: isDisabled
          })
        })
      }
    }
  }
})
