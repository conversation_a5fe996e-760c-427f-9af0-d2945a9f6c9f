const locateService = require('../utils/services/locate')
// const { LOCATE_STORAGE_ENUM } = locateService
import locateStore from '../stores/module/locate'
// import { getUserIsVisitor, clientLoginParallel } from '../service/userService'
import { actionType, appLaunchCacheProtocolParallel, checkIsShowProtocolParallel, checkPrivacyPopupParallel, pagodaProtocolStore } from '../components/base/userProtocol/protocol'

const app = getApp()
const showUserProtocol = 'showUserProtocol'
const skeletonScreen = 'skeletonScreen'
const contentScreen = 'content'

function isFruitPage (ctx) {
  const {currentRoute, mixVegLocate} = ctx
  if (!mixVegLocate) return true
  return [
    'pages/homeDelivery/index',
    'pages/category/index',
    'pages/topic/index',
    'h5/pages/commonh5/index'
  ].includes(currentRoute)
}

function getCurPages (ctx){
  const pages = getCurrentPages()
  const {route} = pages[pages.length - 1]
  ctx.currentRoute = route
}

module.exports = {
  data: {
    currentView: skeletonScreen,
    mainViews: { // 页面缺省页可能展示不同，可自由配置
      noLocationAuth: 'noLocationAuth',
      noStore: 'noStore',
      noCity: 'noStore',
      content: 'content'
    },
    isNeedB2CBusiness: true, // 页面是否需要b2c业务，默认需要
    isGetReceivingAddress: true // 定位地址是否取收货地址
  },
  mixFruitLocate: true, // 标记及时达混入
  currentRoute: '', // 当前页面路径
  showRunLocateReady: true, // 要展示隐私协议弹窗时，需要先执行onLocateReady用于展示，点击完弹窗再onLocateReady一次；如果之前展示过弹窗，那么就不用了
  calledOnShow: false, // 是否执行过onshow
  showAuthPopup: false, // 是否展示授权弹窗
  onlyShowData: false, // 仅展示数据，用默认城市展示数据，不走二级页面跳转等其他逻辑
  fruitProtocolActionFn: {},
  onLoad(){
    this.fruitProtocolActionFn = {
      [actionType.default]: () => {
        console.log('fruitProtocolActionFn default');
        this.loadLocate({
          defaultCity: true
        })
      },
      [actionType.checkAuth]: () => {
        console.log('fruitProtocolActionFn checkAuth');
        // 减少开发量，直接沿用之前的方法
        this.fruitCloseUserProtocol({agree: true})
      },
      [actionType.checkPagodaProtocol]: () => {
        console.log('fruitProtocolActionFn checkPagodaProtocol');
        this.fruitCheckPagodaProtocol()
      },
      [actionType.noAction]: () => {
        console.log('fruitProtocolActionFn noAction');
        // 此时已经定位过，直接取缓存的timelyCity
        this.loadLocate()
      }
    }
  },
  async onShow () {
    console.log('onShow')
    // 处理有些页面比如订单列表跳转到首页/购物车等会触发两次onshow，导致页面调用异常
    // 定位授权弹窗打开之后，点击定位授权弹窗里的协议，导致触发onShow，导致页面异常
    console.log('calledOnShow', this.calledOnShow)
    console.log('showAuthPopup', this.showAuthPopup)
    console.log('currentView', this.data.currentView)
    getCurPages(this)
    if (this.calledOnShow || this.showAuthPopup || this.data.currentView && typeof this.data.currentView === 'string' && this.data.currentView.includes(showUserProtocol)) {
      return
    }
    this.calledOnShow = true
    await appLaunchCacheProtocolParallel()
    getApp && getApp().startReportPoint()
    const type = await checkPrivacyPopupParallel()
    this.fruitProtocolActionFn[type]()
  },
  async fruitCheckPagodaProtocol(){
    const { show, protocol } = await checkIsShowProtocolParallel()
    console.log('fruitCheckPagodaProtocol show', show);
    if (show) {
      isFruitPage(this) && pagodaProtocolStore.toggleShow(true)
      protocol.then((res) => this.fruitCloseUserProtocol(res))
      return
    }
    // 不展示百果园协议弹窗时，直接默认同意
    this.showRunLocateReady = false
    this.fruitCloseUserProtocol({ agree: true })
  },
  /**
   * 关闭隐私弹窗，同意不同意都会走到这个逻辑
   */
  async fruitCloseUserProtocol(e) {
    if (this.data.currentView !== contentScreen) {
      isFruitPage(this) && this.setData({
        currentView: skeletonScreen
      })
    }
    // 是否同意百果园隐私协议，只决定是否退登
    if (!e.agree) {
      app.signOut()
    }
		// 同意隐私弹窗，第一次定位仅作为展示
    // this.onlyShowData = e.agree ? true: false
    // 第一次同意同意隐私协议 才静默登录
    // if (e.agree) {
    //   await clientLoginParallel()
    // }
    // await this.loadLocate({
    //   requestAuth: false
    // })

    this.showRunLocateReady = true

		// 不同意的情况，直接使用默认地址定位
    //  双倍退协议同意时，不走后续位置授权逻辑
		// if (e.protocolType === 'firstProtocol' && !e.agree ) {
    //   this.showAuthPopup = false
		// 	return
    // }
    // if (!e.agree) {
    //   this.showAuthPopup = false
    //   return
    // }
    // 同意的情况

    if (!locateService.hasShowLocateAuthModal()) {
      this.locateWithoutCacheAddress()
      return
    }
    // 点击过，授权信息发生变化(可能又去设置页关闭/开启了)
    // 如果是拒绝授权，lastLocateAuth为false
    if (!app.globalData.lastLocateAuth) {
      // 判断一下是否在设置页改变权限了且之前是使用默认定位，此时需要根据实际定位
      // 如果之前不是使用默认定位，那就还是使用缓存定位
      const hasAuth = await locateStore.checkHasAuth()
      if(hasAuth === true) { locateStore.changeAuth({ auth: true }) }
      if (hasAuth === true && locateStore.useDefault) {
        this.locateWithoutCacheAddress()
        return
      }
    }
    // 点击过，授权信息没有发生变化
    // 第一次不走缓存，其他用缓存
    app.globalData.fruitFirstLocate ? this.locateWithoutCacheAddress() : this.locateWithCacheAddress()
  },
  /**
   * 及时达水果定位逻辑：定位失败，使用缓存地址，无缓存，展示定位失败缺省
   */
  async loadLocate (params = {}) {
    const param = {
      isGetReceivingAddress: this.data.isGetReceivingAddress,
      afterCheckShareCity: this.afterCheckShareCity,
      requestAuth: params.requestAuth,
      defaultCity: params.defaultCity || false
    }
    try {
      this.showAuthPopup = true
      await locateService.requestLocation(param)
    } catch (e) {
      const result = await this.handleLocateFail({ param, isShowFailModal: params.isShowFailModal, isLocateFailGetDefault: params.isLocateFailGetDefault }, e)
      // 定位失败取默认城市信息，不展示缺省页
      const { isGetDefault } = result || {}
      if(isGetDefault) {
        // 取默认定位城市
        await this.loadLocate({
          defaultCity: true
        })
        locateStore.changeUseDefault({useDefault: true})
        return
      }
    }
    if (app.globalData.fruitFirstLocate) {
      app.globalData.fruitFirstLocate = false
      // 首次定位完成，检查红点信息
      // app.checkRedDot()
    }
    this.handleLocate()
    return
  },
  handleLocate () {
    const { mainViews, isNeedB2CBusiness } = this.data
    const { viewKey } = locateService.getViewType({
      isNeedB2CBusiness
    })
    
    isFruitPage(this) && this.setData({
      currentView: mainViews[viewKey] || 'content'
    })
    this.fruitLocateFinish()
  },
  fruitLocateFinish() {
    this.showAuthPopup = false
    if ("function" === typeof this.onLocateReady && this.showRunLocateReady && isFruitPage(this)) this.onLocateReady()
  },
  // 点击开通权限按钮触发
  async onTapOpenSetting () {
    const auth = await locateStore.tryOpenAuth()
    // 用户开启位置权限，调定位方法
    auth && (app.globalData.userLocationAuth = auth)
    return auth === true
  },
  /**
   * 处理当前定位地址失败
   */
  async handleLocateFail(data, failRes) {
    console.log('handleLocateFail');
    // 授权失败
    if (failRes.err && failRes.err.code === 1) {
      console.log('handleLocateFail, 授权失败');
      if (data.isShowFailModal) {
        // 弹窗二次确认
        locateStore.changeAuth({
          auth: app.globalData.userLocationAuth = false
        })
      }
      if (data.isLocateFailGetDefault) {
        console.log('handleLocateFail isGetDefault true');
        return { isGetDefault: true }
      }
      locateStore.changeAuth({
        auth: app.globalData.userLocationAuth = false
      })
    }
  },
  locateWithCacheAddress () {
    app.globalData.fruitFirstLocate = false
    this.loadLocate()
  },
  locateWithoutCacheAddress () {
    app.globalData.fruitFirstLocate = true
    this.loadLocate({
      requestAuth: true,
      isShowFailModal: true,
      isLocateFailGetDefault: true
    })
  },
  onHide() {
    this.calledOnShow = false
  }
}
