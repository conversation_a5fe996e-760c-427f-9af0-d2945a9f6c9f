const coordtransform = require('../coordUtil.js')
var commonObj = require('../../source/js/common').commonObj;
const { checkTimelyGoodsDetailScene } = require('../../pages/homeDelivery/utils/forwardNavigate');
const { router } = require('../router');
import locateStore from '../../stores/module/locate'
const showShareDialogAuto = dialog => dialog(() => {})
import { parallelPromise } from '../promise';
import wxappMap from '../../service/wxappMap'
import { wxPrivacyProtocolStore } from '../../components/base/userProtocol/protocol';
import { YN2Boolean } from '../YNBoolean'
import { deepClone } from '../util'
const sensors = require('../report/sensors')
const _getApp = (function(){ let app; return function() {
  return app || (app = getApp())
} })()

// 公用的一些定位相关的缓存key值枚举
const LOCATE_STORAGE_ENUM = {
  GET_LOCATE_TIP_SHOWED: 'GET_LOCATE_TIP_SHOWED', // 【获取位置信息提示的弹窗】已经展示过
  HAS_SHOW_LOCATE_AUTH_MODAL: 'isShowLocateAuthModal', // 是否展示过【授权弹窗】
}

export const locationAuthType = {
  /**
   * 用户在小程序中未授权
   */
  miniprogramNoAuth: 'miniprogramNoAuth',
  /**
   * 未给微信位置授权
   */
  weixinNoLocateAuth: 'weixinNoLocateAuth',
  /**
   * 没开启系统定位
   */
  noGPSAuth: 'noGPSAuth',
  /**
   * 网络异常
   */
  networkError: 'networkError',
  /**
   * 定位超时
   */
  locateTimeout: 'locateTimeout'
}
export const locationAuthErrMsgMap = {
  'getLocation:fail auth deny': locationAuthType.miniprogramNoAuth,
  'getLocation:fail:auth denied': locationAuthType.miniprogramNoAuth,
  'getLocation:fail authorize no response': locationAuthType.miniprogramNoAuth,
  'getLocation:fail system permission denied': locationAuthType.weixinNoLocateAuth,
  'getLocation:fail:system permission denied': locationAuthType.weixinNoLocateAuth,
  'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF': locationAuthType.noGPSAuth,
  'getLocation:fail:ERROR_NETWORK': locationAuthType.networkError,
  'getLocation:fail:timeout': locationAuthType.locateTimeout
}

function wxLocationErrModal(errMsg) {
  const defaultModalOption = {
    content: '需先开启定位权限方可定位当前地址',
    showCancel: true,
    cancelText: '取消',
    confirmText: '去开启',
  }
  const otherModalOption = {
    title: '定位失败',
    content: `请检查：1、手机系统设置中“微信”的定位权限是否开启；2、手机的GPS（定位服务）是否打开`,
    showCancel: false,
    confirmText: '知道了',
  }
  const modalMap = {
    [locationAuthType.miniprogramNoAuth]: defaultModalOption,
    [locationAuthType.weixinNoLocateAuth]: otherModalOption,
    [locationAuthType.noGPSAuth]: otherModalOption,
  }
  const authType = locationAuthErrMsgMap[errMsg] || locationAuthType.miniprogramNoAuth
  const modalOptions = modalMap[authType] || defaultModalOption
  return {
    authType,
    modalOptions
  }
}
async function handleWXLocationErr(errMsg){
  console.log('handleWXLocationErr', errMsg, wxPrivacyProtocolStore.hasAgreed);
  if (!wxPrivacyProtocolStore.hasAgreed) {
    // 未同意时，不提示
    return
  }
  const {
    authType, modalOptions
  } = wxLocationErrModal(errMsg)
  console.log('authType, modalOptions', authType, modalOptions);
  const res = await _getApp().showModalPromise(modalOptions)
  if (authType === locationAuthType.miniprogramNoAuth && res) {
    return locateStore.tryOpenAuth()
  }
}

let getGeoLocationPromise = null
// 并行请求默认城市信息处理
const getDefaultCityParallel = parallelPromise(getDefaultCity, { cache: true })

// 并行获取定位信息处理
const getGeoLocationParallel = parallelPromise(getGeoLocation)
/**
 * @description 获取当前的地理位置
 */
function getGeoLocation () {
  if (getGeoLocationPromise) { return getGeoLocationPromise }
  getGeoLocationPromise = new Promise(function (reslove, reject) {
    wx.getLocation({
      type: 'gcj02',
      success: function (res) {
        locateStore.changeAuth({
          auth: _getApp().globalData.userLocationAuth = true
        })
        reslove(res)
        _getApp().globalData.curLocation = {
          lat: res.latitude,
          lon: res.longitude,
        }
      },
      fail: function (res) {
        _getApp().globalData.userLocationAuth = false
        // locateStore.changeAuth({
        //   auth: _getApp().globalData.userLocationAuth = false
        // })
        console.log('getLocation fail',res)
        reject({ code: 1, errMsg: res.errMsg })
      },
      complete: function(res) {
        console.log('getGeoLocation complete', res);
        const isShowLocateAuthModal = wx.getStorageSync(LOCATE_STORAGE_ENUM.HAS_SHOW_LOCATE_AUTH_MODAL)
        !isShowLocateAuthModal && setTimeout(() => {
            wx.setStorageSync(LOCATE_STORAGE_ENUM.HAS_SHOW_LOCATE_AUTH_MODAL, true)
        }, 400)
      }
    })
  })
  getGeoLocationPromise.finally(() => {
    getGeoLocationPromise = null
  })
  return getGeoLocationPromise
}
/**
 * @description 获取城市名
 * @param {*} res
 */
async function getCityName (lat, lon) {
  try {
    const res = await wxappMap.reverseGeocoder({
      lat,
      lon,
      get_poi: 1,
      poi_options: 'address_format=short;radius=100;policy=2',
    })
    const {
      ad_info: addressInfo,
      location,
      formatted_addresses:formattedAddress = {},
      _locateAddress,
      address
    } = res.result || {}

    let locationInfo = {
      cityName: addressInfo.city,
      district: addressInfo.district,
      location: location,
      address: _locateAddress || formattedAddress.recommend || address
    }
    const { cityID = '' } = wx.getStorageSync('userCurrLoca') || {}
    if (cityID) {
      Object.assign(locationInfo, { cityID })
    }
    wx.setStorageSync('userCurrLoca', locationInfo)
    return locationInfo
  } catch(err) {
    console.error(err)
    return { code: 2}
  }
}

/**
 * @description 获取默认城市信息
 */
async function getDefaultCity() {
  const params = '-1'
  const { data: { storeList, city: { cityID, cityName, lat, lon } } } = await _getApp().api.checkBgxxIsSupportVip({
    cityName: params,
    lon: params,
    lat: params,
    enableDefaultStore: '1'
  }, { cityCheckOnly: true })
  const store = storeList[0]
  const locationInfo = { isDefaultLocation: true, district: '', ...(store ? {
    cityName: store.cityName,
    location: { lon: store.lon, lat: store.lat },
    address: store.address
  } : {
    cityName: cityName,
    location: { lon, lat },
    address: ''
  })}
  wx.setStorage({ key: 'defaultCity', data: locationInfo })
  return locationInfo
}

/**
 * 转换经纬度
 */
function transformLatAndLon (lon, lat) {
  if (!lon || !lat) {
    return {}
  }
  let latLon = coordtransform.gcj02tobd09(lon, lat)
  return {
    lon: latLon[0],
    lat: latLon[1]
  }
}

async function getLocationByLocate(params = {}) {
  try {
    const { locateScene, requestAuth, defaultCity = false, addressDistanceAllow } = params
    let cityInfo = {}
    // requestAuth: 从首页隐私弹窗来的直接走getGeoLocation
    // 如果是次日达请求定位 并且 权限未知
    // 无定位权限的情况下，直接走默认门店的逻辑
    const locateAuth = await locateStore.checkHasAuth()
    if ('fruit' === locateScene && (defaultCity || locateAuth === false || (!requestAuth && locateAuth === locateStore.UNKNOW))) {
      console.log('getLocationByLocate if');
      // 取默认城市
      cityInfo = await getDefaultCityParallel()
      // 使用默认门店的情况下，尽早设置useDefault
      locateStore.changeUseDefault({ useDefault: true })
      params.isSetCurrCityId = false
    } else if('vegetables' === locateScene && (requestAuth === false)) {
      // 不弹位置授权弹窗，次日达才会取默认城市
      console.log('getLocationByLocate else if 次日达取默认城市')
      cityInfo = await getDefaultCityParallel()
    } else {
      console.log('getLocationByLocate else');
      // 获取地理位置
      let locationInfo = await getGeoLocationParallel()
      // 获取城市名
      cityInfo = await getCityName(locationInfo.latitude, locationInfo.longitude)
    }
    params.addressDistanceAllow = addressDistanceAllow
    await checkCityService(Object.assign(cityInfo, params))
  } catch (err) {
    console.log(err)
    return Promise.reject({err})
  }
  return
}

/**
 * 组装及时达/次日达检查城市服务参数，分发业务请求
 * @param {*} params
 */
async function checkCityService(params) {
  const app = _getApp()
  const { isDefaultLocation, locateScene = 'fruit', location = {}, cityName, address, isGetReceivingAddress = true, isSetCurrCityId = false, afterCheckShareCity, addressDistanceAllow } = params
  const lnglat = ['lon', 'lat'].some(key => !(key in location))
    ? transformLatAndLon(location.lng, location.lat)
    : location
  if (!cityName) {
    return Promise.reject()
  }
  const checkParams = {
    cityName,
    lat: lnglat.lat || -1,
    lon: lnglat.lon || -1,
    address
  }
  if (addressDistanceAllow) {
    checkParams.addressDistanceAllow = addressDistanceAllow
  }
  if (locateScene === 'fruit') {
    if (app.checkSignInsStatus() && isGetReceivingAddress) {
      const { userID } = wx.getStorageSync('user') || {}
      checkParams.customerID = userID
    }
    await checkFruitCityService(checkParams, { isDefaultLocation, isGetReceivingAddress, isSetCurrCityId, afterCheckShareCity })
  } else {
    checkParams.isDefaultLocation = isDefaultLocation
    await checkBgxxCityService(checkParams)
  }
}

/**
 * @description 及时达检查城市服务
 * @param { Object } params
 * @param { Object } [options = {}]
 * @param { boolean } [options.isGetReceiveAddress] 是否绑定取收货地址
 * @param { boolean } [options.isSetCurrCityId] 是否设置当前定位地址的城市id 只有检查城市服务是当前定位地址的情况下，才去设置
 * @param { boolean } [options.isGetOptionsStore] 是否取传入的门店（分享、智慧零售）
 * @param { boolean } [options.showDiffCityShareDialog] 是否显示异地分享弹窗(手动切换地址时时不显示)
 * @param { Function } [params.afterCheckShareCity] 检查完分享城市之后的逻辑(参数: locate.js内部的弹窗提醒函数)
 * @param { boolean } [params.isDefaultLocation] 是否使用默认城市（默认false）
 */
async function checkFruitCityService(params, {
  isGetReceivingAddress = true,
  isSetCurrCityId = false,
  isGetOptionsStore = true,
  showDiffCityShareDialog = true,
  afterCheckShareCity,
  isDefaultLocation,
} = {}) {
  const app = _getApp()
  const optionStore = app.globalData.optionStore || {}
  const handleShareStore = isGetOptionsStore && optionStore.storeID && optionStore.takeawayAttr !== 'B2C'
  const needShareStore = handleShareStore || optionStore.shareStoreCode
  try {
    const res = await app.api.checkCity(Object.assign(params, needShareStore ? [{ dataKey: 'shareStoreId', shareKey: 'storeID' }, { dataKey: 'shareStoreCode', shareKey: 'shareStoreCode' }].reduce(function(data, item) {
      optionStore[item.shareKey] && (data[item.dataKey] = optionStore[item.shareKey])
      return data
    }, {}) : {}))
    const resData = res.data || {}
    let {
      city = {},
      address,
    } = resData
    const {
      storeList = [],
      locateCity,
    } = resData
    address = address || {}
    city = city || {}
    const { cityCanService: hasCityService } = updateCityServerStatus(resData)
    if (isSetCurrCityId) {
      city.id && setCurrLocaCityID(city.id)
      // slice截取，避免影响原数组顺序
      setUserCurrLocaNearSelfTakeStore({
        city,
        storeList: hasCityService ? storeList.slice() : []
      })
    }
    // 无城市服务或者无门店
    if (!hasCityService || storeList.length < 1) {
      const timelyCity = getTimelyCity({
        city,
        address: params,
        locateCity,
      })
      app.changeTimelyCityInfo(timelyCity, { isFreshPageGoods: false })
      wx.removeStorageSync('selectedAddress')
      return timelyCity
    }
    const { store: storeInfo, cityInfo } = storeInfo2City(storeList[0], city)
    if (needShareStore) {
      const notSameStore = [{ shareKey: 'shareStoreId', infoKey: 'storeID' }, { shareKey: 'shareStoreCode', infoKey: 'storeCode' }].every(({ shareKey, infoKey }) => String(storeInfo[infoKey]) !== optionStore[shareKey])
      // 及时达商品有分享门店逻辑
      if (notSameStore && optionStore.source !== 'sqsck' && showDiffCityShareDialog) { // 素材库分享进入不显示超范围弹窗
        const { path, query } = app.globalData.launchOptions
        // 分享者和被分享者是同一个城市
        Number(cityInfo.cityID) === Number(optionStore.cityID) ||
        // 跳转到及时达分类并且带了homeDeliveryObj,说明是商品详情页卡片分享
        (`/${path}` === router.category && query.homeDeliveryObj) ||
        // 跳转到首页并且scence符合调整到及时达商品详情页
        checkTimelyGoodsDetailScene(app.globalData.launchOptions) ||
        // 都不满足上述条件在分享人门店覆盖不到被分享人所在门店时才跳转到首页
        wx.switchTab({
          url: '/pages/homeDelivery/index',
        })
        // 使用默认门店的时候,不展示弹窗
        isDefaultLocation || ((afterCheckShareCity || showShareDialogAuto)((done) => setTimeout(() => {
          commonObj.showModal('提示', `当前定位超出<${optionStore.storeName || '分享门店'}>配送范围，将自动切换至您最近门店(商品价格可能跟随门店变动)`, false, '知道了', '', done)
        }, 1000)))
      }
      // 若用户已登录，只定位一次该分享门店
      if(app.checkSignInsStatus()) {
        app.globalData.optionStore = {}
      }
    }
    let addressInfo = null
    // 及时达优先选择分享门店 其次取第一个门店（第一个门店接口返回逻辑： 1.拉新门店 2.上次下单门店 3.最近门店）
    if (isGetReceivingAddress && address.addressId) {
      // 收货地址绑定门店
      address.isDefault = 'Y'
      wx.setStorageSync('selectedAddress', address)
      addressInfo = address || params || {}
    } else {
      wx.removeStorageSync('selectedAddress')
      addressInfo = params || {}
    }
    cityInfo.cityID !== city.cityID && updateCityServerStatus(cityInfo)
    const timelyCity = getTimelyCity({
      city: cityInfo,
      address: addressInfo,
      store: storeInfo,
      locateCity,
    })
    app.changeTimelyCityInfo(timelyCity, { isFreshPageGoods: false, useDefaultAddress: Boolean(isDefaultLocation) })
    // 将定位后的操作放入下一次task队列，保证 app.changeTimelyCityInfo 方法里的 读取cos先执行
    // await new Promise(resolve => setTimeout(resolve, 0))
  } catch (error) {
    console.log('checkFruitCityService', error)
  }
}

/**
 * 次日达检查城市服务
 */
async function checkBgxxCityService(params) {
  const app = _getApp()
  try {
    const res = await app.api.checkBgxxIsSupportVip(params)
    const { city, storeList = [], supportSuperVipShop = 'N' } = res.data || {}
    if (city.cityID) {
      setCurrLocaCityID(city.cityID)
    }
    const list = (storeList || []).filter( item => item.isSupportVip  === 'Y' || item.isSupportVipDelivery === 'Y')
    const { cityID, cityName, lat, lon, cityCode, deliveryCenterCode } = city || {}
    // 已选地址信息
    const selectAddressInfo = {
      cityID: cityID || '',
      cityCode,
      deliveryCenterCode,
      cityName: (cityName || params.cityName) || '',
      lat: (params.lat || lat) || -1,
      lon: (params.lon || lon) || -1,
      address: params.address || '',
      supportSuperVipShop,
      isDefaultLocation: params.isDefaultLocation || false
    }
    // 已选门店信息
    const selectStoreInfo = {}
    if (list.length > 0) {
      const { storeID, address, storeName, shortName, isSupportVip, isSupportVipDelivery, distance, openingTime, cityID, lat, lon, storeNumber, openTimeOnDay: startTime = '', closeTimeOnDay: endTime = '', storeCode, isStarStore, phone } = list[0]
      Object.assign(selectStoreInfo, {
        storeID,
        storeName: shortName || storeName,
        address,
        isSupportVip,
        isSupportVipDelivery,
        distance: distance > 1000 ? `${(distance / 1000).toFixed(2)}km` : `${distance}m`,
        openingTime,
        cityID,
        lat,
        lon,
        storeCode: storeNumber || storeCode,
        startTime,
        endTime,
        isStarStore,
        phone
      })
    }
    // 设置当前定位所在的城市门店信息
    wx.setStorageSync('bgxxCurrLocateInfo', {
      selectAddressInfo,
      selectStoreInfo
    })
  } catch (error) {}
}

/**
 * 设置当前定位城市的城市id
 */
function setCurrLocaCityID(cityID) {
  const userCurrLoca = wx.getStorageSync('userCurrLoca') || {}
  if (!!userCurrLoca && !!userCurrLoca.cityID) {
    return
  }
  wx.setStorageSync('userCurrLoca', Object.assign(userCurrLoca, { cityID }))
}

/**
 * 设置用户当前定位地址附近最近的自提门店
 */
function setUserCurrLocaNearSelfTakeStore({
  storeList,
  city
}) {
  const clearCacheStore = () => {
    wx.removeStorageSync('currLocaNearSelfTakeStore');
  }
  if (!Array.isArray(storeList) || !storeList.length) {
    clearCacheStore()
    return { store: {}, city }
  }
  // 按照距离由小到大排序
  storeList.sort((a, b) => a.distance - b.distance)
  const selfTakeStore = storeList.find(item => (item.isSupportTake || '').toUpperCase() === "Y")
  if (!selfTakeStore) {
    clearCacheStore()
    return { store: {}, city }
  }
  const {
    lat,
    lon,
    shortName,
    openingTime,
    address,
    storeCode,
    name: storeName,
  } = selfTakeStore || {}

  const storeID = selfTakeStore.id || selfTakeStore.storeID
  const {
    cityCode, id, deliveryCenterCode, name
  } = city || {}

  wx.setStorageSync('currLocaNearSelfTakeStore', {
    openingTime,
    cityID: id,
    lat,
    lon,
    address,
    storeID,
    storeCode,
    shortName,
    storeName,
    cityCode,
    deliveryCenterCode,
    cityName: name
  })
  return { store: selfTakeStore, city }
}

/**
 * 及时达缺省页类型
 */
function getViewType (params = {}) {
  const { isNeedB2CBusiness = false } = params
  const {
    cityCanService,
    hasStoreService,
    userLocationAuth,
  } = _getApp().globalData
  const { cityID, storeID, supportBToCService } = wx.getStorageSync('timelyCity') || {}
  // 有门店/或者支持b2c服务
  if ((cityID && storeID) || (supportBToCService && isNeedB2CBusiness)) {
    return { viewKey: 'content'}
  }
  // 未开启定位权限
  if (!userLocationAuth) {
    return { viewKey: 'noLocationAuth' }
  }
  // 无城市服务
  if (!cityCanService) {
    return { viewKey: 'noCity' }
  }
  // 无门店服务同时没有B2C服务
  if (!hasStoreService && ((!supportBToCService && isNeedB2CBusiness) || !isNeedB2CBusiness)) {
    return { viewKey: 'noStore' }
  }
  return { viewKey: 'content' }
}

/**
 * 次日达缺省页面类型
 * @param isUpdateLocate 是否更新数据
 */
function getBgxxViewType (isUpdateLocate) {

  const app = _getApp()

  const {
    userLocationAuth
  } = app.globalData
  const { storeID = '', cityID = '', deliveryCenterCode } = app.globalData.bgxxCityInfo || {}
  if ((storeID || app.globalData.vegetablesNoStoreTip) && cityID && deliveryCenterCode && !isUpdateLocate) {
    return { viewKey: 'content' }
  }

  const bgxxCurrLocateInfo = wx.getStorageSync("bgxxCurrLocateInfo") || {}
  const {
    selectStoreInfo: { storeID: currLocateStoreID = '' } = {},
    selectAddressInfo: { cityID: currLocateCityID = '', supportSuperVipShop = 'N', deliveryCenterCode: curDeliveryCode } = {}} = bgxxCurrLocateInfo
  if (supportSuperVipShop === 'Y' && currLocateCityID && curDeliveryCode) {
    app.changeBgxxCityInfo(bgxxCurrLocateInfo)
    wx.removeStorageSync('bgxxCurrLocateInfo')
    if (!!currLocateStoreID) {
      return { viewKey: 'content' }
    }
  }

  // 未开启定位权限
  // v4.3.4 未授权情况下取默认城市
  if (!userLocationAuth) {
    return { viewKey: 'content' }
    // return { viewKey: 'noLocationAuth' }
  }

  // 无城市服务
  if (supportSuperVipShop !== 'Y' || !currLocateCityID) {
    app.changeBgxxCityInfo(bgxxCurrLocateInfo)
    return { viewKey: 'noCity' }
  }

  // 无门店
  if (!currLocateStoreID) {
    return { viewKey: 'noStore' }
  }

  return { viewKey: 'content' }
}
/**
 * @description 有地址则获取地址，无地址则通过定位获取位置相关信息
 * @param {Object} params
 * @param { string } [params.locateScene='fruit'] 定位场景，默认fruit水果及时达
 * @param { string } [params.isGetReceivingAddress=true] 是否取收货地址
 * @param { Function } [params.afterCheckShareCity] 检查完分享城市之后的逻辑(参数: locate.js内部的弹窗提醒函数)
 * @param { boolean } [params.requestAuth] 同意协议之后，忽略无权限，直接发起定位申请
 * @param { boolean } [params.defaultCity] 直接取默认城市
 */
async function requestLocation({ requestAuth, locateScene = "fruit", isGetReceivingAddress = true, afterCheckShareCity, defaultCity = false } = {}) {
  const app = _getApp()
  // 如果有位置信息，则直接返回
  if (locateScene === 'fruit') { // 及时达
    // 首次定位，不取缓存定位，重新定位
    if (!app.globalData.fruitFirstLocate) {
      const timelyCity = wx.getStorageSync('timelyCity')
      // 没有timelyCity.locateCity,说明是旧版本没设置过地理城市
      // 重新走一遍定位流程
      if (timelyCity && timelyCity.locateCity) {
        return Promise.resolve()
      }
    }
  }
  if (locateScene === 'vegetables' && app.globalData.isGetBgxxCacheLocation) { // 次日达
    console.log('isHandlebgxxCacheStore', app.globalData.isHandlebgxxCacheStore)
    if (app.globalData.isHandlebgxxCacheStore) {  // 首次启动定位，当前所在定位地址城市信息存在，则不需要定位
      const bgxxSelectLocateInfo = wx.getStorageSync("bgxxSelectLocateInfo") || {}
      if(bgxxSelectLocateInfo.selectStoreInfo) {
        return Promise.resolve()
      }
    } else { // 非冷启动，当前城市门店信息存在，则不需要定位
      const { storeID = '', cityID = '' } = app.globalData.bgxxCityInfo || {}
      if (!!storeID && !!cityID) {
        return Promise.resolve()
      }
    }
  }
  // 解决跳转商品详情页前,在及时达首页/分类页提前弹出问题
  const callAfterCheckShareCity = (function() {
    let hasCall = false
    return function (dialog) {
      hasCall || ((afterCheckShareCity || showShareDialogAuto)(dialog))
      hasCall = true
    }
  })()
  const params = {
    isGetReceivingAddress,
    locateScene,
    isSetCurrCityId: true,
    afterCheckShareCity: callAfterCheckShareCity,
    requestAuth,
    defaultCity,
    addressDistanceAllow: 3000
  }
  const userCurrLoca = wx.getStorageSync('userCurrLoca') || {}

  const result = await (userCurrLoca.cityName
    // 用户当前位置信息存在，说明已经定位过，只需要检查城市服务（次日达及时达页面切换过程中，有一个重新定位过当前位置，则不需要再定位）
    ? checkCityService(Object.assign(userCurrLoca, params))
    : getLocationByLocate(params))
  callAfterCheckShareCity(showShareDialogAuto)
  return result
}

// 是否点击过【授权弹窗】，因为展示时不点击，还是未授权状态
function hasShowLocateAuthModal () {
  return wx.getStorageSync(LOCATE_STORAGE_ENUM.HAS_SHOW_LOCATE_AUTH_MODAL)
}

/**
 * @desc 取出门店的运营城市和地理城市做比较,如果不传cityInfo,则直接直接取门店的运营城市
 */
function storeInfo2City(store, cityInfo) {
  if (!(store && cityInfo)) { return { store: store || {}, cityInfo: cityInfo || {}, } }
  const { cityInfo: operateCity } = store
  const { cityID } = operateCity
  return deepClone({
    store: Object.assign({}, store, {
      cityInfo: void 0, // 移除门店中的cityInfo信息
      // 目前只用到了门店所在的城市id
      cityID,
      // 其他城市信息待定,
    }),
    cityInfo: operateCity && operateCity.cityID === cityInfo.cityID ? cityInfo : operateCity,
  })
}

let cacheLocateInfo = {
  cityName: '',
  cityID: '',
}
/**
 * @desc 根据城市信息生成storage中的timelyCity信息
 * @param { Object } data
 * @param { any } data.city
 * @param { any } [data.store]
 * @param { any } [data.address]
 * @param { any } [data.locateCity]
 * @param { Object } [options]
 * @param { boolean } options.keepLocate 是否保持地理城市信息
 */
function getTimelyCity(data, options = {}) {
  const { city, store, locateCity, address = {} } = data
  const {
    // 主要用于选择附近门店列表中的一家门店
    // 因为此时只是运营城市可能会变
    // 地理位置已经在之前的cityCity中缓存过了
    keepLocate = false,
  } = options
  const storeInfo = store ? {
    storeID: store.storeID,
    storeCode: store.storeCode,
    storeName: store.shortName,
    storeInfo: store,
  } : {}
  const hasLocateCity = locateCity && locateCity.cityID
  const locateCityInfo = hasLocateCity && locateCity.cityID !== city.cityID ? locateCity : city
  // cacheLocateInfo.cityID的检查,是给首次checkCity时,没有设置cacheLocateInfo时使用的
  cacheLocateInfo = (keepLocate || !hasLocateCity) && cacheLocateInfo.cityID  ? cacheLocateInfo : {
    cityName: locateCityInfo.name,
    cityID: locateCityInfo.cityID,
  }
  return deepClone(Object.assign({
    lat: [
      address.lat,
      city.lat,
    ].find(v => v),
    lon: [
      address.lon,
      city.lon,
    ].find(v => v),
    address: [
      address.gisAddress,
      address.address,
      city.gisAddress,
      city.address,
    ].find(v => v) || '',
  }, {
    cityName: city.name,
    cityID: city.cityID,
    supportBToCService: YN2Boolean(city.supportBToCService),
    cityCode: city.cityCode || '',
    deliveryCenterCode: city.deliveryCenterCode || '',
    deliveryCenterId: city.deliveryCenterId || -1,
    supportSuperVipShop: city.supportSuperVipShop,
    locateCity: cacheLocateInfo,
  }, storeInfo))
}

/**
 * @desc 获取定位城市信息
 */
function getLocateCity(resData = {}) {
  const { locateCity, city } = resData
  const cityData = (locateCity && locateCity.cityID ? locateCity : city) || {}
  return deepClone(Object.assign(cityData, cityData.cityID ? {
    supportBToCService: YN2Boolean(cityData.supportBToCService),
  } : {}))
}

/**
 * @desc 更新城市服务状态
 * @param { Object } city
 * @param { import('../YNBoolean').YN } city.hasStoreService
 * @param { import('../YNBoolean').YN } city.cityCanService
 */
function updateCityServerStatus(city) {
  const { cityCanService, hasStoreService } = city
  const globalData = _getApp().globalData
  const booleanValue = {
    /** 是否有城市服务 */
    cityCanService: YN2Boolean(cityCanService),
    /** 是否门店 */
    hasStoreService: YN2Boolean(hasStoreService),
  }
  globalData.cityCanService = booleanValue.cityCanService // 是否有城市服务
  globalData.hasStoreService = booleanValue.hasStoreService // 是否门店
  return booleanValue
}

/**
 * @desc 三无退之前检查城市服务
 */
async function beforeRefundCheckLocate() { 
  const app = _getApp()
  // 开启位置权限
  if(locateStore.userLocationAuth) {
    const { lat, lon } = app.globalData.curLocation 
    console.log(lat, lon)
    return {
      lat,
      lon,
      isNext: true,
    }
  }
  // 未开启位置权限
  sensors.exposureReport({
    element_code: '1162701001',
    element_name: '定位弹窗',
    blockName: '三无退',
    blockCode: '1162701',
  })
  const res = await app.showModalPromise({
    content: '为了给您提供优质的服务，申请获取您的定位信息仅用于获取您附近的百果园门店',
    showCancel: true,
    cancelText: '继续退款',
    confirmText: '开启定位',
  })
  if(res) {
    console.log(locateStore.hasLocationAuth)
    sensors.clickReport({
      element_code: '1162701002',
      element_name: '开启定位',
      blockName: '三无退',
      blockCode: '1162701',
    })
    try {
      const locationInfo = await getGeoLocationParallel()
      console.log(locationInfo)
      return {
        lat: locationInfo.latitude,
        lon: locationInfo.longitude,
        isNext: true,
      }
    } catch(err) {
      console.log(err)
      await handleWXLocationErr(err.errMsg)
      return {
        isNext: false,
      }
    }
    // locationInfo.latitude, locationInfo.longitude
  } else {
    sensors.clickReport({
      element_code: '1162701003',
      element_name: '拒绝定位',
      blockName: '三无退',
      blockCode: '1162701',
    })
  }
  
  return {
    isNext: true,
  }
}

module.exports = {
  updateCityServerStatus, // 更新城市服务状态
  getLocateCity, // 获取定位城市信息
  getTimelyCity, // 根据城市信息生成storage中的timelyCity信息
  storeInfo2City, //取出门店的运营城市和地理城市做比较和,如果不传cityInfo,则直接直接取门店的运城市是
  getGeoLocation, // 获取经纬度地理位置
  getCityName, // 获取城市名
  requestLocation, // 获取地址
  checkCityService, // 检查城市服务
  transformLatAndLon,
  getViewType,
  getBgxxViewType,
  checkFruitCityService,
  setUserCurrLocaNearSelfTakeStore,
  getLocationByLocate,
  hasShowLocateAuthModal,
  LOCATE_STORAGE_ENUM,
  getDefaultCity,
  wxLocationErrModal,
  handleWXLocationErr,
  beforeRefundCheckLocate,
}
