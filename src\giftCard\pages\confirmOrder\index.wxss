.container-wrap {
  width: 100%;
  background-color: #f5f5f5;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.scroll-view {
  width: 100%;
  flex: 1;
  overflow-y: hidden;
  background-color: #fff;
}

.theme-container {
  background-color: #f5f5f5;
  padding-bottom: 24rpx;
}

.title {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  padding: 24rpx 0 14rpx 32rpx;
}

/* 大图展示区 */
.cover-preview {
  width: 100%;
  height: 374rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  padding: 0 32rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

/* 卡面列表 */
.cover-list {
  white-space: nowrap;
}

.cover-item {
  width: 202rpx;
  border: 3rpx solid transparent;
  border-radius: 16rpx;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.cover-item > view {
  display: flex;
  flex-direction: column;
}

.cover-title {
  line-height: 54rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: bolder;
  background-color: #ffffff;
}

.cover-item-selected {
  background-image: url(https://resource.pagoda.com.cn/dsxcx/images/21a7acd1473b190cb9fbe431566d6d21.png);
  width: 52rpx;
  height: 29rpx;
  position: absolute;
  right: 0px;
  top: 0px;
  z-index: 1;
  background-size: 100% 100%;
  border-radius: 0 0rpx 0 15rpx;
  background-color: #00a34f;
}

.cover-item:last-child {
  margin-right: 32rpx;
}

.cover-item:not(:last-child) {
  margin-right: 18rpx;
}

.cover-item:first-child {
    margin-left:32rpx;
}

.cover-item.active {
  border-color: #07C160;
}

.cover-item image {
  width: 202rpx;
  height: 110rpx;
}

/* 面额选择 */
.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #222;
  padding: 36rpx 0 24rpx 32rpx;
  font-weight: bold;
}

.section-title1 {
    padding-bottom: 0;
    font-weight: 400;
    font-size: 28rpx;
}

.section-title2 {
    padding-bottom: 24rpx;
    font-weight: 400;
    font-size: 28rpx;
}

.section-title text {
  font-size: 28rpx;
  color: #FF0C0C;
  font-weight: 400;
  margin-left: 8rpx;
}

.card-list {
  white-space: nowrap;
}

.card-list-wrap {
  white-space: nowrap;
}

.face-list-wrap {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15rpx;
  padding: 0 32rpx;
}

.card-item {
  display: inline-flex;
  min-width: 160rpx;
  height: 113rpx;
  border-radius: 12rpx;
  border: 3rpx solid #F0F0F0;
  background-color: #FFFFFF;
  color: #595959;
  text-align: center;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 5rpx;
}

.card-item.active {
  background-color: #edfff4;
  color: #00A34F;
  border: 3rpx solid #00A34F;
  border-radius: 16rpx;
}

.card-item.disabled {
  border: 3rpx solid #F0F0F0;
  border-radius: 16rpx;
  color: #BFBFBF;
}

.card-amount {
  font-size: 42rpx;
  font-weight: bold;
}

.card-amount::before {
  content: "￥";
  font-size: 28rpx;
  font-weight: 400;
}

.card-sale-out {
  background-color: #F0F0F0;
  font-size: 24rpx;
  color: #8C8C8C;
  position: absolute;
  width: 100%;
  bottom: 0;
  line-height: 43rpx;
}

.card-price {
  font-size: 24rpx;
  color: #666;
}

/* 购买数量 */
.quantity-section {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding-right: 32rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.count {
  display: flex;
}

.count .btn-wrap {
  display: flex;
  align-items: center;
  position: relative;
}

.count .btn-mask--right {
  position: absolute;
  left: 0;
  width: 70rpx;
  height: 90rpx;
  z-index: 1;
}

.count .btn-mask--left {
  position: absolute;
  right: 0;
  width: 70rpx;
  height: 70rpx;
  z-index: 1;
}

.count-num-box {
  width: 72rpx;
  background: #F5F5F5;
  border-radius: 4rpx;
}

.count-num-box .count-num {
  width: 100%;
  font-size: 24rpx;
  font-weight: bold;
  text-align: center;

  height: 36rpx !important;
  min-height: 36rpx !important;
  line-height: 36rpx !important;
}

.subbigger,
.addbigger {
  width: 52rpx;
  height: 36rpx;
}

.subbigger {
  border-right: 0;
}

.addbigger {
  border-left: 0;
}

/* 购买须知 */
.notice-section {
  padding-bottom: 120rpx;
}

.notice-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
  padding: 0 32rpx;
}

.notice-content view {
  margin-bottom: 8rpx;
}

/* 底部支付区域 */
.footer {
  background-color: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.agreement {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
  margin-left: 8rpx;
}

.agreement-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 12rpx;
}

.pay-button {
  height: 88rpx;
  border-radius: 44rpx;
  background: #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
}

.pay-button.active {
  background: #00A644;
}

.pay-amount {
  margin-left: 12rpx;
}