// homeDelivery/pages/signIn/phoneNumber/index.js
const commonObj = require('../../../../source/js/common').commonObj
const app = getApp()
import sensors from '../../../../utils/report/sensors'
import { protocolPopup } from '../components/protocolPopup/service'
import { loginType } from '../service/const'
const util = require('../../../../utils/util')
const routeObj = {
  homeDelivery: '/pages/homeDelivery/index'
}
const loginMixin = require('../../../../mixins/loginMixin')
const config = require('../../../../utils/config')
const h5Domain= config.baseUrl.H5_WEB_DOMAIN
const sms = require.async('../../../../sourceSubPackage/commonUtils/sms/index')
Page({
  mixins: [loginMixin],
  /**
   * 页面的初始数据
   */
  data: {
    loginType: loginType.verify,
    phoneNumberIsCorrect: false,
    sendCodeText: '获取验证码',
    errorInfo: '请输入正确的短信验证码',
    submitDisabled: true,
    sendCodeDisabled: false,
    showErrorInfo: false,
    phoneNumber: '',
    code: '',
    isRefreshLocation: true,
    pageName: 'phoneSignIn',  // 混入方法中使用
    isAgree: false, // 协议是否选中
    isFocus: false, // 手机号输入框是否聚焦
    isCodeFocus: false // 验证码输入框是否聚焦
  },
  _data: {
    finishToRouter: '',
    timer: -1,
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const { isRefreshLocation = true, finishToRouter = '' } = options || {}
    this.setData({ isRefreshLocation: String(isRefreshLocation) !== 'false' })
    this._data.finishToRouter = finishToRouter
  },
  onShow() {
    sensors.pageScreenView()
  },


  // 输入手机号
  inputPhoneNumber (e) {
    const number = e.detail.value
    const phoneNumberIsCorrect = (/^1[0-9]\d{9}$/).test(number)
    this.setData({
      phoneNumber: number,
      phoneNumberIsCorrect,
    })
  },
  // 输入验证码
  inputCode (e) {
    this.setData({ code: e.detail.value })
    this.checkCodeInput()
  },
  // 发送验证码
  async sendCode (e) {
    const that = this
    if (that.data.sendCodeDisabled) return
    // 验证手机号
    if (that.checkPhoneNumber()) {
      if (!this.data.isAgree) {
        // wx.showToast({
        //   title: '请阅读并同意相关协议',
        //   icon: 'none'
        // })
        // return
        protocolPopup.show()
        const agree = await protocolPopup.waitProtocolPopup()
        if (!agree) return
        // 自动勾选
        this.radioTapHandle()
      }
      // 用户手机号输入正确
      const { sceneType, sendSms, CaptchaHandle } = await sms
      CaptchaHandle.selectComp(this)
      const sendRes = await sendSms({ scene: sceneType.login, phoneNumber: that.data.phoneNumber })
      // 只有发送验证码成功才会返回true
      if (sendRes) {
        that.setData({ 'sendCodeDisabled': true })
        let countDown = 60
        that.setData({ 'sendCodeText': `${countDown}` })
        that._data.timer = setInterval(() => {
          if (countDown > 0) {
            --countDown
            that.setData({ 'sendCodeText': `${countDown}` })
          } else {
            that.setData({
              'sendCodeText': '获取验证码',
              'sendCodeDisabled': false
            })
            that.clearTimer()
          }
        }, 1000)
      }
      // 上报神策点击获取验证码
      that.trackClickEvent({
        blockCode: '00',
        element_name: '获取验证码',
        element_code: '100400002'
      })
    }
  },
  // 验证手机号
  checkPhoneNumber () {
    const phoneNumber = this.data.phoneNumber
    if ((/^1[0-9]\d{9}$/).test(phoneNumber)) {
      this.setData({
        showErrorInfo: false,
        errorInfo: ''
      })
      return true
    } else {
      this.setData({
        showErrorInfo: true,
        errorInfo: '请输入正确的手机号码'
      })
      return false
    }
  },
  // 验证验证码
  checkCode () {
    const code = this.data.code
    if ((/^\d{4}$/).test(code)) {
      this.setData({
        showErrorInfo: false,
        errorInfo: ''
      })
      return true
    } else {
      this.setData({
        showErrorInfo: true,
        errorInfo: '请输入正确的短信验证码'
      })
      return false
    }
  },
  // 输入过程中，验证验证码是否是四位数字
  checkCodeInput () {
    const code = this.data.code
    if ((/^\d{4}$/).test(code)) {
      this.setData({
        showErrorInfo: false,
        errorInfo: ''
      })
      this.checkAll()
    } else {
      this.setData({ submitDisabled: true })
    }
  },
  // 手机号输入框失去焦点时，验证输入
  checkPhoneNumberBlur () {
    this.setData({
      isFocus: false
    })
    if (this.checkPhoneNumber() && this.data.code) {
      this.checkAll()
    }
  },
  // 手机号码输入框和验证码输入框失去焦点时，验证手机号和验证码，并以此改变提交按钮状态以及错误提示状态
  checkAll () {
    this.setData({
      isCodeFocus: false
    })
    if (this.checkPhoneNumber() && this.checkCode()) {
      this.setData({ submitDisabled: false })
      return true
    } else {
      this.setData({ submitDisabled: true })
      return false
    }
  },
  // 错误处理
  handleError(errorMsg, code) {
    let content = '系统繁忙，请稍后重试'
    if (errorMsg) {
      content = errorMsg
    }
    // if (typeof code !== 'undefined') {
    //   content += '，code:' + code
    // }
    commonObj.showModal('提示', content, false, '我知道了', '', function (res) {
      if (res.confirm) {
        // wx.navigateBack({delta: 2})
      }
    })
  },
  clearPhoneNumber() {
    this.setData({
      phoneNumber: '',
      submitDisabled: true
    })
  },
  signIn: util.throttle(async function(){
    const that = this
    if (that.data.submitDisabled) return
    if (!that.checkAll()) return
    // if (!this.data.isAgree) {
    //   wx.showToast({
    //     title: '请阅读并同意相关协议',
    //     icon: 'none'
    //   })
    //   return
    // }
    if (!this.data.isAgree) {
      protocolPopup.show()
      const agree = await protocolPopup.waitProtocolPopup()
      if (!agree) return
      // 自动勾选
      this.radioTapHandle()
    }

    //  用户进行手机号登录，在登录后设置标记。用于获取协议版本跳过协议确认弹窗
    wx.setStorageSync('loginSuccessSetProtocol', true)

    this.toLogin({
      loginChannel: 2,
      validateCode: that.data.code,
    })
    this.trackClickEvent({
      blockCode: '00',
      element_name: '注册/登录',
      element_code: '100400003'
    })
  },1500),
  /**
   * @description - 登录失败-弹框显示
   * @param {object} options - 弹窗数据对象
   * @property {string} options.content - 弹窗内容
   */
  signInFail (modelMap) {
    wx.showModal({
      title: '温馨提醒', //弹框标题
      content: modelMap.content, //提示内容
      confirmText: '呼叫',
      cancelText: '取消',
      cancelColor: '#999999',
      confirmColor: '#008C3C',
      success: function (res) {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001811212'
          })
        }
      }
    })
  },
  // 神策上报点击事件
  trackClickEvent( params = {} ) {
    sensors.clickReport(params)
  },
  clearTimer() {
    clearInterval(this._data.timer)
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: async function () {
    const { CaptchaHandle } = await sms
    CaptchaHandle.clear()
    this.clearTimer()
  },
  /**
   * 勾选、取消勾选协议
   */
  radioTapHandle(){
    this.setData({
      isAgree: !this.data.isAgree
    })
    if (this.data.isAgree) {
      this.trackClickEvent({
        blockCode: '00',
        element_name: '勾选协议',
        element_code: '100400004'
      })
    }
  },
  /**
   * 处理手机号输入框聚焦
   */
  handleFocus() {
    this.setData({
      isFocus: !this.data.isFocus
    })
  },
  /**
   * 处理验证码输入框聚焦
   */
  handleCodeFocus() {
    this.setData({
      isCodeFocus: !this.data.isCodeFocus
    })
  }
})
