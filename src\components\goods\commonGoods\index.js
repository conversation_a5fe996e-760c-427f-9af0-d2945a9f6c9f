// components/itemGoods/index.js
/**
 * recommmendGoods组件限定了两列布局，属性很多都是写死的，
 * 并且多处使用了这个组件，直接修改原组件风险较大，
 * 所以新建一个组件，通过传值控制，可展示位两列或多列
 */
import { storeBindingsBehavior } from 'mobx-miniprogram-bindings'
import fruitCartStore from '../../../stores/module/fruitCart'
import { getDefaultGoods } from '../../../utils/goods/getDefaultGoods'
import { filterGoodsObjField } from '../../../utils/goods/goodsData';
import { showFruitCutSpecDesc } from '~/service/fruitGoodsUtil';
import { handleGoodsLabel } from '~/utils/goods/label';
import goodsTagStore, { getSPUGoodsTagParaller, goodsTagHandler } from '../../../stores/module/goodsTag'
import { reportGoods } from './sensor';
import { EVENT_NAME_ENUM } from '~/utils/report/const/event';
import fruitFans from '../store/fruitFans';
const commonObj = require('../../../source/js/common').commonObj;
const picUrl = commonObj.PAGODA_PIC_DOMAIN;
const txSensor = require('../../../utils/report/txSensor')
const setCount = require('../mixins/setCount')
const { getCustomerInfo, checkIsShowActivityPrice } = require('../../../source/js/requestData/activityPrice')
const { MULTI_ENUM, isCombinedMultiGoods } = require('../../../utils/goods/multiSpec')

Component({
  behaviors: [setCount, storeBindingsBehavior],
  properties: {
    // 传入商品信息
    goodsObj: {
      type: Object,
      value: {
        goodsId: '',
        goodsName: '',
        goodsType: '',
        price: '',
        memberPrice: '',  //原价
        heartPrice: '',  //心享会员价
        headPic: '',
        specDesc: '',
        subtitle: '',
        stockNum: '',
        skuID:'',
        goodsLabelList: [],
      },
    },
    hasSlotContent: {
      type: Boolean,
      value: false
    },
    // 综合专题页埋点需要活动id，活动名称
    activityObj: {
      type: Object,
      value: {}
    },
    // 加入购物车神策埋点key值
    addSensorskey: {
      type: String,
      value: ''
    },
    // 减去购物车神策埋点key值
    subSensorskey: {
      type: String,
      value: ''
    },
    // 商品是否显示阴影样式
    isShowShadow: {
      type: Boolean,
      value: false
    },
    // 显示倒计时
    isShowCountDown: {
      type: Boolean,
      value: false
    },
    // 是否展示加购动画
    isShowAnimation: {
      type: Boolean,
      value: false
    },
    isShowCount: {
      type: Boolean,
      value: false
    },
    isShowSubtitle: {
      type: Boolean,
      value: false
    },
    // 需要过滤的标签
    needFilterLabelTypes: {
      type: Array,
      value: ["storeRecommend"]
    },
    // 是否展示限时特价标签
    isShowStoreActivity: {
      type: Boolean,
      value: false
    },
    // 是否是从朋友圈分享进入
    fromShareTimeLine: {
      type: Boolean,
      value: false
    },
    // 是否展示购物车
    isShowGoodCart:{
      type: Boolean,
      value: true
    },
    // 最多显示多少个标签
    showLabelNum: {
      type: String,
      value: 2
    },
    /**
     * @desc 一行展示x排
     * 目前有：【单排 single】，【双排 double】，【三排 treble】
     */
    componentRow: {
      type: String,
      value: '',
      observer (val) {
        const showLabelNum = (() => {
          return val === 'treble' ? 1 : val === 'double' ? 2 : 3
        })()
        this.setData({
          curRow: val,
          cartSize: val === 'treble' ? 'small' : 'default', // 购物车尺寸
          labelSize: val === 'treble' ? 'small' : 'default', // 标签大小
          showLabelNum,
        })
      }
    },
    /**
     * @desc 自定义样式
     * 该组件默认写的是【综合专题页的单排、双排、三排的样式】
     * 如有不同的样式，传入命名空间，在wxss写上对应的样式
     * 目前传入的命名空间有【category：品类页】、【search：搜索页】、【nationalDelivery：全国送商品页】、【b2c：综合专题页一行三排的商品为全国送商品时】、【home：首页】、【topicTimelyGoods：综合专题页及时达商品】
     */
    customClassName: {
      type: String,
      value: '',
    },
    // 块宽度是否按父元素适应
    // 解除componentRow中写死的宽度限制
    widthFill: {
      type: Boolean,
      value: false
    },
    // 块高度是否按父元素适应
    // 解除componentRow中写死的高度限制
    heightFill: {
      type: Boolean,
      value: false
    },
    // 是否排序多拼规格
    sortMultiSpec: {
      type: Boolean,
      value: false
    },
    // 见 setCartType 解释
    multiSpecSingleSkuShowLayer: {
      type: Boolean,
      value: false
    },
    // 品类页点击选规格增加模块id信息上报
    currCategoryInfo:{
      type: Object,
      value: {}
    },
    // 是否搜索商品
    isSearchGoods:{
      type: Boolean,
      value: false
    },
    serachIndex:{
      type: Number,
      value: 0
    },
    serachKeyWord:{
      type: String,
      value: ''
    },
    lazyLoad: {
      type: Boolean,
      value: false
    },
    /**
     * 售罄类型
     * common: 普通售罄
     * search: 搜索页售罄
     */
    sellOutType: {
      type: String,
      value: 'common'
    },
    /**
     * 用于控制售罄业务的配置对象，属性自定
     */
    sellOutConfig: {
      type: Object,
      value: {}
    },
    reportExtendData: {
      type: Object,
      value: {}
    },
    showRankBar: {
      type: Boolean,
      value: false,
    },
  },
  storeBindings: [{
    store: {  fruit: fruitCartStore },
    fields: {
      /**
       * @returns { { goodsSn: { count: number } } }
       */
      goodsCountObj: ({ fruit }) => fruit.goodsCountObj,
    },
  }, fruitFans.storeBindings],
  observers: {
    goodsCountObj (newValue) {
      const { goodsCount, goodsObj } = this.data
      const newCount = this.handleGoodsCount({ goodsObj, goodsCountObj: newValue })
      if (newCount === goodsCount) return
      this.setData({
        goodsCount: newCount
      })
    },
    goodsCount (newValue) {
      this.setData({
        goodsCountStr: newValue > 99 ? '99+' : newValue
      })
    },
    hideMultiSpecDesc (newValue) {
      if (!newValue) return
    },
    goodsObj: async function () {
      // 数据监听器靠前的会先执行
      this.init()
    },
    ...fruitFans.observers('init')
  },
  data: {
    showMinPriceTip: false, // 是否展示【xx元起】
    renderGoods: {}, // 用于页面渲染的商品对象，且是默认规格
    picUrl: picUrl,
    countDown: {
      // 单排属性
      single: {
        countDownSize: 'small',
        countDownContent: '门店特价',
      },
      // 双排属性
      double: {
        countDownSize: 'normal',
        countDownContent: '门店特价',
      },
    },
    goodsCountClassName: 'single-count',
    goodsCount: 0,
    goodsCountStr: '',
    cartAndClickCoord: null,
    curRow: '', // 一行展示多少排商品
    cartSize: 'default', // 购物车尺寸
    labelSize: 'default', // 标签大小
    showGoodsLayer:false, //是否一品多规
    // 瀑布流、品类页、搜索结果页：聚合的多拼商品不拼接规格
    // 因为上述模块都是默认聚合的，所以只要判断：【是多拼且多规格】
    showSpecDesc: true,
    isShow: false,
    labelList: [],
    goodsTagText: '',
  },
  _data: {
    exposeObserver: {},
  },
  lifetimes: {
    created () {
      fruitFans.initPureData(this)
    },
    detached () {
      this._data.promise = null
      if (this._data.exposeObserver) {
        this._data.exposeObserver.disconnect()
      }
      if (this._data.exposeTimer) {
        clearTimeout(this._data.exposeTimer)
      }
    }
  },
  ready () {
    this.sensorsExpose()
    if (this.data.lazyLoad) {
      const observer = this.createIntersectionObserver()
      observer.relativeToViewport({ right: 0, bottom: 0 }) // 屏幕右侧/屏幕底部显示出来
      .observe('.common-goods', ({ intersectionRatio }) => {
        if (!this.data.isShow) {
          this.setData({
            isShow: true
          })
        }
      })

    }
  },

  methods: {
    init () {
      // 刷新用户信息
      this.refreshCustomerInfo()
      this.setDefaultGoods()
      // 根据商品规格和服务决定购物车按钮是直接加入还是选规格
      this.setCartType()
      // 检查商品是否有特价
      this.checkActivityPriceStatus()
      this.setSpecDescDisplay()
      this.setLabelListAndTag()
    },
    async setLabelListAndTag() {
      await getSPUGoodsTagParaller()
      const { renderGoods, showLabelNum, needFilterLabelTypes } = this.data
      const { spuNumber, associationList } = renderGoods

      const text = goodsTagHandler.setGoodsTagTextContent(spuNumber, associationList || [], goodsTagStore.goodsSnTagMap)
      this.setData({
        labelList: handleGoodsLabel({
          goodsObj: renderGoods,
          showLabelNum,
          needFilterLabelTypes
        }),
        goodsTagText: text
      })
    },

    setSpecDescDisplay () {
      const { goodsObj } = this.data
      // 5.0.5新增需求：
      // 果切类型商品，剔除5开头的耗材外，投入品数量大于1 的商品，商品标题不展示规格描述
      const fruitCutShow = showFruitCutSpecDesc(goodsObj)
      const isCombine = isCombinedMultiGoods(goodsObj)
      const show = isCombine ? false : fruitCutShow
      this.setData({
        showSpecDesc: show
      })
    },

    /**
     * @desc 设置默认规格
     */
    setDefaultGoods () {
      const { showMinPriceTip, goodsObj } = getDefaultGoods(this.data.goodsObj, !this.data.isSearchGoods)
      const { categoryName } = this.data.currCategoryInfo || {}
      const { activityID, activityName } = this.data.activityObj || {}
      // 品类页加购，商品信息加上所属分类名称；综合专题页加购，商品信息加上当前综合专题的活动ID和活动名称；用于加购成功后的埋点上报
      Object.assign(goodsObj, {
        reportCategoryName: categoryName || '',
        reportActivityID: activityID || '',
        reportActivityName: activityName || ''
      })
      this.setData({ renderGoods: goodsObj, showMinPriceTip })
    },

    /**
     * @description 刷新用户登录状态，心享会员身份
     */
     refreshCustomerInfo () {
      const { IS_LOGIN = false, IS_VIP_CUSTOMER = false } = getCustomerInfo() || {}
      Object.assign(this._data, {IS_LOGIN, IS_VIP_CUSTOMER})
    },

    /**
     * @desc 此方法检查用户是否要展示特价
     */
     checkActivityPriceStatus () {
      const { IS_LOGIN = false, IS_VIP_CUSTOMER = false } = this._data
      const { renderGoods } = this.data
      const isShowActivityPrice = checkIsShowActivityPrice({ goodsObj: renderGoods, IS_LOGIN, IS_VIP_CUSTOMER }) || false
      this.setData({
        isShowActivityPrice
      })
    },

    // 进入商品详情
    navigateToGoodsdetail () {
      if (this.data.fromShareTimeLine) return
      const { renderGoods, labelList, goodsTagText, reportExtendData } = this.data
      const { goodsSn, takeawayAttr } = renderGoods
      // console.log('renderGoods', this.data.renderGoods);
      if (!goodsSn || !takeawayAttr) return
      const newGoodsObj = filterGoodsObjField(this.data.renderGoods)
      // 智慧零售商品卡点击埋点,1表示商品卡点击埋点
      txSensor.txGoodsSensor(this.data.renderGoods,1);
      wx.navigateTo({
        url: '/homeDelivery/pages/goodsDetail/index?homeDeliveryObj=' + JSON.stringify({
          goodsSn,
          takeawayAttr
        }),
        events: {
          getBasicInfo(callback) {
            callback({ source: 'commonGoods', data: newGoodsObj })
          }
        }
      })
      reportGoods({
        goodsInfo: renderGoods,
        labelList,
        goodsTagText,
        reportExtendData,
        eventName: EVENT_NAME_ENUM.CLICK_GOODS
      })
      this.data.isSearchGoods && this.searchGoodsReportSensors('detail')
    },

    /**
     * 多规格的判断条件： specificationGoodsList.length >= 2
     *
     * @desc 购物车按钮（5种情况）cartType取值
     * 1: 无一品多规 && 无服务 && 有库存，展示【购物车组件】------（点击直接加购）
     * 2: 有一品多规 && 有/无服务 && 有库存，展示【选规格】------（点击弹一品多规弹窗）
     * 3: 无一品多规 && 有服务 && 有库存，展示【购物车icon】------（点击弹一品多规弹窗）
     * 4: 无库存 && (单排 || 双排)，展示【售罄】------（点击无效果）
     * 5: 无库存 && 三排，展示【购物车icon】------（点击无效果）
     * 6: 无一品多规 && 无服务 && 有库存 && 是多拼商品，展示【购物车icon】------（点击弹一品多规弹窗）
     * 7: 三排有一品多规，展示【购物车icon】------（点击弹一品多规弹窗，无一品多规的情况包含在1,2里了）
     */
    setCartType () {
      const {
        curRow, renderGoods, multiSpecSingleSkuShowLayer
      } = this.data
      const {
        specificationGoodsList: specList = [],
        stockNum,
        isMultiSpec = MULTI_ENUM.NOT_MULTI_GOODS
      } = renderGoods
      const showGoodsLayer = !!specList.length
      const stockNumSum = showGoodsLayer ? specList.reduce((total, item) => total + item.stockNum, 0) : stockNum // 多规格商品库存合并
      // 默认有库存 && 有一品多规
      let cartType = 1
      // 无库存
      if (stockNumSum <= 0) {
        cartType = curRow === 'treble' ? 5 : 4
      }
      // 有库存 && 有一品多规服务
      else if (showGoodsLayer) {
        if (curRow === 'treble') {
          cartType = 7
        } else {
          cartType = specList.length === 1 ? 3 : 2
        }
      }
      // 无一品多规 && 无服务 && 有库存 && 是多拼
      // 这种特殊情况是多拼商品只有一个sku又没有服务时，也要展示一品多规弹窗，且只在瀑布流、品类页、搜索页展示
      // 多拼商品其他情况跟普通商品一样
      else if (isMultiSpec === MULTI_ENUM.IS_MULTI_GOODS && multiSpecSingleSkuShowLayer) {
        cartType = 6
      }

      this.setData({
        stockNumSum,
        cartType,
        showGoodsLayer,
        hasMutipleSpecifications: specList.length >= 2 // 是否一品多规
      })
    },

    // 获取购物车数量
    getCartCount (e) {
      this.triggerEvent('updateCount', e.detail)
    },
    // 获取购物车价格和数量(只取及时达)
    updateCartInfo (e) {
      const { cartCountOnSale, totalPriceOnSale } = e && e.detail || {}
      this.triggerEvent('updateCartInfo', { cartCountOnSale, totalPriceOnSale })
    },
    getCarList (e) {
      this.triggerEvent('updateCarList', e.detail)
    },
    // 加入购物车动画
    startAnimation (e) {
      // this.triggerEvent('startAnimation', e.detail)
      this.triggerEvent('wxsAnimation', {
        touchEvent: e.detail,
        goodsObj: this.data.renderGoods
      })
      // this.startWXSAnimation(e.detail)
    },
    // 倒计时结束
    handleCountComplete () {
      this.triggerEvent('complete')
    },

    clickPic (e) {
      this.triggerEvent('clickPic', e.currentTarget.dataset)
    },

    updateGoodsCount (ev) {
      if (!ev) return
      this.setData({
        goodsCount: ev.detail
      })
      console.log('goodsCount', this.data.goodsCount);
    },
    /**
     * @desc 一品多规的时候展示的加购数量是所有sku加购数之和
     * @param { Object } param0
     * @param { Object } param0.goodsObj 当前商品信息
     * @param { Object } param0.goodsCountObj 商品sku对应的数量
     * @returns { number }
     */
    handleGoodsCount ({ goodsObj, goodsCountObj }) {
      const { goodsSn, specificationGoodsList = [] } = goodsObj
      /**
       * @desc goodsCount 默认为0，但 goodsCountObj 里如果没有此 sku 的信息，取到的是 undefined ，所以处理为0
       * @constant { number }
       */
      let newCount = 0
      // 一品多规/可选服务情况下，specificationGoodsList里是所有sku信息
      if (Array.isArray(specificationGoodsList) && specificationGoodsList.length) {
        specificationGoodsList.forEach(spec => {
          newCount += (goodsCountObj[spec.goodsSn] || { count: 0 }).count
        })
      } else {
        newCount += (goodsCountObj[goodsSn] || { count: 0 }).count
      }
      return newCount
    },
    // 商品曝光埋点
    sensorsExpose() {
      this._data.exposeObserver = this.createIntersectionObserver().relativeToViewport({ bottom: 0 })
      this._data.exposeObserver.observe('.common-goods-expose', ({ intersectionRatio }) => {
        // 商品被隐藏
        if (!intersectionRatio) {
          // 如果超过1s,曝光已经执行,再清除timeout无所谓了
          // 如果未超过1s,曝光未执行,清除timeout正是所需要的
          return clearTimeout(this._data.exposeTimer)
        }
        this._data.exposeTimer = setTimeout(() => {
          const { renderGoods, labelList, goodsTagText, reportExtendData } = this.data
          reportGoods({
            goodsInfo: renderGoods,
            labelList,
            goodsTagText,
            reportExtendData,
            eventName: EVENT_NAME_ENUM.EXPOSURE_GOODS
          })
          this._data.exposeObserver.disconnect()
        }, 1000)
      })
    },
    searchGoodsReportSensors(sence) {
      const clickType = sence && sence === 'detail' ? '跳转商详' : '加购商品'
      const { goodsObj: { goodsName = '', goodsSn = '' } = {}, serachIndex } = this.data
      this.triggerEvent('searchGoodsReportSensors', {
        clickType,
        skuID: goodsSn || '',
        skuName: goodsName || '',
        index : serachIndex + 1,
        goodsObj:this.data.renderGoods || {}
      })
    },

    /**
     * 点击查看有货门店
     * @param {*} e
     */
    changeStore() {
      this.triggerEvent('changeStore', {
        goodsObj: this.data.renderGoods
      })
    },
    showChoiceLayer() {
      console.log('showChoiceLayer')
      const { renderGoods, labelList, goodsTagText, reportExtendData, activityObj } = this.data
      if (activityObj && activityObj.hasOwnProperty('activityID')) {
        Object.assign(reportExtendData, {
          activity_ID: activityObj.activityID || '',
          activity_Name: activityObj.activityName || ''
        })
      }
      reportGoods({
        goodsInfo: renderGoods,
        labelList,
        goodsTagText,
        reportExtendData,
        eventName: EVENT_NAME_ENUM.CLICK_CHOOSE_SPEC_BUTTON
      })
    },
    handleTapCart() {
      console.log('tapCart')
      const { renderGoods, labelList, goodsTagText, reportExtendData = {}, activityObj } = this.data
      if (activityObj && activityObj.hasOwnProperty('activityID')) {
        Object.assign(reportExtendData, {
          activity_ID: activityObj.activityID || '',
          activity_Name: activityObj.activityName || ''
        })
      }
      const sensorsObj = {
        goodsInfo: renderGoods,
        labelList,
        goodsTagText,
        reportExtendData,
        eventName: EVENT_NAME_ENUM.CLICK_SHOPPING_CART_BUTTON
      }
      reportGoods(sensorsObj)
    },
    touchHandle() {}
  }
})

