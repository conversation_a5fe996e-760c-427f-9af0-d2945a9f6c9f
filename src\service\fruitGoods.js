/**
 *
 * 先读取缓存文件，没有再通过cos读取cdn文件
 */
import EventSource from '../utils/eventSource/index'
import { newUserOrNotLogin } from '~/source/js/requestData/activityPrice';
import { fruitGoodsMergeAct } from './fruitAct/activity';
const { deepClone, deepCloneMap } = require('../utils/util.js')
const { ENV, baseUrl } = require('../utils/config.js')
const request = require('../utils/request')
const { readZipEntry, downloadFile } = require('../utils/readAndSaveFile/index')
import { getDefaultGoods } from '../utils/goods/getDefaultGoods'
const {
  forMatGoods,
  calculateDosageStock,
  matchMutiGoodsSpuStock,
  handleSpecAndService,
  formatMultiGoods,
  filterAssociationList,
  collectionGoodsSn,
  getCitySaleStatus,
  isCombineGroup,
  SUB_TYPES,
  fliterNewPriceActGoods,
  getSaveMoneyText,
} = require('./fruitGoodsUtil.js')
const cos = require('./cosInstance')
const IS_MULTI_GOODS = 1
const NOT_MULTI_GOODS = 0

let sseStoreCode = '' // 用来缓存sse storeCode
let sseCache = '' // 用来缓存sse
// 将商品类型分类逻辑提取为常量
const PRICE_TYPE_HANDLERS = {
  [SUB_TYPES.XIN_REN_TE_JIA]: 'newPriceList',
  [SUB_TYPES.YUN_YING_TE_JIA]: 'priceList',
  [SUB_TYPES.MEN_DIAN_TE_JIA]: 'storePriceList'
}

// 用来缓存读取cos，并发只读取一次
const cacheCosByReadKey = new Map()
class ReadGoodsInfoByCos {
  constructor({ logger = false, checkETag = false  }) {
    this._logger = logger
    this.cache = cacheCosByReadKey
    // this.cosPathPrefix =  '/goods-zip/'
    this.cos = cos
    this.lastETag = ''
    this.checkETag = checkETag
  }
  getCosHead(readKey){
    const cosUrl = this.cos.completeNoAuthUrl(readKey + '.zip')
    return new Promise((resolve, reject) => {
      wx.request({
        url: cosUrl,
        method: 'HEAD',
        timeout: 2000,
        success: (res) => {
          res ? resolve(res.header) : resolve({})
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }
  getEtag(header) {
    if (Object.prototype.toString(header) !== '[object Object]') return ''
    return header.Etag
  }
  /**
   *
   * @param {object} param
   * @param {string} param.readKey
   * @param {string} param.lastEtag
   */
  async checkCosChange(param) {
    try {
      const {readKey, lastEtag} = param
      const header = await this.getCosHead(readKey)
      const etag = this.getEtag(header)
      // console.log('checkCosChange', etag, lastEtag);
      return etag === lastEtag
    } catch (error) {
      console.log('checkCosChange error', error);
      return false
    }
  }
  /**
   * @description 缓存读取操作
   * @param {*} readKey
   * @returns
   */
  readAsyncWithCache(readKey) {
    if (this.cache.has(readKey)) {
      return this.cache.get(readKey);
    }
    const promise = this.readAsync(readKey);
    this.cache.set(readKey, promise);
    return promise;
  }
  /**
   * @desc 读取文件
   * @param {string} key 文件名
   */
  async readAsync (readKey) {
    if (typeof readKey === 'string') {
      // 文件没有则读取cos
      // this.getResultByReadUnZip(`${readKey}`)
      return this.getResultByReadZipEntry(`${readKey}`)
    }
  }
  /**
   * @desc 从cos生成本地路径直接读取zip
   * @param {string} key
   * @returns {object}
   */
  async getResultByReadZipEntry (readKey) {
    const cosUrl = this.cos.completeNoAuthUrl(readKey + '.zip')
    const tempFilePath = await downloadFile(cosUrl)
    // const { tempFilePath, header } = await downloadFile(cosUrl)
    // this.lastEtag = this.getEtag(header)
    // setInterval(() => {
    //   this.checkCosChange({readKey, lastEtag: this.lastEtag}).then(res => console.log('checkCosChange', res))
    // }, 60 * 1000)
    // wxappLruCache.set(readKey, tempFilePath)
    // 通过本地链接读取zip文件
    let result = ''
    if (tempFilePath){
      result = await readZipEntry(readKey, tempFilePath)
    }
    // 读取完成，删除缓存，防止下次读取不到
    if (this.cache.has(readKey)) {
      this.cache.delete(readKey);
    }
    return this._formatResponse(result.data)
  }
  _formatArratBuff(data) {
    try {
      // 将 ArrayBuffer 转换为 Uint8Array
      const uint8Array = new Uint8Array(data);
      let string = '';
      for (let i = 0; i < uint8Array.length; i++) {
          string += String.fromCharCode(uint8Array[i]);
      }
      const newStr = decodeURIComponent(escape((string)))
      return JSON.parse(newStr)
    } catch(error) {
      console.log('_formatArratBuff error', error);
      return []
    }
  }
  /**
   * @desc string parse to json
   * @param {str} string
   * @returns {object}
   */
  _formatResponse (str) {
    if (!str) {
      return []
    }
    if (typeof str === 'object') {
      return this._formatArratBuff(str)
    }
    if (typeof str !== 'string') {
      return []
    }
    try {
      const data = JSON.parse(str)
      return data
    } catch (error) {
      console.log('_formatResponse error', error);
      return []
    }
  }
}
/**
 * @description 以门店维度缓存商品
 */
class CacheFruitGoods {
  constructor() {
    this.storeGoodsMap = new Map() // 定义门店商品map，门店编码作为key获取商品集合
    this.storeGoodsIdMap = new Map() // 存储id和map的关系
    this.storeSpuMap = new Map()
    this.storeMultiSpecGroupsMap = new Map()
    /**组合品 */
    this.storeCombineGoodsMap = new Map()
    this.cacheSize = 8
    // this.cacheTime = 15 * 1000
    this.cacheTime = 5 * 60 * 1000
    // this.cacheTime = 10000
    this.setTimeMap = new Map();
  }
  delGoodsMap(key) {
    this.storeGoodsMap.delete(key);
    this.storeSpuMap.delete(key);
    this.storeMultiSpecGroupsMap.delete(key);
    this.storeCombineGoodsMap.delete(key)
    this.setTimeMap.delete(key);
  }
  getGoodsMap(key) {
    // if (this.isCacheExpired(key)) {
    //   this.delGoodsMap(key)
    //   return new Map()
    // }
    return this.storeGoodsMap.get(key) || new Map()
  }
  getSpuMap(storeCode) {
    return this.storeSpuMap.get(storeCode);
  }
  setSpuMap(goods, spuMap) {
    const {
      associationList,
      eshopGroupNumber,
      groupNumber,
      takeawayAttr = '',
      goodsSn,
      isMultiSpec,
      multiSpecGroupId,
      saleType
    } = goods
    // 组合品不需要
    if (isMultiSpec === 0 && multiSpecGroupId && saleType === 7) return
    // 剔除耗材
    const associationListFilterRes = filterAssociationList(associationList)
    if (!associationListFilterRes.length || associationListFilterRes.length > 1) return
    // 剔除之后投入品数量等于1的才能组合一品多规
    const spuNumber = associationListFilterRes[0].basicGoodsSn
    if (!spuNumber) return
    let groupNumberMap = spuMap.get(spuNumber)
    if (!groupNumberMap) {
      groupNumberMap = new Map
      spuMap.set(String(spuNumber), groupNumberMap)
    }
    const groupNumberKey = takeawayAttr && takeawayAttr.toUpperCase() === 'B2C' ? groupNumber : eshopGroupNumber


    // 非一品多规商品不用存储
    if (String(groupNumberKey) === 0) {
      return
    }
    if (!groupNumberMap.get(groupNumberKey)) {
      groupNumberMap.set(groupNumberKey, new Map)
    }
    groupNumberMap.get(groupNumberKey).set(goodsSn, goods)

  }
  getMultiSpecGroupsMap(storeCode) {
    return this.storeMultiSpecGroupsMap.get(storeCode);
  }
  setMultiSpecGroupsMap(goods, multiSpecGroupsMap) {
    const {
      goodsSn,
      associationList,
      isMultiSpec = NOT_MULTI_GOODS,
      multiSpecGroupId,
    } = goods
    if (!Array.isArray(associationList)) return
    // 处理多拼的情况
    // 组合多拼商品逻辑： 通过 isMultiSpec 判断是否多拼商品，然后通过 multiSpecGroupId 组合
    if (isMultiSpec === IS_MULTI_GOODS) {
      let sameGroupIdMap = multiSpecGroupsMap.get(String(multiSpecGroupId))
      if (!sameGroupIdMap) {
        sameGroupIdMap = new Map
        multiSpecGroupsMap.set(String(multiSpecGroupId), sameGroupIdMap)
      }
      sameGroupIdMap.set(goodsSn, goods)
    }
  }
  setCombineGoodsMap(goods, combineGoodsMap){
    const {
      goodsSn,
      isMultiSpec = 0,
      multiSpecGroupId,
    } = goods
    // v5.0.1
    // 组合品一品多规逻辑：先判断isMultiSpec等于0，然后multiSpecGroupId相同
    if (isMultiSpec === NOT_MULTI_GOODS && multiSpecGroupId) {
      let sameGroupIdMap = combineGoodsMap.get(String(multiSpecGroupId))
      if (!sameGroupIdMap) {
        sameGroupIdMap = new Map
        combineGoodsMap.set(String(multiSpecGroupId), sameGroupIdMap)
      }
      sameGroupIdMap.set(goodsSn, goods)
      return
    }
  }
  getCombineGoodsMap(storeCode) {
    return this.storeCombineGoodsMap.get(storeCode);
  }
  setGoodsMap(key, goodsList) {
    const goodsMap = this._convertGoodsToMap(key, goodsList)
    if (goodsMap.size === 0) {
      return new Map();
    }
    if (this.storeGoodsMap.size >= this.cacheSize) {
      const deleteKey = this.storeGoodsMap.keys().next().value;
      this.delGoodsMap(deleteKey)
    }
    // this.storeGoodsMap.has(key)
    if (this.storeGoodsMap.has(key) && !this.isCacheExpired(key)) {
      const existingGoodsMap = this.storeGoodsMap.get(key);
      goodsMap.forEach((goods, goodsSn) => existingGoodsMap.set(goodsSn, goods));
    } else {
      this.storeGoodsMap.set(key, goodsMap);
      this.setTimeMap.set(key, Date.now());
    }
    return goodsMap
  }
  getGoodsSn({ eshopGoodsId, storeCode }) {
    const goodsIdMap = this.storeGoodsIdMap.get(storeCode) || new Map();
    return goodsIdMap.get(String(eshopGoodsId)) || '';
  }
  setEshopIdAndGoodsSn({ eshopGoodsId, goodsSn, storeCode }) {
    if (!eshopGoodsId) {
      return
    }
    if (!this.storeGoodsIdMap.has(storeCode)) {
      this.storeGoodsIdMap.set(storeCode, new Map());
    }
    this.storeGoodsIdMap.get(storeCode).set(String(eshopGoodsId), goodsSn);
  }
   // 转换商品列表至map
   _convertGoodsToMap(storeCode, goodsList = []) {
    const goodsMap = new Map();
    const expired = this.isCacheExpired(storeCode)
    let multiSpecGroupsMap = this.storeMultiSpecGroupsMap.get(storeCode);
    if (!multiSpecGroupsMap || expired) {
      multiSpecGroupsMap = new Map();
      this.storeMultiSpecGroupsMap.set(storeCode, multiSpecGroupsMap);
    }
    let spuMap = this.storeSpuMap.get(storeCode);
    if (!spuMap || expired) {
      spuMap = new Map();
      this.storeSpuMap.set(storeCode, spuMap);
    }
    let combineGoodsMap = this.storeCombineGoodsMap.get(storeCode);
    if (!combineGoodsMap || expired) {
      combineGoodsMap = new Map();
      this.storeCombineGoodsMap.set(storeCode, combineGoodsMap);
    }
    goodsList.forEach((goods) => {
      goodsMap.set(String(goods.goodsSn), goods);
      this.setEshopIdAndGoodsSn({ storeCode, eshopGoodsId: goods.eshopGoodsId, goodsSn: goods.goodsSn });
      this.setSpuMap(goods, spuMap)
      this.setCombineGoodsMap(goods, combineGoodsMap)
      this.setMultiSpecGroupsMap(goods, multiSpecGroupsMap)
    })
    return goodsMap;
  }
  isCacheExpired(storeCode) {
    const setTime = this.setTimeMap.get(storeCode);
    if (!setTime) return false;
    const b = Date.now() - setTime >= this.cacheTime
    return b;
  }
}
const cacheFruitGoods = new CacheFruitGoods()
class FruitGoods {
  constructor({
    storeCode,
    cityCode,
    deliveryCenterCode,
    filterCitySaleStatus = true,
    filterSaleStatus = false, // 筛选门店上下架
    isNeedMergeByGroup = true, // 是否聚合一品多规
    isNeedMergeMultiSpec = true, // 是否聚合多拼
  }) {
    this.storeCode = storeCode
    this.cityCode = cityCode
    this.deliveryCenterCode = deliveryCenterCode
    this.filterCitySaleStatus = filterCitySaleStatus
    this.filterSaleStatus = filterSaleStatus
    this.systemTime = Date.now(); // 系统时间
    this.isNeedMergeByGroup = isNeedMergeByGroup
    this.isNeedMergeMultiSpec = isNeedMergeMultiSpec
    this.getGoodsPromiseMap = new Map()
  }
  /**
   * @description 传进商品编码，按原顺序返回商品详细信息
   */
  async getComplateGoodsList(goodsList) {
    try {
      return this.getGoodsComplateInfoList(goodsList)
    } catch(err) {
      console.err(err)
      getApp().monitor.report({
        message: 'fruitGoods getComplateGoodsList',
        errMsg: err,
      })
      return []
    }
  }
  /**
   * @description 传进商品编码，返回商品详细信息map
   * @param {Array} goodsList
   * [{eshopGoodsId: "102705",goodsSn: "",takeawayAttr: "及时达"}]
   * 老数据配置的是eshopGoodsId，新数据是goodsSn
   */
  async getGoodsComplateInfoMap(goodsSnList) {
    const goodsComplateList = await this.getGoodsComplateInfoList(goodsSnList)
    return this._convertToGoodsMap(goodsComplateList)
  }
  // 设置商品动态信息
  async setGoodsDynamicInfo(goodsBaseInfoList) {
    // 扁平化一品多规和多拼数组
    const flattedList = this._getODGoodsList(goodsBaseInfoList)
    const goodsDynamicInfo = await this.getGoodsDynamicInfo(flattedList)
    this._assembleGoods({
      goodsBaseInfoList: flattedList,
      goodsDynamicInfo
    })

    const { spuStock } = goodsDynamicInfo
    const goodsComplateList = goodsBaseInfoList.map((goods) => {
      if (goods.isMultiSpec === IS_MULTI_GOODS) {
        matchMutiGoodsSpuStock(goods, spuStock)
      }
      return handleSpecAndService(goods);
    });
    return goodsComplateList
  }
  /**
   *
   * @param {Array<Record<string, any>>} goodsSnList
   * @returns 完整的商品信息
   */
  async getGoodsComplateInfoList(goodsSnList) {
    // 获取商品基本信息
    const goodsBaseInfoList = await this.getGoodsBaseInfoList(goodsSnList, false)
    const collectionGoodsBaseInfoList = (this.isNeedMergeByGroup || this.isNeedMergeMultiSpec) ? collectionGoodsSn(goodsBaseInfoList, false) : goodsBaseInfoList
    // 设置商品动态信息
    return this.setGoodsDynamicInfo(collectionGoodsBaseInfoList)
  }
  /**
   * @description 获取商品业务类型，返回['及时达', 'B2C']
   * @param {*} goodsList
   */
  getTakeawayAttrs(goodsList) {
    const arr = []
    goodsList.forEach( goods => {
      if (goods.takeawayAttr && !arr.includes(goods.takeawayAttr)) {
        arr.push(goods.takeawayAttr)
      }
    })
    return arr
  }
  /**
   * @description 获取商品的基本信息
   * @param {*} goodsSnList
   * @returns
   *
   */
  async getGoodsBaseInfoList(goodsSnList, isRetry = true) {
    const takeawayAttrs = this.getTakeawayAttrs(goodsSnList)
    const curStoreGoodsMap = takeawayAttrs.includes('及时达') ? await this.getCurStoreGoodsMap('及时达') : new Map() // 及时达
    const curCityGoodsMap = takeawayAttrs.includes('B2C') ? await this.getCurStoreGoodsMap('B2C') : new Map() // 全国送
    const noMatchGoodsList = []
    goodsSnList.forEach( item => {
      const goodsSn = item.goodsSn ? item.goodsSn : this.convertGoodsIdToGoodsSn(item.eshopGoodsId)
      if (!goodsSn) {
        noMatchGoodsList.push(item)
      }
      if (item.takeawayAttr === '及时达' && !curStoreGoodsMap.has(goodsSn)) {
        noMatchGoodsList.push(item)
      }
      if (item.takeawayAttr === 'B2C' && !curCityGoodsMap.has(goodsSn)) {
        noMatchGoodsList.push(item)
      }
    })
    // 判断如果是灰度用户，则走sse逻辑，非灰度用户沿用之前逻辑
    // const isGrayUser = await checkIsSseGray()
    // wx.setStorageSync('isGrayUser', isGrayUser)
    // if (isGrayUser) {
      await this._updateGoodsRealTime()
    // }
    if (noMatchGoodsList.length > 0) { // cos没有查到的商品走接口查询{
      const goodsList = await this.getGoodsDetailList(noMatchGoodsList)
      this.setNoMathGoodsToCache(goodsList)
    }
    let goodsBaseInfoList = this.matchGoodsDetailList(goodsSnList)
    // 过滤城市下架商品
    // cos商品都是没有城市下架的，所以没必要筛选
    // if (this.filterCitySaleStatus) {
    //   goodsBaseInfoList = this.filterCitySaleStatusIsUpGoods(goodsBaseInfoList)
    // }
    // 过滤门店下架商品
    if (this.filterSaleStatus) {
      goodsBaseInfoList = this.filterSaleStatusIsUpGoods(goodsBaseInfoList)
    }
    if (!goodsBaseInfoList.length && isRetry) {
      return this.getGoodsBaseInfoList(goodsSnList.map(item => {
        return {
          ...item,
          goodsSn: ''
        }
      }), false)
    }
    return goodsBaseInfoList
  }
  /**
   * 要过滤城市下架商品和会员价异常商品
   */
  setNoMathGoodsToCache(goodsList = []) {
    if (!goodsList || !goodsList.length) {
      return
    }
    const storeGoodsList = [], cityGoodsList = [];
    goodsList.forEach( goods => {
      const citySaleStatus = getCitySaleStatus(goods)
      const { memberPrice = 0 } = goods
      if (!citySaleStatus) return
      if (Number(memberPrice) === 0) return
      if (goods.takeawayAttr === '及时达') {
        storeGoodsList.push(goods)
      } else {
        cityGoodsList.push(goods)
      }
    })
    cacheFruitGoods.setGoodsMap(this.storeCode, deepClone(storeGoodsList))
    cacheFruitGoods.setGoodsMap(this.cityCode, deepClone(cityGoodsList))
  }
  matchGoodsDetailList(goodsSnList) {
    const takeawayAttrs = this.getTakeawayAttrs(goodsSnList)
    const curStoreGoodsMap = takeawayAttrs.includes('及时达') ?  cacheFruitGoods.getGoodsMap(this.storeCode) : new Map() // 及时达
    const curCityGoodsMap = takeawayAttrs.includes('B2C') ? cacheFruitGoods.getGoodsMap(this.cityCode) : new Map() // 全国送
    const multiSpecGroupsMap = cacheFruitGoods.getMultiSpecGroupsMap(this.storeCode)
    const combineGoodsMap = cacheFruitGoods.getCombineGoodsMap(this.storeCode)
    const citySpuMap = takeawayAttrs.includes('B2C')  ? cacheFruitGoods.getSpuMap(this.cityCode) : new Map()
    const spuMap = takeawayAttrs.includes('及时达')  ? cacheFruitGoods.getSpuMap(this.storeCode) : new Map()
    return goodsSnList.map( item => {
      const goodsSn = item.goodsSn ? item.goodsSn : this.convertGoodsIdToGoodsSn(item.eshopGoodsId)
      const curGoods = deepClone(item.takeawayAttr === '及时达' ? curStoreGoodsMap.get(goodsSn): curCityGoodsMap.get(goodsSn))
      if (!curGoods) {
        return
      }
      if (curGoods.groupSupport === 'Y' || curGoods.saleMode === 1) {
        return
      }
      // 聚合多拼和多规格
      if (curGoods.isMultiSpec === IS_MULTI_GOODS) {
        this.mergeMultiSpec(curGoods, multiSpecGroupsMap)
      }
      else if (isCombineGroup((curGoods))) {
        this.mergeCombineGroup(curGoods, combineGoodsMap)
      }
      else {
        this.mergeByGroup(curGoods, curGoods.takeawayAttr === '及时达'?spuMap: citySpuMap)
      }
      curGoods.systemTime = this.systemTime // 用于结算门店特价倒计时
      return curGoods
    }).filter( item => item)
  }
  /**
   * @description 聚合多规格
   * @param {#} goodsDetail
   */
  mergeByGroup(goodsDetail, spuMap) {
    if (!this.isNeedMergeByGroup || !spuMap) {
      return;
    }
    // 聚合一品多规
    const {
      saleType = 0,
      associationList,
      eshopGroupNumber,
      groupNumber,
      takeawayAttr,
      goodsSn,
      eshopGoodsId,
      multiSpecGroupId
    } = goodsDetail;
    const citySaleStatus = getCitySaleStatus(goodsDetail)
    // 如果这个商品本身就是城市下架，不需要做聚合操作，外面决定是否进行剔除
    if (!citySaleStatus) return
    // 只有标准份和果切才有一品多规
    if (![1, 2].includes(Number(saleType))) {
      return
    }
    const associationListNew = filterAssociationList(associationList)
    // 只有单投入品才可以组成一品多规
    // 标准分只能单投入品
    // 果切可以多投入品，但是剔除耗材之后，只剩一个投入品的情况才能组一品多规
    if (!associationListNew.length || associationListNew.length > 1) {
      return
    }

    // 无关联投入品（找不到spu编码，associationList[0].basicGoodsSn）
    const spuNumber =associationListNew[0].basicGoodsSn
    if (!spuNumber) {
      return
    }
    if (!spuMap) {
      return
    }
    const sameSpuMap = spuMap.get(String(spuNumber))
    if (!sameSpuMap) {
      return
    }
    const groupNumberKey = takeawayAttr && takeawayAttr.toUpperCase() === 'B2C' ? groupNumber : eshopGroupNumber
    // groupNumberKey 为0不聚合
    if (!groupNumberKey) {
      return
    }
    const groupNumberMap = sameSpuMap.get(groupNumberKey)
    if (!groupNumberMap) {
      return
    }
    // 当前商品对应聚合在一起的其他规格商品，
    // 聚合规则：spu编码相同前提，自营商品，eshopGroupNumber分别相同的商品分在同一组，B2C商品：groupNumber分别相同的商品分在同一组
    const specificationGoodsList = [...groupNumberMap.values()].filter(d => {
      // 过滤掉当前商品，并且只有同类型(saleType)的商品才能组合到一起
      const {
        goodsSn: dGoodsSn,
        eshopGoodsId: dEshopGoodsId,
        saleType: dSaleType
      } = d
      const citySaleStatus = getCitySaleStatus(d)
      // 普通一品多规剔除城市下架商品
      if (!citySaleStatus) return false
      if (goodsSn) {
        return dGoodsSn !== goodsSn && saleType === dSaleType
      }
      return String(dEshopGoodsId) !== String(eshopGoodsId) && saleType === dSaleType;
    }).map(d => deepClone(d));
    Object.assign(goodsDetail, {
      specificationGoodsList,
    });
  }
  /**
   * @description 聚合多拼
   * @param {#} goodsDetail
   */
  mergeMultiSpec(goodsDetail, multiSpecGroupsMap) {
    if (!this.isNeedMergeMultiSpec || !multiSpecGroupsMap) return
    const { multiSpecGroupId, goodsSn } = goodsDetail
    if (!multiSpecGroupId) return
    const sameGroupIdMap = multiSpecGroupsMap.get(String(multiSpecGroupId))
    if (!sameGroupIdMap) return
    // 接口使用的字段是 multiSpecGoodsList
    const multiSpecGoodsList = this.filterAndClone(sameGroupIdMap, goodsSn)
    Object.assign(goodsDetail, {
      multiSpecGoodsList,
    });
  }
  /**
   * @desc 聚合组合品
   * v5.0.1 组合品支持一品多规，组合品一品多规逻辑：先判断isMultiSpec等于0，然后multiSpecGroupId相同
   */
  mergeCombineGroup(goodsDetail, combineGoodsMap) {
    // 聚合一品多规
    const {
      goodsSn,
      multiSpecGroupId
    } = goodsDetail;
    if (!this.isNeedMergeByGroup || !combineGoodsMap || !multiSpecGroupId) {
      return;
    }
    let sameGroupIdMap = combineGoodsMap.get(String(multiSpecGroupId))
    if (!sameGroupIdMap) return
    const specificationGoodsList = this.filterAndClone(sameGroupIdMap, goodsSn)
    Object.assign(goodsDetail, {
      specificationGoodsList,
    });
  }
  /**
   * @desc 剔除相同的sku，然后深拷贝一份
   */
  filterAndClone(goodsMap, goodsSn) {
    return [...goodsMap.values()].filter(v => {
      // 剔除相同的sku
      return v.goodsSn !== goodsSn
    }).map(value => deepClone(value))
  }
   /**
   * @desc 筛选城市上架的商品
   * @param {Array} goodsList
   * @returns
   */
  filterCitySaleStatusIsUpGoods (goodsList) {
    if (!Array.isArray(goodsList)) return []
    return goodsList.filter(goods => {
      if (goods.takeawayAttr === '及时达') {
        return goods.citySaleStatus !== 0
      } else if (goods.takeawayAttr === 'B2C') {
        return goods.saleStatus
      }
      // const citySaleStatus = goods.hasOwnProperty('citySaleStatus') ? goods.citySaleStatus : goods.saleStatus
      // return citySaleStatus
    })
  }
  /**
   * @desc 筛选门店上架的商品，所以b2c不考虑
   * @param {Array<any>} goodsList
   */
  filterSaleStatusIsUpGoods(goodsList) {
    if (!Array.isArray(goodsList)) return []
    return goodsList.filter(goods => {
      if (goods.takeawayAttr === '及时达') {
        return goods.saleStatus !== 0
      } else if (goods.takeawayAttr === 'B2C') {
        return false
      }
    })
  }
  async getCurStoreGoodsMap(takeawayAttr = '及时达') {
    try {
      // 先读取缓存
      const key = takeawayAttr === '及时达' ? this.storeCode : this.cityCode
      if (!key) return new Map()
      const readKey = takeawayAttr === '及时达' ? `/goods-zip/simple-${key}-5` : `/goods-b2c-zip/simple-${key}-5`
      FruitGoods.readCosKey = readKey
      const handleGoodsFn = async () => {
        // 没有缓存，走网络请求获取
        let cosGoodsList = await this.getGoodsMapByCos(readKey)
        // 过滤会员价异常的商品
        cosGoodsList = cosGoodsList.filter(item => {
          return item.memberPrice && item.memberPrice > 0
        })
        // 缓存起来，转换为map
        const goodsMap = cacheFruitGoods.setGoodsMap(key, deepClone(cosGoodsList))
        this.getGoodsPromiseMap.delete(readKey)
        return goodsMap
      }
      const promisifyHandle = () => {
        if (this.getGoodsPromiseMap.has(readKey)) {
          return this.getGoodsPromiseMap.get(readKey)
        }
        this.getGoodsPromiseMap.set(readKey, handleGoodsFn())
        return this.getGoodsPromiseMap.get(readKey)
      }
      const curStoreGoods = cacheFruitGoods.getGoodsMap(key)
      // 第一次肯定没有数据，所以不会走进if
      if (curStoreGoods.size) {
        // 这里再检查有效期，若过期，异步获取
        if (cacheFruitGoods.isCacheExpired(key)) setTimeout(promisifyHandle)
        return curStoreGoods
      }
      return promisifyHandle()
    } catch(err) {
      return new Map()
    }
  }
  /**
   * @description 从cos读取商品
   * @returns
   */
  async getGoodsMapByCos(readKey) {
    // 获取商品基本信息
    const readGoodsInfoByCos = new ReadGoodsInfoByCos({ logger: ENV !== 'prod' })
    FruitGoods.readCos = readGoodsInfoByCos
    try {
      const res = await readGoodsInfoByCos.readAsyncWithCache(readKey)
      return res
    } catch(err) {
      console.error('getGoodsMapByCos', err)
      return []
    }
  }
  _getGoodsSock({
    goodsList,
    fruitStockMap,
    b2cStockMap
  }) {
    const res = {
      spuStock: fruitStockMap,
      // 把b2c库存信息整合进来
      skuStock: b2cStockMap || {}
    };
    goodsList.forEach( item => {
      const { associationList, goodsSn } = item;
      if (!associationList || !associationList.length) {
        res.skuStock[goodsSn] = 0;
        return;
      }
      // sku库存计算逻辑：
      // 1.查看associationList里面spu是否启用库存，如果不启用库存，sku计算库存，过滤掉改spu，
      // 2.过滤完不需要计算spu后，sku库存取其余spu最小库存值
      // const associationAllList = _.cloneDeep(associationList);
      // _.remove(
      //   associationAllList,
      //   (item) =>
      //   typeof item.enableInventory !== "undefined" &&
      //   Number(item.enableInventory) === 0
      // );
      const associationAllList = associationList.filter( item => {
        return typeof item.enableInventory === "undefined" || Number(item.enableInventory) !== 0
      })
      if (!associationAllList.length) {
        // spu全都不启用库存校验，sku设置最大库存
        res.skuStock[goodsSn] = 999999;
      } else {
        const dosageStockParams = {
          associationAllList,
          basicGoodsStockMap: fruitStockMap
        }
        res.skuStock[goodsSn] = calculateDosageStock(dosageStockParams);
      }
    });
    return res;
  }
 /**
  * @description 组合商品信息
  * @param {*} param
  * @param {Array} param.goodsBaseInfoList 商品基本信息列表
  * @param {object} param.goodsDynamicInfo 商品动态信息
  * @returns
  */
  _assembleGoods({
    goodsBaseInfoList,
    goodsDynamicInfo
  }) {
    const {
      newActMap,
      goodsPriceActivityMap,
      goodsMaxBuyMap,
      goodsRealPriceMap,
    } = goodsDynamicInfo
    goodsBaseInfoList.forEach( item => {
      const { goodsSn } = item
      fruitGoodsMergeAct({
        fruitGoods: item,
        isNewUser: newUserOrNotLogin(),
        actRecord: { skuToActInfo: goodsPriceActivityMap, skuToNewUserActInfo: newActMap }
      })
      // 匹配限购
      if (typeof goodsMaxBuyMap[goodsSn] !== 'undefined') {
        Object.assign(item, {
          goodsRestrictions: 0,
          purchaseLimit: goodsMaxBuyMap[goodsSn],
        })
      }
      // 匹配实时价格
      const realGoods = goodsRealPriceMap[goodsSn]
      if (realGoods) {
        if (realGoods.saleStatus === 0 ) {
          realGoods.stockNum = 0
        }
        Object.assign(item, realGoods)
      }
      Object.assign(item, forMatGoods(item))
    })
    return goodsBaseInfoList
  }
  /**
   * @description 获取需要查库存的商品列表
   * @param {*} goodsList
   */
  _getNeedQueryStockGoodList(goodsList) {
    const goodsSnSet = new Set();
    const fruitList = []
    const b2cList = []
    goodsList.forEach((goods) => {
      const {
        saleStatus,
        goodsSn,
        associationList = [],
        takeawayAttr,
        verifyInventory, // sku是否启用库存校验 0 不启用 1 启用
      } = goods
      // sku如果不启用库存校验，不需要查spu投入品设置库存
      const checkVerifyInventory = typeof verifyInventory !== "undefined" && Number(verifyInventory) === 0;
      // 下架商品不查库存
      if (!saleStatus) {
        goods.stockNum = 0
        return
      }
      goods.stockNum = checkVerifyInventory ? 999999 : void 0;
      if (goods.stockNum ===  void 0 && !goodsSnSet.has(goodsSn)) {
        const isB2C = takeawayAttr === 'B2C'
        goodsSnSet.add(goodsSn);
        isB2C ? b2cList.push(goodsSn) : fruitList.push({
          goodsSn,
          associationList,
        });
      }
    })
    return { fruitList, b2cList }
  }
  /**
   * @description 扁平化数组
   * @param {*} goodsList
   * @returns
   */
  _getODGoodsList(goodsList) {
    const flattedList = []
    goodsList.forEach((goods) => {
      if (goods.isMultiSpec === IS_MULTI_GOODS) formatMultiGoods(goods)
      flattedList.push(goods);
      const {
        specificationGoodsList = []
      } = goods;
      (specificationGoodsList || []).forEach((spec) => {
        flattedList.push(spec);
      })
    })
    return flattedList
  }
  _getStockParams(goodsList) {
    let spuNumberList = []
    goodsList.forEach((item) => {
      const {
        associationList = []
      } = item
      spuNumberList = spuNumberList.concat(associationList.map( item => item.basicGoodsSn ))
    });
    return Array.from(new Set(spuNumberList))
  }
  /**
   * @description 获取库存、特价、限购信息
   * @param {*} goodsList
   * @returns
   */
  async getGoodsDynamicInfo(goodsList) {
    const { fruitList, b2cList } = this._getNeedQueryStockGoodList(goodsList)
    const spuNumberList = this._getStockParams(fruitList)
    const { userID } = wx.getStorageSync('user') || {}
    const goodsSnList = goodsList.map( item => {
      return {
        goodsSn: item.goodsSn,
        takeawayAttr: item.takeawayAttr || '及时达'
      }
    })
    const params = {
      customerID: userID || '-1',
      cityCode: this.cityCode,
      storeCode: this.storeCode,
      deliveryCenterCode: this.deliveryCenterCode,
      goodsList: goodsSnList,
      spuNumberList,
      b2cList
    }
    if (!goodsSnList.length) {
      return {}
    }
    const dynamicInforRes = await getApp().api.getGoodsDynamicInfoList(params)
    this.systemTime = dynamicInforRes.systemTime
    const {
      spuStockMap,
      b2cStockMap,
      goodsPriceActivityMap = {},
      goodsEffectNumMap = {},
      goodsMaxBuyMap = {},
      goodsRealPriceMap = {},
      /**新人特价 */
      newActMap = {}
    } = dynamicInforRes.data
    const { spuStock, skuStock } = this._getGoodsSock({
      goodsList: fruitList,
      fruitStockMap: spuStockMap,
      b2cStockMap
    })
    goodsList.forEach( (item) => {
      if (typeof item.stockNum === "undefined") {
        item.stockNum = skuStock[item.goodsSn] || 0;
      }
    });
    return {
      newActMap,
      goodsPriceActivityMap,
      goodsEffectNumMap,
      goodsMaxBuyMap,
      goodsRealPriceMap,
      spuStock,
      skuStock
    }
  }
  /**
   * @description 获取商品详细列表
   * @param {*} goodsList  [{eshopGoodsId: "102705",goodsSn: "",takeawayAttr: "及时达"}]
   * @returns
   */
  async getGoodsDetailList(goodsList) {
    const { cityCode, storeCode } = wx.getStorageSync('timelyCity') || {}

    // let goodsSnList = new Set()
    // let goodsIdList = new Set()
    // const newGoodsList = goodsList.filter( item => {
    //   if (item.goodsSn && !goodsSnList.has(item.goodsSn)) {
    //     goodsSnList.add(item.goodsSn)
    //     return item
    //   }
    //   if (item.eshopGoodsId && !goodsIdList.has(item.eshopGoodsId)) {
    //     goodsIdList.add(item.eshopGoodsId)
    //     return item
    //   }
    // })
    const params = {
      cityCode,
      storeCode,
      goodsList: goodsList,
      filterCitySaleStatus: this.filterCitySaleStatus
      // isNeedMergeByGroup: this.isNeedMergeByGroup,
      // isNeedMergeMultiSpec: this.isNeedMergeMultiSpec
    }
    try {
      const result = await getApp().api.getGoodsDetailList(params)
      return result.data
    } catch(err) {
      console.error(err)
      return []
    }
  }
  /**
   * @description 将商品列表转换为map形式返回
   * @param {*} goodsList
   * @param {*} key goodsSn or eshopGoodsId
   * @returns
   */
  _convertToGoodsMap(goodsList = [], key = 'goodsSn') {
    const goodsMap = new Map
    goodsList.forEach( goods => {
      goodsMap.set(String(goods[key]), goods)
    })
    return goodsMap
  }
  convertGoodsIdToGoodsSn(eshopGoodsId) {
    return cacheFruitGoods.getGoodsSn({ eshopGoodsId, storeCode: this.storeCode })
  }
  // 从sse更新商品
  async _updateGoodsRealTime() {
    // 初始化建立一次链接，门店变化时建立新的连接
    try {
      if (!this.storeCode) {
        return
      }
      // 切换门店则之前链接断掉
      if (sseCache && this.storeCode !== sseStoreCode) {
        sseCache.close()
      }
      if (!sseStoreCode || this.storeCode !== sseStoreCode) {
        sseStoreCode = this.storeCode
        await this._updateGoodsByTime()
        this._updateGoodsBySse()
      }
    } catch(err) {
      console.log(err)
    }
  }
  async _updateGoodsBySse() {
    try {
      // const url = await request.getExtraUrl(`${baseUrl.SSE}/sse?part=${this.storeCode}`)
      const url = `${baseUrl.SSE}/sse?part=${this.storeCode}`
      sseCache = new EventSource(url)
      sseCache.addEventListener('message', (event) => {

        if (event.data && event.data.data) {
          const { from, data } = event.data
          if (from === 'GOODS') {
            this.setNoMathGoodsToCache(data)
          }
        }
      })
      wx.onAppHide(function() {
        sseCache.close()
        sseStoreCode = ''
      });
    } catch(err) {
      getApp().monitor.report({
        message: 'fruitGoods _updateGoodsBySse err',
        errMsg: err
      })
      console.log(err)
    }
  }
  async _updateGoodsByTime() {
    try {
      const header = await FruitGoods.readCos.getCosHead(FruitGoods.readCosKey)
      const lastTime = header['Last-Modified']
      if (!lastTime) {
        return
      }
      const now = new Date();
      const res = await getApp().api.getGoodsListByTime({
        organizationCode: this.storeCode,
        beginTime: new Date(lastTime).Format('yyyy-MM-dd hh:mm:ss'),
        endTime: new Date(now.setSeconds(now.getSeconds() + 2)).Format('yyyy-MM-dd hh:mm:ss')
      })
      if (!res.data.length) {
        return
      }
      this.setNoMathGoodsToCache(res.data)
    } catch(err) {
      getApp().monitor.report({
        message: 'fruitGoods _updateGoodsByTime err',
        errMsg: err
      })
      console.log(err)
    }
  }
}

async function getPriceActivityGoodsRequest(options) {
  try {
    const res  = await getApp().api.getPriceActivityGoodsRequest(options)
    const{ goodsSnList } = res.data || {}
    return goodsSnList
  } catch(err){
    console.log(err)
    return []
  }
}
const atGoodsCache = {}
async function getPriceActivityGoods(options) {
  if (atGoodsCache[options.storeCode]) {

    return atGoodsCache[options.storeCode]
  }
  atGoodsCache[options.storeCode] = getPriceActivityGoodsRequest(options);
  return atGoodsCache[options.storeCode];
}
// 提取通用排序函数
const sortByUpdateTime = (arr) => 
  [...arr].sort((a, b) => {
    // 取外层默认商品的更新时间
    const { goodsObj:goodsObjA = {}} = getDefaultGoods(a)
    const { goodsObj:goodsObjB = {}} = getDefaultGoods(b)
    return  new Date(goodsObjB.updatedAt) - new Date(goodsObjA.updatedAt)
  })
// 添加节省金额文本的映射函数
const addSaveMoneyText = goods => ({
  ...goods,
  saveMoneyText: getSaveMoneyText(goods)
})
const classifyByStock = (list) => {
  const [sellList, sellOutList] = [[], []]
  list.forEach(item => (item.stockNum === 0 ? sellOutList : sellList).push(item))
  return [...sellList, ...sellOutList]
}
/**
 * @description 获取特价商品列表
 */
async function getOnlinePriceGoodsList({ 
  storeCode, 
  deliveryCenterCode, 
  cityCode, 
  isNeedMerge = true,
  isFliterNewGoods = false,
}) {
  const goodsSnList = await getPriceActivityGoods({ storeCode, deliveryCenterCode })
  const fruitGoods = new FruitGoods({
    storeCode,
    cityCode,
    deliveryCenterCode,
    isNeedMergeByGroup: isNeedMerge,
    isNeedMergeMultiSpec: isNeedMerge,
    filterCitySaleStatus: true,
    filterSaleStatus: true
  })
  try {
    let goodsList = await fruitGoods.getGoodsComplateInfoList(
      goodsSnList.map(item => {
        return { goodsSn: item, takeawayAttr: '及时达' }
      })
    )
    if (isFliterNewGoods) {
      goodsList = fliterNewPriceActGoods(goodsList)
    }
    // 第三阶段：商品分类与排序
    const categorized = goodsList.reduce((acc, item) => {
      const { goodsObj = {}} = getDefaultGoods(item)
      const subtype = goodsObj?.subtype
      const key = PRICE_TYPE_HANDLERS[subtype] || 'other'
      acc[key].push(item)
      return acc
    }, { newPriceList: [], priceList: [], storePriceList: [], other: [] })
    const orderedList = [
      ...sortByUpdateTime(categorized.storePriceList),
      ...sortByUpdateTime(categorized.newPriceList),
      ...sortByUpdateTime(categorized.priceList)
    ].map(addSaveMoneyText)
    
    return classifyByStock(orderedList)
  } catch(err) {
    console.log('getOnlinePriceGoodsList', err);
    return []
  }
}
module.exports = {
  FruitGoods,
  getPriceActivityGoods,
  getOnlinePriceGoodsList,
}