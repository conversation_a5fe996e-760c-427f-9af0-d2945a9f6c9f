/**
 * @description 及时达优惠券跳转
 */
function fruitCouponsHandle ({ coupon, app }) {
  const {
    couponWay,
    couponType,
    couponCode,
    defineId,
    channelSeparation,
    couponName,
    useableCondition = [],
    couponTypeStr,
    effectTimeStr,
  } = coupon
  let {
    couponValue,
    limitValue
  } = coupon
  if (couponWay === '5') { // 免运券跳转水果外卖
    wx.navigateTo({
      url: '/homeDelivery/pages/category/index'
    })
    return
  }
  // 是否显示核销码 (免运券不显示核销码 渠道来源只要是门店就展示核销码)
  const showQrcode = couponWay !== '5' && channelSeparation.indexOf('P') >= 0
  // 是否显示商品  (小程序可用的展示商品)
  const showGoods = channelSeparation.indexOf('X') >= 0

  // 如果需要展示核销码 则需要动态拼接折扣信息+门店可用信息
  if (showQrcode) {
    // couponWay：1.满减，3.立减，2.满折，4.立折，5.免运
    let limitStr= ''
    limitValue = Number((limitValue/100).toFixed(2)) || 0
    if(['1', '3'].includes(couponWay)) {
      couponValue = Number((couponValue/100).toFixed(2)) || 0
      limitStr = `满${limitValue}元可减${couponValue}元`
    } else if (['2', '4'].includes(couponWay)) {
      couponValue = Number((couponValue/10).toFixed(1)) || 0
      limitStr = `${couponValue}折，满${limitValue}元可用`
    }

    // 使用说明 ()
    if (limitStr) {
      useableCondition.unshift(limitStr)
    }
    if (couponTypeStr) useableCondition.push(couponTypeStr)
  }

  const couponObj = {
    ...coupon,
    defineId,
    couponCode,
    couponType,
    showQrcode,
    showGoods,
    couponName,
    useableCondition,
    effectTimeStr //生效标签
  }
  app.globalData.fruitCouponGoodsParams = couponObj
  wx.navigateTo({
    url: `/userA/pages/couponGoods/index`
  })
}

/**
 * @description 次日达优惠券跳转
 */
 function freshCouponsHandle ({ coupon }) {
  const {
    couponWay,
    couponCode,
    defineId
  } = coupon
  // 除免运券外，其他券调适用商品页面
  if (couponWay !== '5') {
    wx.navigateTo({
      url: `/bgxxShop/pages/couponGoods/index?defineId=${defineId}&couponCode=${couponCode}`
    })
    return
  }
  // 精选食材其它类型券跳转首页
  wx.navigateTo({
    url: '/pages/xxshop/index/index'
  })
}

/**
 * @description 优惠券跳转处理
 */
export default function toUseCoupon({ coupon, app, couponSource }) {
  // 及时达优惠券跳转
  if (couponSource === 'fruit') {
    fruitCouponsHandle({ coupon, app })
    return
  }
  // 次日达优惠券跳转
  if (couponSource === 'fresh') {
    freshCouponsHandle({ coupon })
    return
  }
}
