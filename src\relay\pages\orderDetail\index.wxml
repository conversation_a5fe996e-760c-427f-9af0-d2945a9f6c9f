<!-- 导入支付方式模板 -->
<import src="/pages/template/index.wxml" />
<!-- 导入支付方式模板 -->
<!-- 组件模板 -->
<import src="./template/index.wxml" />
<!-- 组件模板 -->
<!-- 工具类方法 -->
<wxs module="filter" src="../../../utils/common.wxs"></wxs>
<main-page currentView="content" />

<nav-bar
  normalBack
  background="{{ navBarBgColor }}"
  color="{{ navBarColor }}"
  navBarTitle="订单详情"
  bindnavBarHeight="getNavBarHeight"
  style="position: absolute"
>
  <view slot="back" class="nav-back" style="filter: brightness({{ backFilter }})">
    <image src="/source/images/arrow_back.png" />
  </view>
</nav-bar>

<view class="order-container">
    <view class="header-section">
        <!-- 占位导航栏高度 -->
        <view class="header__padding" style="height: {{ navBarHeight }}px;"></view>
        <!-- 占位导航栏高度 -->

        <view class="header__main">
            <!-- 订单状态图标 -->
            <view class="header__status">
                <view class="header__status-icon header__status-icon--{{statusClassName}}"></view>
                {{ statusTitle }}
            </view>
            <!-- 订单状态图标 -->

            <!-- 订单状态文本 -->
            <view class="header__desc">
                {{ statusDesc }}
            </view>
            <!-- 订单状态文本 -->
        </view>
    </view>

    <!-- 收件人/配送信息区域 -->
    <view class="receive-section section {{ needBorderRadius ? '' : 'border-radius-none' }}">
      <!-- 提示内容区域 -->
      <view>
        <view wx:if="{{ !isCancelStatus && !noReasonRefundDesc && !differenceRefundMoney }}" class="receive-tip">
            助力环保，人人有责，提货请自备购物袋或到店购买哦~
        </view>

        <!-- 差额退款提示 -->
        <view wx:if="{{ differenceRefundMoney }}" class="receive-tip receive-tip--green">
            <span class="receive-tip__label">
                差额退款：
            </span>
            <span class="receive-tip__content">
                {{ differenceRefundMoney }}
            </span>
        </view>
        <!-- 差额退款提示 -->

        <!-- 三无退货提示已去掉 -->
      </view>
      <!-- 提示内容区域 -->

      <view class="receive__list">
        <view wx:for="{{ receiveList }}" wx:key="index" wx:for-item="receiveItem" class="list-item {{ index < 2 ? 'list-item--paddingRight' : '' }}">
          <!-- 自提门店 -->
          <view wx:if="{{ receiveItem === '自提门店' }}">
              <view class="list-item__left">
                  自提门店
              </view>

              <view class="list-item__right right-padding">
                  <view class="list-item__right-title row-ellipsis">
                      {{ store.alias_name || store.store_name }}
                  </view>

                  <view class="list-item__right-content row-ellipsis">
                    {{ storeAddress }}
                  </view>
              </view>
          </view>
          <!-- 自提门店 -->

          <!-- 营业时间 -->
          <view wx:if="{{ receiveItem === '营业时间' }}">
              <view class="list-item__left">
                  营业时间
              </view>

              <view class="list-item__right right-padding">
                  <view class="list-item__right-title row-ellipsis">
                      {{ store.storeBusinessTime }}
                  </view>
              </view>
          </view>
          <!-- 营业时间 -->

          <!-- 提货时间 -->
          <view wx:if="{{ receiveItem === '提货时间' }}">
              <view class="list-item__left">
                  提货时间
              </view>

              <view class="list-item__right right-padding">
                  <view class="list-item__right-title row-ellipsis">
                      <block wx:if="{{ store.startDispatchTime === store.endDispatchTime }}">
                        {{ store.startDispatchTime }}
                      </block>
                      <block wx:else>
                        {{ store.startDispatchTime }} - {{ store.endDispatchTime }}
                      </block>
                  </view>
              </view>
          </view>
          <!-- 提货时间 -->

          <template is="icon-list" data="{{ index, receiveList, isClosed: store.isClosed }}"></template>
        </view>
      </view>
    </view>
    <!-- 收件人/配送信息区域 -->

    <!-- 提货码 -->
    <view wx:if="{{ isWaitingCollectionStatus }}" class="scan-section section">
      <view class="scan-section__desc">
        *请于计划时间内到 <span>{{ store.alias_name || store.store_name }}</span> 提货
      </view>
      <image class="scan-section__code" wx:if="{{ barcodeImgSrc }}" src="{{ barcodeImgSrc }}"></image>
      <view class="scan-section__number">
          <span>提货码：</span>
          <span>{{ filter.formatTakeCode(orderDetail.pickUpCode) }}</span>
      </view>
    </view>
    <!-- 提货码 -->

    <!-- 详情商品-->
    <!-- 未拆单的拆单商品列表 -->
    <template wx:if="{{ mixGroupGoodsList && mixGroupGoodsList.length }}" is="mix-goods-list" data="{{ mixGroupGoodsList }}"></template>
    <!-- 拆单后的商品列表 -->
    <template wx:else is="goods-list" data="{{ goodsList }}"></template>
    <!-- 详情商品-->

    <!-- 金额优惠及减免信息列表 -->
    <view class="price-section section">
      <view class="price-row">
        <text>商品金额</text>
        <text>￥{{ filter.formatPrice(orderDetail.totalPrice) }}</text>
      </view>

      <!-- 实付金额 -->
      <view class="price-row--total">
        <text class="total-label">实付总额：</text>
          <text class="total-value">￥<text class="total-value__big">{{ filter.formatPrice(orderDetail.totalPrice) }}</text>
        </text>
      </view>
      <!-- 实付金额 -->
    </view>

    <!-- 金额优惠及减免信息列表 -->
    <view class="pay-mode-box" wx:if="{{ statusTitle && statusTitle !== '已取消' && statusTitle !== '交易成功' && statusTitle !== '待付款'}}">
      <text class="pay-mode-text" wx:if="{{ noInvoiceDesc }}">{{ noInvoiceDesc }}</text>
      <text class="pay-mode-text" wx:elif="{{store && store.isSupportInvoice === 'N'}}">*该门店无法开具电子发票，可联系门店开具纸质发票</text>
      <view wx:else>
          <text class="pay-mode-text">*自提后可在订单详情页中开具电子发票</text>
      </view>
    </view>

    <view class="info-section section">
      <view class="info-section__header">
        订单信息
      </view>

      <view class="info-row">
        <view class="info-row__label">订单编号</view>
        <view class="info-row__content">
            <span>
                {{ orderDetail.orderNo }}
            </span>
            <span class="copy-btn" bindtap="copyOrderNo">复制</span>
        </view>
      </view>

      <block wx:for="{{ timeLineList }}" wx:key="timeLine" wx:for-item="timeLine">
        <view wx:if="{{timeLine.field === 'createTime'}}" class="info-row">
          <view class="info-row__label">创建时间</view>
          <view class="info-row__content">
              {{ filter.formatDateYMD(timeLine.value) }}
          </view>
        </view>

        <view wx:if="{{timeLine.field === 'payTime'}}" class="info-row">
          <view class="info-row__label">付款时间</view>
          <view class="info-row__content">
              {{ filter.formatDateYMD(timeLine.value) }}
          </view>
        </view>

        <view wx:if="{{timeLine.field === 'dispatchTime'}}" class="info-row">
          <view class="info-row__label">提货时间</view>
          <view class="info-row__content">
              {{ filter.formatDateYMD(timeLine.value) }}
          </view>
        </view>

        <view wx:if="{{timeLine.field === 'cancelTime'}}" class="info-row">
          <view class="info-row__label">取消时间</view>
          <view class="info-row__content">
              {{ filter.formatDateYMD(timeLine.value) }}
          </view>
        </view>

      </block>
      <view wx:if="{{exceedRefundTimeDesc}}" class="info-row">
        <view class="info-row__label">售后状态</view>
        <view class="info-row__content">
            <span>{{exceedRefundTimeDesc}}</span>
        </view>
      </view>
    </view>

    <!-- 详情底部 -->
    <view wx:if="{{ hasFooter }}" class="detail-footer safe-area-inset-bottom">
        <!-- 更多按钮组件 -->
        <order-detail-more-btn
            wx:if="{{ showBtn.leftBtnList }}"
            menuList="{{ leftBtnList }}"
        >
        </order-detail-more-btn>
        <!-- 更多按钮组件 -->

        <view wx:if="{{ showBtn.isCanInvoice }}" class="detail-footer__btn" bindtap="invoiceOrder">
            开具发票
        </view>

        <view wx:if="{{ showBtn.isMiniCanShowInvoice }}" class="detail-footer__btn" bindtap="invoiceDetailBtnHandle">
            发票详情
        </view>

        <!-- 待付款、待自提均能展示 -->
        <view wx:if="{{ showBtn.cancelOrder }}" class="detail-footer__btn" bindtap="cancelOrder">
            取消订单
        </view>
        <!-- 待付款、待自提均能展示 -->

        <view wx:if="{{ showBtn.payOrder }}" class="detail-footer__btn detail-footer__btn--pay" bindtap="payOrder">
            立即付款
        </view>

        <!-- 待自提 展示邀请好友下单 -->
        <button wx:if="{{isWaitingCollectionStatus&&sharePic}}" plain hover-class="none"  open-type="share" class="detail-footer__btn detail-footer__btn--pay invite-width">
            邀请好友下单
        </button>
    </view>
    <!-- 详情底部 -->

</view>

<!-- 支付方式选择框 -->
<paymentDialog
  paymentDialog="{{paymentDialog}}"
  target="{{target}}"
  selectPagodaPay="{{pagodaMoney}}"
  selectWxPay="{{selectWxPay}}"
  selectUnionPay="{{selectUnionPay}}"
  mainBalance="{{mainBalance}}"
  rechargeText="{{rechargeText}}"
  mainBalanceIsNotEnough="{{lack}}"
  forbidPdPay="{{false}}"
  bind:hidePaymentWay="hidePaymentWay"
  bind:setWaySelect="setWaySelectHandle"
  bind:switchPagodaPayChange="switchPagodaPayChangeHandle"
  bind:payrightNow="payrightNow">
</paymentDialog>
<!-- 支付方式选择框 -->

<!--取消订单原因弹窗-->
<order-cancel wx:if="{{orderCancelIsShow}}" orderSource="wxMiniOrder" bind:select="onOrderCancelSelected" bind:close="onOrderCancelClosed"></order-cancel>
<!--取消订单原因弹窗-->

<!-- 条形码canvas -->
<canvas canvas-id="canvasbarcode"></canvas>

<!-- 分享图 -->
<canvas class="canvas" class="canvas share-pic" canvas-id="sharePicCanvas"></canvas>

<confirm-modal
  titleText="{{modalContent.title}}"
  contentText="{{modalContent.text}}"
  isShowConfirmModal="{{ showCustomConfirmModal }}"
  showCustomBtn="{{true}}">
  <button size="mini" plain="true" slot="leftBtn" class="confirm-btn" bindtap="customConfirmModalCancel">取消</button>
  <button size="mini" plain="true" slot="rightBtn" class="confirm-btn" bindtap="handleOnlineService">在线客服</button>
</confirm-modal>

<!-- 退款原因弹窗 -->
<reason-confirm-modal
  wx:if="{{ visibleReasonConfirmModal }}"
  popupTitle="请问您遇到了什么问题"
  desc="很抱歉给您带来不好的体验"
  confirmText="去三无退货"
  showModal="{{ showReasonConfirmModal }}"
  reasonType="{{ 150 }}"
  bindconfirm="reasonConfirmModalSubmit"
  bindclose="closeReasonConfirmModal"
  bindreasonLoaded="reasonLoaded"
/>

<!-- 验证码弹窗组件 -->
<pagoda-popup
  model:visible="{{visibleSMS}}"
  showLeftArrow="{{true}}"
  showClose="{{false}}"
  round="{{true}}"
  z-index="{{2000}}"
  clickOverlayClose="{{false}}"
  position="bottom"
  title="更换支付方式"
  head-class="sms-head-class"
  title-class="sms-title-class"
  height="600rpx"
  bind:onBack="onBack">
    <sms-validate
      model:visible="{{visibleSMS}}"
      bind:validated="validated"
    />
</pagoda-popup>

<common-loading />
<captcha id="comp-captcha"/>
