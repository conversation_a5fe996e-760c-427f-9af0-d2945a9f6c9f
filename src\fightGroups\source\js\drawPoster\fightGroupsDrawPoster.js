/**
 * 说明：
 * 1. 该js用于【拼团详情页、拼团支付成功页、拼团订单详情页、拼团订单列表页】的canvas绘制分享海报的图片，海报样式统一
 * 2. canvas绘制过程中出错会抛出异常，可被trycatch捕获
 */
const app = getApp()
const commonObj = require('../../../../source/js/common').commonObj
const { fillText, measureText, roundRectImg, strokeRoundRect, fillRoundRect } = require('../../../../utils/services/2DCanvasUtil')

const bgPath = 'https://resource.pagoda.com.cn/group1/M21/64/FB/CmiLkGF3qwyAMg--AABeekI83dE953.png'; // 海报背景图
const avatarDefault = 'https://resource.pagoda.com.cn/group1/M21/54/64/CmiLkGD5TyKASB85AAAn3XJoLPU505.png' // 用户默认头像
const canvasWidth = 750 // 画布的宽度
const canvasHeight = 1334 // 画布的高度
const posterImage = {}

/**
 * @description 计算分享海报弹窗的宽高，分享海报缩略图的宽高
 */
const getPosterModelSize = function () {
  return new Promise((resolve, reject) => {
    wx.getSystemInfo({
      success: function (res) {
        // canvas绘制的海报高宽比（高：1334，宽：750）
        const posterRatio = canvasHeight / canvasWidth
        // 弹窗显示海报的缩略图的缩小倍数
        const scale = 0.5
        // 海报的宽度 = 屏幕宽度
        const posterWidth = res.screenWidth * 2
        // 海报的高度 = 海报的宽度 x 海报高宽比
        const posterHeight = parseInt(posterWidth * posterRatio);
        // 海报缩略图的宽度
        const smallPosterWidth = parseInt(posterWidth * scale);
        // 海报缩略图的高度
        const smallPosterHeight = parseInt(posterHeight * scale);
        // 模态框的宽度 = 屏幕宽度
        const modelWidth = res.screenWidth * 2;
        // 模态框的高度 = 模态框的宽度的 1.4倍（是UI决定的）
        const modelHeight = parseInt(modelWidth * 1.40);
        resolve ({
          modelWidth,
          modelHeight,
          smallPosterWidth,
          smallPosterHeight
        })
      }
    });
  })
}

/**
 * @description 获取生成海报的图片的本地路径
 * @param { headPic, sceneUrl } headPic 商品头图 sceneUrl 小程序码解释页面链接的参数
 */
const getPosterImagePath = function ({headPic, sceneUrl, isLogin}) {
  return new Promise((resolve, reject) => {
    const { avatarUrl } = wx.getStorageSync('userNameAndImg') || {}
    const avatarPath = isLogin ? avatarUrl || avatarDefault : avatarDefault
    // 生成二维码
    const p1 = getCode(sceneUrl)
    // 获取商品头图
    const p2 = getImageInfo(headPic, 'headPicPath')
    // 获取海报背景图
    const p3 = getImageInfo(bgPath, 'bgImagePath')
    // 获取用户头像
    const p4 = getImageInfo(avatarPath, 'avatarPath')

    const p = [p1, p2, p3, p4]
    Promise.all(p).then(res => {
      resolve()
    }).catch((err) => {
      reject()
    })
  })
}

/**
 * @description 生成小程序码
 * @param sceneUrl 小程序码解释页面链接的参数
 */
const getCode = function (sceneUrl) {
  return new Promise((resolve, reject) => {
    const pageUrl = 'pages@homeDelivery@index'
    const widthUrl = 200
    const isHyalineUrl = false
    const codeUrl = `${commonObj.PAGODA_DSN_DOMAIN}/api/v1/wechat/wxa/wxacodeunlimit/get/${sceneUrl}/${pageUrl}/${widthUrl}/${isHyalineUrl}`
    wx.downloadFile({
      url: codeUrl,
      success: res => {
        storageImage(res.tempFilePath, 'codeImagePath')
        resolve()
      },
      fail: res => reject()
    })
  })
}

/**
 * @description 获取图片的本地路径
 * @param netUrl 图片地址
 * @param storageKeyUrl 图片存储字段名
 */
const getImageInfo = function (netUrl, storageKeyUrl) {
  return new Promise((resolve, reject) => {
    wx.getImageInfo({
      src: netUrl,    //请求的网络图片路径
      success: function (res) {
        //请求成功后将会生成一个本地路径即res.path,然后将该路径缓存到storageKeyUrl关键字中
        storageImage(res.path, storageKeyUrl)
        resolve()
      },
      fail: res => reject()
    })
  })
}

/**
 * @description 将获取到的图片的本地路径存储到 posterImage
 * @param path 图片的本地路径
 * @param key 图片存储字段名
 */
const storageImage = function (path, key) {
  Object.assign(posterImage, { [key]: path })
}

/**
 * @description canvas绘制分享海报的图片
 * @param goodsObj 商品对象
 */
const drawPosterPic = function (canvasId, goodsObj) {
  return new Promise(async (resolve, reject) => {
    const isLogin = app.checkSignInsStatus() || false
    const { nickName } = wx.getStorageSync('userNameAndImg') || {}
    const { headPic, goodsName, groupSize, goodsWeight, groupPrice, originPrice, sceneUrl } = goodsObj
    const refundIcon = '/source/images/poster_refund_icon.png' // 三无退货图标
    const codeText = '长按图片去购买'
    const refundText = '所有商品不好就退'
    const marginLeft = 28

    try {
      // 获取图片资源
      await getPosterImagePath({headPic, sceneUrl, isLogin})
      const { bgImagePath, headPicPath, codeImagePath, avatarPath } = posterImage || {}
      const canvasCtx = wx.createCanvasContext(canvasId);
      canvasCtx.scale(2, 2);
      // 绘制背景图
      canvasCtx.drawImage(bgImagePath, 0, 0, 375, 667);
      // 绘制商品图片
      canvasCtx.drawImage(headPicPath, marginLeft, 94.5, 319, 319);
      // 绘制头像
      roundRectImg(canvasCtx, avatarPath, 22, 14.5, 50, 50, 25, null, '#FFFFFF');
      // 绘制昵称
      const nickNameText = isLogin ? nickName || '果宝宝' : '果宝宝'
      fillText(canvasCtx, {
        text: nickNameText,
        x: 79,
        y: 21,
        fontSize: '17px',
        fontWeight: 'bold',
        fillStyle: '#FFFFFF',
        textBaseline: 'top',
        width: 180,
        MaxLineNumber: 1
      })
      // 绘制推荐文案
      fillText(canvasCtx, {
        text: '"强烈推荐！快来看看"',
        x: 79,
        y: 44,
        fontSize: '13px',
        fillStyle: '#FFFFFF',
        textBaseline: 'top'
      })
      // 绘制商品标题
      const maxWidth = 319
      let fillTop = 420
      fillTop = fillText(canvasCtx, {
        text: goodsName,
        x: marginLeft,
        y: fillTop,
        width: maxWidth,
        MaxLineNumber: 2,
        fontSize: '17px',
        fontWeight: 'bold',
        fillStyle: '#222222',
        lineHeight: 23,
        textBaseline: 'top'
      })
      fillTop += 23 + 5 // 23 字体行高
      //绘制重量
      fillText(canvasCtx, {
        text: goodsWeight,
        x: marginLeft,
        y: fillTop,
        width: maxWidth,
        MaxLineNumber: 1,
        fontSize: '12px',
        fillStyle: '#999999',
        textBaseline: 'top'
      })
      fillTop += 16 + 5 // 16 行高
      // 绘制团人数
      let groupSizeX = 36
      const groupSizeY = fillTop + 2
      const cornerRadius = { bottomRight: 0, bottomLeft: 3, topLeft: 3, topRight: 0 } // 圆角矩形的四个圆角的半径
      const rectWidth1 = measureText(canvasCtx, groupSize, '14px') + 8 * 2 // 8 是“x人团”中“x”的左右边距
      const rectWidth2 = rectWidth1 + 36 // 36 是“人团”占的宽度
      strokeRoundRect(canvasCtx, marginLeft, fillTop, rectWidth2, 20, 3, 1, '#FF7387')
      fillRoundRect(canvasCtx, marginLeft, fillTop, rectWidth1, 20, 3, '#FF7387', cornerRadius)
      fillText(canvasCtx, {
        text: groupSize,
        x: groupSizeX,
        y: groupSizeY,
        fontSize: '14px',
        fillStyle: '#FFFFFF',
        textBaseline: 'top'
      })
      groupSizeX += measureText(canvasCtx, groupSize, '14px') + 12
      fillText(canvasCtx, {
        text: '人团',
        x: groupSizeX,
        y: groupSizeY,
        fontSize: '14px',
        fillStyle: '#FF7387',
        textBaseline: 'top'
      })
      // 绘制第一个价格：拼团价
      let priceTextX = marginLeft
      const priceTextY = 552
      fillText(canvasCtx, {
        text: '¥',
        x: priceTextX,
        y: priceTextY - 3,
        fontSize: '18px',
        fontWeight: 'bold',
        fillStyle: '#FF7387',
        textBaseline: 'bottom'
      })
      priceTextX += 11
      fillText(canvasCtx, {
        text: groupPrice,
        x: priceTextX,
        y: priceTextY,
        fontSize: '28px',
        fontWeight: 'bold',
        fillStyle: '#FF7387',
        textBaseline: 'bottom'
      })
      priceTextX += measureText(canvasCtx, groupPrice, '28px') + 5.5
      // 绘制第二个价格：划线价
      if (originPrice > 0) {
        // 划线价（原售价）
        fillText(canvasCtx, {
          text: `¥${originPrice}`,
          x: priceTextX,
          y: priceTextY - 4,
          fontSize: '15px',
          fillStyle: '#888888',
          lineThrough: true,
          textBaseline: 'bottom'
        })
      }
      // 三无退货
      canvasCtx.drawImage(refundIcon, marginLeft, 554, 16, 16);
      fillText(canvasCtx, {
        text: refundText,
        x: 48,
        y: 557,
        fontSize: '11px',
        fillStyle: '#222222',
        textBaseline: 'top'
      })
      // 绘制小程序码
      canvasCtx.drawImage(codeImagePath, 254.5, 500, 86, 86);
      // 绘制codeText字
      fillText(canvasCtx, {
        text: codeText,
        x: 259,
        y: 602,
        fontSize: '11px',
        fillStyle: '#858996',
        textBaseline: 'bottom'
      })
      canvasCtx.draw()

      // canvas 画布转为图片
      setTimeout(function () {
        wx.canvasToTempFilePath({
          x: 0,
          y: 0,
          width: canvasWidth,
          height: canvasHeight,
          quality: 1,
          destWidth: canvasWidth,
          destHeight: canvasHeight,
          canvasId: canvasId,
          // success: function (res) {
          //   console.log('drawposter',res.tempFilePath)
          //   resolve(res.tempFilePath)
          // },
          // fail: function (res) {
          //   reject()
          // }
          success: function (res) {
            if (!wx.showShareImageMenu) {
              resolve(res.tempFilePath)
            } else {
              wx.showShareImageMenu({
                path:res.tempFilePath,
                success(res) {
                },
                fail(err) {
                  let {errMsg} = err
                  if(errMsg !=="showShareImageMenu:fail cancel"){
                    commonObj.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了', '', function (res) {
                      wx.openSetting()
                  })
                  }
                },
              })
            }
            wx.hideLoading()
          },
          fail: function (res) {
            reject()
            wx.hideLoading()
          }
        })
      }, 300);
    } catch (error) {
      reject()
    }
  })
}

module.exports = {
  drawPosterPic,
  getPosterModelSize
}
