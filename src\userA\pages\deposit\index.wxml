<wxs module="common" src="../../../utils/common.wxs"></wxs>
<view class="container recharge">
  <!-- 冻结提示信息 -->
  <view class="freeze-tip" wx:if="{{ lockingMainBalance + lockingFruitCoinBalance > 0 }}" catchtap="goWithdrawPage">
    <view>提现处理中，已冻结金额{{ common.formatAmount(lockingMainBalance + lockingFruitCoinBalance) }}元</view>
    <view class="arrow-box"><text>详情</text><text class="arrow-right"></text></view>
  </view>
  <!--我的余额-->
  <view class="balance">
    <!-- <view class="invoice" bind:tap="openInvoice">
      <text>开具电子发票</text>
      <image src='/source/images/btn_sign_arrow.png' style='width:12rpx; height:20rpx;margin-left:8rpx;'></image>
    </view> -->
    <view class="bg-wrapper">
      <image mode="widthFix" src="https://resource.pagoda.com.cn/group1/M21/55/E3/CmiWa2EI6LCAIow8AADxerIbuUg073.png" class="balanceImage"></image>
    </view>
    <view class="balance-content">
      {{common.formatAmount(balance)}}<text class="yen">元</text>
    </view>
    <view class="balanceDetail" bindtap="alertBalanceInfo">
      <image  src="/userA/source/image/icon_tips.png" class="balanceInfoImage"></image>
      <text>其中含果币 {{common.formatAmount(fruitCoinBalance)}}元</text>
    </view>
    <view class="view-botton-box">
      <view class="view-more" bind:tap="openInvoice">
        <text>开具电子发票</text>
        <image src='/source/images/btn_sign_arrow.png' style='width:12rpx; height:20rpx;margin-left:8rpx;'></image>
      </view>
      <navigator class='view-more' url="../baldetail/index" bindtap="checkDetailInfo" hover-class="none">
        <text>查看交易明细</text>
          <image src='/source/images/btn_sign_arrow.png'></image>
      </navigator>
    </view>

  </view>
  <!-- tab页 -->
  <view class="anchor">
    <view class="{{currentTabIndex === 0 ? 'tab on' : 'tab'}}" bindtap="handleClickTab" data-index="0">在线充值</view>
    <view class="{{currentTabIndex === 1 ? 'tab on' : 'tab'}}" bindtap="handleClickTab" data-index="1">好吃卡</view>
    <view class="{{currentTabIndex === 2 ? 'tab on' : 'tab'}}" bindtap="handleClickTab" data-index="2">代金券</view>
  </view>
  <!-- swiper滑动部分 -->
  <swiper class="recharge-swiper" current='{{currentTabIndex}}' bindchange="handleSwiperChange" style="height: {{swiperHeight}}px" bindanimationfinish="handleSwiperChanged" bindtransition="handleTransition">
    <swiper-item>
      <!--优惠活动列表-->
      <view id="swiper0">
        <!-- 充值组件 -->
        <recharge-activity id="rechargeActivity" use-default-address="{{ useDefaultAddress }}" onhandleSwiperChanged="handleSwiperChanged" onhandleCanRecharge="handleCanRecharge" onhandleActivityLoaded="handleActivityLoaded" oncustomizeFocus="handleCustomizeFocus"></recharge-activity>

        <!-- 规则说明 广告位 -->
        <block wx:if="{{rechargeRules && hasActivity}}">
          <!-- 充值送券广告位 -->
          <view class="recharge-ad">
            <recharge-ad id="rechargeAd" />
          </view>
          <!-- 充值送券广告位 -->

          <view class="gap-box"></view>
          <view class="charge-tip">
            <view class="charge-tip-title">充值说明</view>
            <text class="charge-tip-content safe-area-inset-bottom">{{ rechargeRules }}
            </text>
          </view>
        </block>
      </view>
    </swiper-item>
    <swiper-item>
      <view id="swiper1">
        <view class="recharge-box">
          <view class="recharge-row">
            <input type='number' class='recharge-input' placeholder="请输入12位卡号" maxlength="14" value="{{cardNo}}" bindinput="watchCardNo" />
            <image class="clear-circle-btn offset-left-icon {{cardNo ? '' : 'clear-circle-btn-hidden'}}" src="/source/images/icon-delete.png" data-icon="cardNo" catchtap="clearBtnClick"></image>
            <image class="ipt-suffix" src="https://resource.pagoda.com.cn/group1/M21/8C/2D/CmiWa2Jo5kaAfK8FAAAFq3jBbsc411.png" mode="widthFix" catchtap="handleClickCamera" data-icon="cardNo" />
          </view>
          <view class="recharge-row">
            <input type='number' class='recharge-input' placeholder="请输入6位密码" maxlength="6" value="{{cardPwd}}" bindinput="watchCardPwd" password />
            <image class="clear-circle-btn {{cardPwd ? '' : 'clear-circle-btn-hidden'}}" src="/source/images/icon-delete.png" data-icon="cardPwd" catchtap="clearBtnClick"></image>
          </view>
        </view>
        <template is="recharge-card" data="{{rechargeCardRules}}"></template>
      </view>
    </swiper-item>
    <swiper-item>
      <view id="swiper2">
        <view class="recharge-box">
          <view class="recharge-row">
            <input type='text' class='recharge-input' placeholder="请输入18位或21位券号" maxlength="26" value="{{couponNo}}" bindinput="watchCouponNo" />
            <image class="clear-circle-btn offset-left-icon {{couponNo ? '' : 'clear-circle-btn-hidden'}}" src="/source/images/icon-delete.png" data-icon="couponNo" catchtap="clearBtnClick"></image>
            <image class="ipt-suffix" src="https://resource.pagoda.com.cn/group1/M21/8C/2D/CmiWa2Jo5kaAfK8FAAAFq3jBbsc411.png" mode="widthFix" catchtap="handleClickCamera" data-icon="couponNo" />
          </view>
        </view>
        <template is="recharge-coupon" data="{{rechargeCouponRules}}"></template>
      </view>
    </swiper-item>
  </swiper>
  <!-- 底部充值按钮 -->
  <view wx:if="{{ (currentTabIndex === 0 && !useDefaultAddress && hasActivity) || currentTabIndex !== 0}}" class="charge-confirm-box safe-area-inset-bottom {{ customizeFocus ? 'static-box' : '' }}">
    <view class="charge-confirm">
      <view class="title">
        <radio-check checked="{{tick}}" catchtap="handleTick"></radio-check>
        <view class="agree">
          <text catchtap="handleTick">我已阅读并同意</text>
          <text wx:if="{{currentTabIndex === 0}}" catchtap="checkConstitution" data-type="prepaidCard" class="protocol-style">《百果园预付卡管理章程》和</text>
          <text catchtap="checkConstitution" data-type="userRechargeRegulation" class="protocol-style">《充值服务个人信息授权声明》</text>
        </view>
      </view>
      <!-- 充值按钮 -->
      <!-- 在线充值 -->
      <view wx:if="{{currentTabIndex === 0}}" class="button {{tick && canRecharge ? 'button-active' : ''}}" catchtap="goRecharge">立即充值</view>
      <!-- 好吃卡充值 -->
      <view wx:if="{{currentTabIndex === 1}}" class="button {{tick && isEnableCardBtn ? 'button-active' : ''}}" catchtap="goRechargeCard">立即充值</view>
      <!-- 代金券充值 -->
      <view wx:if="{{currentTabIndex === 2}}" class="button {{tick && isEnableCouponBtn ? 'button-active' : ''}}" catchtap="goRechargeCoupon">立即充值</view>
    </view>
  </view>

  <!-- <view class='model-mask' wx:if="{{showBalanceMask}}" >
    <view class='model-box' animation="{{animationData}}">
      <view class='model-title'>
        <text>提醒</text>
      </view>
      <view class='icon-close'>
        <image class='icon-close-image' bindtap='colseMask' src='/source/images/icon_close.png'></image>
      </view>
      <view class='model-content'>
        <text>1、果币是充值赠送的金额，可用于下单消费</text>
        <text>2、下单默认先消费果币 </text>
        <text>3、果币消费的金额不计积分，不开发票</text>
      </view>
      <view class='model-btn' bindtap='colseMask'><text>知道了</text></view>
    </view>
  </view> -->

</view>
<!-- 好吃卡充值描述 -->
<template name="recharge-card">
  <view class="active-box">
    <view class="charge-tip">
      <view wx:if="{{rechargeCardRules}}" class="charge-tip-title">充值说明</view>
      <text space="nbsp" class="charge-tip-content safe-area-inset-bottom">{{ rechargeCardRules }}
      </text>
    </view>
  </view>
</template>
<!-- 优惠券充值描述 -->
<template name="recharge-coupon">
  <view class="active-box">
    <view class="charge-tip">
      <view wx:if="{{rechargeCouponRules}}" class="charge-tip-title">充值说明</view>
      <text class="charge-tip-content safe-area-inset-bottom">{{ rechargeCouponRules }}
      </text>
    </view>
  </view>
</template>
<view class="prevent-screen" hidden="{{!prevent}}"></view>

<view class="net-error" wx:if="{{isNetError}}">
  <view class="disconnect-bg">
    <image src="/userA/source/image/home_disconnection_defaultpag.png" />
  </view>
  <view class="disconnect-text">
    <text>嗷~，网络出问题了</text>
    <text>请重新进入页面</text>
  </view>
</view>

<user-error-modal isShow="{{showUserErrorModal}}"></user-error-modal>
<pagoda-popup model:visible="{{showBalanceMask}}" title="果币说明">
      <view class='model-content' catch:touchmove="stopmove">
      <block wx:if="{{fruitCoinDescription.length}}">
        <text wx:for="{{fruitCoinDescription}}" wx:key="index">{{index+1}}、{{item}}</text>
      </block>
      <block wx:else>
        <text>1、果币是充值赠送的金额，可用于下单消费</text>
        <text>2、下单默认先消费果币 </text>
        <text>3、果币消费的金额不计积分，不开发票</text>
      </block>
      </view>
      <view class='model-btn' bindtap='colseMask'><text>我知道了</text></view>
</pagoda-popup>

<!-- 确认模态对话框 -->
<confirm-modal
  confirmText="我知道了"
  showCancel="{{ false }}"
  isShowConfirmModal="{{ showModal }}"
  bindconfirm="handleConfirm">
  <view slot="content">
    <view wx:if="{{ modelContent }}">
      {{ modelContent }}
    </view>
    <view wx:if="{{ modelDescContent }}">
      {{ modelDescContent }}
    </view>
  </view>
</confirm-modal>
<!-- 确认模态对话框 -->

<common-loading />