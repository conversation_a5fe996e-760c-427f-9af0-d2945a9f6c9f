<wxs module="common" src="../../../../utils/common.wxs"></wxs>

<view>
  <!-- 模板2 -->
  <view class="goods-template-3" wx:if="{{templateType === '3'}}">
    <view class="goods-name text-hidden">
    {{ common.goodsLevelFormat(goodsObj.goodsName) }}
    </view>
    <view class="goods-spec text-hidden">
    {{ common.goodsLevelFormat(goodsObj.specDesc) }}

    </view>
    <view class="goods-price">
      <goods-price goodsObj="{{goodsObj}}" showMinPriceTip="{{showMinPriceTip}}" customClassName="subject3" direction="horizontal"></goods-price>
      <fruit-goods-price goodsObj="{{goodsObj}}" currentMode="SUBJECT_PIC_GOODS_THREE" />
    </view>
  </view>
</view>