const app = getApp()
const util = require('../../../../utils/util')
const { protocolUrl } = require('../../../../utils/config')
//  商品图片常量
const GOODSIMAGE = require('../../../../source/const/goodsImage')
const { checkSensitiveWord } = require('../../../../utils/services/tools')
const sensors = require('../../../../utils/report/sensors')
const { createInvoiceSubmitTemplateIds, reportTmplIds } = require('../../../../mixins/ticketEntry')
const { ORDER_TYPE } = require('~/source/const/order')

/**
 * 抬头类型枚举
 */
const TITLE_ENUM = {
  company: '2',
  single: '1'
}

/**百果园实业税号 */
const BGY_TAXCODE = '914403007152447549'

/**
 * @typedef TSubmitData 提交数据模型
 * @property { string } orderType 订单类型
 * @property { string } customerId 用户编码
 * @property { string } orderChannel
 * 电商字段; 订单渠道; 拼团/门店订单，转发电商必传
 *
 * O2O:线上订单; OFFLINE:线下订单; MERGE_CONSUME: 消费类订单合并开票
 *
 * @property { string } eshopChannelOrderSn
 * 电商渠道订单编号;拼团/门店类型，当只开一个订单的发票这个字段转发电商必传
 *
 * @property { string } drawableAmount 可开具发票金额
 * @property { string } invoiceType 开票类型
 * @property { string } buyerInvoiceTitle 抬头
 *
 * 如果发票类型为企业：不允许输入除 空格，中文括号（），英文点.之外的特殊字符，不允许输入阿拉伯数字，限长:35;
 *
 * 如果发票类型为个人：不允许输入数字，不允许输入特殊符号（除空格，空格限制不允许连续两个）,限长：30(由10限制变更为30。********修改)
 *
 * @property { string } buyerTaxCode 纳税人识别号
 * invoiceType为2时必填，一般是15、17、18、20位长度位数字或大写字母，不允许有字母I、O、S、V、Z
 *
 * @property { string } buyerEmail 电子邮箱
 * @property { string } buyerCompanyAddress 购方公司地址
 * @property { string } buyerCompanyPhone 购方公司电话
 * @property { string } buyerBankName 购方开户行名称
 * @property { string } buyerBankAccount 购方银行账号
 * @property { string } buyerRemark 备注
 * @property { string } authWxCardBag 是否授权微信卡包 0:不授权[默认]，1:授权
 * @property { Array<TListItem> } mergeOrderUnitList 发票明细list
 */

/**
 * @typedef TListItem 发票明细listItem
 * @property { string } channelOrderNo 发票号
 * 合并开票则为待开票列表返回的机构合并发票号
 *
 * 单个线上订单为订单号
 *
 * 单个线下订单为小票号
 *
 * @property { string } invoiceChannel 订单发票渠道
 * 线上订单:1; 线下订单:2; 消费类订单合并开票:3
 *
 * @property { string } orgCode 门店编码
 * @property { Array<TSubOrder> } orderUnitList 子订单列表
 */

/**
 * @typedef TSubOrder 子订单模型
 * @property { string } subOrderNo 订单编号
 * 及时达/全国送类型:订单编号;
 * 拼团/门店类型:invoiceUnitList.orderUnitList.eshopOrderChannel 为O2O时表示线上商品订单编号,为OFFLINE时表示线下ERP小票号
 *
 * @property { string } channelOrderNo 发票号
 * @property { string } drawableAmount 可开票金额
 * @property { string } ticketNo 电商字段 订单小票号
 * 拼团/门店订单, 转换电商时
 *
 * 为线上订单:O2O或线下订单:OFFLINE时必传值
 *
 * @property { string } eshopOrderChannel 电商字段 订单渠道
 * 拼团/门店订单,转发电商必传;
 *
 * 线上订单:O2O
 *
 * 线下订单:OFFLINE
 */

const regRule = {
  //  抬头
  buyerInvoiceTitle: /^([\u4e00-\u9fa5]|[（）]){1,30}$/,
  //  税号
  buyerTaxCode: /^[A-HJ-NP-RTUWXY0-9]+$/,
  //  联系地址
  buyerCompanyAddress: /^[\u4e00-\u9fa5A-Z0-9a-z]+$/,
  //  联系电话
  buyerCompanyPhone: /^[0-9+-]+$/,
  //  开户行
  buyerBankName: /^[\u4e00-\u9fa5]+$/,
  //  银行账号
  buyerBankAccount: /^[0-9]+$/,
  //  电子邮件
  buyerEmail: /[a-zA-Z0-9@.]$/
}

Page({

  /**
   * 页面的初始数据
   */
  data: {
    GOODSIMAGE,
    //  是否老电商（充值订单时使用
    isEshop: false,
    //  订单类型（消费订单时使用
    orderType: '',
    /**订单类型 */
    ORDER_TYPE: ORDER_TYPE,
    //  是否单订单开票，单订单开票时。能够展示查看发票金额详情与微信授权
    isSingleOrder: false,
    //  参与开票的订单数量
    orderQuantity: 0,
    //  开票金额
    drawableAmount: '',
    //  是否授权微信卡包
    isAuthWxCardBag: false,

    //  抬头类型
    TITLE_ENUM,
    //  是否展示更多表单内容
    showMore: false,
    //  抬头类型为公司
    invoiceTypeIsCompany: true,
    //  表单数据
    formData: {
      invoiceType: TITLE_ENUM.company
    },
    //  表单数据备份，用于存储公司/个人两套不同表单的数据
    formDataBackup: {
      [TITLE_ENUM.company]: {},
      [TITLE_ENUM.single]: {},
    },

    //  是否展示联想公司列表
    showCompanyList: false,
    //  抬头联想公司列表
    titleCompanyList: [],

    //  是否展示发票信息确认弹窗
    showInvoiceConfirmModal: false,
    //  发票信息确认弹窗展示表单内容
    invoiceConfirmModalFormList: [],

    //  是否同意协议
    isAgreeProtocal: false,
    // 订阅消息相关
    subscribe: {
      // 是否展示订阅
      show: false,
      // 模板列表
      tmplIds: createInvoiceSubmitTemplateIds
    },
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    //  检查是否有表单缓存
    this.checkFormDataCache()

    const {
      //  是否老电商（充值订单
      isEshop,
      //  开票订单数量
      orderQuantity,
      //  开票订单类型
      orderType,
      //  开票金额
      drawableAmount
    } = JSON.parse(options.pageParam);

    this.setData({
      orderType,
      isEshop: Boolean(isEshop),
      isSingleOrder: orderQuantity === 1,
      orderQuantity,
      drawableAmount
    })

    // 上报神策点击查看详情
    this.trackClickEvent({
      'element_code': '1151401001',
      'element_name': '访问数',
      'element_content': '访问数'
    })
  },

  onShow() {
    sensors.pageScreenView()
    this.getWxAuthStatus()
  },

  /**
   * 获取微信授权状态
   */
  async getWxAuthStatus() {
    let orderSerialNo

    const {
      selectedData
    } = wx.getStorageSync('createInvoice_data')

    if (!selectedData || !selectedData.length) {
      return
    }

    const order = selectedData[0]

    if (this.data.isEshop) {
      orderSerialNo = order.rechargeOrderNo
    } else {
      orderSerialNo = order.channelOrderNo
    }

    const { data } = await app.api.getWxAuthStatus({
      orderSerialNo
    })

    this.setData({
      isAuthWxCardBag: data.status === 'SUCCESS'
    })
  },

  /**
   * 切换展示更多内容
   */
  toggleShowMore() {
    this.setData({
      showMore: !this.data.showMore
    })
  },

  /**
   * 发票抬头改变事件
   * @param {*} e
   */
  titleChange: util.debounce(function (e) {
    this.inputChangeHandle(e, async isPass => {
      if (!e.detail.value) {
        this.closeCompanyList()
      }

      if (isPass && this.data.invoiceTypeIsCompany && e.detail.value) {
        try {
          //  开始联想公司信息
          const res = await app.api.titleSearch({
            keyword: e.detail.value
          })
          const data = res.data.filter(item => {
            if (this.data.isEshop) {
              return item.taxCode !== BGY_TAXCODE
            }
            return true
          })

          if (data.length) {
            this.setData({
              titleCompanyList: data,
              showCompanyList: true
            })
          }
        } catch (error) {
          console.info('联想发票抬头服务出了点问题', error)
        }
      }
    })

  }, 300),

  /**
   * 关闭公司联想列表
   */
  closeCompanyList() {
    this.setData({
      showCompanyList: false
    })
  },

  /**
   * 选择联想公司
   * @param {*} e
   */
  chooseCompany(e) {
    const { title, taxCode } = e.currentTarget.dataset.company

    this.setData({
      'formData.buyerInvoiceTitle': title,
      'formData.buyerTaxCode': taxCode
    })
    this.setFormDataCache()
    this.closeCompanyList()
  },

  /**
   * 输入框改变通用处理
   * @param {*} e
   * @returns { Boolean } 内容是否发生更新
   */
  inputChangeHandle(e, afterHandle) {
    const { field } = e.target.dataset

    const isPass = this.validateField(field, e)

    if (isPass) {
      const value = e.detail.value

      this.setData({
        [`formData.${field}`]: value
      })

      this.setFormDataCache()
    }

    if (afterHandle) {
      afterHandle(isPass)
    }
  },

  /**
   * 清空内容统一处理
   * @param {*} e
   */
  clearHandle(e) {
    const { field } = e.target.dataset

    this.setData({
      [`formData.${field}`]: ''
    })

    if (field === 'buyerInvoiceTitle') {
      this.closeCompanyList()
    }

    this.setFormDataCache()
  },

  /**
   * 输入后校验表单字段
   * @param { string } field
   * @param { Event } e
   * @returns { Boolean } 是否通过校验允许设置表单值
   */
  validateField(field, e) {
    const value = e.detail.value

    if (value === '') {
      return true
    }

    const reg = regRule[field]

    const isPass = reg ? reg.test(value) : true
    const hasEmoji = util.checkEmoji(value)
    const notSet = !isPass || hasEmoji

    if (notSet) {
      this.setData({
        [`formData.${field}`]: this.data.formData[field] || ''
      })
    }

    return isPass && !hasEmoji
  },

  /**
   * 检查是否存在表单内容缓存，如存在进行回显设置
   */
  checkFormDataCache() {
    const formDataCache = wx.getStorageSync('createInvoice_formDataCache')

    if (formDataCache) {
      this.setData({
        formData: formDataCache,
        invoiceTypeIsCompany: formDataCache.invoiceType === TITLE_ENUM.company
      })
    }
  },

  /**
   * 设置备份数据
   */
  setBackup() {
    const formData = this.data.formData

    this.setData({
      [`formDataBackup.${formData.invoiceType}`]: { ...formData }
    })
  },

  /**
   * 表单内容发生了变化，均更新表单内容缓存
   */
  setFormDataCache() {
    wx.setStorageSync('createInvoice_formDataCache', this.data.formData)
  },

  /**
   * 切换抬头类型
   * @param {*} e
   */
  chooseInvoiceType(e) {
    const { type } = e.currentTarget.dataset

    if (type === this.data.formData.invoiceType) {
      return
    }

    //  先备份当前表单
    this.setBackup()

    //  切换另一份表单
    const nextBackupData = this.data.formDataBackup[this.data.invoiceTypeIsCompany ? TITLE_ENUM.single : TITLE_ENUM.company]
    const formData = {
      ...nextBackupData,
      invoiceType: type
    }

    this.setData({
      formData,
      invoiceTypeIsCompany: type === TITLE_ENUM.company
    })

    this.setFormDataCache()
  },

  /**
   * 跳转发票金额详情页
   */
  toInvoiceDetail() {
    const query = JSON.stringify({
      isEshop: this.data.isEshop,
      orderType: this.data.orderType,
      drawableAmount: this.data.drawableAmount,
    })

    wx.navigateTo({
      url: '/userB/pages/invoice/invoiceAmountDetail/index?pageParam=' + query
    })

    // 上报神策点击查看详情
    this.trackClickEvent({
      'element_code': '1151401004',
      'element_name': '发票金额',
      'element_content': '发票金额'
    })
  },

  /**
   * 获取微信授权地址
   */
  async getWXAuthURL() {
    const { selectedData } = wx.getStorageSync('createInvoice_data') || {}
    const order = selectedData[0]

    let params
    if (this.data.isEshop) {
      params = {
        orderSerialNo: order.rechargeOrderNo,
        amount: this.data.drawableAmount,
        orderCreateTime: order.rechargeTime
      }
    } else {
      params = {
        orderSerialNo: order.channelOrderNo,
        amount: this.data.drawableAmount,
        orderCreateTime: order.finishTime
      }
    }

    const { data } = await app.api.getWXAuthURL(params)

    this.setData({
      wxAuthData: data
    })

    return data
  },

  /**
   * 查看微信卡包授权提示
   */
  showWxAuthCardBagTip() {
    //  已展示气泡，进行关闭
    if (this.data.showQuestionTip) {
      this.hideQuestionTip()
      return;
    }

    this.clearTipTimer()

    this.setData({
      showQuestionTip: true
    })

    this.data.tipTimer = setTimeout(() => {
      this.hideQuestionTip()
    }, 8000)

    // 上报神策点击微信卡包
    this.trackClickEvent({
    'element_code': '1151401003',
    'element_name': '微信卡包',
    'element_content': '微信卡包',
    })

  },

  /**
   * 清除好评气泡延时器
   */
  clearTipTimer() {
    const tipTimer = this.data.tipTimer
    clearTimeout(tipTimer)
  },

  /**
   * 隐藏问题提示
   */
  hideQuestionTip() {
    this.clearTipTimer()

    this.setData({
      showQuestionTip: false
    })
  },

  /**
   * 授权微信卡包
   */
  async authorizeWxCardBag() {
    if (this.data.isAuthWxCardBag) {
      return
    }
    let wxAuthData = this.data.wxAuthData

    if (!wxAuthData) {
      wxAuthData = await this.getWXAuthURL()
    }

    wx.navigateToMiniProgram({
      appId: wxAuthData.appid,
      path: wxAuthData.auth_url,
      success: () => {
        //  将在onshow回调里重新调用查询状态函数
      },
      fail: error => {
        console.log('进入了fail回调:', error)
      }
    })
  },

  /**
   * 获取用户id
   * @returns
   */
  getUserId() {
    return (wx.getStorageSync('user') || { userID: -1 }).userID
  },

  /**
   * 预览发票案例
   */
  previewExample() {
    const exampleUrl = 'https://resource.pagoda.com.cn/eshop/dcloud/attachments/2022/08/29/85dece7b85c5e59008628fd0f3e92ff5.png%3FimageMogr2/thumbnail/750x484/format/png'
    wx.previewImage({
      urls: [exampleUrl]
    })
  },

  /**
   * 获取微信卡包抬头信息
   */
  chooseWechatInvoice() {
    wx.chooseInvoiceTitle({
      success: res => {
        if (res) {
          const {
            type,
            title = '',
            taxNumber = '',
            companyAddress = '',
            bankName = '',
            bankAccount = '',
            telephone = ''
          } = res

          //  res.type, 0:单位, 1:个人
          const invoiceTypeIsCompany = Number(type) === 0
          let isHasIllegalValue = false

          //  选择了不同类型的抬头
          if (invoiceTypeIsCompany !== this.data.invoiceTypeIsCompany) {
            //  备份当前表单
            this.setBackup()
          }

          /**
           * 格式化和清除空格
           * @param {*} value
           * @param {*} len
           * @returns
           */
          const formatAndClean = (value, len, reg) => {
            value = value.replaceAll(/ /g, '')

            if (!reg.test(value)) {
              value = value.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '')
              isHasIllegalValue = true
            }

            value = value.slice(0, len)
            return value
          }

          this.setData({
            invoiceTypeIsCompany,
            'formData.buyerInvoiceTitle': formatAndClean(title, invoiceTypeIsCompany ? 35 : 30, regRule.buyerInvoiceTitle),
            'formData.invoiceType': invoiceTypeIsCompany ? TITLE_ENUM.company : TITLE_ENUM.single,
            'formData.buyerTaxCode': formatAndClean(taxNumber, 20, regRule.buyerTaxCode),
            'formData.buyerCompanyAddress': formatAndClean(companyAddress, 30, regRule.buyerCompanyAddress),
            'formData.buyerBankName': formatAndClean(bankName, 20, regRule.buyerBankName),
            'formData.buyerBankAccount': formatAndClean(bankAccount, 50, regRule.buyerBankAccount),
            'formData.buyerCompanyPhone': formatAndClean(telephone, 15, regRule.buyerCompanyPhone)
          })

          if (isHasIllegalValue) {
            wx.showToast({
              title: '检测到导入的信息存在特殊符号，请确认您导入的信息',
              icon: 'none'
            })
          }

          this.setFormDataCache()
        }
      }
    })

    // 上报神策点击选择微信发票抬头
    this.trackClickEvent({
      'element_code': '**********',
      'element_name': '微信抬头',
      'element_content': '微信抬头',
    })
  },

  /**
   * 切换点击协议radio
   */
  agreeHandle() {
    this.setData({
      isAgreeProtocal: !this.data.isAgreeProtocal
    })
  },

  /**
   * 跳转协议页
   */
  navigateToPage() {
    const pageUrl = protocolUrl.invoiceRegulation
    wx.navigateTo({
      url: `/h5/pages/commonLink/index?pageUrl=${pageUrl}`
    })
  },

  /**
   * 提交前字段校验
   */
  submitFieldValid() {
    const { buyerInvoiceTitle, buyerTaxCode, buyerEmail } = this.data.formData
    const msgFn = msg => {
      wx.showToast({
        title: msg,
        icon: 'none'
      })

      throw new Error(msg)
    }

    if (!buyerInvoiceTitle) {
      msgFn('请填写发票抬头')
    }

    if (this.data.invoiceTypeIsCompany) {
      if (!buyerTaxCode) {
        msgFn('请填写公司税号')
      }

      if (![15, 17, 18, 20].includes(buyerTaxCode.length)) {
        msgFn('公司税号长度为15位、17位、18位或者20位')
      }
    }

    if (!buyerEmail) {
      msgFn('请填写电子邮箱')
    }

    if (!/^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(buyerEmail)) {
      msgFn('输入正确的电子邮箱地址才能收到电子发票的哦~')
    }
  },
  /**
   * 敏感词校验
   */
  async checkWord() {
    const { buyerRemark = '', buyerCompanyAddress = '' } = this.data.formData

    const res = await checkSensitiveWord({
      loadingText: '校验中',
      defaultToastText: '备注/地址含有敏感信息，请修改后发布',
      bizType: [2, 3],
      text: [
        buyerRemark,
        buyerCompanyAddress
      ]
    }).catch(e => {
      console.log(e)
    })
    // 存在敏感词则res为false
    if (!res) {
      throw new Error('敏感词校验不通过')
    }
  },

  /**
   * 开票前校验
   */
  async beforeSubmit() {
    try {
      const {
        formData: { buyerInvoiceTitle, buyerTaxCode, buyerEmail },
        invoiceTypeIsCompany,
        isAgreeProtocal
      } = this.data

      if (this.data.isEshop && String(buyerTaxCode) === BGY_TAXCODE || buyerInvoiceTitle === '深圳百果园实业(集团)股份有限公司') {
        this.setData({
          showConfirmModal: true,
        })
        return
      }

      const invoicePass = invoiceTypeIsCompany ? buyerTaxCode : true

      if (buyerInvoiceTitle && invoicePass && buyerEmail && isAgreeProtocal) {
        this.submitFieldValid()
        // 增加敏感词校验
        await this.checkWord()

        this.openInvoiceConfirmModal()
      }
    } catch (error) {
      console.info(error)
    }

    // 上报神策点击提交
    this.trackClickEvent({
      'element_code': '1151401005',
      'element_name': '提交',
      'element_content': '提交',
    })
  },

  /**
   * 弹窗确认回调
   */
  modalConfirm() {
    this.setData({
      showConfirmModal: false,
    })
  },

  /**
   * 打开发票信息确认弹窗
   */
  openInvoiceConfirmModal() {
    const formList = [
      {
        label: '发票抬头',
        field: 'buyerInvoiceTitle'
      },
      {
        label: '公司税号',
        field: 'buyerTaxCode',
        condition: () => this.data.invoiceTypeIsCompany,
      },
      {
        label: '电子邮箱',
        field: 'buyerEmail'
      },
      {
        label: '微信卡包',
        formatter: () => this.data.isAuthWxCardBag ? '推送' : '未设置推送',
        condition: () => this.data.orderType !== ORDER_TYPE.GIFTCARD
      },
      {
        label: '联系地址',
        field: 'buyerCompanyAddress',
      },
      {
        label: '联系电话',
        field: 'buyerCompanyPhone',
      },
      {
        label: '开户行',
        field: 'buyerBankName',
      },
      {
        label: '银行账号',
        field: 'buyerBankAccount',
      },
      {
        label: '备注说明',
        field: 'buyerRemark',
      },
    ]

    this.setData({
      showInvoiceConfirmModal: true,
      invoiceConfirmModalFormList: formList.map(form => {
        if (form.content) {
          return form
        }

        let content = this.data.formData[form.field]

        if (form.formatter) {
          content = form.formatter(content)
        }

        return {
          ...form,
          content
        }
      }).filter(form => {
        if (form.condition) {
          return form.condition()
        } else {
          return form.content
        }
      })
    })
  },

  /**
   * 关闭发票信息确认弹窗
   */
  closeInvoiceConfirmModal() {
    this.setData({
      showInvoiceConfirmModal: false
    })
  },

  /**
   * 发票信息确认弹窗提交回调
   */
  invoiceConfirmModalSubmit() {
    this.closeInvoiceConfirmModal()

    this.setData({ 'subscribe.show': true })
  },

  /**
   * 订阅消息关闭事件
   */
  onSubscribeClose({ detail }) {
    this.setData({ 'subscribe.show': false })

    if (this.data.isEshop) {
      this.eshopSubmit()
    } else {
      this.submit()
    }

    if (detail) {
      reportTmplIds(detail.resultStatus)
    }
  },

  /**
   * 创建发票（老电商，充值订单）
   */
  async eshopSubmit() {
    const userID = this.getUserId()
    const { formData, isAuthWxCardBag, drawableAmount } = this.data
    const {
      invoiceData,
      selectedData
    } = wx.getStorageSync('createInvoice_data') || {}

    const submitData = {
      //  用户ID
      customerID: userID,
      //  可开具发票金额, 单位为分
      drawableAmount,
      //  开票类型 P:个人 C:企业
      invoiceType: [0, 'P', 'C'][formData.invoiceType],
      //  抬头
      title: formData.buyerInvoiceTitle,
      //  纳税人识别号，type为C时必填
      taxCode: formData.buyerTaxCode,
      //  电子邮箱
      buyerEmail: formData.buyerEmail,
      //  购方公司地址
      buyerCompanyAddress: formData.buyerCompanyAddress,
      //  购方公司电话
      buyerCompanyPhone: formData.buyerCompanyPhone,
      //  购方开户行名称
      buyerBankName: formData.buyerBankName,
      //  购方银行账号
      buyerBankAccount: formData.buyerBankAccount,
      //  备注
      remark: formData.buyerRemark,
      //  是否授权微信卡包 Y:授权 N:不授权[默认]
      isAuthWxCardBag: isAuthWxCardBag ? 'Y' : 'N'
    }

    //  商品订单编号
    let number

    //  单订单开票
    if (selectedData.length === 1) {
      //  订单渠道，单个订单时。为线上充值订单: O2O_RECHARGE
      submitData.orderChannel = 'O2O_RECHARGE'

      number = selectedData[0].rechargeOrderNo
      //  渠道订单编号（当只开一个订单的发票这个字段必传）
      submitData.channelOrderSn = selectedData[0].rechargeOrderNo
    }
    //  多订单开票
    else {
      //  订单渠道，多个订单时。为充值类订单合并开票: MERGE_RECHARGE
      submitData.orderChannel = 'MERGE_RECHARGE'

      number = invoiceData.channelOrderParentalSn
      //  合并开票子订单列表，多个订单时传递
      submitData.mergeOrderUnitList = [
        {
          //  渠道订单父小票号
          channelOrderParentalSn: invoiceData.channelOrderParentalSn,
          //  子订单列表
          orderUnitList: selectedData.map(order => {
            return {
              //  渠道订单编号
              channelOrderSn: order.rechargeOrderNo,
              //  可开票金额
              drawableAmount: order.rechargeAmount,
              //  订单渠道
              orderChannel: 'MERGE_RECHARGE'
            }
          })
        }
      ]
    }

    try {
      await app.api.eshopApplyInvoice(submitData)

      this.afterSubmit({
        number,
        orderChannel: submitData.orderChannel
      })
    } catch (error) {
      console.info(error)
      if (error.messageInfo) {
        app.showModalPromise({
          content: error.messageInfo
        });
      }
    }

  },

  /**
   * 创建发票（中台，消费订单）
   */
  async submit() {
    const userID = this.getUserId()
    const { orderType, formData, isAuthWxCardBag, drawableAmount } = this.data

    /**
     * 初始提交数据
     * @type { TSubmitData }
     */
    const submitData = {
      orderType,
      customerId: userID,
      drawableAmount,
      invoiceType: formData.invoiceType,
      buyerInvoiceTitle: formData.buyerInvoiceTitle,
      buyerTaxCode: formData.buyerTaxCode,
      buyerEmail: formData.buyerEmail,
      buyerCompanyAddress: formData.buyerCompanyAddress,
      buyerCompanyPhone: formData.buyerCompanyPhone,
      buyerBankName: formData.buyerBankName,
      buyerBankAccount: formData.buyerBankAccount,
      buyerRemark: formData.buyerRemark,
      authWxCardBag: isAuthWxCardBag ? 1 : 0
    }

    const { selectedData, selectedStore } = wx.getStorageSync('createInvoice_data') || {}
    //  这个query将会通过开票成功页带到发票详情页进行详情接口查询
    const query = {
      orderChannel: '',
      channelOrderNo: '',
      operationType: 'HISTORY_LIST'
    }

    //  单订单开票
    if (selectedData.length === 1) {
      const order = selectedData[0]
      submitData.orderChannel = order.orderChannel
      submitData.eshopChannelOrderSn = order.channelOrderNo

      query.orderChannel = order.orderChannel
      query.channelOrderNo = order.channelOrderNo

      const isO2O = order.orderChannel === 'O2O'
      const isOffline = order.orderChannel === 'OFFLINE'
      const obj = {
        channelOrderNo: order.channelOrderNo,
        invoiceChannel: { O2O: '1', OFFLINE: '2' }[order.orderChannel],
        orgCode: order.orgCode,
        orderUnitList: [
          {
            subOrderNo: order.channelOrderNo,
            channelOrderNo: order.channelOrderNo,
            drawableAmount: order.drawableAmount,
            eshopOrderChannel: order.orderChannel,
            //  线上商品订单，线下商品订单需传递字段
            ticketNo: (isO2O || isOffline) ? order.ticketNo : ''
          }
        ]
      }

      if (orderType === ORDER_TYPE.GIFTCARD) {
        obj.invoiceOrderType = 'G'
      }
      submitData.mergeOrderUnitList = [obj]
    }
    //  多订单开票
    else {
      submitData.orderChannel = 'MERGE_CONSUME'
      query.orderChannel = 'MERGE_CONSUME'
      query.channelOrderNo = selectedStore[0].mergeChannelOrderNo

      submitData.mergeOrderUnitList = selectedStore.map(store => {
        const obj = {
          channelOrderNo: store.mergeChannelOrderNo,
          invoiceChannel: '3',
          orgCode: store.orgCode,
          orderUnitList: store.selectedOrderArr.map(order => {
            return {
              subOrderNo: order.channelOrderNo,
              channelOrderNo: store.mergeChannelOrderNo,
              drawableAmount: order.drawableAmount,
              eshopOrderChannel: order.orderChannel
            }
          })
        }

        if (orderType === ORDER_TYPE.GIFTCARD) {
          obj.invoiceOrderType = 'G'
        }
        return obj
      })
    }

    try {
      const { data } = await app.api.applyInvoice(submitData)

      if (data.failShowMsg) {
        app.showModalPromise({
          content: data.failShowMsg
        });
        return
      }

      this.afterSubmit(query)
    } catch (error) {
      console.info(error)
      if (error.description) {
        app.showModalPromise({
          content: error.description
        });
      }
    }
  },

  /**
   * 订单提交成功后
   * @param {*} query
   */
  afterSubmit(query) {
    query.isEshop = this.data.isEshop
    //  query将会通过开票成功页带到发票详情页进行详情接口查询，属性视开票类型而定（充值，消费
    query = JSON.stringify(query)
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.emit('create.invoice.success')
    //  跳转成功提交页
    wx.redirectTo({
      url: `/userB/pages/invoice/invoiceSuccess/index?pageParam=${query}`
    })
  },

  // 神策上报点击事件
  trackClickEvent( params = {} ) {
    if ( app.globalData.reportSensors ) {
      app.sensors.track('Click', {
        ...params,
        blockName: '开具发票页',
        blockCode: 1151401,
      })
    }
  },
})
