const config = require('~/utils/config')
const util = require('~/utils/util')
import operateCartMixin from '~/mixins/bgxx/operateCartMixin'
import cartAnimate from '~/mixins/bgxx/cartAnimate'
import styleConfig from '~/utils/goodsStyleConfig'
import vegetablesLocateMixin from '~/mixins/vegetablesLocateMixin'
import locateMixin from '~/mixins/locateMixin'
import storeBinding from '~/mixins/bgxx/storeBinding'
// import combineGoods from '~/stores/combineGoods'
import sensors from '~/utils/report/sensors'
import locateStore from '~/stores/module/locate'
import {
  jumpH5Vip
} from '~/utils/services/jumpBgxxVip'

// import {
//   tabBarStore,
//   xmdxHomePageName
// } from '~/mixins/tabBarStore'
import {
  createStoreBindings
} from 'mobx-miniprogram-bindings'
import bgxxStore from '~/stores/module/bgxxStore'
import waterFallStore from '../components/waterfallGoods/store/index'
import { checkIsXMDXNewCustomer } from '~/source/js/requestData/getFreshRecommendGoods'
import { getBgxxSaleReportInfo } from '~/utils/report/getSaleReportInfo'
import { changeFreshChannel } from '~/utils/report/setup'
import { freshMetro } from '~/components/bgxx/pagoda-metro/promise'
import { ORDER_TYPE } from '~/source/const/order'
const activity = require('~/utils/activity')

const app = getApp()
const CITY_STATUS = {
  error: -1, // 无城市id
  normal: 0, // 正常情况（有城市无切换）
  change: 1 // 城市改变
}
let hasLoaded = false
const {
  FreshGoods
} = require('~/service/freshGoods')
const {
  FreshSubject
} = require('~/service/freshSubject')

const TOP_PIC_URL = 'https://resource.pagoda.com.cn/dsxcx/images/c222369149cac970f6bb0b1d55e498b3.png'
const MIDDLE_PIC_URL = 'https://resource.pagoda.com.cn/dsxcx/images/4244144fff1d7a1f7ec84366f553fdfb.png'
const BOTTOM_PIC_URL =  'https://resource.pagoda.com.cn/dsxcx/images/68024f12ce572d583402096c95e4f106.png'

// 缓存首页数据，以便下次更快渲染
const CACHEDATA = {}
Page({
  data: {
    ORDER_TYPE,
    showCart: false,
    picDomain: config.baseUrl.PAGODA_PIC_DOMAIN,
    cityInfo: {
      cityId: '',
      storeId: '',
      cityName: ''
    },
    loadFinished: false,
    styleConfig: styleConfig,
    hiddenBackTop: true,
    waterfall: {
      isFixed: false,
      // 当前瀑布流tab的index
      tabIndex: 0,
      // scroll-into-view瀑布流tab的index
      showTabIndex: 0,
      // 瀑布流内容是否加载中
      loading: false,
      // 瀑布流内容是否加载完成
      finished: false,
      // 最后瀑布流是否加载完成(用于显示"没有更多了")
      lastFinished: false,
      // 瀑布流是否触底
      reachBottom: false,
      // 是否释放加载下一个
      releaseToNext: false,
      // 是否是ios(如果是用ios自身的橡皮筋拖拽效果)
      isIos: wx.getStorageSync('systemInfo').platform === 'ios',
      // 底部显示的拖拽提示
      bounceTips: '上拉继续浏览'
    },
    newCustomerGoods: [],
    isShowPopUp: false,
    tabList: [],
    navBar: {
      height: 0,
      paddingRight: 0,
      animation: true
    },
    showClose: false,
    contentRefresh: {
      scrollAnimate: true,
      triggered: false,
      threshold: 45,
      scrollInto: ''
    },
    skeleton: {
      titleHeight: 44,
      statusBarHeight: 20,
      paddingRight: 90
    },
    guideZone: {
      left: 0,
      top: 0,
      width: 0,
      height: 0
    },
    guideTips: {},
    scrollTop: 110,
    swiperlist: [], //banner轮播列表
    splitList: [], // 切分banner
    bgColorList: [],
    btnColor: "",
    newUserCoupons: [], // 新人券包
    videoList: [],
    pagodaBrand: {}, // 品牌专栏
    topBannerList: [],
    salesPromotionObj: {}, // 大促活动数据
    backgroundBelowUrl: '', // 大促底部背景图
    backgroundTopHeight: 0, // 大促头部背景图高度，取状态栏的高度
    isLogin: false, // 是否登录
    newGoodsRecommendList: [], // 新品推荐数据
    ifShowNewRcommend: true,
    metroBannerList: [],
    goodsList: [],
    minHeight: 0,
    userInfo: {}, // 用户信息
    bgxxStoreInfo: {},
    hideStickNav: true,
    // 次日达优惠券数量
    couponNum: 0,
    canScrollY: true,
    pageScrollViewTop: 0,
    topPicUrl: TOP_PIC_URL,
    middlePicUrl: MIDDLE_PIC_URL,
    bottomPicUrl: BOTTOM_PIC_URL,
    navBgImgStyle: `background-image: url('${TOP_PIC_URL}'), url(${MIDDLE_PIC_URL});`, // 导航栏背景图
    saleBgObj: {}, // 大促背景
    showcaseObj: {}, // 橱窗
    waistBannerObj: {}, // 腰部banner
    isHasSaleBg: false, // 是否有大促背景
    searchBoxIsSticky: false, // 搜索栏是否吸顶
  },
  _data: {
    pageProtocol: true,
    // 是否通过onTabItemTap设置过nav-bar动画
    // 将在onHide中重新设置为false
    hasSetNavAnimate: false,
    windowHeight: 0,
    operateAnimation: null,
    operateMoveTimer: -1,
    operateMoveStatus: 0,
    scrollTop: 0, // 记录滚动条上次滚动位置
    floatingAni: null, // floating 动画
    waterfallTop: 0,
    useInfo: {},
    page: {
      pageNum: 0
    },
    goodsList: [], // 瀑布流分页商品全部数据
    reachBottomOb: null, // 监听瀑布流是否触底
    showPopupTime: 240, // 弹窗间隔时间 单位：分钟
    tabBarCartIndex: 2, // operateCartMixin中setCount时设置tabbar角标的index值
    resolveUserGuide: null,
    prioritysData: {},
    priorityId: 1,
    waterfallInited: (function() {
      let resolveFn
      const promise = new Promise(resolve => (resolveFn = resolve))
      return { resolve: resolveFn, promise }
    })(),
    initedRect:false,
    compFetchMap: {},
    needRefreshNewUserInfo: true,
    cacheData: {}
  },
  _storeImageStatus: [],
  mixins: [ operateCartMixin, locateMixin, vegetablesLocateMixin, cartAnimate, storeBinding, waterFallStore],
  async onLoad(options) {
    this.setModelStyle()
    app.changeBgxxCityChangeStatus(true)
    this._data.windowHeight = app.globalData.hh || 600
    this.setData({
      curFloorNum: -1,
      goodsList: [],
      needCartAnimate: false
    })
    this.useScene = 'bgxxShop'
    this.loadOptions = options || {}
    this.checkShareEnter(this.loadOptions)
    this.createOperateAnimation()
    this.checkHomeDeliveryEnter()
    this.storeBindings = createStoreBindings(this, {
      store: bgxxStore,
      fields: ['bgxxCartCount', 'bgxxStoreInfo']
    })
    // 监听用户身份变更
    app.event.on('refreshFreshNewUserInfo', this.refreshFreshNewUserHandle)
    // this.createNavBgAnimation()

    setTimeout(() => {
      this.addScrollEvent()
    },2500)
  },
  /**
   * @description 添加滚动监听，向下滚动，导航栏切回默认背景图，滑到顶部切回大促背景图
   */
  addScrollEvent() {
    this._data.scrollObserve = wx.createIntersectionObserver(this)
    this._data.scrollObserve.relativeToViewport({
      top: 0
    }).observe('.nav-bar-top', res => {
      if (!this.data.isHasSaleBg) {
        return
      }
      if (res.intersectionRatio <= 0) {
        this.setNavBarBgData()
        return
      }
      this.setNavBarBgData(this.data.topPicUrl, this.data.middlePicUrl)
    })
  },
  refreshFreshNewUserHandle() {
    this._data.needRefreshNewUserInfo = true
  },
  /**
   * @desc 从及时达首页生鲜推荐模块跳转
   */
  checkHomeDeliveryEnter() {
    app.event.on(
      'xxshop.index.linkHash',
      ({ linkHash, goodsSn }) => wx.nextTick(async () => {
        const scrollAnimate = this.data.contentRefresh.scrollAnimate
        scrollAnimate && await new Promise(resolve => this.setData({ 'contentRefresh.scrollAnimate': false }, resolve))
        const scrollInto = `scroll-flag-${linkHash}`
        await new Promise(resolve => this.setData({ 'contentRefresh.scrollInto': {
          waterfall: scrollInto,
          new: scrollInto,
          hot: `${scrollInto}-${goodsSn}`
        }[linkHash] }, resolve))
        if (scrollAnimate) {
          await new Promise(resolve => setTimeout(resolve, 1000))
          this.setData({ 'contentRefresh.scrollAnimate': true })
        }
      })
    )
  },
  /**
   * 是否分享携带parentCustomerID进入
   */
  async checkShareEnter(options) {
    let {
      parentCustomerID = '', scene = ''
    } = options
    if (scene) {
      const sceneStr = decodeURIComponent(scene)
      const list = sceneStr.split('@')
      if (list && !!list.length) {
        if (sceneStr.includes('PD')) {
          parentCustomerID = list[1] || ''
        } else {
          const strLength = sceneStr.split('@').length-1
          parentCustomerID = (strLength === 3 ? list[3]: list[2]) || ""
        }
      }
    }
    if (parentCustomerID) {
      this.setParentCustomerID(parentCustomerID)
      this.bindUserRelation()
    }
  },
  reportSensor(props, type = 'MPClick') {
    if (!app.globalData.reportSensors) return false
    app.sensors.track(type, {
      screen_code: '11300',
      screen_name: '次日达首页',
      ...props
    })
  },
  onShow() {
    changeFreshChannel()
    //获得dialog组件
    this.dialog = this.selectComponent("#dialog")
    if (app.checkSignInsStatus()) {
      this.dialog.hideDialog()
    }
    app.getBgxxStoreInfo()
    setTimeout(() => {
      this.reportExposure()
    },2000)

    setTimeout(() => {


    },1000)

  },
  onHide() {
    this.tapUserGuide()
    this.setData({
      isShowPopUp: false,
      showOnePopup: false
    })
    this.toPageData = {
      page: '',
      data: {}
    }
    this._data.hasSetNavAnimate = false
  },
  onTabItemTap() {
    if (!this._data.hasSetNavAnimate) {
      this.setData({
        'navBar.animation': false
      })
      setTimeout(() => {
        this.setData({
          'navBar.animation': true,
          isShowPopUp: false
        })
      }, 100)
      this._data.hasSetNavAnimate = true
    }
  },
  /**
   * 未选择门店时提示弹窗
   */
  async selectedStore() {
    const {
      cityName
    } = this.data.cityInfo
    const {
      selectStoreInfo = {}
    } = wx.getStorageSync("bgxxSelectLocateInfo") || {}
    if (selectStoreInfo.storeID || !app.globalData.userLocationAuth || this.data.currentView !== 'content') {
      return
    }
    const res = await app.showModalPromise({
      content: `您还未进行门店选择哦~   暂不选择将默认为您展示${cityName ? cityName : '深圳市'}的商品`,
      showCancel: true,
      cancelText: '暂不选择',
      confirmText: '选择门店',
    })
    res && wx.navigateTo({
      url: `/bgxxShop/pages/chooseStore/index`
    })
  },
  /**
   * 展示弹窗
   * @param {*} options
   */
  showDialog(options) {
    return new Promise((resolve) => {
      this.dialog.showDialog({
        content: options.content,
        cancelText: options.cancelText,
        confirmText: options.confirmText,
        showCancel: options.showCancel,
        confirm() {
          resolve(true)
        },
        cancel() {
          resolve(false)
        }
      })
    })
  },
  // 更新导航栏高度
  updateNavHeight(e) {
    // this.setData({
    //   'navBar.height': e.detail.height
    // })
  },
  setModelStyle() {
    const that = this
    wx.getSystemInfo({
      success: function ({
        screenWidth,
        windowHeight,
        statusBarHeight
      }) {
        let {
          top,
          height,
          left
        } = wx.getMenuButtonBoundingClientRect()
        that._data.windowHeight = windowHeight
        const titleHeight = height + (top - statusBarHeight) * 2
        that.setData({
          statusBarHeight,
          skeleton: {
            titleHeight,
            statusBarHeight,
            paddingRight: screenWidth - left + 20
          },
          'navBar.height': statusBarHeight + titleHeight,
          'navBar.paddingRight': screenWidth - left,
          'contentRefresh.threshold': statusBarHeight + 25
        })
      }
    });
  },
  reportViewScreen() {
    // 浏览页面上报神策
    sensors.pageScreenView({
      $url_query: JSON.parse(JSON.stringify(this.loadOptions))
    })
  },
  /**
   * 定位完成回调
   * initStep1: 获取首页banner等数据
   * initStep2：页面第二阶段操作，获取各种附加数据，新客专享接口对顶部栏配置有影响
   * initStep3: 页面第三阶段操作，包括获取对应瀑布流商品，计算瀑布流位置，监听瀑布流触底
   */
  async onLocateReady({ isRefresh, onlyShowData }) {
    !onlyShowData && this.showDefaultLocateTips()
    app.loadShopCart().then(res => {
      this.setCount(res.cartCount)
      this.setData({
        showCart: true
      })
    })
    this.initData(onlyShowData)
    const {
      userInfo
    } = this._data
    const isCityChange = isRefresh || this.checkCityStatus()

    try {
      // const { selectAddressInfo } = wx.getStorageSync('bgxxSelectLocateInfo') || {}
      // const { supportSuperVipShop } = selectAddressInfo || {}
      // if (supportSuperVipShop === 'N') {
      //   app.showModalPromise({
      //     content: '当前城市未开通服务，请切换其他城市',
      //     confirmText: '我知道了',
      //   })
      //   return
      // }

      if (isCityChange === CITY_STATUS.error) {
        // 是否有推荐门店
        // const {
        //   bgxxStoreID: storeID = -1,
        //   scene = ''
        // } = this.loadOptions
        // if ((storeID !== 'undefined' && Number(storeID) !== -1) || scene) {
        //   this.checkNavigatePage({
        //     sence: 'noCityId'
        //   })
        // }
        throw new Error('no city id')
      }
      this.selectedStore()
      this.setDataByCahe()
      Promise.all([
        this.initStep1(isCityChange, onlyShowData),
        (async() => {
          await this.initStep2(userInfo, isCityChange, onlyShowData)
          // this.initStep3(userInfo, isCityChange)
        })(),
        // this.initStep4(userInfo)
      ])
    } catch (e) {
      console.error('不支持心享业务~', e)
    }
    this.reportViewScreen()
    // 勿删，兼容历史问题
    onlyShowData || app.changeBgxxCityChangeStatus(false)
    onlyShowData || wx.nextTick(async () => {
      await this._data.waterfallInited.promise
      app.event.emit('xxshop.index.loaded')
    })
  },
  setDataByCahe() {
    const {
      cityCode
    } = this.data.cityInfo
    const cacheData = CACHEDATA[cityCode] || {}
    this.setData({
      ...cacheData
    })
  },
  setDataToCache() {
    const {
      cityCode
    } = this.data.cityInfo
    CACHEDATA[cityCode] = {
      showcaseList: this.data.showcaseList,
      waistBannerObj: this.data.waistBannerObj,
    }
  },
  // 设置用户信息数据,便于查询管理
  setUserInfo() {
    const {
      customerID,
      bgxxCityInfo: {
        cityID,
        storeID
      } = {}
    } = app.globalData
    this._data.userInfo = {
      customerID: customerID || -1,
      cityID: cityID,
      storeID: storeID || -1
    }
    this.setData({
      isLogin: app.checkSignInsStatus(),
      userInfo: {
        customerID: customerID || -1
      },
    })
  },
  /**
   * 首页分发进入其他页面
   */
  async checkNavigatePage(res = {}) {
    if (util.isEmptyObject(this.loadOptions) && util.isEmptyObject(app.globalData.bgxxOptions)) {
      return
    }
    const {
      sence = ''
    } = res
    const isLogin = app.checkSignInsStatus()
    const sceneStr = this.loadOptions.scene ? decodeURIComponent(this.loadOptions.scene) : ''
    const {
      parentCustomerID
    } = this.loadOptions
    const options = {
      ...this.loadOptions,
      ...app.globalData.bgxxOptions
    }
    if (parentCustomerID) {
      wx.setStorageSync('parentInfo', {
        parentCustomerID: parentCustomerID,
        nickName: options.nickName
      })
      // 未登录情况下，去非商品详情页弹窗
      const isShowModal = !isLogin && (!options.toDetail || (!!sceneStr && sceneStr.indexOf('MALL') < 0))
      if (isShowModal) {
        let result = await this.showDialog({
          content: `好友${options.nickName || ''}给您推荐了一个超赞的商城，快来登录看看吧~`,
          confirmText: "去登录",
          showCancel: true,
          cancelText: "先看看"
        })
        wx.removeStorageSync('parentInfo')
        if (result) {
          app.signIn()
          return true
        }
      }
    }
    this.loadOptions = {}
    app.globalData.bgxxOptions = {}
    if (sceneStr) {
      // 小程序码进入，MALL（商品详情页），PD（首页），CSA (次日达综合专题页)
      const sceneList = sceneStr.split('@')
      const id = sceneStr.split('@')[1]
      const storeID = sceneStr.split('@')[2] || ''
      if (sceneStr.indexOf('MALL') > -1) {
        const strLength = sceneStr.split('@').length-1
        const sceneIndex = strLength === 3 ? 3 : 2
        wx.setStorageSync('parentInfo', {
          parentCustomerID: sceneStr.split('@')[sceneIndex] || '',
          nickName: options.nickName
        })
        if(strLength===3){
          //判断是否展示好友推荐
          const {
            status = false
          } = await this.selectedStoreHandle({
            ...options,
            bgxxStoreID:storeID,
            sence
          })
          status && this.toDetail({
            goodsId: id
          })
        }else{
          this.toDetail({
            goodsId: id
          })
        }
      } else if (!isLogin && sceneStr.indexOf('PD') > -1 && !!(sceneStr.split('@')[1])) {
        let result = await this.showDialog({
          content: `好友${options.nickName || ''}给您推荐了一个超赞的商城，快来登录看看吧~`,
          confirmText: "去登录",
          showCancel: true,
          cancelText: "先看看"
        })
        wx.removeStorageSync('parentInfo')
        if (result) {
          app.signIn()
          return true
        }
      } else if (sceneStr.indexOf('JSA') > -1) {
        // 兼容app海报跳转及时达综合专题
        if (sceneList[1]) {
          wx.navigateTo({
            url: `/pages/topic/index?activityID=${sceneList[1]}`,
          })
        }
      }
      else if (sceneStr.indexOf('subjectActivity') > -1 || sceneStr.indexOf('SA') > -1) {
        // type=5 及时达综合专题  JSA@{id}
        // 心享综合专题  CSA@{id}@0
        // 次日达综合专题 CSA@{id}@1
        // 默认心享综合专题
        if (sceneList[2] && Number(sceneList[2]) === 1) {
          this.openSubjectActivity(id, true)
        } else {
          this.openSubjectActivity(id)
        }
      }
    } else if (options.toDetail) {
      //判断是否展示好友推荐
      const {
        status = false
      } = await this.selectedStoreHandle({
        ...options,
        sence
      })
      if (status) {
        // 分享海报进入商品详情页
        const {
          goodsID,
          goodsSn,
          origin
        } = JSON.parse(options.toDetail)
        this.toDetail({
          goodsId: goodsID,
          goodsSn,
          origin,
          fromShareDetail: [1007, 1008, 1047, 1048, 1049].includes(app.globalData.scene)
        })
      }
    } else if (options.toSubjectActivity) {
      //判断是否展示好友推荐
      const {
        status = false
      } = await this.selectedStoreHandle({
        ...options,
        sence
      })
      let toSubjectActivity = options.toSubjectActivity
      if (toSubjectActivity.indexOf("pages/xxshop/") > -1) {
        toSubjectActivity = toSubjectActivity.split('/pages')[0]
      }
      const {
        isFromHomeDelivery = false, activityID
      } = JSON.parse(toSubjectActivity)
      // 进入专题
      status && this.openSubjectActivity(activityID, isFromHomeDelivery)
    } else if (options.toPage && options.toPage === 'category') {
      //判断是否展示好友推荐
      const {
        status = false
      } = await this.selectedStoreHandle({
        ...options,
        sence
      })
      // 进入分类
      status && this.selectedToCategory(options)
    } else if (options.to) {
      return !!this.goOtherPage(options)
    } else if (options.bgxxStoreID) {
      // 进入首页带门店id
      const {
        bgxxStoreID: storeID = -1
      } = options;
      (storeID !== -1 && storeID !== 'undefined') && this.selectedStoreHandle({
        ...options,
        sence
      })
    }
    return true
  },
  /**
   * 进入综合专题
   */
  openSubjectActivity(activityID, isFromHomeDelivery) {
    app.globalData.prePageName = 'subjectActivity'
    wx.navigateTo({
      url: `/bgxxShop/pages/subjectActivity/index?activityID=${activityID}&isFromHomeDelivery=${isFromHomeDelivery}`
    })
  },
  /**
   * 商城跳转
   * @param {Object} options
   */
  goOtherPage(options) {
    let url = ''
    if (options.to === 'orderDetail') {
      app.globalData.prePageName = 'orderDetail'
      url = '/bgxxUser/pages/orderDetail/index'
      if (options.isPay) {
        // 已支付订单详情页
        url += `?orderID=${options.payOrderId}&isPay=${options.isPay}`
      } else {
        // 未支付订单详情页
        url += `?orderID=${options.orderID}`
      }
    } else if (options.to === 'complaintsDetail') {
      // 投诉退款订单详情页
      app.globalData.prePageName = 'complaintsDetail'
      url = `/bgxxUser/pages/complaints/complaintsDetail/index?goodsOrderID=${options.goodsOrderID}`
    };
    !!url && wx.navigateTo({
      url
    })
    return url
  },
  initData(onlyShowData) {
    this.setCityInfo()
    this.setUserInfo()
    this.setData({
      canScrollY: true
    })
    if (!this.data.cityInfo.cityId) {
      return
    }
    if (!onlyShowData) {
      // 仅展示数据则不跳转二级页面
      this.checkNavigatePage()
      freshMetro.init()
    }
  },
  // 检测城市状态
  checkCityStatus() {
    const {freshFirstEntry, mallCityChange } = app.globalData
    if (freshFirstEntry || mallCityChange) {
      app.globalData.freshFirstEntry = false
      this.resetData()
      if (!this.data.cityInfo.cityId) {
        this.setData({
          loadFinished: true
        })
        return CITY_STATUS.error // 城市异常
      }
      return CITY_STATUS.change // 城市改变
    }
    return CITY_STATUS.normal // 城市未变化
  },
  /**
   * 设置城市数据
   */
  setCityInfo() {
    const {
      cityID,
      storeID = '-1',
      cityName = '',
      cityCode,
      storeCode
    } = app.globalData.bgxxCityInfo || {}
    const {
      cityName: currCityName
    } = wx.getStorageSync('userCurrLoca') || {}
    this.setData({
      cityInfo: {
        cityCode,
        cityId: cityID,
        storeId: storeID,
        cityName: cityName || currCityName
      },
      storeCode
    })
  },
  /**
   * 重置页面数据
   */
  resetData() {
    this.setData({
      waterfall: {
        isFixed: false,
        ...this.data.waterfall,
        tabIndex: 0,
        showTabIndex: 0,
        loading: false,
        finished: false,
        lastFinished: false,
        reachBottom: false,
        releaseToNext: false
      },
      newCustomerGoods: [],
      floating: {},
      hotBanner: {},
      tabList: [],
      goodsList: [],
      oneBuyList: [],
      metroBannerList: [],
      'contentRefresh.scrollInto': 'scroll-top-0',
      'scrollAnimate.scrollAnimate': true
    })
    Object.assign(this._data, {
      page: {
        pageNum: 0
      },
      goodsList: [],
      scrollTop: 0,
      waterfallTop: 0,
    })
  },
  /**
   * metro/banner/品牌跳转
   */
  bannerToSkip(event) {
    const {
      item = {}, src = '', index = 0
    } = event.type === 'tap' ? event.currentTarget.dataset : event.detail.currentTarget.dataset
    this.configBannerToSkip(item)
    let codeIndex = ('' + index).padStart(3, '0')
    let nameIndex = ('' + index).padStart(2, '0')
    let props = {
      banner_id: item.id,
      banner_name: item.name,
      screen_type:'次日达',
      banner_type:'轮播图'
    }
    if (src === 'banner') { // 轮播banner
      this.reportSensor({
        element_code: `1130001${codeIndex}`,
        element_name: `轮播banner${nameIndex}`,
        element_content: `轮播banner${nameIndex}`,
        ...props
      })
    }
    if (src === 'brand') { // 服务说明
      this.reportSensor({
        element_code: '1130000004',
        element_name: '服务说明',
        element_content: '服务说明',
        banner_name: item.name
      })
    }
    if (src === 'metro') { // metro
      if (item.openValue === 'allCate') {
        sensors.track(sensors.MPClick, 'freshMetroAllCate')
        return
      }
      this.reportSensor({
        element_code: `1130002${codeIndex}`,
        element_name: `Metro${nameIndex}`,
        element_content: `Metro${nameIndex}`,
        ...props
      })
    }

    if (src === 'hotBanner') {
      sensors.track('MPClick', 'bgxxHomeHotBanner', {
        ...props
      })
    }
    if (src === 'waterfall') {
      sensors.track('MPClick', 'bgxxHomeWaterFallFlowAdvert', {
        ...props
      })
    }
    if (src === 'floating') {
      sensors.track('MPClick', 'bgxxHomeFloating', props)
    }
  },
  /**
   * @description 新广告位跳转
   * @param {*} e
   */
  bannerToPage(e) {
    const { item, src, index } = e.detail.currentTarget.dataset
    const codeIndex = ('' + index).padStart(3, '0')
    activity.toActivityPage(item)
    const props = {
      banner_id: item.id,
      banner_name: item.name,
      screen_type:'次日达',
    }
    if (src === 'split') { // 切分banner
      const pos = ['', '左上', '右上', '左下', '右下']
      this.reportSensor({
        element_code: `1130003${codeIndex}`,
        element_name: `${pos[index]}角切分banner`,
        element_content: `${pos[index]}角切分banner`,
        ...props
      })
    }
  },
  toDetail({
    goodsId = '',
    goodsSn = '',
    origin = '',
    fromShareDetail = false
  }) {
    app.globalData.prePageName = 'goodDetail'
    wx.navigateTo({
      url: `/bgxxShop/pages/goodDetail/index?toDetail=${JSON.stringify({ goodsID:goodsId, goodsSn, origin, fromShareDetail })}`
    })
  },
  /**
   * @description 获取metro
   */
  async getMetroList(params, onlyShowData) {
    try {
      if (!onlyShowData) this.compFetchFinish({ detail: { status: 'pending', name: 'metro-list' } })
      const {
        bgxxCityInfo: {
          cityCode
        } = {}
    } = app.globalData
      const result = await app.api.getMetroListRequest({...params, cityCode })
      this.setData({
        'metroBannerList': result.data
      })
    } catch(err) {
      this.setData({
        'metroBannerList': []
      })
    }
  },
  /**
   * serverless获取热门推荐商品列表recommendGoodsMoudelList
   * @param {Object} data
   */
  async getRecommendGoodsMoudelList() {
    try {
      const {
          bgxxCityInfo: {
            cityCode,
            deliveryCenterCode,
            storeCode
          } = {}
      } = app.globalData
      const recommendParams = {
        cityCode,
        deliveryCenterCode,
        storeCode
      }
      const {
        data: {
          goodsList = [],
          moduleSubTitle,
          moduleTitle
        } = {}
      } = await app.api.getHotRecommendList(recommendParams) // 新接口

      const recommendGoodsList = await new FreshGoods({
        storeCode,
        cityCode,
        deliveryCenterCode
      }).getGoodsComplateInfoList(goodsList, {
        filterSaleStatus: true, // 过滤已下架
        sortSoldOut: true,
        filterGift: true
      })
      return {
        recommendGoodsList,
        moduleSubTitle,
        moduleTitle
      }
    } catch (error) {
      console.error(error)
    }
  },
  /**
   * 上拉触底事件
   */
  onReachBottom() {
    this.selectComponent('#waterfallGoods').data['tabsList'] && this.selectComponent('#waterfallGoods').onReachCallBack()
  },
  /**
   * 分享转发
   */
  onShareAppMessage() {
    const {
      storeID = -1
    } = app.globalData.bgxxCityInfo
    return {
      title: '今天买明天到 水果更多更省更新鲜',
      path: `/pages/xxshop/index/index?bgxxStoreID=${storeID}`,
      imageUrl: 'https://resource.pagoda.com.cn/dsxcx/images/d2a707ba91a7dab5ab4a89b337a1e907.png'
    }
  },
  /**
   * 回到顶部
   */
  handleBackTop() {
    this.reportSensor({
      element_code: '1130000006',
      element_name: '回顶部',
      element_content: '回顶部'
    })
    const scrollAnimate = this.data.contentRefresh.scrollAnimate
    scrollAnimate || this.setData({
      'contentRefresh.scrollAnimate': true
    })
    setTimeout(() => {
      this.setData({
        'contentRefresh.scrollInto': 'scroll-top-0'
      })
    }, scrollAnimate ? 0 : 100)
  },
  /**
   * 页面滚动事件 加上节流
   */
  onContentScroll: util.throttle(function (e) {
    // console.log(e)
    const hiddenBackTopFlag = e.detail.scrollTop < this._data.windowHeight
    const {
      hiddenBackTop
    } = this.data
    // 立下flag 防止重复赋值，造成真机卡顿
    if (hiddenBackTop && !hiddenBackTopFlag) {
      this.setData({
        hiddenBackTop: false
      })
    }
    if (hiddenBackTopFlag && !hiddenBackTop) {
      this.setData({
        hiddenBackTop: true
      })
    }
    this.setOperateAnimation()
  }, 50),
  // 设置顶部导航栏背景色
  setNavBarBg(event) {
    // console.log(event)
    // if (event.isScrollTop === isChangeNavBg) {
    //   return
    // }

    // isChangeNavBg = event.isScrollTop
    // if (event.isScrollTop) {
    //   this.setNavBarBgData(this.data.topPicUrl, this.data.middlePicUrl)
    // } else {
    //   this.setNavBarBgData()
    // }
  },
  createNavBgAnimation: function() {
    const animation = wx.createAnimation({
      duration: 2000, // 动画时长
      timingFunction: 'ease' // 动画缓动方式
    });

    animation.opacity(0).step(); // 设置遮罩层透明度从0到1（这里假设初始为0，需要在样式里指定）
    this.setData({ // 更新数据以应用动画样式
      navBgAnimation: animation.export(),
    });
    setTimeout(( ) => {
      animation.opacity(1).step(); // 设置遮罩层透明度从0到1（这里假设初始为0，需要在样式里指定）
      this.setData({ // 更新数据以应用动画样式
        navBgAnimation: animation.export(),
      });
    },2000)
  },
  // 瀑布流商品吸顶时将商品内容滚动到合适区域
  scrollWaterfallTop() {
    if (!this.data.waterfall.isFixed) {
      return
    }
    const scrollAnimate = this.data.contentRefresh.scrollAnimate
    scrollAnimate && this.setData({
      'contentRefresh.scrollAnimate': false
    })
    setTimeout(() => {
      this.setData({
        'contentRefresh.scrollInto': 'scroll-flag-waterfall',
      })
    }, scrollAnimate ? 100 : 0)
  },

  /**
   * 创建悬浮侧边栏动画实例
   */
  createOperateAnimation() {
    this._data.floatingAni = wx.createAnimation({
      duration: 700,
      timingFunction: 'ease'
    })
  },

  setOperateAnimation() {
    const getAnimationData = (status = 0) => {
      if (this._data.operateMoveStatus === status) {
        return
      }
      this._data.operateMoveStatus = status
      this._data.floatingAni.right(!!status ? '-61rpx' : '0').step({
        delay: !!status ? 0 : 1300
      })
      this.setData({
        floatingAni: this._data.floatingAni.export()
      })
    }
    if (!this._data.operateMoveStatus) {
      getAnimationData(1)
    }
    clearTimeout(this._data.operateMoveTimer)
    this._data.operateMoveTimer = setTimeout(() => {
      getAnimationData()
    }, 400)
  },
  enterSearch(event) {
    app.globalData.prePageName = 'search'
    wx.navigateTo({
      url: `/bgxxShop/pages/search/index?orderType=${ORDER_TYPE.FRESH}&source=次日达首页`
    })
    sensors.clickReport('bgxxShopToSearch')
  },
  navBarTopIsShow (ev) {
    // ev.detail => intersectionRatio
    // 不等于0
    this.setData({
      hideStickNav: !!ev.detail
    })
  },
  /**
   * 获取新客专享商品
   */
  async getNewCustomerGoods(params) {
    let newCustomerGoods = []
    // 判断是否是新用户 Y 新客户，N 老客户 如果是新用户再调用新客专享方法
    const isNewCustomer = await checkIsXMDXNewCustomer()
    if (!!params.cityCode && isNewCustomer === 'Y') {
      try {
        const res = await app.api.getNewCustomerGoods(params)
        newCustomerGoods = (await new FreshGoods({
          ...params,
        }).getComplateGoodsList(!!res && !!res.data ? res.data : [], {
          filterSaleStatus: true, // 过滤已下架
          filterGift: true, // 过滤赠品
          filterStock: true, // 没库存不展示
        })).sort((a, b) => a.retailPrice - b.retailPrice)
      } catch (error) {
        // console.log('newCustomerGoods error', error)
      }
    }
    this.setData({
      newCustomerGoods
    })
  },
  reportAddGood(item) {
    let detail = item.type === 'addCart' ? item.detail : item
    let params = {
      code: '1130004001',
      content: '首页推荐商品加入购物车'
    }
    if (detail.pageOperateContent === 'newCustomerArea') {
      params = {
        code: '1130005001',
        content: '新人专区商品加入购物车'
      }
    }
    this.reportSensor({
      element_code: params.code,
      element_name: params.content,
      element_content: params.content,
      spu_id: detail.spuNumber || '',
      SKU_ID: detail.number || '',
      ...getBgxxSaleReportInfo({
        goodsObj:item,
        reportNum:true,
        goodsActivityObj:this.data.goodsActivityObj
      })
    })
  },
  beforeAddCart(item) {
    const clickType = item.currentTarget.dataset.fallwater || ''
    if (clickType === "fallwater") { // 瀑布流商品点击
      sensors.track('MPClick', 'bgxxHomeWaterFallGoodsAdd')
    }
    this.addCart(item, {
      beforeRequest: this.reportAddGood
    })
  },
  openDetail(item) {
    let detail = item.type === 'openDetail' ? item.detail : item
    const clickType = item.currentTarget.dataset.fallwater || ''
    this.reportSensor({
      element_code: '1130004002',
      element_name: '首页推荐商品商品图',
      element_content: '首页推荐商品商品图',
      spu_id: detail.spuNumber || '',
      SKU_ID: detail.number || ''
    })
    if (clickType === "fallwater") { // 瀑布流商品点击
      sensors.track('MPClick', 'bgxxHomeWaterFallGoodsClick')
    }
  },
  clickNewCustomerGoods(event) {
    const {
      item = {}
    } = event.detail.currentTarget.dataset
    const {
      eshopGoodsId,
      goodsSn,
      goodsName
    } = item
    this.reportSensor({
      element_code: '1130005002',
      element_name: '新人专区商品商品图',
      element_content: '新人专区商品商品图',
      SKU_ID: goodsSn || '',
      SKU_Name: goodsName || ''
    })
    this.toDetail({
      goodsId: eshopGoodsId,
      goodsSn: goodsSn
    })
  },
  /**
   * 新客添加购物车
   */
  addNewCustomerGoods(event) {
    const {
      item = {}
    } = event.detail.currentTarget.dataset
    item.pageOperateContent = 'newCustomerArea'
    this.addCart({
      detail: {
        event,
        goodsData: item,
        type: 1
      }
    }, {
      beforeRequest: this.reportAddGood
    })
  },
  orderToSkip(event) {
    const {
      goodsCount = null, orderId: payOrderID
    } = event.currentTarget.dataset
    sensors.track('MPClick', 'bgxxHomeOrderToSkipTips')
    if (goodsCount > 1) {
      // 多个商品跳转至订单列表页
      // 跳转订单页次日达页面（记录上一页面名称,供跳转tab时使用）
      app.globalData.prePageName = 'selfOrderModule'
      wx.navigateTo({
        url: '/userB/pages/orderList/index',
      })
    } else {
      // 单个商品跳转到订单详情页
      wx.navigateTo({
        url: `/bgxxUser/pages/orderDetail/index?orderID=${payOrderID}&isPay=true`
      })
    }
  },
  // 跳转素生鲜
  goVip() {
    jumpH5Vip()
  },
  //阻止弹出层滑动事件，空函数，不做任何处理
  onPreventTouchMove() {},
  //打开弹窗
  openPopUp: function (data) {
    this.setData({
      isShowPopUp: true
    })
    hasLoaded = true;
    //请求弹窗时间
    wx.setStorageSync('triggerTime', new Date().getTime())
    this.reportBannerExpose(data)
  },
  reportBannerExpose(data){
    const { popupContent = [] } = data
    const { activityID = '',name='' } = popupContent[0] || {}
    sensors.track('banner_show', '', {
      screen_name:'首页',
      screen_type:'次日达',
      screen_code:'11300',
      banner_id:String(activityID),
      banner_name:name,
      banner_type:'首页弹窗'
    })
  },
  /**
     * 监听焦点图出现在视窗内
     */
   hotBannerObserver (data) {
    this._data.hotBannerObserver && this._data.hotBannerObserver.disconnect()
    const hotBannerObserver = wx.createIntersectionObserver(this)
    hotBannerObserver.relativeToViewport().observe('#hot-banner', res => {
      // 焦点图曝光埋点
      if (res.intersectionRatio !== 0) {
        const { id ='',name = '',bannerID = ''} = data
        sensors.track('banner_show', '', {
          screen_name:'首页',
          screen_type:'次日达',
          screen_code:'11300',
          banner_id:String(id || bannerID),
          banner_name:name,
          banner_type:'首页焦点广告位'
        })
      }
    })
    this._data.hotBannerObserver = hotBannerObserver
  },
  //校验时间差
  imspectionTime() {
    const triggerTime = wx.getStorageSync('triggerTime');
    let timeGap = true;
    if (triggerTime) {
      timeGap = parseInt((new Date().getTime() - triggerTime) / 1000 / 60) >= this._data.showPopupTime;
    }
    return timeGap;
  },
  // 隐藏弹窗
  hidePopup(e) {
    this.setData({
      isShowPopUp: false
    })

    // 点击关闭弹窗上报神策
    if (e !== void 0) {
      const type = this.data.popUpData.popupType
      if (Number(type) === 1) {
        sensors.clickReport({
          element_code: '1130019002',
          element_name: '关闭红包雨弹窗',
          element_content: '关闭普通弹窗',
          blockName: '普通弹窗',
          blockCode: '1130018',
        })
      } else if (Number(type) === 2) {
        sensors.clickReport({
          element_code: '1130019002',
          element_name: '关闭红包雨弹窗',
          element_content: '关闭普通弹窗',
          blockName: '普通活动弹窗',
          blockCode: '1130019',
        })
      }
    }
  },
  goDetailPage(event) {
    this.configBannerToSkip(event.currentTarget.dataset.item, true)
    // 点击弹窗上报神策
    this.reportSensor({
      element_code: '1130018002',
      element_name: '点击普通弹窗',
      element_content: '点击普通弹窗',
      screen_type:'次日达',
      banner_type:'首页弹窗'
    })
    const { id, name } = this.data.popUpData
    sensors.adClick({
      banner_id: id,
      banner_name: name,
      Position: 0,
      element_code: 1130018001,
      subclass: '次日达普通弹窗'
    })
  },
  jumpDetailPage(e) {
    let {
      coupon
    } = e.currentTarget.dataset
    app.globalData.prePageName = 'foodsModule'
    let url = '/bgxxShop/pages/category/index' // 优惠券列表
    let {
      couponWay,
      couponDetailCode
    } = coupon
    if (couponWay !== '5') {
      // 优惠方式 1：满减 2：满折 3：立减 4：立折 5：免运费
      // 优惠券类型 1线上,2门店,3通用,4单品
      // 免运券跳转优惠券列表
      // 非免运券  其他优惠券跳适用商品页
      url = `/bgxxShop/pages/couponGoods/index?couponCode=${couponDetailCode}`
    }
    if (!!url) {
      wx.navigateTo({
        url
      })
    } else {
      this.hidePopup()
    }
    // 点击弹窗上报神策
    const { id, name } = this.data.popUpData
    this.reportSensor({
      element_code: '1130019002',
      element_name: '点击红包雨弹窗',
      element_content: '点击红包雨弹窗',
      screen_type:'次日达',
      banner_type:'首页弹窗'
    })
    sensors.adClick({
      banner_id: id,
      banner_name: name,
      Position: 0,
      element_code: 1130019001,
      subclass: '次日达红包弹窗'
    })
  },
  /**
   * user-guide 组件分发事件
   */
  tapUserGuide() {
    if (this._data.resolveUserGuide) {
      this._data.resolveUserGuide()
      this.setData({
        showUserGuide: false,
        canScrollY: true
      })
      wx.showTabBar()
    }
  },
  // 首页弹窗
  async getIndexDialog() {
    try {
      const showPopup = this.imspectionTime(); //校验时间距离展示上次是否超过四小时
      if (!showPopup) return; //不超过四小时 return
      const { customerID } = app.globalData
      const { cityCode: organizationCode = '' } = this.data.cityInfo
      const params = {
        customerID: customerID || -1,
        organizationCode,
        storeCode: this.data.storeCode
      }
      const {
        data
      } = await app.api.getBgxxIndexDialog(params)
      // } = await app.api.getBgxxIndexDialog(customerID || -1, cityId, storeId || -1)

      this.setData({
        popUpData: data
      })
      if (!!data.popupContent && !!data.popupContent.length) {
        // 弹窗类型为红包雨 而且未登录的情况下 不弹
        if (String(data.popupType) === '2' && !app.checkSignInsStatus()) return;
        this.openPopUp(data)
        sensors.adExposure({
          banner_id: data.id,
          banner_name: data.name,
          Position: 0,
          element_code: String(data.popupType) === '2' ? 1130019001 : 1130018001,

        })
      }
    } catch (error) {}
  },
  // 获取第二步数据: 获取心享专区商品,热点banner/floating,瀑布流tabs, 返回前在各函数已将有关数据处理完毕
  async initStep2(userInfo, isCityChange = true, onlyShowData = false) {
    const { selectAddressInfo:{ cityCode = '', deliveryCenterCode = '' } = {}} = wx.getStorageSync('bgxxSelectLocateInfo') || {}
    // let p1 = this.getNewCustomerGoods({
    //   customerID: userInfo.customerID,
    //   cityCode, deliveryCenterCode,
    //   storeCode: this.data.storeCode
    // })
    // let p2 = this.getOrderInfo(userInfo)
    let p4 = this.getNewUserCoupons() // 新客券包
    let promiseArray = [p4]
    // 仅展示数据时不展示弹窗
    if (!hasLoaded && !onlyShowData) {
      this.handleAllDialog()
    }
    if (isCityChange) { // 城市发生改变才需要刷新的请求
      let pc3 = this.getWaterfallTabs(userInfo)
      promiseArray = [...promiseArray, pc3]
    }

    await Promise.all(promiseArray)
  },
  // 获取热点banner/floating
  async getHotBannerAndFloating(params) {
    const res = await app.api.getBgxxHotBannerAndFloating(params)
    let {
      floating = [],
      hotBanner = []
    } = res.data || {}
    this.setData({
      floating: floating[0],
      hotBanner: hotBanner[0] || {}
    })
    const { id = '' } = this.data.hotBanner
    if(id){
      if (this._data.initedRect) return
      wx.nextTick(() => {
        this.hotBannerObserver(this.data.hotBanner)
        this._data.initedRect = true
      })
    }
  },
  // 获取瀑布流商品tab
  getWaterfallTabs(params) {
    return new Promise(resolve => {
      let firstTab = waterFallStore.tabbarList[0] || {
        floorNum: -1
      }
      this.setData({
        tabList: waterFallStore.tabbarList,
        curFloorNum: firstTab.floorNum || -1
      })
      this._data.firstTabIndex = firstTab.floorNum || -1
      resolve()
    })
  },
  // 获取首页的待自提订单信息
  getOrderInfo(params) {
    return new Promise(resolve => {
      app.api.getBgxxOrderInfo(params).then(res => {
        this.setData({
          selfOrderList: res.data
        })
        resolve(res.data)
      }).catch(error => {
        resolve()
      })
    })
  },
  // 页面初始化3: 获取瀑布流第一个tab下商品,计算瀑布流商品高度,设置顶部栏颜色,监听瀑布流触底
  initStep3() {
    //
  },
  // 页面初始化4: 获取次日达优惠券数量
  async initStep4({ customerID }) {
    const { data: { totalNum = 0 } } = await app.api.getCouponCountByBiz({customerID, applicableBizTypes: [4], notOnlyApplicableChannels: [10000]})
    // 查询及时达优惠券数量
    this.setData({
      couponNum: totalNum
    })
  },
  //限购弹窗点击隐藏
  closeOnePouop() {
    sensors.track('MPClick', 'bgxxHomeOneBuyGoodsPopupClose')
    this.setData({
      showOnePopup: false,
    })
  },
  /**
   * 超限购弹窗组件触发更新购物车数量
   */
  updateCartNum(e) {
    const {
      cartCount,
      type = ''
    } = e.detail;
    if (!!type) {
      const sensorsKey = {
        '1': 'bgxxHomeOneBuyGoodsPopupAddCart',
        '2': 'bgxxHomeOneBuyGoodsPopupDelCart'
      } [type]
      sensors.track('MPClick', sensorsKey)
    }
    this.setData({
      cartCount: cartCount
    })
  },
  /**
   * 刷新一元购列表
   */
  refreshOneBuyList() {
    const {
      userInfo = {}
    } = this._data;
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh(e) {
    const triggered = 'contentRefresh.triggered'
    this.setData({
      [triggered]: true
    })
    // 保证状态栏文字颜色是黑色
    // this.setNavBarColor({
    //   detail: {
    //     scrollTop: 1
    //   }
    // })
    const refreshStart = Date.now()
    Promise.all([
      this.onLocateReady({ isRefresh: true })
    ]).then(() => {
      console.log('111')
      this.selectComponent('#waterfallGoods').data['tabsList'] && this.selectComponent('#waterfallGoods').inintWaterfalltabbar()
      setTimeout(() => {
        this.setData({
          [triggered]: false,
        })
        // 刷新完毕恢复状态栏颜色
        // this.setNavBarColor({
        //   detail: {
        //     scrollTop: 0
        //   }
        // })
      }, Math.max(0, 1500 - (Date.now() - refreshStart)));
    })
  },
  /**
   * 图片加载成功
   */
  imgLoadSuccess() {
    this.setData({
      showClose: true
    })
  },
  onUnload() {
    this.storeBindings.destroyStoreBindings()
    if (this._observer) this._observer.disconnect()
    this._data.hotBannerObserver && this._data.hotBannerObserver.disconnect()
    app.event.off('refreshFreshNewUserInfo', this.refreshFreshNewUserHandle)
    this.setDataToCache()
  },
  /**
   * 页面下拉方法
   */
  contentScroll(e) {
    let {
      detail: {
        scrollTop
      }
    } = e
    if (scrollTop < 110) { // 设置阈值 避免频繁调用setData
      this.setData({
        scrollTop: 110 - scrollTop
      })
    }
  },
  /**
   * 色值切换
   */
  onColorChanged(e) {
    const {
      detail: {
        color
      }
    } = e
    this.setData({
      btnColor: color
    })
  },
  /**
   * 获取新人券包
   */
  async getNewUserCoupons() {
    if(!this._data.needRefreshNewUserInfo) {
      return
    }
    this._data.needRefreshNewUserInfo = false
    const {
      customerID
    } = app.globalData
    try {
      const couponsData = await app.api.getNewUserCoupons({
        customerID: customerID || '-1'
      })
      const {
        specialAreaCoupon: {
          couponList = []
        } = {}
      } = couponsData.data || {}
      this.setData({
        newUserCoupons: couponList
      })
    } catch (error) {
      this.setData({
        newUserCoupons: []
      })
    }
  },
  /**
   * 获取新品推荐
   */
  async getNewGoodsRecommend() {
    try {
      const {
        selectAddressInfo: {
          cityCode,
          deliveryCenterCode
        }
      } = bgxxStore.bgxxStoreInfo
      const result = await app.api.getNewGoodsRecommend({
        organizationCode: cityCode,
        channelId: 10,
        pageSize: 16,
        pageNum: 1,
        deliveryCenterCode,
        storeCode: this.data.storeCode
      })

      let list = result.data.list.map( item => {
        return {
          mainImgUrl: item.mainImgUrl,
          storageMethod: item.storageMethod,
          retailPrice: item.retailPrice,
          specialInfo: item.specialInfo
        }
      })
      this.setData({
        newGoodsRecommendList: util.arraySplite2Arry(list, 4, 4),
        ifShowNewRcommend: list.length >= 4
      })
    } catch (error) {
      this.setData({
        newGoodsRecommendList: [],
        ifShowNewRcommend: false
      })
    }
  },
  /**
   * 获取品牌专栏
   */
  async getBrand(params) {
    try {
      const brandResult = await app.api.getPagodaBrand({
        organizationCode: 0,
        customerID: params.customerID,
        storeCode: this.data.storeCode
      })
      const {
        data: {
          adResourceList
        }
      } = brandResult
      this.setData({
        pagodaBrand: adResourceList[0]
      })
    } catch (error) {}
  },
  /**
   * 获取轮播banner
   */
  async getSwiperBanner(params) {
    try {
      const swiperBannerResult = await app.api.getSwiperBanner({
        ...params
      })
      const {
        data = []
      } = swiperBannerResult
      const salesPromotionObj = data.find(item => item.isSalesPromotion === 'Y') || {}
      const { picDomain } = this.data
      const backgroundBelowUrl = salesPromotionObj && salesPromotionObj.backgroundBelowUrl ? picDomain + salesPromotionObj.backgroundBelowUrl : ''
      const { statusBarHeight } = wx.getStorageSync('systemInfo') || {}
      if (Object.keys(salesPromotionObj).length > 0) {
        this.setData({
          topBannerList: [salesPromotionObj],
          // salesPromotionObj,
          backgroundBelowUrl,
          backgroundTopHeight: statusBarHeight + 'px'
        })
      } else {
        this.setData({
          topBannerList: data,
          bgColorList: data.length ? data.map((item) => {
            return item.colorCode
          }) : [],
          salesPromotionObj: {},
          backgroundBelowUrl: ''
        })
      }
    } catch (error) {}
  },
  /**
   * 大图模块进入视图延迟加载 暂时保留
   */
  async getRecommendGoodsMoudelListHandle() {
    const {
      recommendGoodsList,
      moduleSubTitle,
      moduleTitle
    } = await this.getRecommendGoodsMoudelList()
    // 组合品商品信息查询
    // combineGoods.setCache(recommendGoodsList)
    const hotGoodsList = recommendGoodsList.slice(0, 3)
    this.setData({
      hotModule: {
        moduleSubTitle,
        moduleTitle
      },
      hotGoodsList: hotGoodsList
    })
    this.getGoodsActivitys(hotGoodsList)

  },
  /**
   * 点击新客券包去逛逛
   */
  newUserCouponClick() {
    sensors.track('MPClick', 'bgxxHomeNewCustomerCardPackagedClick')
    // 有瀑布流商品 跳瀑布流
    if (!!this.data.goodsList.length) {
      const scrollAnimate = this.data.contentRefresh.scrollAnimate
      scrollAnimate && this.setData({
        'contentRefresh.scrollAnimate': false
      })
      setTimeout(() => {
        this.setData({
          'contentRefresh.scrollInto': 'scroll-flag-waterfall',
        })
      }, scrollAnimate ? 100 : 0)
    }
    // 跳品类页
    else {
      wx.navigateTo({
        url: '/bgxxShop/pages/category/index'
      })
    }

  },
  /**
   * 初始化广告位请求顺序
   */
  async initStep1(isCityChange, onlyShowData) {
    const {
      cityCode
    } = this.data.cityInfo
    // 广告位公共参数
    const commonParams = {
      organizationCode: cityCode,
      customerID: app.globalData.customerID || -1,
      storeCode: this.data.storeCode || '',
      cityCode
    }
    if (isCityChange) {
      await Promise.all([
        // this.getBrand(commonParams),
        // this.getSplitBanner(commonParams),
        // this.getRecommendGoodsMoudelListHandle(),
        // this.getNewGoodsRecommend(),
        // this.getMetroList(commonParams, onlyShowData),
        // this.getSwiperBanner(commonParams),
        this.getHotBannerAndFloating(commonParams),
        this.getBgxxSearchAd(),

        this.getBackgroundAd(commonParams), // 获取大促背景图
        this.getShowcaseBanner(commonParams), // 获取橱窗banner
        this.getWaistBanner(commonParams), // 获取腰部banneer
      ])
    }
    this.setData({
      loadFinished: true
    }, () => {
      if (!onlyShowData && isCityChange) freshMetro.metroRendered()
    })
    wx.nextTick(() => {
      wx.createIntersectionObserver(this)
      .relativeTo('#nav-bar-bottom')
      .observe('#search-box', (res) => {
        this.setData({
          searchBoxIsSticky: res.intersectionRatio !== 0
        })
      });
    })
  },
  /**
   * 选择门店(好友推荐)
   */
  async selectedStoreHandle(options) {
    // 3.7 不展示好友分享门店逻辑
    return {
      status: true
    }
  },
  /**
   * 去品类页
   * */
  selectedToCategory(options) {
    // cityCode相同才进入品类页
    if ((!options.cityCode) || (options.cityCode === app.globalData.bgxxCityInfo.cityCode)) {
      // 进入品类
      app.globalData.prePageName = 'category'
      wx.navigateTo({
        url: `/bgxxShop/pages/category/index?categoryID=${options.categoryID || ''}`
      })
    }
  },
  /**
   * 地址参数
   */
  async handleAddress(params) {
    try {
      const {
        cityName,
        lat,
        lon,
        address
      } = params
      const res = await app.api.checkBgxxIsSupportVip({
        cityName,
        lat,
        lon
      })
      const {
        city,
        supportSuperVipShop = 'N'
      } = res.data || {}
      if (supportSuperVipShop === 'Y' && !!city.cityID) {
        const {
          deliveryCenterCode,
          code: cityCode,
          cityID,
          cityName
        } = (city || {})
        const selectAddressInfo = {
          address,
          cityName,
          lat,
          lon,
          cityID,
          supportSuperVipShop: 'Y',
          deliveryCenterCode,
          cityCode
        }
        this.setData({
          selectedAddress: selectAddressInfo
        })
      } else {
        setTimeout(() => {
          app.showModalPromise({
            content: '当前城市未开通服务，请切换其他城市',
            confirmText: '我知道了',
          })
        }, 300)
      }
    } catch (error) {}
  },
  /**
   * 瀑布流滚动到顶部
   */
  scrollIntoView(e) {
    this.scrollWaterfallTop()
  },
  handleScrollIntoView(id, animation = true) {
    if (!id) {
      return
    }
    // 直接滚动到对应模块，无需动画过渡
    if (!animation) {
      this.setData({
        scrollIntoView: id
      })
      return
    }
    // 滚动到对应模块，使用动画过渡
    this.setData({
      scrollAnimation: true
    })
    setTimeout(() => {
      this.setData({
        scrollIntoView: id,
        scrollAnimation: false
      })
    }, 100)
  },
  /**
   * 获取商品标签
   * @param {*} e
   */
  reqGoodsActivitys(e) {
    const {
      allGoods
    } = e.detail
    const goodsList = allGoods.filter(good => good.id && good.goodsType !== 'ad')
    this.getGoodsActivitys(goodsList)
  },
  // 展示默认地址tips
  showDefaultLocateTips() {
    const addTipes = this.selectComponent('#address-tips')
    addTipes && addTipes.showNoAddressTipsHandle()
  },
  // 开启位置权限后重新定位
  restartLocate() {
    if (locateStore.useFreshDefaultLocation) {
      app.globalData.isGetBgxxCacheLocation = false
    }
  },
  onWaterfallInited() {
    this._data.waterfallInited.resolve()
  },
  /**
   * status: done-完成; pending: 请求中
   */
  compFetchFinish(ev){
    const { name, status, data } = ev.detail || {}
    ;({
      pending: () => {
        let r = null
        this._data.compFetchMap[name] = {
          p: new Promise(resolve => {
            r = resolve
          }),
          r
        }
      },
      done: () => {
        const { compFetchMap } = this._data
        if (!compFetchMap[name]) return
        const { r } = compFetchMap[name]
        r && r(data)

      }
    })[status]()
  },
  /**
   * 展示【所有分类】新人引导
   */
  async setAllCateUserGuide(){
    const allCateStorageKey = 'freshAllCateStorageKey'
    const hasShow = wx.getStorageSync(allCateStorageKey) || false
    if (hasShow) return
    const name = 'metro-list'
    const {p} = this._data.compFetchMap[name] || {}
    if (!p) return
    const data = await p
    // resolve之后清空
    this._data.compFetchMap[name] = void 0
    if (!data) return
    const { top, left, bottom, height, width, right } = data
    const zoomRatio = 1.2
    const zoneWidth = Math.max(height*zoomRatio, width*zoomRatio)
    const zoneTop = top - Math.abs(zoneWidth - height)/2
    const zoneLeft = left - Math.abs(zoneWidth - width)/2
    this.setData({
      canScrollY: false,
      pageScrollViewTop: 0
    }, () => {
      this.setData({
        showUserGuide: true,
        guideName: 'allCateGuide',
        guideZone: {
          top: zoneTop,
          left: zoneLeft,
          width: zoneWidth,
          height: zoneWidth,
          borderRadius: '50%'
        },
        guideTips: {
          imgUrl: ''
        }
      })
    })
    wx.setStorageSync(allCateStorageKey, true)
    await new Promise(resolve => {
      this._data.resolveUserGuide = resolve
    })
  },
  /**
   * @desc 处理次日达所有弹窗
   * 1. 新人引导弹窗
   * 2. 红包弹窗
   */
  async handleAllDialog () {
    await this.setAllCateUserGuide()
    this.getIndexDialog()
    this.setData({
      canScrollY: true
    })
  },
  tabbarIsFixed (event) {
    this.setData({
      'waterfall.isFixed': event.detail
    })
  },
  // 获取大促背景图
  async getBackgroundAd(params) {
    try {
      const result = await app.api.getBackgroundAdRequest({
        ...params
      })
      const {
        bottomPicUrl,
        topPicUrl,
        middlePicUrl,
      } = result.data
      if (!bottomPicUrl) {
        this.setNavBarBgData()
        this.setData({
          isHasSaleBg: false,
          bottomPicUrl: BOTTOM_PIC_URL,
          topPicUrl: TOP_PIC_URL,
          middlePicUrl: MIDDLE_PIC_URL,
        })
        return
      }
      const { picDomain } = this.data
      this.setData({
        saleBgObj: result.data,
        bottomPicUrl: picDomain + bottomPicUrl,
        topPicUrl: picDomain + topPicUrl,
        middlePicUrl: picDomain + middlePicUrl,
        isHasSaleBg: Boolean(middlePicUrl)
      })
      this.setNavBarBgData(this.data.topPicUrl, this.data.middlePicUrl)

    } catch(err){
      this.setNavBarBgData()
      this.setData({
        isHasSaleBg: false,
        bottomPicUrl: BOTTOM_PIC_URL,
        topPicUrl: TOP_PIC_URL,
        middlePicUrl: MIDDLE_PIC_URL,
      })
    }
  },
  setNavBarBgData(topPicUrl = TOP_PIC_URL, middlePicUrl = MIDDLE_PIC_URL) {
    this.setData({
      navBgImgStyle: `background-image: url('${topPicUrl}'), url(${middlePicUrl});`
    })
  },
  toActivityPage() {
    activity.toActivityPage(this.data.saleBgObj)
    const { id, name } = this.data.saleBgObj
    sensors.adClick({
      banner_id: id,
      banner_name: name,
      Position: 0,
      element_code: 1130021001,
    })
  },
  // 获取橱窗banner
  async getShowcaseBanner(params) {
    try {
      const result = await app.api.getShowcaseBannerRequest({
        ...params
      })
      this.setData({
        showcaseObj: result.data
      })
      this.getShowCaseList(result.data)
    } catch(err){
      this.setData({
        showcaseObj: {},
        showcaseList: []
      })
    }
  },
  async getShowCaseList(bannerObj) {
    const { openValue } = bannerObj
    if (!openValue) {
      this.setData({
        showcaseList: []
      })
      return
    }
    const { navPriority, goodsModuleList } = await new FreshSubject({ activityID: openValue}).getGoodsModule({})
    console.log(goodsModuleList)
    const showcaseList = []
    goodsModuleList.forEach( module => {
      const { goodsList, windowImg, modulePriority } = module
      if (goodsList.length > 0 && windowImg) {
        const { specialInfo = {}, goodsName, retailPrice, stock, goodsSn } =  goodsList[0]
        const specialPrice = specialInfo.price
        // 寻找比商品楼层高的下个导航栏再往前找
        const nextNavIndex = navPriority.findIndex((v) => v > modulePriority)
        const curNavPriority = [-1,0].includes(nextNavIndex) ? modulePriority : navPriority[nextNavIndex - 1]
        showcaseList.push({
          modulePriority: curNavPriority,
          windowImg,
          goodsName: goodsName,
          retailPrice: specialPrice || retailPrice,
          stock: stock,
          goodsSn,
        })
      }
    })
    const upGoodsList = [],downGoodsList = []
    showcaseList.forEach( goods => {
      if (goods.stock) {
        upGoodsList.push(goods)
      } else {
        downGoodsList.push(goods)
      }
    })
    this.setData({
      showcaseList: upGoodsList.concat(downGoodsList).slice(0, 6)
    })
    console.log(this.data.showcaseList)
  },
  // 获取腰部banner
  async getWaistBanner(params) {
    try {
      const result = await app.api.getWaistBannerRequest({
        ...params
      })
      this.setData({
        waistBannerObj: result.data
      })
    } catch(err){}
  },
  reportExposure() {
    const { id: saleBgId, name: saleBgName } = this.data.saleBgObj
    if (saleBgId) {
      sensors.adExposure({
        banner_id: saleBgId,
        banner_name: saleBgName,
        Position: 0,
        element_code: 1130021001,
      })
    }
  },
})
