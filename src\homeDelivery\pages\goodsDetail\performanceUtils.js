/**
 * 分享卡片性能优化工具类
 * 提供图片预加载、缓存管理、性能监控等功能
 */

// 图片缓存对象
const imageCache = new Map()
// 正在加载的图片Promise缓存
const loadingPromises = new Map()

/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  constructor(moduleName) {
    this.moduleName = moduleName
    this.timers = new Map()
  }

  /**
   * 开始计时
   * @param {string} label 计时标签
   */
  start(label) {
    this.timers.set(label, Date.now())
    console.log(`[${this.moduleName} Performance] ${label}开始 - 时间: ${this.timers.get(label)}`)
  }

  /**
   * 结束计时并输出耗时
   * @param {string} label 计时标签
   * @param {string} description 描述信息
   */
  end(label, description = '') {
    const startTime = this.timers.get(label)
    if (!startTime) {
      console.warn(`[${this.moduleName} Performance] 未找到计时器: ${label}`)
      return 0
    }
    
    const endTime = Date.now()
    const duration = endTime - startTime
    const desc = description ? ` - ${description}` : ''
    console.log(`[${this.moduleName} Performance] ${label}完成 - 耗时: ${duration}ms${desc}`)
    
    this.timers.delete(label)
    return duration
  }

  /**
   * 记录中间时间点
   * @param {string} label 计时标签
   * @param {string} checkpoint 检查点名称
   */
  checkpoint(label, checkpoint) {
    const startTime = this.timers.get(label)
    if (!startTime) {
      console.warn(`[${this.moduleName} Performance] 未找到计时器: ${label}`)
      return 0
    }
    
    const currentTime = Date.now()
    const duration = currentTime - startTime
    console.log(`[${this.moduleName} Performance] ${label} - ${checkpoint} - 耗时: ${duration}ms`)
    return duration
  }
}

/**
 * 图片缓存管理器
 */
export class ImageCacheManager {
  constructor() {
    this.maxCacheSize = 30 // 最大缓存图片数量
    this.cacheExpireTime = 20 * 60 * 1000 // 缓存过期时间：20分钟
  }

  /**
   * 预加载图片并缓存
   * @param {string} url 图片URL
   * @param {string} cacheKey 缓存键名
   * @returns {Promise<string>} 返回图片本地路径
   */
  async preloadImage(url, cacheKey = url) {
    const monitor = new PerformanceMonitor('ImageCache')
    monitor.start(`preloadImage_${cacheKey}`)

    // 检查缓存
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      monitor.end(`preloadImage_${cacheKey}`, '使用缓存')
      return cached
    }

    // 检查是否正在加载
    if (loadingPromises.has(cacheKey)) {
      console.log(`[ImageCache Performance] 图片正在加载中，等待完成: ${cacheKey}`)
      return await loadingPromises.get(cacheKey)
    }

    // 开始加载图片
    const loadPromise = this.loadImageWithRetry(url, 2)
    loadingPromises.set(cacheKey, loadPromise)

    try {
      const imagePath = await loadPromise
      // 缓存图片
      this.setToCache(cacheKey, imagePath)
      monitor.end(`preloadImage_${cacheKey}`, '加载完成')
      return imagePath
    } catch (error) {
      console.error(`[ImageCache Performance] 图片加载失败: ${cacheKey}`, error)
      throw error
    } finally {
      loadingPromises.delete(cacheKey)
    }
  }

  /**
   * 带重试的图片加载
   * @param {string} url 图片URL
   * @param {number} maxRetries 最大重试次数
   * @returns {Promise<string>}
   */
  async loadImageWithRetry(url, maxRetries = 2) {
    let lastError
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        const result = await this.loadSingleImage(url)
        if (i > 0) {
          console.log(`[ImageCache Performance] 图片重试加载成功: ${url}, 重试次数: ${i}`)
        }
        return result
      } catch (error) {
        lastError = error
        console.warn(`[ImageCache Performance] 图片加载失败，重试 ${i + 1}/${maxRetries}: ${url}`, error)
        
        if (i < maxRetries - 1) {
          // 等待一段时间后重试
          await this.delay(500 * (i + 1)) // 递增延迟
        }
      }
    }
    
    throw lastError
  }

  /**
   * 加载单个图片
   * @param {string} url 图片URL
   * @returns {Promise<string>}
   */
  loadSingleImage(url) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: url,
        success: (res) => resolve(res.path),
        fail: reject
      })
    })
  }

  /**
   * 从缓存获取图片
   * @param {string} cacheKey 缓存键名
   * @returns {string|null}
   */
  getFromCache(cacheKey) {
    const cached = imageCache.get(cacheKey)
    if (!cached) return null

    // 检查是否过期
    if (Date.now() > cached.expireTime) {
      imageCache.delete(cacheKey)
      return null
    }

    return cached.path
  }

  /**
   * 设置图片到缓存
   * @param {string} cacheKey 缓存键名
   * @param {string} imagePath 图片路径
   */
  setToCache(cacheKey, imagePath) {
    // 检查缓存大小，如果超过限制则清理最旧的缓存
    if (imageCache.size >= this.maxCacheSize) {
      this.cleanOldCache()
    }

    imageCache.set(cacheKey, {
      path: imagePath,
      expireTime: Date.now() + this.cacheExpireTime,
      createTime: Date.now()
    })
  }

  /**
   * 清理过期缓存
   */
  cleanOldCache() {
    const entries = Array.from(imageCache.entries())
    
    // 按创建时间排序，删除最旧的缓存
    entries.sort((a, b) => a[1].createTime - b[1].createTime)
    
    const toDelete = Math.ceil(this.maxCacheSize * 0.3) // 删除30%的缓存
    for (let i = 0; i < toDelete && i < entries.length; i++) {
      imageCache.delete(entries[i][0])
    }
    
    console.log(`[ImageCache Performance] 清理了 ${toDelete} 个旧缓存`)
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 清空所有缓存
   */
  clearCache() {
    imageCache.clear()
    loadingPromises.clear()
    console.log(`[ImageCache Performance] 已清空所有缓存`)
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      cacheSize: imageCache.size,
      loadingCount: loadingPromises.size,
      maxCacheSize: this.maxCacheSize
    }
  }
}

// 导出单例
export const imageCacheManager = new ImageCacheManager()

/**
 * 优化的Canvas转换函数
 * @param {Object} options 转换选项
 * @returns {Promise<string>} 临时文件路径
 */
export function optimizedCanvasToTempFilePath(options) {
  const monitor = new PerformanceMonitor('Canvas')
  monitor.start('canvasToTempFilePath')
  
  const defaultOptions = {
    quality: 0.8,
    fileType: 'jpg',
    ...options
  }
  
  return new Promise((resolve, reject) => {
    wx.canvasToTempFilePath({
      ...defaultOptions,
      success: (res) => {
        monitor.end('canvasToTempFilePath', `文件大小: ${res.tempFilePath}`)
        resolve(res)
      },
      fail: (err) => {
        monitor.end('canvasToTempFilePath', '转换失败')
        reject(err)
      }
    })
  })
}

export default {
  PerformanceMonitor,
  ImageCacheManager,
  imageCacheManager,
  optimizedCanvasToTempFilePath
}
