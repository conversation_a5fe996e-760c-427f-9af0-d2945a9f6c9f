/**
 * https://developers.weixin.qq.com/miniprogram/dev/devtools/ci.html
 */
const miniprogramCI = require('miniprogram-ci')
const config = require('../../src/utils/config')
const { privateKeyPath } = require('./const')
const { appId } = config
const projectConfigJson = require('../../project.config.json')
function newProject(basePath) {
  return new miniprogramCI.Project({
    appid: appId,
    type: 'miniProgram',
    projectPath: basePath, // 项目路径
    privateKeyPath, // 私钥的路径
    ignores: ['node_modules/**/*'],
  })
}

function handleSetting() {
  return Object.assign({}, projectConfigJson.setting, {
    // 有些配置与project.config.json不同
    minify: true,
    codeProtect: true,
    autoPrefixWXSS: true
  })
}

module.exports = {
  newProject,
  handleSetting
}
