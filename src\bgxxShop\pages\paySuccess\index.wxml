<!--pages/mall/paySuccess/index.wxml-->
<import src="/bgxxShop/template/suspenseCart/index.wxml"></import>
<view class="pay-success">
  <nav-bar></nav-bar>
  <view class="content-box">
    <view class="t-wap" style="background-image:url('{{bg_top_bg}}');" hover-class="none" hover-stop-propagation="false">
      <view class="pay-success-text">
        <view class="pay-success-tip">付款成功</view>
        <view class='pay-success-tip-text'>我们承诺 所有商品不好就退</view>
        <view class="period-purchase" wx:if="{{!!cycleBuyTip}}">
          <text>{{cycleBuyTip}}</text>
        </view>
        <view class="text-dash"></view>
        <view class="btn-wap" hover-class="none" hover-stop-propagation="false">
          <view class='btn-order-view' bindtap='toMallIndex'>继续逛逛</view>
          <view class='btn-order-view' bindtap='toOrder'>查看订单</view>
        </view>
        <image class="right-icon" mode="aspectFill" src="https://resource.pagoda.com.cn/dsxcx/images/e53bfa7aace975642d3cd4343a094ba5"></image>
      </view>

      <vip-tips class="vip_wrapper" change-vip-tips save-money="{{saveMoney}}">
        <view slot="left-icon" class="vip_wrapper-left-icon"></view>
      </vip-tips>
    </view>
    <view class="banner-box" wx:if="{{bannerInfo.pic}}" bindtap='skipGoodsDetail'>
      <image class="banner-img" mode="widthFix" src="{{picDomain + bannerInfo.pic}}"></image>
    </view>
    <!-- 推荐商品title -->
    <view wx:if="{{goodsList.length > 0}}" class='suggest-title' style="background-image: url('https://resource.pagoda.com.cn/group1/M18/37/B1/CmiLlGAKd2KAWpCWAAAn_VlMS2s898.png'); background-size:192rpx 60rpx">
      猜你喜欢
    </view>
    <!-- 推荐商品列表 -->
    <view class='suggest-goods-list'>
      <goods-preview
        class="goods-preview-double"
        wx:for="{{goodsList}}"
        wx:key="index"
        goodsData="{{item}}"
        styleType="{{styleConfig.verticalSmall}}"
        bindaddCart="paySuccessAddCart"
        bindopenDetail="openDetail"
        height-fill="{{true}}"
        goods-special
      ></goods-preview>
    </view>
    <view class="buttomTips">
      <image class="buttomTips-image" src="https://resource.pagoda.com.cn/group1/M21/56/C9/CmiLkGEMmXaAZpmQAAARZsOceyI923.png" mode="widthFix" />
    </view>
  </view>
  <float-cart show-float= "{{goodsList && goodsList.length > 0}}" bindclick="openCart"/>
  <template is="animateCart" data="{{movingBallInfo, picDomain}}"></template>
</view>

<common-loading />