
.payment-select {
  background-color: #fff;
}
.pay-item+.pay-item {
  border-top: 1rpx solid #f5f5f5;
}
.pay-item {
  display: flex;
  align-items: center;
  height: 110rpx;
  background-color: #fff;
  padding: 0 24rpx;
  font-size: 32rpx;
  justify-content: space-between;
}

.forbidPdPay{
  pointer-events: none;
  /* opacity: 0.5; */
}

.recharge {
  font-size: 28rpx;
}
.disable{
  opacity: 0.5;
}

.t-bottom{
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  color: #FF7387;
  background-color: #FEECEC;
  border-radius: 8rpx;
  display: inline-block;
}
.pay-name + .t-bottom{
  display: inline-flex;
  align-items: center;
  margin-left: 16rpx;
}
.red-right-icon{
  width: 12rpx;
  height: 12rpx;
  margin-left: 8rpx;
  margin-bottom: 2rpx;
}
.pay-name + .t-bottom>.red-right-icon{
  margin-top: 2rpx;
  margin-bottom: 0;
}
.red-right-icon-big{
  width: 20rpx;
  height: 20rpx;
}

.icon-pagodapay,
.icon-wechat,
.icon-unionpay {
  width: 44rpx;
  height: 44rpx;
  padding-right: 12rpx;
}

.pay-desc {
  flex: 1;
}
.forbid-text{
  font-size: 24rpx;
  color: #888
}

.remaining {
  font-size: 28rpx;
  color: #888;
  padding-left: 12rpx;
}

.pay-item radio {
  width: 44rpx;
  height: 44rpx;
}

.payment-header{
  display: flex;
  align-items: center;
  height: 96rpx;
  background-color: #fff;
}
.payment-header-title{
  color: #333;
  font-size: 30rpx;
  flex: 1;
  text-align: center;
  padding-right: 48rpx;
}
.flex {
  display: flex;
}
.align-center {
  align-items: center;
}
