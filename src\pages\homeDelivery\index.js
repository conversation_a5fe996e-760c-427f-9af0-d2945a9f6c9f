import { tabBarStore, bgyHomePageName } from '../../mixins/tabBarStore'
import forwardNavigate from './utils/forwardNavigate'
import { getNewUserGoods } from './utils/getNewUserGoods'
import locateAfterCheckCity from '../../mixins/locateAfterCheckCity';
import { changeTimelyChannel, generateShareAttr } from '../../utils/report/setup'
const commonObj = require('../../source/js/common').commonObj;
const util = require('../../utils/util.js')
const app = getApp();
const StateMachine = require('../../source/js/finiteStateMachine/index')
const locateMixin = require('../../mixins/locateMixin')
const vegetablesLocateMixin = require('../../mixins/vegetablesLocateMixin.js')
const sensors = require('../../utils/report/sensors')
const { jumpH5Vip } = require('../../utils/services/jumpBgxxVip')
const cartMinxins = require('../../mixins/cartMixin')
const ecmSurveys = require.async('../../sourceSubPackage/commonUtils/ecmSurveys/index')
const rankBarStore = require.async('../../sourceSubPackage/goods/rankBarStore')
import GOODSIMAGE from '../../source/const/goodsImage';
import { homeDeliveryStorageCache } from '~/utils/storageCache';
import { PageViewHandle } from '~/utils/report/pageView';
import { getTuringDeviceToken } from '../../service/userService'
import { defaultCustomerAvatar, defaultUnLoginAvatar } from '../../source/const/user';

const DEFAULT_SEARCH = '搜搜想吃的水果'

/**
 * @constant
 * @type {number}
 * @desc 场景值: 扫二维码进入
 */
const openByScanQRCode = 1011

// tabbar高度 px
const tabbarHeight = 48

const {
  /**及时达首页大促广告位缓存数据 */
  activityBgObj,
  /**及时达首页用户定位信息缓存 */
  addressInfo,
} = homeDeliveryStorageCache.getStorageData()
/**默认展示视图（存在缓存时直接展示内容，无缓存时展示骨架屏） */
const defaultCurrentView = activityBgObj ? 'content' : 'skeletonScreen'

Page({
  mixins: [ locateAfterCheckCity, locateMixin, vegetablesLocateMixin, cartMinxins, tabBarStore(bgyHomePageName)],
  _data: {
    showCustomModalResolve: null,
    options: {},
    isToPage: false,
    needRefresh: true, // 是否需要刷新页面商品信息
    needRefreshNewUserInfo: true, // 刷新用户信息
    navigatedToLogin: false, // 去过登录页
    refreshCompList: [], // 注册更新组件集合
    scrollIntoStatus: 0, // 瀑布流切换tab发生的隐藏滚动：0 不是，1 是
    userProtocolIsAgreed: false,
    fsmIsEnd: true, // 状态机是否执行完
    locateTipsResolve: null,
    pageProtocol: true,
    pageDialogFirst: {}, // 活动优惠券是否优先于新人券弹窗展示promise
    isToMemberCenter: false,
    // 缓存的新人优惠券
    tmpNewUserCouponList: []
  },
  data: {
    currentView: defaultCurrentView,
    storeCanDeliveryTipsWord: '',
    showCustomConfirmModal: false,
    defaultCustomerAvatar,  // 用户默认头像
    defaultUnLoginAvatar,  // 用户未登录默认头像
    isLogin: false, // 是否登录
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    defaultImgUrl: GOODSIMAGE.NO_STORE_BUT_CRY, // 缺省图
    showLayer: false,  // 引导添加我的小程序浮层页展示与否
    isShowOrderBar: false, // 是否展示心享会员条幅（没有订单流传时展示）
    isPageScrolling: false, // 页面是否滚动中
    mainViews: { // 定位缺省页配置
      noLocationAuth: 'noLocationAuth',
      noStore: 'content',
      noCity: 'content',
      content: 'content'
    },
    showMemberCode: false, // 展示会员码弹窗组件
    userInfo: {
      customerID: -1,
    }, // 用户信息
    addressInfo: addressInfo || {
      address: '',
      lat: '',
      lon: '',
      cityID: -1,
      storeID: -1,
      cityCode: '',
      storeCode: '',
      storeName: '',
      cityName: '',
      deliveryCenterCode: '',
      deliveryCenterId: -1,
      supportBToCService: false,
      supportStoreService: false
    }, // 地址信息
    showTitleLogoAnimation: true,
    specialAreaCouponList: [], // 新客礼包专区优惠券列表
    isShowSplitLine: false, // 是否展示切分banner与下午茶的分隔线
    searchGoodsPlaceholder: DEFAULT_SEARCH,
    refresher: {
      threshold: 45,
      triggered: false
    },
    freshCityCode: -1, // 生鲜推荐的定位城市code
    freshDeliveryCenterCode: '',
    /**支持b2c或及时达服务 */
    supportB2COrStore: true,
    /**b2c和全国送都不支持 */
    unSupportB2CAndStore: false,
    /**开通了全国送，但无全国送商品 */
    supportB2cButNoContent: false,
    showUpdateProtocol: false, // 是否显示协议更新弹窗
    hideBackTop: true, // 隐藏“回顶部”按钮
    isShowDialog: false, // 是否展示广告弹窗&红包雨弹窗
    fetchDialogData: false, // 广告弹窗&红包雨弹窗单纯获取数据
    couponNum: 0,
    isEshopNewCustomer: false,
    couponData: {},
    navBarHeight: 30,
    activityBgObj: activityBgObj || {},
    tabbarHeight,
    //  是否灰色主题
    isGrayTheme: false,
    canScrollY: true,
    /**搜索栏节点信息 */
    searchBarInfo: {
      /**搜索栏距离顶部距离 */
      top: 0,
    },
    // 是否展示了新人专享区域
    isShowNewGoodsArea: false,
    // 是否加载完成新人特价商品
    isLoadNewGoods: false,
  },
  onLoad: function (options) {
    changeTimelyChannel()
    const { setScancode, scene } = app.globalData
    if (setScancode && !util.isEmptyObject(options)) {
      Object.assign(app.globalData, {
        sceneCopy: scene,
        setScancode: false
      })
    }
    this.initFSM()
    console.log('onLoad', options)
    const { to } = options || {}
    this._data.isToMemberCenter = to === 'memberCenter'
    this._data.options = options || {}
    const { windowHeight = 667 } = wx.getStorageSync('systemInfo') || {}
    this._data.windowHeight = windowHeight
    app.event.on('refreshPageGoods', this.setRefreshGoodsParams)
    // 监听用户身份变更
    app.event.on('refreshNewUserInfo', this.refreshNewUserHandle)

    // 获取腾讯图灵设备token
    getTuringDeviceToken()
  },

  setRefreshGoodsParams() {
    this._data.needRefresh = true
  },
  // 刷新用户信息相关。退出登录、下单、取消订单涉及到用户身份变更触发
  refreshNewUserHandle() {
    // 清除缓存
    this._data.tmpNewUserCouponList = []
    // 重置新用户待获取状态
    this._data.needRefreshNewUserInfo = true
  },
  async onShow() {
   changeTimelyChannel()
    // 浏览页面上报神策
    PageViewHandle.addPageViewSensor({
      $url_query: JSON.parse(JSON.stringify(this._data.options))
    })
  },

  onUnload() {
    // reLaunch会触发tab页重新执行onload，会导致会员码弹窗弹第二次
    app.globalData.sceneCopy = void 0
    app.event.off('refreshPageGoods', this.setRefreshGoodsParams)
    app.event.off('refreshNewUserInfo', this.refreshNewUserHandle)
  },

  async pageContentHandle({ onlyShowData, isNavigate }) {
    const { currentView } = this.data
    if (currentView !== 'content') {
      return
    }
    this.updateUserInfo()
    if (this._data.needRefresh) {
      this.refreshAddressInfo()
      this.getSearchWord()
      onlyShowData || (this._data.needRefresh = false) // 第一次展示数据，下次仍需要刷新
    }
    // freshCityCode === -1 && this.freshRecommendLocate()
    // 3.5.1修改
    this.freshRecommendLocate()
    // 弹窗逻辑需要用到新客礼包的数据，要先请求
    await this.getBaseDataOfPage()
    // 1.状态机未结束，执行onLocateReady回调，不重新走状态机
    // 2.如果打开首页只是为了中转跳转其他页面，则不需要处理初始化弹窗逻辑
    // 3.要在刷新用户信息以及定位信息之后才往下执行状态机
    // ！！！！！！
    // 所以状态机最后一定要指向end！！！！
    // ！！！！！！
    this._data.fsmIsEnd
      ? !isNavigate && !onlyShowData && this.fsm.run({ initState: 'start' })
      : this.fsm.manualTransition()
  },

  /**
   * 定位完成回调页面的方法
   */
  async onLocateReady({ onlyShowData = false } = {}) {
    this.updateTheme()
    // 重新定位触发刷新新用户信息
    this._data.needRefreshNewUserInfo = true
    const { isNavigate, url: navigateUrl, customNavBar, skipHomeContent } = onlyShowData ? {} : this.handleNavigatePage()
    await (
      skipHomeContent
        ? Promise.resolve()
        : this.pageContentHandle({ onlyShowData, isNavigate })
    )
    // 通过二维码唤起微信小程序但是需要跳转到接龙时,需要清空sceneCopy
    // 否则从接龙详情返回后,会在状态机中判断进入会员中心页
    // 其实要解决这个问题,应该在状态机中判断二维码链接的路径(即,options的q参数)是唤起会员中心
    // 但是目前这个方法简单点,就先这样处理
    app.globalData.sceneCopy = isNavigate ? void 0 : app.globalData.sceneCopy
    // 首页中转跳转其他页面，由于跳转太快，跳转新打开页面顶部会缺失返回箭头，所以这里延时跳转
    navigateUrl && setTimeout(() => wx.navigateTo({ url: navigateUrl }), customNavBar ? 0 : 600)
  },

  /**
   * 点击首页tab
   */
  onTabItemTap() {
  },
  closeDefaultLocateTips() {
    this._data.locateTipsResolve && this._data.locateTipsResolve()
  },
  showDefaultLocateTips() {
    const relativeNavTop = this.selectComponent('#relative-nav-top')
    return relativeNavTop ? new Promise(resolve => {
      this._data.locateTipsResolve = resolve
      relativeNavTop.showAddressTips()
    }) : Promise.resolve()
  },
  /**
   * @desc 更新用户信息
   */
   updateUserInfo() {
    const { userID, isEshopNewCustomer } = wx.getStorageSync('user') || {}
    const isLogin = app.checkSignInsStatus()
    this.setData({
      isLogin,
      userInfo: {
        customerID: userID || -1
      },
      isEshopNewCustomer: !!isEshopNewCustomer, // 电商新客
    })
  },

  /**
   * 更新地址信息
   */
  refreshAddressInfo() {
    const currAddr = wx.getStorageSync('selectedAddress')
    const timelyCity = wx.getStorageSync('timelyCity') || {}
    const { cityID, storeID, cityCode, storeName, storeCode, lat, lon, supportBToCService = false, deliveryCenterCode, deliveryCenterId, supportSuperVipShop } = timelyCity
    let { cityName, address } = timelyCity
    if (currAddr) {
     cityName = currAddr.cityName
     address = currAddr.gisAddress
    }
    const supportStoreService = !!cityID && !!storeID
    const addressInfo = {
      address,
      lat,
      lon,
      cityID: cityID || -1,
      storeID: storeID || -1,
      cityCode: cityCode || '',
      storeCode: storeCode || '',
      storeName,
      cityName,
      deliveryCenterCode: deliveryCenterCode || '',
      deliveryCenterId: deliveryCenterId || -1,
      supportBToCService,
      supportStoreService,
    }

    this.setData({
      supportSuperVipShop,
      supportB2COrStore: supportBToCService || supportStoreService, //支持b2c或及时达服务
      unSupportB2CAndStore: !supportBToCService && !supportStoreService,
      addressInfo,
    })

    homeDeliveryStorageCache.setCache('addressInfo', addressInfo)
  },

  /**
   * @desc 生鲜推荐定位逻辑：取次日达首页的地址,若次日达首页城市为空,则默认取深圳市的商品做信息展示
   */
  freshRecommendLocate() {
    const { selectAddressInfo } = wx.getStorageSync('bgxxSelectLocateInfo') || {}
    const {
      cityCode,
      supportSuperVipShop,
      deliveryCenterCode
    } = selectAddressInfo || {}
    this.setData({
      freshCityCode: supportSuperVipShop === 'Y' ? cityCode : '-1',
      freshDeliveryCenterCode: deliveryCenterCode || ''
    })
  },

  /**
   * @desc 获取搜索关键词
   */
  async getSearchWord() {
    const { cityID } = this.data.addressInfo || {}
    if (!cityID || cityID === -1) return
    try {
      const res = await app.api.getSearchHistory(cityID)
      const { searchKeyword = '' } = res.data || {}
      this.setData({
        searchGoodsPlaceholder: searchKeyword
      })
    } catch (error) { }
  },

  /**
   * @desc 跳转选择地址页面
   */
   navigateToLocate() {
    const { lat, lon, address } = this.data.addressInfo
    const homeDeliveryObj = {
      lat,
      lon,
      locaDetailInfo: address
    }
    util.navigateTo({
      url: '/homeDelivery/pages/addressList/index',
      param: {
        homeDeliveryObj
      }
    })
  },

  /**
   * 获取新客礼包，首页背景图、等页面基础数据
   */
  async getBaseDataOfPage () {
    //  初始化次日达入口
    this.initFreshEntry()

    // 新增新人专享商品区域，此区域要求：
    // 1. 未登录、电商新客
    // 2. 过滤后 新人特价商品（含售罄）≥3

    // 大促背景图展示新增规则
    // 1. 未展示新人专享商品区域

    // 新人专享优惠券展示新增规则
    // 1. 未展示新人专享商品区域
    await Promise.all([
      this.initNewUserGoods(),
      //  新人礼包
      this._data.needRefreshNewUserInfo ? this.newUserCouponsFetchData() : true,
      //  获取首页大促背景
      this.backgroundFetchData()
    ])

    this._data.needRefreshNewUserInfo = false

    wx.nextTick(() => {
      this.getSearchBarPosition()
    })
  },

  /**
   * 仅获取数据：新客礼包
   * 未登录或已登录是电商新客才请求接口
   */
  async newUserCouponsFetchData() {
    const { isLogin, isEshopNewCustomer, userInfo } = this.data
    // 如果当前正在请求新客券，则不触发新请求（会员页/首页都会刷新）
    if (app.globalData.isRequestingEshopCoupons) {
      this.handleNewUserCoupons({})
    }
    // 如果是老用户 则直接给空值
    if (isLogin && !isEshopNewCustomer) {
      this.handleNewUserCoupons({
        specialAreaCoupon: {
          data: {
            couponList: []
          }
        }
      })
      return
    }
    try {
      app.globalData.isRequestingEshopCoupons = true
      const { customerID = -1 } = userInfo || {}
      // 有缓存则使用缓存
      if (this._data.tmpNewUserCouponList.length) {
        this.handleNewUserCoupons({
          specialAreaCoupon: {
            couponList: this._data.tmpNewUserCouponList
          }
        })
        return
      }
      const res = await app.api.getEshopNewCustomerGift({ customerID }) || {}
      this.handleNewUserCoupons(res.data || {})
    } catch (err) {
      console.log('newUserCouponsFetchData', err);
      this.handleNewUserCoupons({})
    }
    app.globalData.isRequestingEshopCoupons = false
  },

  /**
   * 处理新客礼包数据
   */
  handleNewUserCoupons (data) {
    const { specialAreaCoupon, dialogCoupon } = data
    this._data.dialogCoupon = dialogCoupon
    this.setNewUserAreaCoupons(specialAreaCoupon)
  },

  /**
   * 设置新客礼包专区
   */
  setNewUserAreaCoupons (data) {
    // 无数据不做处理
    if (!data || !Object.keys(data).length) {
      return
    }
    // 无门店不展示新客礼包专区
    const { couponList = [] } = data || {}
    const { supportStoreService } = this.data.addressInfo || {}
    // 更新缓存
    this._data.tmpNewUserCouponList = couponList
    if (supportStoreService && couponList.length) {
      this.setData({
        specialAreaCouponList: couponList
      })
    } else {
      this.setData({
        specialAreaCouponList: []
      })
    }
  },

  /**
   * 设置新客礼包弹窗
   */
  setNewUserCouponsLayer(data) {
    const { couponList= [] } = data || {}
    if (couponList.length) {
      this.setData({
        couponData: data
      })
      commonObj.updatePopupShowTime() // 更新弹窗缓存时间
      app.globalData.isShowNewCouponDialog = true
    }
  },

  /**
   * 仅获取数据：背景图
   * 无门店不应用背景图，直接返回 {}
   */
  async backgroundFetchData() {
    const { userInfo, addressInfo } = this.data
    const { customerID = -1 } = userInfo || {}
    const {
      cityID,
      storeID,
      supportStoreService
    } = addressInfo || {}

    //  无门店 或 无城市
    if (!supportStoreService || !cityID) {
      this.setBackgroundAd({})
      return
    }

    const params = {
      unionId: wx.getStorageSync('wxSnsInfo').unionid,
      customerID,
      cityID,
      storeID: Number(storeID),
      supportGif: true,
    }
    try {
      const res = await app.api.getBackgroundAd(params) || {}
      this.setBackgroundAd(res.data || {})
    } catch (err) {
      this.setBackgroundAd({})
    }
  },

  /**
   * 设置背景图
   */
  async setBackgroundAd (data) {
    //  有上下背景图(即有大促活动)，则应用大促背景图
    const activityBgObj = data.pic1 ? data : {}
    this.setData({
      activityBgObj
    })
    homeDeliveryStorageCache.setCache('activityBgObj', activityBgObj)

  },

  /**
   * 初始化新人专享商品
   */
  async initNewUserGoods() {
    // 未登录 或 电商新客 展示新人专享商品区域
    const { isLogin, isEshopNewCustomer, addressInfo } = this.data
    const { supportStoreService } = addressInfo || {}
    if (!isLogin || isEshopNewCustomer) {
      const { data: newUserGoodsList = [], onSaleGoodsList = [] } = await getNewUserGoods()
      const isShowNewGoodsArea = onSaleGoodsList.length > 0 && newUserGoodsList.length >= 3 && supportStoreService
      // 新人特价商品≥3个时，展示新人特价模块
      this.setData({
        isShowNewGoodsArea,
        isLoadNewGoods: true
      })
      // 如果展示了新人专享商品区域，则通知子组件刷新数据
      if (isShowNewGoodsArea) {
        const component = this.selectComponent('#new-user-goods')
        component && component.init()
      }
    } else {
      this.setData({
        isShowNewGoodsArea: false,
        isLoadNewGoods: true
      })
    }
  },
  /**
   * 初始化次日达入口
   */
  async initFreshEntry() {
    const component = this.selectComponent('#fresh-entry')
    component && await component.init()
    const relayEntry = this.selectComponent('#relay-entry')
    if (!relayEntry) { return }
    const focusPicUrl = homeDeliveryStorageCache.getCache('focusPicUrl')
    focusPicUrl ? relayEntry.hideEntry() : relayEntry.init()
  },

  // 跳转心享会员页
  navigateToXxVip(e) {
    sensors.track('MPClick', 'homeDeliveryMemberLevel')
    jumpH5Vip('memberIndex')
  },

  onShareAppMessage: function () {
    const { storeID = 0, storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
    const shareObj = {
      ...generateShareAttr(),
      mp_shareTitle: '全球好水果 直供百果园',
      activity_ID: '',
      activity_Name: '',
      groupID: '',
      openerID: '',
      currentCount: '',
      screen_name: '首页',
      screen_code: '1800',
      storeID,
      storeName,
      storeNum: storeInfo.storeCode || ''
    }
    wx.reportAnalytics('share_success')
    if (app.globalData.reportSensors) {
      app.sensors.track('MPShare', shareObj)
    }
    return {
      title: '全球好水果 直供百果园',
      path: '/pages/homeDelivery/index?shareObj=' + JSON.stringify(shareObj) + '&storeID=' + storeID + '&storeName=' + storeName,
      imageUrl: 'https://resource.pagoda.com.cn/dsxcx/images/7cdb659cc9f4b698ed73ce3391ee5914.jpg'
    }
  },

  // 展示心享会员条幅
  showOrderBar(e) {
    const { isShowOrderBar } = e.detail
    this.setData({
      isShowOrderBar
    })
  },

  // 关闭添加我的小程序提示弹窗，摇一摇提示
  closeModal() {
  },

  getNavBarHeight (ev) {
    console.log('getNavBarHeight_detail_' + ev.detail)
    this.setData({
      navBarHeight: ev.detail,
      'refresher.threshold': ev.detail
    })
  },

  /**
   * 页面滚动的回调方法
   */
  onContentScroll: util.throttle(function (e) {
    // 瀑布流切换tab发生的滚动，不走下面的滚动处理方法
    if (!this._data.scrollIntoStatus) {
      this.handleScrolling()
    }
    else {
      this._data.scrollIntoStatus = 0
    }
    // 监听页面滚动距离
    this.pageScrollObserve(e)
  }, 100),

  /**
   * 页面的手指触摸开始事件
   */
  homePageTouchStart (e) {
    this.handleTouching(true)
  },

  /**
   * 页面的手指触摸结束事件
   */
  homePageTouchEnd (e) {
    this.handleTouching(false)
  },

  /**
     * 滚动还未结束 || 手指还在屏幕上（未触发手指触摸结束事件） = floating不显示
     * @des 滚动有惯性，手指触摸结束时，滚动还在进行中，需增加定时器监听滚动的结束
     */
  handleScrolling:util.debounce(function() {
    const that = this
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    that._data.scrollFlag = true
    that.setData({
      isPageScrolling: true
    })

    that.timer = setTimeout(() => {
      that._data.scrollFlag = false
      // 滚动结束时，如果手指结束touch（touchFlag = false），则修改 isScrolling 为 false
      if (!that._data.touchFlag) {
        that.setData({
          isPageScrolling: false
        })
      }
    }, 2000)
  },10),

  /**
   * 触发手指触摸结束事件
   */
  handleTouching(isTouching) {
    const that = this
    // 更新手指touch的标识
    that._data.touchFlag = isTouching
    if (isTouching) return
    if (that._data.scrollFlag) return
    // 手指touch结束时，如果滚动已经结束，则直接修改 isScrolling 为 false
    that.setData({
      isPageScrolling: false
    })
  },

  /**
   * 监听页面滚动
   */
   pageScrollObserve (e) {
    const setShowParam = (list) => {
        list.forEach(item => {
            const {
                key,
                status
            } = item
            if (!this.data[key] === status) {
                this.setData({
                    [key]: status
                })
            }
        })
    }
    setShowParam([{
        key: 'hideBackTop',
        status: e.detail.scrollTop < this._data.windowHeight
    }])
  },

  /**
   *
   */
  handleScrollIntoView (id, animation = true) {
    if (!id) {
      return
    }
    // 直接滚动到对应模块，无需动画过渡
    if (!animation) {
      this.setData({
        scrollIntoView: id
      })
      return
    }
    // 滚动到对应模块，使用动画过渡
    this.setData({
      scrollAnimation: true
    })
    setTimeout(() => {
      this.setData({
        scrollIntoView: id,
        scrollAnimation: false
      })
    }, 100)
  },

  /**
   * 点击新客优惠券
   */
  newUserCouponClick (e) {
    // 没有瀑布流，跳去品类页
    if (!this._data.hasWaterfallGoods) {
      wx.navigateTo({
        url: '/homeDelivery/pages/category/index',
      })
      return
    }
    // 有瀑布流，跳到瀑布流
    this.handleScrollIntoView('scroll-flag-waterfall')
  },

  /**
   * 回到顶部
   */
  handleBackTop() {
    sensors.clickReport({
      blockName: '回顶部',
      blockCode: 180025,
      element_name: '回顶部',
      element_code: '180025001',
    })
    this.handleScrollIntoView('scroll-top-0')
  },

  /**
   * 瀑布流滚动到顶部
   */
   scrollIntoView(e) {
    const { id } = e.detail || {}
    if (id) {
      this._data.scrollIntoStatus = 1 // 瀑布流切换tab发生的隐藏滚动：0 不是，1 是
      this.handleScrollIntoView(id, false)
    }
  },

  /**
   * 瀑布流tabbar初始化完成
   */
  waterfallTabInited(e) {
    const { addressInfo } = this.data
    const { tabsList } = e.detail || {}
    this._data.hasWaterfallGoods = !!tabsList.length
    //  1.有全国送服务 无门店服务 没有瀑布流
    const supportB2cButNoContent = addressInfo.supportBToCService && !addressInfo.supportStoreService && !this._data.hasWaterfallGoods
    this.setData({
      supportB2cButNoContent
    })
  },

  /**
   * 瀑布流tabbar吸顶
   */
  waterfallTabbarFixed (e) {
    const isFixed = e.detail
    this.setData({
      isFixed
    })
  },

  // 阻止事件冒泡
  stop() {
    return false
  },

  /**
   * @desc 处理通过首页中转跳转其他页面
   * @returns {Object} = {
   *    isNavigate: true/false 是否可以跳转
   *    url: '', // 跳转url
   * }
   */
  handleNavigatePage() {
    const { options, isToPage } = this._data
    console.log('handleNavigatePage', options)
    const isEmptyOptions = util.isEmptyObject(options)
    const relayDetailTimelineOptions = app.globalData.relayDetailTimelineOptions || {}
    if ((isEmptyOptions && util.isEmptyObject(relayDetailTimelineOptions)) || isToPage) {
      return {
        isNavigate: false
      }
    }
    const navigateObj = forwardNavigate({ ...(isEmptyOptions ? relayDetailTimelineOptions : options) })
    Object.assign(this._data, {
      options: {},
      isToPage: true
    })
    return navigateObj
  },

  // 神策上报点击事件
  trackClickEvent(params = {}) {
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', params)
    }
  },

  supportMember() { },

  setNavBarColor(bool) {
    const color = bool ? '#000000' : '#ffffff'
    wx.setNavigationBarColor({
      frontColor: color,
      backgroundColor: color
    })
  },

  /**
   * 页面整体使用scroll-view，所以上拉和下拉刷新都使用scroll-view提供的api
   * 同时要禁用掉页面的 enablePullDownRefresh
   */
  /**
   * scroll-View触底，触发距离为距底部500
   */
  onScrollViewBottom() {
    // 如果有瀑布流，下拉触底请求瀑布流的下一页数据
    this._data.hasWaterfallGoods && this.selectComponent('#waterfallGoods').onReachCallBack()
  },
  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    const refreshPromise = this.getRefreshTask()
    if (!refreshPromise.length) {
      return
    }
    const setRefreshStatus = (status) => {
      // 设置触发状态
      this.setData({
        'refresher.triggered': status
      })
      // 设置状态栏样式
      this.setNavBarColor(status)
    }
    setRefreshStatus(true)
    try {
      await Promise.all(refreshPromise)
    } catch (error) {}
    setRefreshStatus(false)
  },
  /**
   * 获取要刷新的任务
   */
  getRefreshTask() {
    const { refreshCompList } = this._data
    const refreshPromise = refreshCompList.map(item => {
      const comp = this.selectComponent(`#${item.name}`)
      if (!comp) {
        return null
      }
      if (typeof comp.refresh !== 'function') {
        return null
      }
      return comp.refresh()
    }).filter(item => !!item)
    refreshPromise.push(this.getSearchWord(), this.getBaseDataOfPage(), rankBarStore.then(({ rankBarStore }) => rankBarStore.updateRankBarConfig()))
    return refreshPromise
  },
  /**
   * @desc 会员码组件动画结束回调
   */
  memberCodeAnimationEnd() {
    this.fsm.manualTransition()
  },
  // userA/pages/coupon/index
  navigateToCoupon () {
    wx.navigateTo({
      url: '/userA/pages/coupon/index'
    })
  },
  navigateToMemberCode () {
    wx.navigateTo({
      url: '/userB/pages/memberCode/index'
    })
  },

  /**
   * @desc 关闭【红包弹窗、广告弹窗】，page-dialog组件抛出hideBounce事件
   */
  closePageDialog () {
    // 特殊场景：不需要弹红包弹窗，showDialogFn setData之后，组件直接抛出事件触发 closePageDialog
    // 此时因为microTask原因未给 resolveExecution 赋值，后续在状态机中优化
    setTimeout(() => {
      console.log('---closePageDialog---');
      this.fsm.manualTransition()
    })
  },

  /**
   * @description 弹窗组件获取完数据回调
   */
  pageDialogLoaded({ detail: { dialogFirst } }) {
    const resolve = this._data.pageDialogFirst.resolve
    resolve && resolve(dialogFirst)
  },
  /**
   * @desc 关闭新用户优惠券弹窗
   */
  closeNewCustomerLayer () {
    console.log('---closeNewCustomerLayer---');
    this.fsm.manualTransition()
    // 情况弹窗内容，下次不弹
    this.setData({
      couponData: {}
    })
    this._data.dialogCoupon = {}
  },

  /****使用状态机控制多任务执行顺序 Start**** */

  /** 扫码进入时，会员码弹窗相关逻辑 */
  /**
   * @desc 是否有门店，无门店不展示会员码
   */
  async checkShowMemberCode () {
    const { addressInfo } = this.data
    const { storeID } = addressInfo || {}

    return storeID !== -1
  },

  toggleMemberCode (bool) {
    // 隐藏tabbar
    // bool ? wx.hideTabBar() : wx.showTabBar()
    // this.setData({
    //   showMemberCode: bool
    // })
    if (!bool) {
      return
    }
    const showCEM = Number(app.globalData.scene === openByScanQRCode)
    const erpAreaInfoPreset = showCEM ? app.api.getERPAreaInfo({
      storeCode: this.data.addressInfo.storeCode
    }) : Promise.resolve()
    const surveyData = showCEM ? ecmSurveys.then(({ getSurveyIdByScene }) => getSurveyIdByScene('scanShowMemberCode')) : Promise.resolve()
    wx.navigateTo({
      url: `/userB/pages/memberCenter/index?from=homeDelivery&showCEM=${showCEM}`,
      events: {
        getPresentData(callback) {
          callback({ erpAreaInfoPreset })
        },
        getSurveyData(callback) {
          callback({ surveyData })
        },
      }
    })
  },

  /**
   * @desc 会员码弹窗收起，展示会员码icon提示
   */
  showMemberCodeTips () {
    this.selectComponent('#relative-nav-top').setMemberCodeTips()
  },

  tryShowPageDialog() {
    const pageDialogFirst = this._data.pageDialogFirst
    pageDialogFirst.promise = new Promise(resolve => {
      pageDialogFirst.resolve = function(value) {
        // 保证只触发一次resolve
        pageDialogFirst.resolve = null
        pageDialogFirst.value = value
        resolve(value)
      }
    })
    this.setData({
      fetchDialogData: true
    })
    return pageDialogFirst.promise
  },

  /**
   * @desc 展示红包雨/广告弹窗
   */
  showPageDialogFn () {
    this.setData({
      isShowDialog: true,
      fetchDialogData: false
    })
  },

  /**
   * @desc 首页弹窗任务初始化有限状态机
   * 弹窗逻辑梳理参考：https://baiguokeji.coding.net/p/DSXCX/wiki/7350
   *
   */
  initFSM () {
    this.fsm = new StateMachine({
      'start': {
        task: () => {
          this._data.fsmIsEnd = false
        },
        next: 'checkIsLogin'
      },
      'checkIsLogin': {
        task: () => app.checkSignInsStatus(),
        next: (result) => {
          return {
            state: result[0] ? 'checkOpenTypeWhenSignIn' : 'isForcedToSignOut'
          }
        }
      },
      'isForcedToSignOut': {
        task: () => {
          /**
           * launchUserLoginStatus 是 onLaunch 时记录的登录状态
           * 此时与 currentLoginStatus 做比较
           * launchUserLoginStatus 为 login 且 currentLoginStatus 为 false 时，表示被踢出登录
           * 但只判断一次，判断完之后清空掉launchUserLoginStatus
           */
          const currentLoginStatus = app.checkSignInsStatus()
          if (!app.globalData.launchUserLoginStatus) return false

          console.log('isForcedToSignOut', currentLoginStatus, app.globalData.launchUserLoginStatus);

          const isForcedToSignOut = app.globalData.launchUserLoginStatus === 'login' && currentLoginStatus === false
          app.globalData.launchUserLoginStatus = ''
          return isForcedToSignOut
        },
        next: (result) => {
          if (result[0]) {
            // 是被踢出登录，清空场景值
            app.globalData.sceneCopy = void 0
            return { state: 'end' }
          } else {
            return { state: 'checkOpenTypeWhenSignOut' }
          }
        }
      },
      // 非登录态检查打开方式：只区分是否扫码进入
      'checkOpenTypeWhenSignOut': {
        task: () => {
          return app.globalData.sceneCopy === openByScanQRCode
        },
        next: (result) => {
          return { state: result[0] ? 'isNavigatedToLogin' : 'popupAndCollect' }
        }
      },
      'isNavigatedToLogin': {
        task: () => {
          return this._data.navigatedToLogin
        },
        next: (result) => {
          if (result[0]) {
            // 去过登录页，清空场景值
            app.globalData.sceneCopy = void 0;
            return { state: 'end' }
          } else {
            return { state: 'loginToShowMemberCode' }
          }
        }
      },
      // 跳转登录
      'loginToShowMemberCode': {
        task: () => {
          this._data.navigatedToLogin = true
          app.signIn()
        },
        next: 'end'
      },
      // 登录态检查打开方式
      'checkOpenTypeWhenSignIn': {
        task: () => {
          // 1. 扫物料码(场景值+启动路径)
          // 2. options参数携带{to: 'memberCenter'}
          const scanStoreQRCode = app.globalData.sceneCopy === openByScanQRCode && app.globalData.launchPath === 'pages/homeDelivery/index'
          return scanStoreQRCode || this._data.isToMemberCenter
        },
        next: (result) => {
          return { state: result[0] ? 'checkShowMemberCode' : 'getNewUserCouponPopupStatus' }
        }
      },
      'checkShowMemberCode': {
        task: this.checkShowMemberCode,
        beforeLeave: () => {
          // 无论有无门店都要清空场景值，防止后面再次弹出
          app.globalData.sceneCopy = void 0
          this._data.isToMemberCenter = false
          // fix: 清空初始登录状态
          // 原因: 登录状态下扫码进来，展示完会员码弹窗之后，退出登录，重新进首页，此时launchUserLoginStatus为login，
          // 会被认为是被踢出登录，所以需要清空
          app.globalData.launchUserLoginStatus = ''
        },
        next: (result) => {
          return { state: result[0] ? 'showMemberCode' : 'end' }
        }
      },
      'showMemberCode': {
        task: () => {
          this.fetchFruitdialog()
          this.toggleMemberCode(true)
        },
        next: 'end'
      },
      // 会员码icon提示
      'showMemberCodeTips': {
        task: this.showMemberCodeTips,
        next: 'fetchCouponNum'
      },
      // 展示添加收藏按钮和广告/红包雨弹窗
      'popupAndCollect': {
        task: () => {
          // 关闭默认地址定位弹窗再显示收藏浮
          this.showDefaultLocateTips()
          this.showPageDialogFn()
        },
        autoTransition: false,
        next: 'fetchCouponNum'
      },
      'getNewUserCouponPopupStatus': {
        task: () => this._data.dialogCoupon,
        next: (result) => {
          const { couponList } = result[0] || {}
          if (couponList && !!couponList.length) {
            return {
              state: 'tryShowPageDialog'
            }
          } else {
            return { state: 'popupAndCollect' }
          }
        }
      },
      // 有新人券的情况下，检查活动红包弹窗是否优先于新人红包弹出
      tryShowPageDialog: {
        task: this.tryShowPageDialog,
        next: ([pageDialogFirst]) => {
          return pageDialogFirst
            ? { state: 'showPageDialog' }
            : {
              state: 'showNewUserCouponPopup',
              payload: this._data.dialogCoupon
            }
        }
      },
      showPageDialog: {
        task: this.showPageDialogFn,
        autoTransition: false,
        next: () => {
          return this._data.pageDialogFirst.value ? {
            state: 'showNewUserCouponPopup',
            payload: this._data.dialogCoupon
          } : {
            state: 'fetchCouponNum'
          }
        }
      },
      // 新用户优惠券弹窗
      'showNewUserCouponPopup': {
        task: (payload) => this.setNewUserCouponsLayer(payload[0]),
        autoTransition: false,
        next: () => {
          return {
            // 活动红包弹窗是否优先于新人红包弹出
            state: this._data.pageDialogFirst.value
              // 如果是活动红包弹窗，
              // 并且优先新人券弹出，
              // 那就不显示活动红包弹窗了
              ? 'fetchCouponNum'
              // 否则在关闭弹窗后再执行fetchCouponNum
              : 'showPageDialog'
          }
        }
      },
      'fetchCouponNum': {
        task: async () => {
          this.getCouponNumber()
        },
        next: 'showStoreSupportTypeTips'
      },
      // 门店是否支持配送、自提弹窗优先级放在最后
      'showStoreSupportTypeTips': {
        task: async () => {
          const { showTips, tipsWord, address, lon, lat } = this.showStoreSupportTypeTips()
          if (!showTips) return
          this.setData({
            storeCanDeliveryTipsWord: tipsWord,
            showCustomConfirmModal: true
          })
          const modalRes = await new Promise(resolve => this._data.showCustomModalResolve = resolve)
          if (modalRes) {
            util.navigateTo({
              url: '/homeDelivery/pages/addressList/index',
              param: { locaDetailInfo: address, lon, lat }
            })
          }
        },
        next: 'end'
      },
      'end': {
        task: () => {
          this._data.fsmIsEnd = true
        }
      },
    }, { logState: true })
  },

  /****使用状态机控制多任务执行顺序 End**** */

  /**
   * 组件注册更新
   */
  registerRefreshComp(e) {
    const { refreshCompList } = this._data
    const { name } = e.detail || {}
    const obj = refreshCompList.find(item => item.name === name)
    if (obj) {
      return
    }
    refreshCompList.push({
      name
    })
  },

  /**
   * @desc 检查门店配送情况，是否需要进行弹窗提示
   * @returns { {showTips: boolean, tipsWord: string | undefined, address: string, } } showTips: 是，需要弹窗提示；否，不需要弹窗提示
   */
  showStoreSupportTypeTips () {
    // 冷启动后只弹窗一次
    if (app.globalData.storeSupportTypeTipsIsShowed) return { showTips: false }
    app.globalData.storeSupportTypeTipsIsShowed = true
    const { storeInfo, address, lon, lat } = wx.getStorageSync('timelyCity') || {}

    // 没有门店信息
    if (!storeInfo) return { showTips: false }
    const { isSupportTake, isTimelySupport, storeName } = storeInfo || {}
    // 支持配送，不需要弹窗
    if (isTimelySupport === 'Y') return { showTips: false }
    const ONLY_SUPPORT_TIMELY = 'ONLY_SUPPORT_TIMELY'
    const ONLY_SUPPORT_TAKE = 'ONLY_SUPPORT_TAKE'
    const genSelectStoreTips = ({ storeName, supportType, notSupportType }) => {
      const tipsMap = {
        ONLY_SUPPORT_TIMELY: '配送',
        ONLY_SUPPORT_TAKE: '自提'
      }
      return `${storeName}仅支持${tipsMap[supportType]}，是否需要切换到其他支持${tipsMap[notSupportType]}的门店？`
    }
    const supportType = isTimelySupport === 'Y' ? ONLY_SUPPORT_TIMELY : ONLY_SUPPORT_TAKE
    const notSupportType = isSupportTake === 'Y' ? ONLY_SUPPORT_TIMELY : ONLY_SUPPORT_TAKE
    const tipsWord = genSelectStoreTips({ storeName, supportType, notSupportType })
    return { showTips: true, tipsWord, address, lon, lat }
  },

  /**
   * 关闭添加到我的小程序提示
   */
  closeAddGandle(){

  },
  async getCouponNumber() {
    const { customerID } = this.data.userInfo
    if (!customerID || customerID === -1) {
      this.setData({
        couponNum: 0
      })
      return
    }
    await this.fetchCouponNumber()
  },
  /**
   * @desc 获取优惠券数量，节流控制
   */
  fetchCouponNumber: util.throttle(async function () {
    // 查询卡券优惠券数量
    const { customerID } = this.data.userInfo
    const { data: { totalNum = 0 } } = await app.api.getCouponCountByBiz({
      customerID,
      applicableBizTypes: [0, 2],
      notOnlyApplicableChannels: [10000],
      showFruitGradeCouponCount: 'Y'
    })
    // 查询及时达优惠券数量
    this.setData({
      couponNum: totalNum
    })
  }, 10000),
  handleChoice (event) {
    const selectConfirm = event.detail === 'confirm'
    this.setData({
      showCustomConfirmModal: false
    })
    this._data.showCustomModalResolve(selectConfirm)
  },

  /**
   * 更新首页主题
   */
  updateTheme() {
    this.setData({
      isGrayTheme: app.globalData.isGrayTheme
    })
  },
  /**
   * 获取及时达首页红包弹窗信息
   * 用于扫门店物料码进来时的场景
   * 只需要调用即可
   */
  fetchFruitdialog(){
    const { customerID } = this.data.userInfo
    const { cityID, storeID, cityCode, storeCode } = this.data.addressInfo
    if (!customerID || customerID === -1) return
    const params = {
      customerID,
      cityID,
      storeID,
      cityCode,
      storeCode
    }
    app.api.getHomepageDialog(params)
  },

  /**
   * 获取搜索栏位置
   */
  getSearchBarPosition(queryTimes = 0) {
    console.log('getSearchBarPosition');
    if (queryTimes > 3) return
    const query = wx.createSelectorQuery()

    query.select('#search-bar').boundingClientRect();
    query.select('#scroll-top-0').boundingClientRect()

    query.exec(res => {
      const [searchBarNode, referenceNode] = res;
      if (!searchBarNode || !referenceNode) {
        setTimeout(() => {
          queryTimes+=1
          this.getSearchBarPosition(queryTimes)
        }, 500)
        return
      }
      this.setData({
        searchBarInfo: {
          top: Math.abs(referenceNode.top - searchBarNode.top),
        }
      })
    });
  },
});
