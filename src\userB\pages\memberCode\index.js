// pages/user/memberCode/index.js
var commonObj = require('../../../source/js/common').commonObj;
var app = getApp()
const act = require('../../../utils/activity')
const genMemberCode = require('../../source/js/memberCode/genMemberCode')
const { formatLevelName, formatCode } = require('../../../utils/format')
var log = require('../../../utils/log.js')
let util = require('../../../utils/util.js')
const sensors = require('../../../utils/report/sensors')
const jsBarcodePaomise = require.async('../../../componentsSubPackage/commonUtils/JsBarcode/index')
const JsBarcode = jsBarcodePaomise.then(module => module.getJsBarcode(app))
import { requestSubscribeMessage } from '../../../utils/wx/subscribeMessage'
import {
  getEncryptKey
} from '../../../service/encrypt';
Page({

	/**
	 * 页面的初始数据
	 */
  data: {
    levelName: '',
    seeMoreFlag: true,
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    canUseOfflinePay: true,
    superVipStatus: 'C',
    isConnected: true, // 是否联网
    balanceInfo: {
      // 积分
      integralAmount: 0,
      // 优惠券数量
      couponsAmount: 0,
      // 钱包充值
      walletAmount: 0,
      // 活动(后续将不再支持)
      activityTitleName: ''
    }, // 账户信息
    bannerObj: {},
    tmplIds: ['0JJz5yDPkq-C1GVb1Q5KLrsqMt5D6kO5WqamyhzCsXc'],
    // 抢购开始提醒  活动优惠金额通知 签到提醒
    showSubGuide: false,
    showSubSetPopup: false,
    memberExceedRemind: '',
    memberH5PageUrl: ''
  },
  _data: {
    memberTimer: null,
    screenBrightness: '', // 屏幕亮度
    codeTimer: null,
    intervalCount: 0, // 记录检查是否支付次数
    windowWidth: 375
  },
	/**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function (options) {
    const { superVipStatus, isIphoneX } = app.globalData
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: superVipStatus === 'C' ? '#008C3C' : '#003B2C'
    })
    this.setData({
      superVipStatus: superVipStatus,
      isIphoneX: isIphoneX
    })
    // 判断用户微信版本是否能拉起线下刷卡支付
    if (wx.openOfflinePayView) {
      this.setData({ canUseOfflinePay: true })
    } else {
      this.setData({ canUseOfflinePay: false })
    }
    let that = this
    wx.getSystemInfo({
      success(t) {
        that._data.windowWidth = t.windowWidth
        console.log(t)
      }
    })
  },
  onShow: function () {
    // 浏览页面上报神策
    sensors.pageScreenView()
    this.setData({
      isLogin: app.checkSignInsStatus()
    })
    if (!app.checkSignInsStatus()) {
      app.showSignInModal()
      return
    }
    this.initPage()
  },
  async getBarCode (code) {
    let width = this._data.windowWidth * 0.8
    const jsBarcode = await JsBarcode
    const e = jsBarcode("#", code, {
      format: "CODE128"
    })
    console.log('code', e._encodings[0][0].data)
    let codeData = e._encodings[0][0].data
    let config = {
      height: 80,
      width: 2,
      marginTop: 10,
      marginleft: this._data.windowWidth*0.1,
      lineColor: "black"
    }
    let context = wx.createCanvasContext('canvasbarcode')
    let top = config.marginTop;
    context.strokeStyle = "#000000"
    config.width = width / codeData.length
    context.lineWidth = config.width
    let s = config.marginleft
    for (var i = 0; i < codeData.length; i++) {
      var l = s + i * config.width;
      if (codeData[i] === "1") {
        context.beginPath()
        context.moveTo(l, top)
        context.lineTo(l, top + config.height)
        context.stroke()
      }
    }
    context.draw()
  },
  onHide: function () {
    this.destroyedPage()
  },
  onUnload: function () {
    this.destroyedPage()
  },
  destroyedPage () {
    clearTimeout(this._data.memberTimer)
    clearInterval(this._data.codeTimer)
    this.cancelSetKeepScreenOn()
  },
  initPage () {
    const { phoneNumber, userID, userToken, superVipStatus } = wx.getStorageSync('user') || {}
    const { cityID, storeID } = wx.getStorageSync('timelyCity') || {}
    let userName = phoneNumber.substring(0, 3) + '****' + phoneNumber.substring(7, 11)
    // 设置屏幕变亮
    this.setKeepScreenOn()
    this.setData({
      userName
    })
    commonObj.getNetworkStatus( isConnected => {
      this.setData({
        isConnected: isConnected
      })
      if (isConnected) {
        this.getCustomerInfo(userID)
        this.getMemberCode();
        this.getBanner();
        this.getBalance()
        this.getMemberCodeVipTips()
        this._data.codeTimer = setInterval( () => {
          this.getLocalCodePaymentInfo()
        }, 1500);
      } else { //没有网络
        this.setData({
          levelName: formatLevelName(superVipStatus) + ':',
          code: null
        })
        setTimeout(() => {
          this.getOfflineCode()
        }, 300);
      }
    })
  },
  // 获取会员到期提醒
  getMemberCodeVipTips () {
    const { userID } = wx.getStorageSync("user")
    app.api.getMemberCodeVipTips({customerID: userID})
      .then(res => {
        const { tips, url } = res.data
        this.setData({
          memberExceedRemind: tips,
          memberH5PageUrl: url
        })
        // this.setData({
        //   memberExceedRemind: '今天过期，立即续费',
        //   memberH5PageUrl: url
        // })
      })
      .catch(err => {
        console.log('getMemberCodeVipTips',err);
      })
  },
  // 获取离线码
  getOfflineCode () {
    const codeKey = wx.getStorageSync('codeKey') || ''
    if (!codeKey) {
      return
    }
    console.log(codeKey)
    let memberCode = genMemberCode(codeKey)
    console.log(memberCode)
    if (!memberCode) {
      let { userID } = wx.getStorageSync('user') || {}
      log.error('生成码错误', userID, codeKey)
    }
    this.formatMemberCode(memberCode)
    this.getBarCode(memberCode)
    this._data.memberTimer = setTimeout( () => {
      this.getOfflineCode()
    }, 30 * 1000)
  },
  // 设置屏幕长亮
  setKeepScreenOn () {
    // 获取屏幕亮度
    wx.getScreenBrightness({
      success:  res => {
        this._data.screenBrightness = res.value
        if (this._data.screenBrightness < 0.5) {
          wx.setScreenBrightness({
            value: 0.5
          })
        }
      }
    })
    // 保持屏幕长亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })
  },
  // 取消设置屏幕常亮
  cancelSetKeepScreenOn () {
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
    const { platform = 'ios' } = wx.getStorageSync('systemInfo') || {}
    // 恢复原来的亮度
    if (this._data.screenBrightness) {
      wx.setScreenBrightness({
        value: platform === 'ios' ? this._data.screenBrightness : '-1'
      })
    }
  },
  // 请求会员码
  async getMemberCode() {
    await app.getMemberCodeKey()
    this.getOfflineCode()
  },
  // 格式化会员码
  formatMemberCode (code) {
    let codeHidden = code.substring(0, 4) + '*************';
    this.setData({
      code: formatCode(code),
      codeHidden: formatCode(codeHidden),
      seeMoreFlag: true
    })
  },
  // 获取用户信息
  async getCustomerInfo(userId) {
    const { data } = await app.api.getMemberInfo({
      customerID: userId
    })

    const { superVipStatus } = data

    this.setData({
      superVipStatus,
      levelName: formatLevelName(superVipStatus) + ':'
    })
  },
  // 获取账户余额
  async getBalance () {
    const { userID } = wx.getStorageSync('user') || {}
    const param = {
      customerID: userID,
      isNeedBalance: 'Y',
      isNeedIntegral: 'Y',
      isNeedCoupon: 'Y',
      notOnlyApplicableChannels: [10000],
      showFruitGradeCouponCount: 'Y'
    }
    const { data: {
      walletAmount = 0,
      integralAmount = 0,
      couponsAmount = 0,
      expireSoonNum = 0
    } = {} } = await app.api.getUserAmount(param)
    const balanceInfo = {
      couponsAmount,
      expireSoonNum,
      integralAmount,
      walletAmount
    }
    this.setData({
      balanceInfo
    })
  },
  navigateToPage (e) {
    const { type } = e.currentTarget.dataset
    const router = {
      'coupon': '/userA/pages/coupon/index',
      'duiba': '/h5/pages/duiba/index',
      'deposit': '/userA/pages/deposit/index',
    }
    if (!router[type]) return
    wx.navigateTo({
      url: router[type]
    })
    if ( app.globalData.reportSensors ) {
      const reportObj = {
        'coupon': { code: 120200002, name: '优惠券' },
        'duiba': { code: 120200001, name: '积分' },
        'deposit': { code: 120200003, name: '钱包' }
      }
      const reportData = reportObj[type]
      app.sensors.track('MPClick', {
        element_code: reportData.code,
        element_name: reportData.name,
        element_content: reportData.name,
        screen_code: '1202',
        screen_name: '会员码页',
      })
    }
  },
  // 跳转心享会员续费页
  navigateToMemberBuy (e) {
    const { url } = e.currentTarget.dataset
    console.log(url);
    act.toActivityPage({
      openType: 17,
      openValue: url
    })
  },
  // 查看数字
  seeMore: function (e) {
    this.setData({ codeHidden: this.data.code, seeMoreFlag: false });
  },
  // 调用微信offlinePay
  async callOfflinePay() {
    const decryptKey = await getEncryptKey('pwd')
    const that = this
    const appId = 'wx1f9ea355b47256dd' // 公众平台appId
    if (that.data.canUseOfflinePay) {
      // 用户微信支持调用
      wx.showLoading({
        title: '加载中...',
        mask: true
      })
      let options = {
        // encryptFlag: true,
        url: '/api/v1/wechat/offlinePayView/sign',
        data: { appId }
      }
      commonObj.requestData(options,
        res => {
          wx.hideLoading()
          if (res.data.errorCode === 0 && res.data.data) {
            let data = JSON.parse(commonObj.Decrypt(res.data.data, decryptKey))
            wx.openOfflinePayView({
              'appId': appId,
              'timeStamp': data.timeStamp + '',
              'nonceStr': data.nonceStr,
              'package': data.package,
              'signType': data.signType,
              'paySign': data.paySign,
              'success': function (res) {
              },
              'fail': function (res) {
                console.log(res)
                wx.reportAnalytics('weixin_pay_fail')
                // commonObj.showModal('提示', '调用微信支付失败，请尝试升级微信后再试', false, '我知道了')
              },
              'complete': function (res) {
                console.log('complete', res)
               }
            })
          }
          else {
            console.log(res.data.errorMsg)
            commonObj.showModal('提示', '系统繁忙，请求超时', false, '我知道了')
          }
        }
      )
    }
    if ( app.globalData.reportSensors ) {
      app.sensors.track('MPClick', {
        element_code: 120200004,
        element_name: '微信支付',
        element_content:  '微信支付',
        screen_code: '1202',
        screen_name: '会员码页'
      })
    }
  },
  // 获取banner广告图
  getBanner: function () {
    var that = this,
      user = wx.getStorageSync('user'),
      cityList = wx.getStorageSync('timelyCity'),
      storeID = cityList.storeID ? cityList.storeID : -1;
    if (cityList && cityList.cityID) {
      var options = {
        url: `/api/v2/banner/${user.userID}/${cityList.cityID}/${storeID}/memberCode`,
      };
      commonObj.requestData(options, function (res) {
        wx.hideLoading();
        if (res.data.errorCode === -1002) {
          commonObj.showModal('提示', '您的登录已过期，是否重新登录？', true, '我知道了', '', function (res) {
            if (res.confirm) {
              app.signOut()
              app.signIn()
            } else if (res.cancel) {
              app.signOut()
              wx.reLaunch({
                url: '/pages/index/index'
              })
            }
          })
        } else if (res.data.errorCode === 0) {
          const bannerObj = res.data.data || {}
          if ( bannerObj.openType ) {
            that.setData({
              isShowBanner: true,
              bannerObj
            })
          } else {
            that.setData({ isShowBanner: false })
          }
        } else {
          commonObj.showModal('提示', res.data.description, false);
        }
      });
    }
  },
  skipGoodsDetail () {
    const { bannerObj } = this.data
    act.toActivityPage( bannerObj )
    if ( app.globalData.reportSensors ) {
      app.sensors.track('MPClick', {
        element_code: '120200005',
        element_name: '会员页广告',
        element_content: '会员页广告',
        screen_code: '1202',
        screen_name: '会员码页',
        banner_id: bannerObj.bannerID,
        banner_name: bannerObj.name,
      })
    }
  },
  // 获取离线会员码消费后展示消费金额
  getLocalCodePaymentInfo () {
    if (this._data.intervalCount > 120) {
      clearInterval(this._data.codeTimer)
      return
    }
    this._data.intervalCount++
    const { userToken, userID } = wx.getStorageSync("user")
    let params = { customerID: userID, userToken, "_appInfo": { "os": "wxApp" } };
    let options = {
      encryptFlag: true,
      url: '/customerManager/getLocalCodePaymentInfo',
      data: params
    }
    commonObj.requestData(options, (res) =>  {
      let data = res.data.data
      // 支付成功
      if (data) {
        const token = wx.getStorageSync("token")
        data = JSON.parse(commonObj.Decrypt(data, token) || '{}')
        wx.redirectTo({
          url: `/userB/pages/codePaySuccess/index?payAmount=${data.payAmount}`
        })
        clearInterval(this._data.codeTimer)
      }
    })
  },
  // 勿删，订阅消息背景引导图
  toShowSubGuide(isShow = true) {
    console.log('toShowSubGuide')
    this.setData({
      showSubGuide: isShow
    })
  },
  // 勿删，订阅消息弹窗
  toShowSubSetPopup(isShow = true) {
    this.setData({
      showSubSetPopup: isShow
    })
  },
  closeSubGuidePopup () {
    this.setData({
      showSubSetPopup: false
    })
  },
  callSubscribeSettings: util.throttle( function () {
    let tmplIds = this.data.tmplIds
    requestSubscribeMessage({ tmplIds }, this)
  }, 1500)
})
