const app = getApp()
const commonObj = require('../../../../../source/js/common').commonObj
const genMemberCode = require('../../../../source/js/memberCode/genMemberCode')
const log = require('../../../../../utils/log.js')
const jsBarcodePaomise = require.async('../../../../../componentsSubPackage/commonUtils/JsBarcode/index')
const JsBarcode = jsBarcodePaomise.then(module => module.getJsBarcode(app))
import { getEncryptKey } from '../../../../../service/encrypt'
import sensors from '../../../../../utils/report/sensors'
import { debounce } from '../../../../../utils/util'
const memberInfoReport = {
  // 积分商城
  'duiba': '1810_181003001',
  // 券包
  'coupon': '1810_181003002',
  // 钱包/充值
  'deposit': '1810_181003003'
}

/**自动刷新会员码倒计时任务 */
let memberTimer
/** 是否需要获取会员码key */
let needGetCodeKey = true

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    showMemberCode: {
      type: Boolean,
      value: false
    },
    fromHomeDelivery: {
      type: Boolean,
      value: false
    }
  },
  observers: {
    // 扫码进来会员中心页，已经在app.js请求过getMemberCodeKey，这种场景下不需要再请求一次
    fromHomeDelivery(val) {
      needGetCodeKey = !val
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    canUseOfflinePay: true,
    // 是否展示新人礼包角标
    showNewCustomerTip: false,
    // 是否联网
    isConnected: true,
    // 账户信息
    balanceInfo: {
      // 积分
      integralAmount: 0,
      // 优惠券数量
      couponsAmount: 0,
      // 钱包充值
      walletAmount: 0
    },
    // 会员码
    memberCode: '',
    // 展示会员码
    showCode: true,
    noData: true
  },
  codeTimer: null,
  // 记录检查是否支付次数
  intervalCount: 0,
  // 屏幕亮度
  screenBrightness: 0,
  windowWidth: 375,
  // 绘制会员码重试次数
  times: 3,

  /**
   * 组件的方法列表
   */
  methods: {
    initPage() {
      const isLogin = app.checkSignInsStatus()
      if (!isLogin) {
        this.setData({
          noData: false
        })
        return
      }
      this.setData({
        isLogin
      })
      // 设置屏幕变亮
      this.setKeepScreenOn()
      commonObj.getNetworkStatus( isConnected => {
        this.setData({
          isConnected,
          noData: false
        })
        if (isConnected) {
          this.getMemberCode()
          this.getBalance()
          // this.codeTimer = setInterval( () => {
          //   this.getLocalCodePaymentInfo()
          // }, 1500);
        } else {
          // 没有网络
          this.setData({
            code: null
          })
          setTimeout(() => {
            this.getOfflineCode()
          }, 300);
        }
      })
    },
    /**
     * 请求会员码
     */
    async getMemberCode() {
      await app.getMemberCodeKey()
      this.getOfflineCode()
    },
    /**
     * 刷新会员码
     */
    refreshMemberCode() {
      //  禁用刷新，返回
      if (this.disabledRefresh) {
        return
      }

      this.disabledRefresh = true
      clearTimeout(memberTimer)

      this.setData({
        isRefreshing: true,
      });
      //  一秒半后再允许点击刷新
      setTimeout(() => {
        this.setData({
          isRefreshing: false,
        });
        this.disabledRefresh = false
      }, 1500);

      this.getMemberCode()
    },
    /**
     * 获取离线码
     */
    getOfflineCode() {
      const codeKey = wx.getStorageSync('codeKey') || ''
      if (!codeKey) {
        return
      }
      console.log(codeKey)
      let memberCode = genMemberCode(codeKey)
      if (!memberCode) {
        let { userID } = wx.getStorageSync('user') || {}
        log.error('生成码错误', userID, codeKey)
      }
      this.setData({
        code: memberCode
      })
      this.getBarCode(memberCode)
      clearTimeout(memberTimer)
      memberTimer = setTimeout( () => {
        this.getOfflineCode()
      }, 30 * 1000)
    },
    /**
     * 绘制会员码
     */
    async getBarCode(code) {
      let width = this.windowWidth * 0.7
      const jsBarcode = await JsBarcode
      let e = jsBarcode("#", code, {
        format: "CODE128"
      })
      let codeData = e._encodings[0][0].data
      let config = {
        height: 73,
        width: 2,
        marginTop: 10,
        marginleft: this.windowWidth*0.1,
        lineColor: "black"
      }
      // 组件里调用要添加this
      let context = wx.createCanvasContext('canvasbarcode', this)
      let top = config.marginTop
      context.strokeStyle = "#000000"
      config.width = width / codeData.length
      context.lineWidth = config.width
      let s = config.marginleft
      for (var i = 0; i < codeData.length; i++) {
        var l = s + i * config.width;
        if (codeData[i] === "1") {
          context.beginPath()
          context.moveTo(l, top)
          context.lineTo(l, top + config.height)
          context.stroke()
        }
      }
      const that = this
      context.draw(false, () => {
        that.canvasToTempFilePath(that)
      })
    },
    /**
     * 转换成图片
     */
    canvasToTempFilePath(that) {
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: 600,
        height: 150,
        quality: 1,
        canvasId: 'canvasbarcode',
        success: function (res) {
          console.info('canvasToTempFilePath success', res)
          that.setData({
            memberCode: res.tempFilePath
          })
        },
        fail: function (res) {
          console.log('失败了', res)
          if (that.times > 0) {
            that.times--
            that.getBarCode(that.data.code)
          }
        }
      }, that)
    },
    /**
     * 跳转页面
     */
    navigateToPage(e) {
      const { type } = e.currentTarget.dataset
      const router = {
        'coupon': '/userA/pages/coupon/index',
        'duiba': '/h5/pages/duiba/index',
        'deposit': '/userA/pages/deposit/index',
      }
      if (!router[type]) return
      wx.navigateTo({
        url: router[type]
      })
      type && sensors.track('Click', memberInfoReport[type])
    },
    /**
     * 获取账户余额
     */
    async getBalance() {
      const { userID, isEshopNewCustomer = false } = wx.getStorageSync('user') || {}
      const param = {
        customerID: userID,
        isNeedBalance: 'Y',
        isNeedIntegral: 'Y',
        isNeedCoupon: 'Y',
        notOnlyApplicableChannels: [10000],
        showFruitGradeCouponCount: 'Y'
      }
      const { data: {
        walletAmount = 0,
        integralAmount = 0,
        couponsAmount = 0,
        expireSoonNum = 0
      } = {} } = await app.api.getUserAmount(param)
      // 是新客且券包数量大于0才展示新人券包角标
      const showNewCustomerTip = isEshopNewCustomer && couponsAmount > 0
      const balanceInfo = {
        couponsAmount,
        expireSoonNum,
        integralAmount,
        walletAmount
      }
      this.setData({
        balanceInfo,
        showNewCustomerTip
      })
    },
    /**
     * 获取离线会员码消费后展示消费金额
     * pos改版了,已经不会有数据响应回来了
     * 就不轮询这个东西了
     */
    getLocalCodePaymentInfo() {
      if (this.intervalCount > 120) {
        clearInterval(this.codeTimer)
        return
      }
      this.intervalCount++
      const { userToken, userID } = wx.getStorageSync("user")
      let params = { customerID: userID, userToken, "_appInfo": { "os": "wxApp" } };
      let options = {
        encryptFlag: true,
        url: '/customerManager/getLocalCodePaymentInfo',
        data: params
      }
      commonObj.requestData(options, (res) =>  {
        let data = res.data.data
        // 支付成功
        if (data) {
          const token = wx.getStorageSync("token")
          data = JSON.parse(commonObj.Decrypt(data, token) || '{}')
          wx.redirectTo({
            url: `/userB/pages/codePaySuccess/index?payAmount=${data.payAmount}`
          })
          clearInterval(this.codeTimer)
        }
      })
    },
    /**
     * 调用微信offlinePay
     */
    async callOfflinePay() {
      if (!this.data.canUseOfflinePay) {
        wx.showToast({
          title: '微信升级到最新版本后，才可以使用哦',
          icon: 'none',
          duration: 2000
        })
        return
      }
      const decryptKey = await getEncryptKey('pwd')
      const that = this
      const appId = 'wx1f9ea355b47256dd' // 公众平台appId
      if (that.data.canUseOfflinePay) {
        // 用户微信支持调用
        wx.showLoading({
          title: '加载中...',
          mask: true
        })
        try {
            const res = await app.api.getWechatOfflinePaySign({ appId })
            wx.hideLoading()
            if (res.errorCode === 0 && res.data) {
              let data = JSON.parse(commonObj.Decrypt(res.data, decryptKey))
              wx.openOfflinePayView({
                'appId': appId,
                'timeStamp': data.timeStamp + '',
                'nonceStr': data.nonceStr,
                'package': data.package,
                'signType': data.signType,
                'paySign': data.paySign,
                'success': function (res) {
                },
                'fail': function (res) {
                  console.log(res)
                  wx.reportAnalytics('weixin_pay_fail')
                },
                'complete': function (res) {
                  console.log('complete', res)
                }
              })
            }
            else {
              console.log(res.data.errorMsg)
              commonObj.showModal('提示', '系统繁忙，请求超时', false, '我知道了')
            }
        } catch {
          wx.hideLoading()
        }
      }
      sensors.track('Click', '1810_181003004')
    },
    /**
     * 设置屏幕长亮
     */
    setKeepScreenOn() {
      // 获取屏幕亮度
      wx.getScreenBrightness({
        success:  res => {
          this.screenBrightness = res.value
          if (this.screenBrightness < 0.5) {
            wx.setScreenBrightness({
              value: 0.5
            })
          }
        }
      })
      // 保持屏幕长亮
      wx.setKeepScreenOn({
        keepScreenOn: true
      })
    },
    /**
     * 取消设置屏幕常亮
     */
    cancelSetKeepScreenOn() {
      wx.setKeepScreenOn({
        keepScreenOn: false
      })
      const { platform = 'ios' } = wx.getStorageSync('systemInfo') || {}
      // 恢复原来的亮度
      if (this.screenBrightness) {
        wx.setScreenBrightness({
          value: platform === 'ios' ? this.screenBrightness : '-1'
        })
      }
    },
    /**
     * 组件关闭时触发，清空定时器，恢复页面亮度，清除页面节点
     */
    componentClose() {
      clearTimeout(memberTimer)
      clearInterval(this.codeTimer)
      this.cancelSetKeepScreenOn()
    },
    /**
     * 控制会员码的展示和隐藏
     */
    controlMemberCode() {
      const { showCode } = this.data
      if(showCode) {
        this.setData({
          showCode: false
        })
        this.componentClose()
      } else {
        this.setData({
          showCode: true
        })
        this.initPage()
      }
      sensors.track('Click', this.data.showCode ? '1810_181003008' : '1810_181003007')
    },
    /**
     * 去登录
     */
    navigateLogin() {
      app.signIn()
      sensors.track('Click', '1810_181001001')
    }
  },
  ready() {
    // 判断用户微信版本是否能拉起线下刷卡支付
    const canUseOfflinePay = !!wx.openOfflinePayView
    this.setData({
      canUseOfflinePay
    })
    let that = this
    wx.getSystemInfo({
      success(t) {
        that.windowWidth = t.windowWidth
      }
    })
    this.initPage()
    this.intervalCount = 0
    this.times = 3
  },
  detached() {
    this.componentClose()
  },
  lifetimes: {
    created() {
      this.getOfflineCode()

      app.event.on('refreshMemberCenterCoupons', () => {
        setTimeout(() => {
          this.getBalance()
          app.event.off('refreshMemberCenterCoupons')
        }, 2000)
      })
    }
  },
  pageLifetimes: {
    /**
     * 因为父组件默认设置这个组件的展示为false，所以跳过了页面的onShow事件
     * 在离开页面回来后，需要重新执行以下逻辑
     */
    show() {
      // 设置屏幕变亮
      const { showCode } = this.data
      if (showCode) {
        this.setKeepScreenOn()
      }
      // this.codeTimer = setInterval( () => {
      //   this.getLocalCodePaymentInfo()
      // }, 1500)
      this.getOfflineCode()
    },
    hide: function() {
      // 页面被隐藏
      this.componentClose()
    },
  }
})
