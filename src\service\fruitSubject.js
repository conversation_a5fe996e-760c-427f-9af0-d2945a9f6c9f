
const { deepClone } = require('../utils/util.js')
const {
  forMatGoods, collectionGoodsSn
} = require('./fruitGoodsUtil.js')
const {
  FruitGoods
} = require('./fruitGoods.js')
const { deliveryCenterCodeDmTransEshop } = require('../utils/deliveryCenterUtil')
const cos = require('./cosInstance')
const { fetchJson } = require('~/utils/readAndSaveFile/index.js')
const { flatGoodsList } = require('~/service/fruitGoodsUtil.js')
import { getDefaultGoods } from '~/utils/goods/getDefaultGoods'
const topIcon = {
  '2': {
    '1': 'https://resource.pagoda.com.cn/dsxcx/images/bc8a716a30921b6de741dc9c4ba1200e.png',
    '2': 'https://resource.pagoda.com.cn/dsxcx/images/d3b901d46b9e8312bca9220be3c0213a.png',
    '3': 'https://resource.pagoda.com.cn/dsxcx/images/95cf0186065f3e7fcbbc3649de40b035.png',
    '4': 'https://resource.pagoda.com.cn/dsxcx/images/0d0975d8f89e886b33d0042f6c928a02.png',
    '5': 'https://resource.pagoda.com.cn/dsxcx/images/6d2fa43f9add9ef8dddff62e85d4291a.png',
    '6': 'https://resource.pagoda.com.cn/dsxcx/images/204fb15039bb780e3b4bf63d093ac55b.png',
  },
  '1': {
    '1': 'https://resource.pagoda.com.cn/dsxcx/images/89b3c0bbce765f81b3c1a89467cf9945.png',
    '2': 'https://resource.pagoda.com.cn/dsxcx/images/b6f2cb830516d30ae0377b8c407a99bb.png',
    '3': 'https://resource.pagoda.com.cn/dsxcx/images/329757c6660302755c043740767588a6.png',
    '4': 'https://resource.pagoda.com.cn/dsxcx/images/094ffd45128223d0070ffd7de90e3d54.png',
    '5': 'https://resource.pagoda.com.cn/dsxcx/images/38665a8b3b4f2e25b0008f19d9047df0.png',
    '6': 'https://resource.pagoda.com.cn/dsxcx/images/d6529cc7bc75fbcb30c50b4695da2e52.png',
  }
}
const { baseUrl } = require('./../utils/config')
const IMG_URL = baseUrl.PAGODA_PIC_DOMAIN
/**
 * 模块类型枚举
 */
const moduleTypeEnum = {
  图片: '1',
  单排商品: '2',
  双排商品: '3',
  三排商品: '6',
  榜单: '9',
}

class SubjectConfig {
  constructor({ activityID, isOnlyTopGoodsModule = false, isNeedMerge = false }) {
    this.activityID = activityID;
    this.isOnlyTopGoodsModule = isOnlyTopGoodsModule
    const { deliveryCenterCode, cityCode, storeCode } = wx.getStorageSync('timelyCity') || {};
    const { userID } = wx.getStorageSync('user') || {};
    const { unionid = '' } = wx.getStorageSync('wxSnsInfo') || {};
    this.deliveryCenterCode = deliveryCenterCode
    this.cityCode = cityCode
    this.storeCode = storeCode
    this.userID = userID
    this.unionid = unionid
    /**
     * 是否聚合
     */
    this.isNeedMerge = isNeedMerge
    // 不需要一品多规去重的商品 即图片模块的商品
    this.noNeedCollectMap = new Map()
    this.needCollectMap = new Map()
    this.picModuleGoodsMap = new Map() // 图片模块商品map
    this.goodsModuleGoodsMap = new Map() // 商品模块商品map
    this.topGoodsModuleGoodsMap = new Map() // 置顶商品模块商品map
  }
  async getModule() {
    // 获取综合专题配置
    const subjectModuleConfig = await this.getSubjectConfig()
    if (this.isOnlyTopGoodsModule) {
      this.filterGoodsAllModule(subjectModuleConfig)
    }
    let configGoodsList = []
    let isRequestModule = false
    subjectModuleConfig.togetherModuleList.forEach( (module) => {
      // 图片模块跳转商品详情的时候也需要查询商品信息
      // 图片模块的商品不需要聚合，商品模块的商品需要聚合
      if (['2','3','6', '9'].includes(module.type)) { // 商品模块：单排、双排、三排、榜单模块
        module.goodsList = this.filterGoodsModule(module)
        configGoodsList = configGoodsList.concat(module.goodsList)
      } else if (module.type === '1') { // 图片模块
        isRequestModule || (isRequestModule = true)
        // module.activityPicList = this.filterPicModule(module)
        const picConfigGoodsList = this.filterPicModuleGoods(module)
        configGoodsList = configGoodsList.concat(picConfigGoodsList)
      } else if (module.type === '4') { // 优惠券模块
        isRequestModule || (isRequestModule = true)
      }
    })
    if(this.isOnlyTopGoodsModule) {
      isRequestModule = false
    }
    /**
     * configGoodsList包含商品模块和图片模块的商品，这里都会进行聚合
     */
    const options = configGoodsList.map(item => ({
      goodsSn: String(item.goodsSn || ''),
      eshopGoodsId: String(item.goodsId || ''),
      takeawayAttr: item.takeawayAttr
    })).filter( item => item.goodsSn || item.eshopGoodsId )
    const fruitGoods = new FruitGoods({
      storeCode: this.storeCode,
      cityCode: this.cityCode,
      deliveryCenterCode: this.deliveryCenterCode,
      isNeedMergeByGroup: this.isNeedMerge,
      isNeedMergeMultiSpec: this.isNeedMerge
    })
    const getGoodsMap = async () => {
      try {
        // 拿到商品基础详情，可能会重复(因为入参可能会重复)
        const base = await fruitGoods.getGoodsBaseInfoList(options, false)
        if (!Array.isArray(base) || !base.length) return {
          picGoodsMap: new Map,
          goodsGoodsMap: new Map,
          topGoodsGoodsMap: new Map,
        }
        // 去重
        const afterCollectList = collectionGoodsSn(base, false)
        // setGoodsDynamicInfo 方法会扁平化一品多规，且返回深拷贝的结果
        const complete = await fruitGoods.setGoodsDynamicInfo(afterCollectList)
        // 扁平化，并且返回map
        const { eshopGoodsIdMap, goodsSnMap, noFlatGoodsSnMap, noFlatEshopGoodsIdMap } = flatGoodsList(complete)
        // 图片模块遍历
        const picGoodsMap = new Map()
        this.picModuleGoodsMap.forEach((value, key) => {
          const ketStr = String(key)
          const goodsDetail = noFlatEshopGoodsIdMap.get(ketStr) || noFlatGoodsSnMap.get(ketStr)
          // 图片模块，每一个配置都能取到对应商品
          if (goodsDetail) picGoodsMap.set(ketStr, goodsDetail)
        }, new Map())
        // 商品模块遍历
        const goodsGoodsMap = new Map()
        this.goodsModuleGoodsMap.forEach((value, key) => {
          const ketStr = String(key)
          // 因为去重，可能会找不到，商品已经被聚合到一品多规列表里了
          // 这里相当于聚合了，如果配置多个规格的，只有取第一个规格会取到商品
          const goodsDetail = noFlatEshopGoodsIdMap.get(ketStr) || noFlatGoodsSnMap.get(ketStr)
          if (goodsDetail) goodsGoodsMap.set(key, goodsDetail) 
        })
        const topGoodsGoodsMap = new Map()
        this.topGoodsModuleGoodsMap.forEach((value, key) => {
          const ketStr = String(key)
          const goodsDetail = noFlatEshopGoodsIdMap.get(ketStr) || noFlatGoodsSnMap.get(ketStr)
          if (goodsDetail) topGoodsGoodsMap.set(key, goodsDetail) 
        })
        return {
          picGoodsMap,
          goodsGoodsMap,
          topGoodsGoodsMap,
        }
      } catch (error) {
        console.log('getModule.getGoodsMap', error);
        return {
          picGoodsMap: new Map,
          goodsGoodsMap: new Map,
          topGoodsGoodsMap: new Map,
        }
      }
    }
    const [
      complateGoodsMap,
      moduleMap
    ] = await Promise.all([
      getGoodsMap(),
      // fruitGoods.getGoodsComplateInfoMap(options),
      isRequestModule ? this.getCouponModule(): {}
    ])
    const { couponModuleMap = {}, picModuleMap = {} } = moduleMap
    return {
      picModuleMap,
      couponModuleMap,
      complateGoodsMap,
      subjectModuleConfig,
      fruitGoods
    }
  }

    /**
   * @description  过滤出为小程序配置的图片模块
   * @param {*} module
   * @returns
   */
  filterPicModule(activityPicList) {
    if(!activityPicList || !activityPicList.length) {
      return []
    }
    const list = []
    activityPicList.forEach(item => {
      // 过滤非小程序渠道
      if (item.platJump && item.platJump !== '2') {
        return
      }
      // 全国送过滤其他城市
      if (item.openType === '62' && item.deptCode && item.deptCode !== this.cityCode) {
        return
      }
      // 及时达商品过滤其他配送中心
      if (item.openType === '3' && item.deptCode && item.deptCode !== deliveryCenterCodeDmTransEshop(this.deliveryCenterCode)) {
        return
      }
      // 次日达商品过滤其他配送中心
      if (item.openType === '63' && item.deptCode) {
        const { selectAddressInfo } = wx.getStorageSync('bgxxSelectLocateInfo') || {}
        const { deliveryCenterCode } = selectAddressInfo || {}
        if (item.deptCode !== deliveryCenterCodeDmTransEshop(deliveryCenterCode)) {
          return
        }
      }
      list.push(item)
    });
    return list
  }
  /**
   * @description 过滤出商品模块
   * @param {*} subjectModuleConfig
   */
  filterGoodsAllModule(subjectModuleConfig) {
    subjectModuleConfig.togetherModuleList = subjectModuleConfig.togetherModuleList.filter( module => ['9'].includes(module.type))
  }
  /**
   * @description 获取优惠券模块
   * @returns
   */
  async getCouponModule() {
    const params = {
      customerID: this.userID || -1,
      unionid: this.unionid || '',
      activityID: Number(this.activityID),
      deliveryCenterCode: this.deliveryCenterCode,
      cityCode: this.cityCode
    };

    try {
      const result = await getApp().api.getSubjectCouponModule(params);
      const couponModuleMap = {};
      const picModuleMap = {}
      const { couponModuleList = [], picModule = [] } = result.data || {}
      couponModuleList.forEach(item => {
        couponModuleMap[item.moduleNumber] = [item];
      });

      for( const key in picModule) {
        picModuleMap[key] = this.filterPicModule(picModule[key] || [])
      }
      return {
        couponModuleMap,
        picModuleMap
      };
    } catch (err) {
      console.log(err)
      return {};
    }
  }
  /**
   * @description 从cos拉取综合专题数据
   * @returns
   */
  async getSubjectConfig() {
    try {
      const cosUrl = cos.completeNoAuthUrl(`/activity/${this.activityID}.json`)
      const result = await fetchJson(cosUrl)
      if (!result) {
        return;
      }
      return result;
    } catch (err) {
      console.error('getSubjectConfig:', err)
      return;
    }
  }

  /**
   * @description 按配置配送中心过滤
   * @param {*} module
   * @returns
   */
  filterGoodsModule(module) {
    const isB2C = module.goodsType === '2'
    const takeawayAttr = isB2C ? 'B2C' : '及时达'
    return module.goodsList.filter(item => {
      item.takeawayAttr = takeawayAttr
      // b2c 用 goodsSn, 及时达用 goodsId
      const { goodsSn, goodsId } = item
      const skuStr = isB2C ? goodsSn : String(goodsId)
      const obj = {
        goodsSn: isB2C ? skuStr : '',
        goodsId: isB2C ? '' : skuStr,
      }
      if (module.type === '9') {
        this.topGoodsModuleGoodsMap.set(skuStr, obj)
      } else {
        this.goodsModuleGoodsMap.set(skuStr, obj)
      }
      if (takeawayAttr === 'B2C') {
        return item.deptCode ? item.deptCode === this.cityCode : true
      } else if(takeawayAttr === '及时达') {
        return item.deptCode ? item.deptCode === this.deliveryCenterCode : true
      }
    })
  }
  /**
   * @description 取出图片模块的商品信息，用于查询商品详情
   * @param {*} module
   * @returns
   */
  filterPicModuleGoods(module) {
    const goodsList = []
    module.activityPicList.forEach(picItem => {
      const { openType, openValue, goodsSn } = picItem;
      //  62 全国送  3 及时达
      if (["3", "62"].includes(openType)) {
        const obj = {
          goodsSn: openType === "62" ? openValue : goodsSn || '',
          goodsId: openType === "3" ? openValue : '',
          takeawayAttr: openType === "62" ? "B2C" : "及时达"
        }
        const openValueStr = String(openValue)
        this.picModuleGoodsMap.set(openValueStr, obj)
        goodsList.push(obj);
      }
    });
    return goodsList
  }
}

class SubjectProcessor {
  constructor({
    subjectModuleConfig,
    complateGoodsMap,
    fruitGoods,
    couponModuleMap, picModuleMap,
  }) {
    this.subjectModuleConfig = subjectModuleConfig;
    this.complateGoodsMap = complateGoodsMap;
    this.fruitGoods = fruitGoods;
    this.couponModuleMap = couponModuleMap;
    this.picModuleMap = picModuleMap
    this.navigationIndex = -1;
    this.navigationList = [];
  }
  // Main method to process all modules
  processModules() {
    const subjectModuleConfig = this.subjectModuleConfig
    subjectModuleConfig.togetherModuleList.forEach((module, index) => {
      const { type } = module;

      switch (type) {
        case '1':
          this.processImageModule(module);
          break;
        case '2':
        case '3':
        case '6':
          this.processGoodsModule(module);
          break;
        case '9':
          this.processTopGoodsModule(module);
          break;
        case '4':
          this.processCouponModule(module);
          break;
        case '7':
          this.processNavigationModule(module, index);
          break;
        case '10':
          this.processVideoModule(module, index);
          break;
        // Handle other module types if needed
        default:
          break;
      }
    });
    
    const togetherModuleList = subjectModuleConfig.togetherModuleList.filter( module => {
      if (module.type === '1') {
        return module.activityPicList.length > 0
      }
      return true
    })
    // Add navigation module to the appropriate position
    if (this.navigationIndex > -1) {
      togetherModuleList.splice(this.navigationIndex, 0, {
        type: '999',
        navigationList: this.navigationList
      });
    }
    subjectModuleConfig.togetherModuleList = togetherModuleList
    subjectModuleConfig.navigationList = this.navigationList
    subjectModuleConfig.activityID = subjectModuleConfig.id
    subjectModuleConfig.cardSharePic = subjectModuleConfig.cardSharePic?.picUrl
    return subjectModuleConfig;
  }
  processImageModule(module) {
    module.activityPicList = this.picModuleMap[module.modulePriority] || []

    module.activityPicList.forEach(picItem => {
      const { openType, openValue } = picItem;
      if (["3", "62"].includes(openType)) {
        const { picGoodsMap } = this.complateGoodsMap
        const skuStr = String(openValue)
        const goodsInfo = picGoodsMap.get(skuStr);
        if (goodsInfo) {
          picItem.goodsInfo = {
            ...goodsInfo,
            // specificationGoodsList: []
          }
        }
        // 这里删除了，下一个配置的多规格就取不到聚合商品
        picGoodsMap.delete(skuStr)
      }
    });
    module.activityPicList =  module.activityPicList.filter(item => {
      const { openType } = item;
      if (["3", "62"].includes(openType)) {
        if (!item.goodsInfo) {
          return false;
        }
      }
      return true;
    })
  }
  // Separate method for processing "商品模块" (Goods module)
  processGoodsModule(module) {
    const sellList = [];
    const sellOutList = [];
    const isB2C = module.goodsType === '2'
    module.goodsList.forEach(goods => {
      const { goodsSn, goodsId } = goods
      const skuStr = isB2C ? String(goodsSn) : String(goodsId)
      const { goodsGoodsMap } = this.complateGoodsMap
      const curGoods = goodsGoodsMap.get(skuStr);
      if (curGoods) {
        const cloneGoods = deepClone(curGoods);
        if (cloneGoods.stockNum !== 0 && cloneGoods.saleStatus === 1) {
          sellList.push(cloneGoods);
        } else {
          sellOutList.push(cloneGoods);
        }
        // 这里删除了，下一个配置的多规格就取不到聚合商品
        goodsGoodsMap.delete(skuStr)
      }
    });
    module.goodsList = sellList.concat(sellOutList);
  }
  processTopGoodsModule(module) {
    const goodsList = []
    const tagtype = module.tagType || 0
    const icon = topIcon[String(tagtype)] || {}
    let index = 0
    // 将榜单图片模版类型转换为图片模块类型
    const obj = {
      '1': '3',
      '2': '12',
      '3': '4',
      '4': '6',
      '5': '10',
    }
    module.goodsList.forEach(goods => {
      const { goodsId, templatePic, templateType } = goods
      // const skuStr = isB2C ? String(goodsSn) : String(goodsId)
      const skuStr =  String(goodsId)
      const { topGoodsGoodsMap } = this.complateGoodsMap
      const curGoods = topGoodsGoodsMap.get(skuStr);
      if (curGoods) {
        const cloneGoods = deepClone(curGoods);
        goodsList.push(Object.assign(goods, {
          goodsInfo: cloneGoods,
          templatePic,
          goodsTemplate: obj[templateType],
          pic: templatePic,
          openType: '',
          openValue: '',
        }))
      }
    });
    // 榜单模块单独楼层进行去重
    const goodsSnMap = new Map()
    module.goodsList = goodsList.filter(item => {
      if (!item.goodsInfo) {
        return false
      }
      const { goodsObj } = getDefaultGoods(item.goodsInfo)
      if(!goodsSnMap.has(goodsObj.goodsSn)) {
        goodsSnMap.set(goodsObj.goodsSn, {})
        return true
      }
      return false
    }).map( item => { 
      index++
      Object.assign(item, {
        topIconUrl: icon[String(index)]
      })
      return item
    })

  }
  processCouponModule(module) {
    const curActivityCouponList = module.activityCouponList.filter(item => this.couponModuleMap[item.moduleNumber]);
    const { moduleNumber } = curActivityCouponList[0] || {};
    module.couponList = this.couponModuleMap[moduleNumber] || [];
  }
  processNavigationModule(module, index) {
    if (this.navigationIndex < 0) {
      this.navigationIndex = index;
    }
    if (module.navigationList) {
      this.navigationList.push(module.navigationList[0]);
    }
  }
  // 视频处理  
  processVideoModule(module) {
    const { video, videoCover, modulePriority } = module
    module.videoObj = {
      videoCover: IMG_URL + videoCover.url,
      videoUrl: IMG_URL + video.url,
      width: video.maxWidth,
      height: video.maxHeight,
      id: modulePriority
    }
  }
}

class FruitSubject {
  constructor({activityID, isOnlyTopGoodsModule = false, isNeedMerge = false}) {
    this.activityID = activityID
    this.isOnlyTopGoodsModule = isOnlyTopGoodsModule
    this.fruitGoods = null
    this.isNeedMerge = isNeedMerge
    this.subjectModuleConfig = null
  }
  /**
   * @description 综合专题使用，通过预请求获取详情
   */
  async getDetailWithPreRequest() {
    const cache = FruitSubject._requestCache[this.activityID]
    if (cache) {
      FruitSubject._requestCache = {}
      return cache
    }
    const result = await this.getDetail();
    return result;
  }
  /**
   * @description 预请求，广告位点击跳转综合专题前请求
   */
  async preRequest() {
    FruitSubject._requestCache[this.activityID] = this.getDetail()
  }
  /**
   * @description 获取综合专题详情
   */
  async getDetail() {
    const subjectConfig = new SubjectConfig({ activityID: this.activityID, isOnlyTopGoodsModule: this.isOnlyTopGoodsModule, isNeedMerge: this.isNeedMerge})
    const {
      picModuleMap,
      couponModuleMap,
      complateGoodsMap,
      subjectModuleConfig,
      fruitGoods
    } = await subjectConfig.getModule()
    this.fruitGoods = fruitGoods
    this.subjectModuleConfig = subjectModuleConfig
    const subjectProcessor = new SubjectProcessor({
      subjectModuleConfig, complateGoodsMap, fruitGoods, couponModuleMap, picModuleMap
    })
    const subjectModule = await subjectProcessor.processModules()
    return subjectModule
  }
  /**
   * @description 返回综合专题商品列表
   * @param {*} goodsLength 需要返回的商品长度
   * @param {*} isGetStoreGoods 不足长度是否需要补充门店其他商品  目前仅焦点图商品需要补充
   * 目前应用场景：首页焦点图商品区域、切分banner、下午茶模块
   */
  async getGoodsModule({
    goodsLength,
    isGetStoreGoods = false,
    isFilterSold = false
  }) {
    const subjectModule = await this.getDetail()
    if (!subjectModule.togetherModuleList) {
      return []
    }
    let goodsList = []
    subjectModule.togetherModuleList.forEach((module) => {
      const { goodsList: moduleGoodsList = [] } = module || {}
      if (['2','3','6'].includes(module.type)) {
        goodsList = goodsList.concat(moduleGoodsList)
      }
    })
    if (isGetStoreGoods) {
      goodsList.forEach( goods => goods.goodsSource = 'topic' )
    }
    if (isGetStoreGoods && goodsList.length < goodsLength) {
      await this._pushStoreGoods(goodsList, goodsLength)
    }
    let activityGoodsList = goodsLength ? goodsList.slice(0, goodsLength): goodsList
    if (isFilterSold) {
      activityGoodsList = activityGoodsList.filter( item => item.stockNum !== 0 && item.saleStatus === 1)
    }
    return {
      [this.activityID]: activityGoodsList
    }
  }
  async getTopGoodsList() {
    const subjectConfig = new SubjectConfig({ activityID: this.activityID, isOnlyTopGoodsModule: true, isNeedMerge: this.isNeedMerge})
    const {
      picModuleMap,
      couponModuleMap,
      complateGoodsMap,
      subjectModuleConfig,
      fruitGoods
    } = await subjectConfig.getModule()
    this.fruitGoods = fruitGoods
    this.subjectModuleConfig = subjectModuleConfig
    const subjectProcessor = new SubjectProcessor({
      subjectModuleConfig, complateGoodsMap, fruitGoods, couponModuleMap, picModuleMap
    })
    const subjectModule = await subjectProcessor.processModules()
    const { togetherModuleList: topicList = [] } = subjectModule
    if (topicList.length) {
      let list = topicList[0].goodsList || []
      if (list.length < 3) {
        // 如果第一个模块商品数量不足3个,则尝试获取第二个模块的商品列表
        list = (topicList[1] && topicList[1].goodsList) || []
      }
      return list.map(item => item.goodsInfo)
    }
    return []
  }

  /**
   * @desc 增加获取综合专题配置的方法
   */
  async getSubjectConfig() {
    if (this.subjectModuleConfig) return this.subjectModuleConfig
    const subjectConfig = new SubjectConfig({ activityID: this.activityID, isOnlyTopGoodsModule: this.isOnlyTopGoodsModule, isNeedMerge: this.isNeedMerge})
    const config = await subjectConfig.getSubjectConfig()
    if (this.isOnlyTopGoodsModule) {
      config.togetherModuleList = config.togetherModuleList.filter( module => ['9'].includes(module.type))
    }
    return config
  }

  /**
   * 过滤存在套图的模块
   * @param {*} moduleList 
   * @returns 
   */
  filterSignatureImageModule(moduleList) {
    return moduleList.filter(module => [
      moduleTypeEnum.图片,
      moduleTypeEnum.单排商品,
      moduleTypeEnum.双排商品,
      moduleTypeEnum.三排商品,
    ].includes(module.type))
  }
  /**
   * @description 当综合专题有效商品不满足展示要求时，补充门店其他商品，用于首页焦点图
   * @param {*} goodsList
   * @param {*} goodsLength
   */
  async _pushStoreGoods(goodsList, goodsLength) {
    const goodsMap = await this.fruitGoods.getCurStoreGoodsMap()
    if(!goodsMap.size) {
      return
    }
    for (let key of goodsMap) {
      if(goodsList.length >= goodsLength) break;
      const isHas = goodsList.find( item => item.goodsSn === key[0])
      if (!isHas) {
        const curGoods = goodsMap.get(key[0],key)
        goodsList.push(Object.assign(forMatGoods(curGoods), { goodsSource: 'store'}))
      }
    }
  }
}
FruitSubject._requestCache = {};
module.exports = {
  FruitSubject,
  moduleTypeEnum,
}

