// userA/pages/couponGoods/index.js
let app = getApp();
let cartMinxins = require('../../../mixins/cartMixin')
let sensor = require('../../../utils/report/sensors')
import { createStoreBindings } from 'mobx-miniprogram-bindings'
import fruitCartStore from '../../../stores/module/fruitCart'
import { freshCartStore } from '../../../stores/module/freshCart'
import {
  drawBarcode
} from '../../../utils/services/canvasUtil'
import { isExchangeCard } from '../../../source/const/coupon'
import { vegetablesLocateInfruit } from '../../../mixins/vegetablesLocateInfruit'
import { ORDER_TYPE } from '../../../source/const/order';
import shopCartStore from '../../../stores/shopCartStore';
import { reaction } from 'mobx-miniprogram';
const { getMemberPrice, getCustomerInfo, checkIsShowActivityPrice } = require('../../../source/js/requestData/activityPrice')
const locateMixin = require('../../../mixins/locateMixin')
const { FOLD_ICON } = require('../../../source/const/goodsImage')
const FruitCouponGoodsPromise = require.async('../../../componentsSubPackage/commonUtils/fruitCouponGoods/index')
const { debounce } = require('../../../utils/util')

/**
 * @typedef { import('../../../componentsSubPackage/commonUtils/fruitCouponGoods/index').FruitCouponGoods } FruitCouponGoods
 * @typedef { import('../../../componentsSubPackage/commonUtils/fruitCouponGoods/index').FruitCouponGoodsConstructor } FruitCouponGoodsConstructor
 */

Page({
  mixins: [cartMinxins, locateMixin],
  /**
   * 页面的初始数据
   */
  data: {
    ORDER_TYPE,
    priceGapList: [
      {label: '全部', min:0, max: 999999, hide: true},
      {label: '0-10元', min:0, max: 1000},
      {label: '10-20元', min: 1001, max: 2000},
      {label: '20-30元', min: 2001, max: 3000},
      {label: '30元以上', min: 3001, max: 999999}
    ],
    curIndex: 0,
    goodsList: [],
    isSupport: true,
    btnSwitch: false,
    isNeedB2CBusiness: false,
    FOLD_ICON,
    showQrcode: false,
    showGoods: false,
    couponObj: {},
    onSaleGoodsList: [],
    // 券门槛
    limitValue: 0,
    // 券规则在购物车已经满足使用该券的金额
    cartEnoughAmount: 0,
    /** 是否加载了优惠券信息了 */
    isLoadedCouponInfo: false,
    /** 是否是兑换卡 */
    isExchangeCard: false,
    /** 兑换卡适用商品是否在购物车中 */
    isExchangeCardGoodsInCart: false,
    /** 是否在加载中 */
    isLoading: false,

    /**是否展示售罄商品折叠按钮 */
    isShowGoodsFold: false,
    /**售罄商品是否已展开 */
    isOpenGoodsFold: false,
    /**售罄商品数量 */
    selloutCount: 0,
    // 是否有下一页
    hasNextPage: false
  },
  _data: {
    requestStatus: {
      isRequesting: false,
      // hasNextPage: false,
      pageSize: 50,
      pageNumber: 0,
    },
    goodsRequestObj: null,
    couponParams: {
      defineId: '',
      couponCode: "",
    },
    onLoadFlag: true,
    // 是否是心享会员
    IS_VIP_CUSTOMER: false,
    // 类型：0全部 1指定商品可用 2指定商品不可用
    type: 0,
    // 该券的可用或者不可用列表，根据type来判断
    specifyGoodsList: [],
    // 是否是仅配送优惠券
    isSupportDelivery: 'N',
    // 是否是仅自提优惠券
    isSupportTake: 'N',
    /**
     * @type {[key: String]: Number}
     * 每张券在计算特价商品时，超出部分应该用原价来计算优惠券力度，由于存在相同sku的商品选择了不同服务的商品，
     * 特价活动会被拆单，导致无法正确计算具体被统计了几次特价，这里需要记录goodsSn在享受特价的次数
     * e.g { 'G0001': 2 }
     */
    objActivityPrice: {},
    // 缓存的次日达商品信息
    fruitGoods: null,
    /** @type { { promise: Promise<FruitCouponGoodsConstructor> instance:FruitCouponGoods  } } */
    FruitCouponGoods: { promise: null, instance: null },
    // 关闭缺省图展示的定时器对象
    timerCloseLoading: null,
    // 果品等级限制范围
    fruitGradeSet: []
  },
  async fruitCouponGoodsInstance() {
    if (this._data.FruitCouponGoods.promise) { return this._data.FruitCouponGoods.promise }
    /** @type { FruitCouponGoodsConstructor } */
    const FruitCouponGoods = await FruitCouponGoodsPromise
    this._data.FruitCouponGoods.instance = new FruitCouponGoods({
      baseParams: {
        couponCode: this._data.couponParams.couponCode
      },
    })
    return this._data.FruitCouponGoods.instance
  },
  /**
   * 当进入页面时就触发计算门槛，因为可能从商详页返回的
   */
  onShow() {
    // 由于onLoad钩子先执行 这里会直接获取到值
    const couponWay = this.data.couponObj.couponWay
    //  上报页面浏览事件
    sensor.track(sensor.SCREENVIEW, couponWay ? {
      CouponWay: couponWay
    } : {})
    // 仅当计算过门槛的情况会触发重新计算 否则就会走OnLoad去计算
    if (this.data.isLoadedCouponInfo) {
      this.updateCarList(true)
    }

    this.unWatch = reaction(() => fruitCartStore.timelyShowGoodsList, () => {
      this.updateCarList(true)
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const {defineId = -1, couponCode = -1,  showQrcode = false, showGoods = false, couponName = '', useableCondition = [], effectTimeStr = ''} = (() => {
      return Object.keys(options).length ? this.getParamsFromH5(options): this.getParamsFromGlobal()
    })();
    this.setData({
      showQrcode, // 是否需要核销码
      showGoods, // 是否展示商品
      couponName,
      useableCondition, // 优惠券使用说明
      effectTimeStr // 生效标签
    })
    Object.assign(this._data.couponParams, {
      defineId,
      couponCode
    })
    app.globalData.fruitCouponGoodsParams = null
    // 设置标题
    const { isExchangeCard, couponObj } = this.data
    const title = `使用${isExchangeCard ? '兑换卡' : (couponObj.couponWay === '6' ? '代金券' : '优惠券')}`
    wx.setNavigationBarTitle({
      title
    })
    this.getStoreData()
    if (showQrcode) {
      this.drawCode(couponCode)
    }
		this.refreshCustomerInfo()
  },
  /**
   * 销毁前清除store绑定
   */
  onUnload() {
    this.storeBindings.destroyStoreBindings()
    this.storeBindingsShopCart.destroyStoreBindings()
    if (this.unWatch) {
      this.unWatch()
    }
  },
  /**
   * 获取和绑定store数据
   */
  getStoreData() {
    fruitCartStore.initStore({ app })
    this.storeBindings = createStoreBindings(this, {
      store: fruitCartStore,
      fields: [
        'onSaleGoodsList',
        'b2cGoodsList',
        'goodsCount',
        'timelyShowGoodsList',
        'timelyIsCheckedCount',
        'actPriceNewMaxDiscountUuid',
      ]
    })
    // 为了统计次日达商品数量
    this.storeBindingsShopCart = createStoreBindings(this, {
      store: freshCartStore,
      fields: [
        'allCheckedHandleListCount'
      ]
    })
  },
  /**
   * @desc h5页面跳转到这个页面，需要通过链接加参数的方式;暂时先这样处理
   */
  getParamsFromH5 (options) {
    // if (!Object.keys(options).length) return {}
    const {
      defineId = -1,
      couponCode = -1,
      channelSeparation,
      couponWay,
      effectTimeStr = '',
      couponName = '',
      useableCondition = '',
      ...coupon
    } = options

    this.setData({
      isExchangeCard: isExchangeCard(defineId),
      couponObj: {
        ...coupon,
        defineId,
        couponCode,
        couponName,
        useableCondition,
        effectTimeStr
      }
    })

    // 是否显示核销码 (免运券不显示核销码 渠道来源只要是门店就展示核销码)
    const showQrcode = couponWay !== '5' && channelSeparation.indexOf('P') >= 0
    // 是否显示商品  (小程序可用的展示商品)
    const showGoods = channelSeparation.indexOf('X') >= 0

    return { defineId, couponCode, couponName, useableCondition: JSON.parse(useableCondition) || [], showQrcode, showGoods, effectTimeStr }
  },

  getParamsFromGlobal () {
    const {
      defineId = -1,
      couponCode = -1,
      showQrcode = false,
      showGoods = false,
      couponName = '',
      useableCondition = [],
      effectTimeStr = '',
      pageFrom,
      ...coupon
    } = app.globalData.fruitCouponGoodsParams || {}
    this.setData({
      isExchangeCard: isExchangeCard(defineId),
      couponObj: {
        ...coupon,
        defineId,
        couponCode,
        couponName,
        useableCondition,
        effectTimeStr
      },
      isBackPrevPage: Boolean(pageFrom)
    })

    return {defineId, couponCode,  showQrcode, showGoods, couponName, useableCondition, effectTimeStr}
  },

  /**
   * 定位完成
   */
  async onLocateReady() {
    const { currentView = ''} = this.data
    if (currentView === 'content') {
      if (this.data.showGoods && this._data.onLoadFlag) {
        this._data.FruitCouponGoods.promise = this.fruitCouponGoodsInstance()
        await this._data.FruitCouponGoods.promise
        this.getCouponGoods()
        // 节约性能 不要频繁的请求购物车数据 有数据就直接展示了
        setTimeout(() => {
          // 放到下个任务队列，不影响商品获取
          if (!this.data.onSaleGoodsList || !this.data.onSaleGoodsList.length) {
            fruitCartStore.setPageParams()
            fruitCartStore.getCartData()
            // 触发获取次日达购物车列表
            vegetablesLocateInfruit(this)
          }
        })
        this._data.onLoadFlag = false
      }
    } else {
      // 定位失败或者无门店情况下，如果当前页面只展示商品(不展示核销码情况)需要提示回到首页
      if (!this.data.showQrcode) {
        const res = await app.showModalPromise({
          content: `${currentView === 'noLocationAuth' ? '定位权限未开启' : '当前地址无门店'}`,
          confirmText: '回首页',
        })
        if (res) {
          wx.switchTab({ url: '/pages/homeDelivery/index' });
        }
      } else {
        this.setData({
          showGoods: false
        })
      }
    }
  },

  bgxxLocateReady: function({ isOnlyShowData } = {}) {
    freshCartStore.getCartData({ fromShopCart: !isOnlyShowData })
  },

  drawCode(code) {
    if (!code) return
    this.setData({
      qrCouponCode: /\S{5}/.test(code) && code.replace(/\s/g, '').replace(/(.{4})/g, '$1 ')
    })
    // 线下券，使用说明默认平铺开展示
    if (!this.data.showGoods) {
      this.setData({
        btnSwitch: true
      })
    }
    const options = {
      app,
      code,
      cavansId: 'barcode1',
      width: 544,
      config: {
        top: 0,
        lineHeight: 220,
        left: 0,
      },
      tempFileOption: {
        width: 544,
        height: 220,
      }
    }

    drawBarcode(options, res => {
      const { tempFilePath } = res || {}
      this.setData({
        tempFilePath
      })
    })
  },

  async getGapGoods(event) {
    // 神策埋点
    sensor.track('MPClick', 'categoryCouponGoodsPriceRange')
    let {index = 0} = event.currentTarget.dataset
    // 如果是当前选中的索引 则直接当做点击全部按钮
    if (this.data.curIndex === index) {
      index = 0
    }
    await this._data.FruitCouponGoods.promise
    this._data.FruitCouponGoods.instance.resetStatus()
    this.setData({
      curIndex: index
    })
    this.resetRequestStatus()
    this.getCouponGoods()
  },
  resetRequestStatus() {
    this._data.requestStatus = {
      pageNumber: 0,
      pageSize: 50,
      isRequesting: false,
    }
    this.setData({
        // 是否有下一页
        hasNextPage: false,
        isLoading: false,
        /**是否展示售罄商品折叠按钮 */
        isShowGoodsFold: false,
        /**售罄商品是否已展开 */
        isOpenGoodsFold: false,
        /**售罄商品数量 */
        selloutCount: 0,
        onSaleGoodsList: [],
        goodsList: [],
        saleOutGoodsList: [],
        isSupport: true
    })
  },
  // 获取优惠券适用商品
  async getCouponGoods (status = 'init') {
    const isInit = status === 'init'
    isInit && wx.showLoading({
      title: '加载中',
      mask: true
    })
    this.changeRequestStatus()
    try {
      const { page, priceGap } = this.getRequestParams()
      const instance = this._data.FruitCouponGoods.instance
      const listPromise = instance.getGoodsList(page, priceGap)
      const requestId = instance.requestId
      this.setData({
        isLoading: true
      })
      const start = new Date().getTime()
      const res = await listPromise
      if (instance.notSameRequestId(requestId)) {
        console.warn('拦截', pageNum)
        this._data.requestStatus.pageNumber--
        this.closeLoading()
        return
      }
      this.changeRequestStatus(1)
      await this.handleGoodsRes(res)
      const end = new Date().getTime()
      console.log('getCouponGoods', end - start)
      this.closeLoading()
    } catch(err){
      console.warn('拦截catch', err)
      const { pageNumber } = this._data.requestStatus
      this.setData({
        isLoading: false,
        isSupport: pageNumber !== 1
      })
      this.changeRequestStatus(1)
      this._data.requestStatus.pageNumber = pageNumber - 1
    }
    isInit && wx.hideLoading()
  },
  getRequestParams() {
    const { pageNumber, pageSize } = this._data.requestStatus
    const priceGap = this.data.priceGapList[this.data.curIndex]
    return { page: {
      pageNumber,
      pageSize
    }, priceGap }
  },
  // 组装数据
  async handleGoodsRes(res) {
    const { coupon = {}, goodsList: normalGoodsList = [], listStatus } = res
    if (!this.data.isLoadedCouponInfo && Object.keys(coupon).length > 1) {
      const { limitValue, isSupportDelivery, isSupportTake, adapterGoodsType: type, adapterGoodsValues, fruitGradeSet } = coupon
      this.setData({
        isLoadedCouponInfo: true,
        // 满x元可用该券
        limitValue: limitValue || 0
      })
      this._data.type = type
      this._data.specifyGoodsList = (adapterGoodsValues || []).map(item => typeof item === 'string' ? item : item.goodsSn)
      // 是否仅配送
      this._data.isSupportDelivery = isSupportDelivery
      // 是否仅自提
      this._data.isSupportTake = isSupportTake
      // 果品等级券限制
      this._data.fruitGradeSet = fruitGradeSet
      // 触发更新购物车数量 重新计算凑单价格
      this.updateCarList()
      this.setData({
        couponObj: {
          ...coupon
        }
      })
    }
    const hasNextPage = !(listStatus.finished && listStatus.selloutFinished)

    let list = []
    // 如果包含售罄数据 则表示是底部区域了
    if (listStatus.hasSelloutGoods) {
      let newGoodsList = []
      /**当前展示售罄商品的数量：在售商品为偶数时，展示2个售罄商品，为奇数时，展示1个售罄商品 */
      const showSelloutCount = listStatus.findNormalCount % 2 === 0 ? 2 : 1

      //  未展示售罄折叠按钮时，将可展示的售罄商品拼接到列表展示
      if (!this.data.isShowGoodsFold) {
        const selloutCount = Math.max(listStatus.findSelloutCount - showSelloutCount, 0)
        this.setData({
          selloutCount,
          isShowGoodsFold: !!selloutCount,
        })

        newGoodsList = normalGoodsList.splice(0, showSelloutCount)

      }
      normalGoodsList.forEach(current => {
        current.isSellOutGoods = true
      })

      list = (this.data.goodsList || []).concat(newGoodsList).concat(normalGoodsList)
    } else {
      list = (this.data.goodsList || []).concat(normalGoodsList)
    }
    this.setData({
      goodsList: list,
      isSupport: !!list.length,
      hasNextPage
    })
    // 有效商品不足一屏时，继续请求商品
    const curIndex = this.data.curIndex
    if (hasNextPage && (list.length < 10 || !normalGoodsList.length)) {
      // 只要有可能请求下一页 这里都置为请求中 避免触底时重复请求
      this._data.requestStatus.isRequesting = true
      setTimeout(() => {
        this._data.requestStatus.isRequesting = false
        // console.warn('可能请求下一页', curIndex)
        // 如果还在这个价格区间就让你加载
        if (curIndex === this.data.curIndex) {
          // console.warn('即将请求下一页', curIndex)
          this.getCouponGoods('next')
        }
      }, 300)
    }
  },
  changeRequestStatus(status = 0) {
    if (status === 1) {
      this._data.requestStatus.isRequesting = false
    } else {
      Object.assign(this._data.requestStatus, {
        pageNumber: ++this._data.requestStatus.pageNumber,
        isRequesting: true
      })
    }
  },
  onReachBottomEvent: debounce(function() {
    // console.warn('触底了', this.data.hasNextPage, this._data.requestStatus.isRequesting)
    if (this.data.hasNextPage && !this._data.requestStatus.isRequesting) {
      this.getCouponGoods('next')
    }
  }, 400),
  toSwitch() {
    // 如果是兑换卡 则必须要有描述 或者 不是兑换卡
    if ((this.data.isExchangeCard && !!this.data.couponObj.ruleDescription) || !this.data.isExchangeCard) {
      this.setData({
        btnSwitch: !this.data.btnSwitch
      })
    }
  },
  /**
   * 获取用户是否是心享vip
   */
  refreshCustomerInfo() {
    const { IS_VIP_CUSTOMER = false } = getCustomerInfo() || {}
    this._data.IS_VIP_CUSTOMER = IS_VIP_CUSTOMER
  },
  /**
   * 去购物车
   */
  toCartPage(){
    const notIsChecked = this.data.timelyIsCheckedCount === 0
    const notGoods = this.data.timelyShowGoodsList.length === 0

    if (notIsChecked || notGoods) {
      //  有加购商品时，提示请选择，否则提示请加购
      const title = notGoods ? '请加购及时达商品后结算' : '请选择及时达商品后结算'
      wx.showToast({
        icon: 'none',
        title,
      })

      return
    }
    shopCartStore.fruitSubmit(ORDER_TYPE.TIMELY)
    const reportData = {
      "element_code": 130013002,
      "element_name": "结算",
      "element_content": "结算",
      "screen_code": 1223,
      "screen_name": "及时达优惠券适用商品页"
    }
    sensor.track('MPClick', reportData)
  },
  /**
   * @desc 回调
   * @param {Boolean} isRefresh 是否需要重新计算
   */
  updateCarList(isRefresh) {
    const { cartEnoughAmount, limitValue, onSaleGoodsList, isLoadedCouponInfo, couponObj, isExchangeCard, isExchangeCardGoodsInCart } = this.data
    // 如果不是强制刷新
    if (!isRefresh) {
      // 如果是兑换卡 则判断是否满足兑换卡使用条件 如果满足了就无须再次判断
      if (isExchangeCard) {
        // 是否在购物车中了
        if (isExchangeCardGoodsInCart) {
          return
        }
      }
      // 凑单已经满了 就没必要继续计算凑单了
      else if (cartEnoughAmount >= limitValue) {
        console.log('已经满足凑单')
        return
      }
    }
    // 当更新数据后 需要等待回调完成后mobx才能同步数据
    wx.nextTick(() => {
      console.log('*****', onSaleGoodsList)
      // 如果加载了卡券信息
      if (isLoadedCouponInfo) {
        // 如果是兑换卡
        if (isExchangeCard) {
          this.setExchangeCard()
          return
        }
        // 接下来是优惠券/代金券/兑换卡
        const isVoucherType = couponObj.couponWay === '6'
        this.setCoupon(isVoucherType)
      }
    })
  },
  /**
   * 当券为兑换卡时，需要动态计算是否满足适用商品
   */
  setExchangeCard() {
    const arrCartGoodsList = [...this.data.onSaleGoodsList]
    // 获取兑换卡所有goodsSn
    const { specifyGoodsList: arrGoodsSn } = this._data

    // 是否已经加购且勾选
    const isExchangeCardGoodsInCart = arrCartGoodsList.some(item =>  item.isChecked && arrGoodsSn.includes(item.goodsSn))

    this.setData({
      isExchangeCardGoodsInCart
    })
  },
  /**
   * 设置优惠券/代金券需要展示的门槛
   * @param {Boolean} isVoucher 是否是代金券 增加此参数是因为
   * 代金券可以无视商品的不可用券限制和特价等限制而能直接参与计算门槛
   * 而优惠券则需要根据`券+商品`的联合规则来判断是否可用券
   */
  setCoupon(isVoucher) {
    // 设置凑单信息
    const setAddOnInfo = () => {
      // 1. 获取代金券的门槛金额
      const { limitValue, onSaleGoodsList, b2cGoodsList } = this.data
      // 2. 遍历勾选项判断是否满足大于代金券的门槛
      let totalAmount = 0
      const { IS_VIP_CUSTOMER, isSupportDelivery, isSupportTake, fruitGradeSet } = this._data
      const arrCartGoodsList = [...onSaleGoodsList]
      // 如果是代金券 则全国送商品的金额需要计入
      if (isVoucher) {
        arrCartGoodsList.push(...(b2cGoodsList || []))
      } else {
        // 不是代金券 清空前一张券产生的特价商品对象
        this._data.objActivityPrice = {}
      }
      // 获取各个商品对应的兑换卡数量
      const goodsSnMapCardCount = this.getGoodsSnMapCardCount()
      for (let index = 0; index < arrCartGoodsList.length; index++) {
        // 浅拷贝 不修改原普通类型
        let element = arrCartGoodsList[index]
        // 仅匹配勾选的项
        if (element.isChecked) {
          const memberPrice = getMemberPrice(element)
          // 3. 判断购物车的某项是否满足参与计算金额门槛
          // 如果存在不符合计算总金额的项 将会被过滤掉
          // 仅优惠券会进入该方法
          // 部分商品没有心享价 则心享价就是原价
          element.heartPrice2 = element.heartPrice || memberPrice
          // 如果是已使用兑换卡 则参与的个数不参与计算
          if (goodsSnMapCardCount[element.goodsSn] > 0) {
            const count = element.count - goodsSnMapCardCount[element.goodsSn]
            element = { ...element, count}
          }
          // 如果是代金券
          if (isVoucher) {
            totalAmount += this.calItemVoucherAmount(element, IS_VIP_CUSTOMER)
          } else {
            // 如果是优惠券
            // 当前商品行是否能参与优惠券凑单
            const isNotSupportCoupon = this.getIsNotSupportCoupon(element, isSupportDelivery, isSupportTake, fruitGradeSet)
            // 如果能参与 则计算
            if (!isNotSupportCoupon) {
              totalAmount += this.calcJoinAmount(element, IS_VIP_CUSTOMER)
            }
          }

          // 满足门槛就退出循环
          if (totalAmount >= limitValue) {
            break
          }
        }
      }
      // 满足门槛 ? 取门槛限值 : 取当前购物车价格
      this.setData({
        cartEnoughAmount: totalAmount >= limitValue ? limitValue : totalAmount
      })
    }
    // 可能存在购物车数据还未同步的情况，这里需要等待购物车同步
    setTimeout(() => {
      setAddOnInfo()
    }, this.data.onSaleGoodsList && this.data.onSaleGoodsList.length ? 0 : 1000)
  },
  /**
   * 获取购物车每个商品对应的正在使用的兑换卡数量
   * @returns { [goodsSn: string]: number } 商品对应的兑换卡数量映射
   */
  getGoodsSnMapCardCount() {
    const { timelyShowGoodsList = [] } = this.data
    return timelyShowGoodsList.reduce((prev, current) => {
      // 如果这个商品没有匹配了兑换卡 则不参与计算
      if (!current.isExchangeCardGoods) {
        return prev
      }
      // 如果已经匹配兑换卡 且存在记录 则累加
      if (prev[current.goodsSn]) {
        prev[current.goodsSn] += current.count
      } else {
        // 不存在则新建
        prev[current.goodsSn] = current.count
      }
      return prev
    }, {})
  },
  /**
   * 计算当前券为代金券时，能参与满减的总金额
   * @param {Object} element 购物车的某一项
   * @param {Boolean} IS_VIP_CUSTOMER 是否是心享vip
   * @returns {Number} 能参与计算门槛的金额
   */
  calItemVoucherAmount(element, IS_VIP_CUSTOMER) {
    let totalAmount = 0
    // 检查商品是否有特价
    const hasActivityLabel = this.checkActivityPriceStatus(element)
    const memberPrice = getMemberPrice(element)
    // 如果是特价商品
    if (hasActivityLabel) {
      // 部分商品特价超出count也是需要调整的
      const effectGoodsNumber = Math.min(element.effectGoodsNumber || 0, element.count)
      // 超出部分金额
      const overCountAmount = Math.max((element.count - effectGoodsNumber) * (IS_VIP_CUSTOMER ? element.heartPrice2 : memberPrice) || 0, 0)
      // 特价+超出部分
      totalAmount += overCountAmount + effectGoodsNumber * element.activityPrice
    } else {
      totalAmount += element.count * (IS_VIP_CUSTOMER ? element.heartPrice2 : memberPrice)
    }
    /** 服务费用 */
    const serviceAmount = (element.selectSpecsServiceList || []).reduce((prev, item) => {
      return prev + item.schemePrice
    }, 0) * element.count
    totalAmount += serviceAmount
    return totalAmount
  },
  /**
   * 计算购物车的该项参与计算门槛的金额
   * 规则：
   * 券：
      仅 及时达/全国送（不考虑）
      仅 部分商品可用该券
      部分商品不可用该券
      指定门店可用/不可用该券（不考虑）
     商品：
      仅 自提/配送专享
      sku不可用券
      活动特价部分是否可用 + 特价超出部分是否可用该券
      门店特价部分一定不可用券 + 特价超出部分是否可用该券

    餐盒费：
      在外层处理了优惠券全场餐盒费的情况，这里处理处理指定商品可用/不可用的餐盒费
   * @param {Object} cartItem 购物车选中的某项商品数据
   * @param {Boolean} IS_VIP_CUSTOMER 是否是心享会员
   * @returns {Number} 该项能计算门槛的金额(分)
   */
  calcJoinAmount(cartItem, IS_VIP_CUSTOMER) {
    // 初始服务费
    let serviceAmount = 0
    // 2.2 判断商品是否是特价商品
    // 检查商品是否有特价
    const hasActivityLabel = this.checkActivityPriceStatus(cartItem)
    /**当前商品为新人特价商品 */
    const hasActivityPriceNew = this.data.actPriceNewMaxDiscountUuid === cartItem.uuid
    /**有同sku不同服务的商品使用了新人特价，不允许展示特价 */
    const hasActivityPriceNewSameSku = this.data.actPriceNewMaxDiscountUuid?.includes(cartItem.goodsSn)
    /**可以使用特价（不为新人特价 且 没有同sku商品使用了新人特价 且 存在特价标签） */
    const canUseActivityPrice = !hasActivityPriceNew && !hasActivityPriceNewSameSku && hasActivityLabel

    /**新人特价商品可用券 */
    const activityNewIsSupportCoupon = String(cartItem.couponStackingNew) === '1'
    /**普通商品可用券 */
    const isSupportCoupon = cartItem.isSupportCoupon === 'Y'
    //  新人特价商品不支持用券 且 普通商品不可用券
    if (!activityNewIsSupportCoupon && !isSupportCoupon) {
      return 0
    }

    const memberPrice = getMemberPrice(cartItem)
    const { objActivityPrice } = this._data

    //  特价可使用
    if (canUseActivityPrice) {
      // 如果存在特价被享受过，按照剩余特价数量来算特价数量
      const usedActivityPriceCount = Math.min(cartItem.effectGoodsNumber, objActivityPrice[cartItem.goodsSn] || 0)
      /** 当前行商品实际还能参与计算特价数量 */
      const actualJoinActiveNum = Math.min(cartItem.effectGoodsNumber - usedActivityPriceCount || 0, cartItem.count)
      // 可用券的服务费数量
      const serviceCount = Number(cartItem.couponStacking) === 0 ?
        cartItem.count - actualJoinActiveNum :
        cartItem.count
      // 服务费价格
      serviceAmount += (cartItem.selectSpecsServiceList || []).reduce((prev, item) => {
        return prev + item.schemePrice
      }, 0) * serviceCount
      // 超出部分金额
      const overCountAmount = Math.max((cartItem.count - actualJoinActiveNum) * (IS_VIP_CUSTOMER ? cartItem.heartPrice2 : memberPrice) || 0, 0)
      // 判断是否可以叠加优惠券适用 0：不能 1：能
      // 不能叠加 意味着只有超出部分算原价
      if (Number(cartItem.couponStacking) === 0) {
        // 不能叠加的场景，如果享受了特价需要记录；且下次计算特价时，会扣除当前的已享次数actualJoinActiveNum
        if (actualJoinActiveNum > 0) {
          if (!objActivityPrice[cartItem.goodsSn]) {
            objActivityPrice[cartItem.goodsSn] = actualJoinActiveNum
          } else {
            objActivityPrice[cartItem.goodsSn] += actualJoinActiveNum
          }
        }
        // 超出部分按照原价计算金额
        return overCountAmount + serviceAmount
      } else {
        // 特价+超出部分
        return overCountAmount + actualJoinActiveNum * cartItem.activityPrice + serviceAmount
      }
    }

    /**商品金额 */
    let amout = 0
    /**商品加购数 */
    let count = cartItem.count
    //  该商品存在新人特价购买件数，计算新人特价商品金额，并使数量减去1
    if (hasActivityPriceNew) {
      if (activityNewIsSupportCoupon) {
        amout += cartItem.activityPriceNew
        serviceAmount += this.calcServiceAmount(cartItem, 1)
      }
      count--
    }

    //  普通商品支持用券
    if (isSupportCoupon && count) {
      amout += (count * (IS_VIP_CUSTOMER ? cartItem.heartPrice2 : memberPrice))
      serviceAmount += this.calcServiceAmount(cartItem, count)
    }
    return amout + serviceAmount
  },
  /**
   * 计算某项商品产生的服务费
   * @param {Object} cartItem 购物车的某项元素
   * @param {Number} num 需要计入服务费的数量，由于特价可能不支持叠加使用优惠券，产生的服务费为sku可用券的超出部分
   */
  calcServiceAmount(cartItem, num) {
    if (num === 0) {
      return 0
    }
    return (cartItem.selectSpecsServiceList || []).reduce((prev, item) => {
      return prev + item.schemePrice
    }, 0) * num
  },
  /**
   * 当前购物车商品行是否是不可用券(是否是0元凑单门槛)
   * @param {Object} cartItem 购物车选中的某项商品数据
   * @param {'Y'|'N'} isSupportDelivery 是否支持配送
   * @param {'Y'|'N'} isSupportTake 是否支持自提
   * @param {Array<string>} fruitGradeSet 果品等级券限制范围
   * @returns 是否是0元凑单门槛
   */
  getIsNotSupportCoupon(cartItem, isSupportDelivery, isSupportTake, fruitGradeSet) {
    // 如果是全国送的商品 不支持该券
    if (cartItem.takeawayAttr === 'B2C' || cartItem.isSupportCoupon !== 'Y') {
      return true
    }
    // 如果是单品券 并且列表里包含/不包含指定商品 则也不能用券
    if (this.getIsTypeNotSupportUse(cartItem)) {
      return true
    }
    // 券限制了果品等级 且 该果品不在果品范围内
    if (fruitGradeSet && !fruitGradeSet?.includes(cartItem.erpGoodsLevel)) {
      return true
    }
    // 2. 再看商品的属性
    // 2.1 判断是否是配送专享
    if (isSupportDelivery === 'Y' && isSupportTake !== 'Y') {
      // 如果是配送专享的券 但是商品属性是自提专享的商品 则不计算
      if (cartItem.goodsLabelList && cartItem.goodsLabelList.some(item => item.key === 'onlySelfTake')) {
        return true
      }
    }
    // 自提
    if (isSupportTake === 'Y' && isSupportDelivery !== 'Y') {
      // 如果是自提专享的券 但是商品属性是配送专享的商品 则不计算
      if (cartItem.goodsLabelList && cartItem.goodsLabelList.some(item => item.key === 'onlyDelivery')) {
        return true
      }
    }
    return false
  },
  /**
   * 判断单品券是否不可用券
   * @returns {Boolean} 是否不可用券
   */
  getIsTypeNotSupportUse(cartItem) {
    // 1. 首先看券的属性
    const { type, specifyGoodsList } = this._data
    // 类型：0全部 1指定商品可用 2指定商品不可用
    if (Number(type) === 1) {
      if (!specifyGoodsList.includes(cartItem.goodsSn)) {
        return true
      }
    }
    if (Number(type) === 2) {
      if (specifyGoodsList.includes(cartItem.goodsSn)) {
        return true
      }
    }
    return false
  },
  /**
   * 判断该购物车商品是否有特价
   * @param {object} cartItem 购物车某一项的商品
   * @returns {Boolean} 是否是特价商品
   */
  checkActivityPriceStatus(cartItem) {
    const { IS_VIP_CUSTOMER = false } = this._data
    return checkIsShowActivityPrice({
      goodsObj: cartItem,
      // 不是购物车页面的判断
      goodsInCart: false,
      goodsCount: cartItem.count,
      // 能进来当前页面都是登录了的
      IS_LOGIN: true,
      IS_VIP_CUSTOMER
    }) || false
  },
  /**
   * 由于数据setData是异步的，会导致缺省图先展示 这里延迟展示
   * 300ms后更新缺省图状态
   */
  closeLoading() {
    if (this._data.timerCloseLoading) {
      clearTimeout(this._data.timerCloseLoading)
    }
    this._data.timerCloseLoading = setTimeout(() => {
      this.setData({
        isLoading: false
      })
    }, 300)
  },
  /**
   * 售罄商品展开/收起
   * @param {*} isShow
   */
  selloutIsShow(event) {
    const isShow = event.detail

    this.setData({
      isOpenGoodsFold: isShow,
    })
  }
})
