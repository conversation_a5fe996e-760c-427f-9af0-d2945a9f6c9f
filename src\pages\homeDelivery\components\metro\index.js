// pages/homeDelivery/components/metro/index.js
const app = getApp()
const act = require('../../../../utils/activity')
const { baseUrl } = require('../../../../utils/config')
const { wrapSensorReportFn, genClickSensor } = require('../../../../utils/report/index')
const {
	sensorReportMap,
	CATECORY_METRO,
	FUNCTION_METRO
} = require('../../sensorReportData')
import { __isStorageCache, homeDeliveryStorageCache } from '~/utils/storageCache'
import sensors from '../../../../utils/report/sensors'


const metroReportData = (() => {
	const obj = {}
	Array.from({length: 10}).forEach((el, index) => {
		obj[`${CATECORY_METRO}${index+1}`] = sensorReportMap[CATECORY_METRO](index+1)
		obj[`${FUNCTION_METRO}${index+1}`] = sensorReportMap[FUNCTION_METRO](index+1)
	})
	return obj
})();

const returnClickReportData = genClickSensor(metroReportData)
const picDomain = `${baseUrl.PAGODA_PIC_DOMAIN}/` // 线上图片地址
Component({
	/**
	 * 组件的属性列表
	 */
	properties: {
		customerID: {
			type: Number,
			value: -1
		},
		locateInfo: {
			type: Object,
			value: {
				cityID: -1,
				storeID: -1,
				deliveryCenterCode: '',
				deliveryCenterId: -1
			}
		}
	},
	observers: {
		'customerID, locateInfo': function (customerID, locateInfo) {
      if (locateInfo[__isStorageCache]) {
        return
      }

			if (!locateInfo && !this._isAttached) return
			const hasChange = this._propertyIsChange({ locateInfo, customerID })
			if (hasChange) {
        this.refresh()
      }
		}
	},
	_isAttached: false,
	_locateInfo: null,
	_customerID: null,
	/**
	 * 组件的初始数据
	 */
	data: {
		/**品类metro */
		categoryMetro: homeDeliveryStorageCache.getCache('categoryMetro', []),
		/**功能metro */
		functionMetro: homeDeliveryStorageCache.getCache('functionMetro', []),
		picDomain,
	},

	/**
	 * 组件的方法列表
	 */
	methods: {
		/**
     * @desc 检查属性值是否发生变化: _customerID, _locateInfo，只要有一个变化就重新赋值并请求接口
     */
    _propertyIsChange ({ locateInfo, customerID }) {
      if (!this._locateInfo) {
        this._setData({ locateInfo, customerID })
        return true
      }
      // customerID发生变化，重新请求
      if (this._customerID !== customerID) {
        this._setData({ locateInfo, customerID })
        return true
      }
      // locateInfo 任意一个属性值发生变化，重新请求
      const hasChange = Object.keys(this._locateInfo).every(key => locateInfo[key] === this._locateInfo[key])
      if (!hasChange) {
        this._setData({ locateInfo, customerID })
        return true
      }
      return false
    },
		_setData ({ locateInfo, customerID }) {
      this._customerID = customerID
      this._locateInfo = locateInfo
    },
		async getMetroList (postData) {
      const setDefault = () => {
        this.setMetroList({
					categoryMetro: [],
					functionMetro: []
				})
      }
			try {
				let { cityID = -1, customerID, storeID = -1, deliveryCenterCode = '', deliveryCenterId = -1 } = postData ? postData : this.data
				if (cityID === -1) {
          setDefault()
          return
        }
				const { data } = await app.api.getHomePageMetroList({
					cityID,
					customerID,
					storeID,
					deliveryCenterCode,
					deliveryCenterId: deliveryCenterId || -1
				})
				const { categoryMetro, functionMetro } = data
				this.setMetroList({
					categoryMetro,
					functionMetro
				})
			} catch (error) {
				setDefault()
			}
		},
		/**
		 * @desc 3.5.0新增需求：当前及时达首页城市对应的次日达城市为开通状态时，占用第一个功能metro的位置，展示次日达icon，点击进入次日达首页
		 * @desc 3.8.0需求：移除metro次日达入口
		 */
		setMetroList ({
			categoryMetro,
			functionMetro
		}) {
			const regex = /width=(\d+)/;

            const _functionMetro = functionMetro.length < 5 ? [] : functionMetro.map((metro) => {
                const picUrl = metro.picUrl
                const match = picUrl.match && picUrl.match(regex)

                if (match && match.length > 1) {
                    const width = match[1]
                    metro.size = `width: ${width}rpx; height: ${width}rpx;`
                }
                return metro
            })

            if (_functionMetro.length === 10) {
                for (let i = 0; i < 5; i++) {
                    _functionMetro[i].margin = 'margin-bottom: 20rpx; '
                }
            }

			homeDeliveryStorageCache.setCache('categoryMetro', categoryMetro.map(({
				labelCopy,
				picUrl,
				name,
			}) => {
				return {
					labelCopy,
					picUrl,
					name,
				}
			}))
			homeDeliveryStorageCache.setCache('functionMetro', _functionMetro.map(({
				margin,
				size,
				picUrl,
				name
			}) => {
				return {
					margin,
					size,
					picUrl,
					name,
				}
			}))

			this.setData({
				categoryMetro,
				functionMetro: _functionMetro,
			})
		},
		navigateToCate: wrapSensorReportFn(function (event) {
			const { value, index, id, name } = event.currentTarget.dataset
			const isAllCate = value === 'allCate'
			try {
				if (isAllCate) {
					sensors.track(sensors.MPClick, 'fruitMetroAllCate')
				} else {
					app.globalData.navigateToCategoryPageId = value
				}
				// switchTab不能传参
				wx.switchTab({
					url: '/pages/category/index'
				})
				return !isAllCate ? returnClickReportData(`${CATECORY_METRO}${index + 1}`, { banner_id: String(id), banner_name: name }) : void 0
			} catch (error) {
				console.log('navigateToCate', error);
				wx.showToast({
					title: '获取不到品类页Id',
					icon: 'none'
				})
			}
		}),
		navigateToPage: wrapSensorReportFn(function (event) {
			const { type, value, index, mini, id, name } = event.currentTarget.dataset
			act.toActivityPage({
        openType: type,
        openValue: value,
				miniProgram: mini
      })
			return returnClickReportData(`${FUNCTION_METRO}${index + 1}`, {
				banner_id: String(id),
				banner_name: name,
				activity_ID: value,
				element_name: `功能metro${index + 1}`,
			})
		}),

    /**
     * 组件业务逻辑更新
     */
    refresh() {
      const { locateInfo, customerID } = this.data
      return this.getMetroList({
				...locateInfo,
				customerID
			})
    }
	},
	attached () {
		this._isAttached = true
		// 注册当前组件下拉刷新时需要刷新
    this.triggerEvent('registerRefreshComp', {
      name: 'metro-list'
    })
	},
  pageLifetimes: {
    hide () {
    }
  }
})
