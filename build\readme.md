## 环境变量的问题
纯原生小程序没有提供注入环境变量的方法，在没有借助构建工具的情况下，目前想到的方法是在打包、预览前新建一个变量文件
```js
// src/env.js
module.exports = { env: 'test' }
```
然后改造一下`config.js`:
```js
// 读取env.js，有就取env.js的环境变量，否则取另外设置的
const ENV = (() => {
  try {
    const envJson = require('../env.js')
    const { env } = envJson
    console.log('there is a env json')
    return env
  } catch (error) {
    console.log('no such file')
  }
  // 切环境改 ENV test staging prod
  return 'test'
})()
```
在构建、预览完成之后，再删除`env.js`

### 缺点
如果同时执行多个构建命令，可能会出现：下一个的构建程序刚新建`env.js`然后上一个构建程序就执行删除`env.js`命令

## 小程序构建
```json
"build-mp:test": "cross-env NODE_ENV=test node build/cd/mp.js",
"build-mp:uat": "cross-env NODE_ENV=staging node build/cd/mp.js",
```
非生产环境只需要预览，生产环境还需要上传
