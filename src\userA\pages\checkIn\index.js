
var commonObj = require('../../../source/js/common').commonObj;
var checkInHeader = require('../../source/image-base64/checkIn-header-bg').bg;
let coupon_bg = require('../../source/image-base64/bg_coupon').bg
var coordtransform = require('../../../utils/coordUtil.js');  //定位引入
import wxappMap from '../../../service/wxappMap'
const sensors = require('../../../utils/report/sensors')
var app = getApp();
const locateService = require('../../../utils/services/locate')

Page({

  // 页面初始数据
  data: {
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    couponBg: coupon_bg, //优惠券背景图shareshare
    checkInHeader, // 签到头部背景图
    showRule: false, // 弹窗显示与否  活动规则
    showCheckIn: false,  // 签到成功
    showStorePosition: false,  // 门店签到  定位
    showStoreAward: false, //门店签到  奖励
    showGiftBox: false, //宝箱弹窗
    showGiftBoxAward: false, //宝箱奖励弹窗
    showContinueAward: false, //七天连续签到
    curImgIndex: 0, // 放大图片当前index值
    shareType: '', // 分享类型
    showLayer:false,  // 是否显示引导页
    switchStatus:'N',   // 签到提醒显示控制   Y 打开
    storeName: '定位中……',
    isLogin: false,
    isBack: false,
    loginTitle: '签到赢积分奖励'
  },
  _data: {
    // 判断是否来自登录页 因为来自登录页需要重新检测是否登录
    isFromLogin: false
  },
   /**
   * 生命周期函数--监听页面加载 页面渲染的后执行
   * options.shareCardInfo  分享卡【送卡与赠卡】的信息
   */
  onLoad(options) {
    if(options && options.shareCardInfo){
      let opts = JSON.parse(options.shareCardInfo);
      this.setData({opts: opts})
    }
    const viewedGuidePage = wx.getStorageSync('viewedGuidePage') || {}
    if (!viewedGuidePage.checkInPageShow) {
      this.setData({ showLayer: true })
      wx.setStorage({
        key: 'viewedGuidePage',
        data: Object.assign(viewedGuidePage, { checkInPageShow: true })
      })
    }
    let that = this;
    this.setData({
      isLogin: app.checkSignInsStatus()
    })
    if (!this.data.isLogin) {
      return
    }
    this.initPage()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (this._data.isFromLogin) {
      const { isLogin } = this.data
      if (isLogin) {
        return
      }
      this.setData({
        isLogin: app.checkSignInsStatus()
      })
      this.initPage()
    }
    // 浏览页面上报神策
    sensors.pageScreenView()
  },
  onHide() {
    this.data.isBack = true
  },
   /**********************************功能函数*******************************/
  // 空白函数 禁止滚动
  preventTouchMove(e) {
  },
  // 活动规则弹窗
  modelPop(e){
    let that = this;
    let type = e.currentTarget.dataset.type;
    if(type === 'rule'){
      that.setData({ showRule: true })
    }
    app.sensors.track("MPClick", {
      element_code: 121700004,
      element_name: '规则',
      element_content: '规则',
      screen_code: '1217',
    })
  },
  // 关闭弹窗
  closeModelPop(e){
    let that = this;
    let type = e.currentTarget.dataset.type;
    switch (type) {
      case 'rule':  // 活动规则
        that.setData({ showRule: false })
        break;
      case 'accept':  // 首页签到  水果卡+积分
        that.setData({ showCheckIn: false })
        that.getCheckInDetail().then(function(){
          if (that.data.continueDays === 7){
            that.acceptAward()
            return;
          }else{
            that.data.prizeBox && that.popGiftBox()
          }
        })
        if (app.globalData.reportSensors) {
          app.sensors.track("MPClick", {
            element_code: 121701001,
            element_name: '收下奖励',
            element_content: '收下奖励',
            screen_code: '1217',
          })
        }
        break;
      case 'dailyAccept': // 首页签到  仅积分
        that.setData({ showDailyScore: false })
        that.getCheckInDetail().then(function(){
          if (that.data.continueDays === 7) {
            that.acceptAward()
            return;
          } else {
            that.data.prizeBox && that.popGiftBox()
          }
          if (app.globalData.reportSensors) {
            app.sensors.track("MPClick", {
              element_code: 121701001,
              element_name: '收下奖励',
              element_content: '收下奖励',
              screen_code: '1217'
            })
          }
        })
        break;
      case 'continueAward':  // 七天连续签到
        that.setData({ showContinueAward: false})
        that.popGiftBox()
        break;
      case 'giftBoxAward': //宝箱奖励  关闭
        that.setData({ showGiftBoxAward: false})
        that.getCheckInDetail();
        // that.popGiftBox()
        break;
      case 'closeCard': //领取一张水果卡
        // that.setData({ popTips: false})
        that.popGiftBox()
        break;
      case 'activityEnd':  // 活动结束
        that.setData({ activityEnd: false})
        break;
      case 'laterCard':   // 来晚了提示
        that.setData({
          laterCard: false,
          opts:'',
        })
        break;
      case 'sentCard':   // 送朋友一张卡  赠卡结果
        that.setData({ popTips: false})
        that.getCheckInDetail()
        break;
      case 'closeOneCard':  // 通过赠卡链接 收到一张水果卡
        that.setData({
           oneCard: false,
           opts:'',
        })
        that.getCheckInDetail()
        that.popGiftBox()
        break;
      case 'faildSendCard': // 未获得卡  赠卡失败
        that.setData({
          requestNoCard: false,
          opts:'',
        })
        break;
      case 'openLocate':  // 未开户门店定位
        that.setData({
          userLocateClose: false,
        })
        break;
      case 'closeGuide':  // 未开户门店定位
        that.setData({
          showLayer: false,
        })
        break;
    }
  },
  // 获取签到详情
  async getCheckInDetail() {
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    let that = this;
    const { customerID } = that.data
    try {
      const res = await app.api.getCheckInDetail({customerID})
      wx.hideLoading()
      if (res.errorCode === 0) {
        let data = res.data || {}
        let {
          amountIntegral, // 总积分
          continueAmountIntegral, // 本轮连续签到获得总积分
          continueDays,    // 本轮连续签到天数
          signInStatus,    // 用户签到状态
          storeSignInStatus,  //门店签到状态
          ownTypeCount, // 拥有水果卡类型数量
          allTypeCount, // 所有水果卡类型数量
          receiveFruitCards,  // 用户向其他用户要到的水果卡名称
          prizeIntegralDetails, // 七天连续签到奖励积分
          fruitCardActivityInfo, // 水果卡活动信息
          ownFruitCards,  // 当所有水果卡活动结束
          todaySignInStore,  //今日签到门店信息
        } = data
        prizeIntegralDetails.forEach(item => {
          item.signInTimesDeal = item.signInTimes.split(' ')[0].split('-').slice(1).join('.')
        });

        if(fruitCardActivityInfo){
          fruitCardActivityInfo.startTime = fruitCardActivityInfo.startTime.split(' ')[0].replace(/\-/g, '.')
          fruitCardActivityInfo.endTime = fruitCardActivityInfo.endTime.split(' ')[0].replace(/\-/g, '.')
        }
        // 判断是否收到卡  卡多少张
        if(receiveFruitCards){
          let cardCount = receiveFruitCards.length;
          if(cardCount > 1){
            that.setData({requestMoreCard: true})
          }else if(cardCount === 1){
            that.setData({requestOneCard: true})
          }
        }
        that.setData({
          amountScore:amountIntegral, // 总积分
          continueAmountScore:continueAmountIntegral, // 连续签到获得积分
          continueDays:continueDays,  // 连续签到天数
          fruitCardInfo:fruitCardActivityInfo, // 水果卡
          checkDate:prizeIntegralDetails, // 签到日历
          FruitCardList:ownFruitCards, // 水果图鉴
          allTypeCount:allTypeCount,  // 所有活动的水果卡总计
          ownTypeCount:ownTypeCount,  // 本人获得水果卡合计
          signInStatus:signInStatus,  // 首页是否签到  0-未签  1-已签
          storeSignInStatus:storeSignInStatus,  // 门店是否签到  0-未签  1-已签
          receiveFruitCards:receiveFruitCards,  // 获得的水果卡信息
          todaySignInStore    //今日打卡门店
        })
      } else{
        commonObj.showModal('提示', '系统繁忙，请求超时', false, '我知道了','',function(){
          wx.navigateBack({
            delta:1
          })
        })
      }
    } catch(fail) {
      wx.hideLoading()
      commonObj.showModal('提示', '系统繁忙，请求超时', false, '我知道了')
    }
  },
  // 签到
  checkIn(){
    let that = this;
    if (that.data.isPunchLoading) return;
    app.requestSubscribeMessage({
      // 签到提醒模板id
      tmplIds: ['nmKkdfOiXLdpFnKcHNlJRRnEfSDM76si9D9ROKyO1Mc']
    }, () => {
      that.toCheckIn()
    })
  },
  async toCheckIn () {
    let that = this;
    that.setData({ isPunchLoading: true })
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    const { customerID } = that.data
    try {
        const res = await app.api.toCheckIn({customerID})
        wx.hideLoading()
        that.setData({isPunchLoading: false})
        if (res.errorCode === 0) {
          let data = res.data || {}
          let {
            prizeIntegral, // 奖励积分
            memberCoupon, // 优惠券奖励  v2.1分成二种  一种为满减 立减；别一种为满折  立折
            prizeFruitCard,  // 水果卡奖励
            prizeBox,    // 宝箱奖励
          } = data
          if(prizeIntegral && prizeFruitCard === null){
            this.setData({
              showDailyScore: true,
            })
          }else if(prizeFruitCard !== null){
            that.setData({
              showCheckIn: true,
              prizeFruitCard:prizeFruitCard,
            })
          }
          that.setData({
            prizeIntegral:prizeIntegral,
            memberCoupon:memberCoupon,
            prizeBox:prizeBox,
          })
          app.sensors.track("MPClick", {
            element_code: 121700001,
            element_name: '签到',
            element_content: '签到',
            screen_code: '1217',
          })
        }
    } catch {
      wx.hideLoading()
    }
  },
  // 获取当前位置
  async nearby(){
    try {
      const city = wx.getStorageSync('timelyCity') || {}
      this.setData({
        storeName: city.storeName,
        storeID: city.storeID,
      })

    } catch(err) {
      locateService.handleWXLocationErr(err.errMsg)
      this.setData({userLocateClose:true})
    }
  },
  // 去任务中心页
  goTaskCenter() {
    app.sensors.track('MPClick', {
      element_code: '121707001',
      element_name: '赚积分',
      element_content: '赚积分',
      screen_code: '1217'
    })
    wx.redirectTo({
      url: '/userA/pages/taskCenter/index',
    })
  },
  // 去门店打卡页
  goStorePunch(e){
    if (app.globalData.reportSensors) {
      app.sensors.track("MPClick", {
        element_code: 121700002,
        element_name: '门店打卡',
        element_content: '门店打卡',
        screen_code: '1217',
      })
    }
    wx.navigateTo({
      url: '../storePunch/index?isPunch=no',
    })
  },

  // 收下首页签到 判断是不是连续七天签到 弹出优惠券
  acceptAward(){
    let that = this;
    if(that.data.memberCoupon){
      that.setData({
        showContinueAward:true
      })
    }
  },
  // 弹出宝箱
  popGiftBox(){
    let that = this;
    if(that.data.prizeBox){
      that.setData({
        showGiftBox:true
      })
    }
  },
  // 打开宝箱
  openGiftBox(){
    let that = this;
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    if (app.globalData.reportSensors) {
      app.sensors.track("MPClick", {
        element_code: 121705001,
        element_name: '打开宝箱',
        element_content: '打开宝箱',
        screen_code: '1217',
      })
    }
    that.setData({
      showGiftBox: false,
      showGiftBoxAward: true
    })
    let requestData ={
      url: `/api/v1/wxmini/signIn/openBox/${that.data.customerID}/${that.data.fruitCardInfo.fruitCardActivityID}/${that.data.prizeBox.boxID}`,
      method: "POST"
    };
    commonObj.requestData(
      requestData,
      res => {
        if (res.data.errorCode === 0) {
          wx.hideLoading()
          let data = res.data.data
          that.setData({
            giftBoxAward:data
          })
        } else {
          wx.hideLoading()
          wx.showToast({
            title: res.data.errorMsg,
            icon: 'none',
            duration: 2000
          })
        }
      }
    )
  },
  // 水果图鉴 放大图片
  enlargeImage (e) {
    let index = e.currentTarget.dataset.index;
    let type = e.currentTarget.dataset.type;
    let animation = wx.createAnimation({
      duration: 1000,
      timingFunction: 'ease',
      delay: 0
    })
    this.animation = animation
    setTimeout( () => {
      animation.translateY(0).step()
      this.setData({
        animationData: animation.export()
      })
    }, 300)
    if(type === 'fruitCard'){// 我的水果卡
      this.setData({
        showFruitCardEnlarge: true,
        curImgIndex: index
      })
    }else if(type === 'fruitImage'){//水果图鉴
      if(this.data.FruitCardList.length > 1){
        if(index === 0){
          this.setData({
            showPre: false,
            showNext: true,
          })
        }else if(index === this.data.FruitCardList.length -1){
          this.setData({
            showPre: true,
            showNext: false
          })
        }else {
          this.setData({
            showPre: true,//水果图鉴前指向
            showNext: true, //水果图鉴后指向
          })
        }
      }else{
        this.setData({
          showPre: false,
          showNext: false,
        })
      }
      this.setData({
        showEnlargeLayer: true,
        curImgIndex: index
      })
    }
  },
  // 关闭放大弹窗
  closeEnlargeLayer (e) {
    let type = e.currentTarget.dataset.type;
    let animation = wx.createAnimation({
      duration: 1000,
      timingFunction: 'ease',
      delay: 0
    })
    this.animation = animation
    setTimeout( () => {
      animation.translateY(1200).step()
      this.setData({
        animationData: animation.export()
      })
    }, 300)
    switch (type){
      case 'fruitCard':
        setTimeout(() => {
          this.setData({
            showFruitCardEnlarge: false
          })
        }, 900)
        break;
      case 'fruitImage':
        setTimeout(() => {
          this.setData({
            showEnlargeLayer: false
          })
        }, 900)
        break;
    }
  },
  // 上一张图片
  preImage () {
    let index = this.data.curImgIndex
    if(index <= 1){
      this.setData({showPre: false})
    }else{
      this.setData({
        showPre: true,
        showNext: true
      })
    }
    if( index === 0) {
      return
    }
    index--
    this.setData({
      curImgIndex: index
    })
  },
  // 下一张图片
  nextImage () {
    let index = this.data.curImgIndex
    if(index >= this.data.FruitCardList.length - 2){
      this.setData({
        showNext: false
      })
    }else{
      this.setData({
        showPre: true,
        showNext: true
      })
    }
    if( index === this.data.FruitCardList.length -1) {
      return
    }
    index++
    this.setData({
      curImgIndex: index
    })
  },
  // 懒加载  方法
  onLazyLoad(info) {
    // console.log(info)
  },
  // 分享 求卡  赠卡
  onShareAppMessage(obj) {
    let that = this;
    let { storeID = 0 , storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
    let share_title, share_path, share_image, shareObj;
    if(obj.from === 'button'){
      let type = obj.target.dataset.type;
      that.setData({shareType: type})
      if(type === 'share'){
        share_title = '集齐12张水果卡，就能瓜分一千万红包，快来一起参加吧';
        share_image =  '/userA/source/image/check_in_share.png';
        share_path = '/pages/homeDelivery/index?to=checkIn';

      }else if (type === 'send'){
        let cardInfo = obj.target.dataset.card;
        cardInfo.type = type;
        cardInfo.fruitCardActivityID = obj.target.dataset.activityid;
        that.setData({shareSuccessInfo:cardInfo});
        let shareTime = new Date().getTime().toString();
        let shareLinkID = that.data.customerID.toString() + cardInfo.fruitCardActivityID.toString() + shareTime;
        shareObj = {
          type: 'send',
          shareCustomerID: that.data.customerID,  // 分享用户ID
          fruitCardName: cardInfo.fruitCardName,  // 用户拥有的水果卡名称
          fruitCardActivityID: cardInfo.fruitCardActivityID,  // 水果卡活动ID
          shareLinkID: shareLinkID,
        }
        share_title = `${that.data.userName}送你1张${cardInfo.fruitCardName}卡，其他小伙伴都已经集齐水果卡啦！`;
        share_image =  '/userA/source/image/send_card.png';
        share_path = `/pages/homeDelivery/index?home2checkIn=${JSON.stringify(shareObj)}`;
      }else if (type === 'request'){
        let cardInfo = obj.target.dataset.card;
        cardInfo.type = type;
        cardInfo.fruitCardActivityID = obj.target.dataset.activityid;
        shareObj = {
          type: 'request',
          shareCustomerID: that.data.customerID,  // 分享用户ID
          fruitCardName: cardInfo.fruitCardName,
          fruitCardActivityID: cardInfo.fruitCardActivityID,  // 水果卡活动ID
        }
        share_title = `我还差1张${cardInfo.fruitCardName}卡，就能集齐水果卡开启宝箱！求送~`;
        share_image =  '/userA/source/image/request_card.png';
        share_path = `/pages/homeDelivery/index?home2checkIn=${JSON.stringify(shareObj)}`;
      }
    }else {
      share_title = '集齐12张水果卡，就能瓜分一千万红包，快来一起参加吧';
      share_image =  '/userA/source/image/check_in_share.png';
      share_path = '/pages/homeDelivery/index?to=checkIn';
    }
    if (that.data.shareType === 'send') {
      if (app.globalData.reportSensors) {
        app.sensors.track("MPClick", {
          element_code: 121704001,
          element_name: '送朋友一张',
          element_content: '送朋友一张',
          screen_code: '1217',
        })
      }
    } else if (that.data.shareType === 'request') {
      if (app.globalData.reportSensors) {
        app.sensors.track("MPClick", {
          element_code: 121703001,
          element_name: '找朋友要一张',
          element_content: '找朋友要一张',
          screen_code: '1217',
        })
      }
    }else{
      if (app.globalData.reportSensors) {
        app.sensors.track("MPClick", {
          element_code: 121700003,
          element_name: '分享好友集水果卡',
          element_content: '分享好友集水果卡',
          screen_code: '1217',
        })
      }
    }
    if (that.data.shareSuccessInfo && that.data.shareSuccessInfo.type === 'send') {
      let requestData = {
        url: '/api/v1/wxmini/signIn/increase/sendCount',
        data: {
          customerID: that.data.customerID,
          fruitCardActivityID: that.data.shareSuccessInfo.fruitCardActivityID,
          fruitCardName: that.data.shareSuccessInfo.fruitCardName,
        },
        method: "POST"
      };
      commonObj.requestData(
        requestData,
        res => {
          if (res.data.errorCode === 0) {
            console.log('送卡分享链接发送成功')
          } else {
            wx.showToast({
              title: res.data.errorMsg,
              icon: 'none',
              duration: 2000
            })
          }
          that.setData({shareSuccessInfo: null})
        }
      )
    }

    if (app.globalData.reportSensors) {
      app.sensors.track('MPShare', {
        mp_shareTitle: share_title,
        activity_Name: '',
        storeID,
        storeName,
        storeNum: storeInfo.storeCode || ''
      })
    }
    return {
      title: share_title,
      imageUrl: share_image,
      path: share_path
    }
  },

  // 通过赠卡链接 领取朋友送的一张卡
  receiveOneCard() {
    let that = this;
    let requestData = {
      url:'/api/v1/wxmini/signIn/receiveFruitCard',
      data:{
        customerID: that.data.customerID,
        shareCustomerID: that.data.inviteCardInfo.shareCustomerID,
        fruitCardActivityID: that.data.inviteCardInfo.fruitCardActivityID,
        shareLinkID: that.data.inviteCardInfo.shareLinkID,
        fruitCardName: that.data.inviteCardInfo.fruitCardName,
      },
      method: "POST"
    }
    commonObj.requestData(
      requestData,
      res => {
        if(res.data.errorCode === 0){
          that.setData({
            getOneCardInfo: res.data.data.prizeFruitCard,
            oneCard: true,
          })
          if(res.data.data.prizeBox){
            that.setData({prizeBox:res.data.data.prizeBox})
          }else{
            that.getCheckInDetail()
          }
        }else if(res.data.errorCode === 35704 || res.data.errorCode === 35705){
          that.setData({laterCard:true})
        }else{
          wx.showToast({
            title: res.data.errorMsg,
            icon: 'none',
            duration: 2000
          })
        }
      }
    )
  },
  //签到详情  领取水果卡
  requestGetCard(){
    let that = this;
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    that.setData({
      requestMoreCard: false,
      requestOneCard: false,
    })
    let requestData = {
      url:`/api/v1/wxmini/signIn/updateDisplayStatus/${that.data.customerID}/${that.data.fruitCardInfo.fruitCardActivityID}`,
      method: "POST"
    }
    console.log(requestData)
    commonObj.requestData(
      requestData,
      res => {
        wx.hideLoading()
        if(res.data.errorCode === 0){
          console.log('求卡得卡',res.data)
          if(res.data.data){
            that.setData({
              prizeBox: res.data.data,
            })
            that.popGiftBox()
          }
        }else{
          wx.showToast({
            title: res.data.errorMsg,
            icon: 'none',
            duration: 2000
          })
        }
      }
    )
  },
  // 甲方求卡  乙方赠卡弹窗  按钮事件
  sendCardConfirm(e){
    let that = this;
    let btnType = e.currentTarget.dataset.cardtype;
    that.setData({requestCard: false})
    if(btnType === 'confirm'){
      wx.showLoading({
        title: '加载中',
        mask: true
      })
      let requestData = {
        url:'/api/v1/wxmini/signIn/sendFruitCard',
        data:{
          customerID:that.data.customerID,
          shareCustomerID:that.data.requestCardInfo.shareCustomerID,
          fruitCardActivityID:that.data.requestCardInfo.fruitCardActivityID,
          fruitCardName:that.data.requestCardInfo.fruitCardName,
        },
        method: "POST"
      }
      commonObj.requestData(
        requestData,
        res => {
          wx.hideLoading()
          if(res.data.errorCode === 0){
            that.setData({
              popTips: true,
              opts:'',
            })
          }else{
            that.setData({
              opts:'',
            })
            wx.showToast({
              title: res.data.errorMsg,
              icon: 'none',
              duration: 2000
            })
          }
        }
      )
    }else{
      that.setData({
        opts:'',
        requestCard: false
      })
    }
  },
  // 立即使用
  useImmediately(){
    if (app.globalData.reportSensors) {
      app.sensors.track("MPClick", {
        element_code: 121706001,
        element_name: '立即使用',
        element_content: '立即使用',
        screen_code: '1217',
      })
    }
    wx.switchTab({
      url: '/pages/homeDelivery/index'
    })
  },

  // v2.1
  // 去我的奖励页面
  toMyAward () {
    wx.navigateTo({
      url: '/userA/pages/myAward/index'
    })
  },

  // 新用户注册领取积分提醒，跳转兑吧
  navigateDuiba: function () {
    wx.navigateTo({
      url: '/h5/pages/duiba/index',
    })
  },

  // 提醒开关
  // switchChange (e){
  //   if(e.detail.value === true){
  //     app.api.changePunchSwitch({
  //       customerID: this.data.customerID,
  //       status: 'Y',
  //     }).then((res) => {
  //       wx.showToast({
  //         title: '设置提醒成功',
  //       })
  //       this.setData({
  //         switchStatus: 'Y'
  //       })
  //     })
  //   }else{
  //     app.api.changePunchSwitch({
  //       customerID: this.data.customerID,
  //       status:'N',
  //     }).then((res) => {
  //       wx.showToast({
  //         title: '取消提醒成功',
  //       })
  //       this.setData({
  //         switchStatus: 'N'
  //       })
  //     })
  //   }
  // },

  // 获取签到状态
  // getCheckStatus (){
  //   app.api.getCheckStatus(this.data.customerID).then((res) => {
  //     this.setData({
  //       switchStatus: res.data
  //     })
  //   })
  // },

  // 跳转到及时达首页
  skipToDelivery (){
    wx.switchTab({
      url: '/pages/homeDelivery/index',
    })
  },

  // 保存 formID
  submitFormId (e){
  },
  toLogin () {
    wx.navigateTo({
      url: '/homeDelivery/pages/signIn/index/index'
    })
    this._data.isFromLogin = true
  },
  cancelLogin () {
    wx.navigateBack()
  },
  initPage() {
    if (!app.checkSignInsStatus()) {
      // 如果是来自登录页 不做任何操作
      if (this._data.isFromLogin) {
        return
      }
      app.signIn()
      return
    }
    const that = this
    const customerID = wx.getStorageSync('user').userID;
    const { nickName: userName } = wx.getStorageSync('userNameAndImg') || {};
    that.setData({
      customerID,
      userName,
    })
    that.getCheckInDetail().then(function(res){
      let shareCardInfo = that.data.opts;
      // that.getCheckStatus()
      if(Number(that.data.storeSignInStatus) === 0){//未签到
        that.nearby()
      }
      if(shareCardInfo && shareCardInfo.type === 'send'){  //赠卡链接
        if(shareCardInfo.shareCustomerID !== that.data.customerID){
          if(that.data.fruitCardInfo){
            that.setData({
              inviteCardInfo: shareCardInfo,
            })
            that.receiveOneCard()
          }else{
            that.setData({activityEnd:true})
          }
        }
      }else if(shareCardInfo && String(shareCardInfo.type) === 'request'){ //求卡链接
        if(shareCardInfo.shareCustomerID !== that.data.customerID){
          if(that.data.fruitCardInfo){
            that.data.FruitCardList.forEach((item,index) =>{
              if(item.fruitCardName === shareCardInfo.fruitCardName){
                if(item.count > 0){
                  that.setData({
                    requestCardInfo: shareCardInfo,
                    selfCardCount: item.count,
                    requestCard: true
                  })
                }else{
                  that.setData({
                    requestCardInfo: shareCardInfo,
                    requestNoCard: true
                  })
                }

              }
            })
          }else{
            that.setData({activityEnd:true})
          }
        }
      }
    })
  }
})
