
const { COSAction } = require('../cos/index.js')
const { ENV } = require('../config')
const { COS_WXAPP, COS_UPLOAD_TYPE_ENUM, COS_UPLOAD_DIRECTORY_MAP } = require('../../source/const/cosConfig')

class UploadFileClass{
  constructor() {
    this._cos = new COSAction({ cosType: COS_WXAPP, logger: ENV !== 'prod' })
  }
  async _refreshTempKey () {
    // 未到过期时间，不需要刷新凭证
    if (this._cos.cosExpiredTimeStamp && new Date().getTime() < this._cos.cosExpiredTimeStamp) return
    await this._cos.getTempAuth()
  }
  async post ({
    cosDirectory,
    filePath,
    filename
  }) {
    await this._refreshTempKey()
    const res = await this._cos.postObject({
      cosDirectory,
      filePath,
      filename
    })
    if (!res.Location) {
      wx.hideLoading()
      showErrToast()
      return
    }
    return {
      groupName: '',
      filePath: res.Location.match(/.com\/(\S*)/)[1]
    }
  }
}

const uploadFile = new UploadFileClass()

function showErrToast() {
  wx.showModal({
    title: '提示',
    content: '上传图片失败，请重试',
    showCancel: false
  })
}
/**
 * 选择图片
 * @param {object} options 参数信息
 * @returns {Promise<WechatMiniprogram.ChooseImageSuccessCallbackResult>} 选择的图片
 */
function chooseImage(options){
  const {
    count = 1,
    sizeType = ['original', 'compressed'],
    sourceType = ['album','camera']
  } = options || {}
  return new Promise((resolve,reject) => {
    wx.chooseImage({
      count,
      sizeType,
      sourceType,
      success: function (res) {
        resolve(res)
      },
      fail:function(){
        reject("选择文件失败")
      }
    })
  })
}
/**
 * 选择媒体文件
 * @param {Object} options 小程序选项
 * @returns {Promise<WechatMiniprogram.ChooseMediaSuccessCallbackResult>} 选择的媒体
 */
function chooseMedia(options) {
  const {
    count = 9,
    mediaType = ['image','video'],
    sourceType = ['album', 'camera'],
    maxDuration = 30,
    camera = 'back',
  } = options || {}
  return new Promise((resolve,reject) => {
    wx.chooseMedia({
      count,
      mediaType,
      sourceType,
      maxDuration,
      camera,
      success: function (res) {
        resolve(res)
      },
      fail:function(){
        reject("选择文件失败")
      }
    })
  })
}
async function uploadPic (params) {
  const {
    chooseImageOptions: options,
    needFilter = false,
    uploadType = COS_UPLOAD_TYPE_ENUM.REFUND
  } = params || {}
  // 选择图片
  const chooseImageResult = await chooseImage(options)
  // console.log('chooseImageResult', chooseImageResult)
  // 上传图片
  const imgArr = await chooseImageResult.tempFiles.map(async (item,index) => {
    wx.showLoading({
      title: chooseImageResult.tempFiles.length > 1 ? `正在上传第${index+1}张` : '正在上传'
    })
    const filePath = item.path
    const filename = filePath.substr(filePath.lastIndexOf('/') + 1)
    const uploadParams = {
      cosDirectory: COS_UPLOAD_DIRECTORY_MAP[uploadType],
      filePath,
      filename
    }
    // 做过滤
    const reg = new RegExp('.*?(png|jpg|jpeg)')
    if(needFilter){
      if(reg.test(filePath)){
        return uploadFile.post(uploadParams)
      }else{
        wx.showToast({
          title: '格式不正确',
          icon:'none'
        })
        return
      }
    }else{
      return uploadFile.post(uploadParams)
    }
  })
  return new Promise((resolve) => {
    Promise.all(imgArr).then((result)=>{
      // console.log(result)
      wx.hideLoading();
      // wx.showToast({
      //   title: '上传成功',
      //   icon:"none",
      //   duration: 2000
      // })
      resolve(result.filter(r => r))
    }).catch(err => {
      wx.hideLoading();
      throw new Error('uploadPic: ',err)
    })
  })
}
module.exports = {
  uploadPic,
  uploadFile,
  chooseMedia
}
