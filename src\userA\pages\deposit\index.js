import { throttle } from '../../../utils/util'
import { createStoreBindings } from 'mobx-miniprogram-bindings'
import locateStore from '../../../stores/module/locate'
import { rechargeModalObj } from './modal'
// 会员充值index.js
var commonObj = require('../../../source/js/common').commonObj;
require('../../../utils/util.js')
const { getRechargeRuleText } = require('../../../api/user.api')
const sensors = require('../../../utils/report/sensors')
const { protocolUrl } = require('../../../utils/config')
const locateMixin = require('../../../mixins/locateMixin')
var app = getApp();
Page({
  mixins: [locateMixin],
  data: {
    isReady: false,
    balance: 0,//账户余额；
    fruitCoinBalance: 0,  // 果币余额
    balanceImage: '../../../source/images/<EMAIL>',
    showBalanceMask: false,  // 是否弹窗展示果币的来源
    tick: false,      // 是否勾选章程
    currentTabIndex: 0,   // 聚焦的tab索引
    swiperHeight: 772.5,   // swiper的高度，由于swiper里面的tab是绝对定位，导致高度为0的情况，这里需要将高度动态计算后传值给swiper
    cardNo: '',   // 好吃卡充值卡号
    cardPwd: '',   // 好吃卡充值密码
    isEnableCardBtn: false,   // 好吃卡充值按钮是否启用
    couponNo: '',   // 优惠券号码
    isEnableCouponBtn: false,   // 优惠券充值按钮是否启用
    rechargeRules: '', // 充值说明
    rechargeCardRules: '', // 好吃卡充值说明
    rechargeCouponRules: '', // 优惠券充值说明
    recharge_version:'', // 已阅读协议的版本
    /* 主钱包冻结金额 */
    lockingMainBalance: 0,
    /* 果币冻结金额 */
    lockingFruitCoinBalance: 0,
    // 在线充值按钮是否可用
    canRecharge: false,
    // 是否有充值活动
    hasActivity: false,
    // 自定义金额输入框是否聚焦
    customizeFocus: false,
    // 是否展示活动充值错误弹窗
    showModal: false,
    // 充值活动弹窗内容
    modelContent: '',
    // 充值活动弹窗描述内容
    modelDescContent: ''
  },
  _data: {
    options: null,
    // 用户充值扫码提示确认框
    confirmRechargeScan: {
      // 好吃卡
      isClickedCard: false,
      // 优惠券
      isClickedCoupon: false
    },
    // 是否在活动充值中（仅在在线充值中使用）
    isPaying: false
  },
  onLoad: function (options) {
    this._data.options = options || {}
    if (options && options.from && options.status) {
      if (options.from === '充值' && options.status === '成功') {
        this.paySuccessToast()
      } else if (options.from === '充值' && (options.status === '待支付' || options.status === '失败')) {
        commonObj.showModal('充值异常', '非常抱歉，因为系统原因，未能充值成功，请联系客服处理~', true, '联系客服', '我知道了', function (res) {
          if (Boolean(res.confirm) === true) {
            wx.makePhoneCall({
              phoneNumber: '************'
            })
          }
        })
      }
    }

    // 获取确认框是否点击的缓存信息
    this.getConfirmRecharge()
    this.setProtocolVersion()
    // 获取store数据
    this.getStoreData()
    // 初始化弹窗
    rechargeModalObj.setContext(this)
  },
  onUnload() {
    // 注销store绑定
    if (this.storeBindings && this.storeBindings.destroyStoreBindings) {
      this.storeBindings.destroyStoreBindings()
    }
    rechargeModalObj.destroy()
  },
  onShow() {
    // 拉取账户余额
    this.getAccountAmount()
  },
  async onLocateReady() {
    //  上报页面浏览事件
    sensors.pageScreenView({
      $url_query: JSON.parse(JSON.stringify(this._data.options))
    })
    wx.setNavigationBarTitle({
      title: '我的账户'
    })
    if (app.checkSignInsStatus()) {
      await this.getRechargeActivityInfoFn()
      // 切换中
      this.handleSwiperChanged(true)
    } else {
      app.showSignInModal()
    }
  },
  // 点击tab 切换索引
  handleClickTab: function(e) {
    const currentTabIndex = parseInt(e.currentTarget.dataset.index);
    this.setData({
      currentTabIndex
    })
    // 切换中
    this.handleSwiperChanged(false);
  },
  // swiper滑动事件
  handleSwiperChange: function(e) {
    // 如果是用户触摸 则将index的值复制给聚焦的tab
    if (e.detail.source === 'touch') {
      this.setData({
        currentTabIndex: e.detail.current
      })
    }
  },
  // swiper滑动结束后
  handleSwiperChanged: function(e, targetIndex) {
    const query = wx.createSelectorQuery();
    query.select(`#swiper${targetIndex === void 0 ? this.data.currentTabIndex : targetIndex}`).boundingClientRect().exec((res) => {
      if (!res || !res[0]) {
        return;
      }
      // 如果是切换中 则判断要切换的高度是否低于原高度
      // 目的是为了防止两个页面切换时 高度高的页面切向高度低的页面时 高度不一致导致的高页面高度消失的情况
      if (!e && this.data.swiperHeight > res[0].height) {
        return;
      }
      // 设置swiper的高度
      this.setData({
        swiperHeight: res[0].height
      });
    })
  },
  /**
   * 充值活动是否已经选择完毕并可以充值
   */
  handleCanRecharge(e) {
    const { canRecharge } = e.detail
    this.setData({
      canRecharge
    })
  },
  /**
   * 充值活动加载完成后，需要判断是否有活动，有活动可以展示充值说明和立即充值按钮
   */
  handleActivityLoaded(e) {
    const { activityList = [] } = e.detail
    this.setData({
      hasActivity: activityList.length > 0
    })
  },
  /**
   * 充值自定义输入框被聚焦或者失焦事件
   */
  handleCustomizeFocus(e) {
    const { focus } = e.detail
    this.setData({
      customizeFocus: focus
    })
  },
  // 切换中事件 节流处理
  handleTransition: throttle(function (e){
    // 如果是第一个tab往左滑动 或者 最后一个tab往右滑动 则不处理
    if ((this.data.currentTabIndex === 0 && e.detail.dx < 0) ||
      (this.data.currentTabIndex === 2 && e.detail.dx > 0)) {
      return;
    }
    // 目标索引
    const move = this.data.currentTabIndex + ((e.detail.dx > 0) ? 1 : -1);
    // 切换中
    this.handleSwiperChanged(false, move);
  }, 500, true),

  // 开具电子发票
  openInvoice () {
    sensors.track('Click', {
      element_code: 120800004,
      element_name: '开具电子发票',
      element_content: '开具电子发票',
      screen_code: 1208,
      screen_name: '我的账户页'
    })
    wx.navigateTo({
      url: '/userB/pages/invoice/invoiceService/index?type=recharge'
    })
  },
  navigateToDownLoadH5(){
    const pageUrl = 'http://mp.weixin.qq.com/s?__biz=MjM5ODAwMTYwMA==&mid=521139153&idx=1&sn=375d762d3186d7596f297023a08d813b&chksm=3c2a3d7e0b5db468c113e25d4421397dd4f5f5bc810e024e8bc5dcc478ace022c9df3aafbb35#rd'
    wx.navigateTo({
      url: '/h5/pages/commonLink/index?pageUrl=' + encodeURIComponent(pageUrl),
    })
  },
  checkDetailInfo: function () {
    sensors.track('Click', {
      element_code: 120800005,
      element_name: '查看交易明细',
      element_content: '查看交易明细',
      screen_code: 1208,
      screen_name: '我的账户页'
    })
    wx.reportAnalytics('balance_detail_click')    // 点击查看余额明细-v2.0
    this.preventEvent();
  },

  alertBalanceInfo: function () {   // 弹窗展示果币的来源、消费和限制内容
    this.setData({showBalanceMask: true})
    sensors.track('Click', {
      element_code: 120800003,
      element_name: '果币说明',
      element_content: '果币说明',
      screen_code: 1208,
      screen_name: '我的账户页'
    })
  },

  colseMask: function () {
    this.setData({showBalanceMask: false})
  },

  onShareAppMessage: function () {
    wx.reportAnalytics('share_success')
    if (app.globalData.reportSensors) {
      const { storeID = 0 , storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
      app.sensors.track('MPShare', {
        mp_shareTitle: '充值劲享优惠，买水果更划算',
        activity_ID: 0,
        activity_Name: '',
        groupID: 0,
        groupSize: '',
        openerID: '',
        currentCount: '',
        storeID,
        storeName,
        storeNum: storeInfo.storeCode || ''
      })
    }
    return {
      title: '充值劲享优惠，买水果更划算',
      imageUrl: 'https://resource.pagoda.com.cn/dsxcx/images/97633469425fa533ba67818cbbd2c0c8.jpg',
      path: '/userA/pages/deposit/index'
    }
  },

  paySuccessToast () {
    wx.showLoading()
    wx.hideLoading()
    const eventChannel = this.getOpenerEventChannel()
    setTimeout(() => {
      wx.showToast({
        title: '充值成功',
        icon: 'none',
      })
      setTimeout(() =>{
        wx.hideToast();
      }, 5000)

      if (eventChannel) {
        //  通过emit的方式进行触发 将子页面/目标页面中的数据传递给当前页面
        eventChannel.emit('rechargeSuccess')
      }
    }, 1000)
  },

  // 防止点击过快，导致页面重复跳转蒙层
  preventEvent: function () {
    this.setData({ prevent: true });
    setTimeout(() => {
      this.setData({ prevent: false });
    }, 400)
  },

  // 点击勾选
  handleTick(){
    const tick = !this.data.tick
    this.setData({
      tick
    })
  },

  /**
   * 去充值
   * 1为常规活动充值，2为自定义金额充值，3为营销活动充值
   */
  goRecharge(){
    const rechargeActivityComp = this.selectComponent('#rechargeActivity')
    if (rechargeActivityComp && rechargeActivityComp.createRechargeOrder) {
      rechargeActivityComp.createRechargeOrder()
    }
  },

  /**
   * 好吃卡充值
   */
  async goRechargeCard() {
    // 判断输入是否合法
    if (this.data.tick && this.data.isEnableCardBtn) {
      const { recharge_version } = this.data
      wx.setStorageSync('recharge_version', recharge_version) // 缓存协议版本
      wx.showLoading({
        title: '正在充值中',
        mask: true
      })
      const user = wx.getStorageSync('user');
      // 请求好吃卡充值接口
      const res = await app.api.depositByCard({
        // 卡号
        cardNumber: this.data.cardNo.replace(/\s/g, ''),
        // 卡密码
        cardPassWord: this.data.cardPwd.replace(/\s/g, ''),
        // 手机号
        // （1. 卡若已激活则必须为激活手机号。2. 卡若未激活，则直接冲入现对应的会员账户。）
        phoneNumber: user.phoneNumber,
        customerID: user.userID
        // 交易序号 serverless端处理
        // dealNum: 32,
        // 充值类型（1好吃卡 2. 充值码）
        // type: 1
      }).catch(async e => {
        wx.hideLoading();
        console.log(e);

        const diaContent = e.pushInfo && e.pushInfo.dialog && e.pushInfo.dialog.content ? e.pushInfo.dialog.content : ''
        const content = e.description || diaContent || '非常抱歉，因为系统原因，未能充值成功，请联系客服处理~'

        // 如果后端提供了错误码 这显示以后端为准 则提示如下
        if ([3017, 10001, 10002, 10003].includes(e.errorCode)) {
          await app.showModalPromise({
            title: '充值失败',
            content,
            confirmText: '我知道啦',
          })
          return
        }
        // 如果后端提供了toast错误码 这显示以后端为准 则提示如下
        const title = e.description || e.messageInfo
        if ([-9999].includes(e.errorCode)) {
          setTimeout(() => {
            wx.showToast({
              title,
              duration: 2000,
              icon: 'none'
            })
          }, 200)
          return
        }
        // 其他错误的提示
        commonObj.showModal('充值异常', '非常抱歉，因为系统原因，未能充值成功，请联系客服处理~', true, '联系客服', '我知道了', function (res) {
          if (res.confirm) {
            wx.makePhoneCall({
              phoneNumber: '************'
            })
          }
        })
      })
      wx.hideLoading();

      // 充值成功
      if (res && res.errorCode === 0) {
        // 清空数据
        this.setData({
          cardNo: '',
          cardPwd: '',
          isEnableCardBtn: false
        })
        // 刷新余额
        this.getRechargeActivityInfoFn()

        setTimeout(() => {
          wx.showToast({
            title: '充值成功',
            duration: 2000,
            icon: 'none'
          })
        }, 200)
      }
    }
  },

  /**
   * 优惠券充值
   */
  async goRechargeCoupon() {
    // 判断输入是否合法
    if (this.data.tick && this.data.isEnableCouponBtn && this.data.couponNo) {
			const { recharge_version } = this.data
			wx.setStorageSync('recharge_version', recharge_version) // 缓存协议版本
      wx.showLoading({
        title: '正在充值中',
        mask: true
      })
      const user = wx.getStorageSync('user');

      const { storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
      // 请求优惠券充值接口
      const res = await app.api.depositByVoucher({
        // 会员id
        customerID: user.userID,
        // 优惠券码
        couponDetailCode: this.data.couponNo.replace(/\s/g, ''),
        // 门店编码
        storeCode: storeInfo.storeCode || ''
      }).catch(e => {
        console.log(e)
        wx.hideLoading()
      })
      wx.hideLoading();
      const { code, description, rechargeAmount } = res && res.data || {}
      const title = description || '非常抱歉，因为系统原因，未能充值成功，请联系客服处理~'
      // 充值成功
      if (rechargeAmount && Number(rechargeAmount) > 0) {
        // 清空数据
        this.setData({
          couponNo: '',
          isEnableCouponBtn: false
        })
        // 刷新余额
        this.getRechargeActivityInfoFn()
        setTimeout(() => {
          wx.showToast({
            title: '充值成功',
            duration: 2000,
            icon: 'none'
          })
        }, 200)
        return
      } else {
        if (code && code === 40000) {
          setTimeout(() => {
            wx.showToast({
              title,
              duration: 2000,
              icon: 'none'
            })
          }, 200)
        } else {
          // 其他错误的提示
          commonObj.showModal('充值异常', title, true, '联系客服', '我知道了', function (res) {
            if (res.confirm) {
              wx.makePhoneCall({
                phoneNumber: '************'
              })
            }
          })
        }
      }
    }
  },


  /**
   * 查看章程
   */
  checkConstitution(e){
    const { type } = e.currentTarget.dataset
    const pageUrl = encodeURIComponent(`${protocolUrl[type]}?close=${this.data.tick? 'Y':'N'}`)
    wx.navigateTo({
      // url这里test分支合并有异常 需要去掉encodeURIComponent
      url: `/h5/pages/commonLink/index?pageUrl=${pageUrl}`
    })
  },

  /**
   * 获取当前机器下是否应该弹窗显示扫码用途确认框
   */
  getConfirmRecharge() {
    const confirmRechargeScan = wx.getStorageSync('confirmRechargeScan');
    if (!confirmRechargeScan) {
      return
    }
    this._data.confirmRechargeScan = {
      ...this._data.confirmRechargeScan,
      ...confirmRechargeScan
    }
  },

  /**
   * 当用户点击我知道了，将用户点击后的动作写入缓存
   * @param {'Card' | 'Coupon'} rechargeType 充值类型：好吃卡 | 优惠券
   * @param {Boolean} isClicked 是否已经点击
   */
  setConfirmRecharge(rechargeType, isClicked = true) {
    this._data.confirmRechargeScan[`isClicked${rechargeType}`] = isClicked
    wx.setStorageSync('confirmRechargeScan', this._data.confirmRechargeScan)
  },

  /**
   * 点击相机图标
   * @param {Object} e Event
   */
  async handleClickCamera(e) {
    const targetInput = e.currentTarget.dataset.icon
    // 先判断是否需要弹窗提示
    const clickScanType = {
      cardNo: {
        text: '好吃卡',
        rechargeType: 'Card',
        isNoClick: !this._data.confirmRechargeScan.isClickedCard
      },
      couponNo: {
        text: '代金券',
        rechargeType: 'Coupon',
        isNoClick: !this._data.confirmRechargeScan.isClickedCoupon
      }
    }
    // 如果没有点击过 则弹窗显示
    if (clickScanType[targetInput].isNoClick) {
      const res = await wx.showModal({
        content: `此处我们使用图片仅用于${clickScanType[targetInput].text}卡面识别券码信息`,
        confirmText: '同意',
        cancelText: '拒绝',
        confirmColor: '#008C3D',
        // showCancel: false
      });
      if (res.cancel) {
        return
      }

      this.setConfirmRecharge(clickScanType[targetInput].rechargeType)
    }
    // 获取扫码内容
    const scanContent = await this.getScanContent();

    // 验证的扫码内容的长度
    const validateLength = e.currentTarget.dataset.icon === 'cardNo' ? [12] : [18, 21]

    // 长度不符合 或 好吃卡不是数字 表示校验失败
    if (!validateLength.includes(scanContent.length) || (e.currentTarget.dataset.icon === 'cardNo' && isNaN(scanContent.length))) {
      wx.showToast({
        title: '格式错误，请扫描正确的卡券',
        icon: 'none'
      })
      return
    }
    // 设置扫码获取的内容
    this.setData({
      // 第二个参数可以指定true 因为前面都做了判断
      [e.currentTarget.dataset.icon]: this.formatNo4(scanContent, true)
    }, () => {
      // 动态设置是否按钮禁用状态
      this.changeCardInput()
      this.changeCoupon()
    })
  },

  /**
   * 打开摄像头扫描二维码，获取扫码内容
   */
  getScanContent() {
    return new Promise((resolve, reject) => {
      wx.scanCode({
        desc: '支持条码/二维码',
        scanType: ['qrCode', 'barCode'],
        success: (res) => {
          // 防止toast被终止 延迟响应
          setTimeout(async () => {
            resolve(res.result)
          }, 1500)
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  },

  /**
   * 监听好吃卡卡号充值 并将字符串格式化为4位一组的形式
   * @param {Object} e Event
   */
  watchCardNo(e) {
    const iptNum = this.formatNo4(e.detail.value)
    this.setData({
      cardNo: iptNum
    })
    // 动态设置是否按钮禁用状态
    this.changeCardInput()
  },

  /**
   * 清空输入框
   * @param {Object} e Event
   */
  clearBtnClick(e) {
    const keyName = e.currentTarget.dataset.icon || ''
    // 点击的清空时 页面索引
    const objIndex = {
      cardNo: 0,
      cardPwd: 0,
      couponNo: 1
    }
    // 清空禁用的key
    const disabledBtnKey = ['isEnableCardBtn', 'isEnableCouponBtn'][objIndex[keyName]]
    this.setData({
      [keyName]: '',
      [disabledBtnKey]: false
    })
  },
  /**
   * 监听好吃卡密码充值的输入
   * @param {Object} e Event
   */
  watchCardPwd(e) {
    this.setData({
      cardPwd: `${e.detail.value}`.replace(/\D/g, '')
    }, () => {
      // 动态设置是否按钮禁用状态
      this.changeCardInput()
    })
  },
  /**
   * 监听优惠券充值 并将字符串格式化为4位一组的形式
   * @param {Object} e Event
   */
  watchCouponNo(e) {
    const iptNum = this.formatNo4(e.detail.value, true)
    this.setData({
      couponNo: iptNum
    }, () => {
      // 动态设置是否按钮禁用状态
      this.changeCoupon()
    })
  },

  /**
   * 将字符串格式化为每4位一组
   * @param {String} txt 要格式化的值
   * @param {Boolean} excludeNumber 结果是否包含非数字
   * @returns 格式化后的值
   */
  formatNo4(txt, excludeNumber) {
    if (!txt) {
      return ''
    }
    if (!excludeNumber) {
      txt = `${txt}`.replace(/\D/g, '')
    } else {
      txt = `${txt}`.replace(/\W/g, '')
    }
    return `${txt}`.replace(/(\w{4})(?=\w)/g, (matchStr, matched) => matched + ' ')
  },

  /**
   * 输入好吃卡信息时触发，判断是否启用充值按钮
   */
  changeCardInput() {
    const isEnableCardBtn = this.data.cardNo.replace(/\s/g, '').length === 12 && this.data.cardPwd.length === 6
    this.setData({
      isEnableCardBtn
    })
  },

  /**
   * 输入优惠券时触发，判断是否启用充值按钮
   */
  changeCoupon() {
    const isEnableCouponBtn = [18, 21].includes(this.data.couponNo.replace(/\s/g, '').length)
    this.setData({
      isEnableCouponBtn
    })
  },
  /**
   * 获取充值信息
   */
  async getRechargeActivityInfoFn(){
    await Promise.all([
      this.getAccountAmount(),
      this.getRechargeRuleText()
    ])
  },
  /**
   * tab底部充值文案
   */
  async getRechargeRuleText() {
    const {
      data: {
        onlineRechargeDesc = '',
        rechargeInstruction = '',
        voucherRechargeRules = ''
      }
    } = await getRechargeRuleText()

    this.setData({
      rechargeRules: onlineRechargeDesc.replace(/\\n(\s)?/g, '\n'),
      rechargeCardRules: rechargeInstruction.replace(/\\n(\s)?/g, '\n'),
      rechargeCouponRules: voucherRechargeRules.replace(/\\n(\s)?/g, '\n'),
    }, () => {
      this.handleSwiperChanged(true);
    })
  },
  /**
   * 设置预付卡协议版本缓存
   */
  async setProtocolVersion() {
    const {
      data: { prepaid_card },
    } = await app.api.getProtocolVersion();
    const recharge_version =  wx.getStorageSync('recharge_version')
    let tick = this.data.tick || false
    if(recharge_version && recharge_version === prepaid_card){ // 有过缓存 切版本协议一致 其他情况就是协议不选中
      tick = true
    }
    this.setData({
      tick,
      recharge_version:prepaid_card
    })
  },
  
  /**
   * @description 跳转至提现页面
   * 点击提现按钮时触发 -> 展示用户提现记录列表
   */
  goWithdrawPage() {
    const params = {
      /* 主钱包冻结金额 */
      lockingMainBalance: this.data.lockingMainBalance,
      /* 果币冻结金额 */
      lockingFruitCoinBalance: this.data.lockingFruitCoinBalance
    }
    wx.navigateTo({
      // 提现记录页路由路径
      url: `/userA/pages/withdrawList/index?params=${encodeURIComponent(JSON.stringify(params))}`,
    })
  },

  /**
   * 获取用户冻结金额
   */
  async getAccountAmount() {
    const user = wx.getStorageSync('user')
    const param = {
      customerID: user?.userID,
      isNeedBalance: 'Y',
      isNeedIntegral: 'N',
      isNeedCoupon: 'N',
      isCountExpireSoon: 'N'
    }
    const { data } = await app.api.getUserAmount(param)
    const { lockingMainBalance = 0, lockingFruitCoinBalance = 0, walletAmount = 0, fruitCoinBalance = 0, fruitCoinDescription = '' } = data || {}
    this.setData({
      /* 主钱包冻结金额 */
      lockingMainBalance,
      /* 果币冻结金额 */
      lockingFruitCoinBalance,
      // 余额
      balance: walletAmount,
      // 果币部分余额
      fruitCoinBalance,
      // 果币说明弹窗文案
      fruitCoinDescription
    })
  },
  /**
   * 获取和绑定store数据
   */
  getStoreData() {
    this.storeBindings = createStoreBindings(this, {
      store: locateStore,
      fields: {
        useDefaultAddress: (store) => store.useDefault
      }
    })
  },
  handleConfirm({
    detail
  }) {
    // 绑定事件
    rechargeModalObj.modalPromise.resolve(detail)
    this.setData({
      showModal: false,
      modelContent: '',
      modelDescContent: ''
    })
  }
})
