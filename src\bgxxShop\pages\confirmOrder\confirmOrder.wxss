/* 取消弹窗样式 */
/* 蒙层 */
@import "/source/style/popup.wxss";
@import "./template/index";
@import '/source/style/border.wxss';
/* 短信验证样式 */
@import '/components/SMS-validate/sms-popup-common.wxss';
page {
  background-color: #f5f5f5;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 300rpx;
  background: linear-gradient(to bottom, #34BA36, #F7F8FA);
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.flex-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt_24rpx {
  margin-top: 24rpx;
}

button::after {
  border: none;
}

.confirm-order {
  color: #333;
  text-align: left;
  top: 0rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
}

/* 地址组件样式 */
.address-border {
  background-color: #fff;
  padding: 20rpx 24rpx;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
}

.fixed-address {
  position: fixed;
  bottom: 96rpx;
  left: 0;
  right: 0;
  z-index: 102;
  background-image: repeating-linear-gradient(-45deg,#FFADB0,#FFADB0 20rpx, #FFFFFF 0, #FFFFFF 30rpx, #A8CFFF 0,#A8CFFF 50rpx,  #FFFFFF 0, #FFFFFF 60rpx);
}

.fixed-address-info {
  margin-top: 4rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  line-height: 40rpx;
  color: #000000;
  background-color: #FFFFFF;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.address-border.fixed.iphone-x {
  bottom: 160rpx;
}

.address {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.address-info {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  overflow: hidden;
}

.address-store-name {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 45rpx;
  font-size: 36rpx;
  font-weight: bold;
  color:#0F0F0F;
}

.address-detail {
  margin-bottom: 11rpx;
  font-size: 26rpx;
  line-height: 40rpx;
  font-weight: 400;
  color:#555555;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.address-store {
  font-size: 24rpx;
  line-height: 34rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.address-store .delivery-door-store {
  display: flex;
  align-items: center;
}

.address-store .store-name {
  display: block;
  max-width: 232rpx;
}

.address-store .door-switch {
  width: 24rpx;
  height: 24rpx;
}

.address-store-time {
  margin-bottom: 12rpx;
  font-size: 22rpx;
  line-height: 37rpx;
  color: #999;
}

.address-right-arrow {
  margin-left: 15rpx;
  width: 20rpx;
  height: 20rpx;
  flex-shrink: 0;
}

.placeholder-style {
  font-weight: normal;
}

.shopping-bag-tip{
  margin-top: 12rpx;
  width: 100%;
  font-size: 22rpx;
  color: #FFA006;
}

.shopping-bag-tip image{
  width: 32rpx;
  height: 32rpx;
}

.shopping-bag-tip text{
  font-size: 24rpx;
  color: #FF7900;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shop-tips {
  width: 100%;
  font-size: 22rpx;
  color: #858996;
}

.shop-tips-inner {
  display: flex;
  align-items: center;
  padding: 3px 0;
}

.shop-tips .icon-tips {
  width: 32rpx;
  height: 31rpx;
}

.choice-address {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 136rpx;
}

.choice-address .choice-address-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 93%;
  height: 72rpx;
  border: 2px dashed #dcdcdc;
  box-sizing: border-box;
  background-color: #fff;
  color:rgba(102,102,102,1);
  font-size: 28rpx;
  border-radius: 4rpx;
}

.choice-address .sign {
  margin-right: 6rpx;
  font-size: 40rpx;
  margin-top: -6rpx;
}

.cell {
  padding-left: 24rpx;
  padding-right: 24rpx;
  min-height: 88rpx;
}

.goods-list-wrapper .goods-list-header {
  display: flex;
  height: 80rpx;
  justify-content: space-between;
  align-items: center;
}

.goods-list-wrapper .goods-list-header .title {
  color: #222;
  font-weight: 500;
  font-size: 28rpx;
}
.goods-list-wrapper .goods-list-header .title.text-red {
  color: #FF7387;
}

.goods-list-wrapper .goods-list-header .delivery-time {
  font-size: 26rpx;
  color: #00A644;
}

.delivery-time:after {
  display: inline-block;
  /* margin-left: 8rpx;
  content: '';
  width: 20rpx;
  height: 20rpx;
  background-image: url('https://resource.pagoda.com.cn/group1/M03/38/09/CmiLkGAVHc6AdSijAAAAx7FzIOM886.png'); */
  background-size: 100% auto;
}

.time-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-picker text {
  font-size: 26rpx;
  color: #00A644;
}

.fail-picker text {
  color: #e81a1a;
}

.total {
  display: flex;
  flex-direction: column;
}

.total .total-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
}

.total .total-original-price, .total-item .total-original-price {
  position: relative;
  margin-right: 12rpx;
  font-size: 24rpx;
  color: #999;
  /* text-decoration: line-through; */
}
.total-original-price::after {
  content: '';
  width: 100%;
  position: absolute;
  left: 2rpx;
  top: 50%;
  height: 1rpx;
  background-color: #999;
}

.total .vip-free {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 22rpx;
  color: #8E672E;
}

.total .total-item:first-child{
  margin-top: 24rpx;
}

.total-item .total-item-tip {
  font-size: 28rpx;
  color:rgba(34,34,34,1);
}

.total-item .right-text {
  font-size: 28rpx;
  color:#0F0F0F;
}

.total-item .total-count {
  margin-right: 8rpx;
  font-size: 22rpx;
  color: #222222;
}

.total-item .total-item-pay {
  font-size: 28rpx;
  font-weight: bold;
  color: #000000;
}

.total .total-item-right {
  margin-top: 8rpx;
  flex-direction: row-reverse;
  height: 96rpx;
}

.total-item-right .pay-right {
  display: flex;
  align-items: baseline;
}

.delivery-money {
  margin-right: 8rpx;
}

/* tips */
.vip-tips-wrapper {
  margin: 20rpx 0 24rpx 0;
  display: flex;
  flex-direction: column;
  font-size: 28rpx;
  color:rgba(34,34,34,1);
}

.vip-tips:empty{
  display: none;
}

.pay-type .pay-type-tip {
  height: 88rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  font-weight: bold;
  color:rgba(34,34,34,1);
}

/* 选择支付类型 */
.pagoda-pay-item {
  border-bottom: 1rpx solid #f5f5f5;
}
.pay-type .pay-type-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 110rpx;
  font-size: 32rpx;
  color: #222222;
  padding: 0 24rpx;
}

.pay-type-item image {
  margin-right: 8rpx;
  width: 44rpx;
  height: 44rpx;
  margin-left: -4rpx;
}

.pay-type .pay-type-item .disabled {
  opacity: .5;
}

.pay-type .pay-type-item .disabled-text {
  font-size: 28rpx;
  color: #999;
}

.pay-type .balance {
  display: inline-block;
  color: #666666;
  font-size: 28rpx;
}

/* 浮动栏 */
.fixed-column-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 102;
  background: #ffffff;
}

.fixed-column {
  position: relative;
  height: 110rpx;
  display: flex;
  flex-direction: row-reverse;
  background-color: #fff;
  align-items: center;
}

.fixed-column-wrapper .iphone-x-column {
  height: 64rpx;
  background-color: #fff;
}

.fixed-column .left-area {
  display: flex;
  align-items: center;
  color:#000000;
  font-size: 28rpx;
}

.fixed-column .order-discount {
  text-align: right;
  font-size: 26rpx;
  color: #FF7387;
}

.fixed-column .submit-btn {
  width: 226rpx;
  height: 80rpx;
  background-color: var(--pf-primary);
  color: #fff;
  font-size: 32rpx;
  text-align: center;
  border-radius: 40rpx;
  overflow: visible;
  margin: 0 24rpx 0 32rpx;
  line-height: 80rpx;
}

.fixed-column .submit-btn.disabled {
  background-color: #ADADAD;
}

.fixed-tips {
  padding: 20rpx 24rpx;
  position: fixed;
  bottom: 110rpx;
  left: 0;
  right: 0;
  z-index: 102;
  background-color: rgba(238, 247, 225, .9);
  font-size: 24rpx;
  color: #7C9D4C;
}

.fixed-tips.iphone-x {
  bottom: 174rpx;
}

.delivery-info {
  margin-top: 24rpx;
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 16rpx;
}

.delivery-info .delivery-way {
  position: relative;
  width: 100%;
  height: 72rpx;
  top: 1rpx;
}

.delivery-way:before {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 64rpx;
  background: linear-gradient(180deg, #FCFFF8 0%, #EBF5E1 100%);
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.delivery-way .way-list {
  display: flex;
  align-items: flex-end;
  position: relative;
  width: 100%;
  height: 100%;
}

.delivery-way image{
  width: 78rpx;
  height: 28rpx;
  position: absolute;
  top: -14rpx;
  right: 68rpx;
}

.delivery-way .zero-cost{
  top: -8rpx;
}

.way-list .delivery-way-item {
  flex: 1;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  font-size: 26rpx;
  color: #555555;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.way-list .actived-way-item {
  padding-top: 8rpx;
  height: 76rpx;
  font-size: 30rpx;
  font-weight: bold;
  color:#4BC14C;
  background-color:#FFFFFF;
}

.way-list .delivery-way-item-line {
  position: absolute;
  left: 160rpx;
  width: 48rpx;
  height: 6rpx;
  background: #34BA36;
  border-radius: 3rpx;
}
.way-list .item-line-take {
  left: 494rpx;
}

.delivery-door-info {
  margin-bottom: 20rpx;
  display: flex;
  overflow: hidden;
  font-size: 26rpx;
  color: #555;
  font-weight: 400;
}

.more-line-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.delivery-door-info .delivery-phone-number {
  margin: 0 12rpx;
  flex-shrink: 0;
}

.address-info .address-info-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.order-list-module {
  width: 100%;
  border-radius: 16rpx;
  background-color: #fff;
}

.total-item .new-full-reduction {
  display: flex;
  align-items: center;
}

.new-full-reduction image {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

.order-list-goods {
  width: 100%;
}

.order-list-goods .goods-list-wrapper {
  margin-top: 20rpx;
  border-radius: 16rpx;
  background-color: #fff;
}

.vip-tips-wrapper .cheap-activity {
  position: relative;
  margin-top: 20rpx;
}

.cheap-activity .cheap-activity-explain {
  margin-top: 4rpx;
  font-size: 22rpx;
  color: #999;
}

.vip-tips-right {
  display: flex;
  align-items: center;
}

.cheap-activity .vip-tips-right view {
  color: var(--bgxxThemeColor);
}

.activity-item .vip-tips-right .coupon-empty{
  color: #858996;
}

.cheap-activity .vip-tips-right .no-join {
  font-size: 28rpx;
  color: #0F0F0F;
}

.flex-cell .cheap-item {
  display: flex;
  align-items: center;
}

.flex-cell .cheap-item text {
  font-size: 28rpx;
  color:rgba(43,43,43,1);
}

.activity-item .cheap-activity-tip {
  margin-left: 12rpx;
  height: 30rpx;
  line-height: 30rpx;
  display: flex;
  align-items: center;
  padding: 0 6rpx;
  font-size: 20rpx;
  color: #FF7387;
}

.invoice-info {
  color:rgba(34,34,34,1);
  font-size: 28rpx;
}

.flex-cell .selected-coupon-count {
  margin-left: 8rpx;
  padding: 3rpx 8rpx;
  border-radius: 8rpx;
  background: #FFF4E5;
  text-align: center;
  font-size: 20rpx;
  color: #FF9A00;
}

.flex-cell .coupon-desc {
  font-size: 28rpx;
}

.coupon-desc .vip-sign {
  display: flex;
  align-items: center;
  height: 34rpx;
  line-height: 34rpx;
}

.vip-sign .img {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 34rpx;
  height: 34rpx;
  background: #FFDC89;
  border-radius: 6rpx 0 0 6rpx;
}

.vip-sign .img image {
  width: 30rpx;
  height: 30rpx;
}

.vip-sign text {
  padding: 0 10rpx;
  background: linear-gradient(135deg, #FFF2DB 0%, #FFEEC5 100%);
  border-radius: 0 6rpx 6rpx 0;
  font-size: 24rpx;
  color: #8E672E;
}

.vip-tips-right .coupon-cheap-price {
  display: flex;
  color: var(--bgxxThemeColor);
  align-items: center;
}

.vip-tips-right .coupon-use {
  color:rgba(34,34,34,1);
}

.vip-tips-right .coupon-empty {
  color:rgba(136,136,136,1);
}

.goods-list-description{
  width: 94%;
  height: 96rpx;
  padding: 8rpx 16rpx;
  margin: 18rpx auto;
  background: #FFF4E5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #E79211;
}

.goods-list-description text:last-child{
  color: var(--bgxxThemeColor);
  text-decoration: underline;
}

.goods-list-scroll-wrap {
  height: calc(75vh - 90rpx);
}

.invalid-goods-list-wrap {
  height: calc(40vh - 128rpx);
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
}

.close-btn {
  margin: 0 24rpx;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  color: #FFFFFF;
  background-color: var(--pf-primary);
  border-radius: 48rpx;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
}

.close-btn-iphonex{
  margin-bottom: 40rpx;
}

.font-color {
  color: var(--bgxxThemeColor)!important;
}

.arrow-right {
  margin-left: 4rpx;
  width: 20rpx!important;
  height: 20rpx!important;
}

.no-discount-box{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  animation: mask ease-in-out 0.5s;
}

@keyframes mask{
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.no-discount-box .no-discount{
  width: 100%;
  height: 50%;
  position: fixed;
  left: 0;
  bottom: 0;
  background: linear-gradient(180deg, #FFD3D3 0%, #FFF3F3 6%, #FFFFFF 9%, #FFFFFF 100%);
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1001;
  animation: noDiscount ease-in-out 0.5s;
}

@keyframes noDiscount{
  0% {
    transform: translateY(100vh);
  }
  100% {
    transform: translateY(0);
  }
}

.no-discount .title{
  margin: 24rpx 0 0 24rpx;
  display: flex;
  align-items: center;
}

.no-discount .title text{
  font-size: 32rpx;
  color: var(--bgxxThemeColor);
  font-weight: bold;
}

.no-discount .title image:first-child{
  width: 198rpx;
  height: 32rpx;
}

.no-discount .title image:last-child{
  width: 302rpx;
  height: 32rpx;
}

.no-discount .description{
  margin: 6rpx 0 24rpx 24rpx;
  font-size: 28rpx;
  color: #858996;
}

.no-discount .discount-goods-list{
  height: calc(40vh - 140rpx);
}

.discount-goods-list .discount-goods-item{
  margin: 0 0 16rpx 24rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.discount-goods-item image{
  margin-right: 24rpx;
  width: 104rpx;
  height: 104rpx;
}

.discount-goods-item .message{
  flex: 1;
  font-size: 28rpx;
  color: #222;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.discount-goods-item .message .price{
  margin-top: 10rpx;
  color: var(--bgxxThemeColor);
  font-weight: 600;
}

.discount-goods-item .message .price text:first-child{
  font-size: 42rpx;
}

.discount-goods-item .message .price text:last-child{
  font-size: 28rpx;
  color: #B5B7C0;
  font-weight: 400;
  text-decoration: line-through;
}

.discount-goods-item .message .price image{
  width: 60rpx;
  height: 28rpx;
  margin: 0 8rpx;
}

.no-discount .handle-btn{
  width: 100%;
  height: 134rpx;
  padding: 14rpx 32rpx 0 32rpx;
  display: flex;
  justify-content: flex-start;
  background: #fff;
  box-shadow: 0 -2rpx 20rpx 0rpx rgba(188, 198, 210, 0.5);
}

.no-discount .handle-btn button{
  width: 335rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 46rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.no-discount .handle-btn button:first-child{
  border: 1px solid #ACACB1;
  background: #fff;
  color: #858996;
}

.no-discount .handle-btn button:last-child{
  margin-left: 16rpx;
  background: var(--bgxxThemeColor);
  color: #fff;
}

.discount-confirm-box{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.discount-confirm-box .discount-confirm{
  width: 75%;
  position: fixed;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background: linear-gradient(180deg, #FFD3D3 0%, #FFF3F3 12%, #FFFFFF 22%, #FFFFFF 100%);
  border-radius: 14rpx;
  z-index: 1001;
  text-align: center;
  font-size: 28rpx;
  color: #222;
}

/* .discount-confirm image{
  width: 160rpx;
  height: 132rpx;
  position: relative;
  top: -116rpx;
} */

.discount-confirm .title{
  margin: 48rpx 0 24rpx 0;
  font-size: 34rpx;
  color: #000;
  font-weight: bold;
}

.discount-confirm .save-money{
  color: var(--bgxxThemeColor);
  font-weight: bold;
}

.discount-confirm>text{
  display: block;
  margin-bottom: 8rpx;
}

.discount-confirm .handle-btn{
  margin-top: 40rpx;
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  border-top: 1rpx solid #ddd;
  font-size: 32rpx;
  color: #000;
}

.discount-confirm .handle-btn button{
  flex: 1;
  height: 100%;
  line-height: 100rpx;
  text-align: center;
  background: #fff;
}

.discount-confirm .handle-btn button:first-child{
  border-right: 2rpx solid #ECEEF5;
}

.delivery-door-address{
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 40rpx;
  font-size: 36rpx;
  color: #0F0F0F;
  font-weight: bold;
}

.show-cost{
  display: flex;
  align-items: center;
}

.show-cost image{
  width: 24rpx;
  height: 24rpx;
}

.cost-description{
  padding: 0 40rpx;
  text-align: center;
}

.freight-description{
  height: calc(40vh - 128rpx);
}

.package-description{
  height: calc(70vh - 128rpx);
}

.package-description .tips-item{
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #222;
}

.package-description .tips-item-total{
  margin: 28rpx 0 24rpx 0;
}

.package-description .tips-item-total text:first-child{
  font-weight: 600;
}

.package-description .tips-item-total text:last-child{
  font-size: 56rpx;
  font-weight: 600;
  color: var(--pf-primary);
}

.package-description .description text:first-child{
  font-weight: 600;
  color: #222;
}

.close-btn-box{
  position: absolute;
  left: 50%;
  bottom: 24rpx;
}

.close-btn-box .know{
  width: 702rpx;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  transform: translateX(-50%);
  background-color: var(--pf-primary);
  border-radius: 48rpx;
  color: #FFFFFF;
}

.cost-description .tips, .package-description .description{
  font-size: 28rpx;
  color: #555;
  text-align: left;
  line-height: 40rpx;
}

.cost-description .tips text, .package-description .description text{
  display: block;
}

.cost-description .tips text:last-child{
  font-size: 24rpx;
  color: #858996;
}

.cost-rule{
  height: calc(88vh - 128rpx);
}

.cost-rule-one{
  display: flex;
  flex-direction: column;
  height: calc(71vh - 106rpx);
  padding: 0 24rpx;
}

.cost-rule .rule-content{
  overflow-y: auto;
  flex: 1;
}

.rule-content .description{
  width: 100%;
  padding: 16rpx;
  margin-bottom: 18rpx;
  background: #FFEDF0;
  border-radius: 8rpx;
}

.rule-content .description .rule-line {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #FD6E82;
}

.rule-content .description .rule-line .point {
  width: 6rpx;
  height: 6rpx;
  margin-right: 8rpx;
  border-radius: 3rpx;
  background: #FF1F3A;
  display: inline-block;
  vertical-align: middle;
}

.subtotal, .add-freight {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-right: 24rpx;
  padding-bottom: 20rpx;
  font-size: 24rpx;
  color: #0F0F0F;
}

.add-freight >text:last-child {
  margin-left: 24rpx;
}

.rmb-size-20 {
  font-size: 20rpx;
}

.subtotal .lineation {
  margin-right: 10rpx;
  text-decoration: line-through;
  color: #999;
}

.subtotal .actual{
  font-size: 28rpx;
  font-weight: bold;
}

.rule-list, .rule-statistics{
  /* margin: 0 24rpx; */
}
.rule-statistics{
  padding-bottom: 10rpx;
}
.rule-list .item{
  margin-top: 32rpx;
}

.rule-list .item .type >text:last-child{
  color: var(--pf-primary);
}

.cost-rule .division{
  width: 100%;
  height: 1rpx;
  background: #F2F2F2;
  margin: 12rpx auto 12rpx auto;
}

.rule-list .item, .rule-statistics{
  display: flex;
  flex-direction: column;
}

.total-rule-statistics {
  margin-top: 30rpx!important;
}

.rule-list .item .total-item, .rule-statistics .total-item, .rule-list .item .total-item .freight{
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.rule-list .item .total-item .freight .exceed{
  margin-left: 8rpx;
  color: var(--bgxxThemeColor);
}

.font-color-gray{
  color: #999;
}

.font-color-black{
  font-size: 28rpx;
  color: #222;
  font-weight: bold;
}

.buttom-tips{
  width: 100%;
  height: 90rpx;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}

.way-1.actived-way-item::after {
  left: 338rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDIiIGhlaWdodD0iNzYiIHZpZXdCb3g9IjAgMCA0MiA3NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNLTMyMyAwSDEuNjhhMTUuOTczIDE1Ljk3MyAwIDAgMSAxNS43MTMgMTMuMTAzTDI2LjU4IDU4LjE4QzI3LjE1NiA2MS4wMDMgMjkuODA3IDc0LjY1NSA0MiA3NmgtMzgxVjE2YzAtOC44MzcgNy4xNjMtMTYgMTYtMTZ6IiBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=');
}

.way-2.actived-way-item::after {
  right: 338rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDIiIGhlaWdodD0iNzYiIHZpZXdCb3g9IjAgMCA0MiA3NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMzY1IDBINDAuMzJhMTUuOTczIDE1Ljk3MyAwIDAgMC0xNS43MTMgMTMuMTAzTDE1LjQyIDU4LjE4QzE0Ljg0NCA2MS4wMDMgMTIuMTkzIDc0LjY1NSAwIDc2aDM4MVYxNmMwLTguODM3LTcuMTYzLTE2LTE2LTE2eiIgZmlsbD0iI0ZGRiIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+');
}

.actived-way-item::after {
  content: '';
  position: absolute;
  bottom: -1rpx;
  width: 42rpx;
  height: 76rpx;
  background-size: 100% 100%;
}

.top-border {
  border-top: 1rpx solid #f5f5f5;
}

.rmb-size {
  font-size: 22rpx!important;
  font-weight: bold;
}

.need-packing-tip {
  margin-left: 12rpx;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999;
}

.need-packing-tip image {
  margin-left: 4rpx;
}

.need-packing-item-amount {
  margin-left: 8rpx;
}

.select-delivery-time{
  font-size: 26rpx;
  color: #00A644;
}

.head-class{
  background: #F5F5F5;
  border-top-right-radius: 20rpx;
  border-top-left-radius: 20rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f5f5f5;
}
.title-class{
  font-size: 32rpx!important;
  height: 96rpx!important;
  line-height: 96rpx!important;
  font-weight: 500!important;
}

.package-title {
  height: 28rpx;
  line-height: 28rpx;
  padding: 0 8rpx;
  margin-left: 8rpx;
  background: #F5F5F5;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: #888;
}

.package-title-wrapper {
    display: flex;
    align-items: center;
}
.last-purchase-box {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: 126rpx;
  bottom: calc(126rpx + constant(safe-area-inset-bottom));
  bottom: calc(126rpx + env(safe-area-inset-bottom));
  width: 702rpx;
  height: 70rpx;
  line-height: 70rpx;
  opacity: 0.7;
  background: #000000;
  border-radius: 16rpx;
  z-index: 103;
}
.last-purchase-box-down {
  bottom: 206rpx;
  bottom: calc(206rpx + constant(safe-area-inset-bottom));
  bottom: calc(206rpx + env(safe-area-inset-bottom));
}
.last-purchase-box-bottom {
  bottom: 246rpx;
  bottom: calc(246rpx + constant(safe-area-inset-bottom));
  bottom: calc(246rpx + env(safe-area-inset-bottom));
}
.last-purchase-box .tips {
  margin-left: 20rpx;
  font-size: 26rpx;
  color: #ffffff;
}
.last-purchase-box .tips text:nth-child(2) {
  margin-left: 8rpx;
  font-weight: bold;
  color: #0AD55D;
}
.last-purchase-box .tips image {
  position: absolute;
  right: 4rpx;
  top: 50%;
  width: 24rpx;
  height: 24rpx;
  transform: translateY(-50%);
  padding: 20rpx;
}
