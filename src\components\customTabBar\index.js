import { storeBindingsBehavior } from 'mobx-miniprogram-bindings'
import store, { tabbarTypeMap } from '../../stores/module/tabbar'
import Transition from '../../utils/transition'
import sensorReportData from './sensorReportData'
import { changeTimelyChannel, changeFreshChannel } from '../../utils/report/setup'
const customQuery = require('../../mixins/behaviors/customQuery')
const { wrapSensorReportFn, genClickSensor } = require('../../utils/report/index')
const genClickReportData = genClickSensor(sensorReportData)

// const TO_XMDX_PIC_URL = 'https://resource.pagoda.com.cn/group1/M21/8A/58/CmiLkGJeH0CAfhhCAAEbuekfYQk360.png'
// const TO_BGY_PIC_URL = 'https://resource.pagoda.com.cn/group1/M21/8A/58/CmiLkGJeH2KAXKD6AAE99y6JHPM028.png'

// const tabbarTypeChange = {
//   [tabbarTypeMap.bgy]: tabbarTypeMap.xmdx,
//   [tabbarTypeMap.xmdx]: tabbarTypeMap.bgy
// }
/**
 * 增加是否点击过过期小红点变量
 */
let isClickedExpireCoupon = false

Component({
  // properties只有在非tab页作为常规组件使用时才会有值传入,
  // 否则就是默认的值
  properties: {
    /**
     * 是否为灰色主题
     */
    isGrayTheme: {
      type: Boolean,
      value: false
    },
    // 页面类型
    pageType: {
      type: String,
      value: 'tab'
    },
    // 高亮index值
    tabIndex: {
      type: Number,
      value: 0
    },
    // tabbar样式
    tabType: {
      type: String,
      value: tabbarTypeMap.bgy
    },
    /**
     * 是否将当前tab按钮，切换为回到顶部按钮模式
     */
    showBack2Top: {
      type: Boolean,
      value: false
    }
  },
  behaviors: [storeBindingsBehavior, customQuery],
  storeBindings: {
    store,
    fields: {
      /**
       * 当前tabbar类型
       * @returns { 'bgy' | 'xmdx' }
       */
      tabBarType: ({ tabBarType }) => tabBarType,
      /**
       * 是否百果园tabbar
       * @returns { boolean }
       */
      isBgyTabBar: ({ tabBarType }) => tabBarType === tabbarTypeMap.bgy,
      /**
       * 是否熊猫大鲜tabbar
       * @returns { boolean }
       */
      isXmdxTabBar: ({ tabBarType }) => tabBarType === tabbarTypeMap.xmdx,
      /**
       * 是否有拼团入口
       * @returns { boolean }
       */
      hasxmdxEntry: ({ hasxmdxEntry }) => hasxmdxEntry,
      /**
       * tabbar红点数据
       * @returns { Record<number, boolean> }
       */
      redDotData: ({ redDot, tabBarType }) => redDot[tabBarType],
      /**
       * tabbar badge数据
       * @returns { Record<number, string> }
       */
      badgeData: ({ badge, tabBarType }) => badge[tabBarType],
      /**
       * tabbar显示状态
       * @returns { { status: boolean; animation: boolean; } }
       */
      showStatus: ({ showStatus, tabBarType }) => showStatus[tabBarType],
      /**
       * 当前tabbar选中项
       * @returns { number }
       */
      selected: (store) => store.selected,
      /**
       * tabbar数据
       * @returns { {
       *   color: string;
       *   selectedColor: string;
       *   list: {
       *     pagePath: string;
       *     text: string;
       *     iconPath: string;
       *     selectedIconPath: string;
       *     pageType: 'tab' | 'normal';
       *     tabType: 'bgy' | 'xmdx';
       *     tabIndex: number;
       *   }[]
       * } }
       */
      tabBar: (store) => store.tabBarData,
      /**
       * 即将过期优惠券数量张数
       * @returns Number
       */
      expireCouponCount: (store) => store.expireCouponCount
    },
    actions: ['setTabBarIndex', 'setTabBarType', 'simulateOnTabItemTap'],
  },
  observers: {
    showStatus({ animation, status }) {
      this.updateShow({
        // 切换tabbar类型导致tabbar显示/隐藏切换,不展示动画效果
        animation: this.data.tabBarType === this._data.tabBarType ? animation : false,
        status
      })
      this._data.tabBarType = this.data.tabBarType
    },
    selected(index) {
      // 如果被点击到我的页面 则需要隐藏即将过期数量
      if (this.data.isClickedExpireCoupon || this.data.tabBar.list[index].tabName !== 'index') {
        return
      }
      this.setData({
        isClickedExpireCoupon: true
      })
      isClickedExpireCoupon = true
    }
  },
  data: {
    showTrans: { show: true, proxyClass: '' },
    isClickedExpireCoupon: false
  },
  methods: {
    /**
     * wxml中触发切换tab操作
     */
    switchTab: wrapSensorReportFn(function (e) {
      const { index, pagePath, pageType, tabType, tabName } = e.currentTarget.dataset
      const pages = getCurrentPages() || []
      const { route = '' } = pages[pages.length - 1] || {}
      // 当前已在需要切换tab页面，不需要再次切换操作
      if (`/${route}` === pagePath) {
        // 如果是首页 并且是展示回到顶部按钮
        if (pagePath === '/pages/homeDelivery/index' && this.data.showBack2Top) {
          this.goBack()
        }
        return
      }

      //  切换至次日达时，修改渠道为次日达
      if (tabName === 'xxshop') {
        changeFreshChannel()
      }
      //  其余tab一律按照及时达处理
      else {
        changeTimelyChannel()
      }

      //  跳转会员中心页时，进行的业务处理逻辑
      if (tabName === 'memberCode') {
        console.log('tabbar点击____________');

        const app = getApp()
        const city = wx.getStorageSync('timelyCity') || {}
        const user = wx.getStorageSync('user')

        const hasStoreCode = city.storeCode
        const isLoginAndNotAddGroup = user && !user.isAddGroup

        if (hasStoreCode && isLoginAndNotAddGroup) {
          //  预先请求及时达定位的门店社群信息
          //  因为该接口时间较长，选择在前端资源加载前进行请求并进行缓存
          //  避免接口时间过长导致用户体验差的问题
          app.api.getManagerCodeByStoreCode({
            storeCode: city.storeCode
          })
        }

      }

      console.log('switchTab', pageType, tabType, tabName, pagePath);
      // tabBarStore mixin 在 onload 和 onshow 有设置 index 和 tabType， 所以这里不调用setTabBarIndex 和 setTabBarType
      // 不同页面类型对应不同跳转方式
      wx[{ normal: 'reLaunch', tab: 'switchTab', secondary: 'navigateTo' }[pageType]]({
        url: pagePath,
        success: () => {
          // success回调里，新的页面实例有可能还没行成，所以延时执行
          setTimeout(() => this.simulateOnTabItemTap({ index, pagePath, type: tabType }), 100)
        },
        fail(result) {
          console.log('result', result)
        }
      })
      // 返回埋点数据
      return genClickReportData(tabName)
    }),
    /**
     * 切换tabbar类型时更新对应tabbar显示状态
     */
    updateShow({ animation, status }) {
      if (status === this.data.showTrans.show) { return }
      if (animation) {
        this._data.showTrans.setShow(status)
      } else {
        this.setData({
          'showTrans.show': status
        })
      }
    },
    /**
     * 回到顶部
     */
    goBack() {
      this.triggerEvent('goBack')
    }
  },
  lifetimes: {
    created() {
      this._data = {
        tabBarType: store.tabBarType, // 当前tabbar类型,用于判断tabbar类型切时是否显示隐藏tabbar
        showTrans: new Transition({
          showTrans: true,
          context: this
        }, {
          enter: 'tab-bar-show',
          enterTo: 'tab-bar-show-to',
          leave: 'tab-bar-hide',
          leaveTo: 'tab-bar-hide-to'
        })
      }
    },
    attached() {
      this.setData({
        isClickedExpireCoupon
      })
    },
    ready() {
      // 冷启动时组件ready在先，mobx的初始化还没完成
      setTimeout(() => {
        this.setData({
          showTrans: { show: this.data.showStatus.status, proxyClass: '' },
        })
      })
    }
  },
})
