// node.js sdk 文档：https://cloud.tencent.com/document/product/436/64980
const COS = require("./sdk/cos-nodejs-sdk-v5.min.js")
const { COS_GLOBAL } = require("./const/cosConfig")
/**@type {CosConfigType}*/
const cosAuthorizationOptions = require('./const/cosConfig').cosAuthorizationOptions[process.env.RUNTIME_ENV || 'test_exc']

/**
 * @typedef CosConfigType
 * @property {string} region
 * @property {string} bucket
 * @property {string} secretId
 * @property {string} secretKey
 */

const COS_MAP = {
  [COS_GLOBAL]: {
    COS_KEY: 'WXAPP_COS_INSTANCE',
    COS_FILE: 'WXAPP_READ_COS_FILE',
    CONFIG: cosAuthorizationOptions
  }
}

/**
 * 存储现有键的COS对象
 */
const API = {}

/**
 * @desc 获取cos实例对象
 * @param {string} cosKey
 * @returns {COSAction}
 */
function cosInstance (cosKey) {
  const cosMap = COS_MAP[cosKey]
  const { COS_KEY, CONFIG } = cosMap
  if (API[COS_KEY]) {
    return API[COS_KEY];
  }
  const cosAction = new COSAction({ logger: true, cosConfig: CONFIG });
  API[COS_KEY] = cosAction;
  return cosAction;
}

class COSAction {
  /**
   *
   * @param {object} payload
   * @param {boolean} payload.logger
   * @param {CosConfigType} payload.cosConfig
   */
  constructor({
    logger = false,
    cosConfig,
  }) {
    const {
      region, bucket, secretId, secretKey
    } = cosConfig
    this.region = region
    this.bucket = bucket
    this.secretId = secretId
    this.secretKey = secretKey
    this._logger = logger
    this._defaultRes = {}
    this._cos = new COS({
      SecretId: secretId,
      SecretKey: secretKey
    })
  }
  /**
   * @desc 默认resolve
   */
  _requestCallback(resolve, reject) {
    return (err, data) => {
      if (err && err.error) {
        console.log('_requestCallback: 返回错误', err.error);
        reject(err)
        // resolve(this._defaultRes)
      } else if (err) {
        console.log('_requestCallback: 请求出错', err);
        reject(err)
        // resolve(this._defaultRes)
      } else {
        // console.log('_requestCallback: 请求成功', data);
        resolve(data)
      }
    }
  }
  /**
   * @desc 获取对象
   * @param {string} key
   */
  getObject(key) {
    return new Promise((resolve, reject) => {
      this._cos.getObject({
        Bucket: this.bucket,
        Region: this.region,
        Key: `${key}`
      }, this._requestCallback(resolve, reject));
    })
  }

  /**
   * @desc 获取文件下载链接
   * @param {object} payload
   * @param {string} payload.key
   * @param {string} payload.bucket
   */
  getObjectUrl({ key, bucket }) {
    return new Promise((resolve, reject) => {
      this._cos.getObjectUrl({
        Bucket: bucket,
        Region: this.region,
        Key: key,
        Sign: true,
        Expires: 60 * 60 * 12 * 3,
      }, this._requestCallback(resolve, reject));
    })
  }
  /**
   * @desc 上传对象
   * @param {object} param0
   * @param {string} param0.cosDirectory
   * @param {string} param0.filename
   * @param {string} param0.filePath
   * @param {Function|undefined} param0.onProgress
   */
  uploadFile({ cosDirectory, filename, filePath, onProgress }) {
    console.log('cosDirectory', cosDirectory, 'filename', filename)

    return new Promise((resolve, reject) => {
      this._cos.uploadFile(
        {
          Bucket: this.bucket,
          Region: this.region,
          Key: `${cosDirectory}/${filename}`,
          FilePath: filePath,
          onProgress:
            onProgress ||
            (({ loaded, percent, speed, total }) => {
              console.log(
                `%c文件[${filename}]上传中` +
                  `%c进度: ${loaded}B / ${total}B, 速度：${speed}B/s, 已完成${(
                    percent * 100
                  ).toFixed(2)}%`,
                `background-color: #006400; color: #fff; padding: 2px 4px; border-radius: 4px 0 0 4px`,
                `background-color: #0198e1; color: #fff; padding: 2px 4px; border-radius: 0 4px 4px 0`
              )
            }),
        },
        this._requestCallback(resolve, reject)
      )
    })
  }
}

exports.cosInstance = cosInstance
