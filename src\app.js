//app.js
require('./mixins/mixin.js');
import overrideTabBarApi from './utils/wx/tabbar';
import promisifyAPI from './utils/wx/promisifyApi';
import { getAllSystemInfo } from './utils/wx/system';
// 重写使用到的TabBar API
overrideTabBarApi();
promisifyAPI();
var common = require('source/js/common.js');

var sensorsConfig = require('./utils/sensorsdata_conf');
var sensors = require('./source/js/sensorsdata.min.js');

var api = require('./api/index.js');
const request = require('./utils/request.js');
var { redDotInit } = require('source/js/redDot.js');
const { 
  themeColorConfig,
  prdUrl,
  ENV,
  baseUrl,
  appVersion,
  TDFP_OPT 
} = require('./utils/config');
import { toQueryString, getCurrentPageSync, _getApp, _setApp } from './utils/util';
import {
  checkUserIsStoreLeader,
  checkUserIsDistributor,
  saveTimelyDistributor,
  getUserAuth,
  userLogout,
  reporLocaionStore,
  isDeviceChangeLoginOuted,
  setDeviceChangeLoginOuted,
  getComplateMemberInfo,
  receiveFirstGradingEquity,
  isDistributorFromCache,
} from './service/userService.js';
import {
  initFruitFansStatus
} from './service/group.js'
import tabbarStore from './stores/module/tabbar';
import { checkTimelyGoodsDetailScene, isScanCode, parseTimelyGoodsDetailScene } from './pages/homeDelivery/utils/forwardNavigate';
import fruitCartStore from './stores/module/fruitCart';
import { freshCartStore } from './stores/module/freshCart';
import activityPrice from './source/js/requestData/activityPrice';
const { setCustomTabbarCartCount } = require('./utils/wx/tabbarCartCount');
import bgxxStores from './stores/module/bgxxStore'
import locateStores from './stores/module/locate'
import userStore from './stores/userStore'
import setCartCount from './mixins/bgxx/cartCount'

import { goodsCartApi } from './api/pandafresh.api'
import { setupSensors, setFromShareId, logoutSensors } from './utils/report/setup'
import { outLoginType } from './source/const/globalConst';
import { defaultVal, getStorage, findValue } from './utils/cyclomatic'
import { overrideLoading } from './utils/wx/wxLoading';
import { subProtocolValid } from './components/base/main-page/mixins/subProtocol'
import { getLandingPage } from './utils/report/utils/sensorUtils';
const locateService = require('./utils/services/locate');
const { wxappLruCache } = require('./utils/wxappLruCache/index')
const { refreshCosStoreInfo } = require('./utils/proxySlrRoute')
const {
  FruitGoods,
  getPriceActivityGoods,
} = require('./service/fruitGoods')
const {
  FreshGoods
} = require('./service/freshGoods')
const { sr, reportSensors, isStartReportPoint, startReportPointPromise } = setupSensors()
//  初始化用户授权信息
const userAuth = getUserAuth()

const emitter = require('./source/js/emitter')
const noPrd = ['debug', 'test', 'staging'].includes(ENV);
// logout 方法来登出已经做了用户关联的用户
logoutSensors()

import Monitor from './utils/monitor/index';
import { watchRouteChange } from './utils/route/index';
import { interceptHook } from './utils/report/pageView';
import { YN2Boolean } from './utils/YNBoolean';
interceptHook()

const monitor = new Monitor({
  reportUrl: `${baseUrl.dskhdSlrApi}/dskhd/api/log/frontEndReport`,
  projectName: 'wxapp',
  projectVersion: appVersion,
  showLog: ENV !== 'prod'
});

App({
  sr,
  subProtocolValid,
  monitor,
  // 开启上报埋点
  startReportPoint() {
		if (this.globalData.isStartReportPoint) {
			return
		}
		console.log('startReportPoint')
		sr.startReport()
		sensors.enableDataCollect();
		sensors.setPara(sensorsConfig);
		// sensors.init();
		this.initSensors()
		this.globalData.reportSensors = true
		this.globalData.isStartReportPoint = true
    startReportPointPromise.resolve()
  },
  onLaunch: function (options = {}) {
    console.log('App onLaunch', options)
    overrideLoading()
    _setApp(this)
    this.globalData.launchOptions = options;
    const { query = {} } = options || {}
    // 只有扫码场景,才维持isCarryStore参数
    // 其他场景,如从扫携带isCarryStore=N的小程序码进入小程序后
    // 再复制链接,不需要维持isCarryStore参数
    query.isCarryStore && (query.isCarryStore = isScanCode(options.scene) ? query.isCarryStore : '')
    this.globalData.globalLaunchOptions = options;

    const optionsShareObj = options.query.shareObj

    // 记录从分享进来的mp_shareID
    setFromShareId(optionsShareObj)

    this.onMemoryWarning();
    this.setLanuchScene(options);
    // 判断用户是否已登录
    this.checkUserLoginStatus();
    // 及时达优先使用当前定位地址，定位失败取缓存地址，所以这里不删除缓存
    this.deleteLocationInfo(0);
    activityPrice.initApp(this)
    freshCartStore.initApp({ app: this })

    // 上报神策冷启动
    if (reportSensors) {
      const uuid = sensors.store.getUUID()
      //  保存轨迹id
      wx.setStorageSync('traceId', `${uuid}_${Date.parse(new Date()) / 1000}`)
      this.reportSensorsData(optionsShareObj, options.path, 'appLaunch', options.query);
    }
    this.showUpdatePopup();
    // 检查广告参数是否过期
    this.checkAdParams();
    // 临时代码，718活动后去掉 不重新检查城市服务 不查电商阿波罗系统配置
    const isFromWaterGoodsPage = options.path === "homeDelivery/pages/waterGoods/index"
    if (isFromWaterGoodsPage) {
      const timelyCity = getStorage('timelyCity')
      if (timelyCity.cityID) {
        this.globalData.fruitFirstLocate = false
      }
    }
    setTimeout(() => {
      // 获取电商阿波罗系统配置
      this.getEshopSysConfig()
      // 检查当前用户是否为团长
      checkUserIsStoreLeader();
      // 查询当前用户是否为及时达分佣团长
      checkUserIsDistributor(true);
      this.getWxConfig() // 主题置灰、默认支付方式
    },5000)
    wx.removeStorageSync('showCurrentCity');
    wx.removeStorageSync('temporarySelectionAddress');
    wx.removeStorageSync('BLESS_CARD_INFO')
    !isFromWaterGoodsPage && this.loadShopCart()
    this.checkOpenid()

    getComplateMemberInfo()
    // 获取同盾blackbox
    this.getBlackbox()
    if (wx.onLazyLoadError) {
      wx.onLazyLoadError((res) => {
        _getApp().monitor.report({
          message: 'onLazyLoadError',
          errorMsg: res,
        })
      })
    }
    this.getSysInfo()
  },

  onShow: function (options = {}) {
    refreshCosStoreInfo()
    this.globalData.setScancode = true;
    this.globalData.launchOptions = options;
    this.globalData.resetFruitCartCheck = true
    console.log('App onshow', options)

    const optionsShareObj = options.query.shareObj

    // 记录从分享进来的mp_shareID
    setFromShareId(optionsShareObj)

    //接受智慧零售传过来的参数
    if (
      options.query &&
      options.query.chan_refer_app_id === 'wx9d4f5f62ea059f08'
    ) {
      const chanId = defaultVal(options.query.chan_id);
      const productId = defaultVal(options.query.productId);
      const chanReferAppId = defaultVal(options.query.chan_refer_app_id);
      const chanWxappScene = defaultVal(options.scene, -1);
      const txSensorData = {
        chanId,
        productId,
        chanReferAppId,
        chanWxappScene,
      };
      this.globalData.txSensorData = txSensorData;
    }
    this.globalData.referrerInfo = options.referrerInfo;
    // 接受心享小程序跳转过来的参数
    this.globalData.miniProgramExtData = options.referrerInfo.extraData;
    this.globalData.isShowSignoutModal = true
    // 这里不能给sceneCopy赋值！！！
    this.globalData.scene = options.scene
    if (options.scene === 1047 && options.path === 'pages/homeDelivery/index') {
      const codetype = options.query.codeType
      this.globalData.isScanCode = ['A', 'B', 'C', 'D', 'E'].includes(codetype);
      const codetypeKey = { A: 'code_type_a', B: 'code_type_b', C: 'code_type_c', D: 'code_type_d', E: 'code_type_e' }[codetype]
      codetypeKey && wx.reportAnalytics(codetypeKey)
    }
    this.globalData.formWxPublicToOrderList = options.scene === 1035 && options.path === 'userB/pages/orderList/index';
    [1045, 1046, 1084].includes(options.scene) && wx.reportAnalytics('pyq_ad', { ad: defaultVal(options.query.ad) })

    // 需要在更新完this.globalData.scene再调用
    saveTimelyDistributor(options.query || {}, this)

    // 获取分享门店信息
    this.getOptionStore(options);

    wx.onNetworkStatusChange(function (res) {
      if (!res.isConnected) {
        wx.reLaunch({
          url: '/homeDelivery/pages/noSignal/index',
        });
      }
    });

    this.checkForUpdate();

    // 登录状态下
    if (this.checkSignInsStatus()) {
      // 刷新是否是心享会员
      this.isSuperVip();
      // 检查是否有红点（红点的公共接口，由于tabbar个数会根据定位地址是否有次日达服务发生改动，所以首次启动定位前不检查红点）
      !this.globalData.fruitFirstLocate && redDotInit(this);
      // 领取首次定级权益（触发初始化领券）
      const receiveFirstMark = wx.getStorageSync('receiveFirstMark') || {}
      if (receiveFirstMark !== 'Y') {
        const { userID = '' } = wx.getStorageSync('user') || {}
        receiveFirstGradingEquity(userID)
      }
    }

    // 绑定刷新红点信息事件
    // this.event.on('refreshRedDot', (e) => {
    //   redDotInit(this);
    // });
    // 获取key值
    this.getMemberCodeKey();

    // 上报神策热启动数据
    if (reportSensors) {
      this.reportSensorsData(optionsShareObj, options.path, 'appShow');
      this.reportDefinedAppShow(options);
    }
    // 查询支付配置

    // 临时代码，718活动后去掉 不重新检查城市服务
    // const isFromWaterGoodsPage = options.path === "homeDelivery/pages/waterGoods/index"
    // 获取广告参数
    this.getAdParams(options);
    setTimeout(() => {
      options = {}
    }, 3000)
    initFruitFansStatus()
    this.refreshUserLabel()
  },
  onError(err) {
    _getApp().monitor.report({
      message: 'onError',
      errorMsg: err,
    })
  },
  onUnhandledRejection(err) {
    /**
     * typeof reason 可能是object, 直接 reason.stack
     */
    const { reason } = err
    if (typeof reason === 'object') {
      _getApp().monitor.report({
        message: 'onUnhandledRejection',
        errorMsg: reason.stack,
      })
    }
  },
  checkOpenid() {
    // 登录状态下，没有openid则踢出登录
    if (this.checkSignInsStatus()) {
      const wxSnsInfo = getStorage('wxSnsInfo')
      if (!wxSnsInfo.openid) {
        this.signOut()
      }
    }
  },
  checkRedDot() {
    if (!this.checkSignInsStatus()) {
      return;
    }
    redDotInit(this);
  },

  getAdParams(options) {
    // const { gdt_vid: clickId = '' } = options.query;
    // let adParams = wx.getStorageSync('adParams') || {};
    // if (clickId) {
    //   wx.setStorageSync('adParams', {
    //     ...adParams,
    //     clickId: clickId,
    //     clickIdUpdateTime: new Date().getTime(),
    //   });
    // }
    const { tx_cps_id: txCpsId = '', utm_source: utmSource } = options.query;
    if (txCpsId) {
      // 以后进入小程序需要缓存的参数统一缓存此
      const cacheQueryParams = getStorage('cacheQueryParams')
      wx.setStorageSync('cacheQueryParams', {
        ...cacheQueryParams,
        txCps: {
          txCpsId,
          updateTime: Date.now(),
        }
      });
    } else if (utmSource === 'zdm') {
      const cacheQueryParams = getStorage('cacheQueryParams')
      wx.setStorageSync('cacheQueryParams', {
        ...cacheQueryParams,
        utmSource: {
          channel: 'zdm',
          updateTime: Date.now(),
        }
      });
    }
  },
  /**
   * @description 根据key值获取缓存在本地的小程序入参，且判断有效期
   * @param cachekey 获取缓存中值对应的key
   * @param targetKey 获取目标值对应的key
   * @param exprime 过期时间
   *
   */
  getCacheQueryParams(cachekey, targetKey, exprime) {
    try {
      const cacheQueryParams = getStorage('cacheQueryParams')
      const target = cacheQueryParams[cachekey]
      if(!target) return ''
      if(Date.now() - target.updateTime > exprime) {
        delete cacheQueryParams[cachekey]
        wx.setStorageSync('cacheQueryParams', {
          ...cacheQueryParams
        })
        return ''
      }
      return defaultVal(target[targetKey])
    } catch(err) {
      return ''
    }
  },
  // 判断是否需要升级
  checkForUpdate() {
    // 判断用户的版本升级
    if (wx.getUpdateManager) {
      const updateManager = wx.getUpdateManager();
      if (!updateManager) {
        return
      }
      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，确定重启应用？',
          showCancel: false,
          success: function () {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate();
          },
        });
      });
      updateManager.onUpdateFailed(function () {
        // 新的版本下载失败
        console.log('下载失败');
        wx.showModal({
          title: '更新失败',
          content: '当前网络异常，请稍后重试',
          success: function () {},
        });
      });
    } else {
      wx.showModal({
        title: '提示',
        content:
          '当前微信版本过低，无法使用全部功能，请升级到最新微信版本后重试。',
        showCancel: false,
        success: function (res) {},
      });
    }
  },

  /**
   * 查询用户登陆状态
   */
  checkUserLoginStatus() {
    const user = getStorage('user')
    if (user.userToken) {
      Object.assign(this.globalData, {
        userToken: user.userToken,
        superVipStatus: defaultVal(user.superVipStatus, 'C'),
        customerID: user.userID,
        launchUserLoginStatus: 'login',
      });
    } else {
      this.resetGlobalUserInfo(user.userID);
    }
  },
  /**
   * 重置全局存储信息
   */
  resetGlobalUserInfo() {
    let cartUnId = wx.getStorageSync('cartUnId');
    if (!cartUnId) {
      cartUnId = common.commonObj.getUniqueId();
      wx.setStorageSync('cartUnId', cartUnId);
    }
    Object.assign(this.globalData, {
      userToken: '',
      customerID: '',
      cartUnId,
    });
    wx.removeStorageSync('user');
  },
  // 检查广告参数是否过期，过期则清空
  checkAdParams() {
    try {
      const adParams = getStorage('adParams');
      const {
        clickId,
        wxPubOpenId,
        clickIdUpdateTime = 0,
        wxOpenIdUpdateTime = 0,
      } = adParams;
      const curTime = new Date().getTime();
      if (clickId && curTime - clickIdUpdateTime > 604800000) {
        adParams.clickId = '';
        wx.setStorageSync('adParams', adParams);
      }
      if (wxPubOpenId && curTime - wxOpenIdUpdateTime > 604800000) {
        adParams.wxPubOpenId = '';
        adParams.wxPubAppId = '';
        wx.setStorageSync('adParams', adParams);
      }
    } catch (err) {
      console.log(err);
    }
  },
  showUpdatePopup() {
    const viewedGuidePage = getStorage('viewedGuidePage')
    if (!viewedGuidePage.hiddeUpdatePopup && !wx.requestSubscribeMessage) {
      wx.showModal({
        title: '提示',
        content:
          '当前微信版本过低，无法使用全部功能，请升级到最新微信版本后重试。',
        showCancel: false,
        success: function (res) {},
      });
      wx.setStorageSync(
        'viewedGuidePage',
        Object.assign(viewedGuidePage, { hiddeUpdatePopup: true })
      );
    }
  },
  event: emitter,
  api: api, // api
  $http: request, // 请求方法
  deleteLocationInfo: function (status = 1) {
    wx.removeStorageSync('userCurrLoca');
    !!status && wx.removeStorageSync('timelyCity');
    wx.removeStorageSync('selectedAddress');
    wx.removeStorageSync('city');
    wx.removeStorageSync('bgxxCurrLocateInfo');
    wx.removeStorageSync('currLocaNearSelfTakeStore');
  },
  // 定位当前位置最近的门店
  async locateStore() {
    try {
      const userCurrLoca = getStorage('userCurrLoca')
      await locateService.checkCityService(userCurrLoca);
    } catch (err) {
      console.log(err);
    }
  },
  getSysInfo: async function () {
    const res = await getAllSystemInfo()
    try {
      const {
        model,
        windowWidth,
        windowHeight,
        SDKVersion,
        screenWidth,
        screenHeight,
        platform,
        system,
        version,
        safeArea,
        statusBarHeight,
      } = res;
      const safeAreaBottom = screenHeight - safeArea.bottom;
      this.globalData.systemInfo = res
      this.globalData.ww = windowWidth;
      this.globalData.hh = windowHeight;
      this.globalData.screenHeight = screenHeight;
      wx.setStorageSync('systemInfo', {
        model,
        windowWidth,
        windowHeight,
        SDKVersion,
        screenWidth,
        screenHeight,
        platform,
        system,
        version,
        safeAreaBottom,
        statusBarHeight,
      });
    } catch (e) {
      console.log(e);
    }
    var isIphoneX = res.model.indexOf('iPhone X') > -1 ? true : false;
    var isIphone = res.model.indexOf('iPhone') > -1 ? true : false;
    this.globalData.isIphoneX = isIphoneX;
    this.globalData.isIphone = isIphone;
    return res
  },
  // 神策初始化
  // 这里存在重复调用init的问题,待优化
  initSensors() {
    // 神策初始化
    // 已登录使用userId
    const user = getStorage('user')
    const userID = user.userID;
    const { unionid = '' } = getStorage('wxSnsInfo')

    if (unionid) {
      sensors.setOpenid(unionid);
    }
    if (userID && String(userID) !== '-1') {
      sensors.login(userID);
      sensors.init();
      common.commonObj.reportSensorsAccessInfo({
        scene: this.globalData.scene,
      });
      const {
        isGroupNewCustomer,
        isEshopNewCustomer,
        isWxminiNewCustomer,
        superVipExpireDay,
        isFreshNewCustomer
      } = user;
      sensors.setProfile({
        userID, // userID作为用户属性上报
        remainingDates: superVipExpireDay || '无', // 心享用户剩余日期
        groupNewUser: isGroupNewCustomer === 1 ? '是' : '否', // 集团新客
        miniNewUser: !!isWxminiNewCustomer ? '是' : '否', // 小程序新用户
        onlineNewUser: !!isEshopNewCustomer ? '是' : '否', // 电商新用户
        NextDeliveryNewUser: isFreshNewCustomer ? '是' : '否' // 次日达新客
      });
    } else if (unionid) {
      // sensors.setOpenid(unionid);
      sensors.init();
      // 匿名id，登录时传给后端，做同步使用
      // this.globalData.anonymousID = sensors.quick('getAnonymousID');
      this.globalData.anonymousID = unionid;
      common.commonObj.reportSensorsAccessInfo({
        scene: this.globalData.scene,
      });
    } else {
      sensors.init();
    }
  },
  // 上报神策冷热启动数据
  async reportSensorsData(shareObj, path, type, query = {}) {
    await startReportPointPromise.promise
    try {
      const params = shareObj ? JSON.parse(shareObj) : {}
      const unionId = getStorage('wxSnsInfo', { unionid: '' }).unionid
      const timelyCity = getStorage('timelyCity', {})
      const {
        location = {},
        cityName,
        address,
        district,
      } = getStorage('userCurrLoca', {})
      const obj = {
        $url_path: path,
        mp_shareID: defaultVal(params.mp_shareID),
        mp_shareTitle: defaultVal(params.mp_shareTitle),
        shareScene: defaultVal(params.shareScene),
        // 综合专题id,或者接龙活动id
        activity_ID: findValue([params.mp_activityID, params.activity_ID, query.activityCode], 0),
        // 综合专题名称,或者接龙活动名称
        activity_Name: findValue([params.activity_Name, params.mp_activityName, query.activityName]),
        groupID: defaultVal(params.mp_groupID, 0),
        openerID: defaultVal(params.mp_openerID),
        unionID: defaultVal(unionId),
        cityID: defaultVal(timelyCity.cityID, 0),
        cityName: defaultVal(timelyCity.cityName),
        lon: defaultVal(location.lng),
        lat: defaultVal(location.lat),
        locationCity: defaultVal(cityName),
        locationDistrict: defaultVal(district),
        locationAddress: defaultVal(address),
        SKU_ID: findValue([params.SKU_ID, params.goodsSn, query.goodsSn]),
        SKU_Name: findValue([params.SKU_Name, params.goodsName, query.goodsName]),
        storeName: findValue([timelyCity.storeName, '']),
        storeNum: findValue([timelyCity.storeCode, '']),
        goods_label: defaultVal(params.goods_label, [])
      };
      /** @type { [boolean, () => any][] } */
      ([
        [
          type === 'appShow',
          () => sensors.para.autoTrack.appShow = function () {
            return obj;
          }
        ],
        [
          true,
          () => {

            // 小程序模板消息卡片上报的参数
            [
              /** 广告系列名称 */
              ['utm_campaign'],
              /** 广告系列媒介 */
              ['utm_medium'],
              /** 广告系列来源 */
              ['utm_source'],
              /** 广告系列字词 */
              ['utm_term'],
              /** 广告系列内容 */
              ['utm_content'],
              /** 策略器名称 */
              ['sf_strategy_name', '$sf_strategy_unit_id'],
            ].forEach(([valueKey, objKey]) => {
              obj[defaultVal(objKey, valueKey)] = defaultVal(query[valueKey])
            })
            sensors.para.autoTrack.appLaunch = function () {
              obj.getLandingPage = getLandingPage(path, query)
              return obj;
            };
          }
        ]
      ]).find(v => v[0])[1]()
    } catch (err) {
      console.log(err);
    }
  },
  // 位置授权全局弹窗
  golbalModalResolve: () => {},
  async golbalModalPromise(globalLocateModalInfo = {}) {
    try {
      const pages = getCurrentPages();
      const curPages = pages[pages.length - 1]
      curPages.setData({
        globalLocateModalInfo
      })
      const modalElement = curPages.selectComponent('#globalModal')
      if (!modalElement) return Promise.resolve()
      return  new Promise(resolve => {
        this.golbalModalResolve = resolve
      })
    } catch(err) {
      console.log('err', err)
      return Promise.resolve()
    }
  },
  /**
   * 上报自定义小程序显示事件
   */
  reportDefinedAppShow(options) {
    const { scene = '', query = '', path = '' } = options;
    const params = {
      url_path: path,
      url_query: toQueryString(query),
      scene,
    };
    try {
      // 微信版本
      const { version = '' } = wx.getStorageSync('systemInfo') || {};
      // 登陆用户信息
      const { userID = '' } = wx.getStorageSync('user') || {};
      Object.assign(params, {
        version,
        userID,
      });
    } catch (error) {}
    sensors.track('MPShow', params);
  },
  // 登录重构-检查登录状态
  checkSignInsStatus() {
    let isLogin = false;
    if (this.globalData.userToken) {
      isLogin = true;
    }
    return isLogin;
  },
  // 登录重构-登录
  signIn(isRefreshLocation = true) {
    if (!this.globalData.userToken) {
      wx.navigateTo({
        url: `/homeDelivery/pages/signIn/index/index?isRefreshLocation=${isRefreshLocation}`,
      });
    }
  },
  // 登录 router 登录完要去的页面名
  toSignIn({ finishToRouter = '', isReplace = false } = {}) {
    // 定义跳转登录页需要关闭之前所有页面的场景
    const routeList = [
      'homeDelivery/pages/confirmOrder/index'
    ]
    const { route = '' } = getCurrentPageSync() || {}
    const url = `/homeDelivery/pages/signIn/index/index?finishToRouter=${finishToRouter}`
    if (routeList.includes(route)) {
      wx.reLaunch({
        url
      })
    } else {
      wx[isReplace ? 'redirectTo' : 'navigateTo']({
        url
      })
    }
  },
  // 未登录提示
  showSignInModal({ onlyCancel = false, finishToRouter } = {}) {
    const that = this;
    common.commonObj.showModal(
      '提示',
      '您还未登录哦，请先登录~',
      true,
      '立即登录',
      '',
      function (res) {
        if (res.confirm) {
          finishToRouter ? that.toSignIn({ finishToRouter }) : that.signIn();
        } else if (res.cancel) {
          if (!onlyCancel) {
            wx.switchTab({
              url: '/pages/homeDelivery/index',
            });
          }
        }
      }
    );
  },
  /**
   *
   * @param {Object} options 参考wx.showModal微信api参数
   * @param {String} useScene fruit及时达使用 vegetables次日达使用（区分对话框按钮风格）
   */
  showModalPromise(options, useScene = 'fruit') {
    return new Promise((resolve) => {
      wx.showModal({
        title: options.title || '提示',
        content: options.content,
        showCancel: options.showCancel || false,
        confirmText: options.confirmText || '我知道了',
        cancelText: options.cancelText || '随便看看',
        cancelColor: '#999999',
        confirmColor: themeColorConfig[useScene],
        success(res) {
          if (res.confirm) {
            resolve(true);
          } else {
            resolve(false);
          }
        },
        fail() {
          resolve(false);
        },
      });
    });
  },
  /**
   * 退出登录
   * 一般情况下被调用存在两种情况：1.被踢(主要) 2.拒绝隐私协议
   * @param {Object} param 参数
   * @param {Boolean} param.isRequestLogout 是否调登出接口，为true下次进来不会重新登录
   * @param {import('./source/const/globalConst').IOutLoginType[keyof import('./source/const/globalConst').IOutLoginType]} [param.logoutCause] 登出原因，默认是被踢下线，如果是拒绝隐私则会单独传参
   */
  signOut({ isDelWxSnsInfo = true, isRequestLogout = false, logoutCause = '' } = {}) {
    // 后端登出
    const customerID = this.globalData.customerID
    const { openid } = wx.getStorageSync('wxSnsInfo') || {}
    if (isRequestLogout) {
      userLogout({ customerID, openid, logoutCause })
    } else if (logoutCause) {
      // 如果是被踢 并且 上报过被踢
      if (logoutCause === outLoginType.deviceChangeLogout && isDeviceChangeLoginOuted) {
        return
      }
      // 如果是被踢
      if (logoutCause === outLoginType.deviceChangeLogout) {
        setDeviceChangeLoginOuted(true)
      }
      // 上报退登埋点
      sensors.track(
        'logout', {
          logoutCause
        }
      )
    }
    this.resetGlobalUserInfo();
    this.globalData.cartCount = 0;
    // 退出登录，清空购物车角标
    tabbarStore.removeAllTabBarBadge()
    this.globalData.cartCountOnSale = 0;
    this.globalData.totalPriceOnSale = 0;
    this.globalData.cartGoods = [];
    this.globalData.isShowNewCouponDialog = false
    fruitCartStore.setFruitGoodsCount({ goodsList: [] })
    this.globalData.optionStore = {};
    wx.removeStorageSync('user');
    wx.removeStorageSync('token');
    // 老用户数据兼容
    // if (isDelWxSnsInfo) {
    //   wx.removeStorageSync('wxSnsInfo');
    // }
    wx.removeStorageSync('selectedAddress');
    wx.removeStorageSync('cartGoods');
    wx.removeStorageSync('BLESS_CARD_INFO')
    wx.removeStorageSync('userAuth');
    // 退出登录删除缓存的领取初始化权益标识
    wx.removeStorageSync('receiveFirstMark')
    wx.removeStorageSync('userNameAndImg'); // 退出登录删除头像昵称
    this.globalData.superVipStatus = 'C';
    // 重新定位
    this.deleteLocationInfo();
    // locateService.requestLocation();
    this.event.emit('refreshPageGoods');
    this.event.emit('refreshNewUserInfo');
    this.event.emit('refreshFreshNewUserInfo');

    userStore.updateIsFruitFans(false)
  },
  // 判断是否是心享会员
  async isSuperVip({ needSetData = true, catchLogout = false } = {}) {
    try {
      const res = await this.api.bgxxGetCustomerInfo({
        data: { customerID: this.globalData.customerID },
        options: { isReturnLoginOutError: catchLogout }
      });
      if (!res.data) {
        return;
      }
      const {
        superVipStatus,
      } = res.data;
      Object.assign(this.globalData, {
        superVipStatus,
      });
      let user = wx.getStorageSync('user');
      user.superVipStatus = superVipStatus;
      wx.setStorageSync('user', user);
      if (!needSetData) { return }
      let pages = getCurrentPages();
      if (pages.length > 0) {
        pages[pages.length - 1].setData({
          superVipStatus: superVipStatus,
        });
        if (
          pages[pages.length - 1].superVipStatusChange &&
          typeof pages[pages.length - 1].superVipStatusChange === 'function'
        ) {
          pages[pages.length - 1].superVipStatusChange();
        }
      }
    } catch (error) {
      if (catchLogout) { return error }
    }
  },
  // 2.2 计算购物车数量
  getCartCount() {
    const { cityCode, storeCode, deliveryCenterCode } =
      wx.getStorageSync('timelyCity') || {};
    const { userID } = wx.getStorageSync('user') || {};
    const params = {
      customerID: userID || -1,
      cityCode,
      storeCode,
      deliveryCenterCode,
      cartUuId: this.getOperateCartUnId(),
    };
    return this.api
      .queryCart(params)
      .then(({ data }) => {
        const { cartCount = 0, onSaleGoodsList = [], b2cGoodsList = [] } = data;
        this.globalData.cartCount = cartCount;
        const list = onSaleGoodsList.concat(b2cGoodsList);
        this.globalData.cartGoods = list
        fruitCartStore.setFruitGoodsCount({ goodsList: list })
        fruitCartStore.setActPriceNewConutByList(onSaleGoodsList)
        setCustomTabbarCartCount(cartCount);
        return data;
      })
      .catch((err) => {});
  },
  // 2.2 同步购物车
  async syncCart() {
    try {
      const { cityCode, storeCode, deliveryCenterCode } =
        wx.getStorageSync('timelyCity') || {};
      const { userID } = wx.getStorageSync('user') || {};
      const params = {
        customerID: userID || -1,
        cityCode,
        storeCode,
        deliveryCenterCode,
        cartUuId: this.getOperateCartUnId(1),
      };
      let res = (await this.api.syncCart(params)) || {};
      let {
        cartCount = 0,
        onSaleGoodsList = [],
        b2cGoodsList = [],
        countTip = '',
      } = res.data || {};
      this.globalData.cartCount = cartCount;
      const list = onSaleGoodsList.concat(b2cGoodsList);
      this.globalData.cartGoods = list
      fruitCartStore.setFruitGoodsCount({ goodsList: list })
      this.globalData.countTip = countTip;
      wx.removeStorageSync('checkObj');
      // 在页面为及时达的情况下设置红点
      setCustomTabbarCartCount(cartCount);
    } catch (err) {}
  },
  async getMemberCodeKey() {
    if (!this.checkSignInsStatus()) {
      return;
    }
    const { userID } = wx.getStorageSync('user') || {};
    try {
      const { data } = await this.api.getMemberCode({
        customerId: userID
      })
      wx.setStorageSync('codeKey', data.key);
      this.getSystemTime(data.systemTime);
    } catch (err) {}
  },
  getSystemTime(systemTime) {
    // 获取系统时间跟当前时间的差值
    if (!systemTime) {
      return;
    }
    let formatTime = this.getFormatTime(systemTime);
    let { year, month, day, hour, minutes, second } = formatTime;
    let date = new Date(year, month, day, hour, minutes, second);
    const timeGap = date.getTime() - Date.now();
    this.globalData.systemTimeGap = timeGap;
  },
  getFormatTime(systemTime) {
    let year = systemTime.substring(0, 4);
    let month = systemTime.substring(4, 6) - 1;
    let day = systemTime.substring(6, 8);
    let hour = systemTime.substring(8, 10);
    let minutes = systemTime.substring(10, 12);
    let second = systemTime.substring(10, 12);
    let formatTime = {
      year: year,
      month: month,
      day: day,
      hour: hour,
      minutes: minutes,
      second: second,
    };
    return formatTime;
  },
  // 封装订阅消息api
  requestSubscribeMessage({ tmplIds = [] }, callback, successCallback = null) {
    if (!wx.requestSubscribeMessage) {
      callback && callback();
      return;
    }
    wx.requestSubscribeMessage({
      tmplIds: tmplIds,
      success(res) {
        console.log('success', res);
        successCallback && successCallback(res);
      },
      fail(err) {
        console.log('err', err);
      },
      complete(e) {
        callback && callback(e);
      },
    });
  },
  navigateBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    }
  },
  /**
   * @param {Object} {"errorCode": int, "description": String:, "url": String}
   * api返回非0弹窗；固定key 的对象
   */
  apiErrorDialog({
    errorCode = -1,
    description = '系统异常，请稍后再试',
  } = {}) {
    const desc =
      errorCode === 30007
        ? '抱歉，该商品已下架，再看看别的商品吧~'
        : description;
    common.commonObj.showModal('提示', desc, false, '我知道了');
  },
  /**
   * 获取操作购物车需要的唯一标示（除登录成功同步购物车外（需要cartUnId），未登录: cartUnId，已登陆: ''）
   * @param {Number} isMixShopCart 是否是登录成功同步购物车操作， 默认否
   */
  getOperateCartUnId(isMixShopCart = 0) {
    if (this.checkSignInsStatus() && !isMixShopCart) {
      return '';
    }
    return this.globalData.cartUnId;
  },
  /**
   * 登录后同步心享购物车
   */
  mixBgxxCart() {
    const { customerID, bgxxCityInfo: { cityID = '', cityCode, deliveryCenterCode, storeID, storeCode } = {} } = this.globalData;
    // let { storeID } = wx.getStorageSync('timelyCity');
    if (!cityID) {
      return;
    }
    const params = {
      customerID,
      cityID,
      storeID: storeID || -1,
      storeCode: storeCode || '',
      cartUuId: this.getOperateCartUnId(1),
      isJoinOneBuy: 'Y', // 是否参与一元购 Y:参与  N：不参与   默认不参与
      cityCode,
      deliveryCenterCode
    };
    this.api.mixBgxxCart(params).then((res)=>{
      let {data:{
        cartCount
        }} = res
          // 在页面为次日达的情况下设置红点
        setCustomTabbarCartCount(cartCount, 2);
    }).catch((res) => {
    });
  },
  /**
   * 获取心享购物车数量
   */
  getBgxxCartCount() {
    let { customerID, bgxxCityInfo: { cityID = '', cityCode, deliveryCenterCode, storeCode } = {} } = this.globalData;
    let { storeID } = wx.getStorageSync('timelyCity');
    if (!cityID) {
      // 由于及时达，次日达定位检查城市服务是分开逻辑，
      // 如果次日达加购时没有城市，则用及时达的城市（主要是避免有些及时达页面出现次日达商品，无法正常加购）
      const { cityID: timelyCityID, cityCode: timelyCityCode, deliveryCenterCode: timelyDeliveryCenterCode } = wx.getStorageSync('timelyCity') || {};
      cityID = timelyCityID || -1;
      cityCode = timelyCityCode;
      deliveryCenterCode = timelyDeliveryCenterCode;
    }
    const params = {
      cartUuId: this.getOperateCartUnId(),
      customerID: customerID || -1,
      cityID: cityID || -1,
      storeID: storeID || -1,
      cityCode,
      deliveryCenterCode,
      onlyReturnCartCount: "Y",
      isJoinOneBuy: "Y",
      storeCode
    }
    return new Promise((resolve, reject) => {
      this.api.getBgxxCartCount(params)
        .then(
          (res) => {
            resolve(res);
          },
          (res) => {
            reject(res);
          }
        );
    });
  },
  /**
   * 切换心享城市信息
   * bgxxCurrLocateInfo 当前门店城市信息
   */
  async changeBgxxCityInfo(bgxxSelectLocateInfo = {}, optinos = {}) {
    if (!bgxxSelectLocateInfo.selectAddressInfo) return
    const cacheCityId = this.globalData.bgxxCityInfo.cityID
    const cacheStoreCode = this.globalData.bgxxCityInfo.storeCode
    const {
      selectAddressInfo: {
        cityCode,
        cityID: currCityID,
        cityName,
        lat,
        lon,
        deliveryCenterCode,
        isDefaultLocation
      },
      selectStoreInfo: { storeID, storeCode },
    } = bgxxSelectLocateInfo;
    // 切换城市才刷新首页数据
    const isChangeStore = String(cacheStoreCode) !== String(storeCode)
    const isChangeCity = String(cacheCityId) !== String(currCityID)
    if (isChangeCity) {
      new FreshGoods({ storeCode, cityCode }).getCurStoreGoodsMap()
    }
    this.changeBgxxCityChangeStatus(isChangeCity || isChangeStore);
    wx.setStorageSync('bgxxSelectLocateInfo', bgxxSelectLocateInfo);
    this.getBgxxStoreInfo()
    emitter.emit(
      'app.global.bgxx.cityChange',
      (this.globalData.bgxxCityInfo = {
        cityID: currCityID,
        cityName,
        lat,
        lon,
        storeID,
        storeCode,
        cityCode,
        deliveryCenterCode,
        isDefaultLocation
      })
    );
    // 更改是否取默认城市
    locateStores.changeUseFreshDefault({ useDefault: isDefaultLocation })
    this.loadShopCart()
  },
  /**
   * 切换心享城市改变状态
   * @param {Boolean} flag 更改为true，则首页信息会更新
   */
  changeBgxxCityChangeStatus(flag) {
    this.globalData.mallCityChange = flag;
  },
  /**
   * 切换修改及时达城市信息
   * @param {Object} timelyCity 当前地址信息
   * @param {Object} {
   *  isFreshPageGoods: false/true, 是否需要更新页面商品信息
   * }
   */
  changeTimelyCityInfo(timelyCity, { useDefaultAddress = false, isFreshPageGoods = true } = {}) {
    console.log('timelyCity', timelyCity)
    const { storeInfo, supportSuperVipShop = 'Y', cityCode, storeCode, deliveryCenterCode } = timelyCity || {};
    if (storeInfo) {

      const { id, storeID, shortName, storeName, cityID , storeCode } = storeInfo;
      new FruitGoods({ storeCode }).getCurStoreGoodsMap()
      // 查询特价活动
      getPriceActivityGoods({ storeCode, deliveryCenterCode })
      storeInfo.storeID = id || storeID;
      storeInfo.storeName = shortName || storeName;
      timelyCity.storeInfo = storeInfo;
      if (cityID) {
        timelyCity.cityID = cityID;
      }
      setTimeout(() => {
        reporLocaionStore(storeCode)
      },3000)
    }
    wx.setStorageSync('timelyCity', timelyCity);
    if (isFreshPageGoods) {
      this.event.emit('refreshPageGoods');
    }
    // 更新地址信息，根据城市是否支持次日达服务，修改tabbar
    // tabbarStore.setTabBarList({ hasxmdxEntry: supportSuperVipShop === 'Y' });
    // const { selectAddressInfo } = wx.getStorageSync('bgxxSelectLocateInfo') || {};
    // const { supportSuperVipShop: bgxxCityIsSupportSuperVipShop } = selectAddressInfo || {}
    // tabbarStore.setTabBarList({ hasxmdxEntry: bgxxCityIsSupportSuperVipShop === 'Y' || supportSuperVipShop === 'Y' });
    setTimeout(() => {
      this.getCartCount()
    },200)
    // useDefaultAddress为false的情况下更新了地址信息
    // 视为已经定位过了
    this.globalData.fruitFirstLocate = useDefaultAddress
    locateStores.changeUseDefault({ useDefault: useDefaultAddress })
    userStore.updateFansGray(cityCode, storeCode)
  },
  getOptionStore(options) {
    if (!this.checkNeedOptionStore(options)) { return }
    const scene = this.globalData.scene || -1;
    const optionStore = {
      storeID: '',
      storeName: '',
      cityID: '',
      takeawayAttr: '',
    };
    try {
      const query = (checkTimelyGoodsDetailScene(options)
        ? parseTimelyGoodsDetailScene(options) // 及时达分享海报小程序码/二维码跳转及时达品类
        : (scene === 1036
            ? JSON.parse(options.query.homeDeliveryObj)
            : options.query)) || {}
      const {
        storeID = '',
        storeName = '',
        cityID = '',
        takeawayAttr = '',
        sckStoreCode,
        source
      } = query;
      Object.assign(optionStore, {
        storeID,
        storeName,
        cityID,
        takeawayAttr
      }, source === 'sqsck' && sckStoreCode ? { shareStoreCode: sckStoreCode, source: 'sqsck' } : {});
    } catch (error) {}
    this.globalData.optionStore = optionStore;
    console.log('获取分享门店信息', optionStore);
    // 清除定位信息
    this.deleteLocationInfo(0);
  },
  checkNeedOptionStore(options) {
    const scene = this.globalData.scene || -1;
    if (!options || !options.query) return false;
    const { source, sckStoreCode  } = options.query
    if (source === 'sqsck' && sckStoreCode) {
      return true
    }
    // App分享卡片打开
    let appShareStoreID = '';
    if (scene === 1036) {
      try {
        const { storeID = '' } = JSON.parse(
          options.query.homeDeliveryObj || '{}'
        );
        appShareStoreID = storeID;
      } catch (e) {}
    }
    const { storeID, chan_refer_app_id } = options.query
    const needOptionScenes = [1007, 1008, 1155, 1154, 1044, 1036];
    return (
      (storeID || appShareStoreID) && needOptionScenes.includes(scene)
    ) ||
    (
      storeID && chan_refer_app_id === 'wx9d4f5f62ea059f08'
    ) ||
    checkTimelyGoodsDetailScene(options)
  },
  /**
   * 查询用户标签身份
   */
  async getNewUserLabels() {
    if (
      !this.globalData.customerID ||
      String(this.globalData.customerID) === '-1'
    ) {
      return;
    }
    try {
      const res = await this.api.getNewUserLabels({
        customerID: this.globalData.customerID,
        userLabels: ['eshopNew', 'appNew', 'wxminiNew'],
      });
      const { eshopNew, appNew, wxminiNew, freshNew } = res.data || {};
      const user = wx.getStorageSync('user') || {};
      Object.assign(user, {
        isWxminiNewCustomer: !!wxminiNew, // 百果园+新客
        isEshopNewCustomer: !!eshopNew, // 电商新客
        isAppNewCustomer: !!appNew, // app新客
        isFreshNewCustomer: !!freshNew // 次日达新客
      });
      wx.setStorageSync('user', user);
    } catch (error) {}
  },
  // 刷新用户电商新客身份
  async refreshUserLabel() {
    const { isEshopNewCustomer } = wx.getStorageSync('user') || {}
    if (isEshopNewCustomer) {
      await this.getNewUserLabels()
    }
  },
  /**
   * 判断新客取消订单
   * @param {*} orderNo 
   */
  judgeNewUserOrderCancel(orderNo) {
    try {
      //  用户取消了
      if (orderNo === wx.getStorageSync('newUserOrderNo')) {
        //  订单取消后，超过5秒后再拉取新的用户标签
        setTimeout(async () => {
          console.info('super', '用户已取消首单订单，开始查询用户标签')
          await this.getNewUserLabels()
          console.info('super', '用户已取消首单订单，新客身份标签是', wx.getStorageSync('user')?.isEshopNewCustomer)
          wx.removeStorageSync('newUserOrderNo')
        }, 5000)
      }
    } catch (error) {
      console.info('judgeNewUserOrderCancel.error', error)
    }
  },
  /**
   * 记录新客首单订单编号
   * @param {*} orderNo 
   */
  async remarkNewUserOrderNo (orderNo) {
    try {
      const { isEshopNewCustomer } = wx.getStorageSync('user') || {}
      if (isEshopNewCustomer && orderNo) {
        console.info('super', '用户为新客，记录订单编号', orderNo)
        wx.setStorageSync('newUserOrderNo', orderNo)
        this.refreshUserLabel()
      }
    } catch (error) {
      console.info('remarkNewUserOrderNo.error', error)
    }
  },
  // 获取电商阿波罗系统配置
  async getEshopSysConfig() {
    try {
      const res = await this.api.getEshopSysConfig();
      const { shareActivityPic, shareActivityFloatingPic } =
        res.data?.systemConfig || {};
      wx.setStorageSync('eshopSysConfig', {
        shareActivityPic, // 红包背景图（支付成功页）
        shareActivityFloatingPic, // 侧边红包悬浮图（支付成功页，订单详情页）
      });
    } catch (e) {}
  },
  /**
   * 非正式环境监听内存
   */
  onMemoryWarning() {
    if (noPrd && !!wx.onMemoryWarning) {
      wx.onMemoryWarning(function (level) {
        console.log('onMemoryWarningReceive', level);
        wx.showToast({
          title: `内存不足警告 ${String(level) || ''}`,
        });
      });
    }
  },
  /**
   * 设置app启动时的场景值（扫二合一码进入首页场景使用）
   * @param {Object} options
   */
  setLanuchScene(options = {}) {
    const { scene, path } = options
    Object.assign(this.globalData, {
      sceneCopy: scene,
      launchPath: path
    })
  },
  /**
   * 获取微信业务配置
   */
  async getWxConfig() {
    let result = await this.api.getWxConfig();
    const { payType = 0, isGrayTheme = false } = result.data;

    this.globalData.reqPayType = payType;
    this.globalData.isGrayTheme = isGrayTheme
  },
  /**
   * @description 跳转去客服页面
   */
  toOnlineService(options) {
    const {
      queStr,
      needShopCartGoods = false
    } = options || {}
    if (!this.checkSignInsStatus()) {
      this.signIn()
      return
    }
    const { userID } = wx.getStorageSync('user')
    // http://kfywtest.pagoda.com.cn
    // https://kfyw.pagoda.com.cn
    let url = `https://kfyw.pagoda.com.cn/webchatbot/h5chat_pagoda.html?sysNum=1638265220559&sourceId=111&lang=zh_CN&memberId=${userID}`
    if (queStr) url += `&queStr=${queStr}`
    if (needShopCartGoods) {
      const {
        cityCode: fruitCityCode = '',
        storeCode: fruitStoreCode = ''
      } = wx.getStorageSync('timelyCity') || {}
      const {
        selectStoreInfo
      } = wx.getStorageSync('bgxxSelectLocateInfo') || {}
      const {
        cityID: freshCityID = -1,
        storeID: freshStoreID = -1
      } = selectStoreInfo || {}

      let timelyGoods = []
      let b2cGoods = []
      const goodsList = this.globalData.cartGoods
      goodsList.forEach(item => {
        item.takeawayAttr === "及时达" ? timelyGoods.push(item.goodsSn) : b2cGoods.push(item.goodsSn)
      })
      timelyGoods = Array.from(new Set(timelyGoods)).join(',')
      b2cGoods = Array.from(new Set(b2cGoods)).join(',')

      url += `&fruitCityCode=${fruitCityCode}&fruitStoreCode=${fruitStoreCode}&freshCityID=${freshCityID}&freshStoreID=${freshStoreID}&timelyGoods=${timelyGoods}&b2cGoods=${b2cGoods}`
    }
    wx.navigateTo({
      url: `/h5/pages/commonLink/index?pageUrl=${encodeURIComponent(url)}`
    })
  },
  /**
   * 更新购物车数据
   * @param {*} list
   */
  updateFreshGoodsCartList(list){
    bgxxStores.updateFreshGoodsCartList(list)
  },
  /**
   * 更新删除的商品
   * @desc count: 需要删除的数量,id||goodsID做唯一标识,activityInfo用来区分删除换购品还是删除正常品
   * @param {{ count: number; id: number; goodsID: number; activityInfo: any; }[]} detailArr
   */
  updateDelGoods(detailArr){
    bgxxStores.updateDelGoods(detailArr)
  },
  /**
   * 获取门店信息
   * @param {*} goodsIds
   */
  getBgxxStoreInfo(){
    let store = wx.getStorageSync("bgxxSelectLocateInfo") || {}
    bgxxStores.getBgxxStoreInfo(store)
  },
  /**
   * 保存存储方式
   * @param {*} storageTypeObj
   */
  async saveGoodsStorageType({goodsSnList = [],goodsIdList = []}){
      try {
        if (!goodsIdList.length && !goodsSnList.length) {
          return {}
        }
        const goodsSnOrgoodsId = goodsSnList.length ? {goodsSn:goodsSnList}:{goodsId:goodsIdList}
        const params = {
            ...goodsSnOrgoodsId,
          organizationCode:this.globalData.bgxxCityInfo.cityCode
        }
        const {data} = await this.api.getBgxxGoodsStorageType(params)
        bgxxStores.saveGoodsStorageType(data)
        return data
      } catch {
        return {}
      }
  },
  /**
   * 请求接口获取购物车数据,登录使用userid 未登录使用随机生成id
   */
  async loadShopCart() {
    const selectStoreInfo = this.globalData.bgxxCityInfo
    if(!selectStoreInfo.cityCode) return {};
    const {
      cityID, storeID, cityCode, deliveryCenterCode, storeCode
    } =  selectStoreInfo;
    const {customerID} = this.globalData
    const params = {
      cartUuId: this.getOperateCartUnId(), isJoinOneBuy: 'Y', customerID: customerID || -1, cityID: cityID || -1, storeID: storeID || -1, cityCode, deliveryCenterCode, storeCode
    }
    try {
      let res = await this.$http.post({ url: goodsCartApi.getPFCartApi, data: params, isLoading: false })
      let {data = {}} = res
      freshCartStore.resultHandler({ data })
      this.updateFreshGoodsCartList(data)
      bgxxStores.updateCartCount(data.cartCount)

      setCartCount.setCount(data.cartCount)
      return data
    } catch (err) {
      // this.apiErrorDialog(err)
      return {}
    }
  },

  /**
   * 更新商品快照缓存文件的信息
   */
  updateLruCacheStorgae () {
    const lruCacheSequence = wxappLruCache.getLruCacheSequence()
    lruCacheSequence && wx.setStorageSync('lruCacheSequence', lruCacheSequence)
  },

  globalData: {
    userInfo: null,
    activeStatus: 'A', // A領卡成功,B未激活和激活成功
    card_id: '', //开卡组件返回的card_id
    code: '', //开卡组件返回的code
    activate_ticket: '', //开卡组件返回的激活票据
    paramObj: '',
    noCityStorage: false, // 判断首页是否有定位城市门店缓存
    isnewCustomer: 0, // 判断是否是双新用户 0-老用户 1-新用户 2-其他情况
    formWxPublicToOrderList: false,
    scene: 1001,
    reportSensors: reportSensors,
    isScanCode: false,
    hasFirst: true,
    userToken: '',
    needInitSensors: true,
    superVipStatus: 'C', // 百享会员状态（C:普通，T：试用，F: 百享会员）
    locationInfo: {},
    cartCount: 0, // 购车数量
    cartCountOnSale: 0, // 购车数量(只取及时达)
    totalPriceOnSale: 0, // 购物车商品总价(只取及时达)
    cartGoods: [], // 购物车商品
    countTip: '', // 购物车数量变化弹窗
    userLocationAuth: locateStores.userLocationAuth, // 位置权限是否开启
    cityCanService: false, // 城市是否有服务
    hasStoreService: false, // 是否有门店服务
    adChannelParams: {}, // 广告渠道参数
    customerID: '', // 用户id
    bgxxCityInfo: {}, // 心享当前使用城市信息
    isShowSignoutModal: true,
    payOrderID: '', // 心享商城支付订单id
    prePageName: '', // 记录上一页面名称,供跳转tab时使用
    bgxxOptions: {},
    anonymousID: '', // 匿名id
    txSensorData: null,
    mchId: '1237402102',
    bgxxCartGiftTipIsNeed: true, // 心享购物车是否需要提示送葱活动
    bgxxHideCartonSaleIdList: [], // 心享离开购物车时的现售商品id列表
    curPageCartCoordForWxs: {}, // 当前页面购物车坐标
    referrerInfo: null, // 接受onSHow中的referrerInfo参数
    systemTimeGap: 0, //会员码获取服务端时间跟用户系统时间的差值
    cartGoodsTipList: [], // 记录购物车tip提示
    optionStore: {}, //记录分享门店的门店信息
    fruitFirstLocate: true, // 及时达是否首次定位
    freshFirstEntry: true, // 次日达首次进入
    isHandlebgxxCacheStore: true, // 次日达是否处理缓存门店逻辑，只有在小程序冷启动第一次打开次日达页面的时候才会
    isGetBgxxCacheLocation: true, // 是否取缓存地址
    pagodaLogo: `${prdUrl.PAGODA_PIC_DOMAIN}/group1/M21/4B/D7/CmiWa2DDIH2Ae73-AAAoXDnDenc906.png`, // 百果园logo
    picDomain: baseUrl.PAGODA_PIC_DOMAIN,
    screenHeight: 0, //屏幕的可视高度
    reqPayType: 0, // 获取支付配置 默认钱包支付
    launchUserLoginStatus: 'noLogin', // app启动时用户登录状态，默认未登录 （用来区分启动app时是踢出登录还是本身未登录状态）
    launchOptions: {}, // app.js中onLaunch/onShow时缓存的参数
    globalLaunchOptions: {}, // 进程中参数不变
    storageTypeImg:{
        "F":"https://resource.pagoda.com.cn/group1/M21/8E/B9/CmiWa2J4iJGAUfL8AAAmCVDQxHI898.png",
        "R":"https://resource.pagoda.com.cn/group1/M21/8F/1A/CmiLkGJ4iIuAW5XZAAAiDTh-K8w482.png",
        "N":""
		},
    isStartReportPoint: isStartReportPoint,
    lastLocateAuth: true, // 记录app onHide时候位置权限
    //  用户授权信息
    userAuth,
    hasShowChangeBgxxCityModal: false, // 次日达冷启动进入时，如果发现实际定位城市与缓存定位城市不同，需要展示切换弹窗。生命周期内之内只展示一次
    systemInfo: {}, // 系统信息
    grayScaleConfig: { // 全局灰度配置，在发出http请求前会赋值。首页使用可能存在延迟
      isGrayScale: false, // 是否灰度
      txChangeToNewServer: 'N' // 非灰度用户是否切换接口
    },
    curLocation: { // 记录当前定位位置
      lat: '',
      lon: ''
    },
    isShowNewCouponDialog: false,// 是否弹过新客弹窗
    blackBox: '', // 同盾backbox
  },
  // 检查是否未灰度用户，用来决定前端功能以及接口是否灰度
  checkIsGray() {
    const { isGrayScale, txChangeToNewServer } = this.globalData.grayScaleConfig
    return isGrayScale || txChangeToNewServer === 'Y'
  },
  onHide: function () {
    wx.removeStorageSync('nativeImagePath');
    // 解绑刷新红点信息事件
    // this.event.off('refreshRedDot');
    // 更新商品快照缓存文件的信息
    this.updateLruCacheStorgae()
    // 记录当前位置权限
    this.globalData.lastLocateAuth = locateStores.hasLocationAuth
    monitor.onAppHide()
  },
  onUnload: function () {},
  /**
   * @description 获取同盾blackbox，用于获取设备id
   */
  async getBlackbox() {
    const that = this
    if (that.globalData.blackBox) {
      return that.globalData.blackBox
    }
    return new Promise( resolve => {
      const tdfpPlugin = require.async('./sourceSubPackage/report/tdfpPlugin.js')
      tdfpPlugin.then( plugin => {
        const fmagent = new plugin.default.FMAgent(TDFP_OPT)
        fmagent.init()
        const { 
          openid,
          unionid
        } = wx.getStorageSync('wxSnsInfo') || {}
        // if (!openid) {
        //   return
        // }
        // return new Promise( resolve => {
        fmagent.getInfo({
          page: that, // 请传入 FMAgent 所在的 Page 或 Component 对象
          mode:'plugin',
          openid: openid || '', // 如果 openid 或者 unionid 为空或者 undefined，请勿加密上传，传空字符串即可
          // 如果您开通了 unionid 功能，请传入加密的用户 unionid，
          // 请传入加密的用户 openid(需保证加密后的 openid 与原始 openid 是一一对应关系)
          unionid: unionid || '', 
          success: function (res) {
            that.globalData.blackBox = res
            resolve(res)
          },// 成功回调，res 为 blackBox 字符串
          fail: function (res) {
            console.log(res)
            resolve('')
          },// 失败回调，res 为各种 exception 对象
          complete: function () {
          } // 完成回调，res为blackbox字符串或者exception 对象
        })
      })
    })
    // })
    .catch(err => {
      console.log(err)
    })
  }
});
/** @desc 目前只是及时达综合专题页和及时达/全国送商品详情页需要检查是否是及时达分佣团长 */
function needCheckDistributor(path) {
  return [
    // 及时达/全国送商品详情分享
    /^\/homeDelivery\/pages\/category\/index/,
    // 及时达综合专题分享
    /^\/pages\/topic\/index.*"activityID":/,
    // 接龙首页或接龙详情分享
    /^\/pages\/homeDelivery\/index.*to=relayDetail/,
  ].some(v => v.test(path))
}
function removeExistParam(url, matchReg) {
  return url.replace(matchReg, function([,match]) {
    return { '?': '?' }[match] || ''
  })
}
function handleShareUrl(path) {
  const app = _getApp()
  const { customerID = '', globalLaunchOptions = {} } = app.globalData;
  const { source, sckStoreCode, isCarryStore = 'Y' } = globalLaunchOptions.query || {};
  const isFromSck = source === 'sqsck' && sckStoreCode; // 来自素材库卡片分享
  const isNoCarryStore = isCarryStore === 'N'
  const url = removeExistParam(
    isNoCarryStore
      // 无门店分享场景,移除url中的门店
      ? removeExistParam(path, /([&?])storeID=\d+/g)
      : path,
    // 删除之前的distributor参数
    /([&?])distributor=\d+/g
  );
  const shareParams = []
  const urlParamsArr = []
  if (customerID && !isNoCarryStore) {
    const { nickName = '' } = wx.getStorageSync('userNameAndImg') || {};
    const isStoreLeader = YN2Boolean(wx.getStorageSync('isStoreLeader'));
    // 次日达推荐官
    isStoreLeader && shareParams.push(...[
      `parentCustomerID=${customerID}`,
      nickName ? `nickName=${nickName}` : '',
    ])
    // 及时达分佣团长
    needCheckDistributor(path) &&
    isDistributorFromCache() &&
    shareParams.push(`distributor=${customerID}`)
  }
  // 不携带门店
  isNoCarryStore && urlParamsArr.push('utm_source=GroupRobot')
  // 素材库
  isFromSck && urlParamsArr.push(...['utm_source=sucaiku', `sckStoreCode=${sckStoreCode}`])
  const urlHasParams = url.includes('?')
  const shareParamsStr = shareParams.filter(Boolean).join('&')
  const urlParmsSplit = url.split('?').filter(Boolean)
  // 将分佣参数插入到url最前面,避免被神策上报截断
  shareParamsStr && urlHasParams
    ? urlParmsSplit.splice(1, 0, shareParamsStr)
    : urlParmsSplit.push(shareParamsStr)
  return [
    shareParamsStr
      ? [urlParmsSplit[0], urlParmsSplit.slice(1).join('&')].join('?')
      : url,
    // 在有shareParamsStr的情况下,参数已经被拼接到url后面了
    // 所以需要在url后面加上的是`&`
    urlHasParams || shareParamsStr ? '&' : '?',
    urlParamsArr.filter(Boolean).join('&')
  ].join('')
}
function handleShareMessage(originalShare) {
  return function (...args) {
    const { title, path, imageUrl, promise } = originalShare.apply(this, args)
    const promiseAfterCheck = needCheckDistributor(path) ? Promise.all([
      promise || Promise.resolve({
        title,
        path,
        imageUrl,
      }),
      // 有5分钟一次缓存的情况下,检查是否为团长
      checkUserIsDistributor(),
    ]).then(([shareResult]) => Object.assign(
      {},
      shareResult,
      { path: handleShareUrl(shareResult.path) }
    )) : promise
    return {
      title,
      path: handleShareUrl(path),
      imageUrl,
      promise: promiseAfterCheck,
    };
  };
}

// Optimize the Page function
const originalPage = Page;
Page = function (e) {
  const app = _getApp();
  if (e.onShareAppMessage) {
    e.onShareAppMessage = handleShareMessage(e.onShareAppMessage);
  }
  watchRouteChange(e)
  return originalPage(app.sr.page(e));
};

// Optimize the Component function
const originalComponent = Component;
Component = function (e) {
  if (e.methods && e.methods.onShareAppMessage) {
    e.methods.onShareAppMessage = handleShareMessage(e.methods.onShareAppMessage);
  }
  return originalComponent(e);
};
