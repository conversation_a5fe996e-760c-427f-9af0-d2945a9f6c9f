<view class="detail_container">
    <view class="navigation">

        <map id="map" longitude="{{markers[0].longitude}}" latitude="{{markers[0].latitude}}" scale="16" markers="{{markers}}" bindcallouttap="markertap">
            <!-- <cover-view class="play" bindtap="play">
                <view class="guide_store">百果园深圳滨海之窗店</view><view class="guide_button">导航</view>
            </cover-view> -->
            <cover-view slot="callout">
                <cover-view marker-id="1">
                    <cover-view class="flex callout-container">
                       <cover-view class="flex callout_float-box">
                                                                                            <!-- 此处使用了全交空格用于解决部分机型ｃｏｖｅｒ－ｖｉｅｗ文字隐藏问题，勿动 -->
                            <cover-view class="flex-c-c" style="line-height: 76rpx;">{{storeName}}　</cover-view><cover-view class="navigator_text-box flex-c-c" style="line-height: 76rpx;">导航</cover-view>
                            </cover-view>
                            <cover-view class="callout_float-dot"></cover-view>
                    </cover-view>
                </cover-view>
            </cover-view>
        </map>
    </view>
    <view class="store_list">
        <view class="store_info">
            <view class="store_name">{{storeName}}
                <text class="distance" wx:if="{{distance && distance != '0'}}">{{distance}}</text>
            </view>
            <view class="box-l">
                <view class="goods_info mari-r" wx-if="{{pageFromPath !== 'vegetables' && !fromTryEatStore && salesCount }}">在售商品:  {{salesCount}}个</view>
                <view class="goods_info">评分：{{scoreNumber ? scoreNumber : '-' }}</view>
            </view>
            <view class="goods_info" wx-if="{{ fromTryEatStore && hasActCode }}">试吃名额仅剩:  {{salesCount}}个</view>
            <view class="store_address">{{address}}</view>
            <view class="store_time">门店营业时间: {{storeBusinessTime}}</view>
        </view>
        <view class="store_detail">
          <view  bindtap="callStore">
            <image class="image-icon" src="../../source/images/phoneIcon.png"></image>
            <view class="store_word" bindtap="dayin">联系门店</view>
          </view>
          <view  bindtap="collectStore" wx:if="{{pageFromPath !== 'vegetables'}}">
            <image wx:if="{{isUserCollection === 'Y'}}" class='image-icon' src='/source/images/selected-star.png'></image>
            <image wx:else class='image-icon' src='https://resource.pagoda.com.cn/dsxcx/images/074b835ada1ff57c1ed6d7990d3a7826'></image>
            <view class="store_word">收藏门店</view>
          </view>
        </view>
    </view>
    <view class="qualification" catchtap='toStoreCredentials'>
        <view class="store_name">门店资质</view>
        <image class="arrow" src="../../source/images/<EMAIL>"></image>
    </view>
</view>

<common-loading />