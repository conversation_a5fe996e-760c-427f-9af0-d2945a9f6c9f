<import src="/pages/template/bgxxOrder.wxml"></import>
<import src="/pages/template/bgxxDialog.wxml"></import>
<import src="/pages/template/index.wxml"></import>
<import src="./template/index.wxml" />
<main-page currentView="content" />

<view class="container">
  <!-- 顶部菜单栏 -->
  <view id="navbar" class="banner">
    <view wx:for="{{tabList}}"
          wx:key="type"
          class="{{item.type == orderClass ? 'tab on' : 'tab'}}"
          data-status="{{item.type}}"
          data-sensorsKey="{{item.sensorsKey}}"
          data-index="{{index}}"
          data-isclicktab="{{ true }}"
          bindtap="bindOrderTap">{{item.label}}
    </view>
  </view>

  <!-- 订单商品 -->
  <view wx:if="{{isLogin}}" class="orderList-box">
    <view class="app-order-box record-box no-store-order">

      <swiper style="min-height: 500px; height: {{ orderListHeight }}px;"
              class="waterfall-swiper"
              current="{{ swiperIndex }}"
              duration="{{ swiperDuration }}"
              bindchange="switchTab"
              >

        <!-- 及时达 -->
        <swiper-item>
          <template is="orderList-A"
                    data="{{ mix_countDown_timeDate, loading, realTimeOrderList, orderHeadIconMap, picUrl, groupBtnClass, paymentDialog, target, lack, pagodaMoney, isIphoneX, mainBalance, rechargeText, selectWxPay, selectUnionPay, disableUnionPay, hideUnionPay, disablePay }}">
          </template>
        </swiper-item>
        <!-- 及时达 -->

        <!-- 次日达 -->
        <swiper-item>
          <view class="order-list">
            <template wx:if="{{ mix_bgxx_orderList.length }}" is="bgxxOrder" data="{{ mix_bgxx_orderList, picUrl, orderHeadIconMap, mix_countDown_timeDate }}">
            </template>
            <template wx:if="{{ !mix_bgxx_orderList.length && !loading }}" is="empty-list"></template>
          </view>
        </swiper-item>
        <!-- 次日达 -->

        <!-- 接龙订单 -->
        <swiper-item>
          <template is="orderList-D" data="{{ loading, chainsOrderList, orderHeadIconMap, picUrl, defaultHeadPic, mix_countDown_timeDate }}"></template>
        </swiper-item>
        <!-- 接龙订单 -->

        <!-- 门店订单 -->
        <swiper-item>
          <template is="orderList-C" data="{{ loading, storeOrderList, headPicList, picUrl, defaultHeadPic, isScan }}"></template>
        </swiper-item>
        <!-- 门店订单 -->

        <!-- 全国送 -->
        <swiper-item>
          <template is="orderList-F" data="{{ mix_countDown_timeDate, loading, b2cOrderList, orderHeadIconMap, picUrl, groupBtnClass, paymentDialog, target, lack, pagodaMoney, isIphoneX, mainBalance, rechargeText, selectWxPay, selectUnionPay, disableUnionPay, hideUnionPay, disablePay }}">
          </template>
        </swiper-item>
        <!-- 全国送 -->
      </swiper>
    </view>
  </view>
  <!-- 门店订单底部添加小票号 -->
  <view wx:if="{{ isLogin && isScan && swiperIndex === 3 }}" class="add-btn safe-area-inset-bottom" bindtap="addOrder">
    <view class="add-btn-icon"></view>
    <text>添加小票号 </text>
  </view>

  <!-- 未登录 -->

  <view wx:if="{{!isLogin}}" class="no-order">
    <default-content
      img="https://resource.pagoda.com.cn/dsxcx/images/fef79f259c199e6b4c4679442bac0bc5.png"
      desc="您还没有登录，请登录后查看订单"
      btnText="立即登录"
      bind:tapBtn="loginRight"
    />
  </view>
  <!-- 未登录 -->

  <order-cancel wx:if="{{orderCancelIsShow}}" bind:select="onOrderCancelSelected" bind:close="onOrderCancelClosed" orderSource="{{orderSource}}" orderID="{{orderInfo.goodsOrderID}}"></order-cancel>
</view>

<!-- 分享给朋友的canvas部分提取到template -->
<!-- <template is="sharePicCanvas"></template> -->
<!-- 此处使用旧api -->
<canvas canvas-id="sharePicCanvas" class="sharePicCanvas" style="width: 992px; height: 794px;"></canvas>
<!-- 使用旧api时添加蒙版 -->
<template is="mask" data="{{prevent}}"></template>

<template is="bgxxPay" wx:if="{{payPopFlag}}" data="{{orderInfo, payType}}"></template>
<template is="bgxxCancelOrder" wx:if="{{tipPopFlag}}" data="{{status}}"></template>
<template is="bgxxReachConfirm" wx:if="{{tipConfirmFlag}}"></template>
<template is="bgxxPickConfirm" wx:if="{{pickedUpFlag}}"></template>
<template is="showCancelTips" wx:if="{{showCancelTips}}"></template>

<confirm-modal titleText="{{modalContent.title}}" contentText="{{modalContent.text}}" isShowConfirmModal="{{ showCustomConfirmModal }}" showCustomBtn="{{true}}">
  <button size="mini" plain="true" slot="leftBtn" class="confirm-btn" bindtap="customConfirmModalCancel" hidden="{{!showCustomConfirmModal}}">取消</button>
  <button size="mini" plain="true" slot="rightBtn" class="confirm-btn" bindtap="handleOnlineService">在线客服</button>
</confirm-modal>

<user-error-modal isShow="{{showUserErrorModal}}"></user-error-modal>

<!-- 验证码弹窗组件 -->
<pagoda-popup
  model:visible="{{visibleSMS}}"
  showLeftArrow="{{true}}"
  showClose="{{false}}"
  round="{{true}}"
  z-index="{{2000}}"
  clickOverlayClose="{{false}}"
  position="bottom"
  title="更换支付方式"
  head-class="sms-head-class"
  title-class="sms-title-class"
  height="600rpx"
  bind:onBack="onBack">
    <sms-validate
      model:visible="{{visibleSMS}}"
      bind:validated="validated"
    />
</pagoda-popup>

<!-- 支付方式选择框 -->
<paymentDialog
  paymentDialog="{{paymentDialog}}"
  target="{{target}}"
  selectPagodaPay="{{pagodaMoney}}"
  selectWxPay="{{selectWxPay}}"
  selectUnionPay="{{selectUnionPay}}"
  disableUnionPay="{{disableUnionPay}}"
  hideUnionPay="{{hideUnionPay}}"
  mainBalance="{{mainBalance}}"
  rechargeText="{{rechargeText}}"
  mainBalanceIsNotEnough="{{lack}}"
  forbidPdPay="{{forbidPdPay}}"
  rechargePayInfo="{{rechargePayInfo}}"
  bind:hidePaymentWay="hidePaymentWay"
  bind:setWaySelect="setWaySelectHandle"
  bind:switchPagodaPayChange="switchPagodaPayChangeHandle"
  bind:payrightNow="payrightNow">
</paymentDialog>

<!-- 全国送未全部发货状态下确认收货弹窗 -->
<confirm-modal
  contentText="{{ b2cConfirmModalText }}"
  isShowConfirmModal="{{ showB2cConfirmModal }}"
  contentTextColor="#000000a6"
  contentTextSize="24"
  showCustomBtn="{{true}}">
  <view class="confirm-title" slot="title">部分商品尚未发货，您可联系客服<text bindtap="handleCustomerService">400-181-1212</text>了解详情。</view>
  <button size="mini" plain="true" slot="leftBtn" class="confirm-btn" bindtap="closeB2cConfirmModel">我知道了</button>
  <button size="mini" plain="true" slot="rightBtn" class="confirm-btn b2c-btn-confirm" bindtap="handleB2cConfirm">确认收货</button>
</confirm-modal>

<!-- 全国送待收货时点击三无退按钮触发弹窗 -->
<confirm-modal
  contentTextAlign="center"
  showCancel="{{false}}"
  clickMaskToClose="{{false}}"
  confirmText="我知道了"
  isShowConfirmModal="{{ showSanwuTip }}"
  bindconfirm="toggleSanwuTip">
  <view slot="content">
    <text>请先确认收货后才能进行三无退货哦</text>
  </view>
</confirm-modal>

<common-loading />
<captcha id="comp-captcha"/>
