<view class="wrap" style="{{ customHeight || (!withPlaceholderHeight) ? '' : 'height:'+ navBarHeight + 'px;' }} display: {{ display }}">
  <view
    class="nav-bar"
    style="position:{{position}};padding-top: {{ statusBarHeight }}px;transition: background-color {{bgColorDuration}}s; background: {{ background }}">
    <view class="title-box" style="{{ customHeight ? '' : 'height:' + titleHeight + 'px;' }} padding-right:{{paddingRight}}px;">
      <view wx:if="{{ isShowBack }}" class="back-box"  bindtap="back">
        <view class="slot-back">
          <slot name="back"></slot>
        </view>
        <image src="/source/images/back-black.png" class="back"></image>
      </view>
      <view class="content-box" style="--padding-left:{{paddingRight}}px;">
        <view class="slot-content">
          <slot name="content"></slot>
        </view>
        <view class="title" style="color: {{ color }}">{{ navBarTitle }}</view>
      </view>
    </view>

    <view>
      <slot name="title-bottom"></slot>
    </view>
  </view>
</view>
