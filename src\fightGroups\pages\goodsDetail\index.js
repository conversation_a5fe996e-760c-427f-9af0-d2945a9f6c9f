var bg_sandclock = require('../../source/image-base64/bg_sandclock');
var fight_group_bg_right = require('../../source/image-base64/fight_group_bg_right')
var bg_detail_priceinfo = require('../../source/image-base64/detail_priceinfo');
var bg_cantuan = require('../../source/image-base64/btn_bg_cantuan').bg;
var commonObj = require('../../../source/js/common').commonObj;
const sensors = require('../../../utils/report/sensors')
var app = getApp();
const locateMixin = require('../../../mixins/locateMixin')
const { throttle } = require('../../../utils/util')
const { createCanvasImage, fillText, measureText, fillRoundRect } = require('../../../utils/services/2DCanvasUtil')
const { drawPosterPic, getPosterModelSize } = require('../../source/js/drawPoster/fightGroupsDrawPoster');
const { defaultCustomerAvatar } = require('../../../source/const/user');
Page({
  mixins: [locateMixin],
  /**
   * 页面的初始数据
   */
  data: {
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    getUrl: commonObj.PAGODA_DSN_DOMAIN,
    disableEffect: false,
    bg_cantuan,
    // cornermark_bg: cornermark_bg.bg,
    bg_sandclock: bg_sandclock.bg,
    bg_detail_priceinfo: bg_detail_priceinfo.bg,
    fight_group_bg_right: fight_group_bg_right.bg,
    indicatorDots: true,
    timeout: {},
    timeDate: {},
    endTimeDate: [],
    endTimeOut: {},
    ms: 9,
    countFlag: true,
    currentGroupShowFlag: true,
    moreGroupShowFlag: true, //拼团列表是否展示
    offStockFlag: false, //是否下架
    soldOutFlag: false, //是否售罄
    isStarted: true, //活动是否开始
    tipsShow: false,
    picChange: '/source/images/icon_label_tick2.png',
    ct: 0, //倒计时方法中递归次数计次，实现排除本地时间影响的倒计时
    extraData: app.globalData.extraData, // 跳转开卡小程序所需要的参数
    appId: app.globalData.appId, // 跳转开卡小程序所需要的参数
     // v1.6
    isShare: false, //是否显示分享弹框
    isThumbnail: false, //是否显示缩略图弹框
    groupList: [],
    priceDescription: '',
    isShowShare: true,
    stockNum:"98", // 当前商品库存,
    shelfStatus:'', // 上下架状态 U上下 D下架
    line: 2, // 评价文字收起时展示的行数
    allTotal: '', // 全部评价总数
    praiseRate: '', // 好评率
    goodsCommentInfo: '',
    defaultCustomerPictuer: defaultCustomerAvatar, // 用户默认头像
    previewSensorsParams: {
      'element_code': '110100004',
      'element_name': '评论图片',
      'element_content': '评论图片',
      'screen_code': '1101'
    },
    activityID: '',
    activityName: '',
    sharePic: null, // 分享图绘制
    currentView: '',
    isNeedB2CBusiness: false,
    hideSaleCount: true // 是否隐藏仅剩库存（从分享卡片，订单详情，支付成功页进来仅剩库存不对，暂时先隐藏）
  },
  _data: { pageProtocol: true },
  onLoad: function(options) {
    var that = this;
    var systemInfo = wx.getStorageSync('systemInfo');
    console.log('商品详情页的options',options)
    this.setData({
      options
    })
    if (systemInfo) {
      var isIphoneX = systemInfo.model.indexOf('iPhone X') !== -1 ? true : false;
      that.setData({
        isIphoneX: isIphoneX
      })
    }
    if (options && options.fightGroupsIndexObj) {
      var fightGroupsIndexObj = JSON.parse(options.fightGroupsIndexObj);
      that.setData({
        goodsID: fightGroupsIndexObj.goodsID,
        activityID: fightGroupsIndexObj.activityID,
        realSaleCount: fightGroupsIndexObj.realSaleCount || ''
      })
      console.log(this.goodsID,this.activityID)
    } else if (options && options.PTorderDetailObj) {
      var PTorderDetailObj = JSON.parse(options.PTorderDetailObj);
      that.setData({
        goodsID: PTorderDetailObj.goodsID,
        activityID: PTorderDetailObj.activityID
      })
    } else if (options && options.paySuccessObj) {
      var paySuccessObj = JSON.parse(options.paySuccessObj);
      that.setData({
        goodsID: paySuccessObj.goodsID,
        activityID: paySuccessObj.activityID
      })
    } else if (options && options.bindCardObj) { //isBindCard登录传过来的参数
      var bindCardObj = JSON.parse(options.bindCardObj);
      that.setData({
        goodsID: bindCardObj.goodsID,
        activityID: bindCardObj.activityID
      })
    } else if (options && options.inviteFriendsObj) {
      var inviteFriendsObj = JSON.parse(options.inviteFriendsObj);
      that.setData({
        goodsID: inviteFriendsObj.goodsID,
        activityID: inviteFriendsObj.activityID
      })
    } else if (options && options.twoDimenObj) { //管理台扫码进详情
      var twoDimenObj = JSON.parse(options.twoDimenObj);
      that.setData({
        goodsID: twoDimenObj.goodsID,
        activityID: twoDimenObj.activityID
      })
    } else if (options && options.topicObj) { // 拼团专题活动跳转
      var topicObj = JSON.parse(options.topicObj);
      that.setData({
        goodsID: topicObj.goodsID,
        activityID: topicObj.activityID
      })
    }

    // 设置分享海报弹窗的宽高，分享海报缩略图的宽高
    that.setPosterModelSize ()
  },

  /**
   * 设置分享海报弹窗的宽高，分享海报缩略图的宽高
   */
  async setPosterModelSize () {
    const { modelWidth, modelHeight, smallPosterWidth, smallPosterHeight } = await getPosterModelSize()
    this.setData({
      modelWidth,
      modelHeight,
      smallPosterWidth,
      smallPosterHeight
    })
  },

  onShow: function() {
    console.log('this is goodsDetail onShow')
    var that = this;
    // 浏览上报神策
    sensors.pageShow('fightGroupsGoodsDetailPage', {
      $url_query: JSON.parse(JSON.stringify(this.data.options)),
      groupEventID: that.data.activityID || '',
      groupEventName: that.data.activityName || ''
    })
    if (that.data.showPayModal && !that.data.disable) {
      commonObj.showModal('确定要放弃支付吗？', '尚未完成支付，喜欢的果果可能会被抢空哦~', true, '继续支付', '暂时放弃', function(res) {
        if (res.confirm) {
          // 跳到确认订单
          if (app.globalData.reportSensors) {
            app.sensors.track('MPClick', {
              element_code: '110202002',
              element_name: '继续支付',
              element_content: '继续支付',
              screen_code: '1102',
              screen_name: '拼团确认订单页',
              groupActivityID: that.data.activityID,
              groupActivityName: that.data.activityName
            })
          }
          var goodsDetailObj = {
            goodsID: (that.data.goodsDetailObj && that.data.goodsDetailObj.goodsID) || that.data.goodsID,
            activityID: that.data.activityID
          }
          wx.navigateTo({
            url: '/fightGroups/pages/PTorderDetail/index?goodsDetailObj=' + JSON.stringify(goodsDetailObj)
          })
        }else{
          if (app.globalData.reportSensors) {
            app.sensors.track('MPClick', {
              element_code: '110202001',
              element_name: '放弃支付订单',
              element_content: '暂时放弃',
              screen_code: '1102',
              screen_name: '拼团确认订单页',
              groupActivityID: that.data.activityID,
              groupActivityName: that.data.activityName
            })
          }
        }
      })
      that.setData({
        showPayModal: ''
      })
    }
    if (that.data.goodsOrderID) {
      commonObj.showModal('温馨提醒', '您有一个未成团商品，邀请好友参团才能大大提高成团率哦~', true, '邀请好友', '关闭', function(res) {
        wx.reportAnalytics("paybacksharetipsbtn") //支付后返回页tipsBtn v1.4
        if (res.confirm) {
          // 分享
          if (app.globalData.reportSensors) {
            app.sensors.track('MPClick',{
              element_code: '110401001',
              element_name: '邀请好友',
              element_content: '邀请好友按钮',
              screen_code: '1102'
            })
          }
          wx.navigateTo({
            url: '/fightGroups/pages/paySuccess/index?payOrderIDorGoodsOrderID=' + that.data.goodsOrderID
          })
        }else{
          if (app.globalData.reportSensors) {
            app.sensors.track('MPClick', {
              element_code: '110401002',
              element_name: '关闭',
              element_content: '关闭按钮',
              screen_code: '1101',
              screen_name: '拼团详情页',
              groupActivityID: that.data.activityID,
              groupActivityName: that.data.activityName
            })
          }
        }
      })
      that.setData({
        showShareObj: ''
      })
    }
    let height = wx.getStorageSync('systemInfo').windowHeight
    this.setData({
      height:height,
      countFlag: true,
      currentGroupShowFlag: false,
      offStockFlag: false,
      soldOutFlag: false
    }); //计时器打开
  },
  // 定位完成触发的方法
  onLocateReady () {
    if (this.data.currentView === 'content') {
      this.initOnShow()
      return
    }
  },
  // 初次进来定位完成会触发
  initOnShow() {
    this.getGoodsDetail(this.data.goodsID, this.data.activityID)
  },
  onReady: function () {

  },
  getGoodsDetail: function(goodsID, activityID) {
    var that = this;
    var user = wx.getStorageSync('user') || {};
    let { cityID,  storeID } = wx.getStorageSync('timelyCity')
    var options = {
      encryptFlag: false,
      url: '/api/v1/groupBuy/goods/detail',
      data: {
        activityID: activityID,
        goodsID: goodsID,
        cityID: cityID,
        storeID: storeID,
        // storeID: 113,
        customerID: user.userID || -1,
        wxOpenId : wx.getStorageSync('wxSnsInfo').openid
      }
    }
    commonObj.requestData(options, function(res) {
      wx.hideLoading()
      if (Number(res.data.errorCode) === 0) {
        var goodsDetailObj = res.data.data;

        if (!!goodsDetailObj.appOnly) {
          wx.hideShareMenu()
        }
        that.getGroupList(activityID, goodsID, cityID);
        // v2.4 获取商品评价数据
        that.getEvaluationList(goodsDetailObj.spuNumber)
        goodsDetailObj.groupPriceArr = String((goodsDetailObj.groupPrice / 100).toFixed(2)).split('.');
        // if (goodsDetailObj.originalPrice!='') {
        //   goodsDetailObj.originalPrice = (goodsDetailObj.originalPrice / 100).toFixed(2);
        // } else {
        //   goodsDetailObj.originalPrice = goodsDetailObj.originalPrice
        // }
        // goodsDetailObj.groupPrice = (goodsDetailObj.groupPrice / 100).toFixed(2);
        // 活动已结束时弹窗
        if (goodsDetailObj.shelfStatus === 'D') {
          commonObj.showModal('提示','活动结束啦,去其他活动看看吧',false,'我知道了','',function(res) {
            if (res.confirm) {
              wx.navigateBack({
                delta: 1
              })
            }
          })
        }

        // 活动是否开始
        const currTime = res.data.systemTime
        const isStarted = !(that.timeStampFormat(goodsDetailObj.groupStartTime) > currTime)

        // 拼团成功一小时后提货的时间展示
        const startTime = that.timeStampFormat(goodsDetailObj.pickUpStartDate)
        const endTime = that.timeStampFormat(goodsDetailObj.pickUpEndDate)
        const startDate = that.DateFormat(goodsDetailObj.pickUpStartDate)
        const endDate = that.DateFormat(goodsDetailObj.pickUpEndDate)
        const currDate = that.DateFormat(currTime, true)
        let pickUpDateStr = ''

        if (goodsDetailObj.deliveryTimeType === 3) {
          // 活动未开始（1: 提货时间只有一天，展示一天  2：提货时间不止一天，展示时间区间 ）
          if (currTime < startTime) {
            pickUpDateStr = startDate === endDate ? `${endDate}` : `${startDate}-${endDate}`
          }
          // 活动已开始，并且未结束（3：提货时间只剩一天，展示今日拼团成功1小时后可提货 4：提货时间还剩不止一天，展示拼团成功1小时后至x月x日
          else if (currTime < endTime) {
            pickUpDateStr = currDate === endDate ? '今日拼团成功1小时后可提货' : `拼团成功1小时后至${endDate}`
          }
        }
        else {
          pickUpDateStr = startDate ? `${startDate}-${endDate}` : `${endDate}`
        }

        that.setData({
          goodsDetailObj: goodsDetailObj,
          currTime,
          activityName: res.data.data.activityName,
          disable: false,
          shelfStatusFlag: res.data.data.shelfStatus === 'D' ? true: false,
          isStarted,
          pickUpDateStr
        })

        if (wx.getStorageSync('user')) {
          that.effect(goodsDetailObj.activityID, goodsDetailObj.goodsID, wx.getStorageSync('user').userID)
        } else {
          wx.hideLoading()
        }
        var enableJoin = goodsDetailObj.enableJoin === 'Y'
        that.setData({
          enableJoin: enableJoin
        })
        if (goodsDetailObj.priceDescription) {
          that.setData({ priceDescription: goodsDetailObj.priceDescription })
        }
        // 绘制分享图
        that.drawSharePic()
      } else {
        commonObj.showModal('提示', res.data.errorMsg , false, '确认', '', function() {
          wx.navigateBack({
            delta: 1
          })
        })
      }
    }, '', function() {
      that.setData({
        isReady: true
      })

      // 获取试吃精选报告
      that.selectComponent('#tryEatModule').getTryEatReport()

      var goodsDetailObj = that.data.goodsDetailObj;
      if (goodsDetailObj) {
        goodsDetailObj.endTime && that.countActivityEndTime(goodsDetailObj.endTime);
        if (goodsDetailObj.currentGroupList) {
          if (goodsDetailObj.currentGroupList.length === 0) {
            that.setData({
              currentGroupShowFlag: false,
              moreGroupShowFlag: false,
              currentGroupList: goodsDetailObj.currentGroupList
            })
            return;
          }
          if (that.data.goodsDetailObj.currentGroupList.length > 2) { //大于2个才显示
            that.setData({
              showMoreFlag: true
            })
          } else if (that.data.goodsDetailObj.currentGroupList.length > 0) {
            that.setData({
              showMoreFlag: false
            })
          }
          that.data.goodsDetailObj.currentGroupList.forEach(function(item, index) {
            that.countTime(item.expireTime, item.groupID);
          })
        }
      }
    })
  },
  // v2.4 跳转到评价详情页
  navigateToEvaluationDetail () {
    const { spuNumber = '' } = this.data.goodsDetailObj || {}
    wx.navigateTo({
      url: `/userB/pages/evaluation/evaluationDetail/index?spuNumber=${spuNumber}`
    })
    // 上报神策点击拼团详情查看全部评价
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110100005',
        element_name: '查看全部评价',
        element_content: '查看全部评价',
        screen_code: '1101',
        screen_name: '拼团详情页',
        groupActivityID: this.data.activityID,
        groupActivityName: this.data.activityName
      })
    }
  },
  // v2.4 获取商品评价数据
  getEvaluationList: function(spuNumber) {
    if (!spuNumber) return
    app.api.getGoodsFirstComment({
      customerID: app.globalData.customerID || -1,
      spuNumber,
      takeawayAttr: '及时达'
    }).then((res) => {
      const { goodsCommentList = [], goodsCommentTotalInfo = {} } = res.data || {}
      if (!goodsCommentList || !goodsCommentList.length) {
        this.setData({
          goodsCommentInfo: null
        })
        return
      }
      const { allTotal = 0, praiseRate = 0 } = goodsCommentTotalInfo || {}
      this.setData({
        allTotal,
        praiseRate,
        goodsCommentInfo: goodsCommentList[0]
      })
    }).catch(() => {
      commonObj.showModal('提示', '系统繁忙，请求超时', false, '我知道了')
    })
  },
  getGroupList: function (activityId, goodsId, cityId) {
    var that = this;
    commonObj.requestData({
      url: '/api/v1/groupBuy/group/list',
      data: {
        activityID: activityId,
        goodsID: goodsId,
        cityID: cityId
      }
    },res => {
      if (res.data.errorCode === 0) {
        that.setData({
          groupList: res.data.data
        })
        that.data.groupList.forEach(function (item, index) {
          that.countTime(item.expireTime, item.groupID);
        })
      }
    })
  },
  countTime: function(expireTime, index) {
    if (index !== 1789 || index !== 1786) {
    }
    var that = this;
    if (!that.data.countFlag) return;
    var expireTime = expireTime.replace(/-/g, '/');
    var leftTime = parseInt((parseInt(new Date(expireTime).getTime()) - parseInt(new Date().getTime())) / 1000);
    if (leftTime <= 0) {
      that.data.timeDate[index] = "00:00:00";
      that.setData({
        timeDate: that.data.timeDate
      })
    } else {
      var hours = parseInt(leftTime / 3600);
      var minute = parseInt(parseInt(leftTime / 60) - hours * 60);
      var second = parseInt(leftTime % 60);
      var hour = hours < 10 ? ("0" + hours) : hours;
      var min = minute < 10 ? ("0" + minute) : minute;
      var sec = second < 10 ? ("0" + second) : second;
      that.data.timeout['timer' + index] = setTimeout(function() {
        that.countTime(expireTime, index);
      }, 1000);

      that.data.timeDate[index] = hour + ":" + min + ":" + sec;
      that.setData({
        timeDate: that.data.timeDate
      })
    }
  },
  countActivityEndTime: function(endTime, duration) {
    var that = this;
    if (!that.data.countFlag) return;
    var endTime = endTime.replace(/-/g, '/');
    let currTime = duration ? (that.data.currTime - (-duration)) : that.data.currTime
    var leftTime = parseInt((parseInt(new Date(endTime).getTime()) - currTime) / 1000);
    // console.log(that.data.currTime)
    // console.log(leftTime)
    if (leftTime <= 0) {
      that.data.endTimeDate = "00:00:00:00";
      that.setData({
        endTimeDate: that.data.endTimeDate
      })
    } else {
      var days = parseInt(leftTime / (24 * 3600));
      var hours = parseInt(parseInt(leftTime / 3600));
      var hourShow = parseInt(parseInt(leftTime / 3600))
      var minute = parseInt(parseInt(leftTime / 60) - hours * 60);
      var second = parseInt(leftTime % 60);
      var day = days < 10 ? ("0" + days) : days;
      var hour = hours < 10 ? ("0" + hours) : hours;
      var min = minute < 10 ? ("0" + minute) : minute;
      var sec = second < 10 ? ("0" + second) : second;
      // that.data.ms < 1 ? that.setData({
      //   ms: 9
      // }) : that.data.ms--;
      that.data.endTimeout = setTimeout(function() {
        that.data.ct++
          that.countActivityEndTime(endTime, that.data.ct * 1000);
      }, 1000);
      that.data.endTimeDate = (day + ":" + hourShow + ":" + min + ":" + sec).split(":");
      that.setData({
        endTimeDate: that.data.endTimeDate
      });
      // that.setData({
      //   ms: that.data.ms
      // })
    }
  },
  showGroup: function() {
    wx.reportAnalytics("seemorejoinbtn") //查看更多凑团btn v1.4
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110101001',
        element_name: '查看参团列表',
        element_content: '查看更多',
        screen_code: '1101',
        screen_name: '拼团详情页',
        groupActivityID: this.data.activityID,
        groupActivityName: this.data.activityName
      })
    }
    var that = this;
    that.data.goodsDetailObj.currentGroupList = that.data.groupList;
    // that.setData({
    //   goodsDetailObj.currentGroupList:
    // })
    this.setData({
      currentGroupShowFlag: true
    })
  },
  toIndex: function() {
    wx.reportAnalytics("backhomebtn") //去首页btn v1.4
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110100003',
        element_name: '首页',
        element_content: '回首页按钮',
        screen_code: '1101',
        screen_name: '拼团详情页',
        groupActivityID: this.data.activityID,
        groupActivityName: this.data.activityName
      })
    }
    wx.navigateTo({
      url: '/fightGroups/pages/fightGroups/index'
    });
  },
  bindCloseTap: function() { //参团关闭
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110101003',
        element_name: '关闭参团列表',
        element_content: '参团关闭',
        screen_code: '1101',
        screen_name: '拼团详情页',
        groupActivityID: this.data.activityID,
        groupActivityName: this.data.activityName
      })
    }
    this.setData({
      currentGroupShowFlag: false
    })
  },
  navigateToDownLoadH5(){
    let pageUrl = 'http://mp.weixin.qq.com/s?__biz=MjM5ODAwMTYwMA==&mid=521139153&idx=1&sn=375d762d3186d7596f297023a08d813b&chksm=3c2a3d7e0b5db468c113e25d4421397dd4f5f5bc810e024e8bc5dcc478ace022c9df3aafbb35#rd'
    wx.navigateTo({
      url: '/h5/pages/commonLink/index?pageUrl=' + encodeURIComponent(pageUrl),
    })
  },
  openGroup: function() { //开团
    wx.reportAnalytics("creatgroupbtn") //一键开团btn v1.4
    var that = this;
    if (!!that.data.goodsDetailObj.appOnly) { // app专享团不让参团
      commonObj.showModal('', '亲亲，此拼团为App专享拼团，请去往百果园App购买哦~', true, '下载App', '放弃优惠', function (res) {
        if (res.confirm) {
          // wx.navigateTo({
          //   url: '/userB/pages/guideToDownLoad/index'
          // })
          that.navigateToDownLoadH5();
          return
        } else if (res.cancel) {
          return
        }
      })
      return
    }
    that.preventEvent();
    let { offStockFlag, soldOutFlag, disableEffect, shelfStatusFlag, endTimeDate, isStarted } = this.data;
    if (offStockFlag || soldOutFlag || disableEffect || shelfStatusFlag || endTimeDate === '00:00:00:00' || !isStarted) {
      return
    }
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110100002',
        element_name: '一键开团',
        element_content: '开团按钮',
        screen_code: '1101',
        screen_name: '拼团详情页',
        groupActivityID: this.data.activityID,
        groupActivityName: this.data.activityName
      })
    }
    if (app.checkSignInsStatus()) {
      // 用户已登录
      wx.reportAnalytics('opengroup_detail');
      var goodsDetailObj = {
        goodsID: (that.data.goodsDetailObj && that.data.goodsDetailObj.goodsID) || that.data.goodsID,
        activityID: that.data.activityID,
        realSaleCount: that.data.realSaleCount
      }
      wx.navigateTo({
        url: '/fightGroups/pages/PTorderDetail/index?goodsDetailObj=' + JSON.stringify(goodsDetailObj)
      })
    } else {
      app.signIn()
    }
  },
  bindCanTuan: function(e) {
    if (this.data.disableEffect) return
    var that = this;
    if (!!that.data.goodsDetailObj.appOnly) { // app专享团不让参团
      commonObj.showModal('', '亲亲，此拼团为App专享拼团，请去往百果园App购买哦~', true, '下载App', '放弃优惠', function (res) {
        if (res.confirm) {
          // wx.navigateTo({
          //   url: '/userB/pages/guideToDownLoad/index'
          // })
          that.navigateToDownLoadH5();
          return
        } else if (res.cancel) {
          return
        }
      })
      return
    }
    if (that.data.disable) {
      return;
    }
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110101002',
        element_name: '参团',
        element_content: '去参团',
        screen_code: '1101',
        screen_name: '拼团详情页',
        groupActivityID: this.data.activityID,
        groupActivityName: this.data.activityName
      })
    }

    wx.reportAnalytics('cantuanbtn_click');
    that.preventEvent();
    if (app.checkSignInsStatus()) {
      // 用户已登录
      var groupId = e.currentTarget.dataset.groupid;
      var openerID = e.currentTarget.dataset.openerid;
      var goodsDetailObj = {
        goodsID: that.data.goodsDetailObj.goodsID,
        groupID: groupId,
        openerID: openerID,
        isJoin: true,
        activityID: that.data.activityID,
        realSaleCount: that.data.realSaleCount
      }
      commonObj.requestData({
        url: `/api/v1/groupBuy/qualificationCheck/${wx.getStorageSync('user').userID}/${that.data.activityID}`,
        method: 'GET'
      }, (res) => {
        if (Number(res.data.errorCode) === 0) {
          if (res.data.data.enableJoin === 'Y') {
            wx.navigateTo({
              url: '/fightGroups/pages/PTorderDetail/index?goodsDetailObj=' + JSON.stringify(goodsDetailObj)
            })
          } else {
            commonObj.showModal('提示', '该团仅限新用户参团，您可以开个新团哦~', true, '我来开团', '取消', function (res) {
              if (res.confirm) {
                var goodsDetailObj = {
                  goodsID: that.data.goodsDetailObj.goodsID,
                  activityID: that.data.activityID
                }
                wx.navigateTo({
                  url: '/fightGroups/pages/PTorderDetail/index?goodsDetailObj=' + JSON.stringify(goodsDetailObj)
                })
              }
            });
          }
        } else {
          wx.showToast({
            title: res.data.errorMsg,
            icon: 'none',
            duration: 2000
          })
        }
      }, (fail) => {

      });
    } else {
      app.signIn()
    }
  },
  showTips: function() { //显示模板
    wx.reportAnalytics("goodsbrandclick") //商品品牌信息点击 v1.4
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110102001',
        element_name: '查看百果园服务说明',
        element_content: '查看服务说明横栏',
        screen_code: '1101',
        screen_name: '拼团详情页',
        groupActivityID: this.data.activityID,
        groupActivityName: this.data.activityName
      })
    }
    this.animation = animation
    animation.translateY(800).step()
    this.setData({
      animationData: animation.export(),
      tipsShow: true
    })
    setTimeout(function() {
      animation.translateY(0).step()
      this.setData({
        animationData: animation.export()
      })
    }.bind(this), 200)
  },
  hideTips: function() { //隐藏模板
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110102002',
        element_name: '我知道了',
        element_content: '我知道了',
        screen_code: '1101',
        screen_name: '拼团详情页',
        groupActivityID: this.data.activityID,
        groupActivityName: this.data.activityName
      })
    }
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    this.animation = animation
    animation.translateY(800).step()
    this.setData({
      animationData: animation.export(),
    })
    setTimeout(function() {
      this.setData({
        animationData: animation.export(),
        tipsShow: false
      })
    }.bind(this), 200)
  },
  //检测限购
  effect: function(activityID, goodsID, customerID) {
    var options = {
      url: '/api/v1/groupBuy/goods/effect',
      data: {
        activityID: activityID,
        goodsID: goodsID,
        customerID: customerID
      }
    }
    commonObj.requestData(
      options,
      res => {
        if (res.data.errorCode === 0) {
          var dt = res.data.data
          this.setData({
            residualEffectNum: dt.residualEffectNum,
            totalEffectNum: dt.effectNum,
            disableEffect: this.data.disable ? false : dt.residualEffectNum === 0
          })
        }
        wx.hideLoading()
      },
      fail => {
        wx.hideLoading()
      });
  },

  // 防止点击过快，导致页面重复跳转蒙层
  preventEvent: function() {
    this.setData({
      prevent: true
    });
    setTimeout(() => {
      this.setData({
        prevent: false
      });
    }, 400)
  },

  onHide: function() {
    this.setData({
      countFlag: false
    }) //停止计时
  },
  onShareAppMessage: function() {
    wx.reportAnalytics("goodssharebtn") //商品详情分享btn v1.4
    var that = this;
    var fightGroupsIndexObj = {
      goodsID: that.data.goodsID,
      activityID: that.data.activityID
    }
    var shareInfo = that.data.goodsDetailObj;
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110100001',
        element_name: '分享拼团活动',
        element_content: '分享按钮/右上角分享',
        screen_code: '1101',
        screen_name: '拼团详情页',
        groupActivityID: this.data.activityID,
        groupActivityName: this.data.activityName
      })
    }
    let { storeID = 0 , storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
    let shareObj = {
      mp_shareTitle: shareInfo ? shareInfo.groupShareTitle : "有人@我 开团了~~",
      activity_ID: shareInfo ? that.data.activityID : 0,
      activity_Name: shareInfo ? that.data.activityName : '',
      groupID: 0,
      groupSize: "",
      openerID: "",
      currentCount: "",
      groupActivityID: shareInfo ? that.data.activityID : 0,
      groupActivityName: shareInfo ? that.data.activityName : '',
      screen_name: '拼团详情页',
      screen_code: '1101',
      storeID,
      storeName,
      storeNum: storeInfo.storeCode || ''
    }
    console.log('this.sharePic', this.data.sharePic)
    if (shareInfo) {
      wx.reportAnalytics('share_success')
      if (app.globalData.reportSensors) {
        app.sensors.track('MPShare', shareObj)
      }
      let title = shareInfo.groupShareTitle
      return {
        title: title,
        path: `/fightGroups/pages/goodsDetail/index?fightGroupsIndexObj=${JSON.stringify(fightGroupsIndexObj)}`,
        imageUrl: that.data.sharePic || that.data.picUrl + shareInfo.groupSharePic,
      }
    } else {
      wx.reportAnalytics('share_success')
      if (app.globalData.reportSensors) {
        app.sensors.track('MPShare', shareObj)
      }
      return {
        title: '有人@我 开团了~~',
        path: `/fightGroups/pages/goodsDetail/index?fightGroupsIndexObj=${JSON.stringify(fightGroupsIndexObj)}`,
        imageUrl: 'https://resource.pagoda.com.cn/group1/M00/14/39/CmiWiF3OFXqAaqfqAACAY5XbFfo343.jpg',
      }
    }
  },
  // V1.6 海报及分享
  showModel: function (e) {
    var that = this;
    var type = e.currentTarget.dataset.type;
    if (type === 'share') {
      that.setData({ isShare: true })
      that.utilShare()
    } else if (type === 'thumbnail') {
      // that.setData({ isThumbnail: true })
      if (!wx.showShareImageMenu) {
        that.setData({ isThumbnail: true })
      }
      that.utilThumbnail()
      wx.showLoading({
        title: '正在生成海报',
      })
      that.drawPoster()
    }
  },
  closeModel: function (e) {
    wx.hideLoading()
    var that = this;
    var type = e.currentTarget.dataset.type;
    if (type === 'share') {
      that.setData({ isShare: false })
    } else if (type === 'thumbnail') {
      that.setData({ isThumbnail: false })
    }
  },

  // 分享弹框
  utilShare: function () {
    var that = this;
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    that.animation = animation;
    animation.translateY(-158).step();
    that.setData({ animationShare: animation.export() });

    setTimeout(function () {
      that.setData({ animationShare: animation })
    }.bind(that), 400)
  },
  // 缩略图弹框
  utilThumbnail: function () {
    var that = this;
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    that.animation = animation;
    animation.translateY(-parseInt((that.data.modelHeight / 2))).step();
    that.setData({ animationImage: animation.export() });

    setTimeout(function () {
      this.setData({ animationImage: animation })
    }.bind(this), 300)
  },
  // 绘制canvas图
  drawPoster: async function () {
    const that = this
    // 若海报已经生成过了，不用重新生成
    if (that.data.shareImage) {
      return wx.hideLoading()
    }
    try {
      const { detailHeadPicList, groupShareTitle: goodsName, groupSize, spec: goodsWeight, groupPriceArr, originalPrice, goodsID, activityID } = that.data.goodsDetailObj
      const groupPrice = groupPriceArr.join('.') // 商品拼团价
      const originPrice = (originalPrice /100).toFixed(2) //商品原价
      const headPic = that.data.picUrl + detailHeadPicList[0] // 商品头图
      const sceneUrl = `GGD@${goodsID}@${activityID}` // 小程序码对应页面链接的参数
      const goodsObj = {
        headPic,
        goodsName,
        goodsWeight,
        groupSize,
        groupPrice,
        originPrice,
        sceneUrl
      }
      // 绘制海报
      const canvasId = 'posterCanvas'
      const shareImage = await drawPosterPic(canvasId, goodsObj)
      // that.setData({
      //   shareImage
      // })
      that.showSharePoster(shareImage)
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      wx.showLoading({
        title: '生成海报出错啦，请稍后重试',
      })
    }
  },
  /**
   * 展示商品生成的海报
   */
  showSharePoster(path) {
    if (!wx.showShareImageMenu) {
      this.setData({
        shareImage: path
      })
    } else {
      wx.showShareImageMenu({
        path: path
      })
    }
  },
  // 海报保存到本地
  savePoster: function () {//shareImage
    var that = this;
    wx.showLoading({
      title: '正在生成海报'
    })
    wx.saveImageToPhotosAlbum({
      filePath: this.data.shareImage,
      success: (res) => {
        wx.hideLoading()
        wx.showToast({ title: '保存成功，快去分享吧~', icon: 'none' });
      },
      fail: (err) => {
        this.setData({ status: 'auth denied' })
        wx.hideLoading()
        commonObj.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了', '', function (res) {
          if (res.confirm && that.data.status === 'auth denied') {
            wx.openSetting()
          }
        })
      }
    })
  },

  // 空白函数 禁止滚动
  preventTouchMove: function (e) {
  },
  bindscrollFunc(e) {
    var scroll = e.detail.scrollTop;
    if (scroll > 320 && this.data.isShowShare) {
      this.setData({
        isShowShare: false
      })
    } else if (scroll <= 320 && !this.data.isShowShare) {
      this.setData({
        isShowShare: true
      })
    }
  },
  // 跳转首页
  handleToHome: throttle(function () {
    const { options = null } = this.data
    if (options && options.pageFrom === 'fightGroupsList') {
      wx.navigateBack({
        delta: 1
      })
    } else {
      wx.navigateTo({
        url: '/fightGroups/pages/fightGroups/index'
      })
    }
  }),
  drawSharePic() {
    if (!!this.data.sharePic) return
    let query = wx.createSelectorQuery()
    query.select('#sharePicCanva').fields({node: true, size: true}).exec(async (res) => {
      if(res[0] && res[0].node) {
        let canvas = res[0].node
        let canvasCtx = canvas.getContext('2d')
        canvas.width = 992
        canvas.height = 794
        let {picUrl, isStarted} = this.data
        let {headPic, groupPrice, spec, sellCount} = this.data.goodsDetailObj
        let bg = '../../source/images/fightGroup-good-share-bg.png'
        let title_img = '../../source/images/share-img-title.png'
        let add_icon = '../../source/images/share-img-add.png'

        canvasCtx.scale(2, 2)
        // 绘制内容区域
        canvasCtx.fillStyle = '#fff'
        fillRoundRect(canvasCtx, 8, 0, 496, 397, 8)
        // 绘制背景图
        bg = await createCanvasImage(canvas, bg)
        canvasCtx.drawImage(bg, 0, 0, 496, 397)
        // 绘制拼团标题
        title_img = await createCanvasImage(canvas, title_img)
        canvasCtx.drawImage(title_img, 26, 19, 317, 50)
        // 绘制商品图片
        headPic = await createCanvasImage(canvas, picUrl + headPic)
        canvasCtx.drawImage(headPic, 14, 110, 250, 250)
        // 绘制商品价格
        const middlePointX = 162 // 以抢购按钮为基准
        const symbolW = measureText(canvasCtx, '¥', '24px')
        const priceTextW = measureText(canvasCtx, groupPrice / 100, '42px')
        const specTextW = measureText(canvasCtx, spec, '24px')
        const symbolTextX = (middlePointX - symbolW - priceTextW) / 2 // ￥符号
        const priceTextX = (middlePointX - priceTextW) / 2  // 价格
        const specTextX = specTextW >= 160 ? 0 : (middlePointX - specTextW) / 2  // 规格
        fillText(canvasCtx,{
          text: '¥',
          x: 285 + symbolTextX,
          y: 170,
          fontSize: '24px',
          fillStyle: '#F22727',
          textBaseline: 'middle'
        })
        fillText(canvasCtx, {
          text: groupPrice/100,
          x: 295 + priceTextX,
          y: 166,
          fontSize: '42px',
          fillStyle: '#FF3535',
          textBaseline: 'middle'
        })
        // 绘制商品规格
        fillText(canvasCtx, {
          text: spec,
          x: 285 + specTextX,
          y: 203,
          fontSize: '24px',
          fillStyle: '#848484',
          MaxLineNumber: 1,
          width: 160,
          textBaseline: 'middle'
        })
        // 绘制已团X件标签
        if (isStarted) {
          const sellCountText = '已团' + sellCount + '件'
          const sellCountTextWidth = measureText(canvasCtx, sellCountText, '18px')
          const sellCountTextX = (middlePointX - sellCountTextWidth) / 2 // 已团
          console.log('sellCountTextWidth', sellCountTextWidth)
          fillRoundRect(canvasCtx, 277 + sellCountTextX, 225, sellCountTextWidth + 18, 28, 4)
          canvasCtx.strokeStyle = "#FF3737"
          canvasCtx.stroke()
          canvasCtx.fillStyle = "#fff"
          canvasCtx.fill()
          fillText(canvasCtx, {
            text: sellCountText,
            x: 286 + sellCountTextX,
            y: 239,
            fontSize: '18px',
            fillStyle: '#FF3837',
            textBaseline: 'middle'
          })
        }
        // 绘制抢购按钮
        add_icon = await createCanvasImage(canvas, add_icon)
        canvasCtx.drawImage(add_icon, 276, 275, 191, 52)
        // canvas 画布转为图片
        wx.canvasToTempFilePath({
          canvas: canvas,
          x: 0,
          y: 0,
          width: 992,
          height: 794,
          quality: 1,
          destWidth: 992,
          destHeight: 794,
          canvasId: 'sharePicCanva',
          success: res => this.setData({
            sharePic: res.tempFilePath
          }),
          finally: () => wx.hideLoading()
        })
      }
    })

  },

  /**
   * @description 时间字符串转成时间戳
   * @param date 2021-01-01 00:00:00
   * @return 1609430400000
  */
  timeStampFormat (dateStr) {
    if(!dateStr) return ''
    return new Date(dateStr.replace(/-/g, "/")).getTime();
  },

  /**
   * @description 截取日期
   * @param dateStr 2021-01-01 00:00:00 或 1627441767550（时间戳）
   * @param isTimeStamp dateStr是否为时间戳字符串
   * @return 01月01日
  */
  DateFormat (dateStr, isTimeStamp = false) {
    if(!dateStr) return ''
    !isTimeStamp && (dateStr = dateStr.replace(/-/g, "/"))
    const data= new Date(dateStr)
    const month = data.getMonth() + 1
    const day = data.getDate()
    return (month < 10 ? "0" + month : month) + "月" + (day < 10 ? "0" + day : day) + "日"
  },

  // 禁止滚动穿透
  preventDefault() {
    return false
  }
})
