// user/pages/complaints/goodsDissatisfy/index.js
import { refundSubscript, reportTmplIds } from '../../../../mixins/ticketEntry'
const common = require('../../../../source/js/common').commonObj
const config = require('../../../../utils/config')
import utils from '../../../../utils/util'
import { MAX_REFOUND, SUBMIT, REFUND_TO_WALLET, CONFIRM_REFUND, CANCEL_REFUND, sensorsReportTimely } from '../sensorReportInfos'
import { outLoginType } from '../../../../source/const/globalConst'
import { SUB_TYPES } from '~/source/const/market'
const app = getApp()
const { GOODS_NULL } = require('../../../../source/const/goodsImage')
import sensors from '../../../../utils/report/sensors'
import { customerSubTypeEnum } from '../../orderLogic/customer'
import { pageTransfer } from '~/utils/route/pageTransfer'
import { COS_UPLOAD_TYPE_ENUM } from '~/source/const/cosConfig'
import { btnOperation, generateValidateList, validate, validateEnum } from './business/validate'
import firstRefundMixin from '~/utils/refund/firstRefundMixin'
const turingReport = require.async('../../../../sourceSubPackage/commonUtils/turing/report.js');
const ORDER_TYPE = {
  '1': 10, // 及时达
  '3': 100, // 门店
  '4': 150, // 接龙
  '5': 130, // 全国送
}
Page({
  mixins: [firstRefundMixin],
  /**
   * 页面的初始数据
   */
  data: {
    BGXX_PIC_DOMAIN: config.baseUrl.PAGODA_PIC_DOMAIN,
    //  商品为空图
    GOODS_NULL,

    /**当前选择的一级原因 */
    primaryReasonIndex: -1,
    /**当前选择的二级原因数组 */
    reasonIndex: [],

    /**不满意原因——一级原因 */
    selStrPrimaryReason: '',
    selArrReasonText: [], // 商品不满意：不满意原因
    otherReason: '',  // 服务不满意的其它原因
    /**是否选择其他原因 */
    isChooseOther: false,

    isShowRefundHelp: false, // 是否展示底部退款说明弹窗
    /**金额列表 */
    priceList: [],
    /**商品金额 */
    goodsAmount: 0,

    refundRatio: 0, // 退款比例
    refundAmount: 0, // 退款金额
    maxRefundAmount: 0, // 商品最高可退金额
    goodsPic: [], // 退款商品证据图
    complaintType: '1', // 投诉类型
    goodsID: '', // 商品ID
    payMentInfo:null,
    newGoodsInfo:{},
    orderType:null,
    defaultHeadPic: 'https://resource.pagoda.com.cn/group1/M21/8C/51/CmiLkGJnsu-AKE_SAAAIGee3pVE401.png', // 默认商品图
    // 服务名称
    serviceName: '',
    // 餐盒费
    serviceAmount: '',
    // 以下是三无退的功能
    // 是否显示退款预警弹窗
    isShowRefundWarning: false,
    // 是否弹过退款预警弹窗
    isShowedWarningTips: false,
    // 退款提醒
    refundWarning: null,

    /**该订单是否仅钱包支付 */
    isOnlyWalletPay: false,

    /**展示退款原因确认弹窗 */
    showReasonConfirmModal: false,
    // 订阅消息相关
    subscribe: {
      // 是否展示订阅
      show: false,
      // 模板列表
      tmplIds: refundSubscript
    },
    // 是否是门店订单里的新订单 新订单支持单品退
    isDmOrder: false,
    // 优惠前总价
    totalAmount: 0,
    // 是否是果粉订单
    isFruitFansOrder: false,
    // 历史订单弹框商品金额
    historyOrderMsgAmount: 0,
    // 是否是配送单
    isDelivery: false,
    uploadType: COS_UPLOAD_TYPE_ENUM.REFUND,
    btnText: '提交',
    btnType: validateEnum.reason,
    // 进入页面时 默认展示的不满意图片
    imgList: []
  },
  _data: {
    // 送礼订单的身份
    identityType: {},
    // 如有弹窗预警时显示的 客服联系
    contact: '',
    // 是否正在提交三无退中 前端控制防止重复提交
    lockSubmitApply: false,
    // 三无退成功后 传到成功页的 tradeNo
    tradeNo: '',
    // 退款方式 0原路退 1主钱包
    refundChannelType: 1,
    // 如果需要短信上行校验 则保存此参数
    submitParams: {},
    // 是否包含果粉的订单信息
    customer: {
      /** @type {typeof customerSubTypeEnum[keyof typeof customerSubTypeEnum]} */
      customerSubType: customerSubTypeEnum.history
    },
    // 选择的不满意原因大类code
    selStrPrimaryReasonCode: '',
    // 选择的不满意原因小类code数组
    selArrReasonCode: []
  },
  /**
   * 生命周期函数--监听页面加载
   * 读取缓存中对应的商品不满意原因填写内容、获取商品不满意原因、计算退款价格
   */
  onLoad: function () {
    const data = pageTransfer.receive()
    const {
      identityType,
      complaintType,
      createSource,
      contact,
      isOnlyWalletPay,
      isDmOrder,
      orderTicket = '',
      customer,
      orderType,
      refundGoods,
      primaryReasonIndex = -1,
      reasonIndex = [],
      // 进入页面时 默认选择的不满意类型
      primaryReasonDic = '',
      // 进入页面时 默认选择的不满意原因
      reasonDic = '',
      // 不满意原因
      refundDesc = '',
      // 上传的图片
      refundImages = [],
      isDelivery
    } = data
    this._data.identityType = identityType || {}
    this._data.validateList = generateValidateList.call(this, isDmOrder)
    this._data.contact = contact
    if (customer) {
      this._data.customer = customer
    }
    // 转化为上传图片组件所需要的数组格式
    const arrObjRefundImages = refundImages.map(item => {
      return {
        httpUrl: item
      }
    })
    // 加载此处为优先设置值 防止未取到值
    this.setData({
      orderType: Number(orderType),
      createSource,
      isDmOrder,
      isOnlyWalletPay,
      orderTicket,
      primaryReasonIndex,
      reasonIndex,
      isFruitFansOrder: this._data.customer.customerSubType !== customerSubTypeEnum.history,
      // 补充的不满意原因文本
      otherReason: refundDesc,
      // 不满意图片
      imgList: arrObjRefundImages,
      // 提交前的图片数组
      goodsPic: arrObjRefundImages,
      isDelivery
    })
    if (primaryReasonIndex === -1 && !primaryReasonDic && !reasonDic) {
      // 没选择原因，主动展示弹窗
      this.chooseReason()
    } else {
      // 已经选择了，则展示出来
      const comp = this.selectComponent('#reason_confirm_modal')
      comp.preSelect({
        primaryReasonDic,
        reasonDic
      })
    }
    validate(this._data.validateList)
    // console.log('refundGoods', refundGoods);
    // 修改特价文案
    if (refundGoods.marketingSubType === SUB_TYPES.XIN_REN_TE_JIA) {
      refundGoods.specialType = '新人价'
    }
    const payMentInfo = this.getPayInfo(refundGoods)
    const newGoodsInfo = this.transGoodsInfo(refundGoods)
    const goodsID = newGoodsInfo.goodsId

    this.setData({
      goodsID,
      complaintType,
      maxRefundAmount: newGoodsInfo.maxRefundAmount || 0,
      refundGoods: refundGoods,
      payMentInfo,
      newGoodsInfo,
      serviceName: newGoodsInfo.serviceName,
      // 餐盒费
      serviceAmount: newGoodsInfo.serviceAmount
    })

    this.getGoodsAmount()
    this.getPriceList()
    // 计算退款价格
    this.getRefundAmount()
  },
  onShow(){
    sensors.pageScreenView(this._data.identityType)
  },
  /**
   * 生命周期函数--监听页面卸载
   * 离开当前页面存储填写内容，如果点击保存，则将填写内容值设置到上个页面。
   */
  onUnload: function () {
  },
  // 输入其他原因
  bindTextArerInput (e) {
    const value = e.detail.value.slice(0, 500)

    this.setData({
      otherReason: value
    })
  },
  // 计算退款价格
  getRefundAmount () {
    const maxRefundAmount = this.data.maxRefundAmount || 0
    const refundRatio = this.data.refundRatio || 0
    const refundAmount = Math.floor(maxRefundAmount * refundRatio / 100)
    this.setData({
      refundAmount
    })
    validate(this._data.validateList)
  },
  // 完成拖动滑块触发
  sliderChange (e) {
    const refundRatio = e.detail.value
    this.setData({
      refundRatio
    })
    this.getRefundAmount()
  },
  // 拖动滑块过程中触发
  sliderChanging (e) {
    const refundRatio = e.detail.value
    this.setData({
      refundRatio
    })
    this.getRefundAmount()
  },
  // 展示退款信息
  showRefundHelp () {
    this.setData({
      isShowRefundHelp: true
    })
    // 最高可退埋点 及时达 全国送 接龙订单
    sensorsReportTimely(MAX_REFOUND, this.data.orderType, this.data.isDmOrder, this._data.identityType)
  },
  // 删除图片
  handleDel (event) {
    const { arr } = event.detail
    this.setData({ goodsPic: [...arr] })
    validate(this._data.validateList)
  },
  // 上传图片
  async handleUpload (event) {
    const { arr } = event.detail
    this.setData({ goodsPic: [...arr] })
    validate(this._data.validateList)
  },
  // 保存/提交售后 按钮分为两个情况
  async submitRefund () {
    const { isShowedWarningTips } = this.data
    // if (refundRatio === 0 || Number(refundAmount) === 0) {
    //   this.showToast('退款比例/金额不能为0')
    //   return
    // }
    // //  如不是选择其他原因 且 没有选择子原因
    // if (!isChooseOther && !selArrReasonText.length) {
    //   this.showToast('请选择商品不满意原因')
    //   return
    // }
    const _firstRefundCheck = await this.firstRefundCheck()
    //  触发新客首单退弹窗逻辑，跳转审核详情或者实名页
    if (!_firstRefundCheck) {
      return
    }

    // 先校验是否实名认证
    const res = await this.checkVerify()
    // 如果校验不通过 则不跳转
    if (!res) {
      console.log('校验不通过，已经跳转');
      return
    }
    // 开始请求
    // 本商品未弹过退款预警弹窗 则需要弹窗
    if (!isShowedWarningTips ) {
      return this.showRefundWarningPopup()
    }
    // 这里是及时达、全国送、接龙订单、新版门店的三无退
    this.refundPathConfirm()
  },

  /**
   * 提交三无退前检查是否需要实名认证
   */
  async checkVerify() {
    const params = this.getSearchParams()
    const { data } = await app.api.threeRefundRatioCheck(params)
    // true表示需要实名认证
    if (!data) {
      return true
    }
    // 查询当前用户是否已经实名认证了
    const { data: { certified, reviewStatus } } = await app.api.getVerifyStatus({
      customerID: app.globalData.customerID || -1
    })

    return this.ratioVerifyHandle({ certified, reviewStatus })
  },

  /**
   * 退款比例实名结果判断处理
   * @param {*} param0
   * @returns
   */
  ratioVerifyHandle({ certified, reviewStatus }) {
    // 实名认证状态 0:未实名 1:已实名 2:审核中
    if (certified === 1) {
      return true
    }
    // 如果审核被拒绝，则需要跳转到审核结果页
    if (reviewStatus === 'R') {
      this.jumpApplyResult(reviewStatus, true)
      return false
    }
    // 如果是未实名 则跳转到实名认证页
    if (certified === 0) {
      this.jumpApplyCertification()
      return false
    }
    // 审核中 则跳转到审核中页面
    if (certified === 2) {
      this.jumpApplyResult(reviewStatus)
      return false
    }
    return true
  },

  /**
   * 获取预警弹窗参数/当前订单是否达到三无退管理台百分比参数
   * @returns 参数对象
   */
  getSearchParams() {
    // 收集判断预警弹窗的弹窗参数
    const { refundAmount } = this.data
    // 再次收集参数
    const { mainOrderNo, itemCode, goodsNumber, isConsumable, saleType } = this.data.refundGoods

    // 是否需要收集spu信息
    const isNeedSpuInfo = () => {
      return (Number(saleType) === 1 || !saleType) && !isConsumable
    }
    // 过滤spu
    const filterSpuInfo = (spu = '') => {
      const strSpu = String(spu)
      if (isNeedSpuInfo()) {
        return strSpu.startsWith('99') || strSpu.startsWith('5') ? '' : strSpu
      }
      return ''
    }

    //  微信unionId
    const unionId = wx.getStorageSync('wxSnsInfo').unionid;
    //  获取用户坐标
    const { longitude, latitude } = utils.getUserLocation()
    // 获取spu  如果是门店订单  则取goodsNumber
    const spuVal = filterSpuInfo(itemCode || goodsNumber)

    const params = {
      // 会员id
      customerID: app.globalData.customerID,
      // 原交易渠道商品订单号
      channelOrderNumber: mainOrderNo,
      // 渠道编码
      refundChannelNo: 10001,
      // 退款金额
      refundAmount: refundAmount,
      spuList: [spuVal],
      //  终端信息
      terminalInfo: {
        unionId,
        longitude,
        latitude
      },
      orderType: ORDER_TYPE[this.data.orderType] || '',
      ticketNo: Number(this.data.orderType) === 3 ? this.data.orderTicket : ''
    }
    return params
  },
  // 微信提示
  showToast (title) {
    wx.showToast({
      title,
      icon: 'none',
      duration: 1000
    })
  },
  // 获取支付商品信息
  getPayInfo(refundGoods){
    let payMentInfo = null
    if(utils.getObjectValue(refundGoods,'paymentInfo')){
      payMentInfo = utils.getObjectValue(refundGoods,'paymentInfo')
    }
    return payMentInfo
  },

  /**
   * 转换商品信息
   * @param {*} goodsInfo
   */
  transGoodsInfo(goodsInfo){
    const newGoodsInfo = {
      goodsId:'',
      count:'',
      price:'',
      name:'',
      spec:'',
      headPic:'',
      maxRefundAmount:'',
      goodsSn:'',
      goodsLevel: '',
      //  赋值商品服务列表
      itemServiceList: goodsInfo.itemServiceList
    }
      switch(this.data.orderType){
        case 1:
        case 5:
            newGoodsInfo.goodsId = goodsInfo.goodsId
            newGoodsInfo.goodsSn = goodsInfo.goodsSn
            newGoodsInfo.spec = goodsInfo.specDesc
            newGoodsInfo.count = goodsInfo.goodsCount
            newGoodsInfo.price = goodsInfo.perAmount || goodsInfo.salesPrice
            newGoodsInfo.name = goodsInfo.goodsName
            newGoodsInfo.headPic = goodsInfo.headPic
            newGoodsInfo.maxRefundAmount = goodsInfo.maxRefundAmount || 0
            newGoodsInfo.goodsLevel = goodsInfo.goodsLevel
          break;
          case 3:
            newGoodsInfo.goodsId = goodsInfo.goodsNumber
            newGoodsInfo.spec = goodsInfo.spec
            newGoodsInfo.count =  `${goodsInfo.goodsValue}${goodsInfo.goodsSpec}`
            newGoodsInfo.price = goodsInfo.goodsPrice
            newGoodsInfo.name = goodsInfo.name || goodsInfo.goodsName
            newGoodsInfo.headPic = goodsInfo.headPic
            newGoodsInfo.maxRefundAmount = goodsInfo.maxRefundAmount || 0
            newGoodsInfo.serialNumber = goodsInfo.serialNumber
            newGoodsInfo.goodsActualAmount = goodsInfo.goodsActualAmount
        break
        case 4:
          newGoodsInfo.goodsId = goodsInfo.goodsId
          newGoodsInfo.goodsSn = goodsInfo.goodsSn
          newGoodsInfo.spec = goodsInfo.specDesc
          newGoodsInfo.count = goodsInfo.goodsCount
          newGoodsInfo.price = goodsInfo.price
          newGoodsInfo.name = goodsInfo.goodsName
          newGoodsInfo.headPic = goodsInfo.headPic
          newGoodsInfo.maxRefundAmount = goodsInfo.maxRefundAmount || 0
          newGoodsInfo.goodsLevel = goodsInfo.goodsLevel
          break;
      }

      newGoodsInfo.goodsAmount = this.setGoodsAmount(goodsInfo, newGoodsInfo.count)

      return newGoodsInfo
  },

  /**
   * 设置商品金额
   * @param { Object } goodsInfo 商品信息
   * @param { number } count 商品行数量
   */
  setGoodsAmount(goodsInfo, count) {
    let goodsAmount = 0;

    switch (this.data.orderType) {
      //  及时达
      case 1: {
        const isTradeCombineOrder = Number(this.data.createSource) === 3
        // 兑换卡情况与普通商品逻辑不一致
        if (goodsInfo.preferentialActType === 2) {
          goodsAmount = goodsInfo.exchangeCardGoodMoney - goodsInfo.heartSaveAmount
          break;
        }
        if (isTradeCombineOrder) {
          // 果粉价订单 且 如果有特价的情况 需要修正特价优惠
          if (this.data.isFruitFansOrder && goodsInfo.joinSpecialPriceCount) {
            goodsInfo.activitySharing = (goodsInfo.retailPrice - goodsInfo.specialPrice) * goodsInfo.joinSpecialPriceCount
            // 如果有特价且 又有果粉优惠的情况 需要修正果粉价优惠
            if (goodsInfo.memberDiscountAmount) {
              goodsInfo.memberDiscountAmount = (goodsInfo.retailPrice - goodsInfo.memberPrice) * (count - goodsInfo.joinSpecialPriceCount)
            }
          }
          /**
           * 商品金额使用最高可退为基数进行反推
           * 商品金额 + 餐盒费 - 优惠券 - 果粉优惠 - 差额退款 - 积分抵扣 - 活动优惠 = 最高可退
           * 商品金额 = 最高可退 + 活动优惠 + 积分抵扣 + 差额退款 + 果粉优惠 + 优惠券 - 餐盒费
           */
          goodsAmount =
            //  最高可退
            goodsInfo.maxRefundAmount +
            //  活动优惠
            goodsInfo.activitySharing +
            //  积分抵扣
            goodsInfo.integralSharing +
            //  差额退款
            goodsInfo.diffRefundSharing +
            //  果粉优惠
            goodsInfo.memberDiscountAmount +
            //  优惠券
            goodsInfo.couponSharing -
            //  餐盒费 果粉订单餐盒费不额外计算
            (this.data.isFruitFansOrder ? 0 : goodsInfo.itemConsumables)
        } else {
          goodsAmount = goodsInfo.goodsCount * (goodsInfo.perAmount || goodsInfo.salesPrice)
        }
        break;
      }
      //  拼团
      case 2: {
        goodsAmount = goodsInfo.count * goodsInfo.price
        break;
      }
      //  门店
      case 3: {
        goodsAmount = goodsInfo.goodsActualAmount
        break;
      }
      //  接龙
      case 4: {
        goodsAmount = goodsInfo.goodsCount * goodsInfo.price
        break;
      }
      //  全国送
      case 5: {
        goodsAmount = goodsInfo.goodsCount * (goodsInfo.perAmount || goodsInfo.salesPrice)
      }
    }

    return goodsAmount
  },

  /**
   * 退款预警弹窗
   */
  async showRefundWarningPopup () {
    const orderType = Number(this.data.orderType)
    const params = this.getSearchParams()
    const req = app.api.selfOperatedRefundWarning(params)

    utils.countDownLoading({
      promiseInstance: req,
      callback: () => {
        // this._data.lockSubmitApply = false
      }
    })

    wx.showLoading()
    // 请求是否需要预警弹窗 要则展示
    req.then(res => {
      wx.hideLoading()
      const { data, errorCode, description } = res
      if (errorCode) {
        wx.showToast({
          title: description,
        })
        return
      }
      const { pass, topThree } = data
      if (pass) {
        return this.refundPathConfirm()
      }
      let showtype = 10
      //  及时达或全国送
      if (orderType === 1 || orderType === 5){
        if (orderType) {
          showtype = orderType
        }
      }
      this.setData({
        refundWarning: Object.assign(
          {},
          data,
          {
            showtype,
            topThree,
            phoneNumber: this._data.contact,
            serverPhone:'4001811212'
          }
        ),
        isShowRefundWarning: true,
        isShowedWarningTips: true
      })
    }).catch(err => {
      console.error(err)
      wx.hideLoading()
    })
  },
  /**
   * 预警弹窗确认按钮事件
   */
  handleConfirm () {
    this.setData({
      isShowRefundWarning: false
    })
    this.refundPathConfirm()
  },
  /**
   * 关闭预警弹窗
   */
  closeRefundWarning(){
    this.setData({
      isShowRefundWarning: false,
      isShowedWarningTips: false
    })
  },
  /**
   * 提交三无退申请（仅及时达、全国送、接龙）
   * @param { number } refundChannelType 退款路径 0原路退，1主钱包
   */
  requestSubmitApply(refundChannelType) {
    this._data.refundChannelType = refundChannelType
    this.setData({
      'subscribe.show': true
    })
  },
  /**
   * 订阅消息关闭事件
   */
  onSubscribeClose({ detail }) {
    // 关闭订阅弹窗
    this.setData({ 'subscribe.show': false })

    // 1. 整理参数信息
    const user = wx.getStorageSync('user')
    const { unionid } = wx.getStorageSync('wxSnsInfo') || {}
    const { subOrderNo, mainOrderNo } = this.data.refundGoods
    const { selStrPrimaryReasonCode, selArrReasonCode } = this._data
    const { createSource, refundAmount, refundGoods, otherReason, goodsPic, refundRatio: refundRatioNum, isDmOrder } = this.data

    // 1.1 整理要退款的商品信息
    // 要退的商品列表 这里表示商品的原价和特价的数组
    const refundItems = []
    // 如果是耗材 则这里会存在
    const refundConsumablesItems = []
    // 1.2.1 耗材简单 先拼接这个
    if (refundGoods.isConsumable) {
      const obj = {}
      obj.itemConsumableId = refundGoods.consumableId
      obj.refundMoney = refundAmount
      obj.quantity = refundGoods.quantity
      obj.goodsSn = refundGoods.goodsSn
      obj.refundReasonType = selStrPrimaryReasonCode
      obj.refundReason = selArrReasonCode.join('|')
      obj.refundDesc = otherReason
      obj.refundPicUrls = goodsPic.length ? goodsPic.map(item => item.httpUrl) : []
      refundConsumablesItems.push(obj)
    } else {
      // 1.2.2 这里是商品的参数拼接
      const {
        itemId,
        quantity,
        goodsId,
        //  商品购买类型，为1时说明为特价购买
        preferentialActType,
        //  普通商品最大可退金额
        normalmaxRefundAmount,
        //  参与特价的商品数量
        joinSpecialPriceCount,
        //  特价订单ItemId
        specialItemId
      } = refundGoods

      //  退款对象
      const obj = {
        itemId,
        quantity,
        goodsId,
        refundAmount: refundAmount,
        goodsSn: refundGoods.goodsSn,
        refundReasonType: selStrPrimaryReasonCode,
        refundReason: selArrReasonCode.join('|'),
        refundDesc: otherReason,
        refundPicUrls: goodsPic.length ? goodsPic.map(item => item.httpUrl) : []
      }

      //  preferentialActType === 1，表示商品用特价购买。
      const isSpecialGoods = preferentialActType === 1

      //  特价商品，且超限时。需要进一步将商品拆分为两条数据进行退款
      if (isSpecialGoods && quantity !== joinSpecialPriceCount) {
        //  copy一个属性相同，但specialItemId、退款数量、退款金额不同的退款对象
        const specialObj = Object.assign({}, obj)
        //  退款比例
        const refundRatio = refundRatioNum / 100

        //  普通商品退款数量 = 商品数量 - 特价商品数量
        obj.quantity = quantity - joinSpecialPriceCount
        //  普通商品退款金额 = 普通商品最大可退金额 * 退款比例
        obj.refundAmount = (normalmaxRefundAmount * refundRatio).toFixed(0)

        specialObj.itemId = specialItemId
        //  特价商品退款数量
        specialObj.quantity = joinSpecialPriceCount
        /**
         * 特价商品退款金额 =（特价商品最大可退金额 * 退款比例）= (specialMaxRefundAmount * refundRatio).toFixed(0)
         * 但由于上述公式根据比例计算，会存在丢失精度问题。所以用商品退款金额减去普通商品退款金额得出
         */
        specialObj.refundAmount = Math.max(refundAmount - obj.refundAmount, 0)

        //  收集2条退款对象（原价商品、特价商品）
        refundItems.push(obj, specialObj)
      }
      //  非特价商品
      else {
        //  收集1条退款对象
        refundItems.push(obj)
      }
    }

    const { refundChannelType } = this._data

    // 及时达、全国送、接龙 中台接口三无退请求参数
    // 中台文档http://op.pagoda.com.cn/#/technical-doc?docId=4865
    const reParams = {
      isDmOrder,
      createSource,
      refundChannelType,
      // 如果涉及到拆单情况 则会有子订单号  否则直接使用主订单号
      'subOrderNo': subOrderNo || mainOrderNo,
      'refundType': '0', // 退款类型[部分退款(0),全额退款(1)]
      'refundModel': 1, // 退款模式 单品退也是传1
      'reason': '三无退', // 	退款原因
      'additionReason': '', // 附加原因
      'desc': '', // 退款描述
      'picUrls': [],
      'refundStatus': 112,
      'refundMoney': refundAmount, // 退款金额
      'launchType': 2, // 发起方
      'customerComplaintType': 1, // 不满意类型
      'refundTradeChannelNum':  10001, // 渠道号
      'blackCheckInfo': {
        'unionId': unionid || '',
        'customerPhone': user.phoneNumber
      },
      refundItems,
      refundConsumablesItems,
      v: 2
    }

    this.selfOperateOderRefund(reParams)
    // 上报到触达域
    detail && reportTmplIds(detail.resultStatus)
  },

  /**
   * 是否同意退到钱包——弹窗确认
   * @returns
   */
  async refundWalletConfirm() {
    let _resolve
    const refundPathResolve = new Promise(resolve => {
      _resolve = resolve
    })
    this.refundPathResolve = _resolve
    this.setData({
      showRefundPathConfirm: true,
    })
    const result = await refundPathResolve

    return result
  },

  /**
   * 退款路径确认
   * @param {*} event
   */
  refundPathChoose(event) {
    const type = event.detail
    if (this.refundPathResolve) {
      this.refundPathResolve(type)
      this.setData({
        showRefundPathConfirm: false,
      })
      delete this.refundPathResolve
    }
  },

  /**
   * 发起退款路径确认流程
   */
  async refundPathConfirm() {
    //  仅钱包支付不需要退款流程弹窗确认
    if (this.data.isOnlyWalletPay) {
      this.requestSubmitApply(1)
      return
    }

    /**是否同意钱包支付 */
    const refundType = await this.refundWalletConfirm()

    const { orderType, isDmOrder } = this.data

    //  退款到钱包
    if (refundType === '0') {
      this.requestSubmitApply(1)
      // 退至会员钱包按钮埋点 及时达 全国送 接龙订单
      sensorsReportTimely(REFUND_TO_WALLET, orderType, isDmOrder, this._data.identityType)
    }
    //  退款到原路
    else if (refundType === '1') {
      this.requestSubmitApply(0)
      // 确定原路退还按钮埋点 及时达 全国送 接龙订单
      sensorsReportTimely(CONFIRM_REFUND, orderType, isDmOrder, this._data.identityType)
    } else {
      // 取消原路退还按钮埋点 及时达 全国送 接龙订单
      sensorsReportTimely(CANCEL_REFUND, orderType, isDmOrder, this._data.identityType)
    }
  },

  /**
   * 及时达 b2c 接龙 门店单品 三无退
   */
  selfOperateOderRefund(reParams){
    const customerID = app.globalData.customerID
    if (!customerID || Number(customerID) === -1) {
      this.showToast('请先登录')
      return
    }
    //  锁定中，不允许重复提交
    if (this._data.lockSubmitApply) {
      this.showToast('正在提交三无退中，请稍后重试')
      return
    }
    //  正在提交，进行锁定
    this._data.lockSubmitApply = true

    const { orderType } = this.data
    const { mainOrderNo: goodsOrderID } = this.data.refundGoods
    const data = {
      customerID,
      data: reParams,
      ratioCheck: this.getSearchParams(),
    }
    const req = app.api.selfOperateOderRefund(data)

    wx.showLoading({
      title: '审核中'
    })
    req.then(res=>{
      const { errorCode, data } = res
      const { refundNo } = data

      //  serverless业务拦截报错
      if (data.success === false) {
        const errorType = data.type

        //  退款比例超出且未实名
        if (errorType === 'ratio') {
          this.ratioVerifyHandle(data.data)
        }

        //  未实名新客首次三无退
        if (errorType === 'firstRefund') {
          this.firstRefundVerifyHandle(data.data)
        }
        wx.hideLoading()
        this._data.lockSubmitApply = false
        return
      }
      if(errorCode === 0){
        // 退款成功 告诉前页面已经申请退款
        const eventChannel = this.getOpenerEventChannel()
        if (eventChannel && eventChannel.emit) {
          eventChannel.emit('refundSuccess')
        }
        // 需求需要等待3s后跳转
        setTimeout(() => {
          wx.hideLoading()
          pageTransfer.send({
            orderType,
            refundNo,
            goodsOrderID,
            tradeNo: this._data.tradeNo,
            isDmOrder: this.data.isDmOrder,
            identityType: this._data.identityType,
          })
          wx.redirectTo({
            url: '/userB/pages/selfSupportComplaints/submitSuccess/index'
          })
        }, 3000)
      } else {
        wx.hideLoading()
      }

      // 上报设备信息给同盾和腾讯
      turingReport.then( report => {
        report.reportOrderDeviceFn({
          orderNo: [reParams.subOrderNo],
          refundOrderNo: refundNo,
          scene: 2,
          orderType: ORDER_TYPE[this.data.orderType],
        })
      })
    }).catch(error => {
      this._data.lockSubmitApply = false
      wx.hideLoading()
      //  用户异常单独提示文案处理
      if(error.errorCode === 550001) {
        common.showModal('提示', '抱歉，当前账户异常，请重新登录', true, '我知道了', '', function (res) {
          if (res.confirm) {
            app.signOut({
              logoutCause: outLoginType.accountErrorLogout
            })
            app.signIn()
          } else if (res.cancel) {
            app.signOut({
              logoutCause: outLoginType.accountErrorLogout
            })
            wx.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
      }
      //  以下几类取后端错误信息提示
      else if([550002, 550003, 550004].includes(error.errorCode)) {
        wx.showModal({
          content: error.description,
          showCancel: false
        })
      }
      //  如果三无退过了 则提示
      else if([101].includes(error.errorCode)) {
        wx.showModal({
          content: '该商品已经申请过三无退了，请勿重复申请',
          showCancel: false
        })
      }
      //  如果门店换货过了 则提示
      else if([430041].includes(error.errorCode)) {
        wx.showModal({
          content: '商品已完成换货，无需再提交三无退款',
          showCancel: false
        })
      }
      //  如果门店换货过了 则提示
      else if([550005].includes(error.errorCode)) {
        wx.showModal({
          content: '退款校验数据过时,请求重新校验',
          showCancel: false
        })
      }
      // 触发短信上行验证
      else if ([550006].includes(error.errorCode)) {
        this._data.submitParams = reParams
        wx.navigateTo({
          url: '/userB/pages/selfSupportComplaints/smsVerification/index',
          events: {
            reSubmit: () => {
              this.selfOperateOderRefund(this._data.submitParams)
            }
          }
        })
      }
      // 短信上行验证超时
      else if ([550007].includes(error.errorCode)) {
        wx.showModal({
          content: '短信上行验证已超时，请稍后重试',
          showCancel: false
        })
      }
      //  其他一律提示网络异常
      else {
        wx.showModal({
          content: '当前网络异常，请稍后重试',
          showCancel: false
        })
      }
    })
  },
  /**
   * 选择不满意原因
   */
  chooseReason () {
    this.setData({
      showReasonConfirmModal: true
    })
  },
  /**
   * 不满意原因确认弹窗确认回调
   */
  reasonConfirmModalSubmit(event) {
    const detail = event.detail
    const {
      primaryReasonIndex,
      reasonIndex,
      selStrPrimaryReason,
      selStrPrimaryReasonCode,
      selArrReasonText,
      selArrReasonCode
    } = detail

    const isChooseOther = selStrPrimaryReason === '其他'
    this.setData({
      primaryReasonIndex,
      reasonIndex,
      selStrPrimaryReason,
      selArrReasonText,
      isChooseOther
    })
    this._data.selStrPrimaryReasonCode = selStrPrimaryReasonCode
    this._data.selArrReasonCode = selArrReasonCode
    this.closeReasonConfirmModal()
  },
  /**
   * 关闭不满意原因确认弹窗
   */
  closeReasonConfirmModal() {
    this.setData({
      showReasonConfirmModal: false
    })
    validate(this._data.validateList)
  },
  /**
   * 关闭最大可退金额弹窗
   */
  closeMaxRefundInfoModal() {
    this.setData({
      isShowRefundHelp: false
    })
  },
  getTotalAmount({
    orderType,
    newGoodsInfo,
    refundGoods,
    isFruitFansOrder,
  }) {
    let totalAmount = 0
    // 门店订单
    if (Number(orderType) === 3) {
      totalAmount = newGoodsInfo.goodsActualAmount
    } else if (refundGoods.preferentialActType === 2) {
      // 兑换卡商品使用原价
      totalAmount = refundGoods.exchangeCardGoodMoney
    } else if (isFruitFansOrder && refundGoods.goodsRowTotalPrice) {
      // 如果是果粉订单 则使用计算好的价格
      totalAmount = refundGoods.goodsRowTotalPrice
    } else if (refundGoods.showTotalPrice) {
      // 如果serverless已经计算价格 则使用serverless提供的
      totalAmount = refundGoods.showTotalPrice
    } else {
      // 否则使用之前的逻辑
      totalAmount =
        (refundGoods.joinSpecialPriceCount
          ? refundGoods.joinSpecialPriceCount * refundGoods.specialPrice +
            refundGoods.amount
          : newGoodsInfo.count * newGoodsInfo.price) +
        (refundGoods.itemConsumables || 0)
    }
    return totalAmount
  },
  getTimelyOrderLable({
    refundGoods,
    historyOrderMsgAmount,
    totalAmount,
    goodsAmount,
  }) {
    const arr = []
    // 果粉订单已经包含餐盒费
    if (!this.data.isFruitFansOrder) {
      refundGoods.itemConsumables &&
        arr.push({
          label: '餐盒费',
          price: refundGoods.itemConsumables,
          isAdd: true
        });
    }
    refundGoods.couponSharing &&
      arr.push({
        label: '优惠券',
        price: refundGoods.couponSharing
      });

    refundGoods.integralSharing &&
      arr.push({
        label: '积分抵扣',
        price: refundGoods.integralSharing
      });

    refundGoods.activitySharing &&
      arr.push({
        label: refundGoods.marketingSubType === SUB_TYPES.XIN_REN_TE_JIA ? '新人价优惠' : '特价优惠',
        price: refundGoods.activitySharing
      });

    refundGoods.memberDiscountAmount &&
      arr.push({
        label: '果粉优惠',
        price: refundGoods.memberDiscountAmount
      });

    refundGoods.diffRefundSharing &&
      arr.push({
        label: '差额退款',
        price: refundGoods.diffRefundSharing
      });
    // 弹窗展示的金额 如果存在历史订单金额 则表示是历史订单 则取反推的值
    const msgAmount = historyOrderMsgAmount || totalAmount
    if (goodsAmount < msgAmount) {
      arr.push({
        label: '其他优惠',
        price: msgAmount - goodsAmount
      });
    }
    return arr
  },
  /**
   * 获取商品优惠前总金额+三无退减去金额信息列表
   * @returns
   */
  getPriceList() {
    let arr = []
    const { orderType, refundGoods, goodsAmount, newGoodsInfo, maxRefundAmount = 0, createSource, isFruitFansOrder } = this.data

    // 如果与零售价不一致 则使用其他优惠补齐
    const totalAmount = this.getTotalAmount({
      orderType,
      newGoodsInfo,
      refundGoods,
      isFruitFansOrder,
    })

    // 特价订单弹窗商品金额
    let historyOrderMsgAmount = 0
    // 如果是历史订单 则反推
    if (this._data.customer.customerSubType === customerSubTypeEnum.history) {
      const isTradeCombineOrder = Number(createSource) === 3
      if (isTradeCombineOrder) {
        // 历史订单 直接用反推逻辑
        historyOrderMsgAmount =
          //  最高可退
          refundGoods.maxRefundAmount +
          //  活动优惠
          refundGoods.activitySharing +
          //  积分抵扣
          refundGoods.integralSharing +
          //  差额退款
          refundGoods.diffRefundSharing +
          //  果粉优惠
          refundGoods.memberDiscountAmount +
          //  兑换卡优惠
          (refundGoods.exchangeDiscount || 0) +
          //  优惠券
          refundGoods.couponSharing -
          //  餐盒费 果粉订单餐盒费不额外计算
          (this.data.isFruitFansOrder ? 0 : refundGoods.itemConsumables)
      } else {
        historyOrderMsgAmount = refundGoods.goodsCount * (refundGoods.perAmount || refundGoods.salesPrice)
      }
    }
    this.setData({
      totalAmount,
      historyOrderMsgAmount
    })

    if (!refundGoods) {
      return arr
    }

    if (+orderType === 1) {
      const timelyOderLabel = this.getTimelyOrderLable({
        refundGoods,
        historyOrderMsgAmount,
        totalAmount,
        goodsAmount,
      })
      arr = arr.concat(timelyOderLabel)
      
    }

    // 门店订单也存在果粉优惠
    if (+orderType === 3) {
      refundGoods.memberDiscountAmount &&
        arr.push({
          label: '果粉优惠',
          price: refundGoods.memberDiscountAmount
        });

      // 最大可退金额+累计优惠项 < 优惠前金额 如果与零售价不一致 则使用其他优惠补齐
      if (maxRefundAmount + refundGoods.memberDiscountAmount < totalAmount) {
        arr.push({
          label: '其他优惠',
          price: totalAmount - maxRefundAmount - refundGoods.memberDiscountAmount
        });
      }
    }

    // 全国送订单 存在心享优惠的情况
    if (+orderType === 5) {
      // 最大可退金额+累计优惠项 < 优惠前金额 如果与零售价不一致 则使用其他优惠补齐
      if (maxRefundAmount < totalAmount) {
        arr.push({
          label: '其他优惠',
          price: totalAmount - maxRefundAmount
        });
      }
    }

    this.setData(
      {
        priceList: arr
      }
    )
  },
  /**
   * 获取商品金额
   * @returns
   */
  getGoodsAmount() {
    const { newGoodsInfo, refundGoods } = this.data
    let value = 0
    if (!refundGoods) {
      return
    }

    if (refundGoods.activityInfo && refundGoods.activityInfo.enjoyOneBuyNum && (Number(refundGoods.activityInfo.enjoyOneBuyNum) === Number(refundGoods.count))) {
      value = refundGoods.activityInfo.price * refundGoods.count
    } else {
      value = newGoodsInfo.goodsAmount
    }

    this.setData({
      goodsAmount: value || 0
    })
  },
  handleBtn(event) {
    const { type, text } = event.currentTarget.dataset
    const btnFunc = btnOperation[type]
    if (btnFunc) btnFunc.call(this)
    const { orderType, isDmOrder } = this.data
    sensorsReportTimely(SUBMIT, orderType, isDmOrder, {element_content: text, ...this._data.identityType})
  }
})
