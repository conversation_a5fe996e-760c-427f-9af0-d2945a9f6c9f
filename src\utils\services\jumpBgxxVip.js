/**
 * 跳转心享会员
 * @param {Number} status  默认 0 进入开通会员 1 进入续费
 */
let app = getApp()
let H5_WEB_DOMAIN = require('../config').baseUrl.H5_WEB_DOMAIN
const memberIndexUrl = `${H5_WEB_DOMAIN}/member/index` // 心享会员首页
const memberRenewUrl = `${H5_WEB_DOMAIN}/member/memberBuy?t=renew` // 心享会员续费页
const memberBuyUrl = `${H5_WEB_DOMAIN}/member/memberBuy` // 心享会员开通页

function getPagePath (url) {
  let pageParam = {
    pageUrl: encodeURIComponent(url)
  }
  let path = `/h5/pages/commonh5/index?pageParam=${JSON.stringify(pageParam)}`
  return path
}
function jumpH5Vip(type) {
  let url
  if (type === 'memberIndex') {
    url = getPagePath(memberIndexUrl)
  } else {
    url = getPagePath('jumpH5Vip')
  }
  console.log(url)
  wx.navigateTo({
    url: url
  })
}


function getH5VipUrl () {
  const {superVipStatus = 'C', superVipRenew = ''} = (app || (app = getApp())).globalData
  let url;
  if (superVipStatus === 'F') {
    // 静默登录版本去掉该逻辑
    // url =  Number(superVipRenew) === 0 ? memberRenewUrl : memberIndexUrl
    url = memberIndexUrl
  } else {
    url = memberBuyUrl
  }
  return url
}

// 跳转心享会员H5 v2版本
const jumpH5VipV2 = function(openValue) {
  const pageParam = {
    pageUrl: encodeURIComponent(openValue)
  }
  const url = `/h5/pages/commonh5/index?pageParam=${JSON.stringify(pageParam)}`
  wx.navigateTo({
    url
  })
}

module.exports = {
    jumpH5Vip,
    getH5VipUrl,
    jumpH5VipV2
}
