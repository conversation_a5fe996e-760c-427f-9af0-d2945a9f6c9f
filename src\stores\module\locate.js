import { observable, action } from 'mobx-miniprogram'

const USE_DEFAULT = 'useDefaultAddress'
const USE_LOCATE_AUTH = 'useLocateAuth'
const HAS_LOCATE_AUTH = 'hasLocateAuth'
const UNKNOW = 'unknow'
const scope = 'scope.userLocation'

const useDefault = wx.getStorageSync(USE_DEFAULT)
const { selectAddressInfo = {}} = wx.getStorageSync('bgxxSelectLocateInfo') || {}

const util = require('../../utils/util')
export default observable({
  useFreshDefaultLocation: selectAddressInfo.isDefaultLocation || false, // 次日达使用默认城市
  useDefault: useDefault === '' || useDefault, // 是否使用默认地址(目前只在及时达使用)
  userLocationAuth: wx.getStorageSync(USE_LOCATE_AUTH) || false, // 位置权限是否开启
  UNKNOW,
  hasLocationAuth: wx.getStorageSync(HAS_LOCATE_AUTH) || UNKNOW, // 是否有定位权限（unknow: 未知，true：有权限，false：没权限）
  changeUseDefault: action(function({ useDefault }) {
    this.useDefault = useDefault
    wx.setStorage({ key: USE_DEFAULT, data: useDefault })
  }),
  changeAuth: action(function({ auth }) {
    this.userLocationAuth = auth
    this.hasLocationAuth = auth
    wx.setStorage({ key: USE_LOCATE_AUTH, data: auth })
    wx.setStorage({ key: HAS_LOCATE_AUTH, data: auth })
  }),
  changeAuthBySetting: action(function({ authSetting }) {
    // AuthSetting: 用户授权设置信息
    // https://developers.weixin.qq.com/miniprogram/dev/api/open-api/setting/AuthSetting.html
    const userLocation = authSetting[scope]
    return (this.hasLocationAuth = (userLocation === void 0 ? UNKNOW : userLocation))
  }),
  checkHasAuth: action(function() {
    // 企微环境无法获取用户是否开启位置权限，默认开启
    if (util.checkIsQy()) {
      return true
    }
    // 微信朋友圈单页模式无法获取是否开启授权，默认开启
    const { sceneCopy = '' } = getApp().globalData
    if(sceneCopy === 1154){
      return true
    }
    return new Promise(resolve => {
      // https://developers.weixin.qq.com/miniprogram/dev/api/open-api/setting/wx.getSetting.html
      wx.getSetting({
        success: (result) => {
          console.log('checkHasAuth wx.getSetting success', result)
          // 如果上次拒绝了授权弹窗，再次调用wx.getSetting时，直接返回 { errMsg: "getSetting:ok", authSetting: { scope.userLocation: false } }
          // 除非删除小程序或者在开发者工具清除授权数据，才会再次唤起弹窗
          const locationAuth = this.changeAuthBySetting(result)
          resolve(locationAuth)
        },
        fail(err) {
          console.log('checkHasAuth wx.getSetting fail',err)
          resolve(UNKNOW)
        }
      })
    })
  }),
  tryOpenAuth: action(function() {
    return new Promise(resolve => {
      wx.openSetting({
        success: (result) => {
          console.log('tryOpenAuth wx.openSetting success')
          resolve(this.changeAuthBySetting(result))
        },
        fail() {
          resolve(UNKNOW)
          console.log('tryOpenAuth wx.openSetting fail')
        }
      })
    })
  }),
  noAddressConfirm({ app = getApp(), scene = 'homeDelivery', asyncHomeStore = 0 } = {}) {
    return app.showModalPromise({
      content: '您当前未选地址，无法下单，请先确认您的地址信息',
      confirmText: '选择地址',
      cancelText: '我知道了',
      showCancel: true
    }).then((argee) => {
      argee && wx.navigateTo({
        url: scene === 'homeDelivery' ? '/homeDelivery/pages/addressList/index' : `/bgxxShop/pages/chooseStore/index?asyncHomeStore=${Number(asyncHomeStore)}`
      })
    })
  },
  changeUseFreshDefault: action(function({ useDefault }) {
    console.log('changeUseFreshDefault', useDefault)
    this.useFreshDefaultLocation = useDefault || false
  }),
})
