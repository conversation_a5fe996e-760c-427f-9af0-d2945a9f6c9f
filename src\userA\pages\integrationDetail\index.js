// 积分明细 index.js
var commonObj = require('../../../source/js/common').commonObj;
var bg_jifen = require('../../source/image-base64/bg_jifen_top');
const sensors = require('../../../utils/report/sensors')
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    purchase: [],
    List: [],
    allInte: 0,
    lastData: '',
    myjifenbg: bg_jifen.bg,
    memberIRL: [],
    isReady: false,
    hasInte: false,
    pageNum: 1
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    const that = this
    if (app.checkSignInsStatus()) {
      // 用户已登录
      if (that.data.List.length === 0) {
        that.getInteDetail(that.data.pageNum) 
      }
    } else {
      app.showSignInModal()
    }
    // 浏览页面上报神策
    sensors.pageShow('userIntegrateDetailPage')
  },
  onReachBottom: function () {
    var that = this;
    let page = that.data.pageNum;
    if (that.data.hasInte) {
      ++page;
      that.getInteDetail(page);
      that.setData({
        pageNum: page
      })
    }
  },

  //******************功能函数*******************/
  // 查询积分明细
  getInteDetail: function (page) {
    let that = this;
    var user = wx.getStorageSync('user')
    let options = {
      url: '/api/v1/customer/integral/changeList/' + user.userID + '/' + page,
    }
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    commonObj.requestNewData(options, function (res) {
      
      if (Number(res.data.errorCode) === 0) {
        var expireIntegral = res.data.data.expireIntegral;
        var expireTime = res.data.data.expireDate;
        var memberIRList = res.data.data.changeList;
        var memberlist = that.data.List;  
        if (memberIRList.length > 0) {
          for (let item in memberIRList) {
            if (parseInt(memberIRList[item].changedValue) > 0){
              memberIRList[item].symbol = true;
            }else{
              memberIRList[item].symbol = false;
            }
            // 将数据push进数组然后渲染
            memberlist.push(memberIRList[item]);
          }
          
          that.setData({ List: memberlist, hasInte: true,})
        }
        that.setData({ expireDate: expireTime, expireInteg: expireIntegral, allInte: res.data.data.integralBalance})
      } else if (that.data.List){
        return;
      }else{
        that.setData({ hasInte: false, allInte: 0 });
      }
    }, '', function () {
      wx.hideLoading();
    })
  },
  //******************End*******************/
  navigateInfo: function () {
    wx.navigateTo({
      url: '/h5/pages/intergraRuleInfo/index'
    })
  }
})