{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [{"value": "package.json", "type": "file"}, {"value": "commitlint.config.js", "type": "file"}, {"value": ".cz-config.js", "type": "file"}, {"value": "package-lock.json", "type": "file"}, {"value": "__tests__", "type": "folder"}, {"value": "src/utils/report/sensorsReport.js", "type": "file"}], "include": []}, "skeleton-config": {"global": {"loading": "shine", "text": {"color": "#EEEEEE"}, "image": {"shape": "rect", "color": "#F5F5F5", "shapeOpposite": []}, "button": {"color": "#F5F5F5", "excludes": []}, "pseudo": {"color": "#EFEFEF", "shape": "rect", "shapeOpposite": []}, "excludes": [], "remove": [], "empty": [], "hide": [], "grayBlock": [], "showNative": false, "backgroundColor": "transparent", "mode": "fullscreen", "templateName": "skeleton", "cssUnit": "rpx", "decimal": 4}}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "package.json", "miniprogramNpmDistDir": "./src"}], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": ["typescript", "sass"], "ignoreUploadUnusedFiles": true, "useStaticServer": true, "condition": true, "skylineRenderEnable": false, "compileWorklet": false, "localPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.7.12", "appid": "wx1f9ea355b47256dd", "projectname": "%E7%99%BE%E6%9E%9C%E5%9B%AD%2B", "cloudfunctionTemplateRoot": "", "miniprogramRoot": "src/", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"miniprogram": {"list": [{"name": "评价详情页", "pathName": "userB/pages/evaluation/evaluationDetail/index", "query": ""}, {"name": "pages/orderList/index", "pathName": "pages/orderList/index", "query": "", "scene": null}, {"name": "邀请好友", "pathName": "fightGroups/pages/inviteFriends/index", "query": "", "scene": null}, {"name": "三无退货任务", "pathName": "userB/pages/noReasonRefund/index", "query": "", "scene": null}, {"name": "pages/index/index", "pathName": "pages/homeDelivery/index", "query": "to=\"AllOrderDetail\"&templateObj=true&goodsOrderID=3095786&payOrderID=3313745&payStatus=\"SUCCESS\"&type=A&myAllOrderObj={type:\"A\"}", "scene": 1045}, {"name": "会员页", "pathName": "pages/index/index", "query": "", "scene": null}, {"name": "会员俱乐部", "pathName": "userA/pages/myGrade/index", "query": "", "scene": null}, {"name": "心享商城", "pathName": "pages/xxshop/index/index", "query": "", "scene": null}, {"name": "pages/xxshop/index/index", "pathName": "pages/xxshop/index/index", "query": "", "scene": 1011}, {"name": "商品详情页", "pathName": "pages/homeDelivery/index", "query": "home2GoodsDetail={\"goodsID\": \"52291\"}", "scene": null}, {"name": "一元弹窗", "pathName": "homeDelivery/pages/oneYuanMask/index", "query": "", "scene": null}, {"name": "pages/homeDelivery/index", "pathName": "pages/homeDelivery/index", "query": "to=commonh5&pageParam={\"pageUrl\":\"https%3A%2F%2Fx.pagoda.com.cn%2Factivity%2Fmodel%3FactivityID%3D8541%26from%3Dgroupmessage%26isappinstalled%3D0\"}", "scene": null}, {"name": "homeDelivery/pages/sharePage/index", "pathName": "homeDelivery/pages/sharePage/index", "query": "", "scene": null}, {"name": "红包", "pathName": "h5/pages/redEnvelope/index", "query": "", "scene": null}, {"name": "公众号进一元吃水果", "pathName": "homeDelivery/pages/unitary/index", "query": "utm_term=un_pay&utm_campaign=official_push&wxOpenId=ogSx0w3qlQYqWfykJjGkc87mitB4&utm_medium=wxcd4f04303cc62998&utm_source=lingyi", "scene": null}, {"name": "一元购首页", "pathName": "homeDelivery/pages/unitary/index", "query": "utm_campaign=yiyuan&utm_term=ad1&gdt_vid=wx0rru6gljfvc7z4&utm_source=pyq&weixinadinfo=2129148548.wx0rru6gljfvc7z4.0.0", "scene": null}, {"name": "userA/pages/baldetail/index", "pathName": "userA/pages/baldetail/index", "query": "", "scene": null}, {"name": "pages/homeDelivery/index", "pathName": "pages/homeDelivery/index", "query": "to=home2Topic&homeDeliveryObj={\"activityID\": \"8545\"}", "scene": null}, {"name": "公共h5", "pathName": "pages/homeDelivery/index", "query": "to=commonLink&pageUrl=https://baiguoyuan-test.01lb.com.cn/h5", "scene": null}, {"name": "心享购物车", "pathName": "bgxxShop/pages/cart/index", "query": "", "scene": null}, {"name": "周期购订单", "pathName": "bgxxUser/pages/cycleBuy/index", "query": "", "scene": null}, {"name": "订单", "pathName": "pages/orderList/index", "query": "", "scene": null}, {"name": "心享分享商品详情页", "pathName": "pages/xxshop/index/index", "query": "toDetail={\"goodsID\": 40927}", "scene": null}, {"name": "分享进入专题", "pathName": "pages/xxshop/index/index", "query": "toSubjectActivity={\"activityID\": 40927}", "scene": null}, {"name": "退款成功页", "pathName": "bgxxUser/pages/refundSuccess/index", "query": "", "scene": null}, {"name": "申请售后", "pathName": "bgxxUser/pages/requestPostSale/index", "query": "", "scene": null}, {"name": "homeDelivery/pages/addressList/index", "pathName": "homeDelivery/pages/addressList/index", "query": "", "scene": null}, {"name": "pages/homeDelivery/index", "pathName": "pages/homeDelivery/index", "query": "", "scene": null}, {"name": "首页", "pathName": "pages/homeDelivery/index", "query": "", "scene": null}, {"name": "userB/pages/memberCode/index", "pathName": "userB/pages/memberCode/index", "query": "to=home2Topic&homeDeliveryObj={\"activityID\": \"986\"}", "scene": null}, {"name": "扫码购", "pathName": "scancodeTobuy/pages/scan/index", "query": "", "scene": null}, {"name": "扫码购确认订单", "pathName": "scancodeTobuy/pages/confirmOrder/index", "query": "", "scene": null}, {"name": "扫码购支付加载", "pathName": "scancodeTobuy/pages/payloading/index", "query": "", "scene": null}, {"name": "扫码购支付成功", "pathName": "scancodeTobuy/pages/paySuccess/index", "query": "", "scene": null}, {"name": "扫码购订单列表", "pathName": "scancodeTobuy/pages/orderList/index", "query": "", "scene": null}, {"name": "bgxxUser/pages/complaints/refundGoods/index", "pathName": "bgxxUser/pages/complaints/refundGoods/index", "query": "", "scene": null}, {"name": "扫码购订单详情", "pathName": "scancodeTobuy/pages/orderDetail/index", "query": "", "scene": null}, {"name": "三无退货", "pathName": "store/pages/refundGoodsList/index", "query": "fromPage=vegetablesOrder&orderTicket=200421008716095516189", "scene": null}, {"name": "扫码购退货提交成功", "pathName": "scancodeTobuy/pages/refundSubmit/index", "query": "", "scene": null}, {"name": "scancodeTobuy/pages/payOrder/index", "pathName": "scancodeTobuy/pages/payOrder/index", "query": "", "scene": null}, {"name": "scancodeTobuy/pages/confirmOrder/index", "pathName": "scancodeTobuy/pages/confirmOrder/index", "query": "", "scene": null}, {"name": "scancodeTobuy/pages/paySuccess/index", "pathName": "scancodeTobuy/pages/paySuccess/index", "query": "", "scene": null}, {"name": "pages/homeDelivery/index", "pathName": "pages/homeDelivery/index", "query": "", "scene": null}, {"name": "scancodeTobuy/pages/paySuccess/index", "pathName": "scancodeTobuy/pages/paySuccess/index", "query": "", "scene": null}, {"name": "pages/homeDelivery/index", "pathName": "pages/homeDelivery/index", "query": "", "scene": null}, {"name": "scancodeTobuy/pages/confirmOrder/index", "pathName": "scancodeTobuy/pages/confirmOrder/index", "query": "", "scene": null}, {"name": "pages/homeDelivery/index", "pathName": "pages/homeDelivery/index", "query": "", "scene": null}, {"name": "scancodeTobuy/pages/refundDetail/index", "pathName": "scancodeTobuy/pages/refundDetail/index", "query": "", "scene": null}, {"name": "pages/homeDelivery/index", "pathName": "pages/homeDelivery/index", "query": "", "scene": null}, {"name": "智慧零售", "pathName": "pages/homeDelivery/index", "query": "chan_refer_app_id=wx9d4f5f62ea059f08&chan_id=8_ac3e76c2e5721f5f&chan_wxapp_scene=1037", "scene": null}, {"name": "素生鲜搜索", "pathName": "bgxxShop/pages/search/index", "query": "", "scene": null}, {"name": "品类", "pathName": "pages/category/index", "query": "", "scene": null}, {"name": "购物车", "pathName": "homeDelivery/pages/shopCart/index", "query": "", "scene": null}, {"name": "pages/homeDelivery/index?to=duiba&sa_utm=M", "pathName": "pages/homeDelivery/index", "query": "to=duiba&sa_utm=M", "scene": null}, {"name": "pages/xxshop/index/index?toSubjectActivity={\"activityID\":\"10222\"}", "pathName": "pages/xxshop/index/index", "query": "toSubjectActivity={\"activityID\":\"10222\"}", "scene": null}, {"name": "h5/pages/commonh5/index", "pathName": "h5/pages/commonh5/index", "query": "pageParam={\"pageUrl\":\"https://eshop-h5-web.dsh5.tencent-prod.pagoda.com.cn/app/download\"}", "scene": null}, {"name": "优惠券列表页", "pathName": "userA/pages/coupon/index", "query": "", "scene": null}, {"name": "智慧零售品类页", "pathName": "pages/category/index", "query": "goodsID=40630", "scene": null}, {"name": "智慧零售落地品类页", "pathName": "pages/category/index", "query": "goodsID=4289&storeID=492&chan_refer_app_id=wx9d4f5f62ea059f08", "scene": null}, {"name": "pages/orderList/index", "pathName": "pages/orderList/index", "query": "type=B", "scene": null}, {"name": "优惠券适用商品", "pathName": "userA/pages/couponGoods/index", "query": "", "scene": null}, {"name": "选择地址", "pathName": "homeDelivery/pages/addressList/index", "query": "", "scene": null}, {"name": "水果外卖", "pathName": "pages/category/index", "query": "", "scene": null}, {"name": "精选食材优惠券适用商品", "pathName": "bgxxShop/pages/couponGoods/index", "query": "", "scene": null}, {"name": "会员码", "pathName": "userB/pages/memberCode/index", "query": "", "scene": null}, {"name": "fightGroups/pages/fightGroups/index", "pathName": "fightGroups/pages/fightGroups/index", "query": "to=commonh5&pageParam={\"pageUrl\": \"https://h5-web.pagoda.com.cn/h5/collectCard https://h5-web.pagoda.com.cn/h5/collectCard?t=_x<PERSON>hang\"}", "scene": null}, {"name": "次日达订单详情", "pathName": "bgxxUser/pages/orderDetail/index", "query": "orderID=3111218&isPay=true", "scene": null}, {"name": "选择门店", "pathName": "bgxxShop/pages/chooseStore/index", "query": "", "scene": null}, {"name": "搜索", "pathName": "bgxxShop/pages/search/index", "query": "", "scene": null}]}}, "srcMiniprogramRoot": "src/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "skeletonConfig": {"global": {"loading": "shine", "text": {"color": "#EEEEEE"}, "image": {"shape": "rect", "color": "#F5F5F5", "shapeOpposite": []}, "button": {"color": "#F5F5F5", "excludes": []}, "pseudo": {"color": "#EFEFEF", "shape": "rect", "shapeOpposite": []}, "excludes": [], "remove": [], "empty": [], "hide": [], "grayBlock": [], "showNative": false, "backgroundColor": "transparent", "mode": "fullscreen", "templateName": "skeleton", "cssUnit": "rpx", "decimal": 4}, "pages": {}}}