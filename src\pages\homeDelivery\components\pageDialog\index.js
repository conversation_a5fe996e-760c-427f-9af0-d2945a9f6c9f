const app = getApp()
const commonObj = require('../../../../source/js/common').commonObj
const config = require('../../../../utils/config')
const sensors = require('../../../../utils/report/sensors')
const activity = require('../../../../utils/activity')
import commonToUseCoupon from '../../../../utils/toUseCoupon'

Component({
  properties: {
    /**
     * 是否为灰色主题
     */
    isGrayTheme: {
      type: Boolean,
      value: false
    },
    isShowDialog: {
      type: Boolean,
      value: false
    },
    // 是否为获取数据逻辑
    fetchDialog: {
      type: Boolean,
      value: false
    },
    userInfo: {
      type: Object,
      value: {
        customerID: -1
      }
    },
    addressInfo: {
      type: Object,
      value: {
        cityID: -1,
        storeID: -1
      }
    },
  },

  data: {
    picDomain: config.baseUrl.PAGODA_PIC_DOMAIN,
    dialogObj: {},
    // 保证滚动条在最上面
    scrollViewTop: 1
  },

  observers: {
    fetchDialog(fetch) {
      if (!(fetch && this.needDialog())) {
        return this.triggerDataLoaded()
      }
      this.getPageDialog()
    },
    'isShowDialog' (isShow) {
      if (!(isShow && this.needDialog())) {
        return this.triggerHide()
      }
      this.getPageDialog()
    }
  },

  lifetimes: {
    created() {
      this._data = {
        dialogData: null
      }
    },
  },

  methods: {
    needDialog() {
      const address = this.properties.addressInfo || {}
      const optionStore = app.globalData.optionStore || {}
      const noDialog = !address.cityID || address.cityID === -1 || app.globalData.isScanCode || optionStore.storeID
      return !noDialog
    },
    /**
     * @description 显示红包弹窗
     * 优惠券过滤
     * 1. 过滤 app 专享券
     * 2. 过滤已过期优惠券
     *
     * send_coupon_params 过滤不能插入微信卡包的情况
     * 1. stockId 为空的优惠券
     */
    showRedPacket(data) {
      // 显示弹窗数据后，清空缓存
      this._data.dialogData = null;
      const {
        sendCouponMerchant: send_coupon_merchant = "",
        sendCouponParams = [],
        sign = ""
      } = data.wxminiCardCoupon || {};
      // channelSeparation：使用渠道 -  A app券 W百果园公众号 I心享小程序
      // couponStatus: 0 - 已过期
      data.couponList = (data.couponList || []).filter(v => {
        const canUseChannel = v.channelSeparation
          .split(",")
          .filter(c => !["A", "I", "W"].includes(c));
        return v.valid && !!canUseChannel.length;
      });
      const send_coupon_params = sendCouponParams
        .filter(item => !!item.stockId)
        .map(
          ({
            outRequestNo: out_request_no,
            stockId: stock_id,
            couponCode: coupon_code
          }) => ({ out_request_no, stock_id, coupon_code })
        );
      if (!data.couponList.length) {
        return this.triggerHide();
      }
      this.setData({
        isShowBounce: true,
        scrollViewTop: 0,
        dialogObj: data,
        coupon: {
          send_coupon_params,
          send_coupon_merchant,
          sign
        }
      });
      this.exposureReport()
      console.log("插卡包参数：", this.data.coupon);
      commonObj.updatePopupShowTime();
    },

    /**
     * 曝光上报
     * 1. 普通弹窗
     * 3. 普通活动弹窗
     * 4. 活动红包弹窗
     */
    exposureReport() {
      const { bannerID, name, popupType } = this.data.dialogObj

      const element_code = {
        1: 180010001,
        3: 180011001,
        4: 180012001,
      }[popupType]

      sensors.adExposure({
        banner_id: bannerID,
        banner_name: name,
        bannerType: '4',
        Position: 0,
        element_code,
      })
    },

    /**
     * 弹窗关闭上报
     */
    closeReport() {
      const { popupType } = this.data.dialogObj
      const reportData = {
        1: {
          blockName: '普通弹窗',
          blockCode: 180010,
          element_code: '180010002',
          element_name: '关闭普通活动弹窗',
        },
        3: {
          blockName: '普通活动弹窗',
          blockCode: 180011,
          element_code: '180011002',
          element_name: '关闭普通活动弹窗',
        },
        4: {
          blockName: '活动红包弹窗',
          blockCode: 180012,
          element_code: '180012002',
          element_name: '关闭普通活动弹窗',
        }
      }[popupType]

      sensors.clickReport(reportData)
    },

    /**
     * @desc 获取弹窗（普通广告弹窗、红包雨弹窗）
     */
    async getPageDialog() {
      const that = this
      const { customerID = -1 } = that.data.userInfo
      const { cityID = -1, storeID = -1, cityCode, storeCode } = that.data.addressInfo
      if (!storeCode) return this.triggerHide()
      const params = {
        customerID,
        cityID,
        storeID,
        cityCode,
        storeCode
      }
      try {
        const { fetchDialog } = this.data
        const dialogData = this._data.dialogData
        const res = dialogData ? { data: dialogData } : await app.api.getHomepageDialog(params)
        const data = res.data
        if (!data || !data.popupType){
          return fetchDialog
            ? this.triggerDataLoaded()
            : this.triggerHide()
        }
        if (fetchDialog) {
          this._data.dialogData = data
          this.triggerDataLoaded(false)
          return
        }
        if (data.popupType === '4' || data.popupType === '3') {
            return this.showRedPacket(data)
        }
        // 普通广告弹窗，4小时弹一次
        const isShowTime = commonObj.checkPopupIsShowTime()
        // 不弹窗，也要抛出事件
        if (!isShowTime) return this.triggerHide()
        this.setData({
          isShowBounce: Number(data.openType) !== -1,
          scrollViewTop: 0,
          dialogObj: data
        })
        if (this.data.isShowBounce) {
          this.reportBannerExpose(data)
          this.exposureReport()
        }
      } catch (error) {
        console.log('error',error)
        this.triggerHide()
      }
    },

    /**
     * 插入卡包的回调函数
     */
    onCouponComfirmBtnTap ({ detail }) {
      console.log('插入微信卡包detail', detail)
      let {errcode, send_coupon_result = []} = detail
      let isFail = errcode !== 'OK' || send_coupon_result.every(({code}) => code !== 'SUCCESS' && code !== 'DUPREQUEST')
      isFail && console.error('未插入微信卡包')
    },

    /**
     * 去使用红包雨弹窗
     */
    toUsecoupon({ currentTarget: { dataset: { coupon = {} } } }) {
      this.setData({
        isShowBounce: false
      })

      // 优惠券品类 T：水果券，F：素生鲜券
      const { couponCategory } = coupon
      const couponSource = couponCategory === 'T' ? 'fruit' : 'fresh'

      // 等插入卡包的提示消失再跳转
      setTimeout(() => {
        commonToUseCoupon({ app, coupon, couponSource })
      }, 1500)

      // 上报神策
      const { bannerID, name, popupType } = this.data.dialogObj
      sensors.track('MPClick', 'categoryRedPaperPopupToUse', {
        banner_id: String(bannerID),
        banner_name: name,
        screen_type:'及时达',
        banner_type:'首页弹窗'
      })

      const [
        element_code,
        subclass
      ] = {
        3: [180011001, '普通活动弹窗'],
        4: [180012001, '活动红包弹窗'],
      }[popupType]

      sensors.adClick({
        bannerType: '4',
        subclass,
        banner_id: String(bannerID),
        banner_name: name,
        Position: 0,
        element_code,
      })
    },

    /**
     * 关闭红包雨弹窗
     */
    closeRedPaperPopup() {
      this.hideBounce()
      const { bannerID, name } = this.data.dialogObj
      // 上报神策
      sensors.track('MPClick', 'categoryRedPaperPopupClose', { banner_id: String(bannerID), banner_name: name })
    },
    reportBannerExpose(data){
      const { bannerID = '',name='',bannerName='' } = data
      sensors.track('banner_show', '', {
        screen_name:'首页',
        screen_type:'及时达',
        screen_code:'1800',
        banner_id:String(bannerID),
        banner_name:name || bannerName,
        banner_type:'首页弹窗'
      })
    },
    /**
     * 点击广告弹窗跳转
     */
    navigateToActivity(event) {
      const { item } = event.currentTarget.dataset
      if (!!item.isDefault) {
        return
      }
      activity.toActivityPage(item)
      this.setData({
        isShowBounce: false
      })
      this.triggerHide()
      // 点击广告弹窗上报神策
      const { bannerID, name, popupType } = this.data.dialogObj

      const [
        element_code,
        subclass
      ] = {
        1: [180010001, '普通弹窗'],
      }[popupType]

      sensors.adClick({
        bannerType: '4',
        subclass,
        banner_id: String(bannerID),
        banner_name: name,
        Position: 0,
        element_code,
      })
    },

    /**
     * 关闭弹窗，销毁组件，不销毁的话，父组件切换登录态会导致弹窗顺序有误
     */
    hideBounce(e) {
      // iphone上会偶现动画执行完之后，再次展示
      setTimeout(() => {
        this.setData({
          isShowBounce: false
        })
      }, 200)
      this.triggerHide()
      // 关闭广告弹窗上报神策
      const { popupType, bannerID, name } = this.data.dialogObj
      if(popupType && String(popupType) === '1') {
        sensors.track('MPClick', 'closeAdvertisementPopup', { banner_id: String(bannerID), banner_name: name })
      }
      this.closeReport()
    },
    /**
     * @description 触发数据加载完成事件
     * @param { boolean } useData 是否使用data中的数据
     */
    triggerDataLoaded(useData = true) {
      const dialogObj = useData
        ? this.data.dialogObj
        : this._data.dialogData
      const { popupType, giftBagEstimate } = dialogObj
      this.triggerEvent('loadedData', {
        // 是否关闭自优先于新人红包展示
        dialogFirst: popupType === '4' && Number(giftBagEstimate) === 1
      })
    },
    triggerHide () {
      this.triggerEvent('hideBounce')
    }
  }
})
