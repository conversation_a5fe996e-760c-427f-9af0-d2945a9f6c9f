// userA/pages/coupons/index.js
const app = getApp();
import sensors from '../../../utils/report/sensors.js';
import toUseCoupon from '../../../utils/toUseCoupon.js'
var util = require('../../../utils/util.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    curTab: 'fruit',
    tabsFruit: {
      label: '及时达',
      type: 'fruit',
      // 该tab对应的适用业务类型
      applicableBizTypes: [0, 2],
      tabs: [],
      // 券种类选择索引
      tabActive: 0
    },
    tabsFood: {
      label: '次日达',
      type: 'food',
      // 该tab对应的适用业务类型
      applicableBizTypes: [4],
      tabs: [],
      // 券种类选择索引
      tabActive: 0
    },
    // 展示兑换码×
    showClear: false,
    // 及时达tab数据
    fruitData: {
      // 券列表
      coupons: [],
      // 控制是否显示缺省图片
      showDefault: false,
      // 是否有更多优惠券
      hasMore: true
    },
    // 次日达tab数据
    foodData: {
      // 券列表
      coupons: [],
      // 控制是否显示缺省图片
      showDefault: false,
      // 是否有更多优惠券
      hasMore: true
    },
    // 自动滚动的tab位置
    scrollIntoView: 'tab0'
  },
  _data: {
    // 如果是从小程序推送消息进入，则会有一个券码
    couponCode: '',

    // 及时达查询参数
    fruitCondition: {
      // 查询参数-第几页
      page: 1,
      // 查询参数-每页多少条
      size: 10,
      // 查询参数-营销资产ID列表
      defineIds: [],
      // 查询参数-适用业务类型列表
      applicableBizTypes: [0, 2]
    },
    // 次日达查询参数
    foodCondition: {
      // 查询参数-第几页
      page: 1,
      // 查询参数-每页多少条
      size: 10,
      // 查询参数-营销资产ID列表
      defineIds: [],
      // 查询参数-适用业务类型列表
      applicableBizTypes: [4]
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const curTab = ['fruit', 'food'][options.activeTab || 0]
    this._data.couponCode = options.couponCode
    this.setData({
      curTab
    })
  },
  /**
   * 获取优惠券列表数量
   * @param {'fruit' | 'food'} type
   */
  async getCouponNum (type = 'fruit') {
    // 适用业务类型多选
    // 点击的是及时达还是次日达 拿到对象
    const targetBigTabs = {
      fruit: {
        name: 'tabsFruit',
        objVal: this.data.tabsFruit,
      },
      food: {
        name: 'tabsFood',
        objVal: this.data.tabsFood
      }
    }[type || 'fruit']
    const applicableBizTypes = targetBigTabs.objVal.applicableBizTypes
    const {
      userID: customerID
    } = wx.getStorageSync('user')
    const { data } = await app.api.getCouponGroupNum({
      // 会员ID
      customerID,
      // 适用业务类型（0-门店下单；2-及时达；4-次日达；6-无人零售）
      // applicableBizType: 2,
      // 适用业务类型多选（0-门店下单；2-及时达；4-次日达；6-无人零售）
      applicableBizTypes,
      // 过滤app的券统计
      notOnlyApplicableChannels: [10000],
      showFruitGradeCouponCount: 'Y'
    })
    const { groupNums = [] } = data || {}
    const newTabList = groupNums.map((item, index) => ({
      label: item.name,
      num: item.num,
      type: item.defineIds,
      index: item.index || index
    }))

    this.setData({
      [targetBigTabs.name + '.tabs']: newTabList
    })
  },
  /**
   * 获取优惠券列表
   */
  async getCoupons() {
    const {
      userID
    } = wx.getStorageSync('user')
    const {
      curTab
    } = this.data
    // 条件
    const condition = this._data[curTab=== 'food' ? 'foodCondition' : 'fruitCondition']
    // 如果是第1页 则刷新分类数量
    if (condition.page === 1) {
      this.getCouponNum(curTab)
    }
    // 获取中台优惠券列表接口
    const { data: { list: couponList = [] }, page: { totalPages = 1 } = {} } = await app.api.getNotUsedCouponList({
      customerID: userID || -1,
      pageNumber: condition.page,
      pageSize: condition.size,
      applicableBizTypes: condition.applicableBizTypes,
      defineIds: condition.defineIds,
      showFruitGradeCoupon: 'Y'
    })
    // 现有列表
    let coupons = this.data[curTab === 'food' ? 'foodData' : 'fruitData'].coupons
    const hasMore = totalPages > condition.page++
    // 如果是第1页直接赋值 否则push
    if (condition.page <= 2) {
      coupons = couponList
    } else {
      coupons.push(...couponList)
    }
    this.setData({
      [curTab === 'food' ? 'foodData.coupons' : 'fruitData.coupons']: coupons,
      [curTab === 'food' ? 'foodData.showDefault' : 'fruitData.showDefault']: true,
      [curTab === 'food' ? 'foodData.hasMore' : 'fruitData.hasMore']: hasMore
    })
    // 跳转到对应券列表
    const couponCode = this._data.couponCode
    this._data.couponCode = ''
    const couponItem = couponCode && coupons.find(v => v.couponCode === couponCode)
    // 如果能找到相关券,并且券不是免运券,并且是部分可用券,跳转到优惠券使用页
    couponItem && couponItem.couponWay !== '5' && couponItem.couponType === '4' && this.toUsePage({ detail: couponItem })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    //  上报页面浏览事件
    sensors.pageScreenView()

    if (!app.checkSignInsStatus()) { // 用户未登录
      app.showSignInModal()
      return false
    }
    // 如果从买好菜首页优惠券弹窗进入,则显示精选食材tab
    const {
      prePageName
    } = app.globalData
    if (['foodsModule'].includes(prePageName)) {
      this.setData({
        curTab: 'food'
      })
    }
    this.getCoupons()
  },
  /**
   * 切换及时达/次日达tab
   */
  switchTab(e) {
    const {
      type
    } = e.currentTarget.dataset
    if (type === this.data.curTab) { return }
    this.setData({
      curTab: type,
      showDefault: false,
      coupons: []
    })
    this._data.fruitCondition.page = 1
    this.getCoupons()
  },
  /**
   * 切换资产tab
   * @param {object} e 事件对象
   */
  switchAssertTab(e) {
    // 点击的对象
    const { type: clickType, index: clickIndex } = e.currentTarget.dataset
    // 点击的是及时达还是次日达 拿到对象
    const targetBigTabs = {
      fruit: {
        name: 'tabsFruit',
        objVal: this.data.tabsFruit,
        conditionField: 'fruitCondition'
      },
      food: {
        name: 'tabsFood',
        objVal: this.data.tabsFood,
        conditionField: 'foodCondition'
      }
    }[clickType || 'fruit']
    // 如果已经聚焦了当前的tab 则不处理
    if (targetBigTabs.objVal.tabActive === clickIndex) {
      return
    }
    this.setData({
      scrollIntoView: 'tab' + clickIndex,
      [targetBigTabs.name + '.tabActive']: clickIndex
    })
    this._data[targetBigTabs.conditionField].defineIds = targetBigTabs.objVal.tabs[clickIndex].type
    this._data[targetBigTabs.conditionField].page = 1
    this.getCoupons()
  },
  // 跳转优惠券历史
  toHistory() {
    wx.navigateTo({
      url: '../couponsHistory/index'
    })
  },
  toUsePage(e) {
    const { curTab } = this.data
    const couponSource = curTab === 'fruit' ? 'fruit' : 'fresh'
    toUseCoupon({ coupon: e.detail, app, couponSource })
  },
  // 监听输入框变化
  bindInputHandler: function (e) {
    var that = this,
      discountCode = e.detail.value;
    discountCode = discountCode.replace(/(^\s*)|(\s*$)/g, "")
    discountCode = discountCode.replace(/[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/ig, "")
    that.setData({
      discountCode: discountCode || '',
    })
  },
  // 新增兑换优惠券功能
  exchange: util.throttle(async function () {
    const that = this
    const user = wx.getStorageSync('user')
    const cityList = wx.getStorageSync('timelyCity') || {}
    const options = {
      customerID: user.userID,
      cityID: cityList.cityID || -1,
      cityCode: cityList.cityCode || -1,
      userToken: user.userToken,
      privilegeCode: that.data.discountCode,
      couponDetailCode: that.data.discountCode,
    }
    if (that.data.discountCode) {
      try {
        await app.api.redeemCoupon(options, true)
        wx.showToast({
          title: '兑换成功',
          icon: 'none',
          duration: 2000
        })
        setTimeout(() => {
          that._data.fruitCondition.page = 1
          that.getCoupons()
          that.setData({
            discountCode: ''
          })
        }, 2000)
      } catch (err) {
        wx.showToast({
          title: err.description || err.errorMsg || '兑换码有误',
          icon: 'none',
          duration: 2000
        })
      }
    } else {
      wx.showToast({
        title: '请输入兑换码',
        icon: 'none',
        duration: 2000
      })
    }
  }, 1500),
  /**
   * 清除输入内容
   */
  clearDiscountCode(){
    // 解决设置为空后 一闪 文字依然存在的问题
    setTimeout(()=>{
      this.setData({
        discountCode: ''
      })
    },10)
  },
  /**
   * 输入框聚焦
   */
  bindFocusHandler(){
    this.setData({
      showClear:true
    })
  },
  /**
   * 输入框失焦
   */
  bindBlurHandler(){
    this.setData({
      showClear:false
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottomEvent() {
    const hasMore = this.data[this.data.curTab === 'food' ? 'foodData' : 'fruitData'].hasMore

    if (hasMore) {
      this.getCoupons()
    }
  },
  /**
   * 用户滑动swiper
   * @param {object} e 滑动事件
   */
  handleSwiperChange(e) {
    // 如果是用户触摸 则将index的值复制给聚焦的tab
    if (e.detail.source === 'touch') {
      this.setData({
        curTab: e.detail.current === 0 ? 'fruit' : 'food'
      })
      // 如果没数据会刷新券列表
      if ((e.detail.current === 0 && !this.data.fruitData.coupons.length) ||
        (e.detail.current === 1 && !this.data.foodData.coupons.length)
      ) {
        this.getCoupons()
      }
    }
  }
})
