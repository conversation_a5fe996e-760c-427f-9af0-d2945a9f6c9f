import { userApi } from '../api/pandafresh.api'
import userInfoApi from '../api/user.api'
import memberApi from '../api/member.api'
import { bgxxGetCustomerInfo } from '../api/bgxxUser.api'
import { setUpdateTimeCache, getUpdateTimeCache } from './storageService'
import { getEncryptKey } from './encrypt'
import { parallelPromise } from '../utils/promise'
import { outLoginType } from '../source/const/globalConst'
const request = require('../utils/request.js')
const util = require('../utils/util.js')
const { 
  subProtocolTypeEnum, 
  TURING,
} = require('../utils/config')
const loginMixin = require('../mixins/loginMixin')
const commonObj = require('../source/js/common').commonObj
const GRAY_SCALE_ENUM = require('../source/const/grayScaleEnum')
const sensors = require('../utils/report/sensors')
let isGetInGrayScale = false
import userStore from '../stores/userStore'
import { superVipStatusEnum } from '~/source/const/user'
import { sleep } from '~/utils/time'
import { objectGetNotVoid } from '../utils/cyclomatic'
import { isFromURLScheme, decodeURLSchemeQuery } from '../pages/homeDelivery/utils/forwardNavigate'


//查询当前用户是否为团长
export async function checkUserIsStoreLeader() {
  const userToken = wx.getStorageSync("token")
  if (!userToken) {
    wx.setStorageSync('isStoreLeader', 'N')
    return
  }
  try {
    const { userID } = wx.getStorageSync('user')
    if (!userID) {
      throw new Error()
    }
    let result = await request.post({ url: userApi.checkIsLeader, data: { customerID: userID }, isLoading: false })
    let isStoreLeader = 'N'
    if (result.errorCode === 0) {
      const { join_mode, community_store_status } = result.data
      // 是销提分离的团长
      isStoreLeader = (join_mode === "2" && community_store_status === "opend") ? 'Y': 'N'
    }
    wx.setStorageSync('isStoreLeader', isStoreLeader)
  } catch(err) {
    wx.setStorageSync('isStoreLeader', 'N')
  }
}

/** @type { Promise<void> } */
let checkUserIsDistributorPromise
/** @type { number } */
let checkUserIsDistributorCacheTime
/**
 * @desc 检查用户是否为及时达分佣团长
 * @returns { Promise<boolean> }
 */
export function checkUserIsDistributor(foceUpdate = false) {
  const now = Date.now()
  // 5分钟内不再请求，直接返回promise
  if (checkUserIsDistributorPromise && !foceUpdate && (now - checkUserIsDistributorCacheTime) < 5 * 60 * 1000) {
    return checkUserIsDistributorPromise
  }

  checkUserIsDistributorCacheTime = now

  checkUserIsDistributorPromise = (async function() {
    const userToken = wx.getStorageSync('token')
    const { userID } = wx.getStorageSync('user') || {}
    if (!(userToken && userID)) {
      wx.setStorageSync('isDistributor', false)
      return false
    }

    const { data: { isDistributor } } = await request.post({
      url: '/dskhd/api/member/account/v1/checkIsDistributor',
      data: { customerID: userID },
      isLoading: false,
    }).catch(() => ({ data: { isDistributor: false } }))

    wx.setStorageSync('isDistributor', isDistributor)
    return isDistributor
  })()

  return checkUserIsDistributorPromise
}
const fromShareCard = '及时达商详分享好友'
const topicFromShareCard = '综合专题分享好友'
const fromSharePost = '及时达商详分享海报'
const fromShareTimeline = '及时达商详分享朋友圈'
const topicFromShareTimeline = '综合专题分享朋友圈'
function checkIsRelayScene(urlQueryObj, bindScene) {
  // 如果不是接龙首页/详情的分享
  if (!['relayDetail'].includes(urlQueryObj.to)) {
    return bindScene
  }
  return {
    [fromShareCard]: urlQueryObj.goodsCode ? {
      relayDetail: '接龙商品分享好友',
    } : {
      relayDetail: '接龙活动分享好友',
    },
    [fromSharePost]: {
      relayDetail: '接龙商品分享海报',
    },
    [fromShareTimeline]: urlQueryObj.goodsCode ? {
      relayDetail: '接龙商品分享朋友圈',
    } : {
      relayDetail: '接龙活动分享朋友圈',
    },
  }[bindScene][urlQueryObj.to] || bindScene
}
export async function trackBindDistributor(urlQueryObj) {
  if (!isFromTimelyDistributor()) {
    return
  }
  if (await checkUserIsDistributor()) {
    return
  }
  const {
    SKU_ID = '',
    SKU_Name = '',
    goods_label = [],
    activity_ID = '',
    activity_Name = '',
  } = getShareObj(urlQueryObj)
  const bindScene = (activity_ID ? {
    1007: topicFromShareCard,
    1008: topicFromShareCard,
    1155: topicFromShareTimeline,
  } : {
    1007: checkIsRelayScene(urlQueryObj, fromShareCard),
    1008: checkIsRelayScene(urlQueryObj, fromShareCard),
    1011: checkIsRelayScene(urlQueryObj, fromSharePost),
    1012: checkIsRelayScene(urlQueryObj, fromSharePost),
    1013: checkIsRelayScene(urlQueryObj, fromSharePost),
    1286: checkIsRelayScene(urlQueryObj, fromSharePost),
    1155: checkIsRelayScene(urlQueryObj, fromShareTimeline),
  })[util._getApp().globalData.scene] || ''
  // 及时达商详分享朋友圈
  sensors.track('BindRecommender', {
    bindScene,
    ...Object.assign(
      {},
      // 综合专题分享 或者 接龙活动详情分享
      activity_ID ? {
        activity_ID,
        activity_Name,
      } : {},
      SKU_ID ? {
        SKU_ID,
        SKU_Name,
        goods_label,
      }
      : {}
    ),
  })
}

function getShareObj(urlQueryObj) {
  const { to, shareObj: shareObjJson } = urlQueryObj
  if (to === 'relayDetail') {
    const {
      activityCode,
      activityName,
      goodsCode,
      goodsName,
    } = urlQueryObj
    return {
      activity_ID: activityCode,
      activity_Name: activityName,
      SKU_ID: goodsCode,
      SKU_Name: goodsName,
    }
  }
  let shareObj = {}
  try {
    shareObj = shareObjJson ? JSON.parse(shareObjJson) : {
      SKU_ID: urlQueryObj.goodsSn,
      SKU_Name: urlQueryObj.goodsName,
      goods_label: urlQueryObj.goods_label ? JSON.parse(urlQueryObj.goods_label) : [],
    }
  } catch {
    shareObj = shareObjJson ? {} : {
      SKU_ID: urlQueryObj.goodsSn,
      SKU_Name: urlQueryObj.goodsName,
      goods_label: [],
    }
  }
  return shareObj
}

// 缓存及时达分佣团长用户id
// 无分佣团长用户id情况下,保持上一个分佣团长用户id
export function saveTimelyDistributor(options, app) {
  const _app = app || util._getApp()
  const globalDataScene = _app.globalData.scene
  // 从摄像头扫码 或者 从h5明文 url scheme 启动小程序 不能绑定团长
  // 从1154单页模式进入小程序,绑定团长不算数
  // 因为再进入小程序,会以1155场景值冷启动
  // 之前的团长信息会丢失,会在1155场景下绑定团长
  if ([1011, 1286, 1154].includes(globalDataScene)) {
    _app.globalData.isFromRecommenderLink = 0
    return
  }
  const urlQueryObj = options.q
    // 此处的q参数,是扫描普通二维码唤起小程序后的启动参数
    ? util.toQueryObj(decodeURIComponent(options.q || '').split('?')[1] || '', { decodeOnEach: true })
    : isFromURLScheme(globalDataScene) ? decodeURLSchemeQuery(options) : options
  const { distributor: id } = urlQueryObj
  const prevParentDistributor = _app.globalData.parentDistributor
  _app.globalData.parentDistributor = id || prevParentDistributor
  const isFromRecommenderLink = Boolean(id)
  _app.globalData.isFromRecommenderLink = Number(isFromRecommenderLink)
  // 和上次不一致才上报绑定事件
  if (prevParentDistributor === id || !isFromRecommenderLink) {
    return
  }
  trackBindDistributor(urlQueryObj)
}

export function isDistributorFromCache() {
  return Boolean(wx.getStorageSync('isDistributor'))
}

export function getTimelyDistributor(app) {
  const _app = app || util._getApp()
  return _app.globalData.parentDistributor || ''
}

/**
 * @desc 是否来自及时达分佣团长入口
 * @returns { 0 | 1 }
 */
export function isFromTimelyDistributor(app) {
  const _app = app || util._getApp()
  return _app.globalData.isFromRecommenderLink
}

export async function updateUserWxInfo({
  nickName,
  avatarUrl,
}) {
    const { userID, phoneNumber } = wx.getStorageSync("user")
    if (!userID) return
    const { openid, unionid } = wx.getStorageSync('wxSnsInfo')
    try {
      const params = {
        customerID: userID,
        phoneNumber: phoneNumber,
        openId: openid,
        unionId: unionid,
        nickName: nickName,
        icon: avatarUrl,
      }
      await request.post({ url: userApi.updateUserWxInfoUrl, data: params, isLoading: false })
    } catch(err) {
      console.log(err)
    }
  }
// 缓存最近一次下单的的设备信息
let PRE_DEVICE_INFO = null
/**
 * @description 获取用户设备信息
 */
export async function getUserDeviceInfo() {
  let { userID } = wx.getStorageSync("user") || {}
  if (!userID) { return }
  try {
    let params = {
      customerID: userID
    }
    const result = await request.post({ url: userApi.getDeviceInfoUrl, data: params, isLoading: true })
    PRE_DEVICE_INFO= result.data
    return result.data
  } catch(err) {
    return {}
  }
}
// 当前设备信息
export const getCurSystemInfo = () => {
  let res = {}
  const app = getApp()
  const sysInfo = app ? app.globalData.systemInfo : null
  if (sysInfo && Object.keys(sysInfo) && Object.keys(sysInfo).length) {
    res = sysInfo
  } else {
    res = wx.getSystemInfoSync()
  }

  // 取值前做一下转换
  res.mobile = getNativeModel(res.model) || ""
  return res
}
/**
 * @desc 根据wx.getSystemInfoSync() 的model结果提取原生的机型
 * 如：iPhone 12 <iPhone13,2> 转换为 iPhone13,2
 * @param {String} model 小程序获取的机型
 * @returns {String} 转换后的值
 */
const getNativeModel = (model) => {
  if (!model) {
    return model
  }
  // 通过正则去匹配<xx>内容
  const str = String(model).match(/<(.+?)>/gi)
  // const arrMatch = []///(?<=<).+?(?=>)/.exec(String(model))
  if (str && str[0]) {
    return str[0].replace(/^<|>$/g, "")
  }
  return model
}

/**
 * @description 校验设备信息与上次下单信息是否一致
 * @params perDeviceInfo
 * @returns true false
 */
export async function checkPayDevice(perDeviceInfo) {
  try {
    const curSystemInfo = getCurSystemInfo()
    // 如果检测到是在企微运行 部分安卓可能读取为WxWork 则不对比
    if (curSystemInfo.mobile && curSystemInfo.mobile.toLowerCase() === 'wxwork') {
      return true
    }
    // 没有上次设备信息，则接口获取
    const prePaySystemInfo = perDeviceInfo || await getUserDeviceInfo()
    console.log('curSystemInfo', curSystemInfo)
    console.log('prePaySystemInfo', prePaySystemInfo)

    // 如果上次设备信息不存在任何键值对 则表示用户还未下过单 此时应该返回true 表示一致
    if (prePaySystemInfo && !Object.keys(prePaySystemInfo).length) {
      return true
    }
    // 如果设备型号一致则无须验证
    return curSystemInfo.mobile === prePaySystemInfo.mobile
  } catch (e) {
    return false
  }
}

/**
 * @description 更新用户设备信息
 */
export async function updateUserDeviceInfo() {
  let { userID } = wx.getStorageSync("user") || {}
  let { unionid: unionId } = wx.getStorageSync("wxSnsInfo") || {}
  if (!userID) { return }
  let {
    brand,
    screenWidth: displayWidth,
    screenHeight: displayHeight,
    system,
    mobile,
    devicePixelRatio
  } = getCurSystemInfo()
  // 设备信息无变更，则不用更新
  if (PRE_DEVICE_INFO && Object.keys(PRE_DEVICE_INFO).length) {
    let checkRes = await checkPayDevice(PRE_DEVICE_INFO)
    if (checkRes) { return }
  }
  // 如果检测到是在企微运行 部分安卓可能读取为WxWork 则不上报该字段
  if(mobile && mobile.toLowerCase() === 'wxwork') {
    mobile = ""
  }
  try {
    let params = {
      customerID: userID,
      displayWidth: Math.floor(devicePixelRatio * displayWidth),
      displayHeight: Math.floor(devicePixelRatio * displayHeight),
      brand,
      mobile,
      system,
      unionId
    }
    await request.post({ url: userApi.updateDeviceInfoUrl, data: params, isLoading: false })
  } catch(err) {}
}

// 查找用户是否为游客
let USERMODE = null
export function getUserIsVisitor() {
  const { isVisitor } = getUserAuth()
  return isVisitor
}

/**
 * @typedef TUserAuth 用户授权
 * @property { boolean } isVisitor 是游客模式
 * @property { boolean } shop 已开启购物功能
 * @property { boolean } login 已开启登录功能
 * @property { boolean } memberService 已开启会员增值服务
 * @property { boolean } infoService 已开启信息交互服务
 * @property { boolean } customerService 已开启客服保障服务
 */

//  默认用户授权信息
const DEFAULT_USER_AUTH = {
  isVisitor: true,
  ...Object.keys(subProtocolTypeEnum).reduce((prev, current) => {
    prev[current] = true
    return prev
  }, {})
}

/**
 * 获取用户授权信息
 * @returns { TUserAuth }
 */
export function getUserAuth() {
  let userAuth = wx.getStorageSync('userAuth')

  //  没有值则赋默认值
  if (!userAuth) {
    userAuth = { ...DEFAULT_USER_AUTH }
    wx.setStorageSync('userAuth', userAuth)
  }

  return userAuth
}

/**
 * 更新用户授权信息
 * @param { TUserAuth } updateData
 */
export function updateUserAuth(updateData) {
  const APP = getApp()
  let userAuth = getUserAuth()

  const onlyUpdateVisitor = Object.keys(updateData).length === 1 && updateData.hasOwnProperty('isVisitor')
  //  仅更新游客模式
  // if (onlyUpdateVisitor) {
  //   //  为了可读性，其余值不做取反处理
  //   userAuth = updateData.isVisitor ? {
  //     isVisitor: true,
  //     ...DEFAULT_USER_AUTH
  //   } : {
  //     isVisitor: false,
  //     ...Object.keys(subProtocolTypeEnum).reduce((prev, current) => {
  //       prev[current] = true
  //       return prev
  //     }, {})
  //   }
  // }
  // //  更新其他项
  // else {
    Object.assign(userAuth, updateData)
  // }

  //  子协议功能均已开启
  // if (Object.keys(subProtocolTypeEnum).every(i => userAuth[i])) {
  //   //  全部功能均已授权，此时不再为游客模式
  //   userAuth.isVisitor = false

  //   //  此处手动调用同意全部协议的方法
  //   const protocol = require('../components/base/userProtocol/protocol')
  //   protocol.closeProtocol({ agree: true })
  // }

  //  storage更新
  wx.setStorageSync('userAuth', userAuth)

  //  globalData也需要保持一致性
  APP.globalData.userAuth = userAuth


  if (!userAuth.isVisitor) {
    // 非游客模式，开启埋点
    // 更新: 不在这开启埋点了
    // 这里的调用是从clientLogin里登录成功后,
    // 调用updateUserAuth进入的
    // 但是clientLogin里调用updateUserAuth的下一行就是
    // 调用loginMixin.loginSuccessHandle
    // 在loginSuccessHandle里会有initSensors的逻辑
    // console.trace('游客模式开启埋点')
    // APP.startReportPoint()
    // 更新子协议时，如果变为非游客模式，则次日达下次不重新定位
    if (!APP.globalData.hasShowChangeBgxxCityModal && !onlyUpdateVisitor) {
      APP.globalData.hasShowChangeBgxxCityModal = true
    }
  }
}

/** 用户是否因为被踢出导致的退出登录 */
export let isDeviceChangeLoginOuted = false

/**
 * 其他页面修改因为被踢出导致的退登触发
 */
export const setDeviceChangeLoginOuted = (value) => {
  isDeviceChangeLoginOuted = value
}
/**
 * 登出接口
 * @param {Object} obj 登出参数
 * @param {String | Number} obj.customerID 用户ID
 * @param {String} obj.openid 用户openId
 * @param {keyof IOutLoginType} [obj.logoutCause] 退出类型
 * userInitiatedLogout: 用户主动退出
 * userPrivacyAgreementDeniedKickOut: 用户拒绝隐私协议踢出
 * deviceChangeLogout: 换设备被退出登录
 * @returns void
 */
export async function userLogout( { customerID, openid, logoutCause = outLoginType.userInitiatedLogout }) {
  if (!customerID || !openid) {
    return
  }
  try {
    // 用户是否因为被踢出导致的退出登录 仅上报一次
    if (logoutCause === outLoginType.deviceChangeLogout) {
      // 如果上报过就不上报了
      if (isDeviceChangeLoginOuted) {
        return
      }
      // 置为上报后的状态
      isDeviceChangeLoginOuted = true
    }
    // 用户退出埋点
    if (logoutCause) {
      await sensors.track(
        'logout', {
          logoutCause
        }
      )
    }
    let result = await getApp().api.userLogout({ customerID, openId: openid })
    console.log(result)
  } catch(err) {
    console.log(err)
  }
}
// 获取凭证code
export async function getWxSns () {
  const code = await getLoginCode()
  const params = {
    "js_code": code
  }
  try {
    let res = await getApp().api.getWxSns(params)
    if (res.errorCode === 0 && res.data) {
      const { openId, unionId } = res.data || {}
      if (openId && unionId) {
        wx.setStorageSync('wxSnsInfo', {
          openid: openId,
          unionid: unionId
        })
        app.globalData.wxOpenId = openId
      }
    }
  } catch (error) {}
}

// 获取凭证code
export async function getLoginCode () {
  return new Promise((resolve, reject) => {
    wx.login({
      success: (res) => {
        resolve(res.code)
      },
      fail: () => {
        resolve()
      }
    })
  })
}
// 登出接口
export async function clientLoginRequest() {
  const jsCode = await getLoginCode()
  try {
    let result = await getApp().api.silentLogin({ jsCode })
    if (typeof result.data === "string") {
      const decryptKey = await getEncryptKey('pwd')
      return JSON.parse(commonObj.Decrypt(result.data,  decryptKey || ''))
    }
    return result.data
  } catch(err) {
    console.log(err)
    return {}
  }
}

let clientLoginPromise = null
export const clientLoginParallel = parallelPromise(clientLogin)

// 静默登录
export function clientLogin(globalApp) {
  if (clientLoginPromise) { return clientLoginPromise }
  clientLoginPromise = (async function() {
    try {
      const app = getApp() || globalApp
      // 未登录才走静默登录
      const { userToken } = wx.getStorageSync("user") || {}
      if (userToken) return
      const loginResult = await clientLoginRequest()
      if (!loginResult) {
        return
      }
      const {
        openId,
        customerId,
        token,
        unionId,
        superVipStatus,
        phoneNumber
      } = loginResult
      console.log('loginResult', loginResult)
      if (openId && unionId) {
        wx.setStorageSync('wxSnsInfo', {
          openid: openId,
          unionid: unionId
        })
        app.globalData.wxOpenId = openId
      }
      // 登录成功则有token返回
      if (!token) {
        return
      }
      const loginData = {
        isSuperVip: superVipStatus,
        userToken: token,
        userID: customerId,
        isNavigateBack: false,
        phoneNumber
      }
      updateUserAuth({
        isVisitor: false
      })
      loginMixin.loginSuccessHandle(loginData)
      return true
    } catch(err) {
      return
    }
  })()
  clientLoginPromise.then(() => {
    clientLoginPromise = null
  })
  return clientLoginPromise
}

// 收藏门店
export async function updateCollectStore(params) {
  try {
    let { userID } = wx.getStorageSync("user") || {}
    params.customerID = userID
    let result = await getApp().api.updateCollectStore(params)
    console.log(result)
    const { tipsCode, tips } = result.data
    if (tipsCode === 6001) {
      console.log('6001')
      const res = await getApp().showModalPromise({
        content: tips,
        showCancel: true,
        cancelText: '我知道了',
        confirmText: '查看收藏',
      })
      if (res) {
        wx.navigateTo({
          url: '/homeDelivery/pages/collectStore/index'
        })
      }
      return false
    }
    return true
  } catch(err) {
    console.log(err)
    return {}
  }
}

export async function reporLocaionStore(storeCode) {
  try {
    const { userID } = wx.getStorageSync("user") || {}
    if (!userID || !storeCode) {
      return
    }
    userInfoApi.updateUserStore({
      customerID: userID,
      storeCode
    })
  } catch(err){}
}


export const recentBuy = {
  // 记录在本地缓存，下单后有15分钟支付时间，用户可能销毁小程序
  // 若用户不支付，先不考虑清除缓存，因为用户会再次下单也会覆盖先前的数据
  collectRecentFruitSku: function ({skuList, time}) {
    if (!Array.isArray(skuList) || !skuList.length) return
    // skuList会有重复数据，所以需要去重
    const list = [...new Set(skuList)].reduce((list, item) => ([...list, {goodsSn: item, time}]), [])
    wx.setStorageSync('fruitRecentBuySkuList', list)
    // getApp().globalData.fruitRecentBuySkuList = list
  },
  getRecentBuySku: function () {
    // const app = getApp()
    // const list = app.globalData.fruitRecentBuySkuList || []
    // delete app.globalData.fruitRecentBuySkuList
    const list = wx.getStorageSync('fruitRecentBuySkuList')
    wx.removeStorageSync('fruitRecentBuySkuList')
    return Array.isArray(list) ? list : []
  }
}


export const getMemberInfo = async (optionalInfo = ['vip', 'group']) => {
  const userID = (wx.getStorageSync('user') || {}).userID
  if (!userID) {
    return {}
  }
  try {
    const { data } = await memberApi.getMemberInfo({
      customerID: userID,
      optionalInfo,
    })
    const {
      isAddFriend,
      isAddGroup,
      isQuitAddGroup,
      currentTypeCode,
      currentTypeTag,
      identifyCode,
      levelId,
      isFruitFans,
      icon,
      nickName
    } = data

    /**
     * 必须在此重新获取一遍，因为上面的await的时间里
     * 其他地方也修改了storage中的user。导致数据不同步
     */
    const user = wx.getStorageSync('user') || {}
    Object.assign(user, objectGetNotVoid({
      isAddFriend,
      isAddGroup,
      isQuitAddGroup,
      currentTypeCode,
      currentTypeTag,
      identifyCode,
      levelId
    }));
    wx.setStorageSync('user', user);
    updateNickAndIcon(nickName, icon)

    userStore.updateIsFruitFans(isFruitFans)
    return data
  } catch (error) {
    console.info('getMemberInfo error', error)
    return {}
  }
}
export function updateNickAndIcon(nickName, icon) {
  const info = wx.getStorageSync('userNameAndImg') || {}
  if (nickName) {
    info.nickName = nickName
  }
  if (icon && (icon.indexOf('thirdwx.qlogo.cn') === -1 && icon.indexOf('https') > -1)) {
    info.avatarUrl = icon
  } else {
    info.avatarUrl = ''
  }
  wx.setStorageSync('userNameAndImg', info)
}

export const getComplateMemberInfo = parallelPromise(getMemberInfo)

// 此方法在app.onShow中才会调用
export const getFruitFansStatus = async () => {
  // 灰度城市非果粉才查询身份
  if (!userStore.isFruitFans) {
    util.retryRequest(async function() {
      const result = await getMemberInfo(['group'])
      // 如果查到是果粉，就无需重试
      return result.isFans
    })
  }

}

/**
 * F: '心享会员'
 * T: '试用心享会员'
 * C: '普通会员'
 * @desc 更新心享会员状态
 * @param {object} options
 * @param {number=} options.retryTimes 查询次数
 */
export async function updateVipStatus(options) {
  const { retryTimes } = options || {}
  const app = getApp()
  let fetchTimes = 0
  // 默认查询3次
  const needFetchTimes = retryTimes || 3
  async function fetchApi () {
    try {
      const res = await bgxxGetCustomerInfo({
        data: { customerID: app.globalData.customerID },
        options: { isReturnLoginOutError: false }
      });
      return res.data
    } catch (error) {
      console.log('updateMemberInfo error', error)
      return
    }
  }
  function setInfo(superVipStatus) {
    Object.assign(app.globalData, {
      superVipStatus,
    });
    userStore.updateIsFruitFans(true)
    const user = wx.getStorageSync('user');
    user.superVipStatus = superVipStatus;
    wx.setStorageSync('user', user);
  }
  // 轮询3次
  while (fetchTimes < needFetchTimes) {
    fetchTimes++
    // 轮询间隔每次增加
    await sleep(fetchTimes*1000)
    const data = await fetchApi()
    if (!data) continue
    const { superVipStatus } = data
    // 如果是普通会员，继续请求
    if ([superVipStatusEnum.XIN_XIANG, superVipStatusEnum.SHI_YONG_XIN_XIANG].includes(superVipStatus)) {
      // 是会员的时候才赋值
      setInfo(superVipStatus)
      break
    }

  }
}

/**
 * 领取首次定级权益（触发初始化领券）
 */
export async function receiveFirstGradingEquity(userID) {
  if (!userID) {
    return
  }
  try {
    const { data }= await userInfoApi.receiveFirstGradingEquity({
      customerID: String(userID)
    })
    const { resultCode } = data || {}
    if (Number(resultCode) === 0) {
      wx.setStorageSync('receiveFirstMark', 'Y')
    }
  } catch (error) {
    console.info('receiveFirstGradingEquity error', error)
  }
}

export const getMemberTypeInfo = async function() {
  const user = wx.getStorageSync('user') || {}
  const { phoneNumber, userID } = user
  if (!userID) {
    return {}
  }
  try {
    const { data } = await memberApi.getMemberTypeInfo({
      customerID: userID,
      phoneNumber,
    })
    const user = wx.getStorageSync('user') || {}

    Object.assign(user, {
      userType: data.type,
    });
    wx.setStorageSync('user', user)
  } catch (error) {
    return {}
  }
}

/**
 * @description 获取腾讯图灵设备信息
 */
let DEVICE_TOKEN = ''
export const getTuringDeviceToken = async function() {
  if (DEVICE_TOKEN) {
    return DEVICE_TOKEN
  }
  const { 
    openid,
  } = wx.getStorageSync('wxSnsInfo') || {}
  // if (!openid) {
  //   return
  // }
  return new Promise((resolve) => {
    const turingmm = require.async('../sourceSubPackage/commonUtils/turing/turingSDK.js')
    turingmm.then(turing => {
      turing.init({
        channel: TURING.channel,
        openid: openid || '',
      })
      turing.getDeviceTokenV2(function (res) {
        //返回结果
        if(res.ret === 0){
          console.log(res)
          DEVICE_TOKEN = res.deviceToken
          resolve(DEVICE_TOKEN) 
          return
        }
        resolve('')
      })
    })
  })
}