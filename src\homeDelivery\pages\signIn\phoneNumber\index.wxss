/* homeDelivery/pages/signIn/phoneNumber/index.wxss */
.phoneNumber-wrapper {
  --primary: #00A34F;
  padding: 0 40rpx;
}

/* 标题样式 */
.title-wrapper {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.title-wrapper .logo {
  width: 168rpx;
  height: 168rpx;
  margin: 60rpx auto 20rpx auto;
}
.title-wrapper .title-text {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary);
}
.slogan {
  width: 460rpx;
  height: 31rpx;
}
/* 表单样式 */
.form-wrapper {
  padding-top: 40rpx;
}
.form-row {
  display: flex;
  height: 98rpx;
  border-bottom: 1rpx solid #ccc;
}
.form-row + .form-row{
  margin-top: 29rpx;
}
.form-input {
  font-size: 34rpx;
  height: 98rpx;
  line-height: 98rpx;
  flex: 1;
}
.btn-close{
  --size: 28rpx;
  width: calc(var(--size) * 2);
  height: 100%;
  font-size: 0;
  /* align-self: flex-end; */
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}
.btn-close image{
  width: var(--size);
  height: var(--size);
  filter: brightness(0);
  opacity: 25%;
  /* color: #BFBFBF; */
  /* background-color: black; */
  /* transform: translateY(-100%); */
  /* filter: drop-shadow(#BFBFBF 0 var(--size)); */
}
.btn-sendCode {
  /* flex: 180rpx 0 0; */
  color: var(--primary);
  border: 1rpx solid var(--primary);
  border-radius: 32rpx;
}
.btn-sendCode.disabled {
  color: #999;
}
.btn-sendCoding{
  color: #ADADAD;
}
.btn-sendCode, .btn-sendCoding{
  align-self: flex-end;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  width: 200rpx;
  height: 64rpx;
}
.sendcode-disabled{
  opacity: 0.5;
}
.error-wrapper {
  height: 54rpx;
  line-height: 54rpx;
  margin-bottom: 6rpx;
  display: flex;
  align-items: center;
}
.icon-error {
  width: 24rpx;
  height: 24rpx;
  margin-right: 12rpx;
}
.error-info {
  font-size: 22rpx;
  color: #F64742;
  vertical-align: middle;
}
.btn-sign {
  height: 96rpx;
  line-height: 96rpx;
  width: 670rpx;
  background: #00A34F;
  text-align: center;
  border: none;
  border-radius: 96rpx;
  color: #fff;
}
.btn-sign.disabled {
  background: #80C59E;
}
radio .wx-radio-input{
  border-radius: 50%;/* 圆角 */
  width: 24rpx;
  height: 24rpx;
  transform:translateY(-3rpx);
}
/* 选中后的 对勾样式 （白色对勾 可根据UI需求自己修改） */
radio .wx-radio-input.wx-radio-input-checked::before{
  border-radius: 50%;/* 圆角 */
  width: 24rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
  height: 24rpx; /* 选中后对勾大小，不要超过背景的尺寸 */
  line-height: 24rpx;
  text-align: center;
  font-size:24rpx;
  background: transparent;
  transform:translate(-50%, -50%) scale(1);
  -webkit-transform:translate(-50%, -50%) scale(1);
}
.tips-wrapper {
  margin-top: 42rpx;
  font-size: 24rpx;
  line-height: 30rpx;
  display: flex;
}
.tips-radio{
  position: relative;
  padding-left: 10rpx;
}
.tips-radio::after{
  content: '';
  width: 200%;
  height: 200%;
  position: absolute;
  top: -50%;
  left: -50%;
  z-index: 0;
}
.focus-color {
  border-bottom: 1rpx solid #008C3C;
}
