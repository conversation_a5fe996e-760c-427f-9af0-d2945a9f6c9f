const config = require('../../../utils/config');
import styleConfig from '../../../utils/goodsStyleConfig';
import operateCartMixin from '../../../mixins/bgxx/operateCartMixin';
import cartAnimate from '../../../mixins/bgxx/cartAnimate';
import vegetablesLocateMixin from '../../../mixins/vegetablesLocateMixin';
import storeBinding from '../../../mixins/bgxx/storeBinding';
// import combineGoods from '../../../stores/combineGoods';
// import {$Loading} from '../../../utils/base'
import sensors from '../../../utils/report/sensors';
const { debounce, throttle } = require('../../../utils/util');
const { generateShareAttr, ShareScene } = require('../../../utils/report/setup');
const app = getApp();
let obAnchor = null;
import { getBgxxSaleReportInfo } from '../../../utils/report/getSaleReportInfo';
import { ORDER_TYPE } from '../../../source/const/order';
const activity = require('../../../utils/activity')
const log = require('../../../utils/log.js')

const {
  FreshSubject
} = require('../../../service/freshSubject')

class SellOutList {
  /** 单排 && 售罄商品数量＞1 */
  static singleRowMoreSellOut(res) {
    res.showListStatus = true
    res.showList = res.sellList.concat(res.sellOutList[0])
    // 隐藏商品数量
    res.hideListNum = res.sellOutList.length - 1
    res.sellOutList.splice(0, 1)
    return res
  }
  /** 单排 && 售罄商品数量<=1 */
  static singleRowLessSellOut(res) {
    res.showListStatus = false
    res.showList = res.sellList.concat(res.sellOutList[0])
    return res
  }
  /** 双排 && 售罄商品>=2 */
  static doubleRowMoreSellOut(res) {
    //展示商品数量是奇数
    if (res.sellList.length % 2 === 1) {
      res.showListStatus = true
      res.showList = res.sellList.concat(res.sellOutList[0])
      res.hideListNum = res.sellOutList.length - 1
      res.sellOutList.splice(0, 1)
    }
    //展示商品数量是偶数
    else if (res.sellList.length % 2 === 0) {
      //售罄商品数量 === 2
      if (res.sellOutList.length === 2) {
        res.showListStatus = false
      }
      // 售罄商品数量 !== 2
      else if (res.sellOutList.length !== 2) {
        res.showListStatus = true
      }
      res.showList = res.sellList.concat(res.sellOutList.slice(0, 2))
      res.hideListNum = res.sellOutList.length - 2
      res.sellOutList.splice(0, 2)
    }
    return res
  }
  /**双排 && 售罄商品<2 */
  static doubleRowLessSellOut(res) {
    res.showListStatus = false
    res.showList = res.sellList.concat(res.sellOutList[0])
    return res
  }
  /** 三排 在售商品总数为3的倍数时，且楼层内售罄商品>3 */
  static tripleRowThreeMoreSellOut(res) {
    res.showListStatus = true
    res.showList = res.sellList.concat(res.sellOutList.slice(0, 3))
    res.hideListNum = res.sellOutList.filter((item) => {
      return !item.isAdd
    }).length - 3
    res.sellOutList.splice(0, 3)
    return res
  }
  /** 三排 在售商品总数为3的倍数+1时，且楼层内售罄商品>2 */
  static tripleRowThreePlusOneMoreSellOut(res) {
    res.showListStatus = true
    res.showList = res.sellList.concat(res.sellOutList.slice(0, 2))
    res.hideListNum = res.sellOutList.filter((item) => {
      return !item.isAdd
    }).length - 2
    res.sellOutList.splice(0, 2)
    return res
  }
  /** 三排 在售商品总数为3的倍数+2时，且楼层内售罄商品>1 */
  static tripleRowThreePlusTwoMoreSellOut(res) {
    res.showListStatus = true
    res.showList = res.sellList.concat(res.sellOutList.slice(0, 1))
    // 去掉补足的部分
    res.hideListNum = res.sellOutList.filter((item) => {
      return !item.isAdd
    }).length - 1
    res.sellOutList.splice(0, 1)
    return res
  }
  /** 其他情况 */
  static default(res) {
    res.showListStatus = false
    res.showList = res.sellList.concat(res.sellOutList)
    return res
  }
}

Page({
  data: {
    ORDER_TYPE,
    options: {},
    styleConfig: styleConfig,
    picDomain: config.baseUrl.PAGODA_PIC_DOMAIN,
    subjectData: null,
    showCart: false,
    goodsExposeData: {
      activityID: '',
      activityName: '',
    },
    shareInfo: {},
    isFromHomeDelivery: 'true', // 默认为false
    goodsSpecial: false, // 是否使用商品内的特价信息
    previewArrangeObject: {
      2: '',
      3: 'subject-goods-double',
      6: 'subject-goods-three',
    },
    subjectItemArrangeObject: {
      2: '',
      3: 'goods-preview-double',
      6: 'goods-preview-three',
    },
    styleTypeNum: {
      // 样式类型
      2: 2,
      3: 1,
      6: 3,
    },
    scrollInfo: {
      scrollId: 0,
    },
    showFixNav: false,
    toView: '', // 滚动位置id
    hasData: true,
    isShowBlank: true,
    isScrollAnimation: false, // 如果要滚动到楼层的话，要动画会很慢。滚动到楼层后 在设置为需要动画
  },
  _data: {
    pageProtocol: true,
    isReportPageShow: false,
    curRoute: "bgxxShop/pages/subjectActivity/index",
    isInitSetSelectNav: false,
  },
  mixins: [
    operateCartMixin,
    cartAnimate,
    vegetablesLocateMixin,
    storeBinding,
  ],
  onLoad({ activityID, modulePriority }) {
    console.log(
      'activityID,isFromHomeDelivery',
      activityID,
    );
    this.shareData = {};
    this.setData({
      'goodsExposeData.activityID': (this.activityID = activityID),
      isFromHomeDelivery: true,
      goodsSpecial: true,
      options: {
        activityID,
        isFromHomeDelivery: true
      },
      modulePriority,
      isShowBlank: modulePriority ? true: false,
      isScrollAnimation:  modulePriority ? false: true,
    });
    // 隐藏分享按钮
    wx.hideShareMenu()
  },
  onUnload: function () {
    obAnchor && obAnchor.disConnect();
  },
  onHide: function () {
    // obAnchor在每次请求数据之后进行监听赋值
    // 在每次onshow时会定位，定位后请求数据，所以onHide时需要清除
    obAnchor && obAnchor.disConnect();
    this._data.isReportPageShow = false

    this.setData({
      modulePriority: ''
    })
  },
  async onLocateReady() {
    if (this.data.currentView === 'content') {
      this.getCartCount();
        this.getActivity();
    }
  },
  async getSubjectDetail() {
    const freshSubject = new FreshSubject({ activityID: this.activityID })
    const detail = await freshSubject.getDetailWithPreRequest()
    return detail
  },
  /**
   * 获取专题活动数据
   */
  async getSubjectData(status = 'first') {

    let pageTitle = '专题活动';
    // $Loading.show()
    try {
      const {
        customerID,
        bgxxCityInfo: { cityID = '', storeID = '' } = {},
      } = app.globalData;
      const res = await app.api.getBgxxSubjectDetail(
        {
          activityID: this.activityID,
          customerID: customerID || -1,
          cityID: cityID || -1,
          storeID: storeID || -1,
        },
        status === 'first'
      );
      if (!res.data) {
        return;
      }
      const {
        togetherMoudleList = [],
        activityStatus = 'U',
        name = '专题活动',
        sharePic,
        shareMajor,
        isShareOpen,
      } = res.data;
      const goodsIDList = (() => {
        const result = [];
        togetherMoudleList
          .filter((el) => ['2', '3', '6'].includes(el.type))
          .forEach((el) => {
            el.goodsList.forEach((good) => {
              result.push(good.id);
            });
          });
        return result;
      })();
      // 处理商品售罄数据
      let list = this.handleSellOutList(togetherMoudleList);
      // 记录组合品商品数据
      // togetherMoudleList.forEach(({ goodsList }) =>
      //   combineGoods.setCache(goodsList)
      // );
      this.setData({
        subjectData: list,
        'goodsExposeData.activityName': (pageTitle = name),
      });

      this.setShareData({
        isShareOpen: isShareOpen,
        title: shareMajor,
        imageUrl: sharePic ? this.data.picDomain + sharePic : '',
      });
      if (activityStatus !== 'N') {
        // 非进行中的活动
        this.showActivityErrorTip(activityStatus);
      }
      this.checkCartShow();
      // 获取商品买赠标签
      let labelGoodsList = [];
      togetherMoudleList.forEach((el) => {
        el.goodsList.forEach((good) => {
          labelGoodsList.push(good);
        });
      });
      this.getGoodsActivitys(labelGoodsList);

      if (!this._data.isReportPageShow) {
        //  上报页面浏览事件
        sensors.pageScreenView({
          $url_query: this.data.options,
          activity_ID: this.activityID || ''
        })
        this._data.isReportPageShow = false
      }
    } catch (error) {
      const { errorCode = -1, description = '活动太火爆了，休息一下再试试吧' } =
        error || {};
      const content =
        errorCode === 30033 ? '活动还在预热中，敬请期待~' : description;
      wx.showModal({
        title: '',
        content: content,
        showCancel: false,
        confirmText: '知道了',
        success(res) {
          if (res.confirm) {
            wx.navigateBack();
          }
        },
      });
    }
    if (this.getCurRoute() === this._data.curRoute) wx.setNavigationBarTitle({
      title: pageTitle,
    });
    // $Loading.hide()
  },
  addGoodsInfo(item, propertyname) {
    item.sellList = [] // 在售商品列表
    item.sellOutList = [] // 售罄商品列表
    item.showList = [] // 展示商品列表
    item.showListStatus = false // 是否显示展开售罄商品按钮 false/不展示 true/展示
    item.buttonStatus = false // 按钮状态 false/展开 true/收起
    item.hideListNum = 0 // 隐藏商品数量
    ;(item[propertyname] || item.goodsList).forEach((good) => {
      // 在售商品
      (
        Number(good.stock) !== 0 &&
        (!good.shelfStatus || good.shelfStatus !== 'D')
      ) && item.sellList.push(good)
      // 售罄商品
      ;(
        Number(good.stock) === 0 ||
        (!!good.shelfStatus && good.shelfStatus === 'D')
      ) && item.sellOutList.push(good)
    })
  },
  /**
   * 加载数据列表
   * @param {Object} list 商品列表
   */
  handleSellOutList(list, propertyname = 'goodsList') {
    return list
      .map((item) => {
        const isGoodsModule = ['2', '3', '6'].includes(item.type)
        isGoodsModule && this.addGoodsInfo(item, propertyname)
        const {
          sellOutList,
          type,
          sellList,
        } = item;
        if (!isGoodsModule) { return item }
        /** @type { [boolean, () => any][] } */
        const strategyList = [
          // 单排 && 售罄商品数量＞1
          [String(type) === '2' && sellOutList.length > 1, () => SellOutList.singleRowMoreSellOut(item)],
          // 单排 && 售罄商品数量<=1
          [String(type) === '2' && sellOutList.length <= 1, () => SellOutList.singleRowLessSellOut(item)],
          // 双排 && 售罄商品>=2
          [type === '3' && sellOutList.length >= 2, () => SellOutList.doubleRowMoreSellOut(item)],
          // 双排 && 售罄商品<2
          [type === '3' && sellOutList.length < 2, () => SellOutList.doubleRowLessSellOut(item)],
          // 三排  在售商品总数为3的倍数时，且楼层内售罄商品>3
          [
            type === '6' &&
            sellOutList.length > 3 &&
            sellList.length % 3 === 0,
            () => SellOutList.tripleRowThreeMoreSellOut(item)
          ],
          // 三排  在售商品总数为3的倍数+1时，且楼层内售罄商品>2
          [
            type === '6' &&
            sellOutList.length > 2 &&
            sellList.length % 3 === 1,
            () => SellOutList.tripleRowThreePlusOneMoreSellOut(item)
          ],
          // 三排 在售商品总数为3的倍数+2时，且楼层内售罄商品>1
          [
            type === '6' &&
            sellOutList.length > 1 &&
            sellList.length % 3 === 2,
            () => SellOutList.tripleRowThreePlusTwoMoreSellOut(item)
          ],
          // 其他情况
          [true, () => SellOutList.default(item)],
        ]
        const callback = strategyList.find(v => v[0])[1]
        const result = callback()
        return {
          ...result,
          goodsList: void 0,
          showList: result.showList.filter((item) => item),
          sellOutList: result.sellOutList.filter((item) => item),
          sellList,
        };
      });
  },
  /**
   * 点击收起或者展开按钮
   * @param {Object} data 按钮所在楼层的商品数据
   */
  toggleButtonStatus(data) {
    let index = data.currentTarget.dataset.index;
    let list = this.data.subjectData;
    list[index].buttonStatus = !list[index].buttonStatus;
    if (list[index].buttonStatus) {
      list[index].showList = list[index].goodsList;
    } else {
      // 单排 && 售罄商品数量＞1
      if (String(list[index].type) === '2' && list[index].sellOutList.length > 1) {
        list[index].showList = list[index].sellList.concat(
          list[index].sellOutList[0]
        );
      }
      // 双排 && 售罄商品>=2
      else if (String(list[index].type) === '3' && list[index].sellOutList.length >= 2) {
        //展示商品数量是奇数
        if (list[index].sellList.length % 2 === 1) {
          list[index].showList = list[index].sellList.concat(
            list[index].sellOutList[0]
          );
        }
        //展示商品数量是偶数
        else if (list[index].sellList.length % 2 === 0) {
          list[index].showList = list[index].sellList.concat(
            list[index].sellOutList.slice(0, 2)
          );
        }
      }
    }
    this.setData({
      subjectData: list,
    });
  },
  /**
   * 显示异常活动提示
   * @param {String} activityStatus 活动状态
   */
  showActivityErrorTip(content) {
    wx.showModal({
      title: '',
      content: content,
      showCancel: false,
      confirmText: '知道了',
      success(res) {
        if (res.confirm) {
          wx.navigateBack();
        }
      },
    });
  },
  /**
   * 活动跳转
   */
  activityToSkip(e) {
    const value = e.currentTarget.dataset.item
    activity.toActivityPage(value)
  },
  /**
   * 根据是否有商品判断购物车是否显示
   */
  checkCartShow() {
    const { subjectData } = this.data;
    if (!!subjectData && subjectData.length > 0) {
      for (let index = 0, len = subjectData.length; index < len; index++) {
        const { showList = null, activityPicList = [] } = subjectData[index];
        if (!!showList && showList.length > 0) {
          this.setData({
            showCart: true,
          });
          this.setCartBusPos();
          return;
        }
        if (activityPicList && activityPicList.length > 0) {
          const { goodsTemplate } = activityPicList[0]
          if (goodsTemplate) {
            this.setData({
              showCart: true,
            });
            this.setCartBusPos();
            return;
          }
        }
      }
    } else {
      this.setData({
        showCart: false,
      });
    }
  },
  /**
   * 分享转发
   */
  onShareAppMessage() {
    const { shareInfo, isFromHomeDelivery } = this.data;
    const { storeID = -1 } = app.globalData.bgxxCityInfo;
    const mp_shareTitle = shareInfo.title || '强烈推荐！百果园家的食材果然不一样，新鲜好吃还实惠'
    const shareObj = Object.assign(generateShareAttr(), {
      shareScene: ShareScene.次日达综合专题页,
      mp_shareTitle,
    })
    const { activityID, activityName } = this.data.goodsExposeData
    sensors.track('MPShare', {
      ...shareObj,
      activity_ID: activityID,
      activity_Name: activityName,
    })
    return {
      title: mp_shareTitle,
      imageUrl:
        shareInfo.imageUrl ||
        'https://resource.pagoda.com.cn/group1/M00/33/77/CmiWiF-iMoaATlYtAAJXnQ-NC6g072.png',
      path: `/pages/xxshop/index/index?toSubjectActivity=${JSON.stringify({
        activityID,
        isFromHomeDelivery: isFromHomeDelivery === 'true',
      })}&bgxxStoreID=${storeID}&isFromHomeDelivery=${isFromHomeDelivery}&shareObj=${JSON.stringify(shareObj)}`,
      success: function () {},
      fail: function () {},
    };
  },
  /**
   * 点击优惠券
   */
  handleTapCoupon: throttle(function (e) {
    const item = e.currentTarget.dataset.item
    this.bannerJumpHandle(item)
  }, 1000),
  bannerJumpHandle(options) {
    const { openType, openValue } = options
    switch (String(openType)) {
      case '30':
      {
        this.toReceiveCopon(options)
        break;
      }
      default:
      {
        activity.toActivityPage({...options})
        break;
      }
    }
  },
  /**
   * @description 领取优惠券
   * @param {*} options
   */
  async toReceiveCopon(options) {
    if (!app.checkSignInsStatus()) {
      app.signIn()
      return
    }
    const { moduleNumber } = options
    const { userID } = wx.getStorageSync('user') || {}
    const { unionid } = wx.getStorageSync('wxSnsInfo') || {}
    const params = {
      activityID: Number(this.activityID),
      customerID: userID,
      moduleNumber,
      unionid: unionid
    }
    const result = await app.api.receiveCouponBySubject(params)
    const { receiveStatus, message } = result.data
    if (receiveStatus !== 'S') {
      this.showToast(message)
      return
    }
    this.showToast(message)
    // 领券成功刷新优惠券模块
    this.refreshCouponModule(moduleNumber)

    sensors.track('MPClick', 'bgxxSubjectActivityUseCoupon');
  },
  async refreshCouponModule(curModuleNumber) {
    const couponModuleMap = await this.getCouponModule()
    const { subjectData } = this.data
    let setIndex = 0, setCoponList = [];
    subjectData.forEach( (item, index) => {
      let {
        type,
        activityCouponList = []
      } = item
      if (type === '4') { // 优惠券模块
        const curActivityCouponList = activityCouponList.filter( item => {
          return couponModuleMap[item.moduleNumber]
        })
        const { moduleNumber } = curActivityCouponList[0] || {}
        if (curModuleNumber === moduleNumber) {
          setIndex = index
          setCoponList = couponModuleMap[moduleNumber]
        }
      }
    })
    const key = `subjectData[${setIndex}].couponList`
    let obj = {}
    obj[key] = setCoponList
    this.setData(obj)
  },
  async getCouponModule() {
    const { userID } = wx.getStorageSync('user') || {}
    const { unionid } = wx.getStorageSync('wxSnsInfo') || {}
    const { selectAddressInfo } = wx.getStorageSync('bgxxSelectLocateInfo') || {}
    const { deliveryCenterCode } = selectAddressInfo || {}
    const params = {
      customerID: userID || -1,
      unionid,
      activityID: Number(this.activityID),
      deliveryCenterCode
    }
    try {
      const result = await app.api.getSubjectCouponModule(params)
      this._data.systemTime = result.systemTime
      const couponModuleMap = {}
      result.data.couponModuleList.forEach( item => {
        couponModuleMap[item.moduleNumber] = [item]
      })
      return couponModuleMap
    } catch(err) {
      return {}
    }
  },
   /**
   * @description 消息弹窗
   * @param {string} - msg 消息文本
   */
  showToast(msg) {
    if (!msg) return
    wx.showToast({
      title: msg,
      icon: "none",
      duration: 2500
    })
  },
  /**
   * 加入购物车数据埋点
   */
  addCartCall(e) {
    const { goodsData = {} } = e.detail;
    sensors.track('MPClick', 'bgxxSubjectActivityAddCart', {
      ...getBgxxSaleReportInfo({
        goodsData,
        referrer:2,
        goodsActivityObj:this.data.goodsActivityObj
      }),
      SKU_ID: goodsData.number || '',
      SKU_Name: goodsData.name || '',
    });
    this.addCart(e);
  },
  /**
   * 商品图片数据埋点
   */
  openDetail(e) {
    const { detail = {} } = e;
    sensors.track('MPClick', 'bgxxSubjectActivityGoodsPic', {
      SKU_ID: detail.number || '',
      SKU_Name: detail.name || '',
    });
  },
  /**
   * 设置分享信息
   */
  setShareData(obj) {
    let shareInfo = {};
    if (['', 'Y'].includes(obj.isShareOpen)) {
      wx.showShareMenu({
        withShareTicket: false,
        menus: ['shareAppMessage'],
      });
      // 使用后台配置的分享
      shareInfo.title = obj.title;
      shareInfo.imageUrl = obj.imageUrl;
      this.setData({ shareInfo: shareInfo });
    }
  },
  async getActivity() {
    wx.showLoading({
      title: '加载中',
    })
    try {
      const data = await this.getSubjectDetail()
      console.log(data)
      // const { selectAddressInfo, selectStoreInfo } =
      //   wx.getStorageSync('bgxxSelectLocateInfo') || {};
      // const { cityCode, deliveryCenterCode, cityID } =
      //   selectAddressInfo || {};
      // const { storeID, storeCode } = selectStoreInfo || {};
      // const { userID } = wx.getStorageSync('user') || {};
      // const params = {
      //   storeID,
      //   cityID,
      //   customerID: userID || -1,
      //   cityCode,
      //   storeCode,
      //   deliveryCenterCode,
      //   activityID,
      // };
      const { picDomain } = this.data;
      // await app.api.getActivityBgxxDetail(params);
      const {
        shareMajor,
        cardSharePic,
        isShareOpen,
        // activityStatus,
        togetherModuleList,
        pageTitle = '专题活动',
        name: activityName,
        pageConfig = {
          backgroundColor: '#000', //页面背景色
          navigationBarColor: '#fff', //导航栏背景色
          navigationButtonColor: '#00C587', //导航按钮色
          navigationDefaultColor: '#000', //导航字体默认色
          navigationSelectedColor: '#fff', //导航字体选中色
        },
      } = data;
      //   设置分享数据
      this.setShareData({
        isShareOpen: isShareOpen,
        title: shareMajor,
        imageUrl: cardSharePic ? `${picDomain}${cardSharePic}` : '',
      });
      const goodsParamList = [];
      togetherModuleList
        .filter((el) => ['2', '3', '6'].includes(el.type))
        .forEach((el) => {
          el.goodsList.forEach((good) => {
            goodsParamList.push({ id: good.id, goodsSn: good.goodsSn });
          });
        });
      // 过滤掉除图片 优惠券，商品列表、导航栏外的其他类型数据
      const filteredTogetherModuleList = togetherModuleList.filter((item) =>
        ['1', '2', '3', '4', '6', '999', '7'].includes(item.type)
      );
      // 处理商品售罄数据
      const list = this.handleSellOutList(
        filteredTogetherModuleList,
        'goodsList'
      );
      let navList = [];
      let navItemIdx = 0;
      let firstNavId = 0
      list.forEach((topic) => {

        topic.navItemIdx = navItemIdx;
        topic.id = `scroll-${topic.modulePriority}`
        navItemIdx++;
        // if (topic.type === '4') {
        //   topic.couponList.forEach((coupon) => {
        //     this.mapCouponParams(coupon);
        //     //   coupon.modulePriority = topic.modulePriority
        //   });
        // }
        // 导航栏
        if (topic.type === '999') {
          topic.navigationList.forEach((nav, idx) => {
            nav.id = `scroll-${nav.priority}`;
            nav.picUrl = nav.pic ? this.data.picDomain + nav.pic : ''
          });
          navList = topic.navigationList;
          firstNavId = navList[0].priority
        }
        // 导航楼层模板添加id
        if (topic.type === '7') {
          const { priority } = topic.navigationList[0];
          navList.forEach((nav) => {
            if (nav.priority === priority) {
              topic.id = nav.id;
            }
          });
        }
      });
      this.setData({
        subjectData: list,
        'goodsExposeData.activityName': pageTitle,
        navList,
        pageConfig,

        hasData: this.checkHasData(list),
        // scrollInfo: {
        //   scrollId: firstNavId,
        // },
      });
      // 解决自动滚动导致监听交叉不触发问题
      if (!this._data.isInitSetSelectNav) {
        const scrollId = this.getSelectNav(navList)
        this.setData({
          scrollInfo: {
            scrollId: scrollId || 0
          }
        })
      }
      this._data.isInitSetSelectNav = true
      // list.forEach( (item, index) => {
      //   // setTimeout(() => {
      //     delete item.goodsList
      //     delete item.sellList
      //     // delete item.goodsList
      //     const key =  `subjectData[${index}]`
      //     const obj = {}
      //     obj[key] = item
      //     console.log(item)
      //     this.setData(obj)
      //   // })
      // })
      setTimeout(() => {
        this.observerAnchor();
        this.setData({
          // scrollInfo: { scrollId: this.data.modulePriority },
          toView: `scroll-${this.data.modulePriority}`,
        })
      }, 500);
      setTimeout(() => {
        this.data.isShowBlank && this.setData({
          isShowBlank: false,
        })
        !this.data.isScrollAnimation && this.setData({
          isScrollAnimation: true
        })
      }, 700)
      // if (activityStatus !== 'N') {
      //   // 非进行中的活动
      //   this.showActivityErrorTip(activityStatus);
      // }
      // 记录组合品商品数据
      // filteredTogetherModuleList.forEach(({ showList }) =>
      //   combineGoods.setCache(showList)
      // );
      // 获取商品买赠标签
      this.getGoodsActivitys(goodsParamList);
      app.loadShopCart({ isXmdxPage: true });
      this.checkCartShow();
      if (this.getCurRoute() === this._data.curRoute) {
        wx.setNavigationBarTitle({
          title: pageTitle,
        })
      }
      if (!this._data.isReportPageShow) {
        //  上报页面浏览事件
        sensors.pageScreenView({
          $url_query: this.data.options,
          activity_ID: this.activityID || '',
          activity_Name: activityName
        })
        this._data.isReportPageShow = false
      }
      wx.hideLoading()
    } catch(err) {
      wx.hideLoading()
      console.error(err)
      this.showActivityErrorTip('活动太火爆了，休息一下再试试吧')
      log.error('次日达综合专题报错', err)
    }
  },
    /**
   * @description
   * @param {*} topicList
   */
  getSelectNav(navList) {
    const { modulePriority } = this.data
    if (!modulePriority) return
    const selectNav = navList.filter( item => item.priority <= modulePriority)
    if (!selectNav.length) {
      return
    }
    return selectNav[selectNav.length - 1].priority
  },
  checkHasData(topicList = []) {
    let goodsCount = 0, noGoods = 0;
    let picCount = 0, noPic = 0;
    let couponCount = 0, noCoupon = 0;
    let navCount = 0, noNav = 0;
    topicList.forEach( module => {
      const { type } = module;
      switch (type) {
        case '1':
          picCount++
          if (!module.activityPicList.length) {
            noPic++
          }
          break;
        case '2':
        case '3':
        case '6':
          goodsCount++
          if (!module.showList?.length && !module.sellOutList?.length) {
            noGoods++
          }
          break;
        case '4':
          couponCount++
          if (!module.couponList.length) {
            noCoupon++
          }
          break;
        case '999':
          navCount++
          if (module.navigationList.length < 3) {
            noNav++
          }
          break;
        default:
          break;
      }
    })
    if (picCount === noPic && goodsCount === noGoods && couponCount === noCoupon && navCount === noNav) {
      return false
    }
    return true
  },
  /**
   * @desc 匹配优惠券字段
   * @param {Object} coupon
   */
  mapCouponParams(coupon) {
    // 3.5.1需求
    // userType: 1:所有用户(区分新老用户) 2:所有用户(区分心享用户) 3:所有用户(不区分) 4:新用户  5:心享用户 6:标签用户  7:社群用户
    // 图片字段对应的用户类型
    // newImgsUrl，newReceiveImgsUrl: 新用户,心享会员用户,标签用户,社群用户,所有用户
    // oldImgsUrl，oldReceiveImgsUrl: 老用户,非心享会员,非标签用户
    // 没有命中时取banner字段的数据
    // 但数据不会同时出现
    const {
      banner,
      newImgsUrl,
      newReceiveImgsUrl,
      oldImgsUrl,
      oldReceiveImgsUrl,
    } = coupon;
    if (banner) {
      coupon.pic = banner.picUrl;
      coupon.openType = banner.openType;
      coupon.openValue = banner.openValue;
      coupon.isBanner = true;
      coupon.miniProgram = banner.miniProgram;
      return;
    }
    if (newImgsUrl) {
      coupon.pic = coupon.newImgsUrl;
      coupon.receicePic = newReceiveImgsUrl;
      return;
    }
    if (oldImgsUrl) {
      coupon.pic = coupon.oldImgsUrl;
      coupon.receicePic = oldReceiveImgsUrl;
      return;
    }
  },
  /**
   * @desc 监听导航楼层位置，使导航cover按钮跳转至对应位置
   */
  observerAnchor() {
    obAnchor = wx
      .createIntersectionObserver(this, {
        observeAll: true,
      })
      .relativeTo('#ob-reference')
      .observe('.inner-anchor-nav', (res) => {
        // relativeTo('#fixNav') 只监听高度很小的元素，所以每次触发的只有一个元素
        // 在点击跳转时不触发
        if (this.data.clickToScroll) return;
        this.setScrollInfo(res);
      });
  },
  stickyNav(value) {
    const { id } = value.detail;
    this.setData({
      clickToScroll: true,
      scrollInfo: { scrollId: id },
      toView: `scroll-${id}`,
    });
  },
  // noStickyNavHide(bool) {
  //   console.log(bool)
  //   setTimeout(() => {
  //     this.setData({ showFixNav: bool.detail });
  //   }, 60);
  // },
  isScroll: debounce(function () {
    this.setData({
      clickToScroll: false,
    });
  }, 300),
  setScrollInfo: debounce(function () {
    const {
      dataset: { startid: startId, endid: endId },
    } = arguments[0];
    this.setData({
      scrollInfo: {
        scrollId: typeof startId !== 'undefined' ? startId : endId,
      },
    });
  }, 100),
  getCurRoute() {
    const routes = getCurrentPages()
    return routes[routes.length - 1].route
  },
  backPage() {
    wx.navigateTo({
      url: '/pages/xxshop/index/index',
    })
  }
});
