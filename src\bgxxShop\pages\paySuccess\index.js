// pages/mall/paySuccess/index.js
const config = require('../../../utils/config')
import styleConfig from '../../../utils/goodsStyleConfig'
import operateCartMixin from '../../../mixins/bgxx/operateCartMixin'
import cartAnimate from '../../../mixins/bgxx/cartAnimate'
import storeBinding from '../../../mixins/bgxx/storeBinding'
import sensors from '../../../utils/report/sensors'
import bgxxStores from '../../../stores/module/bgxxStore'
import { updateUserDeviceInfo } from '../../../service/userService'
import { FreshGoods } from '../../../service/freshGoods'
import { toSubShopCart } from '../../../utils/util'

let app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    saveMoney: -1,
    paymentOrderID: '',
    goodsList: [],
    styleConfig: styleConfig,
    picDomain: config.baseUrl.PAGODA_PIC_DOMAIN,
    cycleBuyTip: '',
    bgxxTips: {},  // 心享会员提示语
    navBarBgColor: 'linear-gradient(to bottom, #FD97A6 0%,  #FB8898 100%)', // 导航栏背景颜色
    navBarColor: '#fff', // 导航栏字体颜色
    // bg_top_bg:'https://resource.pagoda.com.cn/group1/M21/56/AC/CmiLkGELq3GAKT7_AAChh8DI56w977.png'
    bg_top_bg:'https://resource.pagoda.com.cn/group1/M21/58/DC/CmiWa2Ebb1eAWlCGAABwWppSPLw347.png'
  },
  _data: {
    // 次日达推荐商品对象
    freshGoods: null
  },
  mixins: [operateCartMixin, cartAnimate, storeBinding],
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options = {}) {
    const { paymentOrderID } = options
    this.setData({
      paymentOrderID
    })
    this.getBgxxPaySuccessData(paymentOrderID)
    // 获取banner
    this.getBgxxPaySuccessBanner()
    //  添加支付成功页上报信息
    // sensors.track('MPClick', 'bgxxPaySuccessPage')
    // 上报本次支付成功的设备信息
    updateUserDeviceInfo()
    this.getFavoriteGoods()
  },
  onShow() {
    this.getCartCount()
    // 自刷新数量
    bgxxStores.updateCartCount(0,true)
    //  上报页面浏览事件
    sensors.pageScreenView()
  },
  /**
   * 获取推荐商品
   */
  async getFavoriteGoods() {
    let that = this
    const { bgxxCityInfo: { cityCode = '', deliveryCenterCode = '', storeCode } = {} } = app.globalData
    if (!cityCode || !deliveryCenterCode) {
      return
    }
    const params = {
      cityCode
    }
    const { data: { allGoodsList = [], recommendGoodsList = [] } = {} } = await app.api.getBgxxMaybeLike(params)

    // 整合数据
    this._data.freshGoods = this._data.freshGoods || (new FreshGoods({
      cityCode: cityCode,
      deliveryCenterCode: deliveryCenterCode,
      storeCode: storeCode
    }))
    // 查询商品详情
    const completeRecommendGoodsList = await this._data.freshGoods.getComplateGoodsList(recommendGoodsList, {
      filterSaleStatus: true,
      filterGift: true,
      filterStock: true,
    })
    const completeAllRecommendGoodsList = await this._data.freshGoods.getComplateGoodsList(allGoodsList, {
      filterSaleStatus: true,
      filterGift: true,
      filterStock: true,
    })
    const res = ([...completeRecommendGoodsList, ...completeAllRecommendGoodsList] || []).splice(0, 8)

    that.setData({
      goodsList: res
    })
    if (!!that.data.goodsList && that.data.goodsList.length > 0) {
      this.setCartBusPos()
      // 获取商品买赠标签
      this.getGoodsActivitys(that.data.goodsList)
    }
  },

  // 点击查看订单，跳转至订单列表页面。
  toOrder() {
    sensors.track('MPClick', 'bgxxPaySuccessCheckOrder')
    const { prePageName } = app.globalData
    app.globalData.prePageName = 'paySuccess'
    // 从周期购页面跳转过来
    if (prePageName === 'cycleBuy') {
      wx.redirectTo({
        url: '../../../bgxxUser/pages/cycleBuy/index',
      })
      return true
    }
    // 默认跳转订单页次日达页面
    wx.navigateTo({
      url: '/userB/pages/orderList/index',
    })
  },

  // 点击继续逛逛，跳转至商城首页
  toMallIndex() {
    sensors.track('MPClick', 'bgxxPaySuccessToMallIndex')
    wx.navigateTo({
      url: '/pages/xxshop/index/index',
    })
  },

  // 点击继续逛逛，跳转至商品详情页面
  toGoodsDetail(e) {
    wx.redirectTo({
      url: '/bgxxShop/pages/goodDetail/index?toDetail=' + JSON.stringify({
        goodsID: e.currentTarget.dataset.id
      }),
    })
  },

  // banner广告运营可配置跳转
  skipGoodsDetail() {
    const bannerInfo = this.data.bannerInfo
    if (!bannerInfo) { return }
    this.configBannerToSkip(bannerInfo)
    sensors.track('MPClick', 'bgxxPaySuccessBanner', {
      banner_name: bannerInfo.name
    })
  },
  openCart() {
    sensors.track('MPClick', 'bgxxPaySuccessOpenCart')
    toSubShopCart()
  },
  /**
   * 商品图片数据埋点
   */
  openDetail(e){
    const {
        detail = {}
    } = e
    sensors.track('MPClick', 'bgxxPaySuccessGoodsPic',{
        SKU_ID: detail.number || '',
        SKU_Name: detail.name || ''
    })
  },
  /**
   * 商品图片数据埋点
   */
  paySuccessAddCart(e){
    const {
      goodsData = {}
    } = e.detail
    sensors.track('MPClick', 'bgxxPaySuccessGoodsAddCart',{
        SKU_ID: goodsData.number || '',
        SKU_Name: goodsData.name || ''
    })
    this.addCart(e)
  },
  // 获取支付成功数据
  async getBgxxPaySuccessData(paymentOrderID) {
    try {
      const res = await app.api.getBgxxPaySuccessData({ customerID: app.globalData.customerID, paymentOrderID })
      if (res.data) {
        const {
          vipFree,
          cycleBuyTip = ''
        } = res.data
        this.setData({
          cycleBuyTip,
          saveMoney: Number(vipFree) * 100
        })
      }
    } catch(err) {}
  },
  // 获取banner
  async getBgxxPaySuccessBanner() {
    try {
      const { customerID = -1, bgxxCityInfo:{ cityCode:organizationCode = '', storeCode } } = app.globalData
      const result = await app.api.getBgxxPaySuccessBanner( { customerID, organizationCode, storeCode })
      this.setData({
        bannerInfo: result.data || {}
      })
    } catch(err) {}
  }
})
