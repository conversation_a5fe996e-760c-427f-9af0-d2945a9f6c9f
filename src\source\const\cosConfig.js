// 读取商品快照使用的cos
const COS_GLOBAL = 'COS_GLOBAL'
// 前端上传图片使用的cos
const COS_WXAPP = 'COS_WXAPP'
// 体验家ECM配置COS
const COS_ECM = 'COS_ECM'
// 云闪付优惠活动信息配置
const COS_UNIONPAY = 'COS_UNIONPAY'
// 前端上传dskhd桶 sfz图片使用的cos
const REALNAME_CHANGE = 'REALNAME_CHANGE'
// 前端上传dskhd桶上传用户头像
const AVATAR = 'AVATAR'

const dskhd_cos_config = {
  draft: {
    bucket: 'test--dskhd--cos-1317204308',
    region: 'ap-guangzhou',
  },
  test: {
    bucket: 'test--dskhd--cos-1317204308',
    region: 'ap-guangzhou',
  },
  staging: {
    bucket: 'staging--dskhd--cos-1317204308',
    region: 'ap-guangzhou',
  },
  prod: {
    bucket: 'prod--dskhd--cos-1317204308',
    region: 'ap-guangzhou',
  },
}

const wxapp_cos_config = {
  draft: {
    bucket: "fastdfs-test-1251596386",
    region: "ap-guangzhou"
  },
  test: {
    bucket: "fastdfs-test-1251596386",
    region: "ap-guangzhou"
  },
  staging: {
    bucket: "fastdfs-test-1251596386",
    region: "ap-guangzhou"
  },
  prod: {
    bucket: "fastdfs-prod-1251596386",
    region: "ap-guangzhou"
  }
}

const COS_CONFIG_MAP = {
  COS_GLOBAL: {
    draft: {
      bucket: "serverless-cdn-test-1251596386",
      region: "ap-guangzhou"
    },
    test: {
      bucket: "serverless-cdn-test-1251596386",
      region: "ap-guangzhou",
      domain: 'https://serverless-cdn-test-1251596386.cos.ap-guangzhou.myqcloud.com'
    },
    staging: {
      bucket: "serverless-cdn-uat-1251596386",
      region: "ap-guangzhou",
      domain: 'http://eshop-cos.uat.pagoda.com.cn'
    },
    prod: {
      bucket: "serverless-cdn-prod-1251596386",
      region: "ap-guangzhou",
      domain: 'https://eshop-cos.prod.pagoda.com.cn'
    }
  },
  COS_WXAPP: wxapp_cos_config,
  COS_ECM: wxapp_cos_config,
  COS_UNIONPAY: wxapp_cos_config,
  REALNAME_CHANGE: dskhd_cos_config,
  AVATAR: dskhd_cos_config,
}
const COS_UPLOAD_TYPE_ENUM = {
  REFUND: 'REFUND', // 退款类型
  EVALUATION: 'EVALUATION', // 评价类型
  IMAGES: 'IMAGES', // 普通图片
  FEEDBACK: `FEEDBACK`, // 意见反馈
  AVATAR: `AVATAR`, // 头像
  CARD: 'CARD' // 身份证图片
}
const COS_UPLOAD_DIRECTORY_MAP = {
  [COS_UPLOAD_TYPE_ENUM.REFUND]: `dsxcx/refund`,
  [COS_UPLOAD_TYPE_ENUM.EVALUATION]: `dsxcx/evaluation`,
  [COS_UPLOAD_TYPE_ENUM.IMAGES]: `dsxcx/images`,
  [COS_UPLOAD_TYPE_ENUM.FEEDBACK]: `dsxcx/feedback`,
  [COS_UPLOAD_TYPE_ENUM.FEEDBACK]: `dsxcx/feedback`,
  [COS_UPLOAD_TYPE_ENUM.AVATAR]: `avatar`,
  [COS_UPLOAD_TYPE_ENUM.CARD]: `drac`,
}
const ESHOP_COS_CONFIG = {
  'debug': 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com/grayFile-mixpay.json',
  'test': 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com/gray/wxapp/test/wxappGrayFile.json',
  'staging': 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com/gray/wxapp/uat/wxappGrayFile.json',
  'prod': 'https://serverless-cdn-prod-1251596386.cos.ap-guangzhou.myqcloud.com/gray/wxappGrayFile.json',
}

const SSE_GRAY_JSON = {
  'debug': 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com/grayFile-mixpay.json',
  'test': 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com/gray/test/sseGray.json',
  'staging': 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com/gray/uat/sseGray.json',
  'prod': 'https://serverless-cdn-prod-1251596386.cos.ap-guangzhou.myqcloud.com/gray/sseGray.json',
}

const FRUITFANS_GRAY_JSON = {
  'debug': 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com/gray/fruitFansGray.json',
  'test': 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com/gray/fruitFansGray.json',
  'staging': 'https://serverless-cdn-uat-1251596386.cos.ap-guangzhou.myqcloud.com/gray/fruitFansGray.json',
  'prod': 'https://serverless-cdn-prod-1251596386.cos.ap-guangzhou.myqcloud.com/gray/fruitFansGray.json',
}

const APP_CONFIG = {
  'debug': 'https://serverless-cdn-prod-1251596386.cos.ap-guangzhou.myqcloud.com/gray/wxapp/prod/wxapp-config.json',
  'test': 'https://serverless-cdn-prod-1251596386.cos.ap-guangzhou.myqcloud.com/gray/wxapp/prod/wxapp-config.json',
  'staging': 'https://serverless-cdn-prod-1251596386.cos.ap-guangzhou.myqcloud.com/gray/wxapp/prod/wxapp-config.json',
  'prod': 'https://serverless-cdn-prod-1251596386.cos.ap-guangzhou.myqcloud.com/gray/wxapp/prod/wxapp-config.json',
}

const REPORT_JSON = 'https://eshop-cos.prod.pagoda.com.cn/gray/wxapp/sensorsReport.json'
const CONFIG_JSON_URL = 'https://eshop-cos.prod.pagoda.com.cn/gray/wxapp/config.json'
/**神策埋点页面配置文件 */
const PAGE_CONFIG_JSON = 'https://eshop-cos.prod.pagoda.com.cn/gray/wxapp/sensorsPageConfigMap.json'
module.exports = {
  COS_GLOBAL,
  COS_WXAPP,
  COS_ECM,
  COS_UNIONPAY,
  REALNAME_CHANGE,
  AVATAR,
  COS_CONFIG_MAP,
  COS_UPLOAD_TYPE_ENUM,
  COS_UPLOAD_DIRECTORY_MAP,
  ESHOP_COS_CONFIG,
  SSE_GRAY_JSON,
  REPORT_JSON,
  CONFIG_JSON_URL,
  PAGE_CONFIG_JSON,
  FRUITFANS_GRAY_JSON,
  APP_CONFIG,
}
