const ENV = (() => {
  try {
    const envJson = require('../env.js')
    const { env } = envJson
    console.log('there is a env json')
    return env
  } catch (error) {
    console.log('no such file')
  }
  // 切环境改 ENV test staging prod
  return 'test'
})()

/**
 * 是否打印超级神策日志
 * 如需在控制台中过滤日志，请输入过滤条件: superLog
 */
const SHOW_SENSORS_SUPER_LOG = false

// 云标识
const CLOUD_FLAG = {
  'tx': 'tencent-',
  'hw': 'huawei-'
}
// serverless环境变量
const SL_EXC = {
  test: 'test_exc',
  staging: 'uat_exc',
  prod: 'exc'
}

/**
 * 在这里放一个本地调试地址，需要的可以自取

http://localhost:3001/draft_exc

 */

const baseUrl = {
  debug: {
    // 开发环境
    PAGODA_DSN_DOMAIN: 'https://a8-domain.pagoda.com.cn:11047', //新电商测试:11047
    PAGODA_PIC_DOMAIN: 'http://fastdfs-download.test.pagoda.com.cn', //图片地址11052测试,14052uat
    PAGODA_APP_DOMAIN: 'https://a8-domain.pagoda.com.cn:11046', //APP测试:11046
    PAGODA_EMS_DOMAIN: 'https://a8-domain.pagoda.com.cn:33002', //小程序登录域名
    PAGODA_SMS_DOMAIN: 'https://a8-domain.pagoda.com.cn:33001', //小程序短信发送
    PAGODA_UPLOAD_DOMAIN: 'http://fastdfs-uploading.test.pagoda.com.cn', //上传图片url
    sensorsUrl: 'https://sensorsdata.pagoda.com.cn:8106/sa?project=default', // 神策数据分析地址
    BGXX_API_DOMAIN: 'https://a8-domain.pagoda.com.cn:11059', // 百果心享api
    activityUrl: 'https://baiguoyuan-test.01lb.com.cn', // 零一活动url
    serverlessApi: 'https://serverless-runtime-api.kd1.pagoda.com.cn/dev_exc', // serverless2.0接口域名
    H5_WEB_DOMAIN: 'http://h5-web.dev.pagoda.com.cn', //跳转到 h5
    SSE: 'https://sse.dskhd.tencent-test.pagoda.com.cn',
  },
  // 测试环境
  test: {
    PAGODA_DSN_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-test.pagoda.com.cn', //小程序新地址
    PAGODA_PIC_DOMAIN: 'http://fastdfs-download.test.pagoda.com.cn', //图片正式环境
    PAGODA_EMS_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-test.pagoda.com.cn', // 小程序登录
    PAGODA_APP_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-test.pagoda.com.cn', //APP测试环境地址
    PAGODA_SMS_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-test.pagoda.com.cn', // 小程序短信发送
    PAGODA_UPLOAD_DOMAIN: 'http://fastdfs-uploading.test.pagoda.com.cn', //上传图片url
    sensorsUrl: 'https://sensorsdata.pagoda.com.cn:8106/sa?project=default', // 神策数据分析地址
    BGXX_API_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-test.pagoda.com.cn', // 百果心享api
    activityUrl: 'https://baiguoyuan-test.01lb.com.cn', // 零一活动url
    serverlessApi: 'http://dskhd-middle-server-api.dskhd.tencent-test.pagoda.com.cn/test_exc', // serverless2.0接口域名-腾讯云
    dskhdSlrApi: 'http://dskhd-middle-server-api.dskhd.tencent-test.pagoda.com.cn/test_exc',
    H5_WEB_DOMAIN: 'https://eshop-h5-web.dsh5.tencent-test.pagoda.com.cn', //跳转到 h5
    DSKHD_WEB_DOMAIN: 'https://dskhd-web.dsxcx.tencent-test.pagoda.com.cn',
    SSE: 'https://sse.dskhd.tencent-test.pagoda.com.cn',
  },
  // staging环境
  staging: {
    PAGODA_DSN_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-staging.pagoda.com.cn', // 新电商UAT:14064
    PAGODA_PIC_DOMAIN: 'http://fastdfs-download.test.pagoda.com.cn', //图片地址11052测试,14052uat
    PAGODA_APP_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-staging.pagoda.com.cn', //APP UAT环境地址
    PAGODA_EMS_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-staging.pagoda.com.cn', //小程序登录域名
    PAGODA_SMS_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-staging.pagoda.com.cn', //小程序短信发送
    PAGODA_UPLOAD_DOMAIN: 'http://fastdfs-uploading.test.pagoda.com.cn', //上传图片url
    sensorsUrl: 'https://sensorsdata.pagoda.com.cn:8106/sa?project=default', // 神策数据分析地址
    BGXX_API_DOMAIN: 'http://dskhd-middle-server-api.dskhd.tencent-staging.pagoda.com.cn', // 百果心享api
    activityUrl: 'http://baiguoyuan-uat.01lb.com.cn', // 零一活动url
    serverlessApi: 'http://dskhd-middle-server-api.dskhd.tencent-staging.pagoda.com.cn/uat_exc', // serverless2.0接口域名
    dskhdSlrApi: 'http://dskhd-middle-server-api.dskhd.tencent-staging.pagoda.com.cn/uat_exc',
    H5_WEB_DOMAIN: 'https://eshop-h5-web.dsh5.tencent-staging.pagoda.com.cn', //跳转心享h5
    DSKHD_WEB_DOMAIN: 'http://dskhd.tencent-staging.pagoda.com.cn',
    SSE: 'https://sse.dskhd.tencent-staging.pagoda.com.cn',
  },
  // 正式环境
  prod: {
    PAGODA_DSN_DOMAIN: 'https://dskhd-middle-server-api.dskhd.tencent-prod.pagoda.com.cn', //小程序新地址
    PAGODA_APP_DOMAIN: 'https://dskhd-middle-server-api.dskhd.tencent-prod.pagoda.com.cn', //APP生产环境地址
    PAGODA_PIC_DOMAIN: 'https://resource.pagoda.com.cn', //图片正式环境
    PAGODA_EMS_DOMAIN: 'https://dskhd-middle-server-api.dskhd.tencent-prod.pagoda.com.cn', // 小程序登录
    PAGODA_SMS_DOMAIN: 'https://dskhd-middle-server-api.dskhd.tencent-prod.pagoda.com.cn', // 小程序短信发送
    PAGODA_UPLOAD_DOMAIN: 'https://fastdfs-uploading.pagoda.com.cn', //上传图片url
    BGXX_API_DOMAIN: 'https://dskhd-middle-server-api.dskhd.tencent-prod.pagoda.com.cn', // 百果心享api
    sensorsUrl: 'https://sensorsdata.pagoda.com.cn/sa?project=pgdClient', // 神策数据分析新地址
    activityUrl: 'https://baiguoyuan.01lb.com.cn', // 零一活动url
    serverlessApi: 'https://dskhd-middle-server-api.dskhd.tencent-prod.pagoda.com.cn/exc', // serverless接口域名
    hwServerlessApi: 'https://dskhd-middle-server-api.dskhd.tencent-prod.pagoda.com.cn/exc',
    dskhdSlrApi: 'https://dskhd-middle-server-api.dskhd.tencent-prod.pagoda.com.cn/exc',
    H5_WEB_DOMAIN: 'https://eshop-h5-web.dsh5.tencent-prod.pagoda.com.cn', //跳转心享new h5
    DSKHD_WEB_DOMAIN: 'https://dskhd-web.dsxcx.tencent-prod.pagoda.com.cn',
    SSE: 'https://sse.dskhd.tencent-prod.pagoda.com.cn',
  },
};

const addressLabels = [{
    borderColor: 'F88A0B',
    id: 1,
    labelBackgroundColor: 'D4FCE5',
    labelName: '家',
    labelNameColor: '00A644',
    type: '1',
  },
  {
    borderColor: '5D9DEB',
    id: 2,
    labelBackgroundColor: 'D9EAFF',
    labelName: '公司',
    labelNameColor: '3B94FF',
    type: '1',
  },
  {
    borderColor: '999999',
    id: 8,
    labelBackgroundColor: 'FFEED9',
    labelName: '父母家',
    labelNameColor: 'FF9924',
    type: '1',
  }
]

/**
 * 主题色配置
 */
const themeColorConfig = {
  fruit: '#008C3C', // 水果及时达
  vegetables: '#FF1F3A' // 蔬菜次日达
}

const defaultcityIDMap = {
  test: 1,
  staging: 1,
  prod: 1
}
const defaultCityInfo = {
  cityName: '深圳市',
  cityID: defaultcityIDMap[ENV] || defaultcityIDMap.test
}

// 一级tabbar页面路径
const tabBarPage = [
  '/pages/homeDelivery/index',
  '/pages/index/index',
  '/pages/category/index',
  '/pages/shopCart/index'
]

// 订单分类与对应icon图标映射
const orderHeadIconMap = {
  '及时达': 'orderList_fruit',
  '拼团': 'orderList_fightGroup',
  '门店': '', // 门店没有icon
  '接龙': 'orderList_jielong',
  '次日达': 'orderList_vegetable',
  'b2c': 'orderList_b2c',
  '试吃自提': 'orderList_tryEat'
}

// h5协议地址
const H5DOMAIN = baseUrl[ENV].H5_WEB_DOMAIN
const protocolUrl = {
  userRechargeRegulation: H5DOMAIN + '/app/userRechargeRegulation', // 《充值服务个人 信息授权声明》
  evaluationGoodsRegulation:  H5DOMAIN + '/app/evaluationGoodsRegulation', // 《评价服务用户行为规范》
  recommendedAlgorithmRegulation:  H5DOMAIN + '/app/recommendedAlgorithmRegulation', // 推荐算法
  prepaidCard: H5DOMAIN + '/app/prepaidCard', // 《百果园预付卡管理章程》
  terms: H5DOMAIN + '/app/terms', // 百果园用户服务协议
  privacyInfo: H5DOMAIN + '/app/privacyInfo', // 百果园用户隐私政策
  loginPrivacyInfo: H5DOMAIN + '/app/privacyLogin', // 百果园用户登录隐私政策
  shopPrivacyInfo: H5DOMAIN + '/app/privacyShop', // 百果园用户网上功能隐私政策
  memberAddService:  H5DOMAIN + '/app/memberAddService', // 百果园会员增值服务隐私政策
  infoService:  H5DOMAIN + '/app/infoService', // 百果园信息交互服务隐私政策
  customerServiceGuarantee:  H5DOMAIN + '/app/customerServiceGuarantee', // 百果园客服保障服务隐私政策
  returnGoods: H5DOMAIN + '/app/returnGoods', // 不好吃三无退货规则
  indemnity: H5DOMAIN + '/indemnity', // 超时赔券服务协议
  memberService: H5DOMAIN + '/app/memberService', // 百果心享会员服务协议
  walletPromise: H5DOMAIN + '/app/walletPromise', // 百果园会员钱包支付承诺协议
  convention: H5DOMAIN + '/app/convention', // 用户行为公约
  invoice: H5DOMAIN + '/app/invoice', // 发票服务开票说明
  invoiceRegulation: H5DOMAIN + '/app/invoiceRegulation',  //  发票服务个人信息授权声明
  benefitsDetail: H5DOMAIN + '/member/privilege', // 心享权益细则页
  // 错一赔十规则
  compensation: H5DOMAIN + '/app/compensation',
  // 百果园儿童个人信息保护规则及监护人须知
  childInfoProtection: H5DOMAIN + '/app/childInfoProtection',
  // 特定商品受赠顾客申请“三无退货”规则
  giftRefund: H5DOMAIN + '/app/protocolPage?type=SLDDSWT'
}

/**
 * 子协议类型枚举
 */
const subProtocolTypeEnum = {
  //  购物功能
  shop: 'shop',
  //  登录功能
  login: 'login',
  //  会员增值服务
  memberService: 'memberService',
  //  信息交互服务
  infoService: 'infoService',
  //  客服保障服务
  customerService: 'customerService'
}

// 同盾配置相关
const TDFP_OPT = {
  partnerCode: "baiguoyuan", // 请填入您的partner code
  appName: "baiguoyuan_xcx", // 请填入您的app name 同一个公司的不同小程序请填入不同的AppName
  channel: "baiguoyuan_xcx", // 请填入您的channel
  env: "PRODUCTION" 
}
// 腾讯图灵配置
const TURING = {
  channel: '109072',
}
const config = {
  appVersion: '5.3.6', // 小程序版本号
  baseUrl: baseUrl[ENV], // 请求baseUrl
  addressLabels, // 地址标签。请求失败时用词配置项
  ENV,
  prdUrl: baseUrl.prod,
  themeColorConfig,
  defaultCityInfo,
  tabBarPage,
  orderHeadIconMap,
  appId: 'wx1f9ea355b47256dd', // 百果园+小程序appid
  protocolUrl, // 协议地址
  subProtocolTypeEnum,
  AUTH_KEY: '0!RVXVMYAkxrezek',
  CLOUD_FLAG,
  SL_EXC,
  userCosEncryptKey: 'pagodainbase64de',
  TDFP_OPT,
  TURING,
  SHOW_SENSORS_SUPER_LOG,
}
module.exports = config
