const { recentBuy } = require('../../../service/userService');

var commonObj = require('../../../source/js/common').commonObj;
var timer = null
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    orderId: '',
    fromTo: '',
    groupId: ''
  },
  _data: {
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (!options || !options.toObj) {
      return
    }

    this.initPageData(options.toObj)
  },
  initPageData(params) {
    const { mainOrderNo, fromTo,  groupId, customerId, payNos } = JSON.parse(params) || {};
    this.setData({
      orderId: mainOrderNo,
      fromTo,
      payNos,
      isGiftCard: fromTo === '礼品卡',
    })
    // 跳入这个支付失败已经不会携带mainOrderNo了,所以就不继续执行了
    // 及时达切换到中台，其他按照原来，
    // if (this.data.fromTo !== '及时达') {
    //   return
    // }
    // this.checkTimePayResult()
    // else {
    //   this.setData({
    //     groupId
    //   })
    //   const reqObj = {
    //     url: '/api/v1/order/checkPayStatus/' + customerId + '/' + mainOrderNo
    //   }
    //   this.getStatus(reqObj)
    // }
  },
  /**
   * 及时达检查支付结果（切换到中台）
   */
  async checkTimePayResult() {
    const that = this
    try {
      const fruitSku = recentBuy.getRecentBuySku()
      const { userID } = wx.getStorageSync('user') || { userID: '' }
      const res = await app.api.checkPayStatusCombine({ payNos: this.data.payNos, orderNo: this.data.orderId, fruitSku, customerID: userID })
      const { status = '' } = res.data
      if (status === 'SUCCESS') {
        wx.redirectTo({
          url: '/homeDelivery/pages/paySuccess/index?paySuccessObj=' + this.data.orderId
        })
      } else {
        timer = setTimeout(() => {
          that.checkTimePayResult()
        },1000)
      }
    } catch (error) {
      console.error(error)
    }
  },

  /**
   * 跳转礼品卡列表页
   */
  toGiftCardList: function(){
    wx.redirectTo({
      url: '/giftCard/pages/home/<USER>'
    })
  },
  toOrderDetail: function(){
    const { fromTo } = this.data
    wx.navigateTo({
      url: `/userB/pages/orderList/index${fromTo === '接龙' ? '?type=D' : ''}`,
    })
  },
  getStatus(reqObj) {
    var that = this;
    var newReqObj = reqObj;
    commonObj.requestData(reqObj, function (res) {
      if (Number(res.data.errorCode) === 0) {
        if (res.data.data && res.data.data.status === 'SUCCESS') {
          wx.redirectTo({
            url: '/fightGroups/pages/paySuccess/index?payOrderIDorGoodsOrderID=' + that.data.orderId + '&fromPTorderDetail=' + true + '&groupId=' + that.data.groupId
          })
        } else {
          timer = setTimeout(() => {
            that.getStatus(newReqObj)
          },1000)
        }
      }
    });
  },
  onHide() {
    this.clearPage()
  },
  onUnload() {
    this.clearPage()
  },
  clearPage() {
    clearTimeout(timer)
  }
})
