/**
 * 微信小程序将文本保存为文件
 * @param {object} options 参数
 * @param {string} options.fileName 文件名
 * @param {string} options.txtContent 文件内容
 * @param {'ascii'|'base64'|'binary'|'hex'|'ucs2'|'ucs-2'|'utf16le'|'utf-16le'|'utf-8'|'utf8'|'latin1'} [options.encoding] 文件编码
 * @returns {Promise<string>} 保存的文件路径
 */
export function writeFile({ fileName, txtContent, encoding = 'utf8' }) {
  const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().writeFile({
      filePath,
      data: txtContent,
      encoding,
      success: (res) => {
        console.log('写入文件成功', res)
        resolve(filePath)
      },
      fail: (res) => {
        console.error('写入文件失败', res)
        reject('写入文件失败', res.errMsg)
        getApp().monitor.report({
          message: '[上传]写入文件失败',
          res,
        })
      }
    })
  })
}

/**
 * 删除临时文件
 * @param {Object} options 参数
 * @param {string} options.filePath 临时文件路径
 * @returns {Promise<string>} 删除的文件路径
 */
export function deleteFile({ filePath }) {
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().unlink({
      filePath,
      success: (res) => {
        console.log('删除文件成功', res)
        resolve(filePath)
      },
      fail: (res) => {
        console.error('删除文件失败', res)
        reject('删除文件失败', res.errMsg)
        getApp().monitor.report({
          message: '[上传]删除文件失败',
          res,
        })
      }
    })
  })
}

/**
 * 生成一个随机文件名，根据用户ID和5位随机字符作为参数
 * @param {string} [customId] 用户ID
 * @returns 文件名
 */
export function generateFileName(customId) {
  if (!customId || customId === -1 || customId === '-1') {
    customId = ''
  }
  // 拼接日期和用户ID
  const prefix = new Date().Format('yyyyMMddHHmmss') + customId
  // 定义一个字符串，包含所有不为数字的字符
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  // 定义一个空字符串，用于存放随机字符
  let suffix = ''
  // 循环五次，每次从chars中随机选取一个字符，并拼接到suffix中
  for (let i = 0; i < 5; i++) {
    const index = Math.floor(Math.random() * chars.length)
    suffix += chars[index]
  }
  // 返回文件名，即前缀和后缀的组合
  return prefix + suffix
}

/**
 * 生成随机字符串
 * @param {Number} length 长度
 * @returns 随机字符串
 */
export function getRandomBase64Str(length = 32, hasBase64Chars = false) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789' + (hasBase64Chars ? '+/=' : '')
  let str = ''
  for (let i = 0; i < length; i++) {
    const index = Math.floor(Math.random() * chars.length)
    str += chars[index]
  }
  return str
}

/**
 * 根据COS文件url获取key
 * 根据 https://xx.myqcloud.com/drac/a/b.txt
 * 得到 /drac/a/b.txt
 */
export function getPathName(url) {
  if (!url || typeof url !== 'string') {
    return url
  }
  const match = url.match(/^https?:\/\/[^\/]+(.*)$/)
  if (!match) {
    return url
  }
  return match[1] || url
}

/**
 * 获取http网络资源完整url
 * @param {string} domain 域名
 * @param {string} url 网络路径
 * @returns {string} 完整url
 */
export function getHttpFullUrl(domain, url) {
  if (!url) {
    return ''
  }
  if (url.indexOf('http') !== -1) {
    return url
  }
  return domain + (url.startsWith('/') ? '' : '/') + url
}