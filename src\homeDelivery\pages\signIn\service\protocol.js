import config  from '../../../../utils/config'
// const h5Domain = config.baseUrl.H5_WEB_DOMAIN
const configProtocolUrl = config.protocolUrl
export const protocolKeyMap = /** @type { const } */({
  server: 'server',
  privacy: 'privacy',
  login: 'login',
  refund: 'refund',
  // giftRefund: 'giftRefund'
})
export const giftRefundProtocol = /** @type { const } */ ({
  // 跳转百果园三无退货规则
  // key: protocolKeyMap.giftRefund,
  text: '《特定商品受赠顾客申请“三无退货”规则》',
  // 暂时这么写,h5那边还没开发
  url: `/h5/pages/commonLink/index?pageUrl=${encodeURIComponent(`${configProtocolUrl.giftRefund}&close=${closeQurey()}`)}`
})
export const protocolLinkMap = /** @type { const } */({
  [protocolKeyMap.server]: {
    // 跳转百果园用户服务条款
    key: protocolKeyMap.server,
    text: '《百果园用户服务条款》',
    url: `/h5/pages/commonLink/index?pageUrl=${encodeURIComponent(`${configProtocolUrl.terms}?close=${closeQurey()}`)}`
  },
  [protocolKeyMap.privacy]: {
    // 跳转百果园隐私协议
    key: protocolKeyMap.privacy,
    text: '《百果园用户隐私协议》',
    url: `/h5/pages/commonLink/index?pageUrl=${encodeURIComponent(`${configProtocolUrl.privacyInfo}?query=${encodeURIComponent('close=N')}`)}`
  },
  [protocolKeyMap.login]: {
    // 跳转百果园登录服务条款
    key: protocolKeyMap.login,
    text: '《百果园登录政策》',
    url: `/h5/pages/commonLink/index?pageUrl=${encodeURIComponent(`${configProtocolUrl.loginPrivacyInfo}?close=${closeQurey()}`)}`
  },
  [protocolKeyMap.refund]: {
    // 跳转百果园三无退货规则
    key: protocolKeyMap.refund,
    text: '《“不好吃三无退货”规则》',
    url: `/h5/pages/commonLink/index?pageUrl=${encodeURIComponent(`${configProtocolUrl.returnGoods}?close=${closeQurey()}`)}`
  },
})
export const protocolLinkList = /** @type { const } */([
  protocolLinkMap.server,
  protocolLinkMap.privacy,
  protocolLinkMap.login,
  protocolLinkMap.refund,
])

function closeQurey() {
  return getApp().checkSignInsStatus()?'Y':'N'
}