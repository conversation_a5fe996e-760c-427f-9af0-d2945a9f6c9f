<pagoda-popup
  visible="{{showPopup}}"
  round
  position="center"
  clickOverlayClose="{{false}}"
  title="服务协议与隐私保护"
  bind:onBack="popupClose"
  width="600rpx"
  zIndex="1000"
>
  <view class="popup-content">
    <view class="popup-protocol">
      <protocolLabel isPopup />
    </view>
    <template is="{{loginType}}"></template>
  </view>
</pagoda-popup>

<template name="direct">
  <view class="popup-btn" catch:tap="handleAgree">同意</view>
  <view class="popup-btn-cancel" catch:tap="handleDisagree">拒绝</view>
</template>

<template name="weixin">
  <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" bindtap="tapBtn" class="popup-btn button">同意</button>
  <view class="popup-btn-cancel" catch:tap="handleDisagree">拒绝</view>
</template>

<template name="verify">
  <view class="popup-btn" catch:tap="handleAgree">同意</view>
  <view class="popup-btn-cancel" catch:tap="handleDisagree">拒绝</view>
</template>
