// homeDelivery/pages/confirmOrder/components/mouth-card/index.js
const inTimeCouponDesc = {
  '1':'线上及时达',
  '2':'线下专享',
  '3':'线上线下通用',
};
const YearCardType = ['XY','Y']
const { protocolUrl } = require('../../../utils/config');
import utils from '../../../utils/util'
import sensors from '../../../utils/report/sensors'
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 飞享卡信息
    vipCardInfo: {
      type: Object,
      value: {},
      observer: 'setCardInfo',
    },
    // 是否禁止点击
    isDisabled: {
      type: Boolean,
      value: false,
    },
    // 心享会员节省金额
    saveMoney: {
      type: Number,
      value: 0,
      observer: 'setSaveMoney',
    },
    // 本单可用优惠券key
    useKey: {
      type: String || Number,
      value: '',
    },
    // 可用券批次号列表
    availableListKey:{
      type: Array,
      value: [],
      observer: 'setCanUseRenewCoupon',
    },
    // 券包内最佳券批次号
    selfBestCouponBatchNum:{
      type: String,
      value: '',
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showMouthCard: false,
    chooseMouthCard: false, //是否选中月卡,
    cardInfo: {},
    couponList: [],
    isReceive: false,
    totalVipCouponNum: 0,
    list:[],
    chooseRenewType:'',
    activity:{},//续费年卡送券活动
    selectedIndex:0,
    forbidShow: false,
    canUseRenewCoupon: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * @desc 勾选飞享会员
     * @param { boolean } status 是否展示月卡区域
     * @param { boolean } refresh 是否调用结算接口
     * */
    clickIcon({ status = true, refresh = true }) {
      if (this.data.cardInfo.money === 0 || this.data.isDisabled) return;
      const { chooseMouthCard } = this.data
      this.setData({
        chooseRenewType: !chooseMouthCard ? this.data.cardInfo.type : '',
        chooseMouthCard: !chooseMouthCard
      });
      if (!status) {
        this.setData({
          showMouthCard: status,
          chooseRenewType:'',
          chooseMouthCard:false,
          forbidShow: true
        });
      }
      this.triggerEvent('chooseMouthCard', {
        isSelected: !status ? status : this.data.chooseMouthCard,
        chooseRenewType: !status ? '' : this.data.chooseRenewType,
        giftCouponInfo: {},
        activityCode:'',
        buryingPointType: this.data.cardInfo.type,
        refresh,
        ruleLadderId:''
      });
    },
    /**
     * @desc 勾选续费会员
     * */
    clickListIcon(e) {
      const { type = '' } = e.currentTarget.dataset
      if (this.data.cardInfo.money === 0 || this.data.isDisabled) return;
      let resultType = type
      const { chooseRenewType = '' } = this.data
      // 点击已选择项，为取消
      if(resultType === chooseRenewType) {
        resultType = ''
      }
      this.resetCardInfo({
        chooseRenewType:resultType,
        selectedIndex:this.data.selectedIndex
      })
      this.setData({
        chooseRenewType: resultType,
      });
      const { activityCode = '', coupon = {}, ruleLadderId = '' } = this.data.activity
      this.triggerEvent('chooseMouthCard', {
        isSelected: resultType !== '',
        chooseRenewType: resultType,
        giftCouponInfo: YearCardType.includes(resultType) ? coupon: {}, // 送券详情
        activityCode: YearCardType.includes(resultType) ? activityCode : '',
        ruleLadderId: YearCardType.includes(resultType) ? ruleLadderId : '',
        buryingPointType:type,//仅用于埋点
        refresh:true,
      });
    },
     /**
     * @desc 更改月卡心享会员状态----仅限开通月卡
     * @param { boolean } selected 开通/取消月卡
     * @param { boolean } refresh 是否调用结算接口 默认调用
     * */
     changeVipCardSelected({ selected = true, refresh = true}){
      if (this.data.cardInfo.money === 0 || this.data.isDisabled) return;
      this.setData({
        chooseRenewType: selected ? this.data.cardInfo.type : '',
        chooseMouthCard: selected
      });
      this.triggerEvent('chooseMouthCard', {
        isSelected: selected,
        chooseRenewType: selected ? this.data.chooseRenewType : '',
        giftCouponInfo: {},
        activityCode:'',
        buryingPointType: this.data.cardInfo.type,
        refresh,
        ruleLadderId:''
      });
    },
    /**
     * @desc 设置本单可用
     * */
    setCanUseRenewCoupon(val) {
      if(val.length){
        const availableListKey  = val
        const { activity = {} } = this.data
        const { coupon:{ batchNum = '' } = {} } = activity
        this.setData({
          canUseRenewCoupon: availableListKey.includes(batchNum)
        })
      }
    },
    /**
     * @desc 获取本单可省金额后调整开通文案
     * */
    setSaveMoney(val) {
      const { cardInfo = {} } = this.data;
      if (val < 0 || !cardInfo.money) return;
      const isReceive = val >= cardInfo.money;
      this.setOpenContent({
        isReceive,
        saveMoney: val,
        cardInfo,
      });
    },
    /**
     * @desc 设置优惠券，权益信息
     * */
    setCardInfo(val) {
      const { chooseRenewType = '' } = this.data
      if(chooseRenewType) return //选择月卡后不在触发setCardInfo
      const activityObj = val.list.find(item=>YearCardType.includes(item.type))
      let newActObj = activityObj && activityObj.activity ? this.setSendCouponInfo(activityObj) : {}
      this.setData({
        list: val.list,
        activity:newActObj
      })
      // 默认选择第一项展示
      const select = val.list[this.data.selectedIndex]
      let { couponList = [] } = select;
      const isReceive = this.data.saveMoney >= select.money;
      let totalVipCouponNum = 0
      // couponWay：1.满减，3.立减，2.满折，4.立折，5.免运
      couponList.forEach((item) => {
        totalVipCouponNum += item.amount
        // 满减
        if (['1','3'].includes(item.couponWay)) {
          item.couponValue = Number((item.couponValue / 100).toFixed(2)) || 0;
          item.unit = '元';
        }
        // 满折
        else if (['2','4'].includes(item.couponWay)) {
          item.couponValue = Number((item.couponValue / 10).toFixed(1)) || 0;
          item.unit = '折';
        }
        // 免运券
        else if (item.couponWay === '5') {
          item.couponValue = Number((item.couponValue / 100).toFixed(2)) || 0;
          item.unit = '元';
        }
        item.couponSenceType =  this.getCouponSenceType({
          couponCategory: item.couponCategory,
          applicableBizType: item.applicableBizType
        })
        item.couponDescStr = item.couponSenceType === 'T'
          ? inTimeCouponDesc[item.couponUseChannel]
          : '线上次日达';
      });
      this.setData({
        showMouthCard: !!val.show && !!couponList.length,
        cardInfo: val.list[this.data.selectedIndex] || {},
        couponList,
        totalVipCouponNum
      });
      this.setOpenContent({
        isReceive,
        saveMoney: this.data.saveMoney,
        cardInfo: this.data.cardInfo,
      });
    },
    /**
     * @desc 获取优惠券类型
     * */
    getCouponSenceType({ couponCategory = '', applicableBizType = []}){
      if(couponCategory) return couponCategory
      if(applicableBizType.includes(0) || applicableBizType.includes(2)){
        return 'T'
      }else if(applicableBizType.includes(4)){
        return 'F'
      }else{
        return ''
      }
    },
    /**
     * @desc 设置开通文案
     * @param { Boolean } isReceive 是否一单回本
     * @param { Number } saveMoney 节省金额
     * @param { Object } cardInfo 月卡信息
     * */
    setOpenContent({ isReceive = false, saveMoney = 0, cardInfo = {} }) {
      // 开通文案
      let openTitle = '',
        openSub = '',
        fixOpenTitle = '',
        showModeMoney = ''
      if (cardInfo.type === 'M') {
        openTitle = '开通飞享月卡，享';
        fixOpenTitle = isReceive ? '开通会员，本单省' : '开通会员，享';
        showModeMoney = isReceive ?  utils.formatPrice(saveMoney) : cardInfo.couponTotalMoney
        openSub = isReceive ? '元' : '元券包';
      }
      Object.assign(cardInfo, {
        // 节省金额
        totalMoney: utils.formatPrice(saveMoney),
        showModeMoney,
        openTitle,
        fixOpenTitle,
        openSub,
      });
      this.setData({
        cardInfo,
        isReceive,
      });
      this.triggerEvent('updateOpenCardInfo', cardInfo);
    },
    /**
     * @desc 点击去《百果心享会员服务协议》
     * */
    clickAgreement(e) {
      const { type } = e.currentTarget.dataset;
      let pageUrl = encodeURIComponent(`${protocolUrl[type]}`);
      wx.navigateTo({
        url: `/h5/pages/commonLink/index?pageUrl=${pageUrl}`,
      });
    },
    /**
     * @desc 展示权益详情
     * */
    showBenfitsDetail(){
      this.triggerEvent('showBenfitsDetail',{
        url:this.data.cardInfo.rightsImageUrl
      })
      const { type = '' } = this.data.cardInfo
      if(!type) return
      const sensorsKey = YearCardType.includes(type) ? 'confrimOrderClickYearCardBenefits' : 'confrimOrderClickMonthCardBenefits'
      sensorsKey && sensors.track('MPClick', sensorsKey)
    },
    /**
     * @desc 重新设置cardInfo
     * */
    resetCardInfo({chooseRenewType = '',selectedIndex = 0}){
      let cardInfo = {}
      const { list = [] } = this.data.vipCardInfo
      if(chooseRenewType){
        cardInfo = list.find(item=>item.type === chooseRenewType) || {}
      }else{
        cardInfo = list[selectedIndex]
      }
      this.setData({
        cardInfo
      })
    },
    /**
     * @desc 续费年卡赠送券详情
     * */
    setSendCouponInfo(activityObj){
      const { couponList = [] } = activityObj.activity
      //优惠方式 1：满减； 2：满折； 3：立减；4：立折  5：运费；6：代金券
      couponList.forEach(item=>{
        // 满减 | 代金券
        if (['1','3','6'].includes(item.couponWay)) {
          item.couponValue = Number((item.couponValue / 100).toFixed(2)) || 0;
          item.unit = '元';
        }
        // 满折
        else if (['2','4'].includes(item.couponWay)) {
          item.couponValue = Number((item.couponValue / 10).toFixed(1)) || 0;
          item.unit = '折';
        }
      })
      const coupon = couponList[0] || {}
      let showCoupon = true
      const { couponCategory = '',couponUseChannel = '',channelSeparation = '' , couponWay = '',applicableBizType = [] } = coupon
      const couponSenceType =  this.getCouponSenceType({
        couponCategory,
        applicableBizType
      })
      // 次日达、线下用的券、app专用的券、运费券不展示
      if(couponSenceType !== 'T' || couponUseChannel === '2' || !channelSeparation.includes('X') || couponWay === '5'){
        showCoupon = false
      }
      return {
        ...activityObj.activity,
        showCoupon,
        coupon:{
          ...coupon,
          useChannelDesc:couponSenceType === 'T'
            ? inTimeCouponDesc[coupon.couponUseChannel]
            : '线上次日达'
        },
      }
    }
  },
});
