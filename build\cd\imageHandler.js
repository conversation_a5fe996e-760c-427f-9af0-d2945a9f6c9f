const jimp = require('jimp')
const {createHash} = require('crypto')
const fs = require('fs/promises')
/**
 * @desc 微信预览是直接生成本地图片，所以还需要读取图片
 * @param {object} payload
 * @param {string} payload.previewImagePath
 * @param {string} payload.env
 * @param {string} payload.platform
 * @returns {{hash: string, base64: string} | void 0}
 */
async function handlePreviewImage({previewImagePath, env, platform}) {
  try {
    const imageData = await fs.readFile(previewImagePath)

    const addTextImgData = await addTextOnImage({ image: imageData, text: `${platform} ${env}`, left: 60, top: 0 })
    console.log('添加文字水印完成')
    // try {
    //   fs.writeFile(previewImagePath, addTextImgData)
    // } catch (error) {
    //   console.log('writeFile error: ', error);
    // }
    // 加前缀报错
    const base64 = `${addTextImgData.toString('base64')}`
    // @ts-ignore
    const hash = createHash('md5').update(addTextImgData, 'utf-8').digest('hex')
    return {
      hash, base64
    }

  } catch (error) {
    console.log('handlePreviewImage error: ', error)
    return error
  }
}

/**
 *
 * @param {object} payload
 * @param {Buffer} payload.image
 * @param {string} payload.text
 * @param {number} payload.top
 * @param {number} payload.top
 * @returns
 */
async function addTextOnImage(payload) {
  const { image, text, left, top } = payload
  const imageData = await jimp.read(image)
  const font = await jimp.loadFont(jimp.FONT_SANS_16_BLACK)
  imageData.resize(256, 256).print(font, left, top, text)
  return imageData.getBufferAsync(jimp.MIME_PNG)
}

module.exports = {
  handlePreviewImage,
  addTextOnImage,
}
