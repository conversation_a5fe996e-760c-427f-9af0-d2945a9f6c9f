import { observable, action } from 'mobx-miniprogram'

export const protocolPopupStore = observable({
  show: false,
  toggleShow: action(function (bool) {
    this.show = bool
  }),
})
class ProtocolPopup {
  constructor() {
    this.promise = null
    this.resolve = null
  }
  show(){
    protocolPopupStore.toggleShow(true)
    this.promise = new Promise(resolve => {
      this.resolve = resolve
    })
  }
  hide(bool) {
    protocolPopupStore.toggleShow(false)
    this.resolve && this.resolve(bool)
  }
  waitProtocolPopup() {
    return this.promise
  }
}

export const protocolPopup = new ProtocolPopup()