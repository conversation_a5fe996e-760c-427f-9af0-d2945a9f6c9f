import { getSendDetail, giftCardSendOperate, receiveGiftCard } from '../../api/index'
import util from '../../../utils/util'
import { getPromiseObj } from '../../../utils/promise'
import { giftCardReceiveTemplateIds, reportTmplIds } from '../../../mixins/ticketEntry'
import { onShareAppMessage } from '../../utils/send'
import { StatusType, OperateType } from '../../../sub-common/types/giftCard/type'
import sensors from '../../../utils/report/sensors'
import { allTrue, conditionExpression } from '../../../utils/cyclomatic'

const OperateTypeNum = /** @type { const } */ ({
  [OperateType.未知]: OperateType.未知,
  [OperateType.赠送]: OperateType.赠送,
  [OperateType.主动撤销赠送]: OperateType.主动撤销赠送,
  [OperateType.超时撤销赠送]: OperateType.超时撤销赠送,
  [OperateType.领取]: OperateType.领取,
})

const pageTitle = /** @type { const } */ '礼品卡'

function paddingZero(num) {
  return num < 10 ? `0${num}` : num
}


/**
 * @desc 检查赠送结果信息
 */
function checkSendDetail(error, cardInfo) {
  return (error && error.errorCode) || !cardInfo.number
}

/**
 * @desc 已赠送埋点
 */
function receivedReport(isSelfSend, sendInfo) {
  isSelfSend && sendInfo.status === OperateType.领取 &&
  sensors.exposureReport({
    element_content: '礼品卡已赠送',
    element_name: '礼品卡已赠送',
    element_code: '1162100001',
  })
}

/**
 * @desc 赠送中埋点
 */
function receivingReport(isSelfSend, sendInfo) {
  isSelfSend && sendInfo.status === OperateType.赠送 &&
  sensors.exposureReport({
    element_content: '礼品卡赠送中',
    element_name: '礼品卡赠送中',
    element_code: '1162100002',
  })
}

const app = util._getApp()

Component({
  data: {
    OperateTypeNum,
    loading: {
      page: true,
      cancelSend: false,
      refresh: false,
      receiving: false,
    },
    navBar: {
      background: 'transparent',
      title: '',
    },
    // giftCardCoverHiding: false,
    hideGiftCardCover: false,
    showCancelPopup: false,
    customerID: '',
    // 接收礼品卡参数
    /** 赠送礼品卡时生成的giveCode */
    giveCode: '',
    /** 赠送礼品卡时生成的activityId */
    activityId: '',
    // 查询赠送详情参数
    /** 从赠送中列表跳转过来时携带的cardNumber */
    cardNumber: '',
    placeholder: {
      top: 0,
      bottom: 0,
    },
    cardInfo: {
      number: '',
      cover: '',
      name: '',
      amount: 0,
      getCode: '',
      saleCode: '',
    },
    senderInfo: {
      headImg: '',
      phone: '',
      memberId: '',
    },
    sendInfo: {
      isSelfReceive: false,
      isSelfSend: false,
      wish: '',
      status: OperateType.未知,
      expireTime: '',
      giveCode: '',
      activityId: '',
    },
    expireTimeCountDown: '',
    subscribe: {
      show: false,
      tmplIds: giftCardReceiveTemplateIds,
    },
  },
  observers: {
    'loading.page': function (pageLoading) {
      if (pageLoading) {
        return
      }
      wx.hideLoading()
      this.startNavOb()
    },
    'sendInfo.status': function () {
      this.setBottomPlaceholderHeight()
    }
  },
  methods: {
    startNavOb() {
      this._data.readyPromise.promise.then(() => {
        const scrollNavBarOb = wx.createIntersectionObserver()
        this._data.scrollNavBarOb = scrollNavBarOb
        scrollNavBarOb.relativeTo('.gift-card-send-scroll', {
          top: -1 * (this.data.placeholder.top - 1),
        }).observe('.gift-card-send-scroll-placeholder', ({ intersectionRatio }) => {
          this.setData({
            'navBar.background': intersectionRatio ? 'transparent' : '#FFF'
          })
        })
      })
    },
    getNavBarHeight({ detail }) {
      this.setData({
        'placeholder.top': detail,
      })
    },
    setBottomPlaceholderHeight() {
      this._data.readyPromise.promise.then(() => {
        const query = wx.createSelectorQuery()
        query.select('.gift-card-bottom').fields({
          size: true,
        }).exec((res) => {
          const { height = 100 } = Array.isArray(res) ? res[0] : res
          this.setData({
            'placeholder.bottom': height
          })
        })
      })
    },
    // 领取动效
    // onHideGiftCardCover() {
    //   this.setData({
    //     giftCardCoverHiding: true,
    //     hideGiftCardCover: false,
    //   })
    // },
    onHideGiftCardCover() {
      if (!app.checkSignInsStatus()) {
        app.toSignIn()
        return
      }
      this.setData({
        // giftCardCoverHiding: false,
        hideGiftCardCover: true,
        'navBar.title': pageTitle,
      }, () => {
        this.startNavOb()
        this.setBottomPlaceholderHeight()
      })
    },
    toHomePage() {
      wx.switchTab({
        url: '/pages/homeDelivery/index',
      })
    },
    startCountDonwTimer(expireTime = 0) {
      if (expireTime <= 0) {
        this.setData({
          // 倒计时结束,隐藏确认弹窗
          showCancelPopup: false,
          'loading.refresh': true,
          expireTimeCountDown: '00:00:00',
        })
        this.getSendDetail()
        app.event.emit('refreshGiftCardList')
        return
      }
      const hours = Math.floor(expireTime / 3600)
      const minutes = Math.floor((expireTime - hours * 3600) / 60)
      const seconds = expireTime - hours * 3600 - minutes * 60
      const now = Date.now()
      this.setData({
        expireTimeCountDown: `${paddingZero(hours)}:${paddingZero(minutes)}:${paddingZero(seconds)}`,
      }, () => {
        this._data.expireTimeCountDownTimer = setTimeout(
          this.startCountDonwTimer.bind(this),
          Math.max(0, (now - Date.now()) + 1000),
          --expireTime
        )
      })
    },
    clearCountDownTimer() {
      clearTimeout(this._data.expireTimeCountDownTimer)
      this.setData({
        expireTimeCountDown: '00:00:00',
      })
    },
    onCancelPopupTap({ currentTarget: { dataset: { type } }}) {
      this.setData({
        showCancelPopup: false,
      })
      if (type === 'confirm') {
        this.cancelSend()
        sensors.clickReport({
          element_content: '取消赠送弹窗确认按钮',
          element_name: '取消赠送弹窗确认按钮',
          element_code: '1162100006',
        })
      } else {
        sensors.clickReport({
          element_content: '取消赠送弹窗关闭按钮',
          element_name: '取消赠送弹窗关闭按钮',
          element_code: '1162100005',
        })
      }
    },
    showCancelSend() {
      if (this.data.loading.cancelSend || this.data.loading.refresh) {
        return
      }
      sensors.clickReport({
        element_content: '取消赠送按钮',
        element_name: '取消赠送按钮',
        element_code: '1162100005',
      })
      this.setData({
        showCancelPopup: true,
      })
    },
    async cancelSend() {
      if (this.data.loading.cancelSend || this.data.loading.refresh) {
        return
      }
      this.setData({
        'loading.cancelSend': true,
      })
      this.clearCountDownTimer()
      const { customerID, cardInfo, sendInfo } = this.data
      const { data = {}, error } = await giftCardSendOperate({
        customerID: customerID,
        cardNumber: cardInfo.number,
        operateType: OperateType.主动撤销赠送,
      }).then(({ data }) => ({ data })).catch(error => ({ error }))
      sensors.exposureReport(error ? {
        element_content: '取消失败提示',
        element_name: '取消失败提示',
        element_code: '1162100006',
      } : {
        element_content: '取消成功提示',
        element_name: '取消成功提示',
        element_code: '1162100007',
      })
      wx.showToast({
        title: error ? error.description : (data.message || '取消成功'),
        icon: 'none'
      })
      this.setData(Object.assign({
        'loading.cancelSend': false,
      }, error ? {} : {
        sendInfo: Object.assign({}, sendInfo, data.sendInfo || {})
      }))
      app.event.emit('refreshGiftCardList')
    },
    showSubscribe() {
      sensors.clickReport({
        element_content: '分享好友领取',
        element_name: '分享好友领取',
        element_code: '1162100008',
      })
      const subscribePromise = getPromiseObj()
      this._data.subscribePromise = subscribePromise
      this.setData({
        'subscribe.show': true,
      }, () => subscribePromise.resolve())
    },
    onSubscribeClose({ detail = {} }) {
      detail.resultStatus && reportTmplIds(detail.resultStatus)
    },
    async receiveCard() {
      if (this.data.loading.receiving || this.data.loading.refresh) {
        return
      }
      this.setData({
        'loading.receiving': true
      }, () => this.setBottomPlaceholderHeight())
      sensors.clickReport({
        element_content: '领取赠送',
        element_name: '领取赠送',
        element_code: '1162100009',
      })
      const { data, error } = await receiveGiftCard({
        customerID: this.data.customerID,
        activityId: this.data.activityId,
        giveCode: this.data.giveCode,
      }).catch(error => ({ error }))
      if (error) {
        this.setData({
          'loading.receiving': false
        }, () => this.setBottomPlaceholderHeight())
        app.showModalPromise({ content: error.description || '领取失败,请稍后再试' })
        return
      }
      const { success, tips, sendInfo } = data
      ;(success || (sendInfo || {}).status === OperateType.领取) && sensors.exposureReport({
        element_content: '礼品卡已领取',
        element_name: '礼品卡已领取',
        element_code: '1162100010',
      })
      success || (tips && wx.showToast({ title: tips, icon: 'none' }))
      const newSendInfo = Object.assign(
        this.data.sendInfo,
        success
          ? {
            status: OperateType.领取,
            isSelfReceive: true,
            expireTime: 0,
          }
          : sendInfo
      )
      this.clearCountDownTimer()
      this.setData({
        'loading.receiving': false,
        sendInfo: newSendInfo,
      })
    },
    toGiftCardList() {
      [StatusType.NOTUSED, StatusType.SENGDING].includes(this._data.from)
        ? wx.navigateBack()
        : wx.navigateTo({
          url: '/giftCard/pages/home/<USER>',
        })
    },
    toGiftCardDetail() {
      sensors.clickReport({
        element_content: '查看我的礼品卡',
        element_name: '查看我的礼品卡',
        element_code: '1162100011',
      })
      const { sendInfo, cardInfo } = this.data
      this._data.from === 'detail'
        ? wx.navigateBack()
        : wx.navigateTo({
          url: `/giftCard/pages/detail/index?${
            sendInfo.isSelfSend
              ? (
                cardInfo.saleCode
                  ? `saleCode=${cardInfo.saleCode}`
                  : `getCode=${cardInfo.getCode}`
              )
              : `cardNumbers=["${cardInfo.number}"]`
          }`,
        })
    },
    startCountDonwTimerAfterDetail(isSending, sendInfo) {
      isSending && sendInfo.expireTime && this.startCountDonwTimer(sendInfo.expireTime)
    },
    async getSendDetail() {
      this.clearCountDownTimer()
      const { data, error } = await getSendDetail({
        giveCode: this.data.giveCode,
        cardNumber: this.data.cardNumber,
        customerID: this.data.customerID,
      }).catch(error => ({ error }))
      this.setData({
        'loading.refresh': false,
        'loading.page': false,
      })
      const { cardInfo = {}, senderInfo, sendInfo: sendInfoResult } = data || {}
      if (checkSendDetail(error, cardInfo)) {
        app.showModalPromise({
          content: error ? error.description : '网络错误,请稍后再试'
        })
        return
      }
      const sendInfo = sendInfoResult || {}
      const isSending = sendInfo.status === OperateType.赠送
      const isSelfSend = Boolean(sendInfo.isSelfSend)
      const showGiftCardCover = allTrue(isSending, !isSelfSend, !this._data.hasShowCover)
      // 未登录不更新hasShowCover
      this._data.hasShowCover = Boolean(this.data.customerID && (this._data.hasShowCover || showGiftCardCover))
      this.setData(Object.assign(
        {
          hideGiftCardCover: !showGiftCardCover,
          'navBar.title': showGiftCardCover ? '' : pageTitle,
          cardInfo,
        },
        conditionExpression(senderInfo, { senderInfo }, {}),
        conditionExpression(sendInfoResult, { sendInfo }, {}),
      ))
      this.startCountDonwTimerAfterDetail(isSending, sendInfo)
      receivedReport(isSelfSend, sendInfo)
      receivingReport(isSelfSend, sendInfo)
    },
    getScreenViewProps() {
      const { customerID, sendInfo, senderInfo, cardInfo } = this.data
      return {
        // 送卡人/领卡人/访问人
        gift_card_identity: customerID && sendInfo.isSelfSend
          ? '送卡人'
          : customerID && sendInfo.status === OperateType.领取 ? '领卡人' : '访问人',
        gift_card_givefrom: senderInfo.memberId,
        gift_card_shelf: cardInfo.name,
      }
    },
    onShareImageLoaded({ detail }) {
      this._data.shareImagePromise.resolve(detail.tempFilePath)
    },
    onShareAppMessage() {
      this._data.lockOnShow = true
      return onShareAppMessage(this.data.sendInfo, this._data.subscribePromise.promise.then(() => this._data.shareImagePromise.promise))
    },
    onLoad(options) {
      wx.updateShareMenu({
        isPrivateMessage: true,
      })
      wx.hideShareMenu()
      this.setData({
        giveCode: options.giveCode,
        activityId: options.activityId,
        cardNumber: options.cardNumber,
      })
      this._data.from = options.from
    },
    onShow() {
      if (this._data.lockOnShow) {
        this._data.lockOnShow = false
        return
      }
      const { userID = '' } = wx.getStorageSync('user') || {}
      this.setData({
        customerID: userID,
      })
      this.getSendDetail().then(() => {
        sensors.pageScreenView(this.getScreenViewProps())
      })
    },
    onReady() {
      this._data.readyPromise.resolve()
    },
    onUnload() {
      [
        this._data.scrollNavBarOb,
      ].forEach(ob => ob && ob.disconnect())
      this.clearCountDownTimer()
    },
  },
  lifetimes: {
    created() {
      this._data = {
        hasShowCover: false,
        subscribePromise: getPromiseObj(),
        lockOnShow: false,
        readyPromise: getPromiseObj(),
        shareImagePromise: getPromiseObj(),
        scrollNavBarOb: null,
        expireTimeCountDownTimer: '',
        from: '',
      }
    },
    attached() {
      wx.showLoading()
    },
  }
})