// userB/pages/updateNickName/index.ts
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    nickName: '',
    saveBtnFlag: false,
  },
  _data: {
    inputValue: ''
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(o) {
    console.log(o)
    this.setData({
      nickName: o.nickName || '',
      saveBtnFlag: Boolean(o.nickName)
    })
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  handle(e) {
    console.log('handle', e)
    const { pass } = e.detail
    if (pass) {
      this.setData({
        saveBtnFlag: Boolean(this._data.inputValue),
        nickName: this._data.inputValue
      })
      this.nicknamereviewResolve && this.nicknamereviewResolve()
    } else {
      this.setData({
        saveBtnFlag: false,
        nickName: ''
      })
    }
  },
  async submit(e) {
    console.log('submit',e)
    if (!this.data.saveBtnFlag) {
      return
    }
    if (this.nicknamereviewPromise) {
      await this.nicknamereviewPromise
    }

    const pages = getCurrentPages()
    const prePage = pages[pages.length - 2] || {}
    prePage.updateValue(this.data.nickName)
    wx.navigateBack()
  },
  inputHandle(e) {
    console.log('inputHandle', e)
    const value = e.detail.value
    this._data.inputValue = value
    this.setData({
      saveBtnFlag: Boolean(value),
    })
    let resolve
    if (this.nicknamereviewPromise) {
      return
    }
    this.nicknamereviewPromise = new Promise(_resolve => {
      resolve = _resolve
    })

    this.nicknamereviewResolve = resolve
  }
})