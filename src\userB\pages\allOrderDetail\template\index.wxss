/* 商品模块 start */
.lf-img{
  width:160rpx;
  height:160rpx;
}
.goods-detail{
  margin-left:12rpx;
  flex:1;
  /* display:flex;
  flex-direction: column;
  align-content: center;
  justify-content: space-between; */
  overflow: hidden;
}
.txt-top {
  font-size: 26rpx;
  color: #222;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6rpx;
}
.goods-desc{
  display: flex;
  align-items: center;
}
.lineclamp2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.goods-level {
  display: inline-block;
  font-size: 22rpx;
  color: #fff;
  background: linear-gradient(to bottom right, #65D667, #34B764);
  border-radius: 4rpx;
  font-weight: 500;
  margin-right: 8rpx;
  padding: 0 11rpx;
  height: 28rpx;
  text-align: center;
  line-height: 28rpx;
  box-sizing: border-box;
}
.price{
  font-size: 30rpx;
  font-weight: 500;
  color: #222;
  margin-left: 20rpx;
}
.unit-price, .count, .single-num-price, .goods-service{
  font-size:24rpx;
  color:rgba(136, 136, 136, 1);
  margin-bottom: 9rpx;
}
.small-margin{
  margin-left: 20rpx;
}
.unit-price {
  margin: 2rpx 0 8rpx 0;
}
.nocoupon {
  flex: 1;
  margin-top: 8rpx;
}
.nocoupon> .txt{
  border: 1rpx solid #01BA50;
  border-radius: 3px;
  width: 88rpx;
  height: 32rpx;
  line-height: 32rpx;
  font-size: 18rpx;
  color: #269D59;
  text-align: center;
}
.subtotal{
  display:flex;
  justify-content: flex-start;
  align-items: center;
}
/* 商品模块 end */

/* 商品标签 start */
.goods-property {
  display: flex;
  height: 32rpx;
  width: 110rpx;
  overflow: hidden;
}
.label-box{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}
.goods-label {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 40rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  transform: scale(0.5);
  transform-origin: 0 0;
}

.label-text {
  font-size: 22rpx;
  color: #269D59;
  margin-left: 8rpx;
}
.goods-delivery {
  /* margin-left: 8rpx; */
  font-size: 22rpx;
  color: #3CAD65;
}
/* 商品标签 end */

/* 物流信息 start */
.delivery-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}
.package-containor{
  display: flex;
  height: 100%;
}
.package-box {
  flex: 1;
  height: 100%;
}
.scroll-box {
  width: 100%;
  height: 100%;
}
.delivery-package-content{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}
.delivery-package {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #222222;
  overflow: hidden;
}
.delivery-package-label {
  width: 76rpx;
  /* margin-right: 12rpx; */
  margin: 4rpx 0 14rpx;
  padding: 0 10rpx;
  font-size: 20rpx;
  line-height: 28rpx;
  color: #008C3D;
  border: 1rpx solid #008C3D;
  border-radius: 4rpx;
}
.delivery-company {
  margin-right: 12rpx;
}
.delivery-company,
.delivery-number{
  font-weight: bold;
}
.delivery-number{
  /* max-width: 200rpx; */
  overflow: hidden;
  text-overflow: ellipsis;
}
.delivery-package-label,
.delivery-company,
.delivery-number-copy{
  flex-shrink: 0;
}
.delivery-number-copy{
  width: 72rpx;
  /* height: 28rpx; */
  border-radius: 14rpx;
  border: 1rpx solid rgba(85, 85, 85, .5);
  font-size: 20rpx;
  color: #555555;
  line-height: 26rpx;
  text-align: center;
  margin-left: 8rpx;
}
.delivery-desc {
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #888888;
  /* font-weight: lighter; */
}
.two-line{
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.delivery-time {
  font-size: 24rpx;
  color: #888888;
  /* font-weight: lighter; */
}
.icon-arrow {
  width: 24rpx;
  height: 24rpx;
  flex-shrink: 0;
  /* margin-top: 4rpx; */
  /* padding: 0 25rpx 0 12rpx; */
}

/* swiper指示点 */
.dots-box {
  height: auto;
  display: flex;
  justify-content: center;
}
.dots-box .dot {
  margin-left: 8rpx;
  width: 24rpx;
  height: 4rpx;
  background: #DDDDDD;
}
.dots-box .dot:first-child {
  margin-left: 0;
}
.dots-box  .dot.active {
  background: #00CC6D;
}
/* 物流信息 end */
