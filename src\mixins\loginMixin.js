
const commonObj = require('../source/js/common').commonObj
const sensors = require('../utils/report/sensors')
import { bgxxStore } from '../stores/index'
import { 
  checkUserIsStoreLeader,
  checkUserIsDistributor,
  clientLoginRequest,
  updateUserAuth,
  getMemberInfo,
  getMemberTypeInfo,
  receiveFirstGradingEquity, 
  getTuringDeviceToken 
} from '../service/userService'
import {
  getEncryptKey
} from '../service/encrypt';
import fruitCartStore from '../stores/module/fruitCart';
const routeObj = {
  homeDelivery: '/pages/homeDelivery/index',
  feedbackEntry: '/store/pages/feedbackEntry/index',
  storeOrderList: '/store/pages/storeOrderList/index',
  selfContentRecommend: '/userB/pages/selfContentRecommend/index',
  newUserGoods: '/homeDelivery/pages/newUserGoods/index'
}
const util = require('../utils/util')
module.exports = {
  data: {
    showUserErrorModal: false
  },
  navigateBackDuiba() {
    const detailObj = {
      signIn: 1,
      phoneSignIn: 2
    }
    let delta = 0
    if (this.data.pageName === 'infoCertification') {
      delta = this.data.pageFrom === 'signIn' ? 2 : 3
    } else {
      delta = detailObj[this.data.pageName] || 1
    }

    const pages = getCurrentPages();
    var prevPage = pages[pages.length - delta - 1];  // 上一个页面
    prevPage && prevPage.setData({
      isLoginBack: true
    });
    wx.navigateBack({
      delta
    })
  },
  async receiveNewCustomerCoupon(customerID = -1) {
    try {
      const app = getApp()
      app.api.getEshopNewCustomerGift({ customerID })
      app.api.getNewUserCoupons({ customerID})
    } catch(err){}
  },
  startReportPointAfterLogin(app) {
    if (app.globalData.needInitSensors) {
      // 如果未开启过上报埋点,就调用startReportPoint
      if (app.globalData.isStartReportPoint) {
        app.initSensors()
      } else {
        app.startReportPoint()
      }
    }
  },
  async loginSuccessHandle(data) {
    console.log('loginSuccessHandle', data)
    const app = getApp()
    const {
      isSuperVip: superVipStatus,
      userToken,
      userID,
      isNavigateBack = true // 登录完成是否返回
    } = data
    data.superVipStatus = superVipStatus
    app.globalData.userToken = userToken
    app.globalData.customerID = userID
    app.globalData.isShowSignoutModal = true
    app.globalData.superVipStatus = superVipStatus
    const decryptKey = await getEncryptKey('pwd')
    const token = commonObj.Decrypt(userToken, decryptKey); //	103306748|15be1dcd75c|0,中间的作为有的请求的密钥
    wx.setStorageSync('user', data)
    wx.setStorageSync('token', token.split('|')[1]);

    // 登录后清空之前的数据 防止偶现退出未清空的问题
    app.updateFreshGoodsCartList({})
    // 重新获取最近门店，并获取最近地址
    this.data.isRefreshLocation && app.locateStore()
    // 登录之后刷新首页信息
    app.event.emit('refreshPageGoods')
    app.mixBgxxCart()
    fruitCartStore.reset()
    app.event.emit('global.loginSuccess')
    // await Promise.all([app.syncCart(), app.getNewUserLabels(), getComplateMemberInfo(), this.updateUserInfo()])
    await Promise.all([app.syncCart(), app.getNewUserLabels(), getMemberInfo()])
    // 登录之后刷新红点信息
    // app.event.emit('refreshRedDot')
    // 次日达绑定用户邀请关系
    bgxxStore.bindUserRelation()
    //查询当前用户是否为团长
    checkUserIsStoreLeader()

    // 查询当前用户是否为及时达分佣团长
    checkUserIsDistributor(true)

    // 获取离线会员码key值
    app.getMemberCodeKey()
    // 存储会员的微信头像及昵称（供app使用）
    // 会员中台暂时不上这个接口，先注释啦

    // 登录完领券
    this.receiveNewCustomerCoupon(userID)
    // 领取首次定级权益（触发初始化领券）
    receiveFirstGradingEquity(userID)
    this.startReportPointAfterLogin(app)
    if (app.globalData.reportSensors) {
      app.sensors.setOnceProfile({
        registerTime: new Date()
      })
    }
    wx.removeStorageSync('isManuallyLogout')
    getMemberTypeInfo()
    // 登录成功后同盾blackBox
    app.getBlackbox()
    // 登录成功获取腾讯图灵设备token
    getTuringDeviceToken()
    
    app.sensors.login(userID);
    // 无须返回
    if (!isNavigateBack) {
      return
    }
    const { finishToRouter } = this._data
    if (finishToRouter) {
      if (['homeDelivery'].indexOf(finishToRouter) !== -1) {
        wx.switchTab({
          url: routeObj[finishToRouter],
        })
      }
      else if (finishToRouter.indexOf('duiba') > -1) { // 跳转h5
        this.navigateBackDuiba()
      }
      else if (finishToRouter.indexOf('http') > -1) { // 跳转h5
        wx.redirectTo({
          url: `/h5/pages/commonh5/index?webUrl=${finishToRouter}`,
        })
      }
      else {
        wx.redirectTo({
          url: routeObj[finishToRouter],
        })
      }
    } else {
      const detailObj = {
        signIn: 1,
        phoneSignIn: 2
      }
      let delta = 0
      if (this.data.pageName === 'infoCertification') {
        delta = this.data.pageFrom === 'signIn' ? 2 : 3
      } else {
        delta = detailObj[this.data.pageName] || 1
      }
      wx.navigateBack({
        delta
      })
    }
  },
  async toLogin (options) {
    const app = getApp()
    const userNameAndImg = wx.getStorageSync('userNameAndImg') || {}
    const sexual = userNameAndImg.sexual === 1 ? '男' : '女'
    const {cityID = -1, cityName = '', lat = '', lon = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
    const wxSnsInfo = wx.getStorageSync('wxSnsInfo') || {}
    // try{
    //   // 神策防黑产埋点
    //   sensors.safeGuardSensor('login', { customerId: this.data.phoneNumber })
    // }catch(err){
    //   console.log(err)
    // }
    const params = {
      encryptFlag: true,
      encryptType: 'pwd',
      data: {
        phoneNumber: this.data.phoneNumber,
        firstVisitStoreId: storeInfo.storeCode || '',
        gender: sexual,
        openID: wxSnsInfo.openid,
        unionID: wxSnsInfo.unionid,
        anonymousID: app.globalData.anonymousID,
        cityID,
        cityName,
        ...options,
        isWorkWx: util.checkIsQy() ? 'Y' : 'N'
      },
      mask: true,
      header: {
        gpslocation: JSON.stringify({
          lat,
          lon
        })
      }
    }
    try {
      const res = await app.api.userLogin(params);
      const decryptKey = await getEncryptKey('pwd')
      const decryptdata = JSON.parse(commonObj.Decrypt(res.data, decryptKey));
      console.log(decryptdata);
      const { isIdentity } = decryptdata;
      app.globalData.anonymousID = '';
      app.event.emit('refreshNewUserInfo')
      app.event.emit('refreshFreshNewUserInfo')
      // 登录成功
      app.sr.track('register_wxapp');
      app.sr.track('login_wxapp');
      //  用户已登录，设置同意登录协议
      updateUserAuth({
        isVisitor: false
      })

      if (isIdentity) {
        // 判断是否需要实名认证
        const { pageName, isRefreshLocation } = this.data;
        wx.navigateTo({
          url: `/homeDelivery/pages/signIn/infoCertification/index?loginData=${JSON.stringify(
            decryptdata
          )}&finishToRouter=${
            this._data.finishToRouter
          }&isRefreshLocation=${isRefreshLocation}&pageFrom=${pageName}`,
        });
        return;
      }
      // 增加用户是否认证的字段 certified 0：未实名  1: 已实名
      await this.loginSuccessHandle({ ...decryptdata, certified: decryptdata.isIdentified === 'N' ? 0 : 1 });
      // 最后再上报
      const { loginChannel = '' } = options
      sensors.track(
        'SignUp', {
        // 1：手机号快捷登录 2：短信验证码登录
        VerificationWay: loginChannel,
        // 是否注册
        isRegister: decryptdata.isFirstLogin === 'Y' ? '注册' : '登录'
      })
      app.loadShopCart() // 登录成功后加载购物车，用于展示对应商品的数量
    } catch (res) {
      console.log(res)
      if (res.errorCode === 55307 || res.errorCode === 55308 || res.errorCode === 55309 || res.errorCode === 31042) {
        this.showCallPhoneModal(res.description)
      } else if (res.errorCode === 55306 || res.errorCode === 55340) {
        this.setData({
          showUserErrorModal: true,
          isSuperVip: res.errorCode === 55340
        })
      } else if (res.errorCode === 30007) {
        // 验证码错误
        this.setData({
          showErrorInfo: true,
          errorInfo: '您输入的短信验证码错误'
        })
        this.setData({ submitDisabled: false })
      } else {
        console.error('登录失败...')
        this.handleError(res.description, res.errorCode)
      }
    }
  },
  showCallPhoneModal(description) {
    wx.showModal({
      title: '温馨提醒', //弹框标题
      content: description, //提示内容
      confirmText: '呼叫',
      cancelText: '取消',
      cancelColor: '#999999',
      confirmColor: '#008C3C',
      success: function (res) {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4001811212'
          })
        }
      }
    })
  },

  // 企微静默登录
  async qyClientLogin({
    authPhone
  } = {}) {
    try {
      const app = getApp()
      const loginResult = await clientLoginRequest()
      // 登录成功则有token返回
      if (!loginResult.token) {
        return
      }
      const {
        openId,
        customerId,
        token,
        unionId,
        superVipStatus,
        phoneNumber
      } = loginResult
      console.log('loginResult', loginResult)
      // 此处判断在企微环境下授权登录生效，若用户授权手机号与用户不一致，不静默登录
      if ( authPhone && authPhone !== phoneNumber) {
        return
      }
      if (openId && unionId) {
        wx.setStorageSync('wxSnsInfo', {
          openid: openId,
          unionid: unionId
        })
        app.globalData.wxOpenId = openId
      }
      const loginData = {
        isSuperVip: superVipStatus,
        userToken: token,
        userID: customerId,
        phoneNumber
      }
      this.loginSuccessHandle(loginData)
      return true
    } catch(err) {
      return
    }
  }
}
