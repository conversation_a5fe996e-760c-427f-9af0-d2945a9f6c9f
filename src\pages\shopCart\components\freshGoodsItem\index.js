import { goodsItem } from '../goodsItem.js'  // 引入封装的方法
import { ComponentWithStore } from 'mobx-miniprogram-bindings'
import { freshCartStore } from '../../../../stores/module/freshCart'
import setCountMixins from '../../../../mixins/bgxx/setCount'
import { throttle } from '../../../../utils/util';

ComponentWithStore({
	behaviors: [goodsItem({
    methods: {
      /** 获取埋点数据 */
      getReportData({ key }) {
        const goodsInfo = this.data.goodsInfo
        return {
          // 重新调整后的key(如,选中/非选中,购物车加号/减号)
          key: {
            checkSingle: goodsInfo.isSelected === 'Y' ? 'unCheckSingle' : key
          }[key] || key,
          // 需要上报的额外数据(如SKU_ID,SKU_Name)
          data: {
            SKU_ID: goodsInfo.goodsSn,
            SKU_Name: goodsInfo.name || goodsInfo.shortName
          }
        }
      },
      goDetail() {
        const { goodsID: id, goodsSn: goodssn } = this.data.goodsInfo
        setCountMixins.goDetail.call(
          {
            ...setCountMixins,
            // 这里面有个埋点需要用到pageSource
            // _data: {
            //   pageSource: 'cart'
            // }
          },
          {
            currentTarget: {
              dataset: { id, goodssn }
            }
          },
          {
            pageSource: 'cart'
          }
        )
      },
      checkSingle() {
        freshCartStore.checkSingle(this.data.goodsInfo)
      },
      changeCount({ currentTarget: { dataset: { type } } }) {
        if (this.changeCounting) { return }
        this.changeCounting = true
        freshCartStore.changeCount({
          currentTarget: {
            dataset: {
              complete: () => {
                setTimeout(() => {
                  this.changeCounting = false
                })
              },
              type,
              detail: this.data.goodsInfo,
              actpage: false,
              needTop: false
            }
          }
        })
      },
      setCount({ detail: { value } }) {
        const { resetCount } = freshCartStore.countChange({ value, goodsInfo: this.data.goodsInfo }) || {}
        resetCount && this.setData({
          count: this.data.count
        })
      },
      onDelHandle() {
        freshCartStore.delete([this.data.goodsInfo])
      },
    }
  })],
  storeBindings: [{
    store: freshCartStore,
    fields: ['isVip', 'goodsStorageTypeObj']
  }],
  properties: {
    matchExchange: {
      type: Boolean,
      value: false
    }
  },
  observers: {
    matchExchange() {
      this.updateNoMatch()
    },
    activityInfo() {
      this.updateNoMatch()
    },
    'goodsInfo.count': function(value) {
      this.updatePromotLimit(value)
    }
  },
  data: {
    showNoMatch: false,
    promotion: {
      overLimit: false,
      maxEffectNum: 0,
      normalCount: 0,
    }
  },
  methods: {
    updateNoMatch() {
      this.setData({
        showNoMatch: this.data.goodsInfo.activityInfo && !this.data.matchExchange
      })
    },
    updatePromotLimit() {
      const goodsInfo = this.data.goodsInfo
      const overLimit = false // Boolean(goodsInfo.specialInfo && !goodsInfo.activityInfo && goodsInfo.specialInfo.maxEffectNum && goodsInfo.count > goodsInfo.specialInfo.maxEffectNum)
      this.setData({
        promotion: {
          overLimit,
          maxEffectNum: overLimit ? goodsInfo.specialInfo.maxEffectNum : 0,
          normalCount: overLimit ? goodsInfo.count - goodsInfo.specialInfo.maxEffectNum : 0,
        }
      })
    },
  },
  lifetimes: {
    attached() {
      this.updatePromotLimit = throttle(this.updatePromotLimit, 100)
    },
  },
});
