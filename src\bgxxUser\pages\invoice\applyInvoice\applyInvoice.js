// user/pages/invoice/applyInvoice/applyInvoice.js
//获取应用实例
var app = getApp()
const common = require('../../../../source/js/common').commonObj
let sysInfo = wx.getSystemInfoSync()
let noticeList = [
  "熊猫大鲜目前仅支持开具数电发票，数电发票与纸质发票具有相同的法律效力，可以作为报销、售后、维权凭证，不易丢失，更方便环保。数电发票统一在国家税务总局深圳市电子税务局查验；",
  "订单交易完成后可在订单详情页内申请开票；",
  "历史订单补开发票仅支持开具最近180天内交易成功的订单发票；",
  "发票金额不含好吃卡/礼品卡、主钱包、优惠券、满折活动、微信代金券等优惠扣减金额；",
  "主钱包支付订单暂不支持在小程序开具电子发票，可前往百果园App进行充值订单开票；",
  "开具发票后订单全额退款，系统会自动撤销当前已开具的发票，且不可重新开具发票；",
  "申请开具数电发票后一般会在24小时内会开票成功，可在发票详情页预览，若已授权微信卡包，也可以在微信卡包查看发票信息；",
  "当遇到系统升级或报税等情况时，可能会出现频繁开票失败或长期处于开票中的情况，如急需发票，请联系客服登记处理；"
]
Page({

  /**
   * 页面的初始数据
   */
  data: {
    invoiceType:'E', //N不需要，E电子发票
    invoiceHeader:'C', //P个人，C企业
    showPop:false, // 是否显示popup
    noticeList // 须知列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options)
    let isIphoneX = sysInfo.model.indexOf("iPhone X") > -1 ? true : false
    this.setData({ options: options, isIphoneX: isIphoneX });
    let that = this;
    that.getHistoryInvoice().then(function(res){
      let emplyeeInvoice = that.data.emplyeeInvoice;
      let personInvoice = that.data.personInvoice;
      let invoiceAmount = +options.invoiceAmount || 0
      if (options && (options.fromPage === 'order' || options.fromPage === 'orderDetail')) {
        // that.setData({
        //   showBtnFlag: false,
        //   invoiceType: 'E',
        //   orderID: options.orderID,
        //   invoiceAmount,
        //   userName:  options.title ||'',
        //   pEmail:  options.email||'',
        //   companyName:  options.title ||'',
        //   taxcode: options.taxcode ||'',
        //   cEmail:  options.email ||''
        // })
        if(options.type === 'C'){ // 集团
          that.setData({
            showBtnFlag: false,
            invoiceType: 'E',
            orderID: options.orderID,
            invoiceAmount: options.invoiceAmount || 0,
            companyName:  options.title || (emplyeeInvoice ? emplyeeInvoice.title : ''),
            taxcode:options.taxcode || (emplyeeInvoice ? emplyeeInvoice.taxcode : ''),
            cEmail:options.email|| (emplyeeInvoice ? emplyeeInvoice.email : ''),
            pEmail:"",
            invoiceHeader:"C"
          })
        }else{ // 个人
          that.setData({
            showBtnFlag: false,
            invoiceType: 'E',
            orderID: options.orderID,
            invoiceAmount: options.invoiceAmount,
            userName:  options.title || (personInvoice ? personInvoice.title : ''),
            pEmail: options.email || (personInvoice ? personInvoice.email : ''),
            cEmail:'',
            taxcode:  '',
            invoiceHeader: options.type || 'C',
          })
        }
      }
      //  else if (options && options.fromPage === 'confirm') {
      //   if (options.invoiceInfo) {
      //     var invoiceInfo = JSON.parse(options.invoiceInfo);
      //     that.setData({
      //       showBtnFlag: true,
      //       invoiceType: 'E',
      //       invoiceAmount,
      //       invoiceHeader: invoiceInfo.type,
      //       pEmail: invoiceInfo.type === 'P' ? invoiceInfo.email : (personInvoice && personInvoice.email || ''),
      //       userName: invoiceInfo.type === 'P' ? invoiceInfo.title : (personInvoice && personInvoice.title || ''),
      //       cEmail: invoiceInfo.type === 'C' ? invoiceInfo.email : (emplyeeInvoice && emplyeeInvoice.email || ''),
      //       companyName: invoiceInfo.type === 'C' ? invoiceInfo.title : (emplyeeInvoice && emplyeeInvoice.title || ''),
      //       taxcode: invoiceInfo.type === 'C' ? invoiceInfo.taxcode : (emplyeeInvoice && emplyeeInvoice.taxcode || ''),
      //     })
      //   } else {
      //     that.setData({
      //       showBtnFlag: true,
      //       invoiceType: 'E',
      //       invoiceAmount,
      //       invoiceHeader: 'C',
      //       userName: '',
      //       cEmail: '',
      //       pEmail: ''
      //     })
      //   }
      // }
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  },

  bindTypeTap (e){
    var type = e.currentTarget.dataset.type;
    var kind = String(e.currentTarget.dataset.kind);
    var that = this;
    if (kind === '1'){
      that.setData({
        invoiceType: type
      })
      that.lastInvoiceSet()
    } else if (kind === '2'){
      that.setData({
        invoiceHeader: type
      })
    }
  },
  // 监听输入框输入变化
  bindInputHandler (e){
    let that = this,
    curInput= e.currentTarget.id,
    curDetail = e.detail.value;
    if (curInput === 'userName'){
      that.setData({
        userName: curDetail
      })
    } else if (curInput === 'companyName'){
      that.setData({
        companyName: curDetail
      })
    } else if (curInput === 'taxcode'){
      that.setData({
        taxcode: curDetail
      })
    } else if (curInput === 'pEmail'){
      that.setData({
        pEmail: curDetail
      })
    } else if (curInput === 'cEmail'){
      that.setData({
        cEmail: curDetail
      })
    }
  },
  //纳税人识别号正则
  checkTax (tax){
    if (tax && /^[A-Z0-9]+$/.test(tax.toUpperCase())) {
      return true
    }
  },
  // 抬头名称正则
  checkName (name) {
    if ( name === '') {
      return false
    }
    if (/^([\u4e00-\u9fa5]|[（）()]){1,30}$/.test(name)) {
      return true
    }
  },
  // 邮箱正则验证
  checkEmail (email){
    if (email && /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(email)){
      return true
    }
  },
  // 确认按钮
  confirm(){
    let that = this, invoiceInfo,
      options = that.data.options;
    if (that.data.invoiceType !== 'N'){
      if (that.regTaxInfo()) {
        if (options && (options.fromPage === 'order' || options.fromPage === 'orderDetail')) {
          console.log('?????')
          app.requestSubscribeMessage({tmplIds:['t-Z7b2h-jodZ0yChAXEEO_h2LJCiABBxnOvtGFUAugQ']},'', that.createInvoice)
          // that.createInvoice()
        } else if (options && options.fromPage === 'confirm') {
          invoiceInfo = {
            type: that.data.invoiceHeader,
            title: that.data.invoiceHeader === 'P' ? that.data.userName : that.data.companyName,
            taxcode: that.data.invoiceHeader === 'C' ? that.data.taxcode : '',
            email: that.data.invoiceHeader === 'P' ? that.data.pEmail : that.data.cEmail,
          }
          that.invokeLastPageMethod('setSelectInvoice', invoiceInfo)
          wx.navigateBack()
        }
      }
    } else {
      that.invokeLastPageMethod('setSelectInvoice', null)
      wx.navigateBack()
    }
  },
  // 调用上一页的方法
  invokeLastPageMethod(methodName, ...args) {
    let pages = getCurrentPages()
    let prevPage = pages[pages.length - 2]

    return prevPage[methodName](...args)
  },
  //字段输入信息验证
  regTaxInfo() {
    var that = this,
      regFlag = false;
    if (that.data.invoiceHeader === 'P' && (!that.checkName(that.data.userName))) {
      wx.showToast({
        title: '请填写正确的抬头名称',
        icon: 'none',
        duration: 2000
      })
    } else if (that.data.invoiceHeader === 'C' && (!that.checkName(that.data.companyName))) {
      wx.showToast({
        title: '请填写正确的抬头名称',
        icon: 'none',
        duration: 2000
      })
    }else if(that.data.invoiceHeader === 'P' && that.data.userName.length<2){
      wx.showToast({
        title:'发票姓名请填写至少两位',
        icon: 'none',
        duration: 2000
      })
  }else if(that.data.invoiceHeader === 'C' && that.data.companyName.length<2){
    wx.showToast({
      title:'企业名称请填写至少两位',
      icon: 'none',
      duration: 2000
    })
  } else if (that.data.invoiceHeader === 'C' && !that.checkTax(that.data.taxcode)){
      wx.showToast({
          title: '请填写正确的纳税人识别号',
          icon: 'none',
          duration: 2000
        })
    } else if (that.data.invoiceHeader === 'C' && !(that.data.taxcode.length === 15 || that.data.taxcode.length === 18 || that.data.taxcode.length === 20)){
      wx.showToast({
        title: '您输入的税号位数不正确',
        icon: 'none',
        duration: 2000
      })
    } else if ((that.data.invoiceHeader === 'P' && !that.checkEmail(that.data.pEmail)) ||
      (that.data.invoiceHeader === 'C' && !that.checkEmail(that.data.cEmail))) {
      wx.showToast({
        title: '请输入合法的邮箱地址',
        icon: 'none',
        duration: 2000
      })
    }else{
      regFlag = true
    }
    return regFlag
  },
  // 新增发票
  createInvoice (){
    let that = this,
      user = wx.getStorageSync('user')
    if (that.data.confirmLoading) return
    that.setData({ confirmLoading: true })
    let params = {
      customerId: user.userID,
      type: that.data.invoiceHeader,
      title: that.data.invoiceHeader === 'P' ? that.data.userName : that.data.companyName,
      taxcode: that.data.invoiceHeader === 'C'? that.data.taxcode : '',
      email: that.data.invoiceHeader === 'P' ? that.data.pEmail : that.data.cEmail,
      goodsOrderID: that.data.orderID
    }
    app.api.createBgxxInvoice(params).then(() => {
      that.setData({ confirmLoading: false })
      wx.showToast({
        title: '开票申请已提交',
        icon: 'none',
        duration: 2000,
        success: function () {
          if (that.data.options.fromPage === 'order'){
            setTimeout(function () {
              wx.redirectTo({
                url: '/bgxxUser/pages/orderDetail/index?orderID=' + that.data.orderID,
              })
            }, 2000)
          } else if (that.data.options.fromPage === 'orderDetail'){
            wx.navigateBack()
          }
        }
      })
    }, err => {
      common.showModal('提示', err.description, false);
      that.setData({ confirmLoading: false })
    })
  },
  getHistoryInvoice(){
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    var that = this;
    return new Promise(function(resolve, reject){
      app.api.getBgxxHistoryInvoice(app.globalData.customerID).then(res => {
        wx.hideLoading()
        if (!!res.data) {
          var data = res.data;
          that.setData({
            emplyeeInvoice: data.employeeInoice,
            personInvoice: data.personInvoice
          })
          resolve('success')
        }
      }, () => {
        wx.hideLoading()
        common.showModal('提示', '系统繁忙，请求超时', false, '我知道了')
        reject("fail")
      })
    })
  },
  lastInvoiceSet () {
    let that = this;
    let employeeInvoice = that.data.emplyeeInvoice;
    let personInvoice = that.data.personInvoice;
    if (that.data.invoiceType === 'E'){
      if (personInvoice || employeeInvoice) {
        that.setData({
          userName: personInvoice && personInvoice.title,
          pEmail: personInvoice && personInvoice.email,
          companyName: employeeInvoice && employeeInvoice.title,
          taxcode: employeeInvoice && employeeInvoice.taxcode,
          cEmail: employeeInvoice && employeeInvoice.email,
        })
      }
    }else{
      that.setData({
        invoiceHeader: 'C',
        userName: '',
        pEmail:'',
      })
    }
  },
   /**
    * 显示须知
    *
  */
 showNoticeHandle(){
  this.setData({
    showPop:true
  })
},
popConfirm(){
  this.setData({
    showPop:false
  })
},
  /**
   * 清空输入框内容
   */
   clearInputHandle(e){
    let {val} = e.currentTarget.dataset;
    this.setData({
      [val+'']:''
    })
  }
})
