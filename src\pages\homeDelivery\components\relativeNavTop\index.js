// pages/homeDelivery/components/navTop/index.js
import { tickEntryBehavior } from '../../../../mixins/ticketEntry'
const app = getApp()
const config = require('../../../../utils/config')
const act = require('../../../../utils/activity')
const sensors = require('../../../../utils/report/sensors')
const { wrapSensorReportFn, genClickSensor } = require('../../../../utils/report/index')
const {
	sensorReportMap,
	TAP_MEMBER_CODE_ICON,
	TAP_COUPON_ICON
} = require('../../sensorReportData')
const returnClickReportData = genClickSensor(sensorReportMap)

const customQuery = require('../../../../mixins/behaviors/customQuery')
const PIC_DOMAIN = config.baseUrl.PAGODA_PIC_DOMAIN

const MEMBER_CODE_SHOW_TIME = 5000 // 会员码展示5秒
const MAIN_CONTENT_HEIGHT = 426 // 大促背景下，主视觉区域高度
const SHOW_TIPS = 'defalutLocateTips'

import { storeBindingsBehavior } from 'mobx-miniprogram-bindings'
import locateStore from '../../../../stores/module/locate'
import { locateInfo } from './properties'
import { homeDeliveryStorageCache } from '~/utils/storageCache'

let closeShowTips = false

Component({
	/**
	 * 组件的属性列表
	 */
	behaviors: [customQuery, tickEntryBehavior, storeBindingsBehavior],
	properties: {
		locateInfo,
		searchText: {
			type: String,
			value: ''
		},
		couponNum: {
			type: Number,
			value: 0
		},
    // 大促背景对象
    activityBgObj: {
      type: Object,
			value: {}
    },
		// 是否展示了新品专区 如果展示了 则不能展示大促背景
		isShowNewGoodsArea: {
			type: Boolean,
			value: false
		}
	},
	observers: {
		'locateInfo': function (locateInfo) {
			if (!locateInfo) return
			const { supportBToCService, supportStoreService } = locateInfo
			this.setData({
				supportStoreService,
				supportB2COrStore: supportBToCService || supportStoreService,
			})
		},
		'couponNum': function (couponNum) {
			this.setCouponNumber(couponNum)
		},
    'activityBgObj': function (obj) {
      this.setActivityBackground(obj)
    },
		isShowNewGoodsArea: function () {
      this.setActivityBackground(this.data.activityBgObj)
		}
	},

	/**
	 * 组件的初始数据
	 */
	data: {
		navBarBgColor: '#transparent',
		/**展示定位提示 */
		showAddressTips: false,
		/**展示去开启按钮 */
		showOpenBtn: true
	},

	_isAttached: false,
	_cityID: null,
	_navBarHeight: 0,

	/**
	 * 组件的方法列表
	 */
	methods: {
		getNavBarHeight(event) {
			this._navBarHeight = event.detail
			this.triggerEvent('getNavBarHeight', event.detail)
			homeDeliveryStorageCache.setCache('_navBarHeight', event.detail)
		},
		setNavBarColor(bool) {
      const color = bool ? '#000000' : '#ffffff'
      wx.setNavigationBarColor({
        frontColor: color,
        backgroundColor: color
      })
    },
		// 跳转到查询优惠券页面
		navigateToCoupon: app.subProtocolValid('memberService', wrapSensorReportFn(function () {
			if (app.checkSignInsStatus()) {
				// 用户已登录
				wx.reportAnalytics('coupon_click') // 首页-会员优惠券点击
				this.showTicketSubscribe().then(() => wx.navigateTo({
					url: '/userA/pages/coupon/index',
				}))
			} else {
				app.signIn()
			}
		}, returnClickReportData(TAP_COUPON_ICON))
		),

		/**
		 * 跳转会员码页
		 */
		navigateToMemberCode: app.subProtocolValid('memberService', wrapSensorReportFn(function() {
      console.log('不会是这里吧 relativeNavTop____________');
			if (app.checkSignInsStatus()) {
				// 用户已登录
				wx.navigateTo({
					url: '/userB/pages/memberCenter/index',
				})
			} else {
				app.signIn()
			}
		}, returnClickReportData(TAP_MEMBER_CODE_ICON))),
		/**
		 * @desc 展示会员码提示，5秒之后关闭
		 */
		setMemberCodeTips () {
			console.log('setMemberCodeTips');
			this.setData({
				showMemberCodeTips: true
			})
			setTimeout(() => {
				this.setData({
					showMemberCodeTips: false
				})
			}, MEMBER_CODE_SHOW_TIME)
		},
		observerSearchContent () {
			this.createIntersectionObserver().relativeToViewport({ top: -this._navBarHeight })
      .observe('#relative-nav-top_search-content', ({ intersectionRatio }) => {
				this.setNavBarColor(!intersectionRatio)
      })
		},
		async setCouponNumber (couponNum) {
			let couponNumberStr = ''
			if (couponNum) {
				couponNumberStr = couponNum > 99 ? '99+' : `${couponNum}`
			}
			this.setData({
				couponNumberStr
			})
		},
    /**
     * 设置大促背景
     * @param {*} activityBgObj
     */
    setActivityBackground (activityBgObj) {
		const {
			pic1,
			pic2,
			imageType,
		} = activityBgObj

	  const hasPic = imageType === '0' ? pic1 : pic1 && pic2
      // 展示了新人专享区域 且 无状态栏背景图或者无焦点图，不需要设置大促背景
      if (this.data.isShowNewGoodsArea || !hasPic) {
        this.setData({
          mainHeight: 'auto',
          activityBgStyle: ''
        })
        return
      }
      const { statusBarHeight, windowWidth } = wx.getStorageSync('systemInfo') || {}
      const mainConcentH = MAIN_CONTENT_HEIGHT * windowWidth / 750 // 主视觉区域高度 rpx转px
      const _navBarHeight = this._navBarHeight || homeDeliveryStorageCache.getCache('_navBarHeight', 0)
      const capsuleAreaH = _navBarHeight - statusBarHeight // 胶囊区域高度 = 自定义导航高度 - 状态栏高度
      const mainHeight = mainConcentH - capsuleAreaH // 主内容区域高度 = 主视觉区域高度 - 胶囊区域高度高度

	  let activityBgStyle = ''
	  let bgPosition = ''

	  //	动图模式
	  if (imageType === '0') {
		activityBgStyle = [
			`background-image:url(${PIC_DOMAIN}${pic1})`,
			`background-position: 0 0`,
			`background-size: 100% auto`,
			'background-repeat: no-repeat, no-repeat'
		].reduce((prev, current) => {
			return prev += current + ';'
		}, '')
	  } else {
		  const activityBgStyleArr = [
			`background-image:url(${PIC_DOMAIN}${pic1}), url(${PIC_DOMAIN}${pic2})`,
			`background-position: 0 0, 0 ${statusBarHeight}px`,
			`background-size: 100% ${statusBarHeight}px, 100% auto`,
			'background-repeat: no-repeat, no-repeat'
		]
		activityBgStyle = activityBgStyleArr.reduce((prev, current) => {
			return prev += current + ';'
		}, '')
		bgPosition = `background-position: 0 0, 0 ${statusBarHeight + 35}px;background-size: 100% ${statusBarHeight + 35}px, 100% auto;`
	  }

      this.setData({
		imageType,
		bgPosition: bgPosition,
        mainHeight: mainHeight,
        activityBgStyle, // 大促背景样式
      })
      // 焦点图曝光埋点
      this.reportExpose()
    },
    /**
     * 上报曝光埋点
     */
    reportExpose () {
		const {
			__isStorageCache,
			bannerId,
			bannerName,
			openType,
			openValue,
		} = this.data.activityBgObj
		//	缓存设置的图片不上报埋点
		if (__isStorageCache) {
			return
		}

		sensors.adExposure({
			banner_id: bannerId,
			banner_name: bannerName,
			bannerType: openType,
			Position: 0,
      element_name: '首页大促背景图',
			element_code: 180014001,
			activity_ID: openValue,
		})
    },
    /**
     * 点击焦点图跳转
     */
    clickFocusArea (e) {
      const { data } = e.currentTarget.dataset
      act.toActivityPage(data)

      const { openValue, bannerId, bannerName, openType } = this.data.activityBgObj

      // 点击焦点图上报埋点
      sensors.adClick({
        bannerType: openType,
        banner_id: bannerId,
        banner_name: bannerName,
        Position: 0,
        element_name: '首页大促背景图',
        element_code: 180014001,
		activity_ID: openValue,
      })
    },
    async showAddressTips() {
      const now = Number.parseInt(Date.now() / 1000)
      const lastShow = wx.getStorageSync(SHOW_TIPS)
      // 超过7天没展示过
      const showByCache = !lastShow || (now - lastShow > (7 * 24 * 60 * 60))
      const locateAuth = await locateStore.checkHasAuth()
			const isVisitor = app.globalData.userAuth.isVisitor || false
      // 手动关闭过弹窗，本次进程不展示弹窗
      // 无权限或七天前显示过，才按权限展示弹窗
    	// 没弹过位置授权弹窗
      if ((!closeShowTips) && locateStore.hasLocationAuth !== true) {
				const showOpenBtn = (() => {
					// 访客模式不展示btn
					if (isVisitor) return false
					// 没有展示过位置【位置授权】弹窗
					if (locateAuth === locateStore.UNKNOW) return false
					return true
				})();
				this.setData({
					showAddressTips: true, // 使用默认城市时，必须展示提示条
					showOpenBtn: showOpenBtn
				})
				this.triggerEvent('hideAddModle')
				return
      }
			// 这里是没有选择默认地址的情况
      if ((!closeShowTips) && ((!locateAuth) || showByCache)) {
        this.triggerEvent('hideAddModle')
      }
			this.closeAddressTips()
    },
    closeAddressTips(e) {
      wx.setStorage({
        key: SHOW_TIPS,
        data: Number.parseInt(Date.now() / 1000)
      })
      this.setData({
        showAddressTips: false
      })
      // 当前进程手动关闭 记录状态
      // 是手动关闭的情况下，不重复给closeShowTips赋值
      closeShowTips || (closeShowTips = !!e)
      this.triggerEvent('closeLocateTips')
    },
    openSetting(){
       this.triggerEvent('openSetting')
    }
    },
	attached() {
		this._isAttached = true
		this.observerSearchContent()
    }
})
