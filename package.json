{"name": "wxapp", "version": "v3.7.0", "private": true, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "dependencies": {"chalk": "^4.1.2", "gulp-htmlmin": "^5.0.1", "miniprogram-ci": "2.1.8", "mobx-miniprogram": "^6.12.3", "mobx-miniprogram-bindings": "^5.0.0", "regenerator-runtime": "^0.13.9"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/register": "^7.16.0", "babel-jest": "^27.0.6", "babel-polyfill": "^6.26.0", "commitizen": "^3.0.0", "commitlint": "^13.1.0", "commitlint-config-cz": "^0.13.2", "conventional-changelog": "^3.1.24", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^6.3.0", "del": "^6.0.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.1", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-imagemin": "7.1.0", "gulp-path-alias": "^1.2.1", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.3", "gulp-sass": "^5.0.0", "gulp-string-replace": "^1.1.2", "gulp-uglify": "^3.0.2", "husky": "^4.0.2", "jest": "^27.0.6", "minimist": "^1.2.6", "miniprogram-api-typings": "^4.0.1", "miniprogram-simulate": "^1.4.1", "sass": "^1.38.2", "typescript": "^5.6.3"}, "resolutions": {"miniprogram-api-typings": "^4.0.1"}, "scripts": {"dev": "gulp dev", "build": "gulp build --unhandled-rejections=strict", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "cz:changelog": "conventional-changelog -p angular -i CHANGELOG.md -s && git add CHANGELOG.md", "build-mp:test": "cross-env NODE_ENV=test node build/cd/mp.js", "build-mp:uat": "cross-env NODE_ENV=staging node build/cd/mp.js", "build-mp:prod": "cross-env NODE_ENV=prod node build/cd/mp.js", "preview": "miniprogram-ci preview --pp ./src/ --scene 1001 --pkp private/private.wx1f9ea355b47256dd.key --appid wx1f9ea355b47256dd --upload-description test环境 --uv 3.7 --enable-es6 true --enable-es7 true --enable-minify true --enable-bigPackageSizeSupport true --qrcode-format terminal --qrcode-output-dest /previewCode/code.jpg", "preview:uat": "miniprogram-ci preview --pp ./src/ --scene 1001 --pkp private/private.wx1f9ea355b47256dd.key --appid wx1f9ea355b47256dd --upload-description uat环境 --uv 3.7 --enable-es6 true --enable-es7 true --enable-minify true --enable-bigPackageSizeSupport true --qrcode-format terminal --qrcode-output-dest /previewCode/code.jpg", "preview:prd": "miniprogram-ci preview --pp ./src/ --scene 1001 --pkp private/private.wx1f9ea355b47256dd.key --appid wx1f9ea355b47256dd --upload-description prd环境 --uv 3.7 --enable-es6 true --enable-es7 true --enable-minify true --enable-bigPackageSizeSupport true --qrcode-format terminal --qrcode-output-dest /previewCode/code.jpg", "upload": "npm run build && node uploadCode.js", "uploadSensor": "node build/uploadSensor/uploadSensorJson.js", "pull": "git pull && git submodule update --init"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": ""}}}