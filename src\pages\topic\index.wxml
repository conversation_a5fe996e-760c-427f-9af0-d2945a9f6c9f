<!--homeDelivery/pages/activity/index.wxml-->
<import src="/pages/template/index.wxml" />

<wxs module="moduleFn">
  var getNavHeight = function(navList) {
    if (!navList.length || navList.length < 3) {
      return 1
    }
    var name = navList[0].name
    var pic = navList[0].pic
    if (pic) {
      return 72
    }
    return 103
  }
  var checkModuleIsEmpty = function (module) {
    if (module.type === '999') {
      return false
    }
    if ((module.type === '2' || module.type === '3' || module.type === '6')) {
      var goodsList = module.showList.filter( function(goods) {
        return goods
      })
      return goodsList.length
    }
    return true
  }
  var getGoodsHeight = function (type) {
    if (type === '2') {
      return 240
    } else if (type === '3') {
      return 618
    } else if (type === '6') {
      return 425
    }
  }
  var getImgStyle = function (url) {
    // 使用正则表达式匹配height=数字部分
    var e = getRegExp('height=(\d+)')
    var heightMatch = url.match(e);
    if (heightMatch && heightMatch[1]) {
      return 'min-height:' + heightMatch[1] + 'rpx;'
    }
  }
  module.exports.getNavHeight = getNavHeight
  module.exports.checkModuleIsEmpty = checkModuleIsEmpty
  module.exports.getImgStyle = getImgStyle
  module.exports.getGoodsHeight = getGoodsHeight
</wxs>

<nav-bar
  normalBack
  background="{{navBarBgColor}}"
  color="{{navBarColor}}"
  navBarTitle="{{navBarTitle}}"
  bindnavBarHeight="getNavBarHeight"
  style="position:absolute;"
>
  <view slot="back" class="nav-back"  style="filter: brightness({{backFilter}})">
    <image src="/source/images/arrow_back.png" />
  </view>
</nav-bar>

<main-page currentView="{{currentView}}">
  <!-- observer参考系 -->
  <view id="ob-reference" style="top:{{navBarHeight}}px"></view>
  <view class="topic-container">
    <view wx:if="{{isShowBlank}}" class="scroll-view-blank">
      <image mode="widthFix" src="https://resource.pagoda.com.cn/dsxcx/images/79f174dd8dab1b7f00492faa21607e0f.png"/>
    </view>
    
    <scroll-view scroll-y="true" style="height: {{scrollViewHeight+'px'}};background: {{pageConfig.backgroundColor}}" scroll-into-view="{{toView}}"	scroll-into-view-offset="-{{navBarHeight}}" bindscroll="isScroll" class="scroll-box">
      <view wx:if="{{!isScrollNav}}" style="height: {{navBarHeight}}px"></view>
      <view>
      <block wx:if="{{hasData}}" wx:for="{{topicList}}" wx:for-index="idx" wx:key="idx" wx:for-item="topicItem">
        <!-- type:7 导航组件模板 -->
        <view wx:if="{{moduleFn.checkModuleIsEmpty(topicItem)}}" class="sv-anchor">
          <!-- <view class="inner-anchor end-anchor" data-endid="{{topicItem.navItemIdx - 1}}"></view> -->
          <!-- <view id="{{topicItem.id}}" class="inner-anchor start-anchor" data-startid="{{topicItem.navItemIdx}}"></view> -->
          <view class="inner-anchor end-anchor {{topicItem.type === '7' ? 'inner-anchor-nav': ''}}" data-endid="{{topicItem.navItemIdx - 1}}"></view>
					<view id="{{topicItem.id}}" style="height:{{moduleFn.getNavHeight(navList)}}rpx" class="{{topicItem.type === '7' ? 'inner-anchor-nav': ''}} inner-anchor start-anchor" data-startid="{{topicItem.modulePriority}}"></view>
        </view>
        <!--type:1 图片  1：图片模板，2 单排商品模板 3 双排商品模板 4 优惠券组件模板-->
        <view wx:if="{{topicItem.type === '1'}}" class="topic-img-box">
          <view class="topic-img-box-item" wx:for="{{topicItem.activityPicList}}" wx:for-item="pic" wx:for-index="picIdx" wx:key="picIdx" 
          style="{{moduleFn.getImgStyle(pic.pic)}}"
          >
            <!-- openType为70表示店长二维码 -->
            <shop-manager-qrcode wx:if="{{pic.openType === '70'}}" picUrl="{{pic.pic}}"></shop-manager-qrcode>
            <!-- onpenType为3表示商品详情模板，目前点击事件只有加购 -->
            <pic-goods
              wx:else
              class="p-bottom24"
              goodsObj="{{pic.goodsInfo || null}}"
              picObj="{{pic}}"
              fromShareTimeLine="{{fromShareTimeLine}}"
              bind:clickPic="clickPic"
              bind:updateCount="updateCartCount"
              bindcomplete="handleCountComplete"
              bind:wxsAnimation="handlewxsAnimation"
              bind:toShowChoiceLayer="toShowChoiceLayer"
              addSensorskey="categoryTopicAddCart"
              choiceSensorskey="categoryTopicToChoice"
              activityObj="{{activityObj}}"
              isShowCount
            ></pic-goods>

          </view>
        </view>
        <!-- type:2 单排商品 -->
        <!-- type:3 双排商品 -->
        <!-- type:6 三排商品 -->
        <view wx:if="{{topicItem.type === '2' || topicItem.type === '3' || topicItem.type === '6'}}" class="{{topicItem.type !== '2' ? 'goods-gap-box' : 'single-goods-gap-box'}} topic-item-goods">
          <block wx:for="{{topicItem.showList}}" wx:key="index">
            <view class=" type-margin-hor {{topicItem.type === '2' ? 'type-margin-ori' : ''}}"
              style="min-height: {{moduleFn.getGoodsHeight(topicItem.type)}}rpx"
            >
              <common-goods
                id="goods_{{item.goodsSn}}"
                style="{{topicItem.type === '2' ? 'width:100%' : ''}}"
                componentRow="{{componentFeature[topicItem.type]}}"
                class="p-bottom24"
                goodsObj="{{item}}"
                isShowCount
                isShowCountDown="{{topicItem.type !== '6'}}"
                isShowSubtitle="{{topicItem.type === '2' || topicItem.type === '3' ? true : false}}"
                fromShareTimeLine="{{fromShareTimeLine}}"
                bind:updateCount="updateCartCount"
                bindcomplete="handleCountComplete"
                bind:wxsAnimation="handlewxsAnimation"
                bind:toShowChoiceLayer="toShowChoiceLayer"
                addSensorskey="categoryTopicAddCart"
                choiceSensorskey="categoryTopicToChoice"
                activityObj="{{activityObj}}"
                customClassName="{{item.takeawayAttr === 'B2C' ? 'higher' : 'topicTimelyGoods'}}"
                lazyLoad
              ></common-goods>
            </view>
          </block>
          <!-- 展示售罄商品 -->
          <block wx:if="{{topicItem.showListStatus}}">
            <pagoda-sellout-item style="flex:1;" sellOutNum="{{topicItem.hideListNum}}">
                <block wx:if="{{topicSelloutGoodsMap}}" wx:for="{{topicSelloutGoodsMap[idx] || []}}" wx:key="index">
                    <view class=" type-margin-hor {{topicItem.type === '2' ? 'type-margin-ori' : ''}}">
                        <common-goods
                          id="goods_{{item.goodsSn}}"
                          style="{{topicItem.type === '2' ? 'width:100%' : ''}}"
                          componentRow="{{componentFeature[topicItem.type]}}"
                          class="p-bottom24"
                          goodsObj="{{item}}"
                          isShowCount
                          isShowCountDown="{{topicItem.type !== '6'}}"
                          isShowSubtitle="{{topicItem.type === '2' || topicItem.type === '3' ? true : false}}"
                          fromShareTimeLine="{{fromShareTimeLine}}"
                          bind:updateCount="updateCartCount"
                          bindcomplete="handleCountComplete"
                          bind:wxsAnimation="handlewxsAnimation"
                          bind:toShowChoiceLayer="toShowChoiceLayer"
                          addSensorskey="categoryTopicAddCart"
                          choiceSensorskey="categoryTopicToChoice"
                          activityObj="{{activityObj}}"
                          customClassName="{{item.takeawayAttr === 'B2C' ? 'higher' : ''}}"
                          lazyLoad
                        ></common-goods>
                    </view>
                </block>
            </pagoda-sellout-item>
          </block>
        </view>

        <view wx:if="{{topicItem.type === '8'}}" class="price-module-box">
          <block wx:for="{{topicItem.showList}}" wx:key="index">
            <view class=" type-margin-hor type-margin-ori">
              <price-goods
                goodsObj="{{item || null}}"
                bind:clickPic="clickPic"
                bind:updateCount="updateCartCount"
                bindcomplete="handleCountComplete"
                bind:wxsAnimation="handlewxsAnimation"
                bind:toShowChoiceLayer="toShowChoiceLayer"
                addSensorskey="categoryTopicAddCart"
                choiceSensorskey="categoryTopicToChoice"
              >
              </price-goods>
            </view>
          </block>
          <!-- 展示售罄商品 -->
          <block wx:if="{{topicItem.showListStatus}}">
            <pagoda-sellout-item style="flex:1;" sellOutNum="{{topicItem.hideListNum}}">
              <block wx:for="{{topicItem.sellOutList}}" wx:key="index">
                <view class=" type-margin-hor type-margin-ori">
                  <price-goods
                    goodsObj="{{item || null}}"
                    bind:clickPic="clickPic"
                    bind:updateCount="updateCartCount"
                    bindcomplete="handleCountComplete"
                    bind:wxsAnimation="handlewxsAnimation"
                    bind:toShowChoiceLayer="toShowChoiceLayer"
                    addSensorskey="categoryTopicAddCart"
                    choiceSensorskey="categoryTopicToChoice"
                  >
                  </price-goods>
                </view>
              </block>
            </pagoda-sellout-item>
          </block>
        </view>

        <view wx:if="{{topicItem.type === '999' && topicItem.navigationList.length > 2}}" style="position: sticky;z-index:1000;top:{{navBarHeight}}px;background: {{pageConfig.backgroundColor}}">
          <!-- sticky="{{false}}" -->
          <sticky-nav id="stickyNav" name="stickyNav" navList="{{topicItem.navigationList}}" styleConfig="{{pageConfig}}" sticky="{{false}}" showFull="{{false}}" bind:stickyNav="stickyNav" bind:noStickyNavHide="noStickyNavHide" scrollInfo="{{scrollInfo}}"></sticky-nav>
        </view>
        <view wx:if="{{topicItem.type === '4'}}" class="topic-img-box">
          <block wx:for="{{topicItem.couponList}}" wx:for-item="coupon" wx:for-index="couponIdx" wx:key="couponIdx">
            <image src="{{picUrl}}{{coupon.imgUrl}}" style="width: 100%;" mode="widthFix" data-item="{{coupon}}" bindtap="handleTapCoupon"/>
          </block>
        </view>
        <!-- type:9 榜单模块 -->
        <view wx:if="{{topicItem.type === '9'}}" class="top-module-box">
          <block wx:for="{{topicItem.goodsList}}" wx:key="index">
            <view
              wx:if="{{!item.templatePic}}" 
              class=" type-margin-hor type-margin-ori topic-topgoods"
            >
              <image style="left: 16rpx"  class="topic-topgoods-icon" src="{{item.topIconUrl}}" />
              <common-goods
                lazyLoad
                id="goods_{{item.goodsInfo.goodsSn}}"
                style="width:100%"
                componentRow="single"
                class="p-bottom24"
                goodsObj="{{item.goodsInfo}}"
                isShowCount
                isShowCountDown="true"
                isShowSubtitle="true"
                fromShareTimeLine="{{fromShareTimeLine}}"
                bind:updateCount="updateCartCount"
                bindcomplete="handleCountComplete"
                bind:wxsAnimation="handlewxsAnimation"
                bind:toShowChoiceLayer="toShowChoiceLayer"
                addSensorskey="categoryTopicAddCart"
                choiceSensorskey="categoryTopicToChoice"
                activityObj="{{activityObj}}"
                customClassName="{{item.goodsInfo.takeawayAttr === 'B2C' ? 'higher' : 'topicTimelyGoods'}}"
              ></common-goods>
            </view>
            <view 
              wx:else
              class="topic-img-box-item topic-topgoods topic-topgoods-img"
            >
              <pic-goods
                wx:if="{{item.goodsInfo}}"
                class="p-bottom24"
                goodsObj="{{item.goodsInfo}}"
                picObj="{{item}}"
                fromShareTimeLine="{{fromShareTimeLine}}"
                bind:clickPic="clickPic"
                bind:updateCount="updateCartCount"
                bindcomplete="handleCountComplete"
                bind:wxsAnimation="handlewxsAnimation"
                bind:toShowChoiceLayer="toShowChoiceLayer"
                addSensorskey="categoryTopicAddCart"
                choiceSensorskey="categoryTopicToChoice"
                activityObj="{{activityObj}}"
                isTopGppds="true"
              ></pic-goods>
            </view>
          </block>
        </view>
        <view wx:if="{{topicItem.type === '10' && topicItem.videoType === '1'}}" class="video-module-box">
          <video-module
            src="{{topicItem.videoObj.videoUrl}}"
            videoId="video_{{topicItem.videoObj.id}}"
            object-fit="fill"
            class="video-module"
            show-cover="false"
            poster="{{topicItem.videoObj.videoCover}}"
            data-priority="{{topicItem.videoIndex}}"
            videoIndex="{{topicItem.videoIndex}}"
            width="{{topicItem.videoObj.width}}"
            height="{{topicItem.videoObj.height}}"
            isInView="{{currentPlayingVideo === topicItem.videoIndex}}"
            bindmanualPlay="handleVideoManualPlay"
            bindscreenChange="handleScreenChange"
          />
        </view>
      </block>
      </view>
      <view wx:if="{{!hasData}}" class="no-data">
        <view>
          <image class="icon-no-data" src="https://resource.pagoda.com.cn/dsxcx/images/6709774a863a5234a5aecc00aaa7c21e.png"></image>
          <view class="l-t">这里没有任何内容哦~</view>
          <view class="back-btn" bindtap='backPage'>回首页</view>
        </view>
      </view>
      <!-- 购物车结算栏 -->
      <view style="{{activityType == 'onlinePrice' ? '--primary: #FE2E38' : ''}}">
        <cart-popup wx:if="{{ showCartSubmit }}"  orderType="{{ ORDER_TYPE.TIMELY }}" />
      </view>
      <!-- 购物车结算栏 -->
    </scroll-view>

    <!-- 微信卡券 -->
    <v-popup
      z-index="{{10002}}"
      show="{{couponIsShow}}"
      closeable="{{false}}"
      customStyle="height:352rpx;width:560rpx;border-radius:8rpx;"
      close-on-click-overlay="{{false}}">
      <view class="coupon-container">
        <view class="coupon-title">提示</view>
        <view class="coupon-subtitle">
          <view>优惠券领取成功，已帮你插入微信卡包，可及时获取优惠券过期提醒</view>
        </view>

        <view class="coupon-btns">
          <view class="coupon-btn coupon-btn--primary" catchtap='couponClose'>
            <send-coupon
              bindcustomevent="onCouponComfirmBtnTap"
              send_coupon_params="{{coupon.send_coupon_params}}"
              sign="{{coupon.sign}}"
              send_coupon_merchant="{{coupon.send_coupon_merchant}}"
            >
              知道了
            </send-coupon>
          </view>
        </view>
      </view>
    </v-popup>
  </view>
  <!-- 蒙层 -->
  <view class="prevent-screen" hidden="{{!prevent}}"></view>
  <!-- <template is="add-to-cart-animation" data="{{...addToCartData}}"></template> -->
  <!--cart-animation-ball id="goodsCartAnimationBall"></cart-animation-ball -->
  <!-- sku加购弹层 -->
  <add-goods-layer showLayer="{{showLayer}}" goodsInfo="{{goodsInfo}}" sensorsInfo="{{activityObj}}"
bind:updateCount="updateCartCount"></add-goods-layer>
</main-page>


<confirm-modal id="globalModal" globalModalInfo="{{globalLocateModalInfo}}"></confirm-modal>
<request-subscribe show="{{subscribe.show}}" tmpl-ids="{{subscribe.tmplIds}}" with-tab-bar bind:close="afterTicketEntrySubscribe" />
