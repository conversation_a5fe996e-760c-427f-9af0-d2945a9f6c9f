// userA/pages/myGrade/index.js
let bg_grade_general = require('../../source/image-base64/bg_grade_general').bg //普卡背景图
let bg_grade_silver = require('../../source/image-base64/bg_grade_silver').bg //银卡背景图
let bg_grade_gold = require('../../source/image-base64/bg_grade_gold').bg //金卡背景图
let bg_grade_diamond = require('../../source/image-base64/bg_grade_diamond').bg //钻卡背景图
let bg_grade_vip = require('../../source/image-base64/bg_grade_vip').bg //心享会员背景图
let {jumpH5Vip} = require('../../../utils/services/jumpBgxxVip')
var common = require('../../../source/js/common');
var commonObj = common.commonObj;
const sensors = require('../../../utils/report/sensors')
const prdPicDomain = require('../../../utils/config').prdUrl.PAGODA_PIC_DOMAIN
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userName: '',
    bg_grade_footer: `${prdPicDomain}/group1/M21/6D/A2/CmiLkGG620CAeySmAAA-moPULnI297.png`, //普通会员、试用心享会员底部banner
    bg_grade_vip_footer: `${prdPicDomain}/group1/M21/6D/45/CmiWa2G66VSASLsGAAAktiVAQP4654.png`, //心享会员底部banner
    bgGrade: { // 顶部背景图
      '1': bg_grade_general,
      '2': bg_grade_silver,
      '3': bg_grade_gold,
      '4': bg_grade_diamond,
      'vip': bg_grade_vip
    },
    trackColor: { // 进度条轨道颜色值
      '1': '#3AA668',
      '2': '#A7A7A7',
      '3': '#E6B576',
      '4': '#A8B9C8',
      'vip': '#C29967'
    },
    gradeInfo: {
      levelID: '', // 当前等级id：普1 银2 金3 钻4
      levelName: '', // 当前等级名称
      levelExpireTime: '', // 当前等级积分(果粒值)过期时间 或者 心享会员/试用心享会员到期时间
      integral: 0, // 当前获取果粒值
      superVipStatus: '', // 百享会员状态（C:普通，T：试用，F: 百享会员）
      diffIntegralToUpgrade: 0, // 下一等级升级积分差额
      upgradeLevelName: '', // 下一等级名称
      isIdentified: '', // 是否实名认证（Y：已实名   N：未实名）
      publicPrivilegeList: [], // 公共的特权详情列表
      privilegeList: [], // 当前等级的特权详情列表
      diffPrivilegeList: [], // 对比下一等级的特权详情列表
      superVipRenewTip: '', // 心享会员续费提示
      superVipPageUrl: '', // 底部按钮心享小程序跳转url
    },
    isSuperVip: false, // 是否是心享会员/试用心享会员
    picUrl: commonObj.PAGODA_PIC_DOMAIN, // 图片地址前缀
    progressStartText: '', // 进度条开始阀值
    progressEndText: '', // 进度条结束阀值
    percentage: 0 // 当前果粒值百分比
  },
  _data: {
    memberRuleInfoUrl: '' // 查看规则页url
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    //  上报页面浏览事件
    sensors.pageScreenView()
    this.getUserName()
    this.getMemberPrivilege()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  // 获取用户名
  getUserName() {
    const user = wx.getStorageSync('user');
    const { nickName = '' } = wx.getStorageSync('userNameAndImg') || {}
    const { phoneNumber = '' } = user
    // 用户脱敏手机号
    const maskPhone = phoneNumber.substring(0, 3) + '****' + phoneNumber.substring(7, 11)
    this.setData({
      userName: nickName ? nickName : maskPhone
    })
  },
  // 获取会员权益数据
  getMemberPrivilege() {
    const customerID = wx.getStorageSync('user').userID || -1
    app.api.getMemberLevel(customerID).then(res => {
      if (!res.data) {
        return
      }
      const { levelName, superVipStatus, integral, diffIntegralToUpgrade, upgradeLevelName, memberRuleInfoUrl, superVipSaveMoney } = res.data
      let percentage = 0
      if ( superVipStatus === 'C') { // 普通会员
        // 果粒值总值
        const integralTotal = Number(integral) + Number(diffIntegralToUpgrade)
        // 当果粒值差额为0,则进度为100%
        percentage = Number(diffIntegralToUpgrade) === 0 ? 100 : (integral / integralTotal) * 100
      } else {
        percentage = Number(superVipSaveMoney) > 199 ? 100 : (superVipSaveMoney / 199) * 100
      }

      this.setData({
        superVipSaveMoney,
        gradeInfo: res.data,
        percentage,
        isSuperVip: superVipStatus === 'T' || superVipStatus === 'F'
      })
      // console.log(integralTotal)
      console.log('果粒值进度', this.data.percentage)
      // 普通会员
      if (!this.data.isSuperVip) {
        this.setData({
          ['gradeInfo.levelName']: `${levelName}卡`,
          ['gradeInfo.upgradeLevelName']: `${upgradeLevelName}卡`,
          progressStartText: levelName,
          progressEndText: upgradeLevelName
        })
      } else {
        // 心享会员levelName
        this.setData({
          ['gradeInfo.levelName']: superVipStatus === 'F' ? '心享会员' : '试用心享会员'
        })
      }
      this._data.memberRuleInfoUrl = memberRuleInfoUrl

    }).catch((error) => {
      console.log(error)
      commonObj.showModal('提示', '系统繁忙，请求超时', false, '我知道了');
    })
  },
  // 专属权益事件处理
  tapPrivlgHandle(e) {
    if (this.data.isSuperVip) {
      // 心享/试用心享会员
      jumpH5Vip('memberIndex')
    } else {
      // 普通会员
      this.toPrivlgDetailPage(e)
    }
  },
  // 跳转到会员权益详情页
  toPrivlgDetailPage(e) {
    const { url, name } = e.currentTarget.dataset
    switch (name) {
      case "三无退货":
      // 上报神策点击三无退货
      this.trackClickEvent({
        'element_code': '120301001',
        'element_name': '三无退货',
        'element_content': '三无退货',
        'screen_code': '1203',
      })
      break
      case "充值加赠":
      // 上报神策点击充值加赠
      this.trackClickEvent({
        'element_code': '120301002',
        'element_name': '充值加赠',
        'element_content': '充值加赠',
        'screen_code': '1203',
      })
      break
      case "会员专享价":
      // 上报神策点击会员专享价
      this.trackClickEvent({
        'element_code': '120301003',
        'element_name': '会员专享价',
        'element_content': '会员专享价',
        'screen_code': '1203',
      })
      break
      case "积分兑好礼":
      // 上报神策点击积分兑好礼
      this.trackClickEvent({
        'element_code': '120301004',
        'element_name': '积分兑好礼',
        'element_content': '积分兑好礼',
        'screen_code': '1203',
      })
      break
      default:
      console.log('error')
    }
    wx.navigateTo({
      url: '/h5/pages/myPrivlgDetail/index?url=' + encodeURIComponent(url)
    })
  },
  // 跳转到心享小程序
  toMemberMiniProgram(e) {
    jumpH5Vip()
    if( !this.data.gradeInfo.superVipRenewTip || this.data.gradeInfo.superVipRenewTip === "立即开通" ) {
      // 上报神策点击立即开通
      this.trackClickEvent({
        'element_code': '120303001',
        'element_name': '开通',
        'element_content': '开通',
        'screen_code': '1203'
      })
    } else if( this.data.gradeInfo.superVipRenewTip === "立即续费" ) {
      // 上报神策点击立即续费
      this.trackClickEvent({
        'element_code': '120303002',
        'element_name': '续费',
        'element_content': '续费',
        'screen_code': '1203'
      })
    }
  },
  // 查看果粒值明细
  toMemberLevel() {
    // 上报神策点击果粒值
    this.trackClickEvent({
      'element_code': '120300002',
      'element_name': '果粒值',
      'element_content': '果粒值',
      'screen_code': '1203',
    })
    wx.reportAnalytics('vip_guolizhi');
    wx.navigateTo({
      url: '/userA/pages/berryValue/index'
    })
  },
  // 查看会员积分规则
  toRulePage() {
    // 上报神策点击查看规则
    this.trackClickEvent({
      'element_code': '120300001',
      'element_name': '查看规则',
      'element_content': '查看规则',
      'screen_code': '1203',
    })
    wx.reportAnalytics('myprivlg_navigateinfo');
    wx.navigateTo({
      url: `/h5/pages/commonLink/index?pageUrl=${encodeURIComponent(this._data.memberRuleInfoUrl)}`
    })
  },
  // 实名认证
  toAuthPage() {

  },
  // 神策上报点击事件
  trackClickEvent( params = {} ) {
    if ( app.globalData.reportSensors ) {
      app.sensors.track('MPClick', params)
    }
  }
})
