const request = require('../utils/request')
const config = require('../utils/config')
module.exports = {
  // 地址相关接口
  getAddressList (userID) {
    return request.get({ url: `/api/v2/customer/address/freshList/${userID}`})
  },
  // 获取地址标签
  getLablesList () {
    return request.get({ url: `/api/v1/content/addressLable/list`})
  },
  // 新增收货地址
  addAddress (data) {
    return request.post({ url: `/api/v1/customer/address/create`, data})
  },
  // 编辑收货地址
  editAddress (data) {
    return request.post({ url: `/api/v1/customer/address/edit`, data})
  },
  // 删除地址
  delAddress ({userID, addressId}) {
    return request.post({ url: `/api/v1/customer/address/delete/${userID}/${addressId}`, isLoading: false})
  },
  // 检查城市服务
  checkCity (data = {}) {
    return request.post({ url: '/dskhd/api/city/v1/checkCity', data: Object.assign(data, {
      lat: String(data.lat),
      lon: String(data.lon),
    }) })
  },
  // 获取常用收获地址
  getFrequentlyList (data) {
    return request.post({url: `/wxapp/address/v2/getFrequentlyList`, data})
  },
  getStoreInfoById(data) {
    return request.post({url: `/wxapp/store/v1/getStoreInfoByID`, data})
  },
  getStoreDetail(data) {
    return request.post({url: '/dskhd/api/store/v1/getStoreDetailByCode', data})

  },
  /** 门店ERP信息 */
  getERPAreaInfo(data) {
    return data.storeCode
      ? request.post({url: '/dskhd/api/store/v1/getERPAreaInfo', data: Object.assign(data, { storeCode: String(data.storeCode) }) }).catch(() => ({ data: {} }))
      : Promise.resolve({ data: {} })
  },
  isExistOvertimeActivity(data) {
    return request.post({url: `/wxapp/city/v1/isExistOvertimeActivity`, data, isLoading: false })
  }
}
