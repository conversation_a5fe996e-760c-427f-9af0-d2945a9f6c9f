/**
 * 页面名称：订单接龙详情页
 */
import { fruitOrderPay } from '../../../utils/services/fruitOrderPay'
const app = getApp()
const wxbarcode = require.async('../../../sourceSubPackage/barcode/index.js');
const { relayOrdeDetailrMap } = require('../../sensorReportData')
const sensors = require('../../../utils/report/sensors')
const SMSMixin = require('../../../mixins/SMSMixin')
import { drawSharePic,getCanvasLocalImage} from '../common/shareRelay'
import { getStoreBusinessTime, formatPrice, toHttps } from '../../../utils/util'
const locateService = require('../../../utils/services/locate');
import { ORDER_TYPE } from '../../../source/const/order';
import { handleGoodsName } from '../../../service/fruitGoodsUtil';
import { pageTransfer } from '~/utils/route/pageTransfer';
const log = require('../../../utils/log.js')
const { handlePickupTime } = require('../common/orderGoods')
const {
  //  打开导航
  openNavigate
} = require('../../../utils/util')
//  倒计时函数
const CountDown = require('../../../utils/countDown')
import util from '../../../utils/util'
const {
  //  获取商品快照
  mapSnapShotDetail
} = require('../common/orderGoods')

const commonObj = require('../../../source/js/common').commonObj

const { refundInfo } = require('../common/refund.js')

//  订单状态键值对（后面要放到公共常量文件里）
const STATUS_CODE = {
  //  交易成功
  OK: 90,
  //  已取消
  CANCEL: 100,
  //  待付款
  UN_PAYMENT: 10,
  //  待自提
  DISPATCHING: 80,
  //  退款中
  REFUNDING_VAL: 85
}

let globalPickupCode = ''

Page({
  mixins: [SMSMixin],
  data: {
    //  当前用户
    user: wx.getStorageSync('user') || {},
    //  接龙订单
    orderClass: 'D',
    //  订单信息
    orderDetail: {},
    //  订单id
    orderNo: '',
    //  是成功状态
    isSuccessStatus: false,
    //  是取消状态
    isCancelStatus: false,
    //  是待付款状态
    isPendingStatus: false,
    //  是待自提状态
    isWaitingCollectionStatus: false,

    //  状态文本
    statusTitle: '',
    //  状态描述
    statusDesc: '',
    //  状态类名
    statusClassName: '',

    //  门店信息对象
    store: {},
    storeAddress: '',
    //  接龙商品列表
    goodsList: [],
    //  订单信息展示数组
    timeLineList: [],

    //  取消订单弹窗是否显示
    orderCancelIsShow: false,
    //  是否iphoneX
    isIphoneX: app.globalData.isIphoneX,

    //  倒计时实例
    countDown: null,

    //  左侧菜单收起按钮
    leftBtnList: [],
    //  右侧展示按钮
    btnList: [],

    //  导航栏背景颜色
    navBarBgColor: '#transparent',
    //  导航栏字体颜色
    navBarColor: '#fff',
    //  导航栏 filter样式
    backFilter: 1,

    paymentDialog: false, // 支付弹窗
    pagodaMoney: true, // 是否账户余额支付
    selectWxPay: true, // 是否微信支付
    selectUnionPay: true, // 是否云闪付
    needBorderRadius:true,
    reqRecordsList:true,
    goodTotalAmount:0,//接龙数量
    recordsList:[],//活动记录
    sharePic:'',
    /** 不可开票文案 */
    noInvoiceDesc: '',
    /** 不满意原因确认弹窗是否显示 */
    showReasonConfirmModal: false,
    /** 是否渲染不满意原因确认弹窗 */
    visibleReasonConfirmModal: false,
    /** 混合订单商品列表 */
    mixGroupGoodsList: []
  },
  //  不知名属性，为了让混入能够奏效
  _data: {
    storeImageStatus: {},
    drawStatus:false,
    // 点击三无退按钮时索引
    clickIndex: -1,
    // 点击的即将进行三无退的商品行
    clickRefundGoods: {},
    /** 选择的不满意类型索引 */
    primaryReasonIndex: -1,
    /** 选择的不满意原因索引列表 */
    reasonIndex: [],
    // 订单的完结时间 存起来将在重新申请时传值给售后详情页
    finishTime: '',
    // 服务器与本地偏移时间ms
    // 由于本地时间与服务器时间可能存在误差，计算三无退倒计时时可能存在一定的时间偏差
    // 本地时间与服务器时间偏差值 所以这里记录的是 服务器-本地时间 的偏差值
    // 如果服务器的时间快于客户端，则偏差值为正
    // 如果服务器的时间慢于客户端 则偏差值为负
    // 如果要获取准确的服务时间 则  服务器时间 = 客户端时间 + 偏差值
    offSetTime: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 页面跳转使用options.orderObj JSON字符串传参
    // 订阅消息推送使用oprions.orderNo传参
    const orderObj = options.orderObj ? JSON.parse(options.orderObj) : options
    orderObj.orderNo && this.setData({
      orderNo: orderObj.orderNo
    })
    // 禁止右上角分享
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },
  onShow(){
    this.data.orderNo&&this.setOrderDetail()
  },
  onHide: function () {
    this.destroyCountDownTimer()
  },

  onUnload: function () {
    this.destroyCountDownTimer()
  },
  /**
   * 摧毁倒计时定时器
   */
  destroyCountDownTimer() {
    this.data.countDown && this.data.countDown.stop()
  },

  /**
   * 获取订单详情
   * @returns
   */
  async getOrderDetail() {
    const { userID:customerID = -1 } = wx.getStorageSync('user')
    const params = {
      customerID,
      orderNo: this.data.orderNo,
      optionalInfo: ['timeline', 'receiver', 'store', 'payment', 'comment', 'invoice', 'userHide', 'checkNoReasonRefund', 'refund', 'preferential']
    }
    return app.api.getRelayOrderDetail(params)
  },

  /**
   * 设置页面标题及描述
   * @param {*} orderDetail 订单详情
   */
  setPageTitleAndDesc(orderDetail) {
    const { orderStatus, cancelType } = orderDetail

    //  订单状态配置
    const STATUS_CONFIG = {
      [STATUS_CODE.OK]: {
        title: '交易成功',
        desc: '感谢您对百果园的信任，欢迎下次光临！',
        className: 'success'
      },
      [STATUS_CODE.CANCEL]: {
        title: '已取消',
        desc: '订单未支付，已自动为您取消订单',
        className: 'cancel'
      },
      [STATUS_CODE.UN_PAYMENT]: {
        title: '待付款',
        className: 'pending'
      },
      [STATUS_CODE.DISPATCHING]: {
        title: '待自提',
        desc: '*如您未及时自提，系统将自动取消订单并退款',
        className: 'pending'
      },
      [STATUS_CODE.REFUNDING_VAL]: {
        title: '退款中',
        desc: '退款将会在1-7个工作日内退回您的原支付',
        className: 'success'
      }
    }

    const config = STATUS_CONFIG[orderStatus]

    const {
      title,
      className
    } = config

    let desc = config.desc

    //  取消订单需要判断取消类型进行展示不同内容
    if (orderStatus === STATUS_CODE.CANCEL) {
      desc = {
        1: '订单未支付，已自动为您取消订单',
        2: '您已取消订单',
        3: '订单已取消，支付金额已原路退回支付账户'
      }[cancelType]
    }

    this.setData({
      statusTitle: title,
      statusDesc: desc,
      statusClassName: className
    })
  },

  /**
   * @todo 后续要统一成和及时达/全国送等按钮处理方法
   */
  setBtnShowLogic(orderDetail) {
    const {
      isSuccessStatus,
      isPendingStatus,
      isWaitingCollectionStatus,
      leftBtnList
    } = this.data
    const showBtn = {
      leftBtnList: Boolean(leftBtnList.length),
      isCanInvoice: isSuccessStatus && orderDetail.isCanInvoice,
      isMiniCanShowInvoice: isSuccessStatus && orderDetail.isMiniCanShowInvoice,
      cancelOrder: isPendingStatus || isWaitingCollectionStatus,
      payOrder: isPendingStatus
    }

    this.setData({
      showBtn,
      hasFooter: Boolean(Object.values(showBtn).filter(Boolean).length)
    });
  },

  /**
   * 设置左侧按钮列表
   * @param {*} orderDetail 订单信息
   */
   setLeftBtnList(orderDetail) {
    const leftBtnList = [];

    if (orderDetail.isCanShowHideBtn) {
      leftBtnList.push({
        label: '删除订单',
        callback: this.deleteOrder
      })
    }

    this.setData({
      leftBtnList
    })
  },

  /**
   * 设置详情状态
   */
  async setOrderDetail() {
    const { data: orderDetail, systemTime } = await this.getOrderDetail()
    this._data.finishTime = orderDetail.finishTime
    this._data.offSetTime = systemTime - new Date().getTime()
    //  对items字段的商品列表匹配快照详情，并追加属性
    await mapSnapShotDetail([orderDetail])
    const { orderStatus, store, items, mixOrderList, timeLineList, pickUpCode } = orderDetail

    //  待提货状态，设置提货码
    if (orderStatus === STATUS_CODE.DISPATCHING) {
      this.getBarcodeImg(pickUpCode)
    }
    //  待支付，设置倒计时
    if (orderStatus === STATUS_CODE.UN_PAYMENT) {
      this.setCountDown(orderDetail.payment.logicalExpirationTime, systemTime)
    }
    //  存在倒计时，清空
    else if (this.data.countDown) {
      this.data.countDown.stop()
      this.setData({
        countDown: null
      })
    }

    //  设置页面标题及描述
    this.setPageTitleAndDesc(orderDetail)

    // 获取退款信息
    this.getRefundInfo(orderDetail)

    //  判断订单详情页是否需要展示文案
    this.setNoInvoiceDesc(orderDetail)
    /**
     * 判断目标状态是否为订单状态
     * @param {*} target
     */
    const isStatus = target => orderStatus === target

    // 获取商品单价，取接龙价
    items.forEach(item => {
      item.goodsName = handleGoodsName(item)
      item.price = item.goodsPricePer
      // 管理台可能把活动删除,导致goodsPicture为undefined
      item.goodsPicture = toHttps(item.goodsPicture)
      item.btnText = '售后详情'
      // 如果有退款信息 则设置按钮文本
      if (item.refundInfo) {
        // 有4种情况 分为
        // 1.未申请 2.退款中 3.退款成功 4.退款失败
        if (['退款中', '审核中'].includes(item.afterSaleInfo.title)) {
          item.btnText = '退款中'
        }
        if (item.afterSaleInfo.title === '退款成功') {
          item.btnText = `已退款 ¥${formatPrice(item.afterSaleInfo.refundMoney)}`
        }
        if (item.afterSaleInfo.title === '退款失败') {
          item.btnText = '退款失败'
        }
        if (item.afterSaleInfo.title === '退款关闭') {
          item.btnText = '退款关闭'
        }
      }
    })

    const {
      city_name, // 市
      county_name, // 县/区
      address2 // 详细地址
    } = store

    Object.assign(store,{
      storeBusinessTime:getStoreBusinessTime({
        startTime:store.startTime||'',
        endTime:store.endTime||'',
        openingTime:store.openingTime?`${store.openingTime}-${store.closingTime}`:''
      })
    })

    this.setReceiveList(orderDetail)
    const mixGroupGoodsList = this.setMixGroupGoodsList(orderStatus, mixOrderList, items)
    this.setData({
      orderDetail,

      store,
      storeAddress: [city_name, county_name, address2].join(''),
      goodsList: items,
      mixGroupGoodsList,
      timeLineList,

      isSuccessStatus: isStatus(STATUS_CODE.OK),
      isCancelStatus: isStatus(STATUS_CODE.CANCEL),
      isPendingStatus: isStatus(STATUS_CODE.UN_PAYMENT),
      isWaitingCollectionStatus: isStatus(STATUS_CODE.DISPATCHING)
    })

    //  设置左侧按钮列表
    this.setLeftBtnList(orderDetail)
    this.setBtnShowLogic(orderDetail)

    // 请求一次接龙订单数量和记录
    if(this.data.reqRecordsList&&orderStatus === STATUS_CODE.DISPATCHING){
      this.getOrderRecordsNums(orderDetail)
      this.setData({
        reqRecordsList:false
      })
    }

    // 进入订单详情页
    const { alias_name = '', storeCode = '', storeID = '' } = this.data.store
    sensors.pageScreenView({
      storeName: alias_name,
      storeNum: storeCode,
      storeAddress: this.data.storeAddress,
      storeID
    })
  },

  /**
   * 设置订单可展示的信息列表
   * @param {*} order
   */
   setReceiveList(order) {
    const orderStatus = order.orderStatus
    let list = ['自提门店', '营业时间', '提货时间']

    // 取消的订单不展示提货时间
    if ([STATUS_CODE.CANCEL].includes(orderStatus)) {
      list = ['自提门店', '营业时间']
    }
    // 未支付的订单 且 是混合订单，不展示总的提货时间
    else if ([STATUS_CODE.UN_PAYMENT].includes(orderStatus) && order.mixOrderList && order.mixOrderList.length > 0) {
      list = ['自提门店', '营业时间']
    }
    // 已完成的订单不展示营业时间
    else if (orderStatus === STATUS_CODE.OK) {
      list = ['自提门店', '提货时间']
    }

    this.setData({
      receiveList: list
    })
  },

  /**
   * 获取条形码图片
   */
   getBarcodeImg(pickUpCode) {
    const that = this
    let failAgainRequest = false
    globalPickupCode = pickUpCode
    const getCanvasImage = () => {
      wxbarcode.then((module) => module.barcode('canvasbarcode', pickUpCode, 600, 175, () => {
        setTimeout(() => {
          // 判断是否是当前的条形码
          if (pickUpCode !== globalPickupCode) return
          wx.canvasToTempFilePath({
            canvasId: 'canvasbarcode',
            success: (res) => {
              that.setData({
                barcodeImgSrc: res.tempFilePath
              })
            },
            fail: () => {
              // 华为手机偶现条形码加载失败，如果失败，重新绘制一次
              if (!failAgainRequest) {
                failAgainRequest = true
                getCanvasImage()
              }
            }
          }, this)
        }, 1000)
      }))
    }
    getCanvasImage()
  },
  /**
   * 待支付订单倒计时
   * @param {*} expireTime 过期时间
   * @param {*} systemTime 系统时间（当前时间）
   * @returns
   */
  setCountDown(expireTime, systemTime) {

    const countDown = new CountDown({
      expireTime,
      systemTime,
      callback: ({ time }) => {
        this.setData({
          statusDesc: `付款剩余时间：${time}`
        })
      },
      finishCallback: () => {
        wx.showToast({
          title: '超过15分钟未支付，订单已取消',
          icon: 'none',
          duration: 2000,
          mask: true,
          success: () => {
            setTimeout(() => {
              this.countDownFinishCb()
            }, 2000)
          }
        })
      }
    })

    this.setData({
      countDown
    })
  },

  /**
   * 倒计时结束
   */
  async countDownFinishCb() {
    try {
      await this.cancelUnPayOrder(110)
      this.refreshOrder(0)
    } catch (error) {
      //  可能存在订单已取消的情况，还需要刷新
      this.refreshOrder(0)
    }
  },

  /**
   * 复制订单编号
   */
  copyOrderNo() {
    const { orderNo } = this.data.orderDetail

    wx.setClipboardData({
      data: orderNo,
      success() {
        wx.showToast({
          title: '复制成功',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 导航到店
   */
  navigateStore() {
    const { isClosed, longitude, latitude, short_name, store_name, address } = this.data.store
    // 如果已经闭店了 则无须跳转
    if (isClosed) {
      return
    }
    openNavigate({
      lon: longitude,
      lat: latitude,
      address,
      name: short_name || store_name
    })
    // 导航到店
    sensors.trackClickEvent(relayOrdeDetailrMap.NAV_STORE)
  },

  /**
   * 联系商家
   */
  concatStore() {
    let phoneNumber
    const {
      storePhone, phone, telephone, mobilephone
    } = this.data.store
    phoneNumber = storePhone || phone || telephone || mobilephone || '4001811212'

    wx.makePhoneCall({
      phoneNumber
    })
    // 联系商家
    sensors.trackClickEvent(relayOrdeDetailrMap.CALL_STORE)
  },

  /**
   * 开具发票
   */
   async invoiceOrder() {
    const orderDetail = this.data.orderDetail
    const { data } = await app.api.getOrderInvoiceInfo({
      orderNos: [orderDetail.orderNo]
    })

    const { canUseInvoiceAmount } = data[0]

    //  携带参数跳转到对应页面
    const query = JSON.stringify({
      //  订单类型
      orderType: ORDER_TYPE.RELAY,
      //  开票金额
      drawableAmount: canUseInvoiceAmount,
      //  开票订单数
      orderQuantity: 1
    });
    //  设置开具发票页使用的开票参数
    wx.setStorageSync('createInvoice_data', {
      selectedData: [
        {
          orderChannel: 'O2O',
          orgCode: orderDetail.orgCode,
          channelOrderNo: orderDetail.orderNo,
          drawableAmount: canUseInvoiceAmount,
          finishTime: orderDetail.finishTime
        }
      ]
    })

    wx.navigateTo({
      url: '/userB/pages/invoice/createInvoice/index?pageParam=' + query,
    });
    // 开具发票
    sensors.trackClickEvent(relayOrdeDetailrMap.DRAW_BILL)
  },
  /**
   * 加载完不满意原因数据后再弹窗
   */
  reasonLoaded(e) {
    wx.hideLoading()
    this.setData({
      showReasonConfirmModal: true
    })
  },
  /**
   * 跳转到三无退货页面(及时达/全国送/门店订单)
   */
  goRefund() {
    const params = this.getRefundPageParams()
    pageTransfer.send(params)
    wx.navigateTo({
      url: '/userB/pages/selfSupportComplaints/goodsDissatisfy/index',
    })
  },
  /**
   * 点击三无退货按钮
   */
  returnGoods: app.subProtocolValid('shop',async function (e) {
    const { goods: refundGoods, index } = e.currentTarget.dataset
    this._data.clickIndex = index
    const { isNext, lat, lon } = await locateService.beforeRefundCheckLocate()
    if (!isNext) {
      return
    }
    // 三无退货
    sensors.trackClickEvent({ 
      ...relayOrdeDetailrMap.RETURN_GOODS,
      longitu: lon,
      latitu: lat,
    })

    // 获取订单最高可退金额
    const maxRefundAmount = refundGoods.maxRefundAmount || refundGoods.orderMaxRefundAmount

    //  商品的最大可退金额为0
    const goodsMaxRefundAmount = Number(maxRefundAmount)
    if (!goodsMaxRefundAmount || goodsMaxRefundAmount === 0) {
      wx.showToast({
        title: '该商品优惠后实付金额为0，无可退金额啦~',
        icon: 'none',
        duration: 1000
      })
      return
    }
    this._data.clickRefundGoods = refundGoods
    // 如果已经加载过数据，直接弹窗
    if (this.data.visibleReasonConfirmModal) {
      this.setData({
        showReasonConfirmModal: true
      })
      return
    }
    wx.showLoading()
    // 否则就等待加载数据完成后弹窗
    this.setData({
      visibleReasonConfirmModal: true
    })
  }),

  getRefundPageParams() {
    // 拼接一波参数
    const { store = {}, orderDetail = {} } = this.data
    const { payment } = orderDetail
    const { customer, primaryReasonIndex, reasonIndex } = this._data
    const refundGoods = { ...this._data.clickRefundGoods }

    // 这里需要取快照数据
    refundGoods.headPic = refundGoods.goodsPicture

    const isOnlyWalletPay = !payment.hasThirdPartyPay
    const params = {
      complaintType: '1',
      refundGoods,
      orderType: '4',
      createSource: refundGoods.createSource,
      contact: store.storePhone || store.phone || store.telephone || store.mobilephone || '4001811212',
      customer: customer,
      primaryReasonIndex: primaryReasonIndex,
      reasonIndex
    }
    if (isOnlyWalletPay) {
      params.isOnlyWalletPay = true
    }
    return params
  },
  /**
   * 点击取消订单按钮
   */
  cancelOrder() {
    const { orderStatus } = this.data.orderDetail
    //  未支付订单
    if (orderStatus === STATUS_CODE.UN_PAYMENT) {
      this.beforeCancelUnPayOrder()
    }
    //  已支付订单，打开取消原因弹窗
    else {
      this.setData({
        orderCancelIsShow: true
      })
    }
    // 取消订单
    sensors.trackClickEvent(relayOrdeDetailrMap.CANCEL_ORDER)
  },

  /**
   * 取消订单选择弹窗——确认选择回调
   * @param {*} params 回调参数
   */
  onOrderCancelSelected(params) {
    //  询问用户是否接收订阅消息，只有在用户点击以及支付成功回调中调起，退款通知
    app.requestSubscribeMessage({
      tmplIds: ['jLaaeFpXwM90AMJrCpS63A4A1tBH3zT7tVHleo621gE'],
    }, () => {
      this.refundOrder(params)
    })
  },
  // 发票详情
  invoiceDetailBtnHandle() {
    const orderDetail = this.data.orderDetail
    //  携带参数跳转到对应页面
    const query = JSON.stringify({
      //  及时达/全国送 订单渠道为 O2O
      orderChannel: 'O2O',
      channelOrderNo: orderDetail.orderNo
    });


    wx.navigateTo({
      url: '/userB/pages/invoice/invoiceDetail/index?pageParam=' + query,
    });
  },
  handleOnlineService () {
    this.setData({
      showCustomConfirmModal: false
    })
    app.toOnlineService({ queStr: '发票服务' })
  },
  // 关闭自定义弹窗
  customConfirmModalCancel () {
    this.setData({
      showCustomConfirmModal: false
    })
  },
  /**
   * 取消已支付订单
   * @param {*} e
   */
  async refundOrder(e) {
    const { user, orderDetail } = this.data
    const {
      orderNo,
      payment,
      createSource
    } = orderDetail
    const { totalPrice } = payment
    const { userID:customerID = -1 } = wx.getStorageSync('user')
    const params = {
      createSource,
      customerID
    }

    // 神策防黑产埋点
    sensors.safeGuardSensor('refund', { orderNo, orderType: 'RELAY' })
    const {
      cancelReason
    } = e.detail

    Object.assign(params, {
      // 订单号
      subOrderNo: orderNo,
      // 退款类型
      refundType: '1',
      // 退款模式
      refundModel: '5',
      // 退款原因
      reason: cancelReason,
      // 售中取112,售后取110(即三无退),小程序默认112
      refundStatus: 112,
      // 退款额，即实际支付金额
      refundMoney: totalPrice,
      // 发起方
      launchType: 2,
      customerComplaintType: 0,
      refundTradeChannelNum: 10001
    })

    try {
      const {
        errorCode,
        description
      } = await app.api.cancelPaidOrder(params)

      //  请求失败
      if (Number(errorCode) !== 0) {
        wx.showToast({
          title: description,
          icon: 'none',
          duration: 3000
        })
        wx.hideLoading()
        return
      }

      //  关闭取消订单弹窗
      this.onOrderCancelClosed()

      this.orderCancelSuccess()
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: error.description,
        icon: 'none',
        duration: 3000
      })
    }
  },

  /**
   * 取消订单选择弹窗——关闭回调
   */
  onOrderCancelClosed() {
    this.setData({
      orderCancelIsShow: false
    })
  },

  /**
   * 取消未支付订单，弹出确认取消弹窗（及时达：orderCancelPaiedFailed）
   */
  async beforeCancelUnPayOrder() {
    //  取消订单
    const showModalRes = await app.showModalPromise({
      content: '您精挑细选的商品真的要取消吗？',
      showCancel: true,
      confirmText: '残忍取消',
      cancelText: '考虑一下',
    })
    //  弹窗取消，此处可以进行行为上报
    if (!showModalRes) {
      return
    }

    // 点击“确定”操作
    wx.showLoading({
      title: '取消中',
      mask: 'true'
    })

    try {
      await this.cancelUnPayOrder(100)
      wx.hideLoading()
      // 请求成功
      this.orderCancelSuccess()

      //  此处可以进行上报行为

    } catch (errorInfo) {
      wx.hideLoading()
      const errorType = Object.prototype.toString.call(errorInfo)
      if (/error/i.test(errorType)) {
      } else {
        commonObj.showModal('提示', errorInfo.description, false, '我知道了')
      }
    }
  },

  /**
   * 取消未支付的订单
   * @param { number } operate
   * @returns
   */
   async cancelUnPayOrder(operate) {
    const { orderDetail } = this.data
    const { attachOrderNo, orderNo: subOrderNo } = orderDetail
    const { userID:customerID = -1 } = wx.getStorageSync('user')
    const params = {
      customerID,
      subOrderNo: (attachOrderNo && attachOrderNo !== '-') ? attachOrderNo : subOrderNo,
      operate,
      paramMap: {
        operatorCode: customerID,
        launchType: '2'
      }
    }

    try {
      //  调用取消订单接口
      await app.api.cancelUnpaidOrder(params)
      return Promise.resolve()
    } catch (error) {
      //  订单异常情况
      return Promise.reject(error)
    }
  },

  /**
   * 订单取消成功，弹出成功提醒
   */
  orderCancelSuccess() {
    wx.showToast({
      title: '订单取消成功',
      icon: 'success',
      duration: 2000,
      mask: true,
      success: () => {
        this.refreshOrder(1000)
      }
    })
  },

  /**
   * 刷新订单页
   * @param { number } timeout
   * @returns
   */
  refreshOrder(timeout = 2000) {
    const pageStack = getCurrentPages()
    const lastPage = pageStack[pageStack.length - 1]

    if (!lastPage) {
      return
    }
    //  延时执行时要判断是否在当前页面
    setTimeout(() => {
      if (lastPage.route === 'relay/pages/orderDetail/index') {
        this.setOrderDetail()
      }
    }, timeout)
  },

  /**
   * 接龙删除订单
   * @Todo
   * 因为现在订单均慢慢接入中台
   * 按钮显示逻辑/按钮点击逻辑可以抽离到一文件中统一处理。可以在之前的 orderBtn.js 基础上进行优化
   */
  async deleteOrder() {
    const orderDetail = this.data.orderDetail;

    const showModalRes = await app.showModalPromise({
      title: '确认删除订单吗？',
      content: '订单删除后您无法查看该笔订单，也无法对该笔订单开具发票和评价。',
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消'
    })
    //  弹窗取消，此处可以进行行为上报
    if (!showModalRes) {
      return
    }

    const { userID: customerID = -1 } = wx.getStorageSync('user');

    const data = {
      orderType: orderDetail.orderType,
      customerID,
      orderNoList: [orderDetail.orderNo]
    }

    try {
      await app.api.updateUserHide(data);

      const pages = getCurrentPages();
      const orederListPageName = 'userB/pages/orderList/index';
      const prevPage = pages[pages.length - 2]
      //  上个页面为订单列表页
      if (prevPage.route === orederListPageName) {
        prevPage.setData({ mix_updateOrder_singleUpdateDelete: true })
        wx.navigateBack();
      } else {
        // 去订单列表页
        wx.redirectTo({
          url: `/${orederListPageName}?type=D`
        });
      }
    } catch (error) {
      wx.showModal({
        content: '订单删除失败'
      })
    }
  },

  /**
   * 点击立即支付按钮
   */
  payOrder() {
    const orderDetail = this.data.orderDetail
    //  获取账户余额
    this.getWalletAmount(orderDetail.payment.totalPrice)
    this.setData({
      paymentDialog: true,
      target: {
        payAmount: Number((orderDetail.payment.totalPrice / 100).toFixed(2))
      }
    })
    this._data.curOrderInfo = orderDetail
    // 立即付款
    sensors.trackClickEvent(relayOrdeDetailrMap.HANDLE_PAY)
  },

  /**
   * 获取余额
   */
  async getWalletAmount(originPayAmount) {
    const { orderDetail } = this.data
    const { store = {} } = orderDetail
    const { userID:customerID = -1 } = wx.getStorageSync('user')
    try {
      const { data: resData } = await app.api.getBalanceNew({
        customerID,
        isNeedBalance: true
      })
      const {
        walletAmount = 0
      } = resData
      const totalAmount = walletAmount
      const lack = originPayAmount > totalAmount
      this.setData({
        lack: lack,
        pagodaMoney: totalAmount !== 0,
        selectWxPay: lack,
        selectUnionPay: false,
        mainBalance: Number(totalAmount)
      })
      lack && this.rechargeGuide(store.id, originPayAmount, customerID)
    } catch (error) {
      console.log('getWalletAmount', error);
      wx.showModal({
        content: '钱包余额获取失败'
      })
    }
  },

  /**
   * 获取充值活动信息
   */
  rechargeGuide(storeID, amount, userID) {
    const param = {
      storeID,
      amount,
      customerID: userID,
    }
    app.api.rechargeGuide(param).then(res => {
      const rechargeText = res.data.depositText
      this.setData({
        rechargeText
      })
    }).catch((res) => {})
  },
  // 设置微信支付
  setWaySelectHandle(e) {
    const { selectWxPay, selectUnionPay } = e.detail
    this.setData({
      selectWxPay,
      selectUnionPay,
    });
  },
  /**
   * 支付
   */
  payrightNow: util.debounce(async function() {
    const { pagodaMoney, selectWxPay, selectUnionPay, mainBalance } = this.data
    const { payment, orderTradeNo, orderNo } = this._data.curOrderInfo

    const confirmHandle = () => {
      const payInfo = {
        usePagodaPay: pagodaMoney,
        useWxPay: selectWxPay,
        useUnionPay: selectUnionPay,
        paymentAmount: payment.totalPrice,
        tradeNo: payment.tradeNo,
        mainOrderNo: orderTradeNo,
        mainBalance
      }
      const succCb = this.paySuccCallback.bind(this, this._data.curOrderInfo)
      const failCb = this.failCallback.bind(this, this._data.curOrderInfo)
      const extraInfo = {
        succCb,
        failCb
      }
      fruitOrderPay.handlePay(payInfo, extraInfo)
      sensors.safeGuardSensor('pay', { orderNo, orderType: 'RELAY' })
    }

    if (pagodaMoney && !(selectWxPay || selectUnionPay)) {
      // 校验是否需要进入验证码环节
      const isNeedValidate = await this.checkPayDevice()

      if (!isNeedValidate) {
        // 缓存一下提交订单的请求
        this._data.tmpConfirmHandle = confirmHandle
        // 弹出验证码输入框
        this.showSmsValidate()
        return
      }
      const showModalRes = await app.showModalPromise({
        content: `确认使用会员钱包支付${Number((payment.totalPrice / 100).toFixed(2))}元吗？`,
        showCancel: true,
        confirmText: '确认',
        cancelText: '取消',
      })
      if (showModalRes) {
        confirmHandle()
      }
      return
    }
    confirmHandle()
  }, 300),

  /**
   * 支付成功回调
   */
  paySuccCallback (orderInfo, payData) {
    const { data: { payNo, payNos } } = payData
    const { orderTradeNo: mainOrderNo, store = {}, startDispatchTime, endDispatchTime, preferentialList = [], items = [], mixOrderList = [] } = orderInfo
    let pickupStart = startDispatchTime
    let pickupEnd = endDispatchTime
    // 混合单，取最早开始时间和最晚结束时间
    if (mixOrderList.length) {
      // 获取所有活动开始结束时间
      const arrStartTime = []
      const arrEndTime = []
      mixOrderList.forEach(item => {
        arrStartTime.push(item.startDispatchTime)
        arrEndTime.push(item.endDispatchTime)
      })
      // 排序开始时间 第一个最早
      arrStartTime.sort((a,b)=>{
        return new Date(a.replace(/-/g, '/')).getTime() - new Date(b.replace(/-/g, '/')).getTime()
      })
      // 排序结束时间 第一个最晚
      arrEndTime.sort((a,b)=>{
        return new Date(b.replace(/-/g, '/')).getTime() - new Date(a.replace(/-/g, '/')).getTime()
      })
      pickupStart = arrStartTime[0]
      pickupEnd = arrEndTime[0]
    }
    const activityCode = this.getOrderFirstItemActivityCode(orderInfo)
    const orderDetailObj = {
      customerID: app.globalData.customerID || -1,
      mainOrderNo,
      isNotMixOrder: mixOrderList.length === 0,
      payNo,
      payNos,
      storeName: store.shortName || store.storeName || store.alias_name || store.store_name,
      storeCode: store.storeCode || store.store_code,
      pickupStart,
      pickupEnd,
      // 如果外层没有返回活动编码，则取preferentialList的第一项活动编码
      activityCode: activityCode || preferentialList[0]?.preferentialCode,
      shareImg: items[0].goodsPicture||items[0].headPic,
      goodsCount:items.reduce((sum,item)=>{
        return sum + item.quantity
      },0)
    }
    pageTransfer.send(orderDetailObj)
    // 支付成功跳转
    wx.redirectTo({
      url: '/relay/pages/waitPay/index'
    })
  },

  failCallback (orderData, failReason) {
    console.log('failReason', orderData, failReason);
    const { payType, err } = failReason
    if (fruitOrderPay.isThirdPay(payType, { excludeMixinPay: true })) {
      if (err.errMsg.indexOf('cancel') === -1) {
        // 非取消支付引起的支付失败
        wx.showToast({
          title: err.errMsg || '支付失败，稍后再试',
          icon: 'loading',
          duration: 2000,
          mask: true
        })
        const { phoneNumber } = wx.getStorageSync('user') || {}
        log.error('useWXPayFailed', err, phoneNumber)
      }
    } else if (fruitOrderPay.isPagodaPay(payType, { includeMixinPay: true })) {
      wx.showToast({
        title: '支付失败，请重试',
        duration: 2000,
        icon: 'none'
      })
    }
  },

  /**
   * 关闭支付弹窗
   */
  hidePaymentWay() {
    this.setData({
      paymentDialog: false
    })
  },

  /**
   * 选择支付弹窗
   */
  stopBubbling(){
    return
  },

  /**
   * 获取退款信息
   */
  getRefundInfo(orderInfo){
    const info = refundInfo(orderInfo)
    const { desc:noReasonRefundDesc='',exceedRefundTimeDesc='',differenceRefundMoney=''} = info
    this.setData({
      noReasonRefundDesc,//三无退货说明
      differenceRefundMoney,//差额退说明
      exceedRefundTimeDesc //售后状态
    })
  },
  /**
   * 获取订单记录和数量
   */
   async getOrderRecordsNums(orderObj){
    const activityCode = this.getOrderFirstItemActivityCode(orderObj)
    const { userID:customerID = -1 } = wx.getStorageSync('user') || {};
    const params = {
      activityCode,
      count:3,
      nextStartTime:'',
      customerID
    }
    const initialNum = this.data.goodsList.reduce((sum,item)=>{
      return sum + item.quantity
    },0)
    try {
      const { data: {recordsList = [],actTotalAmountArray = {} } = {}} = await app.api.getOrderRecordsNums(params)
      // 过滤掉当前订单
      const list = recordsList.filter(item=>item.orderNo!==this.data.orderNo).slice(0,2)
      this.setData({
        recordsList:list,
        goodTotalAmount:actTotalAmountArray[activityCode]||initialNum
      })
      // 生成分享图
      this.getShareImgHandle()
    } catch (error) {
      this.setData({
        recordsList:[],
        goodTotalAmount:initialNum
      })
    }
  },
  /**
   * 商品头图
   */
   getDetailHeadPic: function() {
    const { goodsPicture:shareImg = ''} = this.data.goodsList[0]
    return getCanvasLocalImage(shareImg, 'goodPic')
  },
  /**
   * 转换头像
   */
  getAvatarImg: function(){
    const { avatarUrl } = wx.getStorageSync('userNameAndImg') || {}
    return getCanvasLocalImage(avatarUrl||'https://resource.pagoda.com.cn/group1/M21/54/64/CmiLkGD5TyKASB85AAAn3XJoLPU505.png', 'avatarImg')
  },
  /**
   * 转换背景
   */
  getBgImg: function(){
    return getCanvasLocalImage('https://resource.pagoda.com.cn/group1/M21/92/A6/CmiWa2KMUAKAehvLAAECsLKxyfE244.png', 'bgPic')
  },
  /**
   * 绘制分享图片钱缓存本地图片
   */
  getShareImgHandle(){
    const that = this
    const p = [
      that.getAvatarImg(),//用户头像
      that.getDetailHeadPic(),//商品头图
      that.getBgImg()//背景
    ]
    that.data.recordsList.forEach((item,index)=>{
      p.push(getCanvasLocalImage(item.userInfo.icon||'',  `recordAvatar${index}`))
    })
    Promise.all(p).then(res => {
      // 缓存本地图片
      res.forEach(item=>{
        that._data.storeImageStatus[item.key] = item.path
      })
      const params = {
        storeImageStatus:that._data.storeImageStatus,
        goodsAmount:that.data.goodsList.reduce((sum,item)=>{
          return sum + item.quantity
        },0),
        goodTotalAmount:that.data.goodTotalAmount,
        recordsList:that.data.recordsList
      }
      // 绘制分享图
      drawSharePic(params)
      // 保存图片
      setTimeout(() => {
        that.canvasToTempFilePath()
      }, 300)
    }).catch( error => {
      console.log('绘制分享图片失败',error)
      // 绘制失败，重新绘制一次
      if(!this._data.drawStatus){
        that.getShareImgHandle()
        this._data.drawStatus = true
      }
    })
  },

  /**
   * 保存图片
   */
   canvasToTempFilePath () {
    const that = this
    wx.canvasToTempFilePath({
      x: 0,
      y: 0,
      width: 496,
      height: 397,
      quality: 1,
      canvasId: 'sharePicCanvas',
      success: function (res) {
        that.setData({
          sharePic:res.tempFilePath
        })
      },
      fail: function (res) {
      }
    })
  },
  /**
   * 分享
   */
  onShareAppMessage() {
    // 上报神策埋点
    const sensorReporData = relayOrdeDetailrMap.ORDER_SHARE
    sensorReporData && sensors.trackClickEvent(sensorReporData)

    const activityCode = this.getOrderFirstItemActivityCode(this.data.orderDetail)

    const { storeCode } = this.data.store
    const params = {
      title: '这是我买的商品，店长记得帮我留一下哟~ ',
      path:`/pages/homeDelivery/index?to=relayDetail&activityCode=${
        activityCode
      }&storeCode=${
        storeCode || ''
      }&fromSharePage=orderDetail`,
      imageUrl: this.data.sharePic,
    };
    return params;
  },
  onPageScroll(e) {
    const { scrollTop } = e
    if(scrollTop>=300){
      this.setData({
        needBorderRadius:false
      })
    }else{
      this.setData({
        needBorderRadius:true
      })
    }

    this.setNavBarStyleOnScroll(e)
  },

  /**
   * 通过子组件事件，获取自定义导航栏高度
   * @param {*} ev
   */
  getNavBarHeight (ev) {
    // 这里的高度单位是px
    this.setData({
      navBarHeight: ev.detail
    })
  },

  /**
   * 设置顶部栏样式
   * @param {*} event
   * @returns
   */
  setNavBarStyleOnScroll (event) {
    const { scrollTop } = event
    const { navBarHeight = 0 } = this.data
    const ratio = Math.min(scrollTop / (navBarHeight/2), 1)

    if (this._data.ratio === ratio) {
      return
    }
    this._data.ratio = ratio

    if (ratio <= 0) {
      //  没有滚动距离，顶部栏背景设置透明
      this.setData({
        navBarBgColor: 'transparent',
        navBarColor: '#fff',
        backFilter: 1
      })
    }
    //  滚动到下方，设置白色背景
    else {
      this.setData({
        navBarBgColor: `rgba(255,255,255, ${ratio})`,
        navBarColor: '#222222',
        backFilter: 1 - ratio
      })
    }
  },
  /**
   * 跳转到三无退详情页
   * @param {Object} e event事件
   * @param {Boolean} isRedirect 是否是关闭当前页面并跳转
   */
  goDetailHandle(e, isRedirect) {
    const { goods: refundGood, index } = e.currentTarget.dataset
    this._data.clickRefundGoods = refundGood
    this._data.clickIndex = index
    // 详情页显示的数据通用，这里取快照数据进行转换
    refundGood.headPic = refundGood.goodsPicture
    // 如果详情页需要直接跳转到三无退申请页 这里赋值参数
    const refundPageParams = this.getRefundPageParams()
    pageTransfer.send({
      refundGood,
      orderType: 4,
      finishTime: this._data.finishTime,
      offSetTime: this._data.offSetTime,
      // 三无退申请页的参数
      refundPageParams
    })
    wx[isRedirect ? 'redirectTo' : 'navigateTo']({
      url: '/userB/pages/selfSupportComplaints/complaintsDetail/index',
    })
  },
  /**
   * 该方法被三无退结果页跨页面调用（接龙）
   * 根据用户点击对应商品跳转到三无退详情页
   */
  async checkRefundDetail() {
    // 刷新页面
    await this.setOrderDetail()
    // 等待data赋值完成后执行跳转
    const goods = this.data.goodsList[this._data.clickIndex]
    this.goDetailHandle({
      currentTarget: {
        dataset: {
          goods
        }
      }
    }, true)
  },

  setNoInvoiceDesc(orderDetail){
    const paymentList = orderDetail.payment.paymentList
    if(!paymentList.length) {
      return
    }
    //  支付方式
    const {
      isPagodaPay,
    } = paymentList.reduce(function(res, item) {
      if (item.payChannel === 'M') { // 会员钱包支付
        res.isPagodaPay = true
      }
      return res
    }, {
      isPagodaPay: false,
    })
    this.setData({
      // 混合支付时,用了第三方支付方式(如微信,云闪付)都能开票
      // 不是混合支付(paymentList.length === 1)时,只有会员钱包支付时不能开票
      noInvoiceDesc: paymentList.length === 1 && isPagodaPay ? '*该订单使用钱包余额支付，不可开票' : ''
    })
  },
  // 余额支付开关
  switchPagodaPayChangeHandle(e) {
    const { selectPagodaPay } = e.detail
    this.setData({
      pagodaMoney: selectPagodaPay,
    })
  },
  /**
   * 不满意原因确认弹窗确认回调
   */
  reasonConfirmModalSubmit(event) {
    const detail = event.detail
    const {
      primaryReasonIndex,
      reasonIndex
    } = detail

    this._data.primaryReasonIndex = primaryReasonIndex
    this._data.reasonIndex = reasonIndex
    this.closeReasonConfirmModal()
    this.goRefund()
  },
  /**
   * 关闭不满意原因确认弹窗
   */
  closeReasonConfirmModal() {
    this.setData({
      showReasonConfirmModal: false
    })
  },
  /**
   * 设置混合单商品列表
   */
  setMixGroupGoodsList(orderStatus, mixOrderList, items) {
    if (!Array.isArray(mixOrderList) || !mixOrderList.length) {
      return []
    }
    const mixGroupGoodsList = []
    mixOrderList.forEach(order => {
      const {
        items: orderGoodsList = []
      } = order
      const currentOrderGoodsList = []
      orderGoodsList.forEach(orderGood => {
        const targetGood = items.find(item => item.goodsSn === orderGood.goodsSn)
        currentOrderGoodsList.push(targetGood)
      })
      // 已取消的订单不再展示提货时间
      const pickupTimeStr = orderStatus === STATUS_CODE.CANCEL ? '' : handlePickupTime(order.startDispatchTime, order.endDispatchTime)
      mixGroupGoodsList.push({
        pickupStart: order.startDispatchTime,
        pickupEnd: order.endDispatchTime,
        pickupTimeStr,
        goodsList: currentOrderGoodsList
      })
    })
    // 按照提货时间进行先后排序
    return mixGroupGoodsList.sort((a, b) => {
      return new Date(a.pickupEnd.replace(/-/g, '/')).getTime() - new Date(b.pickupEnd.replace(/-/g, '/')).getTime()
    }).sort((a, b) => {
      return new Date(a.pickupStart.replace(/-/g, '/')).getTime() - new Date(b.pickupStart.replace(/-/g, '/')).getTime()
    })
  },
  /**
   * 获取第一个商品的活动编码
   * @param {Object} orderDetail 订单详情
   * @returns {String} 活动的编码
   */
  getOrderFirstItemActivityCode(orderDetail) {
    const { activityCode: randomActivityCode, items = [], preferentialList = [] } = orderDetail
    let activityCode = randomActivityCode
    // 如果是多个活动的订单 则取第一个商品的活动信息
    if (preferentialList) {
      activityCode = preferentialList[0].preferentialCode
      if (preferentialList.length > 1) {
        try {
          const shareGoodsItemId = items[0]?.itemId
          const tmpActivityCode = preferentialList.find(item => item.relationId === shareGoodsItemId)?.preferentialCode
          activityCode = tmpActivityCode ? tmpActivityCode : activityCode
        } catch (error) {
          console.log('订单详情获取活动信息失败', error)
        }
      }
    }
    return activityCode
  }
})
