const envMap = {
  test: 'test',
  staging: 'staging',
  prod: 'prod',
}

const descMap = {
  [envMap.debug]: '调试环境',
  [envMap.test]: '测试环境',
  [envMap.staging]: 'uat环境',
  [envMap.prod]: '现网环境',
}

const platformNameMap = {
  mp: 'wx miniprogram'
}

const robotMap = {
  [envMap.test]: 1,
  [envMap.staging]: 2,
  [envMap.prod]: 3,
}

const privateKeyPath = 'private/private.wx1f9ea355b47256dd.key'

const outputDir = './build/output'

const mpPreviewImagePath = `${outputDir}/mp/mpPreview.png`

module.exports = {
  envMap,
  descMap,
  platformNameMap,
  robotMap,
  privateKeyPath,
  mpPreviewImagePath
}
