import config from '../utils/config'
const testGroupUrl = 'https://static-test.pagoda.com.cn/rkhyApp/#/h5/qr?isMiniProgram=1'
const prodGroupUrl = 'https://static.pagoda.com.cn/rkhyApp/#/h5/qr?isMiniProgram=1'
import { getFruitFansStatus } from './userService'

/**
 * 跳转社群h5参数拼接
 * user_id: 会员id，store_id: 门店编号，store_name： 门店名称，locationCity： 定位城市，utm_source： 来源（埋点用）
 */
function getGroupH5Url() {
  const url = config.ENV === 'prod' ? prodGroupUrl : testGroupUrl
  const user = wx.getStorageSync('user') || {}
  const city = wx.getStorageSync('timelyCity') || {}
  const { cityName = '', storeID = '-1', storeName = '' } = city || {}
  const { userID = '' } = user || {}
  const groupH5Url = `${url}&user_id=${userID}&store_id=${storeID}&store_name=${storeName}&locationCity=${cityName}&utm_source=MP_huiyuanzhongxin`
  return {
    groupH5Url
  }
}


/**
 * 跳转社群h5
 */
const cacheGroup = {
  isFromJoinGroup: false
}

export function navigateGroupH5() {
  if (!getApp().checkSignInsStatus()) { // 判断是否登录
    getApp().showSignInModal({ onlyCancel: true })
    return
  }
  const { groupH5Url } = getGroupH5Url()
  wx.navigateTo({
    url: `/h5/pages/commonLink/index?pageUrl=${encodeURIComponent(groupH5Url)}`
  })
  cacheGroup.isFromJoinGroup = true
}


export const initFruitFansStatus = async function() {
  // 只有从社群h5加群在进入的才会查询用户信息
  // if (cacheGroup.isFromJoinGroup) {
    getFruitFansStatus()
    // cacheGroup.isFromJoinGroup  = false
  // }
}
