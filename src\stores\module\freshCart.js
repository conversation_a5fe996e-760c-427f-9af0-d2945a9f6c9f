import { observable, action, toJS, reaction } from 'mobx-miniprogram'
import { makeUpList, onSaleGoodsInfo, preSaleGoodsInfo, getActivity } from '../../utils/freshCart'
import { goodsCartApi } from '../../api/pandafresh.api'
import specialStore from './special'
import setCountMixin from '../../mixins/bgxx/setCount'
import utils from '../../utils/util'
import { donotRenderCode } from '../../source/const/cartCode'
import sensors from '../../utils/report/sensors'
import setCartCount from '../../mixins/bgxx/cartCount'
import locateStore from './locate'
import shopCartStore from '../shopCartStore'
import { YN2Boolean, boolean2YN, revertYN } from '../../utils/YNBoolean'

const { changeCount, setCount, setCountResult, showGiftInvalidTip, initSetCountApp } = setCountMixin

let isSelectting = false

/**
 * @typedef { import('../../utils/YNBoolean').YN } YN
 * /
/**
 * @typedef { import('../../components/cartSubmitBar/index').BillItem } BillItem
 * /

/**
 * @typedef { Object } CartGoodsItem
 * @property { Object } [activityInfo] - 买赠活动信息
 * @property { string } beginTakeTime - 提货时间
 * @property { number } bidPrice - 原价
 * @property { 'A' | 'V' } buyerType - A: 所有用户可购买 V: 心享会员专享
 * @property { number } count - 加购数量
 * @property { number } goodsID - 商品ID
 * @property { string } goodsNumber - 商品SKU
 * @property { string } goodsSn - 商品SKU
 * @property { number } goodsTotalPrice - 商品总价(计算了心享会员价&换购/特价活动优惠价之后的价格)
 * @property { string } headPic - 商品图片
 * @property { YN } isCombined - 是否组合品
 * @property { YN } isNewExclusive - 是否新客专享
 * @property { YN } isSelected - 是否选中
 * @property { YN } isShelf - 是否在架
 * @property { YN } isSupportCoupon - 是否可用优惠券
 * @property { string } name - 商品名称
 * @property { null | number } residualEffectNum - 商品限购数(为null表示无限购)
 * @property { 'N' | 'P' } saleType - 商品销售类(N: 现售, P: 预售)
 * @property { 'U' | 'D' } shelfStatus - 商品shelf状态(U: 上架, D: 下架)
 * @property { string } shortName - 商品简称
 * @property { string } skuId - 商品SKU
 * @property { string } spec - 商品规格
 * @property { number } stockNum - 商品库存
 * @property { YN } stockShow - 是否显示仅剩库存
 * @property { number } vipPrice - 心享会员价
 * @property { 1 | 2 } vipPriceType - 心享会员价类型(1: 心享95折 2: 用户自定义)
 * /
/**
 * @typedef { Object } CartInfo
 * @property { Object } buyGiftActivityInfo - 买赠活动信息
 * @property { Array<CartGoodsItem> } validGoodsList - 购物车商品列表
 * /
/**
 * @typedef { Object } CartItem
 * @property { string } addOnItemFreightTip - 凑单助手提示
 * @property { string } beginTakeTime - 提货时间
 * @property { string } type - 销售类型(预售/现售)
 * @property { string } freightTip - 运费提示
 * @property { YN } isAddOnItem - 是否需要运费凑单
 * @property { YN } isAllSelected - 是否全选
 * @property { Array<CartInfo> } subSuperVipCartGoodsList - 购物车信息列表
 */


function eachCartList(list, callback) {
  list.forEach(({ subSuperVipCartGoodsList = [] }, listIndex) => {
    subSuperVipCartGoodsList.forEach((data, index) => {
      callback(data, listIndex, index)
    })
  })
}

function isOnSaleMoudle(type) {
  return type === onSaleGoodsInfo
}

function getCartItemGoodsList(cartItem) {
  /** @type { CartGoodsItem[] } */
  const cartGoodsList = []
  eachCartList([cartItem], (cartItem) => {
    const { validGoodsList } = /** @type { CartInfo } */ (cartItem)
    validGoodsList.forEach(item => {
      cartGoodsList.push(item)
    })
  })
  return cartGoodsList
}

/**
 * @desc 通过购物车项计算出submitBar的数据
 * @param { Object } params 参数
 * @param { string } params.itemTitle 购物车模块标题
 * @param { boolean } params.isAddOnItem 是否需要凑单
 * @param { number } params.baseFreightFree 基础配送费
 * @param { CartItem } params.cartItem 购物车模块
 * @param { CartGoodsItem[] } params.exchangeCartList 换购品加购项
 * @param { boolean } params.isVip 是否是心享会员
 * @param { boolean } params.isOnSaleMoudle 是否现售模块
 */
function getSubmitBarInfo({ itemTitle, cartItem, exchangeCartList, isVip, isOnSaleMoudle, isAddOnItem, baseFreightFree }) {
  /** @type { CartGoodsItem[] } */
  const cartGoodsList = getCartItemGoodsList(cartItem)
  let totalCount = 0
  let selectCount = 0
  let totalPrice = 0
  let beforeDiscountsPrice = 0
  let showSaveMoney = false
  let activitySaveMoney = 0
  let vipSaveMoney = 0
  let originPrice = 0
  // 如果是现售模块,则需要计算换购商品的数量
  ;(isOnSaleMoudle ? [...cartGoodsList, ...exchangeCartList] : cartGoodsList).forEach(({ bidPrice, vipPrice, count, isSelected, specialInfo, activityInfo, goodsTotalPrice }) => {
    totalCount += count
    if (!YN2Boolean(isSelected)) { return }
    // const curPrice = isVip ? vipPrice : bidPrice
    // 5.0.2需求: 需要在结算弹窗里展示心享价优惠明细,所以这里的总价按非心享来计算
    const curPrice = bidPrice
    selectCount += count
    totalPrice += goodsTotalPrice
    if (specialInfo && !activityInfo) { // 排除换购品有特价的情况
      const { maxEffectNum: activityLimit, price } = specialInfo
      // 不享受特价 或者 加购份数小于特价限购 总价按特价来计算
      beforeDiscountsPrice += (activityLimit && count <= activityLimit ? price : curPrice) * count
      const calcSaveMoney = activityLimit && count > activityLimit
      activitySaveMoney += calcSaveMoney ? activityLimit * (curPrice - price) : 0
      // 若所勾选商品中含有特价商品且勾选数量超出单次限购数量，则展示明细的icon
      showSaveMoney = showSaveMoney || calcSaveMoney
      // 超限部分,计算心享节省
      vipSaveMoney += isVip && calcSaveMoney ? (bidPrice - vipPrice) * (count - activityLimit) : 0
    } else {
      beforeDiscountsPrice += (activityInfo ? activityInfo.price : curPrice) * count
      // 特价商品和换购品不计算心享节省
      vipSaveMoney += isVip && !(specialInfo || activityInfo) ? (bidPrice - vipPrice) * count : 0
    }
    originPrice += bidPrice&&count ? bidPrice*count : 0
  })
  // 没有选中任何商品,或者没有总价,就不能结算/显示优惠明细
  // 现售模块为什么不用[...cartGoodsList, ...exchangeCartList]
  // 因为换购商品是基于正常商品来购买的,是否能结算,取决于是否选中正常换购品
  const submitDisabled = cartGoodsList.every(item => !YN2Boolean(item.isSelected)) || !totalPrice
  const submitBillItems = [
    { title: '商品总额', value: `￥${utils.formatPrice(beforeDiscountsPrice)}` }
  ]
  vipSaveMoney && submitBillItems.push({ title: '心享价优惠', value: `-￥${utils.formatPrice(vipSaveMoney)}` })
  activitySaveMoney && submitBillItems.push({ title: '特价优惠', value: `-￥${utils.formatPrice(activitySaveMoney)}` })
  return {
    totalPrice,
    freightTips: submitDisabled ? '' : (isAddOnItem ? '不含配送费' : `免配送费¥${utils.formatPrice(baseFreightFree)}`),
    showSaveMoney: (showSaveMoney || isVip) && !submitDisabled,
    saveMoney: vipSaveMoney + activitySaveMoney,
    totalCount,
    selectCount,
    submitText: `${
      isOnSaleMoudle
      ? '次日达'
      : '预售'
    }结算`,
    submitDisabled,
    billDetailKey: isOnSaleMoudle ? '1900_190003016' : '',
    submitKey: isOnSaleMoudle ? '1900_190003017' : '1900_190006012',
    originPrice,
    submitBill: [
      /** @type { BillItem } */ ({
        title: itemTitle,
        items: submitBillItems,
        total: `¥${utils.formatPrice(totalPrice)}`
      })
    ]
  }
}

let app = getApp()

/** @desc 所有的失效商品 */
const invalidListMap = {}

const moduleStyleMap = /** @type { const } */ {
  [onSaleGoodsInfo]: { color: '#FF7387' },
  [preSaleGoodsInfo]: { color: '#222222' }
}

/**
 * @desc - 将失效商品按(现售)/(预售的提货时间)分类
 * @param { CartGoodsItem[] } list - 失效商品列表
 * @return { Record<string, CartGoodsItem[]> }
 */
function beginTakeTime2invalidList(list) {
  return list.reduce(function(map, item) {
    const { saleType, beginTakeTime } = item
    if (saleType === 'N') {
      map[onSaleGoodsInfo].push(item)
    } else {
      const beginTakeTimeList = map[beginTakeTime] || (map[beginTakeTime] = [])
      beginTakeTimeList.push(item)
    }
    return map
  }, {
    [onSaleGoodsInfo]: []
  })
}

/** 根据再来一单加购的商品,重新排序次日达购物车列表 */
function sortCartList(cartList, buyAgainList) {
  if (!(buyAgainList && buyAgainList.length)) { return }
  const { goodsSnAgainList, eshopIdAgainList, } = buyAgainList.reduce(function(data, item) {
    item.goodsSn && data.goodsSnAgainList.push(item.goodsSn)
    item.eshopGoodsId && data.eshopIdAgainList.push(item.eshopGoodsId)
    return data
  }, { goodsSnAgainList: [], eshopIdAgainList: [], })
  if (!(goodsSnAgainList.length || eshopIdAgainList.length)) {
    return
  }
  const bayAgainGoodsList = []
  eachCartList(cartList, function({ validGoodsList = [] }) {
    validGoodsList.forEach(function(goods) {
      (goodsSnAgainList.includes(goods.goodsSn) || eshopIdAgainList.includes(String(goods.goodsID))) && bayAgainGoodsList.push(goods)
    })
  })
  // 根据再来一单的数据重新排序购物车
  bayAgainGoodsList.length && shopCartStore.setTopCart({ freshGoods: bayAgainGoodsList })
}

function onSaleCheck(item) {
  return item.saleType === 'N'
}

function getCartItemKey(v) {
  return isOnSaleMoudle(v.type) ? 'N' : v.beginTakeTime
}

function getFreshSortNum(num) {
  return num !== void 0 ? num : Number.POSITIVE_INFINITY
}

/**
 * @desc 根据invalidGoodsList里失效商品现售/预售,补充cartList
 * @desc 防止某个模块里全是售罄商品时,不展示对应的模块
 * @param { Object } list
 * @param { CartItem[] } list.cartList
 * @param { CartGoodsItem[] } list.invalidGoodsList
 * @param { any[] } buyAgainList
 */
function mergeInvalid({ cartList, invalidGoodsList }, buyAgainList) {
  /** @type { Record<string, CartItem> } */
  const breakTime2Item = cartList.reduce(function(map, item) {
    map[isOnSaleMoudle(item.type) ? onSaleGoodsInfo : item.beginTakeTime] = item
    return map
  }, {})
  const waitMergeMap = {}
  invalidGoodsList.forEach(function(item) {
    const { beginTakeTime } = item
    const isOnSale = onSaleCheck(item)
    const key = isOnSale ? onSaleGoodsInfo : beginTakeTime
    if (breakTime2Item[key] || waitMergeMap[key]) {
      return
    }
    waitMergeMap[key] = {
      addOnItemFreightTip: '',
      freightTip: '',
      isAddOnItem: 'N',
      subSuperVipCartGoodsList: [{
        validGoodsList: [],
        buyGiftActivityInfo: {},
        oneBuyActivityInfo: {}
      }],
      type: isOnSale ? onSaleGoodsInfo : preSaleGoodsInfo,
      isAllSelected: 'N',
      beginTakeTime: beginTakeTime,
      oneBuyPupop: {}
    }
  })
  const tempList = Object.values(waitMergeMap)
  sortCartList(cartList, buyAgainList)
  const indexMap = shopCartStore.freshSortList.reduce((map, v, k) => {
    const mapKey = onSaleCheck(v) ? v.saleType : v.beginTakeTime
    map[mapKey] = k
    return map
  }, {})
  const newList = tempList.length ? [...cartList, ...tempList] : cartList
  return shopCartStore.freshSortList.length ? newList.sort((v1, v2) => {
    return getFreshSortNum(indexMap[getCartItemKey(v1)]) - getFreshSortNum(indexMap[getCartItemKey(v2)])
  }) : ([...newList.filter(v => v.type === onSaleGoodsInfo), ...newList.filter(v => v.type === preSaleGoodsInfo).sort((a,b) =>{
    return new Date(a.beginTakeTime) - new Date(b.beginTakeTime)
  })])
}

const numberReg = new RegExp(/^\d{1,}$/)

function waringToast(tips) {
  return new Promise(resolve => {
    const duration = 1500
    wx.showToast({
      title: tips,
      icon: 'none',
      duration,
      complete() {
        setTimeout(resolve, duration);
      }
    })
  })
}

export function countChangeChecker(value) {
  if(!numberReg.test(value)){ // 只能为纯数字
    waringToast('请输入正确商品数量')
    return { resetCount: true }
  }
  if(Number(value) === 0){ // 输入的数量不能为 0
    waringToast('输入数量必须大于0哦~')
    return { resetCount: true }
  }
  return { resetCount: false }
}

export const freshCartStore = observable({
  cartCount: 0,
  cartUnId: '',
  /** @type { Array<CartItem> } */
  cartList: [],
  superVipCartTip: '',
  freightTemplateData: {}, // 运费模板
  exchangeCartList: [],

  vipFree: 0,

  /** 是否换购品的加减购 */
  isExchangeChange: false,

  // 换购活动
  exchangeActivity: {
    matchLimit: false, // 是否满足门槛
    code: '', // 换购活动编码
    goodsList: [], // 换购商品列表
    maxLimit: 0, // 限购数量
    selectNum: 0, // 已选数量
    selectAmount: 0, // 已选换购品金额
    money: 0, // 换购门槛
  },

  goodsStorageTypeObj: {}, // 商品冷冻标签

  exchangePopup: {
    show: false
  },

  updateExchangePopup: action(function (data) {
    this.exchangePopup = toJS(Object.assign(this.exchangePopup, data))
  }),

  freightPopup: {
    show: false,
    freightTips: []
  },

  updateFreightPopup: action(function (data) {
    this.freightPopup = toJS(Object.assign(this.freightPopup, data))
  }),

  changeActivity: {
    show: false,
    activityList: []
  },

  updateChangeActivity: action(function (data) {
    this.changeActivity = toJS(Object.assign(this.changeActivity, data))
  }),

  // 兼容setCountMixin中使用了this._data
  _data: {},
  // 兼容setCountMixin中使用的this.refreshPageData
  refreshPageData: function() {},
  // 兼容setCountMixin中使用的this.setData
  setData: function() {},

  /** @desc 是否vip */
  get isVip() { return ['T', 'F'].includes(app.globalData.superVipStatus) },

  /** @desc 是否展示换购活动 */
  get exchangeShow() {
    return !!(this.onSaleGoodsList.length || this.exchangeCartList) && this.exchangeActivity.code
  },

  get cartCountText() {
    return this.cartCount > 99 ? '99+' : this.cartCount
  },

  /** @desc 所有的商品(不包括失效商品和换购商品) */
  get handleList() {
    const list = []
    eachCartList(this.cartList, function({ validGoodsList }) {
      list.push(...validGoodsList)
    })
    return list
  },

  /** @desc 现售商品列表 */
  get onSaleGoodsList() {
    const list = []
    eachCartList(this.cartList.filter(v => v.type === onSaleGoodsInfo), function({ validGoodsList }) {
      list.push(...validGoodsList)
    })
    return list
  },

  /** @desc 所有的商品(不包括失效商品,但是包括换购商品) */
  get allHandleList() {
    return [...this.handleList, ...this.exchangeCartList]
  },
  /** @desc 所有的商品勾选的数量 现售+预售 */
  get allCheckedHandleListCount() {
    return this.allHandleList.reduce((prev, cur) => prev + (cur.isSelected === 'Y' ? cur.count : 0), 0)
  },
  get noFreshCart() {
    return !this.cartList.some(item => {
      const { type, beginTakeTime, subSuperVipCartGoodsList } = /** @type { CartItem } */(item)
      const onSaleMoudle = isOnSaleMoudle(type)
      const invalidGoodsList = invalidListMap[onSaleMoudle ? onSaleGoodsInfo : beginTakeTime] || []
      return invalidGoodsList.length || subSuperVipCartGoodsList.some(v => v.validGoodsList.length)
    })
  },
  /** @desc 渲染在页面上的购物车列表 */
  get renderCartList() {
    const { fullReducedAmount = 0, reducedFreight, freight } = this.freightTemplateData
    const { cityName } = this.getGlobalData().bgxxCityInfo || {}
    return this.cartList.map((item, index) => {
      const { type, beginTakeTime, subSuperVipCartGoodsList, isAddOnItem, freightTip } = /** @type { CartItem } */(item)
      const onSaleMoudle = isOnSaleMoudle(type)
      /** @type { CartGoodsItem[] } */
      const invalidGoodsList = invalidListMap[onSaleMoudle ? onSaleGoodsInfo : beginTakeTime] || []
      const title = `次日达${onSaleMoudle ? '' : '预售'}`
      const exchangeCartList = onSaleMoudle ? this.exchangeCartList : []
      let goodsCount = exchangeCartList.length
      let hasValidGoodsList = !!goodsCount
      const needExchange = onSaleMoudle && this.exchangeActivity.matchLimit
      // 如果是现售模块并且满足换购条件,先判断换购品的全选状态
      // 先默认成true/换购品的全选状态,后续如果没商品,再根据hasValidGoodsList做处理
      let isAllSelected = !(needExchange && !(exchangeCartList.every(v => YN2Boolean(v.isSelected))))
      let totalSelectedPrice = needExchange ? exchangeCartList.reduce((sum, v) => {
        return sum + Number(YN2Boolean(v.isSelected) ? v.goodsTotalPrice : 0)
      }, 0) : 0
      const isAddOnItemBoolean = YN2Boolean(isAddOnItem)
      const cartList = subSuperVipCartGoodsList.map((item) => {
        const goodsListLength = item.validGoodsList.length
        hasValidGoodsList = hasValidGoodsList || !!goodsListLength
        goodsCount += goodsListLength
        return {
          goodsList: item.validGoodsList.map(v => {
            const isSelected = YN2Boolean(v.isSelected)
            isAllSelected = isAllSelected && isSelected
            isSelected && (totalSelectedPrice += Number(v.goodsTotalPrice))
            return {
              ...v,
              uuid: `${v.goodsSn}N` // 用于滑动删除(N表明非换购品)
            }
          }),
          buyGift: item.buyGiftActivityInfo
        }
      })
      isAllSelected = isAllSelected && hasValidGoodsList
      const baseFreightFree = freight - reducedFreight
      const isNotEnough = isAddOnItemBoolean && fullReducedAmount - totalSelectedPrice > 0
      let addOnItemFreightTip = []
      const isFree = Number(reducedFreight) === 0
      // 如果满足了运费
      if (!isNotEnough) {
        addOnItemFreightTip = [
          { text: '已满' },
          { text: `${utils.formatPrice(fullReducedAmount)}元`, highlight: true },
          { text: isFree ? '，免' : '，享满' },
          { text: `${isFree ? utils.formatPrice(baseFreightFree) : `${utils.formatPrice(fullReducedAmount)}减${utils.formatPrice(baseFreightFree)}`}元`, highlight: true },
          { text: '配送费' },
        ]
      } else {
        addOnItemFreightTip = [
          { text: '再购' },
          { text: `${utils.formatPrice(fullReducedAmount - totalSelectedPrice)}元`, highlight: true },
          { text: '，可享满' },
          { text: `${isFree ? `${utils.formatPrice(fullReducedAmount)}元` : `${utils.formatPrice(fullReducedAmount)}减${utils.formatPrice(baseFreightFree)}元`}`, highlight: true },
          { text: isFree ? '免' : '' },
          { text: '配送费' },
        ]
      }
      return {
        // 页面中会过滤无加购项的模块
        // 所以需要这个index来定位模块
        cartIndex: index,
        title,
        titleStyle: moduleStyleMap[type],
        freightTips: freightTip && cityName ? (cityName + freightTip).split('。').filter(v => v).map((v, i, arr) => `${v}${(i + 1) === arr.length ? '。' : '；'}`) : [],
        addOnItemFreightTip,
        // 如果是现售商品,使用this.superVipCartTip作为送达提示
        beginTakeTime,
        beginTakeTimeText: onSaleMoudle ? this.superVipCartTip : `预计${beginTakeTime.replace(/(\d{4})-(0?)(\d+)-(0?)(\d+)/, '$3月$5日').split(' ').shift()}送达`,
        exchangeCartList,
        cartList,
        goodsCount,
        isOnSaleMoudle: onSaleMoudle,
        isAddOnItem: isAddOnItemBoolean,
        isAllSelected,
        submitBar: getSubmitBarInfo({
          isAddOnItem: isAddOnItemBoolean,
          baseFreightFree,
          itemTitle: title,
          cartItem: item,
          exchangeCartList,
          isVip: this.isVip,
          isOnSaleMoudle: onSaleMoudle
        }),
        invalidGoodsList,
        invalidCount: invalidGoodsList.length
      }
    })
  },

  // 在app.js中调用
  initApp: action(function({ app: appContext }) {
    app = appContext
    initSetCountApp(appContext)
  }),

  initUnid: action(function() {
    this.cartUnId = app.getOperateCartUnId()
  }),

  getGlobalData: action(function() {
    const {
      bgxxCityInfo: {
        cityID = '',
        storeID = ''
      } = {}
    } = app.globalData
    return {
      ...app.globalData,
      cityID,
      storeID
    }
  }),

  /**
   * @desc 获取购物车数据(调用之前需要保证及时达定位完成)
   */
  getCartData: action(function({ fromShopCart } = {}) {
    this.initUnid()
    if (fromShopCart) {
      const { payOrderID } = this.getGlobalData()
      if (payOrderID) {
        app.globalData.payOrderID = null
        return this.buyAgain()
      }
    }
    return this.getCartList()
  }),
  showGiftInvalidTip: action(showGiftInvalidTip),
  setCountResult: action(setCountResult),
  changeCount: action(changeCount),
  setCount: action(setCount),
  countReset: action(function ({ detail, prevCount }) {
    this.setGoodsCount({
      detail,
      count: prevCount
    })
  }),
  findCartItem: action(function (goodsId, hasActivityInfo) {
    // 换购活动加入购物车的商品,需要额外检查activityInfo
    return this.allHandleList.find(v => String(v.goodsID) === String(goodsId) && ((!hasActivityInfo) || v.activityInfo))
  }),
  setGoodsCount: action(function ({ detail, count }) {
    const { goodsID, activityInfo } = detail
    const item = this.findCartItem(goodsID, activityInfo)
    item.count = Number(count)
    // 如果有activityInfo说明是换购品
    if (item.activityInfo) {
      this.exchangeCartList = toJS(this.exchangeCartList)
    } else {
      this.cartList = toJS(this.cartList)
    }
  }),
  delete: action(async function(goodsList, { excludeMode } = {}) {
    const {
      customerID,
      cityID,
      storeID,
      bgxxCityInfo
    } = this.getGlobalData()
    const { cityCode, deliveryCenterCode, storeCode } = bgxxCityInfo || {}
    try {
      const res = await app.$http.post({ url: goodsCartApi.delPFCartApi, data: {
        customerID,
        cityID,
        storeID,
        storeCode,
        excludeMode: boolean2YN(excludeMode),
        goodsList: goodsList.map(({ activityInfo, goodsSn }) => ({
          isTradeInGoods: boolean2YN(activityInfo),
          goodsSn
        })),
        cartUuId: this.cartUnId,
        cityCode,
        deliveryCenterCode
      }, isLoading: false })
      // 删除某个商品
      app.updateFreshGoodsCartList(res.data);
      this.resultHandler(res)
    } catch (err) {
      app.apiErrorDialog(err)
    }
  }),
  clearInvalid: action(function() {

  }),
  countChange: action(function ({ value, goodsInfo }) {
    const count = goodsInfo.count

    const checkResult = countChangeChecker(value)
    if (checkResult.resetCount) { return checkResult }
    if(Number(value) === Number(count)) return; // 数量没变 就什么也不做
    this.setGoodsCount({
      detail: goodsInfo,
      count: value
    })
    this.changeCount({
      currentTarget: {
        dataset: {
          type: 3,
          detail: goodsInfo,
          actpage: false,
          needTop: false,
          toast: false,
          goodsid: goodsInfo.goodsID,
          customCount: Number(value),
          prevCount: count
        }
      }
    })
  }),
  buyAgain: action(async function() {
    const {
      cityID,
      customerID,
      freshBuyAgainInfo,
      storeID,
      bgxxCityInfo
    } = this.getGlobalData()
    const { goodsList, type } = freshBuyAgainInfo || {}
    const { cityCode, deliveryCenterCode, storeCode } = bgxxCityInfo || {}
    app.globalData.isPay = null
    app.globalData.freshBuyAgainInfo = null
    try {
      const res = await app.api.bgxxBuyAgain({
        customerID: customerID || -1,
        cityID: cityID || -1,
        storeID: storeID || -1,
        storeCode: storeCode || '',
        isJoinOneBuy: 'Y',
        cityCode,
        deliveryCenterCode,
        goodsList,
        type
      }, false)
      const {
        tipCode,
        tip
      } = res.data
      this.resultHandler(res, goodsList)
      ;[0, 35806, 35807].includes(tipCode) && setTimeout(function() {
        wx.nextTick(function() {
          wx.showToast({
            title: `${tip}`,
            icon: 'none',
            duration: 1500
          })
        })
      }, 0)
    } catch (err) {
      app.apiErrorDialog(err)
      // 再来一单中商品全部被删除时，显示默认
      if (err.errorCode === 30007) {
        this.loadShopCart() // 接口出错重新加载购物车
      }
    }
  }),
  getCartList: action(async function() {
    const {
      cityID,
      customerID,
      storeID,
      bgxxCityInfo
    } = this.getGlobalData()
    const { cityCode, deliveryCenterCode, storeCode } = bgxxCityInfo || {}
    const params = {
      cartUuId: this.cartUnId,
      isJoinOneBuy: 'Y',
      customerID: customerID || '-1',
      // 获取次日达城市id
      // 业务逻辑是默认深圳市
      // 所以默认成1
      // 避免在获取运费模板时出错
      cityID: cityID || '1',
      storeID: storeID || '-1',
      storeCode,
      cityCode,
      deliveryCenterCode
    }
    try {
      if (!params.cityCode) return {}
      const { data } = await app.$http.post({ url: goodsCartApi.getPFCartApi, data: params, isLoading: false })
      app.updateFreshGoodsCartList(data)
      this.resultHandler({ data })
    } catch (err) {
      app.apiErrorDialog(err)
    }
  }),
  // 加减购物车后的业务处理,重新加载页面
  countHandler: action(function(res = {}, { isOffShelf, detail } = {}) {
    if (isOffShelf) {
      // 商品下架重新刷一下购物车
      this.getCartList()
    } else {
      this.isExchangeChange = !!(detail && 'activityInfo' in detail && detail.activityInfo)
      const { errorCode, data = {} } = res
      // 检查是否需要重新渲染购物车列表
      donotRenderCode.includes(errorCode || data.tipCode) || this.resultHandler(res)
    }
  }),
  resultHandler: action(async function({ data } = {}, buyAgainList) {
    const {
      cartCount,
      subSuperVipCartV2,
      invalidGoodsList = [],
      superVipCartTip = '',
      freightTemplateData,
      vipFree = 0,
    } = data
    const { cartList, exchangeCartList } = makeUpList(subSuperVipCartV2 || {})
    const newCartList = mergeInvalid({ cartList, invalidGoodsList }, buyAgainList)
    cartList.splice(0, cartList.length, ...newCartList)
    this.cartCount = cartCount
    const cartGoodsList = []
    // 补充maxEffectNum
    eachCartList(cartList, function({ validGoodsList }) {
      validGoodsList.forEach(function(item) {
        cartGoodsList.push(item)
        const specialInfo = item.specialInfo
        if (specialInfo) {
          item.specialInfo = {
            ...specialInfo,
            maxEffectNum: specialStore.getMaxEffectNum(specialInfo)
          }
        }
      })
    })
    // 每次获取到购物车数据时,重置一下invalidListMap
    // 否则会导致上一次的售罄商品列表还在invalidListMap里
    Object.keys(invalidListMap).forEach(key => {
      invalidListMap[key] = []
    })
    Object.assign(invalidListMap, beginTakeTime2invalidList(invalidGoodsList))
    this.vipFree = vipFree
    this.superVipCartTip = superVipCartTip
    this.freightTemplateData = freightTemplateData
    this.exchangeCartList = exchangeCartList.map(v => ({
      ...v,
      uuid: `${v.goodsSn}Y` // 用于滑动删除(Y表明是换购品)
    }))
    this.cartList = cartList
    this.getStorageType([...invalidGoodsList, ...this.allHandleList].map(v => v.goodsSn))
  }),
  getStorageType: action(async function(goodsSnList) {
    const goodsSnListAfterFilter = goodsSnList.filter(v => !(v in this.goodsStorageTypeObj))
    // 只请求之前没有的数据
    if (!goodsSnListAfterFilter.length) { return }
    // 应该优化到bgxxStore.saveGoodsStorageType这个方法里,暂时这么处理吧
    const goodsStorageTypeObj = await app.saveGoodsStorageType({ goodsSnList: goodsSnListAfterFilter })
    this.goodsStorageTypeObj = toJS(Object.assign(this.goodsStorageTypeObj, goodsStorageTypeObj))
  }),
  getCheckedList: action(function() {
    return this.allHandleList.filter(v => v.isSelected === 'Y').map(v => ({
      goodsSn: v.goodsSn,
      isTradeInGoods: v.activityInfo ? 'Y' : 'N'
    }))
  }),
  goodsSelect: action(async function() {
    isSelectting = true
    const {
      customerID,
      cityID,
      storeID,
      bgxxCityInfo
    } = this.getGlobalData()
    const { cityCode, deliveryCenterCode, storeCode } = bgxxCityInfo || {}
    try {
      wx.showLoading({ title: '加载中', mask: true })
      const res = await app.api.bgxxGoodsSelect({
        goodsList: this.getCheckedList(), // 选中的商品
        cartUuId: this.cartUnId,
        customerID: customerID || -1,
        cityID: cityID || -1,
        storeID: storeID || -1,
        storeCode: storeCode || '',
        cityCode,
        deliveryCenterCode
      });
      this.setCountResult(res)
    } catch (err) {
      app.apiErrorDialog(err)
    }
    wx.hideLoading()
    isSelectting = false
  }),
  checkSingle: action(async function(goodsInfo) {
    if (isSelectting) { return }
    const { activityInfo, goodsID } = goodsInfo
    const item = this.findCartItem(goodsID, activityInfo)
    // 如果没到达换购门槛,不做选中操作
    if ((!item) || (activityInfo && !this.exchangeActivity.matchLimit)) { return }
    item.isSelected = revertYN(item.isSelected)
    this.goodsSelect()
  }),
  checkAll: action(function(options = {}) {
    // 这里为什么不用this.allHandleList的原因
    // 因为换购商品需要正常购买商品为前提,如果没有正常购买的商品
    // 那就算有换购列表也不能正常结算
    if (isSelectting || !this.handleList.length) { return }
    const { cartIndex = 0, checkStatus } = options
    // 没有cartIndex的情况用于购物车总结算栏
    const noCartIndex = !('cartIndex' in options)
    const { isAllSelected, type } = noCartIndex ? {} : this.cartList[cartIndex]
    const checkYNStatus = 'checkStatus' in options || noCartIndex
      ? boolean2YN(checkStatus)
      : revertYN(isAllSelected)
    ;(noCartIndex || isOnSaleMoudle(type)) && this.exchangeCartList.forEach(v => {
      v.isSelected = YN2Boolean(checkYNStatus)
        // 选中场景下,只有满足换购条件才自动勾选换购品
        // 否则不选中换购品
        ? boolean2YN(this.exchangeActivity.matchLimit)
        : checkYNStatus
    })
    eachCartList(this.cartList, function(list, index) {
      if ((!noCartIndex) && index !== cartIndex) { return }
      list.validGoodsList.forEach(v => {
        v.isSelected = checkYNStatus
      })
    })
    this.goodsSelect()
  }),
  activityChange: action(async function({ buyGiftActivityCode, joinGoodsGoodsSnList }) {
    sensors.track('MPClick', 'bgxxCartActivityChange')
    const {
      cityID,
      customerID,
      storeID,
      bgxxCityInfo
    } = this.getGlobalData()
    const { cityCode, deliveryCenterCode } = bgxxCityInfo || {}
    try {
      const res = await app.api.bgxxSetBuyGiftActivity({
        cartUuId: this.cartUnId,
        customerID: customerID || -1,
        cityID: cityID || -1,
        storeID: storeID || -1,
        cityCode,
        deliveryCenterCode,
        buyGiftActivityCode, // 买赠活动编码
        joinGoodsList: joinGoodsGoodsSnList,
        isJoinOneBuy: 'Y' // 是否参与一元购活动
      })
      this.resultHandler(res)
    } catch (err) {
      app.apiErrorDialog(err)
    }
  }),
  checkLimit: action(function({ globalActivity, limitIdList, money }) {
    const selectAmount = this.onSaleGoodsList.reduce((sum, { isSelected, goodsID, goodsTotalPrice }) => {
      const inLimit = limitIdList.includes(`${goodsID}`)
      const match = globalActivity ? !inLimit : inLimit
      return sum + (match && YN2Boolean(isSelected) ? goodsTotalPrice : 0)
    }, 0)
    return {
      matchLimit: selectAmount >= money,
      selectAmount
    }
  }),
  getActivity: action(async function() {
    // TODO
    // if (!this.isExchangeChange) { return }
    const activity = await getActivity(app)
    const { goodsList, money, maxLimit, activityCode } = activity
    if (this.exchangeActivity.code && this.exchangeActivity.code !== activityCode && this.isExchangeChange) {
      waringToast('抱歉，该活动已结束')
    }
    this.isExchangeChange = false
    this.exchangeActivity = toJS(Object.assign(this.exchangeActivity, {
      ...this.checkLimit(activity),
      code: activityCode,
      goodsList,
      money,
      maxLimit
    }))
  }),
  updateExchangeSelectNum: action(function() {
    this.exchangeActivity = toJS(Object.assign(this.exchangeActivity, {
      selectNum: this.exchangeCartList.reduce((sum, v) => sum + v.count, 0)
    }))
  }),
  getActivityInfo: action(function(cartItem) {
    const { type, subSuperVipCartGoodsList } = /** @type { CartItem } */ (cartItem)
    // 现售模块的换购活动
    const joinOneBuyActivity = isOnSaleMoudle(type) ? (() => {
      const selectExchange = this.exchangeCartList.filter(v => YN2Boolean(v.isSelected))
      if (!selectExchange.length) { return {} }
      const { code, money, maxLimit } = this.exchangeActivity
      return {
        // 活动编码
        activityCode: code,
        buyLimitAmount: money,
        // 参与方式：M-金额，W-数量
        buyLimitType: 'M',
        maxLimit: maxLimit,
        oneBuyGoodsList: selectExchange.map(item => ({
          count: item.count,
          enjoyCount: item.count,
          goodsId: item.goodsID,
          maxEffectNum: item.activityInfo.maxEffectNum,
          number: item.goodsNumber,
          price: item.activityInfo.price
        }))
      }
    })() : {}
    const cartJoinBuyGiftDataList = subSuperVipCartGoodsList.filter(
      subCartGood => YN2Boolean((subCartGood.buyGiftActivityInfo || {}).isEnable)
    ).map(buyGiftGoodItem => {
      // 如果有满赠数据 并且 达到活动满赠条件 则先获取通用参数
      const joinGoodsList =
        buyGiftGoodItem.buyGiftActivityInfo.buyGiftActivityList
          .filter(
            item =>
              item.activityInfo.activityCode ===
              buyGiftGoodItem.buyGiftActivityInfo.buyGiftActivityCode
          )
          .map(item => item.joinGoodsList).reduce((list, item) => {
            list.push(...item)
            return list
          }, [])
      const arrAllBuyGiftGoods = []
      // 获取选中的项 的商品列表
      const selBuyGiftGoods =
        buyGiftGoodItem.buyGiftActivityInfo.buyGiftActivityList
          .filter(v => YN2Boolean(v.isSelected))
          .map(item => item.goodsList)
      selBuyGiftGoods.forEach(item => arrAllBuyGiftGoods.push(...item))
      return {
        // 活动编码
        activityCode: buyGiftGoodItem.buyGiftActivityInfo.buyGiftActivityCode,
        buyLimitAmount: buyGiftGoodItem.buyGiftActivityInfo.buyLimitAmount,
        // 参与方式：M-金额，W-数量
        buyLimitType: buyGiftGoodItem.buyGiftActivityInfo.satisfyType,
        // 单笔最大赠送限制
        maxLimit: buyGiftGoodItem.buyGiftActivityInfo.maxLimit,
        giftGoodsList: arrAllBuyGiftGoods.map(buyGiftGood => ({
          count: buyGiftGood.count,
          goodsId: buyGiftGood.eshopGoodsId,
          originCount: buyGiftGood.originCount
        })),
        // Y-倍送，N-非倍送
        isDoubleSend: buyGiftGoodItem.buyGiftActivityInfo.isDoubleSend,
        joinGoodsList: joinGoodsList,
        // 倍赠倍数
        giftCountMultiple: buyGiftGoodItem.buyGiftActivityInfo.giftCountMultiple
      }
    })
    return {
      joinOneBuyActivity,
      cartJoinBuyGiftDataList
    }
  }),
  checkNewExclusive: action(function(selectList) {
    let count = 0
    for (let i = 0, len = selectList.length; i < len; i++) {
      if (YN2Boolean(selectList[i].isNewExclusive)) {
        if (count >= 1) {
          wx.showToast({
            title: '每人限购一种新客专享商品哦~~',
            icon: 'none'
          })
          return false
        }
        count++
      }
    }
    return true
  }),
  settlement: action(utils.throttle(function ({ cartIndex = 0 }) {
    if (isSelectting) { return }
    if (!app.checkSignInsStatus()) { return app.signIn() }
    if (locateStore.useFreshDefaultLocation) {
      return locateStore.noAddressConfirm({ app, scene: 'fresh', asyncHomeStore: true })
    }
    const cartItem = /** @type { CartItem } */(this.cartList[cartIndex])
    const cartGoodsList = getCartItemGoodsList(cartItem)
    const selectList = (isOnSaleMoudle(cartItem.type) ? [...cartGoodsList, ...this.exchangeCartList] : cartGoodsList).filter(v => YN2Boolean(v.isSelected))
    if (!(selectList.length && this.checkNewExclusive(selectList))) { return }
    const { joinOneBuyActivity, cartJoinBuyGiftDataList } = this.getActivityInfo(cartItem)
    app.globalData.sellementList = selectList.map(item => {
      const { activityCode = '', maxEffectNum = 0, couponStacking = 0 } = item.specialInfo || {}
      const isTradeInGoods = item.activityInfo
      return {
        goodsID: item.goodsID,
        count: item.count,
        isTradeInGoods: boolean2YN(isTradeInGoods),
        // 特价活动活动编码
        activityCode: isTradeInGoods ? '' : activityCode,
        maxEffectNum,
        couponStacking
      }
    })
    // 活动信息
    app.globalData.objActivityInfo = {
      // 参加换购的商品
      joinOneBuyActivity,
      // 参加满赠活动的商品列表
      cartJoinBuyGiftDataList
    }
    wx.navigateTo({
      url: '/bgxxShop/pages/confirmOrder/confirmOrder',
      events: {
        // 订单生成后需要删除选中的商品
        'bgxxCart.deleteSelected': () => {
          this.delete(selectList)
        }
      }
    })
  }, 500))
})

// 购物车数据变化后,重新加载换购活动
reaction(() => freshCartStore.cartList, function() {
  setCartCount.setCount(freshCartStore.cartCount)
  freshCartStore.getActivity()
})
// 换购购物车数据变化后,计算换购品已选数量
reaction(() => freshCartStore.exchangeCartList, function() {
  freshCartStore.updateExchangeSelectNum()
})
