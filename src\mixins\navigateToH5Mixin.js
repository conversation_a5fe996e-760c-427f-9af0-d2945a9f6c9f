const H5_WEB_DOMAIN = require('../utils/config').baseUrl.H5_WEB_DOMAIN
const sensors = require('../utils/report/sensors')

const pageTypes = [
  { type: '1', url: '/indemnity' }, // 超时赔券协议
  { type: '2', url: '/app/aboutUs?needLogin=N' }, // 关于我们
  { type: '3', url: '/member/openManagement' }, // 开通记录页
  { type: '4', url: '/member/memberList' }, // 节省记录页
  { type: '5', url: '/member/memberBuy?t=renew&referrer=MP_huiyuanzhongxin' }, // 百果心享续费页
  { type: '6', url: '/member/memberBuy?&referrer=MP_huiyuanzhongxin' }, // 百果心享购买页
  { type: '7', url: '/member/index' }, // 百果心享首页
  { type: '8', url: '/h5/fruitFans' }, // 果粉福利
  { type: '9', url: '/member/couponList' }, // 月度礼包页
  { type: '10', url: '/app/personalInfoList?needLogin=N' }, // 个人信息清单
  { type: '11', url: '/app/tripartiteInfoList?needLogin=N' }, // 第三方信息共享清单
  { type: '12', url: '/app/privacyAbstract?needLogin=N' }, // 隐私政策摘要
  { type: '13', url: '/memberClub/index' }, // 会员俱乐部页
  { type: '14', url: '/memberClub/fruitList' }, // 我的果粒值页
]

let navigateToCommonH5Page = function (e) {
  const { urltype = '', pageurl='', sensorskey = '' } = e.currentTarget.dataset
  let h5Url = pageurl

  // urltype='all'时，不需要匹配 pageTypes，直接取传入的pageurl；如果 urltype != 'all' 则先匹配 pageTypes
  if (urltype !== 'all') {
    const page = pageTypes.find(item => { return item.type === urltype })
    if(!page) return false
    h5Url = `${H5_WEB_DOMAIN}${page.url}`
  }
  
  wx.navigateTo({
    url: `/h5/pages/commonh5/index?pageParam=${JSON.stringify({ pageUrl: encodeURIComponent(h5Url) })}`
  })

  // 上报神策埋点
  sensorskey && sensors.track('MPClick', sensorskey)
}

module.exports = {
  navigateToCommonH5Page
}