<!--activity/pages/helpDetail/index.wxml-->
<import src="./skeleton/index.skeleton.wxml"/>
<wxs module='common' src="../../../utils/common.wxs"></wxs>
<view>
  <template is="skeleton" wx:if="{{loading}}" data="{{hidePage}}"/>
  <view class="container {{roulePopupFlag?'over-hidden':''}}" data-skeleton-hide="hidePage" hidden="{{!hidePage}}">
    <nav-bar
      background="{{navBarBgColor}}"
      color="{{navBarColor}}"
      navBarTitle="{{navBarTitle}}"
      bindnavBarHeight="getNavBarHeight"
      bindback="back"
    >
      <view slot="back" class="nav-back" style="filter: brightness({{backFilter}})">
        <image src="/source/images/arrow_back.png" />
      </view>
    </nav-bar>
    <!-- content -->
    <view class="content" style="margin-top: -{{navBarHeight}}px">
      <!-- 顶部背景 -->
      <view class="head-content" style="background-image:url('{{bgPic||bg_detail_top}}');">
        <!-- 活动规则 -->
        <view class="active-rule bg-radius" data-key="helpCouponInviteeOpenRule"  bindtap="handleOpenRoulePopup" style="top:{{addTop}}px!important">活动规则</view>
        <!--获奖滚动-->
        <view class="active-scroll-record">
          <scroll-reward></scroll-reward>
        </view>

      </view>
      <!-- 领券流程 -->
      <view class="process-list">
        <block wx:for="{{processList}}" wx:key="index">
          <view class="process-item">
            <image src="{{item.icon}}" mode="aspectFill" />
            <view class="process-desc">
              <view class="process-desc-text">{{item.text1}}</view>
              <view class="process-desc-text">{{item.text2}}</view>
            </view>
          </view>
        </block>
      </view>
      <!-- 活动详情 -->
      <view class="active-invite">
        <view class="active-item">
          <image mode="aspectFill" class="active-pic" src="{{headPic}}" />
          <view class="active-detail">
            <view class="active-title text-hidden">{{actInfo.name}}</view>
            <view class="active-desc text-hidden">{{couponDesc}}</view>
            <view class="count-down">
              <view class="text mlo">{{effectiveTimeStr}}</view>
            </view>
            <view class="active-condition">
              <view class="condition-button">{{activityLabel||'全民助力'}}</view>
              <view class="condition-desc">需{{actInfo.thresholdNum}}人</view>
            </view>
          </view>
        </view>
        <view class="split-line">
          <view class="divide-circle left-circle"></view>
          <view class="divide-line"></view>
          <view class="divide-circle right-circle"></view>
        </view>
        <view class="active-process">
          <view class="process-button" bind:tap="helpCoupon" wx:if="{{!actSuccess&&actOngoing&&!finish}}">帮我点一下</view>
          <!--  -->
          <block wx:if="{{finish||!actOngoing||actSuccess}}">
            <view class="process-button" bind:tap="toStartActive" data-type="start">我也要免费领</view>
          </block>
          <view class="process-subtitle">{{subtitle}}</view>
          <view class="invite-List">
            <invite-List list="{{inviteList}}" shouldNum="{{shouldNum}}"></invite-List>
          </view>
        </view>
      </view>
      <!-- 助力人助力成功后优惠券信息 -->
      <view class="success-content" wx:if="{{showCouponInfo}}">
        <view class="success-title">谢谢你帮我助力，送你一张{{mutualRewards.isExchange ? '兑换卡' : '优惠券'}}</view>
        <view wx:if="{{mutualRewards.isExchange}}" class="exchange-coupon">
          <view class="exchange-coupon__left">
            <view class="exchange-coupon__left__price-info">
              <text class="exchange-coupon__left__price-info__yuan">￥</text>
              <text class="exchange-coupon__left__price-info__value">{{mutualRewards.couponValue}}</text>
              <text class="exchange-coupon__left__label">兑换价格</text>
            </view>
            <view class="exchange-coupon__left__name">{{mutualRewards.couponName}}</view>
            <!-- <view class="exchange-coupon__left__expiretime">有效期至 {{}}</view> -->
          </view>
          <view class="exchange-coupon__right">
            <view class="exchange-coupon__button" data-type="1" bind:tap="usePopup">去使用</view>
          </view>
        </view>
        <view class="success-desc" wx:else>
          <view class="success-coupon-content">
            <!-- 满减 立减-->
            <view class="price-content" wx:if="{{mutualRewards.couponWay==='1'||mutualRewards.couponWay==='3'}}">
              <view class="icon">￥</view>
              <view class="amount">{{mutualRewards.couponValue}}</view>
            </view>
            <!-- 满折 立折-->
            <view class="price-content" wx:if="{{mutualRewards.couponWay==='2'||mutualRewards.couponWay==='4'}}">
              <view class="amount">{{mutualRewards.couponValue}}</view>
              <view class="icon_font">折</view>
            </view>
            <!-- 免运费 -->
            <view class="price-content align-center" wx:if="{{mutualRewards.couponWay==='M'}}">
              <view class="icon">运费券</view>
              <!-- <view class="amount">{{common.formatPrice(mutualRewards.couponValue)}}</view> -->
            </view>
            <view class="desc text-hidden">
              <view class="text-coupon-name text-hidden">{{mutualRewards.couponName}}</view>
              <view class="text-rule text-hidden" wx:if="{{mutualRewards.couponWay==='5'}}">满0元可用</view>
              <view class="text-rule text-hidden" wx:if="{{mutualRewards.limitValue>=0&&mutualRewards.couponWay!=='5'}}">满{{common.formatPrice(mutualRewards.limitValue)}}元可用</view>
            </view>
          </view>
          <view class="use-button" data-type="1" bind:tap="usePopup">去使用</view>
        </view>
      </view>
      <!-- 适用商品/其他活动 -->
      <view class="active-recommend" wx:if="{{tabIndex===2&&actList.length>0}}">
        <view class="tab-list">
          <view class="tab-item-selection" data-tab="2">更多活动</view>
        </view>
        <view class="reommend-content">
            <active-list list="{{fiveList}}" page="helpDetail"></active-list>
            <view wx:if="{{actList.length<5}}" class="no-more-goods">没有更多活动了</view>
            <view class="process-button" wx:if="{{actList.length>=5}}" data-key="helpCouponCheckMoreActive" bind:tap="toHelpList">查看更多</view>
        </view>
      </view>
    </view>
    <!--活动弹框-->
    <roule-popup roulePopupFlag="{{roulePopupFlag}}" title="活动规则" bindhandleCloseRoulePopup="handleCloseRoulePopup">
        <view wx:for="{{discountsDesc}}" wx:key="index">{{item}}</view>
    </roule-popup>
    <!-- 领券弹窗 -->
    <view class="popup-coupon-mask showMask" wx:if="{{showReward}}">
      <view class="popup-coupon-content">
        <view class="inviter-content">
          <image class="avatarUrl" src="{{avatarPath||'https://resource.pagoda.com.cn/group1/M21/54/64/CmiLkGD5TyKASB85AAAn3XJoLPU505.png'}}" alt="" />
          <view class="text text-hidden-line2">谢谢你帮我助力，送你一张{{mutualRewards.isExchange ? '兑换卡' : '优惠券'}}快去使用吧</view>
        </view>
        <view class="coupon-desc {{mutualRewards.isExchange ? 'exchangeBg' : 'normal'}}">
          <view class="title text-hidden">
            <block wx:if="{{mutualRewards.isExchange}}">助力好友奖励</block>
            <block wx:else>{{mutualRewards.couponName}}</block>
          </view>
          <view class="coupon-amount">
            <!-- 满减 立减-->
            <view class="show-price" wx:if="{{mutualRewards.couponWay==='1'||mutualRewards.couponWay==='3'}}">
              <view class="price">{{mutualRewards.couponValue}}</view>
              <view class="company">元</view>
            </view>
            <!-- 满折 立折-->
            <view class="show-price" wx:if="{{mutualRewards.couponWay==='2' || mutualRewards.couponWay==='4'}}">
              <view class="price">{{mutualRewards.couponValue}}</view>
              <view class="company">折</view>
            </view>
            <!-- 免运费 -->
            <view class="show-price" wx:if="{{mutualRewards.couponWay==='5'}}">
              <!-- <view class="price">免运费</view> -->
              <!-- <view class="company lh-150">运费券</view> -->
              <view class="price font-100">运费券</view>
            </view>
            <!-- 兑换卡 -->
            <view class="show-price" wx:if="{{mutualRewards.isExchange}}">
              <view class="price">{{mutualRewards.couponValue}}</view>
              <view class="company">元</view>
            </view>
            <view class="condition text-hidden exchange" wx:if="{{mutualRewards.isExchange}}">{{mutualRewards.couponName}}</view>
            <view class="condition text-hidden" wx:if="{{mutualRewards.couponWay==='5'}}">满0元可用</view>
            <view class="condition text-hidden" wx:if="{{mutualRewards.limitAmount>=0&&mutualRewards.couponWay!=='5'}}">满{{common.formatPrice(mutualRewards.limitAmount)}}元可用</view>
          </view>
          <block wx:if="{{insertWxCardParams.sign}}">
            <send-coupon
              bindcustomevent="onCouponComfirmBtnTap"
              send_coupon_params="{{insertWxCardParams.send_coupon_params}}"
              sign="{{insertWxCardParams.sign}}"
              send_coupon_merchant="{{insertWxCardParams.send_coupon_merchant}}"
            >
              <image class="coupon-use-button" src="https://resource.pagoda.com.cn/group1/M21/60/4C/CmiLkGFQH7WAZVkHAAAnLyrccJY239.png" mode='aspectFill' />
              <view class="coupon-use-title">为您插入微信卡包，及时获取用券提醒</view>
            </send-coupon>
          </block>
          <block wx:else>
            <image class="coupon-use-button" data-type="2" bind:tap="usePopup" src="https://resource.pagoda.com.cn/group1/M21/60/4C/CmiLkGFQH7WAZVkHAAAnLyrccJY239.png" mode='aspectFill' />
          </block>
        </view>
        <block wx:if="{{insertWxCardParams.sign}}">
            <send-coupon
              bindcustomevent="onCouponCloseBtnTap"
              send_coupon_params="{{insertWxCardParams.send_coupon_params}}"
              sign="{{insertWxCardParams.sign}}"
              send_coupon_merchant="{{insertWxCardParams.send_coupon_merchant}}"
            >
            <image class="closed" src="https://resource.pagoda.com.cn/group1/M21/5F/E9/CmiWa2FQGI2AR6S0AAAE51zlKNg336.png" alt="" />
            </send-coupon>
          </block>
          <block wx:else>
            <image class="closed" bind:tap="closePopup" src="https://resource.pagoda.com.cn/group1/M21/5F/E9/CmiWa2FQGI2AR6S0AAAE51zlKNg336.png" alt="" />
          </block>
      </view>
    </view>
    <view class="home-buttom" bind:tap="goHome">
        <image class="home-buttom-img"  src="../../source/images/home_button.png" alt="" />
    </view>
  </view>
</view>


<common-loading />