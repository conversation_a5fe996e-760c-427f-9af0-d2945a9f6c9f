/**
 * 本页面是次日达分享卡片、海报 拆分出来独立功能的文件 后续涉及到卡片、海报分享的修改在本文件修改
 * 多行注释表示是共有方法 单行注释表示该类私有方法，暂未对外暴露
 */
import { fillText, drawVipIcon, drawGoodsPicRadius } from '../../../utils/services/canvasUtil'
import { fillRoundRect, measureText, strokeRoundRect } from '../../../utils/services/2DCanvasUtil'
import sensors from '../../../utils/report/sensors'
import { allTrue, defaultVal } from '../../../utils/cyclomatic'
import { getWxCodeTempFilePath }  from '../../../service/weChat'
import { YN2Boolean } from '~/utils/YNBoolean'
const commonObj = require('../../../source/js/common').commonObj
// 商品图片常量
const {NEXT_DAY_SHARE_CARD, NEXT_DAY_SHARE_POSTER, WHITE_MONEY, WHITE_FLASH, VIP95ICON, VIP_V_ICON, FRESH_TIPS_ICON} = require('../../../source/const/goodsImage')
const { generateShareAttr, ShareScene, ShareWay } = require('../../../utils/report/setup')
const config = require('../../../utils/config')

// 由于模板图是https的资源，无法直接在canvas里面绘制，这里保存的是封面图模板可直接绘制的对象
// 分享卡片模板缓存图 次日达
let cardCover = null
// 分享海报模板缓存图
let posterCover = null

const app = getApp()

export default class ShareCard {
  /**
   * 构造函数 接受商详页传入的data
   * @param {Object} that 页面操作的this对象
   */
  constructor(that) {
    this.that = that
    this.initDefaultData()
  }

  /**
   * 初始化默认数据
   */
  initDefaultData() {
    const data = {
      // 海报临时地址 tempFilePath 用于显示界面中的canvas
      shareImage: '',
      // 是否是iPhone机型
      isIphone: app.globalData.isIphone
    }
    const _data = {
      // 分享海报背景图
      posterBgUrl: NEXT_DAY_SHARE_POSTER,
      // 分享卡片背景图
      shareCardBgUrl: NEXT_DAY_SHARE_CARD,
      // 海报-白色闪电图标
      WHITE_FLASH,
      // 海报-白色人民币图标
      WHITE_MONEY,
      // 心享95折图标
      vip95Icon: VIP95ICON,
      // 心享V图标
      vipVIcon: VIP_V_ICON,
      // 网络图片下载好的临时路径
      storeImageStatus: {}
    }
    Object.assign(this.that.data, data)
    Object.assign(this.that._data, _data)
  }

  getShareReportData() {
    const that = this.that
    const { goodsSn, goodsName, shareTitle } = that.data.detail
    const bgxxSelectLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo') || { selectStoreInfo: {}, selectAddressInfo: {} }
    const {
      selectStoreInfo: { storeCode, storeID, storeName },
    } = bgxxSelectLocateInfo
    return {
      ...generateShareAttr(),
      shareScene: ShareScene.次日达商品,
      mp_shareTitle: shareTitle,
      screen_name: '次日达商品详情页',
      screen_code: 11304,
      SKU_ID: goodsSn,
      SKU_Name: goodsName,
      storeID,
      storeName,
      storeNum: storeCode || '',
    }
  }

  /**
   * 用户分享卡片事件
   */
  onShareAppMessage() {
    const that = this.that
    const { goodsSn, eshopGoodsId, goodsName, shareTitle } = that.data.detail
    const bgxxSelectLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo') || { selectStoreInfo: {}, selectAddressInfo: {} }
    const {
      selectStoreInfo: { storeCode, storeID, storeName },
      selectAddressInfo: { cityCode, deliveryCenterCode }
    } = bgxxSelectLocateInfo
    const shareObj = this.getShareReportData()
    sensors.track('MPShare', shareObj)
    sensors.track('Click', {
      element_code: 1130401001,
      element_name: '微信好友',
      element_content: '分享给好友',
      screen_code: 11304,
      screen_name: '次日达商品详情页',
      SKU_ID: goodsSn,
      SKU_Name: goodsName,
      storeID,
      storeName,
      storeNum: storeCode || ''
    })
    return {
      title: `${that.data.detail.goodsName}`,
      path: '/pages/xxshop/index/index?toDetail=' + JSON.stringify({
        goodsID: eshopGoodsId,
        goodsSn: goodsSn,
        origin: that._data.origin || { cityCode: cityCode, deliveryCode: deliveryCenterCode }
      }) + '&shareObj=' + JSON.stringify(shareObj)+ '&bgxxStoreID=' + storeID||-1,
      imageUrl: that._data.sharePic || that.data.headPic
    }
  }

  /**
   * 绘制分享卡片 一个方法搞定
   */
  async drawSharePic() {
    const self = this
    const that = this.that

    // 头图需要对尺寸进行优化，增加参数&imageView2/3/w/496
    const connectCharacter = that.data.headPic.indexOf('?') === -1 ? '?' : '&';
    that.data.headPic += connectCharacter + 'imageView2/3/w/496'

    const p1 = self.getCanvasImage(that.data.headPic, 'headPic')
    const p2 = self.setCoverBgImg(that._data.shareCardBgUrl, 'shareCardBgPic')
    const p3 = self.getCanvasImage(that._data.vip95Icon, 'vip95Icon')
    const p4 = self.getCanvasImage(that._data.vipVIcon, 'vipVIcon')
    const p5 = self.getCanvasImage(that._data.posterBgUrl, 'WHITE_MONEY')
    await Promise.all([p1, p2, p3, p4, p5]).catch((e) => {
      console.error(e)
    })

    const canvasCtx = wx.createCanvasContext('sharePicCanvas')
    canvasCtx.scale(2, 2) // 画布为效果图的1/2大小，画笔也缩小为原来的1/2

    const { goodsSaleObj, detail, goodsSpecial } = that.data
    let { shareCardBgPic, headPic } = that._data.storeImageStatus

    // 绘制背景图
    const BG_W = 248
    const BG_H = 198.5
    canvasCtx.drawImage(shareCardBgPic, 0, 0, BG_W, BG_H)

    // 绘制商品图片
    canvasCtx.drawImage(headPic, 6, 22, 124, 124)

    /*
    // 绘制人气值 业务要求不展示人气值 这里注释掉
    let popularity = ''
    if (goodsSaleObj && goodsSaleObj[detail.id]) {
      popularity = `人气值${goodsSaleObj[detail.id]}`
    }
    if (popularity) {
      const marginLeft = 6
      const marginRight = 10
      const maxWidth = 100
      const popularityW = measureText(canvasCtx, popularity, '10px')
      const width = (popularityW < maxWidth ? popularityW : maxWidth) + marginLeft + marginRight
      const cornerRadius = { bottomRight: 0, bottomLeft: 11, topLeft: 0, topRight: 0 } // 圆角矩形的四个圆角的半径
      fillRoundRect(canvasCtx, BG_W - width, 0, width, 18, 4, '#ffeeef', cornerRadius)
      fillText(canvasCtx, {
        text: popularity,
        x: BG_W - width + 10,
        y: 3,
        bolder: true,
        fontSize: '10',
        lineHeight: '10',
        color: '#FF6980',
        textBaseline: 'top',
        width: maxWidth,
        MaxLineNumber: 1
      })
    }
    */

    // 绘制价格标题
    const isSpecialGoods = goodsSpecial && goodsSpecial.goodsSn
    let priceTitle = '抢购价'
    if (isSpecialGoods) {
      priceTitle = '限时特价'
    }
    // 指定一个x坐标值 刚好在按钮的中间 这样方便居中
    // 使用CENTER - 要居中元素的一半 则能得到x坐标了
    const CENTER = 184.5

    const priceTitleW = measureText(canvasCtx, priceTitle, '12px')
    fillText(canvasCtx, {
      text: priceTitle,
      x: CENTER - priceTitleW / 2,
      y: 36,
      fontSize: '12',
      lineHeight: '12',
      color: '#222222',
      textBaseline: 'top',
      MaxLineNumber: 1
    })

    // 绘制价格
    const { firstPrice, secondPrice } = this.getPrice()

    // 求出绘制最大字号的 ￥ 符号所占宽度
    const textWidth1 = measureText(canvasCtx, '￥', '12px')
    // 求出绘制最大字号的金额 1000 所占宽度
    const textWidth2 = measureText(canvasCtx, firstPrice, '24px')
    // 这里会得到绘制的起始位置
    let firstPriceX = CENTER - (textWidth1 + textWidth2 + 2) / 2
    firstPriceX < 123 && (firstPriceX = 123) // 价格位置在x方向不能小于123
    // 绘制大金额
    fillText(canvasCtx, {
      text: '￥',
      x: firstPriceX - 2,
      y: 58,
      fontSize: 17,
      lineHeight: 12,
      color: '#222222',
      textBaseline: 'top',
      MaxLineNumber: 1
    })
    firstPriceX = firstPriceX + textWidth1
    fillText(canvasCtx, {
      text: firstPrice,
      x: firstPriceX,
      y: 50,
      fontSize: 26,
      lineHeight: 26,
      color: '#222222',
      textBaseline: 'top',
      MaxLineNumber: 1
    })

    // 特价/赠品 则 需要展示划线价
    if (isSpecialGoods || (detail.isGift === 'Y')) {
      const textWidth = measureText(canvasCtx, `￥${secondPrice}`, '12px')
      const offsetY = 78
      // 绘制划线价
      fillText(canvasCtx, {
        text: `￥${secondPrice}`,
        x: CENTER - textWidth / 2,
        y: offsetY,
        fontSize: 12,
        lineHeight: 12,
        color: '#cccccc',
        textBaseline: 'top',
        MaxLineNumber: 1
      })
      // 绘制原价的删除线
      canvasCtx.beginPath()
      canvasCtx.setLineWidth(1)
      canvasCtx.moveTo(CENTER - textWidth / 2 + 1, offsetY + 7)
      canvasCtx.lineTo(CENTER - textWidth / 2 + textWidth + 2, offsetY + 7)
      canvasCtx.setStrokeStyle('#cccccc')
      canvasCtx.stroke()
    }
    canvasCtx.draw(false, () =>{
      // 生成图片到临时路径
      setTimeout(() => {
        wx.showLoading({
          title: '正在生成图片'
        })
        wx.canvasToTempFilePath({
          x: 0,
          y: 0,
          width: 496,
          height: 397,
          quality: 1,
          // destWidth: 860,
          // destHeight: 672,
          canvasId: 'sharePicCanvas',
          success: function (res) {
            that._data.sharePic = res.tempFilePath
            that._data.isCreatSharePic = true
            // 此时允许分享卡片
            that.triggerShareMenu(true)
            wx.hideLoading()
          },
          fail: function (res) {
            that._data.isCreatSharePic = true
            wx.hideLoading()
          }
        })
      }, that.data.isIphone ? 0 : 100)
    })
  }

  /**
   * 绘制分享海报 canvas图
   */
  async drawPoster () {
    const self = this
    const that = this.that
    // 缩放0.5倍 后面的单位都是进行×0.5计算后显示
    const canvasCtx = wx.createCanvasContext('posterCanvas')
    canvasCtx.scale(2, 2)

    const { posterBgImg, detailHeadPic, canvasShowImg, vipVIcon, FRESH_TIPS_ICON } = that._data.storeImageStatus
    // 绘制背景图片 750 1304
    canvasCtx.drawImage(posterBgImg, 0, 0, 375, 652)

    // 中心点
    const CENTER = 375/2
    // 商品头图尺寸
    const goodsImgW = 300
    const goodsImgH = 300
    let xOffset = 40

    // 绘制商品头图
    // canvasCtx.drawImage(detailHeadPic, 19, 58, 338, 338)
    drawGoodsPicRadius({canvasCtx, imagePath: detailHeadPic})
    // 绘制商品类型
    canvasCtx.drawImage(FRESH_TIPS_ICON, 28, 67, 60, 24)

    // 商品名称
    const { detail, goodsSpecial } = that.data
    const { goodsSn, goodsName, subtitle, buyerType = 'A', vipPriceType = 2 } = detail
    const isSpecialGoods = goodsSpecial.goodsSn
    const isGift = that.data.isGift === 'Y' // 赠品价格为0
    let curPrice = isGift ? '0' : ((isSpecialGoods ? goodsSpecial.price : detail.retailPrice) / 100).toFixed(2)
    curPrice = '' + (+curPrice)
    // 绘制商品标题
    let yOffset = 150 + goodsImgH + 12
    // 最大标题宽度
    const maxWidth = 160
    const LINE_HEIGHT = 24
    fillText(canvasCtx, {
      text: goodsName,
      x: CENTER - goodsImgW / 2,
      y: yOffset,
      width: maxWidth,
      color: '#222222',
      fontSize: 18,
      bolder: true,
      lineHeight: LINE_HEIGHT,
      MaxLineNumber: 2
    })
    // 计算是否超过一行 因为超过1行 描述文案的位置需要下移
    canvasCtx.setFontSize(16)
    const isWrap = canvasCtx.measureText(goodsName).width > maxWidth - 10

    // 绘制商品描述 如果商品名称换行 则位移更下一点
    yOffset = yOffset + LINE_HEIGHT + (isWrap ? 28 : 2)
    fillText(canvasCtx, {
      text: subtitle,
      x: CENTER - goodsImgW / 2,
      y: yOffset,
      width: maxWidth,
      color: '#999999',
      fontSize: 12,
      lineHeight: 16,
      MaxLineNumber: 2
    })

    // 绘制红色价格/黑色心享专享价
    yOffset = yOffset + (isWrap ? 0 : 28) - 75

    // 如果不是心享专享 或者 是特价 则绘制红色原价文字
    if (defaultVal(buyerType !== 'V', isSpecialGoods)) {
      // 绘制非心享专享商品的售价
      const curPriceColor = '#FF7387'
      canvasCtx.setFontSize(18)
      canvasCtx.setFillStyle(curPriceColor)
      canvasCtx.setTextAlign('left')
      canvasCtx.fillText('¥', xOffset, yOffset - 1)
      xOffset = xOffset + canvasCtx.measureText('¥').width
      canvasCtx.setFontSize(28)
      canvasCtx.setFillStyle(curPriceColor)
      canvasCtx.setTextAlign('left')
      canvasCtx.fillText(curPrice, xOffset, yOffset)
    }

    // 接下来是黑色文字(心享专享) 或者是 划线价
    xOffset = buyerType !== 'V' ? 40 + canvasCtx.measureText(`¥${curPrice}`).width : 40
    // 如果不是赠品 且 有心享价
    if (allTrue(!isGift, defaultVal(detail.heartPrice, detail.heartPrice === 0))) {
      const transprice = isSpecialGoods
        ? detail.retailPrice // 如果是普通会员 且是特价商品 那么划线价取原售价
        : detail.heartPrice
       // 有心享价（其中buyerType = 'V' 为心享专享商品）
      let heartPrice = (transprice / 100).toFixed(2)
      heartPrice = '' + (+heartPrice)

      // 如果是特价商品 这里是需要绘制划线价
      if(isSpecialGoods){
        canvasCtx.setFontSize(15)
        canvasCtx.setFillStyle('#888888')
        canvasCtx.setTextAlign('left')
        canvasCtx.fillText(`¥${heartPrice}`, xOffset, yOffset - 1)
        const priceWidth = canvasCtx.measureText(`¥${heartPrice}`).width

        //绘制原价的删除线
        canvasCtx.beginPath()
        canvasCtx.setLineWidth(.5)
        canvasCtx.moveTo(xOffset - 2, yOffset - 7)
        canvasCtx.lineTo(xOffset + priceWidth + 2, yOffset - 7)
        canvasCtx.setStrokeStyle('#888888')
        canvasCtx.stroke()

      }

      // 如果不是特价 则直接显示心享价格
      if (!isSpecialGoods) {
        // 判断是否是心享95折
        // 是心享专享
        if (buyerType === 'V') {
          // 绘制心享价(黑色的价格)
          canvasCtx.setFontSize(18)
          canvasCtx.setFillStyle('#000000')
          canvasCtx.setTextAlign('left')
          canvasCtx.fillText('¥', xOffset, yOffset - 1)

          xOffset += canvasCtx.measureText('¥').width + 2
          canvasCtx.setFontSize(38)
          canvasCtx.setFillStyle('#000000')
          canvasCtx.setTextAlign('left')
          canvasCtx.fillText(heartPrice, xOffset, yOffset + 1)
          const priceWidth = canvasCtx.measureText(heartPrice).width

          drawVipIcon({
            canvasCtx,
            image: vipVIcon,
            text: '心享价',
            x: xOffset + priceWidth + 6,
            y: yOffset - 22,
            height: 21
          })
        } else {
          // 绘制心享价或者心享95折
          const text = vipPriceType === 1 ? '心享95折' : `¥${heartPrice}`
          drawVipIcon({
            canvasCtx,
            text,
            x: xOffset,
            y: yOffset - 22,
            height: 17
          })
        }
      }
    } else {
      let origPrice = detail.retailPrice
      if (origPrice || Number(origPrice) === 0) {
        origPrice = (origPrice / 100).toFixed(2)
        origPrice = '' + (+origPrice)
        canvasCtx.setFontSize(15)
        canvasCtx.setFillStyle('#888888')
        canvasCtx.setTextAlign('left')
        canvasCtx.fillText('¥' + origPrice, xOffset, yOffset)
        //绘制原价的删除线
        canvasCtx.beginPath()
        canvasCtx.setLineWidth(.5)
        canvasCtx.moveTo(xOffset - 1, yOffset - 5)
        canvasCtx.lineTo(xOffset + canvasCtx.measureText('¥' + origPrice).width + 1, yOffset - 5)
        canvasCtx.setStrokeStyle('#888')
        canvasCtx.stroke()
      }
    }
    // 绘制小程序码
    canvasCtx.drawImage(canvasShowImg, 228, 401, 120, 120)

    // 长按图片去购买
    fillText(canvasCtx, {
      text: '长按图片去购买',
      x: 228 + 16,
      y: 401 + 120 + 10,
      color: '#888888',
      fontSize: 12,
      lineHeight: 10,
      MaxLineNumber: 1
    })

    canvasCtx.setTextBaseline('normal')

    canvasCtx.draw(false, () => {
      wx.showLoading({
        title: '正在生成图片'
      })
      // canvas 画布转为图片
      setTimeout(function () {
        wx.canvasToTempFilePath({
          x: 0,
          y: 0,
          width: 750,
          height: 1304,
          quality: .4,
          destWidth: 750 * 1.5,
          destHeight: 1304 * 1.5,
          canvasId: 'posterCanvas',
          success: function (res) {
            that._data.posterPic = Object.assign(that._data.posterPic, {
              [goodsSn]: res.tempFilePath
            })
            self.showSharePoster(res.tempFilePath)
            wx.hideLoading()
          },
          fail: function () {
            wx.hideLoading()
          }
        })
      }, that.data.isIphone ? 0 : 100)
    })
  }

  /**
   * 用户点击底部弹出的分享按钮
   */
  share (e) {
    const that = this.that
    let sensorsEvent
    const { goodsSn, goodsName } = that.data.detail
    const { type } = e.currentTarget.dataset
    const bgxxSelectLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo') || {selectStoreInfo: {}}
    let {selectStoreInfo:{storeCode,storeID,storeName}} = bgxxSelectLocateInfo

    if (type === 'share') {
      if(!that._data.isCreatSharePic) {
        wx.showLoading({
          title: '正在生成图片'
        })
      }
      this.utilShare()
      sensorsEvent = 'bgxxShopGoodsDetailToshare'
    } else if (type === 'thumbnail') {
      app.subProtocolValid('shop', () => {

        // 商品名称
        let { detail, goodsSpecial } = that.data
        let { goodsSn } = detail
        const { posterPic = {} } = that._data
        // 若海报已经生成过了，不用重新生成
        if (posterPic.hasOwnProperty(goodsSn)) {
          this.showSharePoster(posterPic[goodsSn])
          return
        }
        wx.showLoading({
          title: '正在生成海报',
        })
        if (!wx.showShareImageMenu) {
          that.setData({
              isThumbnail: true
          })
        }
        this.utilThumbnail()

        const promiseList = [
          this.getCode(),
          this.getPosterBgImg(),
          this.getDetailHeadPic(),
          this.getGoodsTipsIcon()
        ]
        Promise.all(promiseList).then(() => {
          this.drawPoster()
        }).catch((e) => {
          console.error(e)
          commonObj.showModal('提示', '生成海报出错啦，请稍后重试', false, '我知道了', '', function (res) {
            if (res.confirm) {
              that.setData({
                isThumbnail: false
              })
            }
          })
        })
        sensorsEvent = 'bgxxShopGoodsDetailSharePoster'
      })()
    }
    sensors.track('Click', sensorsEvent, {
      SKU_ID: goodsSn,
      SKU_Name: goodsName,
      storeID,
      storeName,
      storeNum: storeCode || ''
    })
  }

  /**
   * 关闭海报模态窗
   * @param {Object} e 点击对象event值
   */
  closeModel (e) {
    const that = this.that
    const { goodsSn, goodsName } = that.data.detail
    const { type } = e.currentTarget.dataset
    wx.hideLoading()
    sensors.track('Click', 'bgxxShopGoodsDetailShareCancel', {
      SKU_ID: goodsSn,
      SKU_Name: goodsName
    })
    if (type === 'share') {
      that.setData({
        isShare: false
      })
    } else if (type === 'thumbnail') {
      that.setData({
        isThumbnail: false
      })
    }
  }

  /**
   * 海报保存到本地
   */
  savePoster () {
    const that = this.that
    wx.showLoading({
      title: '正在生成海报'
    })
    sensors.track('Click', 'bgxxGoodDetailSavePoster')
    wx.saveImageToPhotosAlbum({
      filePath: this.data.shareImage,
      success: (res) => {
        wx.hideLoading()
        wx.showToast({
          title: '保存成功，快去分享吧~',
          icon: 'none'
        })
      },
      fail: (err) => {
        wx.hideLoading()
        commonObj.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了', '', function (res) {
          if (res.confirm) {
            wx.openSetting()
          }
        })
      }
    })
  }

  // 获取图片的本地路径
  getCanvasImage(netUrl, key) {
    const that = this.that
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: netUrl, //请求的网络图片路径
        success: function (res) {
          that._data.storeImageStatus[key] = res.path
          resolve(res.path)
        },
        fail: function (err) {
          console.error(`资源加载失败netUrl: [${netUrl}],key: [${key}]`, err)
          reject(false)
        }
      })
    })
  }

  // 展示商品生成的海报
  showSharePoster(path) {
    try {
      const that = this.that
      if (!wx.showShareImageMenu) {
        that.setData({
          shareImage: path
        })
      } else {
        wx.showShareImageMenu({
          path,
          success: () => {
            sensors.track('MPShare', {
              ...this.getShareReportData(),
              shareWay: ShareWay.分享海报,
            })
          },
          async fail(err) {
            let {errMsg} = err
            if(errMsg !=="showShareImageMenu:fail cancel"){
              const res = await app.showModalPromise({
                content: `保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试`,
                showCancel: false,
                confirmText: '我知道了',
              })
              if (res) wx.openSetting()
            }
          },
          complete () {
            that.setData({
              isShare: false,
              isThumbnail: false
            })
          }
        })
      }
    } catch (e) {
      console.error(e)
    }
  }

  // 将商品图缓存
  getImageInfo(netUrl, storageKeyUrl) {
    const self = this
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: netUrl,    //请求的网络图片路径
        success: function (res) {
          //请求成功后将会生成一个本地路径即res.path,然后将该路径缓存到storageKeyUrl关键字中
          self.storageImage(res.path, storageKeyUrl)
          resolve()
        },
        fail: res => reject()
      })
    })
  }

  // 将获取到的图片的本地路径存储到 posterImage
  storageImage(path, key) {
    this.that._data.posterImage = Object.assign(this.that._data.posterImage, {
      [key]: path
    })
  }

  // 生成二维码
  async getCode() {
    const that = this.that
    // 当前用户是否为销提分离团长
    let isStoreLeader = wx.getStorageSync('isStoreLeader')
    const { customerID = '',bgxxCityInfo: { storeID = ''} = {}, globalLaunchOptions: { isCarryStore = 'Y' } } = app.globalData
    const pageUrl = 'pages/xxshop/index/index'
    // MALL 标记跳转商品详情页【首页跳二级页面】
    let sceneUrl = `MALL@${that.data.detail.id}@${ YN2Boolean(isCarryStore) ? storeID : '' }@`
    const widthUrl = 200
    const isHyalineUrl = false
    if (!!customerID) {
      sceneUrl += `${isStoreLeader === 'Y' ? customerID : ''}`
    }
    // 生成心享小程序码地址
    const getUrl = config.baseUrl.PAGODA_DSN_DOMAIN
    //const posterUrl = `${getUrl}/api/v1/wechat/wxa/wxacodeunlimit/get/${sceneUrl}/${pageUrl}/${widthUrl}/${isHyalineUrl}` //生成心享小程序码
    const posterUrl = await getWxCodeTempFilePath({
      page: pageUrl,
      scene: sceneUrl,
      width: widthUrl,
      isHyaline: isHyalineUrl
    })
    return this.getCanvasImage(posterUrl, 'canvasShowImg')
  }

  // 获取海报背景图
  getPosterBgImg() {
    return this.setCoverBgImg(this.that._data.posterBgUrl, 'posterBgImg')
  }

  // 获取商品详情头图
  getDetailHeadPic() {
    const { detail } = this.that.data
    const detailHeadPic = (detail.headPic ? detail.headPic : (detail.detailHeadPicList.length > 0 ? detail.detailHeadPicList[0].url : ''))
    return this.getCanvasImage(detailHeadPic, 'detailHeadPic')
  }

  // 获取海报权益图标
  getPosterIconImg() {
    return Promise.all([
        this.getCanvasImage(this.that._data.WHITE_FLASH, 'WHITE_FLASH'),
        this.getCanvasImage(this.that._data.WHITE_MONEY, 'WHITE_MONEY')
      ]
    )
  }

  // 获取次日达商品标识
  getGoodsTipsIcon() {
    return this.getCanvasImage(FRESH_TIPS_ICON, 'FRESH_TIPS_ICON')
  }

  // 分享弹框
  utilShare () {
    const that = this.that
    that.setData({
      isShare: true
    })
    const animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    that.animation = animation
    animation.translateY(-158).step()
    that.setData({
      animationShare: animation.export()
    })

    setTimeout(function () {
      that.setData({
        animationShare: animation
      })
    }.bind(that), 400)
  }

  /**
   * 缩略图弹框 生成动画
   */
  utilThumbnail () {
    const that = this.that
    const animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    that.animation = animation
    animation.translateY(-parseInt((that.data.modelHeight / 2))).step()
    that.setData({
      animationImage: animation.export()
    })

    setTimeout(function () {
      that.setData({
        animationImage: animation
      })
    }.bind(that), 300)
  }

  // 获取绘制分享图片价格{firstPrice: 普通价/心享专享价/0, secondPrice：心享价/划线价}
  getPrice() {
    let { isGift, detail, goodsSpecial } = this.that.data
    // vipPriceType 1: 心享95折 2: 用户自定义
    let { buyerType = 'A', vipPriceType = 2 } = detail
    isGift = isGift === 'Y'
    // 赠品价格为0, 非赠品为普通价
    let firstPrice = buyerType === 'V' ? (detail.heartPrice / 100).toFixed(2) : (isGift ? '0' : (detail.retailPrice / 100).toFixed(2))
    // 非赠品有心享价格绘制心享价格，否则绘制普通价格
    let secondPrice = (((detail.heartPrice || detail.heartPrice === 0) && !isGift
      ? detail.heartPrice
      : detail.retailPrice) / 100).toFixed(2)
    // 如果是次日达特价活动
    if (goodsSpecial.goodsSn) {
      firstPrice = (goodsSpecial.price / 100).toFixed(2)
      secondPrice = (detail.retailPrice / 100).toFixed(2)
    }
    // 输出价格时,缺0不补0
    return { firstPrice: '' + (+firstPrice), secondPrice: '' + (+secondPrice), isGift, isVip95: vipPriceType === 1 }
  }
  /**
   * private方法 获取分享卡片背景图缓存
   * @param {String} netUrl 网络地址
   * @param {'shareCardBgPic' | 'posterBgImg'} key 要保存的键
   */
  async setCoverBgImg(netUrl, key) {
    const that = this.that
    const storeImageStatus = that._data.storeImageStatus
    const isCard = key === 'shareCardBgPic'
    const isPoster = key === 'posterBgImg'

    if (isCard && cardCover) {
      storeImageStatus[key] = cardCover
      return true
    }
    if (isPoster && posterCover) {
      storeImageStatus[key] = posterCover
      return true
    }
    const img = await this.getCanvasImage(netUrl, key)
    if (isCard) {
      cardCover = img
    }
    if (isPoster) {
      posterCover = img
    }
    return true
  }
}
