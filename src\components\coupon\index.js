import { isExchangeCard as isExchangeCardFn } from "../../source/const/coupon";
const {jumpH5Vip} = require('../../utils/services/jumpBgxxVip')
const util = require('../../utils/util');
const { VIP_RADIUS_ICON, FOLD_ICON, EXCHANGE_CARD } = require('../../source/const/goodsImage')
const app = getApp();
// components/coupon/index.js
Component({
  /**
   * 组件的属性列表
   * coupon: 优惠券数据
   * type: 页面来源 {select: 选框，normal: 展示，disabled: 无法使用  }
   * color: 页面色调 {light: 轻色调, deep: 深色调}
   * src: 来源字段，可为页面/不同tab来源提供标志
   * orderBestCouponCode: 本单最优券code
   */
  properties: {
    coupon: {
      type: Object,
      value: () => {},
      observer(newVal) {
        if (!newVal) {
          return
        }
        // 详细说明
        let useableCondition = newVal.useableCondition || []
        //  是否兑换卡
        const isExchangeCard = isExchangeCardFn(newVal.defineId)
        this.setData( {
          // 只要有提示就展示详细说明
          hasMore: isExchangeCard ? !!newVal.ruleDescription : !!useableCondition.length
        })
      }
    },
    type: {
      type: String,
      value: 'normal'
    },
    color: {
      type: String,
      value: 'deep'
    },
    src: {
      type: String,
      value: ''
    },
    orderBestCouponCode:{
      type: String,
      value: ''
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    hasMore: false, // 是否展示详细信息栏
    isFold: true, // 是否折叠详细信息栏
    offSet: 1, // 不可用券起始下标
    // 图标
    VIP_RADIUS_ICON,
    EXCHANGE_CARD,
    FOLD_ICON,
    // 是否是兑换卡
    isExchangeCard: false
  },
  lifetimes: {
    attached: function() {
      const { coupon, src } = this.properties
      if (!coupon) {
        return
      }
      let { couponWay, channelSeparation, defineId, ruleDescription = '' } = coupon
      let useableCondition = coupon.useableCondition || []
      let len = useableCondition.length
      let couponValue = coupon.couponValue
      // 百果园 + 当渠道来源是微信小程序(以及v3.1.1增加门店的渠道来源)才显示去使用/去看看
      let showToUse = (src === 'fruit' && (channelSeparation.indexOf('X') >= 0 || channelSeparation.indexOf('P') >= 0)) || src === 'food'
      // 1:满减券 3:立减券 5:免运费 6:代金券
      if(['1', '3', '5', '6'].includes(couponWay)) {
        couponValue = Number((couponValue/100).toFixed(2)) || 0
      }
      // 2:满折券 4:立折券
      if(['2', '4'].includes(couponWay)) {
        couponValue = Number((couponValue/10).toFixed(1)) || 0
      }
      //  是否兑换卡
      const isExchangeCard = isExchangeCardFn(defineId)
      this.setData( {
        // 只要有提示就展示详细说明
        hasMore: isExchangeCard ? !!ruleDescription : Boolean(len),
        offSet: len + 1,
        couponValue,
        showToUse,
        isExchangeCard
      })
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    toggleMore() {
      this.setData({
        isFold: !this.data.isFold
      })
    },
    toUsePage() {
      const { coupon } = this.properties
      this.triggerEvent('toUsePage', coupon)
    },
    change(val) {
      const { coupon } = this.properties
      this.triggerEvent('selectChange', coupon)
    },
    navigateRenewPage() {
      jumpH5Vip('memberRenewUrl')
    },
    navigateOpenPage() {
      jumpH5Vip('memberBuyUrl')
    },
    async appUse() {
      const res = await app.showModalPromise({
        content: "该券仅可在App使用",
        showCancel: true,
        confirmText: '下载App',
        cancelText: "取消"
      })
      if (res) {
        util.navigateToDownLoadH5()
      }
    }
  }
})
