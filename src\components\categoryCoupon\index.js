// components/coupon/index.js
const common = require('../../source/js/common.js');
const util = require('../../utils/util');
const sensors = require('../../utils/report/sensors')
const { VIP_RADIUS_ICON } = require('../../source/const/goodsImage')
const { jumpH5Vip } = require('~/utils/services/jumpBgxxVip')
const app = getApp()

Component({
  /**
   * 组件的属性列表
   * coupon: 优惠券数据
   */
  properties: {
    coupon: {
      type: Object,
      value: () => {}
    },
    getCouponSensorskey: {
      type: String,
      value: ''
    },
    useCouponSensorskey: {
      type: String,
      value: ''
    },
    appPopupCloseSensorskey: {
      type: String,
      value: ''
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    hasMore: false, // 是否展示详细信息栏
    isFold: true, // 是否折叠详细信息栏
    // 心享专享优惠券图标
    VIP_RADIUS_ICON
  },
  lifetimes: {
    attached: function() {
      const { coupon } = this.properties
      const { couponWay, useableCondition = [] } = coupon || {}
      const len = (useableCondition || []).length
      // 百果园 + 当渠道来源是微信小程序是才显示去使用/去看看
      this.setData( {
        hasMore: couponWay !== '5' &&  len,
      })
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    toggleMore() {
      this.setData({
        isFold: !this.data.isFold
      })
    },
    receiveCoupon() {
      // 上报神策
      const sensorskey = this.properties.getCouponSensorskey
      sensorskey && sensors.track('MPClick', sensorskey)

      this.triggerEvent('receiveCoupon', this.properties.coupon)
    },
    toUsePage() {
      // 上报神策
      const sensorskey = this.properties.useCouponSensorskey
      sensorskey && sensors.track('MPClick', sensorskey)

      let { defineId, couponCode, couponWay, channelSeparation, couponTypeStr, couponName, useableCondition, limitValue, couponValueShow } = this.properties.coupon
      if (couponWay === '5') { // 免运券跳转水果外卖
        this.triggerEvent('closeCouponPopup')
        return
      }
      // 是否显示核销码 (免运券不显示核销码 渠道来源只要是门店就展示核销码)
      const showQrcode = channelSeparation.indexOf('P') >= 0
      // 是否显示商品  (小程序可用的展示商品)
      const showGoods = channelSeparation.indexOf('X') >= 0

      // 如果需要展示核销码 则需要动态拼接折扣信息+门店可用信息
      if (showQrcode) {
        // couponWay：1.满减，3.立减，2.满折，4.立折，5.免运
        let limitStr= ''
        limitValue = Number((limitValue/100).toFixed(2)) || 0
        if(['1', '3'].includes(couponWay)) {
          limitStr = `满${limitValue}元可减${couponValueShow}元`
        }
        else if (['2', '4'].includes(couponWay)) {
          limitStr = `${couponValueShow}折，满${limitValue}元可用`
        }

        // 使用说明 ()
        if (limitStr) {
          useableCondition.unshift(limitStr)
        }
        useableCondition.push(couponTypeStr)
      }

      const couponObj = {
        ...this.properties.coupon,
        defineId,
        couponCode,
        showQrcode,
        showGoods,
        couponName,
        useableCondition
      }
      app.globalData.fruitCouponGoodsParams = couponObj
      wx.navigateTo({
        url: `/userA/pages/couponGoods/index`
      })
      wx.showTabBar()
      return
    },
    appUse() {
      common.commonObj.showModal('提示', '该券仅可在App使用', true, '下载App', '取消', (res) => {
        if (res.confirm) {
          util.navigateToDownLoadH5()
        } else if (res.cancel) {
          // 关闭下载app弹窗上报神策
          const sensorskey = this.properties.appPopupCloseSensorskey
          sensorskey && sensors.track('MPClick', sensorskey)
        }
      })
    },
    // 会员续费
    navigateRenewPage() {
      jumpH5Vip('memberRenewUrl')
    },
    // 会员购买
    navigateOpenPage() {
      jumpH5Vip('memberBuyUrl')
    },
  }
})
