const request = require('../utils/request')
const config = require('../utils/config')
const commonObj = require('../source/js/common').commonObj
let cacheDialogCoupon = {} // 记录是否为第一次有新客弹窗返回

// 在文件顶部添加缓存对象和缓存时间配置
const requestCache = new Map() // 请求缓存
const CACHE_EXPIRE_TIME = 2000 // 缓存有效期2秒

// 封装缓存请求方法
const cacheRequest = (key, requestFn) => {
  const now = Date.now()
  const cachedData = requestCache.get(key)
  
  if (cachedData && (now - cachedData.timestamp < CACHE_EXPIRE_TIME)) {
    return Promise.resolve(cachedData.data)
  }

  const promise = requestFn()
  promise.then(data => {
    console.log(data)
 
    requestCache.set(key, {
      timestamp: now,
      data
    })
  })
  
  return promise
}

module.exports = {
   // 用户相关信息
   getCustomerInfo ({ customerID, isLoading = true}) {
    return request.get({ url: `/api/v1/customer/detail/information/${customerID}`, encryptFlag: true, isEncryptRes: true, isLoading })
  },
  // 获取下单充值引导活动信息
  rechargeGuide ({storeID, amount, customerID}) {
    return request.get({ url: `/api/v1/customer/activity/queryDepositActivity/${storeID || -1}/${amount}/${customerID}`, isLoading: false })
  },
  // 会员账户余额查询（旧接口）
  getBalance (data) {
    const { userID, cityID, storeID, isReturnLoginOutError = false, isLoading = false } = data
    return request.get({ url: `/api/v1/customer/getBalanceNew/${userID || -1}/${cityID || -1}/${storeID || -1}`, isLoading, isReturnLoginOutError})
  },
  // 会员账户余额查询
  getBalanceNew (data) {
    return request.post({ url: '/wxapp/member/v2/getBalanceNew', data, isLoading: false})
  },
  // 会员账户余额/积分/券数量查询
  getUserAmount (data) {
    if (!data.customerID || Number(data.customerID) === -1) {
      return Promise.reject('未登录用户无法查询余额信息')
    }
    return request.post({ url: '/dskhd/api/member/account/v1/getUserAmount', encryptFlag: true, encryptType: 'token', isEncryptRes: true, data, isLoading: false})
  },
  async getEshopNewCustomerGift(data) {
    return cacheRequest('getEshopNewCustomerGift', () => {
      return this.getEshopNewCustomerGiftRequest(data)
    })
  },
  // 获取电商新用户注册优惠券列表
  async getEshopNewCustomerGiftRequest(data) {
    const user = wx.getStorageSync('user') || {}
    const { isEshopNewCustomer = true } = user
    // 非新客不调用此接口
    if (!isEshopNewCustomer) {
      return {}
    }
    const res = await request.post({ url: '/wxapp/homePage/v2/getNewUserCoupons', data, isLoading: false})
    if (res.data) {
      const { couponList: dialogCouponList = [] } = res.data.dialogCoupon || {} // 新客弹窗 只在领取那次返回
      const { couponList: areaCouponList = [] } = res.data.specialAreaCoupon || {} // 新客区域
      if (dialogCouponList.length) {
        cacheDialogCoupon = res.data.dialogCoupon
      }
      if (cacheDialogCoupon.couponList && areaCouponList.length && !dialogCouponList.length) {
        // 没有弹过新客弹窗
        if (!getApp().globalData.isShowNewCouponDialog) {
          res.data.dialogCoupon = {
            couponList: cacheDialogCoupon.couponList.map( item => { return {...item} }),
            wxminiCardCoupon: cacheDialogCoupon.wxminiCardCoupon
          }
          cacheDialogCoupon = {}
        }
      }
    }
    return res
  },
  // 获取综合专题活动
  getActivityDetail (data) {
    return request.post({ url: '/wxapp/subject/v2/getActivityDetail', data})
  },
  // 获取综合专题活动 由及时达到到次日达
  getActivityBgxxDetail (data) {
    return request.post({ url: '/wxapp/subject/v3/getActivityDetail', data})
  },
  getCouponInTopic(customerID,data) {
    return request.post({ url:`/api/v1/activity/together/coupon/receive/${customerID}`,data})
  },
  // 获取综合专题优惠券模块
  getSubjectCouponModule (data) {
    return request.post({ url: '/wxapp/subject/v2/getcouponModule', data, isLoading: false })
  },
  // 综合专题领券
  receiveCouponBySubject (data) {
    return request.post({ url: '/wxapp/coupon/fruit/v1/receiveCouponBySubject', data})
  },
  // 获取离线会员码消费后展示消费金额
  getLocalCodePaymentInfo (data) {
    return request.post({ url:`${config.baseUrl.PAGODA_DSN_DOMAIN}/customerManager/getLocalCodePaymentInfo`, data})
  },
  // v2.4 充值活动数据
  queryRechargeActivityList ({ userID, storeID }, isLoading = true) {
    return request.post({ url: '/api/v1/recharge/queryRechargeActivityList', data: { customerID: userID, storeID, channel: 'X' }, isLoading })
  },
  // 自定义充值获取文案信息(已废弃)
  queryRechargeDescriptor ({ userID, amount, storeID, activityCode }) {
    return request.post({ url: '/api/v1/recharge/queryRechargeDescriptor', data: { customerID: userID, money: amount, storeID, activityCode, channel: 'X' }, isLoading: false })
  },
  // v2.4.1 果粒值，会员等级范围（会员）
  getMemberLevelSchedule ({customerID}) {
    return request.get({ url: `/api/v1/customer/getMemberLevelSchedule/${customerID}`, isLoading: false })
  },
  // v2.4.1 滚动消息通知（会员）
  getNotifyMsg ({customerID, storeID}) {
    return request.get({ url: `/api/v1/customer/notifyMsg/${customerID}/${storeID}`, isLoading: false })
  },
  // v2.4.1 任务列表（会员）
  getTasks ({customerID, storeID}) {
    return request.get({ url: `/api/v1/customer/getTasks/${customerID}/${storeID}`, isLoading: false })
  },
  // v2.4.1 获取用户等级/权益信息（会员）
  getMemberLevel (customerID) {
    return request.get({ url: `/api/v2/customer/memberLevel/${customerID}`})
  },
  // v5.2.4 根据会员和状态查询冻结记录
  queryLockingRecords (data) {
    return request.post({ url: '/dskhd/api/member/account/v1/queryLockingRecords', data, isLoading: true })
  },
  getDuibaUrl (data) {
    return request.post({ url: '/api/v1/customer/integral/duiba/redirectDuiBaChildURL', data, isLoading: false})
  },
  // 获取兑吧积分商城url
  getDuibaIntegralUrl({userID}) {
    return request.get({ url: `/api/v1/customer/integral/duiba/${userID || -1}` })
  },
  // 查询会员等级信息，成长值
  queryMemberLevelSchedule(data) {
    return request.post({ url: '/member/queryMemberLevelSchedule', data, isLoading: false })
  },
  // 在线充值查询活动
  getRechargeActList(data) {
    return request.post({ url: '/dskhd/api/member/account/v1/getRechargeActList', data, isLoading: true })
  },
  // 充值页广告位
  getRechargeAd(data) {
    return request.post({ url: '/dskhd/api/member/recharge/v1/getRechargeAd', data, isLoading: false })
  },
  // 在线充值(废弃)
  rechargeCheck(data) {
    return request.post({ url: '/api/v1/recharge/rechargeCheck', data, isLoading: false })
  },
  // 在线充值下单
  createRechargeOrder(data) {
    return request.post({ url: '/dskhd/api/member/recharge/v1/createOrder', data, isLoading: false })
  },
  // 查询充值订单是否支付
  checkRechargeOrderIsCancel(data) {
    return request.post({ url: '/dskhd/api/member/recharge/v1/checkOrderIsCancel', data, isLoading: false })
  },
  // 取消充值未支付的订单
  cancelTradeUnpaidOrder(data) {
    return request.post({ url: '/dskhd/api/trade/v1/cancelUnpaidOrder', data, isLoading: false })
  },
  // 获取充值规则文案
  getRechargeRuleText(data) {
    return request.get({ url: '/dskhd/api/member/recharge/v1/getRechargeRuleText', data, isLoading: false })
  },
  // 支付
  payRequest(options = {}) {
    const { data = {} } = options || {}
    return request.post({ url: `${config.baseUrl.PAGODA_EMS_DOMAIN}/wxmini/api/v1/pay/request/${data.customerID}`, ...options, isLoading: false, encryptFlag: true, encryptType: 'token', isEncryptRes: true })
  },
  // 获取任务中心数据
  getTaskCenterInfo({customerID, storeID}) {
    return request.get({ url: `/api/v1/customer/taskList/${customerID}/${storeID}`, isLoading: false })
  },
  // 领取任务积分
  receiveTaskIntegral(data) {
    return request.post({ url: '/api/v1/customer/task/receiveTaskIntegral', data, isLoading: false })
  },
  // 获取签到详情
  getCheckInDetail({customerID}) {
    return request.get({ url: `/api/v1/wxmini/signIn/detail/${customerID}`, isLoading: false })
  },
  // 去签到
  toCheckIn({customerID}) {
    return request.get({ url: `/api/v1/wxmini/signIn/customer/${customerID}`, isLoading: false })
  },
  // 获取会员余额明细
  getBalanceDetail ({customerId, pageNum, pageSize}) {
    return request.get({ url: `${commonObj.PAGODA_EMS_DOMAIN}/wxmini/api/v1/member/getBalanceDetail/${customerId}/${pageNum}/${pageSize}` })
  },
  // 获取会员账户余额
  getMemberBalance ({ customerID }) {
    return request.get({ url: `/api/v1/customer/getBalance/${customerID}` })
  },
  getTasksbannerList( { customerID, cityID, storeID }) {
    return request.get({ url: `/api/v2/banner/${customerID}/${cityID}/${storeID}/taskCenter`, isLoading: false })
  },
  // 防黑产实名认证信息提交
  submitInfoCertification(options){
    return request.post({ url:'/wxapp/user/v1/submitInfoCertification', ...options})
  },
  // 登录接口
  userLogin (options = {}) {
    return request.post({ url:`${config.baseUrl.PAGODA_EMS_DOMAIN}/customer/api/v2/login/wxMini`, ...options })
  },
  // 登出接口
  userLogout (data) {
    console.log('111',data)
    return request.post({ url:'/wxapp/member/v1/logout', data })
  },
  // 静默登录接口
  silentLogin(data) {
    return request.post({ url:'/wxapp/member/v1/silentLogin', data, encryptFlag: true, encryptType: 'pwd' })
  },
  getMemberCodeKey(options = {}) {
    return request.post({ url:`${commonObj.PAGODA_EMS_DOMAIN}/wxmini/api/v1/member/receiveMemberCodeKey`, ...options , isLoading: false })

  },
  // 获取优惠券数量（旧接口）
  getCouponNum(customerID) {
    return request.get({ url: `/api/v1/coupon/count/${customerID}` })
  },
  // 获取优惠券组数量
  getCouponGroupNum(data) {
    return request.post({ url: '/dskhd/api/member/coupon/v1/getCouponGroupNum', data })
  },
  // 获取指定业务下优惠券组数量
  getCouponCountByBiz(data) {
    return request.post({ url: '/dskhd/api/member/coupon/v1/getCouponCountByBiz', data })
  },
  // 获取所有业务类型下未使用优惠券数量(用于首页展示即将过期数量、我的页卡券数量)
  getCouponTotalCount(data) {
    return request.post({ url: '/dskhd/api/member/coupon/v1/getCouponTotalCount', data })
  },
  // 获取精选食材(素生鲜)优惠券列表（旧接口）
  getFoodsCoupons(data = {}) {
    const { customerID } = data
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/tomorrow/coupon/listByStatus/${customerID}`, data })
  },
  // 获取百果园+优惠券列表（已废弃）
  getFruitCoupons(options){
    return request.post({ url: '/wxapp/coupon/fruit/v1/getNotUsedCouponList', data: options})
  },
  // 获取优惠券列表（切中台，一个方法搞定）
  getNotUsedCouponList(data){
    return request.post({ url: '/dskhd/api/member/coupon/v1/getCouponList', data })
  },
  // 获取历史优惠券(已废弃)
  /*
  getHistoryCoupons(data = {}) {
    const { customerID } = data
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/history/coupon/listByStatus/${customerID}`, data })
  },
  */
  // 获取历史优惠券
  getHistoryCoupons(data = {}) {
    return request.post({ url: '/dskhd/api/member/coupon/v1/getHistoryCouponList', data })
  },
  // 兑换优惠券（非灰度，即将废弃）
  exchangeCoupon(params){
    return request.post({ url: '/api/v1/customer/coupon/redeem' , data: params})
  },
  // 兑换优惠券（中台，包含电商）
  redeemCoupon(data, isLoading = false){
    return request.post({ url: '/wxapp/coupon/fruit/v1/redeemCoupon', data, isLoading})
  },
  // 获取客服帮助列表
  getUserSupportList () {
    return request.get({ url: '/wxapp/member/getHelpServiceList' })
  },
  // 获取用户小红点
  getRedDot ({ customerID }) {
    return request.get({ url:`${commonObj.PAGODA_APP_DOMAIN}/api/v1/customer/common/redDot/${customerID}` })
  },
  // 获取水果优惠券适用商品
  getCouponGoodsData (data, isLoading = false) {
    return request.post({ url: '/wxapp/coupon/fruit/v2/getCouponGoods', data, isLoading})
  },
  getFruitCouponGoodsData (data, isLoading = false) {
    return request.post({ url: '/dskhd/api/fruit/goods/v1/getCouponGoodsList', data, isLoading})
  },
  // 获取会员页顶部心享会员广告位配置
  getAdList (data, isLoading = false) {
    return request.post({ url: '/wxapp/member/v1/getBgxxVipAdConfig', data, isLoading})
  },
  // 获取会员码页面到期提醒
  getMemberCodeVipTips (data, isLoading = false) {
    return request.post({ url: '/wxapp/member/v2/getMemberCodeVipTips', data, isLoading})
  },
  // 中台接口获取钱包余额
  getMemberBalanceByMiddle (data) {
    return request.post({ url: '/wxapp/user/v1/getMemberBalance', data})
  },
  // 获取意见反馈的问题类型（废弃）
  getfeedbackConfig (data) {
    return request.post({ url: '/wxapp/feedback/v2/getFeedbackConfig', data })
  },
  // 获取意见反馈的问题类型
  getFeedbackConfig2 (data) {
    return request.post({ url: '/dskhd/api/config/v1/getBaseDic', data })
  },
  // 获取意见反馈的问题类型
  getHistoryFeedback (data) {
    return request.post({ url: '/dskhd/api/member/feedback/v1/getHistoryFeedback', data })
  },
  // 意见反馈不满意回复打分和评价
  updateFeedbackScore (data) {
    return request.post({ url: '/dskhd/api/member/feedback/v1/updateFeedbackScore', data })
  },
  // 存储用户的意见反馈信息
  saveFeedbackInfo (data, isLoading = false) {
    return request.post({ url: '/wxapp/feedback/v2/saveFeedbackInfo', data, isLoading })
  },
  subscribeGood (data, isLoading = true) {
    return request.post({ url: '/wxapp/activity/limitedTimeSpecialActivities/v1/subscribeGood', data, isLoading })
  },
  // 用户注销接口
  userLogoff(data, loginOutBackHomePage = true){
    return request.post({url:`${config.baseUrl.PAGODA_SMS_DOMAIN}/customer/api/v1/member/logoff/${data.customerId}`, data, loginOutBackHomePage})
  },
  // 验证验证码
  verifyCode (data, loginOutBackHomePage = true) {
    return request.post({url:`${config.baseUrl.PAGODA_SMS_DOMAIN}/customer/api/v1/sms/verify`, data, loginOutBackHomePage})
  },
  // 获取阿波罗配置的注销原因
  getLogoffReason (loginOutBackHomePage = true) {
    return request.get({url:`${config.baseUrl.PAGODA_APP_DOMAIN}/api/v1/member/logoff/config`, isLoading: true, loginOutBackHomePage})
  },
  // 获取充值页信息
  queryRechargeActivityInfo (data, isLoading = true) {
    return request.post({ url: '/wxapp/user/v1/queryRechargeActivityList', data, isLoading })
  },
  // 获取充值送券信息
  getRechargeCouponInfo (data, isLoading = true) {
    return request.post({ url: '/dskhd/api/member/coupon/v1/getRechargeCouponInfo', data, isLoading })
  },
  getCosAuthorization (data, isLoading = false) {
    return request.post({ url: '/wxapp/auth/getCosAuthorization', data, isLoading })
  },
  // 好吃卡充值
  depositByCard(params) {
    return request.post({ url:`${config.baseUrl.PAGODA_SMS_DOMAIN}/customer/api/v1/pay/card/deposit/${params.customerID}`, data: params, isLoading: false, encryptFlag: true, encryptType: 'token' })
  },
  // 代金券充值
  depositByVoucher(params) {
    return request.post({ url: '/wxapp/user/v1/voucherRecharge', data: params, isLoading: false, encryptFlag: true, encryptType: 'token' })
  },
  // 获取微信信息
  getWxSns(params) {
    return request.post({ url:`${config.baseUrl.PAGODA_DSN_DOMAIN}/api/v1/wechat/wxSns`, data: params, isLoading: false })
  },
  // 获取登录信息
  getLoginInfoRequest(params) {
    return request.post({ url:'/dskhd/api/member/login/getLoginInfo', data: params, isLoading: false })
  },
  //获取月卡/年卡信息
  getVipCardInfo(params) {
    return request.post({ url:'/wxapp/trade/fruit/v3/getVipCardInfo', data: params, isLoading: false })
  },
  getPhoneNumber(params) {
    return request.post({ url: `${config.baseUrl.PAGODA_DSN_DOMAIN}/api/v1/wechat/phoneNumber/get`, data: params, isLoading: false })
  },
  // 查询收藏 门店
  queryCollectStore(params) {
    return request.post({ url: '/wxapp/user/collectionStore/queryAll', data: params })
  },
  // 更新收藏门店
  updateCollectStore(params) {
    return request.post({ url: '/wxapp/user/collectionStore/update', data: params })
  },
  updateUserStore(params) {
    return request.post({ url: '/wxapp/user/v1/updateStore', data: params })
  },
  reportTmplIds(params) {
    return request.post({ url: '/wxapp/user/v1/reportTmplIds', data: params, isLoading: false })
  },
  // 获取用户第一次下单信息
  getFirstOrderInfo(userID) {
    return request.get({ url: `${config.baseUrl.PAGODA_DSN_DOMAIN}/api/v1/customer/statistical/${userID}`, data: {}, isLoading: false })
  },
  // 上报订阅消息属性
  reportSubProps(params) {
    return request.post({ url: '/wxapp/user/v1/reportSubProps', data: params, isLoading: false })
  },
  /**
   * @desc 获取门店附近的用户收货地址
   * @param {object} params
   * @param {string} params.customerID
   * @param {string} params.lat
   * @param {string} params.lon
   * @returns {Promise<{any}>}
   */
  getAddressByDistance(params) {
    return request.post({ url: '/dskhd/api/member/address/getAddressByDistance', data: params, isLoading: false, isEncryptRes: true })
  },

  getWxCodeUnlimit(params) {
    return request.post({ url: '/dskhd/api/wechat/getWxCodeUnlimit', data: params, isLoading: false })
  },
  /**
   * 领取首次定级权益（触发初始化领券）
   * @param {*} data
   * @returns
   */
  receiveFirstGradingEquity(data) {
    return request.post({ url: '/dskhd/api/member/coupon/v1/receiveFirstGradingEquity', data, isLoading: false })
  },
}
