<wxs module="common" src="../../../../utils/common.wxs"></wxs>
<import src="../../../../components/goods/template/index.wxml" />
<view class="product-card"  catchtap="navigateToGoodsdetail">
  <block>
    <view class="goods-mask" wx:if="{{ renderGoods.stockNum === 0 }}"></view>
  </block>
  <view class="product-info">
    <!-- 商品图片 -->
    <view class="product-image-box">
      <count-down
        wx:if="{{renderGoods.activitySource === 'S'}}"
        activityEndTime="{{renderGoods.activityEndTime}}"
        systemTime="{{renderGoods.systemTime}}"
        bindcomplete="handleCountComplete"
        size="small"
        content="门店特价"
       ></count-down>
      <image class="product-image"  src="{{common.formatWebp(common.getImageUrl(picUrl,renderGoods.headPic)) || ''}}" mode="aspectFill"></image>
      <view
        wx:if="{{ renderGoods.stockNum === 0 }}"
        class="pos-out horizontal-out">售罄</view>
    </view>
    
    <view class="product-details">
      <view>
        <!-- 商品标题 --> 
        <view class="product-title goods-name text-hidden-line2 lineclamp2">
          <goods-level class="c-goods-level" text="{{renderGoods.goodsLevel || ''}}" size="small"></goods-level>
          <text>{{ common.goodsLevelFormat(renderGoods.goodsName) }} {{common.goodsLevelFormat(renderGoods.specDesc)}}</text>
        </view>
        <!-- <view class="goods-subtitle text-hidden" wx:if="{{renderGoods.subtitle}}">{{ renderGoods.subtitle }}</view> -->
        <!-- 促销信息 -->
        <view class="promotion-info">
          <label-list style="--label-margin-top: 6rpx" class="label-list" labelList="{{labelList}}"></label-list>
        </view>
      
      </view>
      
      <!-- 价格信息 -->
      <view class="price-bottom">
        <view class="price-section">
          <view class="save-price" wx:if="{{saveMoneyText}}">
            <view class="save-price-box-pre"></view>
            <view class="save-price-box">{{saveMoneyText}}</view>
          </view>
          <view class="price-info">
            <text class="current-price">{{common.filtersYuan(goodsObj.activityPrice)}}</text>
            <text class="current-price-fen">{{common.filtersFen(goodsObj.activityPrice)}}</text>
            <text class="original-price">¥{{common.formatPrice(goodsObj.retailPrice)}}</text>
          </view>
        </view>
        <view class="goods-cart-box">
          <operate-cart
            wx:if="{{!renderGoods.specificationGoodsList || !renderGoods.specificationGoodsList.length}}"
            pic-cart-class="pic-cart"
            goodsObj="{{goodsObj}}"
            goodsCount="{{goodsCount}}"
            bind:updateCount="getCartCount"
            bind:updateCartInfo="updateCartInfo"
            bind:updateCarList="getCarList"
            bind:updateGoodsCount="updateGoodsCount"
            showTipAndNoAdd="{{true}}"
            bind:startAnimation="startAnimation"
            addSensorskey="{{addSensorskey}}"
            subSensorskey="{{subSensorskey}}"
            isShowAnimation="{{isShowAnimation}}"
            initStyle="priceGoods"
            isShowCount="{{isShowCount}}"
            fromShareTimeLine="{{fromShareTimeLine}}"
            activityObj="{{activityObj}}"
          ></operate-cart>
          <template wx:else is="cart-box" data="{{renderGoods: goodsObj, goodsCount,goodsCountStr, fromShareTimeLine, cartType: 8, isShowCount: true}}"></template>
        </view>
      </view>
      
    </view>
  </view>
</view>