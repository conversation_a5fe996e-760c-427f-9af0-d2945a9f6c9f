// import {mallApi,commonApi} from '../../api/index'
const app = getApp();
const coordtransform = require('../../../../../utils/coordUtil')
const locateService = require('../../../../../utils/services/locate')
Component({
    /**
     * 组件的属性列表
     */
    properties: {
      //城市列表
      data: {
        type: Array,
        value: [],
        observer: function (newVal, oldVal) {
          let data = newVal; //组件接收的参数 城市列表
          this.resetRight(data);  // 得到右侧的分组 字母["A","B"]
          this.getHeight(data);  //的到每一组分类到 滚动起始点的距离
          this.getCityById(data,'selectCurrentCity') //查找当前选中城市
        }
      },
      myCity: { //当前我所在的城市 Object
        type: Object,
        value: {},
      },
      currentClickCityId: {  //当前选中的城市
        type: String,
        value: '',
      },
      // 用于外部组件搜索使用
      search:{
        type:String,
        value:"",
        observer: function (newVal, oldVal) {
          this.value = newVal;
          this.searchMt();
        }
      }
    },
    data: {
      selectedChar: '',
      inputValue: '',
      list: [],
      rightArr: [],// 右侧字母展示
      jumpNum: '',//跳转到那个字母
      screenHeight:wx.getSystemInfoSync().screenHeight,
      scrollTimeOut: -1,
      isScroll:true,
      currentClickCity: {},
      isClearInput: false,
    },
    methods: {
      // 数据重新渲染
      resetRight(data) {
        let rightArr = []
        data.map((item)=>{
          rightArr.push(item.title.substr(0, 1)); //取出 大写字母 ["A","B"]
        })
        this.setData({
          list: data,
          rightArr
        })
      },
      //过滤热门城市
      // filterHotCity(data){
      //   let arr = [];
      //   //根据首字母过滤城市列表
      //   for (let i = 0; i < data.length; i++) {
      //     for (let j = 0; j < hotCityKey.length; j++) {
      //       if(data[i].title === hotCityKey[j]){
      //         arr = arr.concat(data[i].item)
      //       }
      //     }
      //   };
      //   // 根据城市名匹配热门城市
      //   let hotList = arr.filter(item=>hotCityList.includes(item.cityName))
      //   this.setData({
      //     hotCityList:hotList
      //   })
      // },
      getCityById (data,target) {
        for (let i = 0; i < data.length; i++) {
          for (let j = 0; j < data[i].item.length; j++) {
            var str = data[i].item[j];
            if (target === 'selectMyCity') {
              if (str.id && (Number(str.id) === Number(this.data.myCityId))) {
                this.setData({
                  myCity: str
                })
                break;
              }
            }
            if (target === 'selectCurrentCity') {
              if (str.id && (Number(str.id) === Number(this.data.currentClickCityId))) {
                this.setData({
                  currentClickCity: str
                })
                this.getSelectedCharByKey(this.data.currentClickCity.key);
                break;
              }
            }
          }
        }
      },
      // 右侧字母点击事件
      clickCapital(e) {
        let jumpNum = e.currentTarget.dataset.id;
        this.setData({jumpNum:jumpNum, selectedChar: jumpNum,isScroll:false});
      },
      // 列表点击事件
     async clickCity(e) {
        const cityInfo = e.currentTarget.dataset;
        if (cityInfo.type) {  // 商城首页点切换城市如果cityID为空 重新调获取cityID接口
          const params = {
            cityName: cityInfo.detail.cityName,
            lat: cityInfo.detail.location.lat, //纬度
            lon: cityInfo.detail.location.lng//经度
          }
          await app.api.checkBgxxIsSupportVip(params).then(res => {
            if (!!res.data) {
              const { deliveryCenterCode, code: cityCode, cityID } = (res.data.city || {})
              Object.assign(cityInfo.detail, {
                cityID,
                cityCode,
                deliveryCenterCode
              })
            }
          })
        }
        this.setData({ //设置当前选中城市
          currentClickCity: cityInfo.detail
        })
        this.getSelectedCharByKey(this.data.currentClickCity.key);
        this.triggerEvent('myevent',this.data.currentClickCity)
      },
      getSelectedCharByKey (key) {
        this.data.rightArr.map((item,index)=>{
          if (item === key) {
            this.setData({
              selectedChar: `index${index}`,
              jumpNum: `index${index}`,
              isScroll: false
            })
          }
        })
      },
      // 获取搜索输入内容
      input(e) {
        if (this.data.isClearInput) {
          return
        }
        this.value = e.detail.value;
        this._search();
        this.setData({
          inputValue:e.detail.value,
        })
      },
      focusHandle () {
        this.data.isClearInput = false
      },
      clear() {
        this.value = '';
        this.setData({
          inputValue:''
        })
        this._search();
        this.data.isClearInput = true
      },
      // 基础搜索功能
      searchMt() {
        clearTimeout(this.data.scrollTimeOut)
        this.data.scrollTimeOut=setTimeout(() => {
          this._search();
        }, 1000);
      },
      _search(){
        let data = this.data.data;
        let newData = [];
        for (let i = 0; i < data.length; i++) {
          let itemArr = [];
          for (let j = 0; j < data[i].item.length; j++) {
            var str = data[i].item[j];
            if ((new RegExp(`^${this.value}.*$`).test(str.cityName))||(new RegExp(`^${this.value}.*$`,'i').test(str.pinyin))) {
              let itemJson = {};
              for (let k in str) {
                itemJson[k] = str[k];
              }
              itemArr.push(itemJson);
            }
          }
          if (itemArr.length === 0) {
            continue;
          }
          newData.push({
            title: data[i].title,
            type: data[i].type ? data[i].type : "",
            item: itemArr
          })
        }
        this.resetRight(newData);
      },
      // 城市定位
      locationMt() {
        // 定位自己的城市，需要引入第三方api
      },
      scrollEvent (e) {
        if (this.data.isScroll) {
          this.setSelectedChar(e)
        }
        this.setData({
          isScroll: true
        })
      },
      setSelectedChar(e) {
        for (let i =0; i< this.data.arrToTopHeight.length;i++) {
          if (e.detail.scrollTop<this.data.arrToTopHeight[i].height) {
            this.setData({
              selectedChar: `index${i}`
            })
            break;
          }
        }
      },
      getHeight (data) {
        var newArray= []
        var l= 95
        for (let i = 0; i < data.length; i++) {
          l += data[i].item.length *  44
          var obj = {
            key: data[i].title,
            height: l
          }
          newArray.push(obj)
        }
        this.setData({
          arrToTopHeight: newArray
        })
      },
      /**
       * 重新定位
       */
      currentLocation: app.subProtocolValid('shop', async function() {
        try {
          await this.locateCurrAddress()
          const { cityName = '', location: { lng = '', lat = ''} = {}, address = '' } = wx.getStorageSync('userCurrLoca') || {}
          const bdLocation = coordtransform.gcj02tobd09(lng, lat) || []
          const params = {
            cityName,
            lat: bdLocation[1] || -1,
            lon: bdLocation[0] || -1,
            address,
          }
          if(!!params.cityName){
            this.setData({
              myCity:params
            })
            this.triggerEvent('resetAddress',params)
          }else{
            const res = await app.showModalPromise({
              content: `无法获取当前定位城市`,
              showCancel: false,
              confirmText: '我知道了',
            })
          }
        } catch (error) {
          console.log("getCurrentCity error",error)
        }
      }),
      /**
       * 定位当前地址
       */
      async locateCurrAddress() {
        try {
          const locationInfo = await locateService.getGeoLocation()
          await locateService.getCityName(locationInfo.latitude, locationInfo.longitude)
        } catch (error) {
          if (!app.globalData.userLocationAuth) {
            const res = await locateService.handleWXLocationErr(error.errMsg)
            if (res) {
              this.setData({
                refresh:true
              })
            } else{
              wx.showToast({
                title: '未成功获取到您的定位授权，刷新失败!',
                icon: 'none'
              })
            }
            throw new Error()
          }
        }
      },
       /**
       * 地址授权后刷新当前城市
       */
      async refreshAddress(){
        try {
          const locationInfo = await locateService.getGeoLocation()
          await locateService.getCityName(locationInfo.latitude, locationInfo.longitude);
          const { cityName = '', location: { lng = '', lat = ''} = {}, address = '' } = wx.getStorageSync('userCurrLoca') || {}
          const bdLocation = coordtransform.gcj02tobd09(lng, lat) || []
          const params = {
            cityName,
            lat: bdLocation[1] || -1,
            lon: bdLocation[0] || -1,
            address,
          }
          if(!!params.cityName){
            this.setData({
              myCity:params
            })
            this.triggerEvent('resetAddress',params)
          }else{
            const res = await app.showModalPromise({
              content: `无法获取当前定位城市`,
              showCancel: false,
              confirmText: '我知道了',
            })
          }
        } catch (error) {
          locateService.handleWXLocationErr(error.errMsg)
        }
      }
    }
  })
