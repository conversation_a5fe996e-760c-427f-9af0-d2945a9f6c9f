import { getSendDetail, giftCardSendOperate } from '../../api/index'
import util from '../../../utils/util'
import { giftCardReceiveTemplateIds, reportTmplIds } from '../../../mixins/ticketEntry'
import { onShareAppMessage } from '../../utils/send'
import { getPromiseObj } from '../../../utils/promise'
import { OperateType } from '../../../sub-common/types/giftCard/type'
import sensors from '../../../utils/report/sensors'

const app = util._getApp()
const defaultWish = '送你一份心意~'

Component({
  data: {
    getCode: '',
    customerID: '',
    defaultWish,
    wish: '',
    sendLoading: false,
    cancelLoading: false,
    cardInfo: {
      number: '',
      cover: '',
      name: '',
      amount: 0,
    },
    showPopup: false,
    showBackPopup: false,
    backPopupText: '',
    subscribe: {
      show: false,
      tmplIds: giftCardReceiveTemplateIds,
    },
    // 发送分享时使用此
    activityId: '',
    giveCode: '',
  },
  methods: {
    onTapWish() {
      sensors.clickReport({
        element_content: '赠言编辑',
        element_name: '赠言编辑',
        element_code: '1162000001',
      })
    },
    onChangeWish() {
      this.setData({
        wish: this.data.wish.replace(/\r?\n/g, '').trim().slice(0, 200),
      })
    },
    inputWish({ detail }) {
      this.setData({
        wish: detail.value,
      })
    },
    async sendReq() {
      this.onChangeWish()
      const { sendLoading, getCode, wish, customerID } = this.data
      if (sendLoading) {
        return
      }
      if (wish && util.checkEmoji(wish)) {
        wx.showToast({
          title: '仅支持输入中文、数字、英文大小写和常规符号',
          icon: 'none',
        })
        return
      }
      sensors.clickReport({
        element_content: '提交赠送',
        element_name: '提交赠送',
        element_code: '1162000002',
      })
      this.setData({
        sendLoading: true,
      })
      const { data, error } = await giftCardSendOperate({
        customerID,
        getCode,
        wish,
      }).catch(error => ({ error }))
      this.setData({
        sendLoading: false,
      })
      if (error && error.errorCode) {
        error.errorCode === 403 && sensors.exposureReport({
          element_content: '赠言违规',
          element_name: '赠言违规',
          element_code: '1162000005',
        })
        error.errorCode === 404 ? this.showBackDialog(error) : wx.showToast({
          title: error.description,
          icon: 'none',
        })
        return
      }
      this.setData({
        showPopup: true,
        activityId: data.activityId,
        giveCode: data.giveCode,
      })
      app.event.emit('refreshGiftCardList')
    },
    showSubscribe() {
      sensors.clickReport({
        element_content: '确认赠送弹窗',
        element_name: '确认赠送弹窗',
        element_code: '1162000003',
      })
      this._data.sharePromise = getPromiseObj()
      const subscribePromise = getPromiseObj()
      this._data.subscribePromise = subscribePromise
      this.setData({
        showPopup: false,
        'subscribe.show': true,
      }, () => subscribePromise.resolve())
    },
    toSendDetail() {
      const { giveCode, activityId } = this.data
      wx.redirectTo({
        url: `/giftCard/pages/forward/index?giveCode=${giveCode}&activityId=${activityId}${this._data.from ? `&from=${this._data.from}` : ''}`,
      })
    },
    async cancelSend() {
      if (this.data.cancelLoading) {
        return
      }
      sensors.clickReport({
        element_content: '弹窗取消赠送',
        element_name: '弹窗取消赠送',
        element_code: '1162000004',
      })
      this.setData({
        cancelLoading: true,
      })
      const { customerID, cardInfo } = this.data
      const { data = { sendInfo: {} }, error } = await giftCardSendOperate({
        customerID: customerID,
        cardNumber: cardInfo.number,
        operateType: OperateType.主动撤销赠送,
      }).then(({ data }) => ({ data })).catch(error => ({ error }))
      this.setData({
        cancelLoading: false,
      })
      data.sendInfo.status === OperateType.领取 && this.toSendDetail()
      error ? wx.showToast({ title: error.description, icon: 'none' }) : this.closeSendPopup()
    },
    closeSendPopup() {
      this.setData({
        showPopup: false,
        'subscribe.show': false,
      })
    },
    onSendClose({ detail = {} }) {
      this.closeSendPopup()
      detail.resultStatus && reportTmplIds(detail.resultStatus)
      this._data.sharePromise.promise.then(() => this.toSendDetail())
    },
    onBackPopupTap() {
      wx.navigateBack()
    },
    showBackDialog(error) {
      sensors.exposureReport({
        element_content: '卡数量不足',
        element_name: '卡数量不足',
        element_code: '1162000006',
      })
      this.setData({
        showBackPopup: true,
        backPopupText: error ? error.description : '网络错误,请稍后再试',
      })
    },
    async getCardInfo() {
      wx.showLoading({
        mask: true
      })
      const { data, error } = await getSendDetail({
        getCode: this.data.getCode,
        customerID: this.data.customerID,
      }).catch((error) => ({ data: {}, error }))
      wx.hideLoading()
      if ((error && error.errorCode) || !data) {
        return this.showBackDialog(error)
      }

      this.setData({
        cardInfo: data.cardInfo,
        defaultWish: data.defaultWish || defaultWish,
      })
    },
    onLoad(options) {
      wx.updateShareMenu({
        isPrivateMessage: true,
      })
      wx.hideShareMenu()
      const { userID = '' } = wx.getStorageSync('user') || {}
      this.setData({
        customerID: userID,
        getCode: options.getCode,
      })
      this._data.from = options.from
      this.getCardInfo()
    },
    onShow() {
      console.log('this._data.sharePromise.resolve')
      this._data.sharePromise.resolve()
      if (this._data.lockOnShow) {
        this._data.lockOnShow = false
        return
      }
      sensors.pageScreenView()
    },
    onShareImageLoaded({ detail }) {
      this._data.shareImagePromise.resolve(detail.tempFilePath)
    },
    onShareAppMessage() {
      this._data.lockOnShow = true
      return onShareAppMessage(Object.assign({}, this.data, {
        wish: this.data.wish || this.data.defaultWish,
      }), this._data.subscribePromise.promise.then(() => this._data.shareImagePromise.promise))
    },
  },
  lifetimes: {
    created() {
      this._data = {
        sharePromise: getPromiseObj(),
        subscribePromise: getPromiseObj(),
        lockOnShow: false,
        shareImagePromise: getPromiseObj(),
        from: '',
      }
    },
  }
})