/**
 * 商品分享图片配置缓存管理器
 * 通过CDN方式获取goodsSharePictures.json配置文件，支持内存缓存
 */

import { COS_CONFIG_MAP, COS_GLOBAL } from '../../../source/const/cosConfig'
import { ENV } from '../../../utils/config'

// 内存缓存对象
let shareImageConfigCache = {
  // 配置数据
  data: null,
  // 缓存时间戳
  cacheTime: 0,
  // 是否正在加载
  loading: false,
  // 加载Promise，避免重复请求
  loadingPromise: null,
  // 上次加载是否失败
  lastLoadFailed: false
}

// 配置文件路径
const CONFIG_FILE_PATH = 'goodsSharePictures/goodsSharePictures.json'

// 缓存有效期：程序运行期间永久有效（除非加载失败）
const CACHE_DURATION = Infinity

class ShareImageCacheManager {
  constructor() {
    this.performanceMonitor = this.createPerformanceMonitor()
  }

  /**
   * 创建性能监控器
   */
  createPerformanceMonitor() {
    return {
      start: (label) => {
        const startTime = Date.now()
        console.log(`[ShareImageCache Performance] ${label}开始 - 时间: ${startTime}`)
        return startTime
      },
      end: (label, startTime, description = '') => {
        const endTime = Date.now()
        const duration = endTime - startTime
        const desc = description ? ` - ${description}` : ''
        console.log(`[ShareImageCache Performance] ${label}完成 - 耗时: ${duration}ms${desc}`)
        return duration
      }
    }
  }

  /**
   * 获取CDN域名
   * @returns {string} CDN域名
   */
  getCdnDomain() {
    const cosConfig = COS_CONFIG_MAP[COS_GLOBAL]
    const envConfig = cosConfig[ENV]
    
    if (!envConfig || !envConfig.domain) {
      console.warn(`[ShareImageCache] 未找到${ENV}环境的CDN域名配置，使用默认域名`)
      // 使用默认的COS域名
      return `https://${envConfig?.bucket || 'serverless-cdn-prod-1251596386'}.cos.${envConfig?.region || 'ap-guangzhou'}.myqcloud.com`
    }
    
    return envConfig.domain
  }

  /**
   * 构建配置文件的完整URL
   * @returns {string} 配置文件URL
   */
  buildConfigUrl() {
    const domain = this.getCdnDomain()
    const url = `${domain}/${CONFIG_FILE_PATH}`
    console.log(`[ShareImageCache] 配置文件URL: ${url}`)
    return url
  }

  /**
   * 检查缓存是否有效
   * @returns {boolean} 缓存是否有效
   */
  isCacheValid() {
    if (!shareImageConfigCache.data) {
      return false
    }
    
    // 如果上次加载失败，缓存无效
    if (shareImageConfigCache.lastLoadFailed) {
      return false
    }
    
    // 程序运行期间永久有效
    return true
  }

  /**
   * 通过wx.downloadFile下载配置文件
   * @returns {Promise<Object>} 配置数据
   */
  async downloadConfigFile() {
    const startTime = this.performanceMonitor.start('downloadConfigFile')
    const configUrl = this.buildConfigUrl()
    
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: configUrl,
        timeout: 10000, // 10秒超时
        success: (res) => {
          if (res.statusCode === 200) {
            this.performanceMonitor.end('downloadConfigFile', startTime, '下载成功')
            this.readDownloadedFile(res.tempFilePath)
              .then(resolve)
              .catch(reject)
          } else {
            const error = new Error(`下载失败，状态码: ${res.statusCode}`)
            this.performanceMonitor.end('downloadConfigFile', startTime, `下载失败: ${res.statusCode}`)
            reject(error)
          }
        },
        fail: (err) => {
          this.performanceMonitor.end('downloadConfigFile', startTime, `下载失败: ${err.errMsg}`)
          reject(new Error(`下载失败: ${err.errMsg}`))
        }
      })
    })
  }

  /**
   * 读取下载的文件内容
   * @param {string} filePath 临时文件路径
   * @returns {Promise<Object>} 解析后的JSON数据
   */
  async readDownloadedFile(filePath) {
    const startTime = this.performanceMonitor.start('readDownloadedFile')
    
    return new Promise((resolve, reject) => {
      const fs = wx.getFileSystemManager()
      
      fs.readFile({
        filePath: filePath,
        encoding: 'utf8',
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            
            // 检查数据是否为空
            if (!data || !data.goodsShareImgList || Object.keys(data.goodsShareImgList).length === 0) {
              this.performanceMonitor.end('readDownloadedFile', startTime, '文件内容为空')
              reject(new Error('配置文件内容为空'))
              return
            }
            
            this.performanceMonitor.end('readDownloadedFile', startTime, `解析成功，包含${Object.keys(data.goodsShareImgList).length}个配置`)
            resolve(data)
          } catch (parseError) {
            this.performanceMonitor.end('readDownloadedFile', startTime, `JSON解析失败: ${parseError.message}`)
            reject(new Error(`JSON解析失败: ${parseError.message}`))
          }
        },
        fail: (err) => {
          this.performanceMonitor.end('readDownloadedFile', startTime, `文件读取失败: ${err.errMsg}`)
          reject(new Error(`文件读取失败: ${err.errMsg}`))
        }
      })
    })
  }

  /**
   * 加载配置数据
   * @returns {Promise<Object>} 配置数据
   */
  async loadConfig() {
    const startTime = this.performanceMonitor.start('loadConfig')
    
    try {
      const data = await this.downloadConfigFile()
      
      // 更新缓存
      shareImageConfigCache = {
        data: data,
        cacheTime: Date.now(),
        loading: false,
        loadingPromise: null,
        lastLoadFailed: false
      }
      
      this.performanceMonitor.end('loadConfig', startTime, '加载成功')
      console.log(`[ShareImageCache] 配置加载成功，缓存${Object.keys(data.goodsShareImgList || {}).length}个商品分享图片配置`)
      
      return data
    } catch (error) {
      // 标记加载失败
      shareImageConfigCache.lastLoadFailed = true
      shareImageConfigCache.loading = false
      shareImageConfigCache.loadingPromise = null
      
      this.performanceMonitor.end('loadConfig', startTime, `加载失败: ${error.message}`)
      console.error(`[ShareImageCache] 配置加载失败:`, error)
      
      throw error
    }
  }

  /**
   * 获取商品分享图片配置
   * @returns {Promise<Object>} 配置数据
   */
  async getShareImageConfig() {
    const startTime = this.performanceMonitor.start('getShareImageConfig')
    
    // 检查缓存
    if (this.isCacheValid()) {
      this.performanceMonitor.end('getShareImageConfig', startTime, '使用缓存')
      console.log(`[ShareImageCache] 使用缓存数据`)
      return shareImageConfigCache.data
    }
    
    // 如果正在加载，等待加载完成
    if (shareImageConfigCache.loading && shareImageConfigCache.loadingPromise) {
      console.log(`[ShareImageCache] 正在加载中，等待完成`)
      try {
        const result = await shareImageConfigCache.loadingPromise
        this.performanceMonitor.end('getShareImageConfig', startTime, '等待加载完成')
        return result
      } catch (error) {
        this.performanceMonitor.end('getShareImageConfig', startTime, '等待加载失败')
        throw error
      }
    }
    
    // 开始加载
    shareImageConfigCache.loading = true
    shareImageConfigCache.loadingPromise = this.loadConfig()
    
    try {
      const result = await shareImageConfigCache.loadingPromise
      this.performanceMonitor.end('getShareImageConfig', startTime, '新加载完成')
      return result
    } catch (error) {
      this.performanceMonitor.end('getShareImageConfig', startTime, '新加载失败')
      throw error
    }
  }

  /**
   * 根据SPU编号获取分享图片URL
   * @param {string} spuNumber SPU编号
   * @returns {Promise<string>} 分享图片URL，如果没有则返回空字符串
   */
  async getShareImageBySpuNumber(spuNumber) {
    if (!spuNumber) {
      console.log(`[ShareImageCache] SPU编号为空`)
      return ''
    }
    
    const startTime = this.performanceMonitor.start(`getShareImageBySpuNumber_${spuNumber}`)
    
    try {
      const config = await this.getShareImageConfig()
      const shareImageUrl = config.goodsShareImgList[spuNumber] || ''
      
      this.performanceMonitor.end(`getShareImageBySpuNumber_${spuNumber}`, startTime, `找到配置: ${!!shareImageUrl}`)
      console.log(`[ShareImageCache] SPU ${spuNumber} 分享图片: ${shareImageUrl || '未找到'}`)
      
      return shareImageUrl
    } catch (error) {
      this.performanceMonitor.end(`getShareImageBySpuNumber_${spuNumber}`, startTime, '获取失败')
      console.error(`[ShareImageCache] 获取SPU ${spuNumber} 分享图片失败:`, error)
      return ''
    }
  }

  /**
   * 清空缓存（用于调试或强制刷新）
   */
  clearCache() {
    shareImageConfigCache = {
      data: null,
      cacheTime: 0,
      loading: false,
      loadingPromise: null,
      lastLoadFailed: false
    }
    console.log(`[ShareImageCache] 缓存已清空`)
  }

  /**
   * 获取缓存状态信息
   * @returns {Object} 缓存状态
   */
  getCacheStatus() {
    return {
      hasCache: !!shareImageConfigCache.data,
      cacheTime: shareImageConfigCache.cacheTime,
      loading: shareImageConfigCache.loading,
      lastLoadFailed: shareImageConfigCache.lastLoadFailed,
      configCount: shareImageConfigCache.data ? Object.keys(shareImageConfigCache.data.goodsShareImgList || {}).length : 0
    }
  }
}

// 导出单例
export default new ShareImageCacheManager()
