<!--pages/signIn/index.wxml-->
<!--用户授权页面-->

<view class="signin-warpper">
  <nav-bar
    background="#fff"
    color="#000"
    navBarTitle="欢迎登录百果园"
    style="position: relative"
    normalBack
  >
  </nav-bar>
  <view class="logo-container">
    <image class="logo" src="https://resource.pagoda.com.cn/dsxcx/images/c8509dfe9a34d7222985c9eafe0574a7.png"></image>
    <image class="slogan" src="https://resource.pagoda.com.cn/dsxcx/images/996e718bde528911288f27a42041f8ab.png" />
  </view>
  <block wx:if="{{showUserAuthorization}}">
    <view class="captions">您暂未授权百果园+小程序获取你的信息，将无法正常使用小程序的功能。如需要正常使用，请点击“授权”按钮，打开头像，昵称等信息的授权。</view>
    <view class="btn-row">
      <!-- 授权按钮 -->
      <button lang="zh_CN" bindtap="getUserProfile" class="btn bg-green btn-authorization">授权登录</button>
      <view class="btn btn-cancel" bindtap="cancelLogin">取消</view>
    </view>
  </block>
  <view class="btn-signin-wrapper" wx:else>
    <view class="tips-wrapper">
      <view bindtap="radioTapHandle" class="tips-radio" style="padding-left: 10rpx;">
        <radio value="r1" checked="{{isAgree}}"/>
      </view>
      <protocolLabel />
    </view>
    <view class="sign-btn-row">
      <block wx:if="{{isOneClickLogin}}">
        <button class="btn bg-green" bindtap="toOneClickLogin" disabled="{{isLogining}}">
          一键登录
        </button>
      </block>
      <block wx:else>
        <button open-type="{{isAgree ? 'getPhoneNumber':''}}" bindgetphonenumber="getPhoneNumber" class="btn bg-green" bindtap="handleGetPhone"  disabled="{{isLogining}}">
          <!-- <image class="icon" src="/source/images/icon-wxpay.png"></image> -->
          一键登录
        </button>
      </block>
      <button class="btn" bind:tap="navBack">
        取消登录
      </button>
    </view>
    <view class="phone-number-sigin">
      <view class="phone align-items-center">
        <text class="line"></text>
        <text class="phone-text">其他登录方式</text>
        <text class="line"></text>
      </view>
      <view bindtap="goSignInWithPhoneNumber" class="phone-number-img" disabled="{{isLogining}}">
        <image src="https://resource.pagoda.com.cn/dsxcx/images/64f21b60a14fc110f9636d30c3a5e93a"></image>
        <text>短信验证登录</text>
      </view>
    </view>
  </view>
</view>
<protocolPopup loginType="{{loginType}}" bind:getPhoneNumber="componentGetPhoneNumber" />
<user-protocol />
<user-error-modal isShow="{{showUserErrorModal}}" isSuperVip="{{isSuperVip}}" isNavigate="{{false}}"></user-error-modal>
<common-loading />