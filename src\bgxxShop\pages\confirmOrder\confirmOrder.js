const config = require('../../../utils/config')
const commonObj = require('../../../source/js/common').commonObj
import deliveryTimeUtils from '../../../utils/deliveryTime'
const util = require('../../../utils/util')
const sensors = require('../../../utils/report/sensors')
const log = require('../../../utils/log.js')
const app = getApp()
const SMSMixin = require('../../../mixins/SMSMixin')
const { defaultVal, allTrue } = require('../../../utils/cyclomatic')
// const iphoneXBottomHeight = 64 //rpx

let defaultPagePaddingBottom = 110 // rpx
Page({
  mixins: [SMSMixin],
  onLoad({
    haveCycleGoods = false
  }) {
    this.setNavigationBar()
    this.haveCycleGoods = JSON.parse(haveCycleGoods)
    this.initParams()
  },
  setNavigationBar() {
    // 顶部状态栏颜色
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#34BA36'
    })
  },
  initParams() {
    this.paySuccessToShow = false;
    this.storeInfoAreaHeight = 68
    const strGoodsList = JSON.stringify(app.globalData.sellementList)
    this.goodsList = JSON.parse(strGoodsList)
    // 深拷贝一份订单列表数据 并剔除掉没有参与特价活动的
    this._data.specialActivityGoodsList = (JSON.parse(strGoodsList) || []).filter(item => item.activityCode)
    // 活动信息
    this._data.objActivityInfo = app.globalData.objActivityInfo
    app.globalData.sellementList = null
    // 清空本地缓存活动信息
    app.globalData.objActivityInfo = {
      joinOneBuyActivity: {},
      cartJoinBuyGiftDataList: []
    }
    this.initStoreOrderTimeInfo()
  },
  async onShow() {
    if (!this.paySuccessToShow) {
      this.setData({
        isIphoneX: app.globalData.isIphoneX
      })
      this.checkUserInfo()
      if (this._data.onLoadFlag) {
        await this.getLatelyAddress()
        this.setBgxxSelectLocateInfo()
      }
      // 选择优惠券返回，不需要重新结算
      if (!this._data.fromSelectCouponBack) {
        this.requestData(this.initPage)
      }
      this._data.fromSelectCouponBack = false
      this._data.onLoadFlag = false
    }

    // 上报神策数据 v3.7.0
    sensors.pageScreenView()
  },
  onReady() {
    this.selfModal = this.selectComponent('#selfModel')
  },
  onUnload() {
    this.changeHomePageStoreAddress()
    app.globalData.bgxxUseCouponList = null
    app.globalData.bgxxNotUseCouponList = null
    wx.removeStorageSync('bgxxBeginTakeTime')
  },
  changeHomePageStoreAddress() {
    const { selectedDeliveryWay, storeInfo } = this.data
    const { selfTake, deliveryToDoor } = storeInfo
    const bgxxSelectLocateInfo = this._data.bgxxSelectLocateInfo[selectedDeliveryWay]
    const isSelfTake = selectedDeliveryWay === 'selfTake' && selfTake
    const isDelivery = deliveryToDoor && deliveryToDoor.id;
    (isSelfTake || isDelivery) && app.changeBgxxCityInfo(bgxxSelectLocateInfo)
  },
  data: {
    // 是否展示切换活动弹窗
    showChangeActivity: false,
    // 现货商品信息
    goodsInStockInfo: null,
    // 预定商品信息
    advanceSaleInfoList: null,
    // 周期购商品列表
    cycleSaleInfoList: null,
    // 失效商品列表
    invalidGoodsList: [],
    // 弹窗提示用户失效商品的列表
    newInValidGoodsList: null,
    // 会员卡省钱信息
    tips: {
      isVip: false,
      sale: 0
    },
    // 支付金额(实付)
    payAmount: 0,
    // 商品总价
    goodsTotalAmount: 0,
    // 钱包余额
    balanceInfo: {
      count: 0,
      enough: true,
    },
    // 发票信息
    invoiceInfo: null,
    // 支付类型
    payType: 0,
    pickUpTip: '',
    pagePaddingBottom: defaultPagePaddingBottom + 'rpx',
    storeInfo: {}, // 存储门店自提门店信息及配送上门收货地址信息
    isInvalidGoodsListMore: false,
    // 是否所有商品都失效
    allGoodsDisabled: true,
    isIphoneX: false,
    isFixedStoreShow: false,
    popupVisible: false,
    orderDeliveryWay: [{
        label: '配送上门',
        status: 'deliveryToDoor'
      },
      {
        label: '门店自提',
        status: 'selfTake'
      }
    ],
    selectedDeliveryWay: '',
    deliveryPriceInfo: {
      totalFreight: 0, // 总运费
      totalWeight: 0 // 总重量
    },
    scrollViewId: '',
    currUseActivity: null, // 当前使用的活动
    joinActivityList: [], // 可参与的活动信息（满赠/满折/以上都不参与）
    totalCount: 0, // 商品总数
    selectedCouponList: [], // 当前选中适用的优惠券列表
    unAvailableCouponCount: 0, //不可用优惠券数量
    canUseCouponCount: 0, // 可用优惠券数量
    couponCheapMoney: 0, // 选择的优惠券优惠总金额
    couponMaxCheapMoney: 0, // 优惠券最优优惠金额
    picUrl: config.baseUrl.PAGODA_PIC_DOMAIN,
    isShowPopup: false, // 是否展示弹窗
    goodsList: [], // 清单列表（弹窗）
    invalidGoodsTitle: {
      size: '34rpx',
      height: '128rpx'
    },
    isJoinOneBuy: 'Y',       //是否参与一元购
    oneBuyFreeAmount: 0,     //一元购活动优惠金额
    oneBuyOverrun: false,    //一元购活动是否超限
    totalPackAmount: 0,      //包装费
    packAmountTips: '',      //包装费说明
    showCostBox: false,      //费用说明弹窗
    costBoxType: 1,          //1为包装费弹窗说明，2为运费券弹窗说明
    costBoxTitle: {          //底部弹窗组件头部样式
      size: '36rpx',
      height: '128rpx',
      bold: 'bold'
    },
    freightTemplate: {},     //运费模板
    costCouponCheapMoney: 0, // 运费券优惠金额
    totalBasicCost: 0,       //合计基础运费
    totalFreightCost: 0,     //合计续重运费
    orderList: [],           //订单列表
    showRuleBox: false,      //运费规则详情弹窗
    cityName: '',            //当前城市
    overweight: false,       //是否超重
    bgxxTips: {},  // 心享会员提示语
    vipFree: 0,     // 商品心享价节省金额
    vipSaveMoney: -1,     // 商品心享价节省金额
    popupGoodsCount: 0,  // 商品列表弹窗的商品总数
    totalOriginalPrice: 0,  // 订单中所有商品总的原价
    isNeedPackingBag: 0,  // 自提模式下是否需要包装费（0:不需要 1:需要）
    selectPackingBagBox: false,  // 自提模式下选择包装费弹窗
    cityPackingFee: 0,  // 当前城市设置的包装费（自提模式下选择包装费弹窗展示用）
    isAllVip: false,  // 选择的优惠券是否都为心享会员专享的券
    orderDiscount: 0,  // 订单优惠金额
    discountFreightCost: 0,  // 订单配送费优惠金额
    customPickerVisible: false, // 自定义Picker是否展示
    deliveryTimePickerArray: [
      [],
      []
    ],
    deliveryTimePickerSeleted: [],
    latelyDeliveryWay: null, // 上次下单方式
    showPurchaseBox: false // 是否展示上次下单方式提示
  },
  _data: {
    shouldShowCustomePicker: false,
    deliveryLimitInfo: [],
    fromSelectCouponBack: false,
    couponKey: '',
    goodsDisabled: true,
    onLoadFlag: true,
    bgxxSelectLocateInfo: { // 分别保存门店自提/配送上门方式对应的已选地址，已选门店信息
      selfTake: {},
      deliveryToDoor: {}
    },
    currentSubOrderSelected: 'goodsInStockInfo', // 标记出现弹窗时当前选择的子单
    subOrderPickerObj: {  // 记录选择的时间索引
      'goodsInStockInfo': [0, null], // 现货，子单只会有一个
      '0_advanceSaleInfoList': [0, null], // 预售，子单会有多个，key加上序号
    },
    // 第一次结算是否通过
    isFirstSettled: false,
    // 当优惠信息发生变更后是否需要返回上一页
    // 是否所有的普通商品都下架了 当此值为true时 点击“我知道了”会返回上一页
    isNeedBack: false,
    // 是否要显示活动信息变更弹窗
    isNeedShowActivityChangePopup: false,
    // 特价活动超出活动数量后，最高可参与活动数量数组，提交订单时使用
    specialActivityGoodsList: [],
    // 活动信息列表
    objActivityInfo: {
      // 参加换购的商品
      joinOneBuyActivity: {},
      // 参加满赠活动的商品列表
      cartJoinBuyGiftDataList: []
    }
  },

  /**
   * 查询更新会员状态
   */
  async checkUserInfo() {
    await app.isSuperVip()
    const { superVipStatus } = app.globalData
    const isVip = ['T', 'F'].indexOf(superVipStatus) !== -1
    this.setData({
      tips: {
        ...this.data.tips,
        isVip,
      },
    })
  },

  /**
   * 获取上次配送方式及收货地址
   */
  async getLatelyAddress() {
    const failAction = () => {
      this.setData({
        selectedDeliveryWay: 'selfTake'
      })
    }
    try {
      const {
        customerID,
        bgxxCityInfo: { cityID = ''} = {}
      } = app.globalData
      const res = await app.api.getBgxxLatelyAddress({
        userID: customerID,
        cityID: cityID || -1
      })
      await this.setAddressInfo(res.data)
    } catch (e) {
      failAction()
      app.apiErrorDialog(e || {})
    }
  },
  /**
   * 设置当前配送方式
   * @param {Object} data
   */
  async setAddressInfo(data) {
    const { latelyDeliveryWay = 0, address = {}, store = {} } = data || {}
    const { selectStoreInfo = {} } = wx.getStorageSync("bgxxSelectLocateInfo") || {}
    const { isSupportVip = 'N',
      isSupportVipDelivery = 'N', storeID: selectStoreID = '', storeName = '' } = selectStoreInfo || {}
    // 判断上次是否下了单，配送address或者自提store不为空对象
    const showPurchase = Object.keys(address).length > 0 || Object.keys(store).length > 0
    this.setData({
      latelyDeliveryWay: Number(latelyDeliveryWay) === 1 ? '门店自提' : '配送上门',
      showPurchaseBox: showPurchase && Number(latelyDeliveryWay) !== 0
    })
    // 首页未选择门店，优先展示自提方式，自提门店为空，配送上门地址为空
    if (!selectStoreID) {
      this.setEmptyStoreAddressInfo()
      return
    }
    // 首页选择门店既不支持配送也不支持自提
    if (![isSupportVip, isSupportVipDelivery].includes('Y')) {
      await this.storeCloseService()
      return
    }
    Object.assign(selectStoreInfo, {
      storeName: `百果园${storeName}`
    })
    let params = {}
    if (Number(latelyDeliveryWay) === 1) { // 上次下单为自提
      if (isSupportVip === 'Y') { // 支持自提
        params = {
          selectedDeliveryWay: 'selfTake',
          storeInfo: {
            selfTake: selectStoreInfo,
            deliveryToDoor: isSupportVipDelivery === 'Y' ? {
              storeName,
              storeID: selectStoreID
            } : null,
          }
        }
      } else {
        params = await this.handleOnlySupportDelivery({storeName, selectStoreID})
      }
    } else { // 上次下单为配送
      if (isSupportVipDelivery === 'Y') { // 支持配送
        // 判断收获地址配送范围是否包含改门店
        const { checked, cityCode } = await this.checkAddressContainStore(address, selectStoreID)
        const deliveryParams = Object.assign({ cityCode }, checked ?  (address || {}) : await this.getDeliveryAddress(false), {
          storeName,
          storeID: selectStoreID
        })
        params = {
          selectedDeliveryWay: 'deliveryToDoor',
          storeInfo: {
            selfTake: isSupportVip === 'Y' ? selectStoreInfo : null,
            deliveryToDoor: deliveryParams,
          }
        }
      } else {
        params = await this.handleOnlySupportSelfTake(selectStoreInfo)
      }
    }
    this.setData(params,this.setStoreInfoAreaHeight)
  },
  /**
   * 门店关闭服务处理
   */
  async storeCloseService() {
    const res = await app.showModalPromise({
      content: '您所选的门店暂时关闭线上服务,去其他门店看看吧',
      confirmText: '我知道啦',
    })
    if (res) {
      this.setEmptyStoreAddressInfo()
    }
  },
  /**
   * 自提门店，配送地址设置为空
   */
  setEmptyStoreAddressInfo() {
    this.setData({
        selectedDeliveryWay: 'selfTake',
        storeInfo: {
          deliveryToDoor: null,
          selfTake: null
        }
      },
      this.setStoreInfoAreaHeight
    )
  },
  /**
   * 不支持自提，支持配送处理
   */
  async handleOnlySupportDelivery({storeName, selectStoreID}) {
    const res = await app.showModalPromise({
      content: '抱歉，您所选择的门店暂不支持自提，是否切换为配送方式',
      showCancel: true,
      confirmText: '切换',
      cancelText: '不切换'
    })
    return {
      selectedDeliveryWay: res ? 'deliveryToDoor' : 'selfTake',
      storeInfo: {
        selfTake: null,
        deliveryToDoor: {
          storeName,
          storeID: selectStoreID
        },
      }
    }
  },
  /**
   * 不支持配送，只支持自提处理
   */
  async handleOnlySupportSelfTake(selectStoreInfo) {
    const res = await app.showModalPromise({
      content: '抱歉，您所选择的门店暂不支持配送，是否切换为自提方式',
      showCancel: true,
      confirmText: '切换',
      cancelText: '不切换'
    })
    return {
      selectedDeliveryWay: res ? 'selfTake' : 'deliveryToDoor',
      storeInfo: {
        selfTake: selectStoreInfo,
        deliveryToDoor: null,
      }
    }
  },
  /**
   * 判断收获地址是否覆盖门店
   */
  async checkAddressContainStore(address, selectStoreID) {
    if (!address || !address.cityName) {
      return { checked: false, cityCode: '' }
    }
    const {
      lat,
      lon,
      cityName,
    } = address
    try {
      const res = await app.api.checkBgxxIsSupportVip({
        cityName,
        lat,
        lon
      })
      const { storeList = [], city = { cityCode: '' } } = res.data || {}
      const supportList = (storeList || []).filter(item => item.isSupportVipDelivery === 'Y')
      if (!!supportList.length) {
        const findStore = supportList.find(store => String(store.storeID) === String(selectStoreID))
        return { checked: !!findStore, cityCode: city.cityCode }
      }
    } catch (error) {}
    return { checked: false, cityCode: '' }
  },
  setStoreInfoAreaHeight() {
    const query = wx.createSelectorQuery()
    query.select('#storeInfoArea').boundingClientRect(res => {
      if (res && res.height) {
        this.storeInfoAreaHeight = res.height
      }
    })
    query.exec()
  },
  setBgxxSelectLocateInfo() {
    const { selfTake, deliveryToDoor } = this.data.storeInfo
    const bgxxSelectLocateInfo = wx.getStorageSync("bgxxSelectLocateInfo") || {}
    if (!!selfTake) {
      this._data.bgxxSelectLocateInfo.selfTake = bgxxSelectLocateInfo
    }
    if (!!deliveryToDoor && deliveryToDoor.id) {
      const { cityID, cityName, lat, lon, gisAddress = '', gisDistrict = '', gisProvince = '' } = deliveryToDoor
      const { selectStoreInfo, selectAddressInfo } = bgxxSelectLocateInfo
      const { cityCode, deliveryCenterCode } = (selectAddressInfo || {})
      this._data.bgxxSelectLocateInfo.deliveryToDoor = {
        selectStoreInfo,
        selectAddressInfo: {
          cityCode,
          deliveryCenterCode,
          cityID,
          cityName,
          lat,
          lon,
          address: `${gisProvince}${cityName}${gisDistrict}${gisAddress}`,
          supportSuperVipShop: 'Y'
        }
      }
    }
  },
   /**
   * 调用结算接口
   */
  async requestData(successCb, isNotRetry) {
    const that = this
    const {
      storeInfo,
      selectedDeliveryWay,
      currUseActivity,
      isJoinOneBuy,
      isNeedPackingBag
    } = this.data
    const {
      customerID,
      bgxxCityInfo: { cityID: currCityID, lat: currLat, lon: currLon } = {}
    } = app.globalData
    const {
      lon = currLon,
      lat = currLat,
      cityID = currCityID,
      storeID = -1
    } = defaultVal(storeInfo[selectedDeliveryWay], {})
    const settlementActInfo = {
      isEnableActCalc: 'N',
      activityInfoInputList: []
    }
    if (currUseActivity) {
      settlementActInfo.isEnableActCalc = 'Y'
      settlementActInfo.activityInfoInputList.push({ activityCode: currUseActivity.activityCode, activityType: currUseActivity.activityType })
    }
    const settlementPostData = {
      goodsList: this.goodsList,
      settlementActInfo,
      isJoinOneBuy,
      // 换购(一元购活动)新入参
      isTradeInGoods: isJoinOneBuy
    }
    const cartJoinBuyGiftDataList = util.getObjectValue(this._data, 'objActivityInfo.cartJoinBuyGiftDataList', [])
    const joinOneBuyActivity = util.getObjectValue(this._data, 'objActivityInfo.joinOneBuyActivity', {})
    ;[
      // 如果存在满赠活动 则带上参数
      {
        condition: cartJoinBuyGiftDataList.length,
        key: 'cartJoinBuyGiftDataList',
        value: cartJoinBuyGiftDataList
      },
      // 如果存在换购活动 则带上参数
      {
        condition: Object.keys(joinOneBuyActivity).length,
        key: 'joinOneBuyActivity',
        value: joinOneBuyActivity
      },
      // 自提模式下增加是否需要包装费的入参
      {
        condition: selectedDeliveryWay === 'selfTake',
        key: 'isNeedPackingBag',
        value: isNeedPackingBag
      },
    ].forEach(v => {
      v.condition && (settlementPostData[v.key] = v.value)
    })
    wx.showLoading({
      title: '正在加载',
      mask: true
    })
    try {
      const res = await app.api.bgxxOrderSettle({ customerID, cityID: defaultVal(cityID, -1), storeID, lon, lat, deliveryWay: selectedDeliveryWay === 'deliveryToDoor' ? 2 : 1 }, settlementPostData)
      wx.hideLoading()
      successCb && successCb(res.data, res.systemTime)
    } catch (error) {
      wx.hideLoading()
      this.resetOrderParams()
      const { errorCode = -1, description } = defaultVal(error, {})
      if ([30121, 30140].includes(errorCode)) {
        // 30121 活动商品仅可购买一件，可多次参与（送葱活动）
        // 30140 每人限购一种新客专享商品哦~
        commonObj.showModal('提示', description, false, '我知道了', '', () => {
          wx.navigateBack()
        })
      } else if(errorCode === 35450){
        // 35450 当前优惠信息发生变更，重新确认订单信息
        wx.showModal({
          title: '提示',
          content: description,
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: config.themeColorConfig.fruit,
          success(res){
            if(allTrue(res.confirm, !isNotRetry)){
              that.requestData(that.initPage, true)
            }
          }
        })
      }
      else {
        console.log('error', error);
        app.apiErrorDialog(defaultVal(error, {}))
        if (allTrue(selectedDeliveryWay === 'selfTake', errorCode === 35402)) {
          that.setData({
            'storeInfo.selfTake': null
          })
        }
      }
    }
  },

  /**
   * 重置订单页面参数
   */
  resetOrderParams() {
    this.setData({
      goodsInStockInfo: null,
      advanceSaleInfoList: null,
      cycleSaleInfoList: null,
      invalidGoodsList: [],
      payAmount: 0,
      goodsTotalAmount: 0,
      deliveryPriceInfo: {
        totalFreight: 0,
        totalWeight: 0
      },
      allGoodsDisabled: true
    })
    this._data.goodsDisabled = true
  },

  // 调用确认订单接口
  async requestConfirmOrder(successCb, failCb = () => {}) {
    const that = this
    const {
      selectedDeliveryWay,
      storeInfo = {},
    } = this.data
    const {
      cityID = app.globalData.bgxxCityInfo.cityID, storeID = -1
    } = storeInfo[selectedDeliveryWay] || {}
    const { postData, subOrderList }  = this.getConfirmOrderParams()
    wx.showLoading({
      title: '正在加载',
      mask: true
    })
    try {
      const res = await app.api.submitBgxxOrder({ customerID: app.globalData.customerID, cityID: cityID || -1, storeID, deliveryWay: selectedDeliveryWay === 'deliveryToDoor' ? 2 : 1 }, postData)
      wx.hideLoading()
      successCb && successCb(res.data, subOrderList)
      this.submitOrderSuccessReport(res.data)
    } catch (error) {
      wx.hideLoading()
      that.confirmOrderLoading = false
      const { errorCode = -1, description = '请求出错' } = error || {}
      if (errorCode === 6133 || errorCode === 6134 || errorCode === 30007) {
        // 商品失效
        failCb()
      } else if (errorCode === 6142) {
        // 配送不足起送金额（再买xxx元可配送上门...）
        that.showDeliveryNoEnoughTip(description)
      } else if (errorCode === 6141 || errorCode === 6147) {
        // 6141 超过配送时间(当前时间已超16:00,请重新选择配送时间)
        // 6147 自提时间更新(当前时间已超16:00,请重新确认自提日期)
        const message = `门店截单时间变更，${selectedDeliveryWay === 'selfTake' ? '请重新确认自提时间' : '请重新选择配送时间'}`
        wx.showModal({
          title: '',
          content: message,
          showCancel: false,
          confirmText: '知道了',
          success(res) {
            if (res.confirm) {
              // that.initStoreOrderTimeInfo()
              that.storeOrderTimeInfo.stock = {}
              that._data.subOrderPickerObj.goodsInStockInfo = [0, null]
              that.requestData(that.initPage)
            }
          }
        })
      } else if (errorCode === 35450 || errorCode === 35452) {
        // 35450 当前优惠信息发生变更     35452 订单信息发生变更（包装费、续重费）
        wx.showModal({
          title: '',
          content: description,
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: config.themeColorConfig.fruit,
          success(res) {
            if (res.confirm) {
              failCb()
            }
          }
        })
      } else if (errorCode === 30140) {
        // 新客专享商品有多个种类
        commonObj.showModal('提示', description, false, '我知道了', '', () => {
          wx.navigateBack()
        })
      } else if (errorCode === 6102) {
        // 地址被删除
        commonObj.showModal('提示', description, false, '我知道了', '', () => {
          that.setData({
            'storeInfo.deliveryToDoor': null
          })
          that.initStoreOrderTimeInfo()
          that.requestData(that.initPage)
        })
      } else if (errorCode === 30118 || errorCode === 30117) {
        // 30118无效的优惠券  30117 优惠券金额超出限制
        commonObj.showModal('提示', errorCode === 30118 ? '抱歉，当前优惠信息发生变更，\r\n请重新确认订单信息' : description, false, '我知道了', '', () => {
          failCb()
        })
      } else if (errorCode === 6149){
        // 6149 下单门店和城市id不匹配
        log.error('选择的门店信息', storeInfo[selectedDeliveryWay])
        log.error('全局存储的城市信息', app.globalData.bgxxCityInfo)
        wx.showModal({
          title: '提示',
          content: description,
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: config.themeColorConfig.fruit,
          success(res){
            if(res.confirm){
              if(selectedDeliveryWay === 'selfTake'){
                that.setData({
                  'storeInfo.selfTake': null
                })
              }else{
                that.setData({
                  'storeInfo.deliveryToDoor': null
                })
              }
            }
          }
        })
      } else if (errorCode === 59999) {
        that.deliveryTimeIsLimit({ description })
      }else {
        app.apiErrorDialog({
          errorCode,
          description
        })
      }
    }
  },

  deliveryTimeIsLimit ({ description }) {
    // 当前时间段已约满
    const _this = this
    wx.showModal({
      title: '',
      content: description,
      showCancel: false,
      confirmText: '知道了',
      success(res) {
        if (res.confirm) {
          // _this.resetSelectedTime()
          _this.resetAllSelectedTime()
          _this.requestData(_this.initPage)
        }
      }
    })
  },

  resetAllSelectedTime () {
    const obj = this._data.subOrderPickerObj
    Object.keys(obj).forEach(key => {
      obj[key] = [0, null]
    })
    this._data.shouldShowCustomePicker = true
  },

  resetSelectedTime () {
    let firstToPicker = ''
    let preSaleIdx = 0
    const { advanceSaleInfoList, goodsInStockInfo } = this.data
    const { goodsList = [] } = goodsInStockInfo || {}
    const { deliveryLimitInfo } = this._data
    // deliveryTime: "2022-10-31 09:00-10:00"
    // deliveryTime: "2022-04-30 09:00-10:00"

    // "2022-10-31 10:00-11:00" => "2022-10-31 10:00:00"
    const formatTime = (time) => time.replace(/-\d\d:00/, ':00')
    // 没有截单时间信息，不操作
    if (!deliveryLimitInfo.length) return
    // 有现售
    if (goodsList && goodsList.length) {
      const onSaleDeliveryTime = formatTime(goodsInStockInfo.deliveryTime)
      const { isFull } = (deliveryLimitInfo.find(el => el.date === onSaleDeliveryTime) || { isFull: 0 })
      if (isFull) {
        // 清空对应的时间选项
        const key1 = `goodsInStockInfo.deliveryTime`
        const key2 = `goodsInStockInfo.showDeliveryTime`
        this.setData({
          [key1]: '',
          [key2]: ''
        });
        if (subOrderPickerObj.goodsInStockInfo) subOrderPickerObj.goodsInStockInfo = [];
        firstToPicker = 'goodsInStockInfo'
      }
    }
    // 有预售
    if (advanceSaleInfoList && advanceSaleInfoList.length) {
      advanceSaleInfoList.forEach((pre, index) => {
        const preSaleDeliveryTime = formatTime(pre.deliveryTime)
        const { isFull } = (deliveryLimitInfo.find(el => el.date === preSaleDeliveryTime) || { isFull: 0 })
        if (isFull) {
          const key1 = `advanceSaleInfoList[${index}].deliveryTime`
          const key2 = `advanceSaleInfoList[${index}].showDeliveryTime`
          this.setData({
            [key1]: '',
            [key2]: ''
          })
          if (subOrderPickerObj[`${index}_advanceSaleInfoList`]) subOrderPickerObj[`${index}_advanceSaleInfoList`] = [0, null]
          firstToPicker || (firstToPicker = `${index}_advanceSaleInfoList`);
          preSaleIdx = index
        }
      })
    }
    if (!firstToPicker) return
    // 找到对应的deliveryTimeRange
    let deliveryTimeRange = []
    if (firstToPicker === 'goodsInStockInfo') {
      deliveryTimeRange = goodsInStockInfo.deliveryTimeRange
    } else {
      const idx = firstToPicker.split('_')[0]
      deliveryTimeRange = advanceSaleInfoList[idx].deliveryTimeRange
    }
    this.pickerCheckStore({ currentTarget: { dataset: { item: deliveryTimeRange, index: firstToPicker } } })
  },

  getConfirmOrderParams() {
    const {
      invoiceInfo,
      selectedDeliveryWay,
      storeInfo = {},
      currUseActivity,
      selectedCouponList = [],
      isNeedPackingBag
    } = this.data
    const { id = -1 } = storeInfo[selectedDeliveryWay] || {}
    let postData = {}
    // 发票信息
    !!invoiceInfo && (postData.invoiceInfo = invoiceInfo);
    // 商品信息
    const subOrderList = this.getSubOrderList()
    postData.subOrderList = subOrderList
    if (selectedDeliveryWay === 'deliveryToDoor') {
      postData.receiveAddrID = id
    }
    // 参与活动信息
    if (!!currUseActivity) {
      const { activityCode, activityType } = currUseActivity
      postData.activityInfoInputList = [{
        activityCode,
        activityType
      }]
    }
    // 优惠券信息
    !!this._data.couponKey && (postData.couponKey = this._data.couponKey)
    postData.couponInfoList = selectedCouponList.map(item => {
      const { couponCode, couponWay, limitValue, couponValue, couponMoney, isOnlyVip  } = item
      return {
        couponCode,
        couponWay,
        limitValue,
        couponValue,
        couponMoney,
        isOnlyVip
      }
    })
    //是否参与一元购活动
    postData.isJoinOneBuy = 'Y'
    // 自提模式下增加是否需要包装费的入参
    if (selectedDeliveryWay === 'selfTake') {
      postData.isNeedPackingBag = isNeedPackingBag
    }
    // 有特价活动的情况
    postData.specialActivityGoodsList = this._data.specialActivityGoodsList.map(item => ({
      goodsID: item.goodsID,
      count: item.count,
      activityCode: item.activityCode,
      // isTradeInGoods: item.isTradeInGoods
    }))
    return { postData, subOrderList }
  },

  initPage(data, systemTime = -1) {
    this.setActivityList(data)
    this.setCouponData(data)
    this.setGoodsData(data, systemTime)
    this.setPageData(data)
    this.setSuperVipPickUpTip(data.superVipPickUpTip)
    // 当活动信息发生变更且第一次进来 则弹窗
    this.showActiveChangeMsg(data)
    // 当特价活动变更，特价数量超出等情况 则弹窗提示
    this.showSpecialActiveChangeMsg(data)
    // 当特价活动变更后，需要重新匹配活动id
    this.resetSpecialActiveCode(data)
  },

  /**
   * 设置满折，满赠活动信息
   */
  setActivityList(data) {
    const { joinActivityList = null } = data || {}
    const beforeUseActivity = !!this.data.currUseActivity ? JSON.parse(JSON.stringify(this.data.currUseActivity)) : null
    if (!!joinActivityList && !!joinActivityList.length) {
      const list = joinActivityList.map((item, index) => {
        const { activityType, activityName } = item
        let isSelected = 'N'
        if (!beforeUseActivity) {
          // 当前没有在使用的活动，默认使用第一个
          if (index === 0) {
            isSelected = 'Y'
            this.setData({
              currUseActivity: item
            })
          }
        } else {
          // 匹配当前正在使用的活动
          isSelected = beforeUseActivity.activityType === activityType ? 'Y' : 'N'
        }
        return {
          ...item,
          isSelected,
          buyGiftActivityTip: activityType === 'P' ? '新客满折活动' : activityName,
          buyGiftActivityCode: activityType,
        }
      })
      const selectedItem = list.find(obj => {
        return obj.isSelected === 'Y'
      })
      // 如果当前使用的活动存在，但在活动列表中没有匹配到，则说明后台活动发生变更，使用最新的活动
      if (!selectedItem && !!beforeUseActivity) {
        list[0].isSelected = 'Y'
        this.setData({
          currUseActivity: list[0]
        })
      }
      this.setData({
        joinActivityList: list
      })
      // this.discountModal = this.selectComponent('#discountActivity')
    } else {
      // 无可以参与的活动
      this.setData({
        currUseActivity: null,
        joinActivityList: []
      })
    }
    this.calculateOrderDiscount()
  },

  /**
   * 设置优惠券信息
   */
  setCouponData(data) {
    const { couponList = [], unAvailableCouponList = [], couponKey, vipFree } = data || {}
    // let newUnAvailableList =  util.filterChannelSeparation(unAvailableCouponList, 'couponTypeStr', ['百果园App可用']) // 此处后端 过滤逻辑一样 那边不想做修改
    this._data.couponKey = couponKey
    app.globalData.bgxxUseCouponList = couponList
    app.globalData.bgxxNotUseCouponList = unAvailableCouponList

    this.setData({
      canUseCouponCount: couponList.length,
      unAvailableCouponCount: unAvailableCouponList.length,
      vipFree,
      vipSaveMoney: vipFree
    })
    if (!this.data.canUseCouponCount) {
      this.setData({
        selectedCouponList: [],
        couponCheapMoney: 0,
        couponMaxCheapMoney: 0
      })
      return
    }
    // 查找最优优惠券组合(最多两张 一张免运券，一张普通券)方式
    const selectedCouponList = [], maxNumber = Number(couponList[0].couponWay) === 5 ? 2 : 1
    for (let index = 0, len = couponList.length; index < len; index++) {
      if (selectedCouponList.length >= maxNumber) {
        break
      }
      const { couponWay } = couponList[index]
      if ((!!selectedCouponList.length && Number(couponWay) !== 5) || !selectedCouponList.length) {
        selectedCouponList.push(couponList[index])
      }
    }
    const couponCheapMoney = this.getCouponCheapMoney(selectedCouponList)
    this.setData({
      selectedCouponList,
      couponCheapMoney,
      couponMaxCheapMoney: couponCheapMoney
    })
    this.getVipCouponCheapMoney(selectedCouponList)
    this.calculateOrderDiscount()
  },

  getCouponCheapMoney(selectedCouponList) {
    let couponCheapMoney = 0
    selectedCouponList.forEach(item => {
      couponCheapMoney += item.couponMoney
    })
    return couponCheapMoney
  },

  /**
   * 获取心享专享优惠券的优惠金额，运费券优惠金额
   * @param {Array} selectedCouponList
   */
  getVipCouponCheapMoney(selectedCouponList) {
    let vipCouponCheapMoney = 0
    let couponCheapMoney = 0
    let costCouponCheapMoney = 0
    const { vipFree } = this.data
    selectedCouponList.forEach(item => {
      if (item.isOnlyVip === 'Y') {
        vipCouponCheapMoney += item.couponMoney
      }
      if (Number(item.couponWay) === 5) {
        costCouponCheapMoney += item.couponMoney
      }
      couponCheapMoney += item.couponMoney
    })
    // 选择的优惠券是否都为心享会员专享的券
    this.setData({
      isAllVip: Number(couponCheapMoney) === Number(vipCouponCheapMoney) ? true : false,
      costCouponCheapMoney,
      vipSaveMoney: vipFree + vipCouponCheapMoney
    })
  },

  /**
   * 计算订单优惠总金额
   * 金额 = 商品特价参与优惠 + 心享会员会员价优惠 + 优惠券使用优惠（免运券+满减/满折/立减/立折）+ 运费达到门槛省下的金额 + 新客满折活动的优惠
   */
  calculateOrderDiscount() {
    const { vipFree = 0, couponCheapMoney = 0, currUseActivity, tips, goodsInStockInfo, discountFreightCost } = this.data
    // couponCheapMoney（优惠券使用优惠），discountFreightCost（运费达到门槛省下的金额）
    let orderDiscount = couponCheapMoney + discountFreightCost
    // 新客满折优惠
    if(currUseActivity && currUseActivity.activityType === 'P' && currUseActivity.activityFree) {
      orderDiscount += currUseActivity.activityFree
    }
    // 心享会员优惠
    if (tips && tips.isVip) {
      orderDiscount += vipFree
    }
    // 加价购活动优惠金额
    if (goodsInStockInfo && goodsInStockInfo.goodsList && goodsInStockInfo.goodsList.length > 0) {
      let amount = 0
      goodsInStockInfo.goodsList.forEach(goods => {
        // 心享会员不做判断 按照原价计算
        let price = goods.bidPrice
        // 换购活动
        if (goods.activityInfo && goods.activityInfo.enjoyOneBuyNum && goods.activityInfo.price) {
          amount += goods.activityInfo.enjoyOneBuyNum * (price - goods.activityInfo.price)
        }
        // 特价活动
        if (goods.specialActivityInfo && goods.specialActivityInfo.activityCount && goods.specialActivityInfo.activityPrice) {
          amount += goods.specialActivityInfo.activityCount * (price - goods.specialActivityInfo.activityPrice)
        }
      })
      orderDiscount += amount
    }
    this.setData({
      orderDiscount
    })
  },

  /**
   * 设置页面相关参数
   * @param {Object} data
   */
  setPageData(data) {
    const {
      totalFreight = 0,
      totalWeight = 0,
      goodsTotalAmount = 0,
      invalidGoodsList,
      mainBalance,
      payAmount,
      vipFree,
      storeOutput = null,
      oneBuyFreeAmount,
      totalPackAmount = 0,
      packAmountTips,
      freightTemplate,
      cityPackingFee = 0
    } = data
    this.noActivityAmount = payAmount
    this.mainBalance = mainBalance
    const invalidGoodsCount = invalidGoodsList.reduce((sum, item) => sum + item.count, 0)
    this.setData({
      allGoodsDisabled: true,
      invalidGoodsList: invalidGoodsList,
      invalidGoodsCount,
      deliveryPriceInfo: {
        totalFreight,
        totalWeight: Number((totalWeight / 1000).toFixed(3))
      },
      balanceInfo: {
        count: +parseFloat(mainBalance / 100).toFixed(2),
      },
      goodsTotalAmount: oneBuyFreeAmount && oneBuyFreeAmount > 0 ? goodsTotalAmount - oneBuyFreeAmount : goodsTotalAmount,
      tips: Object.assign(this.data.tips, {
        sale: +parseFloat(vipFree / 100).toFixed(2)
      }),
      oneBuyFreeAmount,
      totalPackAmount,      //包装费
      packAmountTips,       //包装费说明
      freightTemplate,       //运费模板
      cityName: (storeOutput && storeOutput.cityName) || '',   //当前城市
      cityPackingFee        // 当前城市设置的包装费
    })
    this.setPayParams()
    if (this.data.selectedDeliveryWay === 'selfTake') {
      const { selfTake } = this.data.storeInfo
      if (!!storeOutput) {
        if (!!selfTake) {
          const { address, cityName, cityID, openingTime, shortName, lon, lat, id, startTime, endTime } = storeOutput
          if (String(id) === String(selfTake.storeID)) {
            const info = {
              storeID: id,
              storeName: shortName,
              address,
              cityName,
              cityID,
              openingTime,
              lon,
              lat,
              startTime,
              endTime
            }
            this.setData({
              'storeInfo.selfTake': info
            })
          }
        }
      }
    }
  },

  /**
   * 设置现售预售周期购列表数据
   */
  setGoodsData(data, systemTime = -1) {
    const {
      storeOutput = null,
      toastGoodsList = [],
      subOrderList = [],
      deleteGoodsIdList = [],
      freightTemplate = {},
      deliveryLimitInfo = []
    } = data
    // 计算合计基础运费和合计续重运费
    let totalBasicCost = 0
    let totalFreightCost = 0
    let freightTips = false
    let orderList = []
    let totalOriginalPrice = 0
    let discountFreightCost = 0
    subOrderList.forEach(item =>{
      if(item.saleType === 'N' || item.saleType === 'P'){
        totalBasicCost += item.subBaseFreight
        totalFreightCost += item.subRenewFreight
        orderList.push(item)
        // 任一子单超重，增加toast提示
        if(item.subRenewFreight && item.subRenewFreight > 0 && !freightTips){
          wx.showToast({
            icon: 'none',
            title: '订单超重，将加收运费',
            duration: 1000
          })
          freightTips = true
        }
        // 计算订单中所有商品总的原价
        item.goodsList && item.goodsList.forEach (goods => {
          if (goods.isGift === 'N') {
            totalOriginalPrice += goods.bidPrice * goods.count
          }
        })
        // 计算子单中配送费的优惠金额
        if (freightTemplate && freightTemplate.freight && item.subBaseFreight >= 0) {
          discountFreightCost += (freightTemplate.freight - item.subBaseFreight)
        }
      }
    })
    // 现货商品信息
    let goodsInStockInfo = {
      goodsList: []
    }
    // 预售商品信息
    const advanceSaleInfoList = []
    // 周期购商品列表
    const cycleSaleInfoList = []
    let pickerSelectTime = []
    let showNoStore = false
    // 一元购活动是否超限
    let oneBuyOverrun = false

    if (!!storeOutput) {
      // storeOutput.openingTime门店营业结束时间跨天切割时间会有问题，与产品沟通后写死为09:00-22:00
      pickerSelectTime = deliveryTimeUtils.getTimePointList('09:00-22:00')
    } else {
      // 配送上门的地址无门店信息提示
      showNoStore = this.showDeliveryNoStore()
    }

    if (!!deleteGoodsIdList && deleteGoodsIdList.length > 0) {
      toastGoodsList.unshift({
        isValidReason: '部分商品已更新'
      })
    }

    // 不显示门店提示弹框前提下,显示失效弹框
    if (!!toastGoodsList && toastGoodsList.length > 0 && !showNoStore) {
      this.showInvalidBox(toastGoodsList)
    }

    this.stockOrderNum = 0 // 记录原始接口订单有几笔现货订单

    let allGoodsDisabled = true

    // 不可用券的活动
    const arrDisabledCouponList = this._data.specialActivityGoodsList.filter(item => item.couponStacking === 0)
    subOrderList.forEach(list => {
      // 过滤失效商品 判断、增加不可用券标签
      const filterList = list.goodsList.filter(goods => {
        // 记录一元购活动是否超限
        if(goods.activityInfo && Number(goods.activityInfo.enjoyOneBuyNum) !== 0 && (goods.count > goods.activityInfo.enjoyOneBuyNum) && !oneBuyOverrun){
          oneBuyOverrun = true
        }
        if (goods.isValid === 'Y') {
          // 记录是否所有商品都失效
          allGoodsDisabled = false
          // 加个判断，如果可用券 则判断是否有特价活动，因为特价活动可能会设置不可用券
          if (goods.isSupportCoupon === 'Y' && goods.specialActivityInfo && goods.specialActivityInfo.activityCount) {
            const goodsActivity = arrDisabledCouponList.find(item => item.goodsID === goods.id)
            // 如果活动不可用券 则商品修正为不可用券标签
            if (goodsActivity && goodsActivity.couponStacking === 0) {
              goods.isSupportCoupon = 'N'
            }
          }
          return true
        }
      })
      if (list.saleType === 'N') {
        // 现货
        const {
          beginTakeTime,
          saleType,
          storageType,
          subRenewFreight,
          subBaseFreight
        } = list
        const goodsList = goodsInStockInfo.goodsList.concat(filterList).sort((a, b) => b.isTradeInGoods - a.isTradeInGoods)
        goodsInStockInfo = {
          ...goodsInStockInfo,
          beginTakeTime,
          saleType,
          [`storageType${this.stockOrderNum}`]: storageType,
          goodsList,
          deliveryTime: this.storeOrderTimeInfo.stock.deliveryTime || '', // 传给接口的配送时间
          showDeliveryTime: this.storeOrderTimeInfo.stock.showDeliveryTime || '', // 页面显示配送时间
          deliveryTimeRange: [
            // 配送时间选择范围
            [deliveryTimeUtils.getPickerShowTime(beginTakeTime, systemTime)],
            pickerSelectTime
          ],
          subRenewFreight,     //现售订单超重费
          subBaseFreight       //现售订单基础配送费
        }
        this.stockOrderNum++
      } else {
        // 预售和周期购
        if (filterList.length) {
          const setList = list.saleType === 'P' ? advanceSaleInfoList : cycleSaleInfoList
          const {
            showDeliveryTime = '',
              deliveryTime = ''
          } = this.getSaveDeliveryTime(list)
          const listItem = {
            ...{
              ...list,
              goodsList: filterList
            },
            deliveryTime, // 传给接口的配送时间
            showDeliveryTime, // 页面显示配送时间
            deliveryTimeRange: [
              list.saleType === 'P' ? [deliveryTimeUtils.getPickerShowTime(
                list.beginTakeTime,
                systemTime
              )] : deliveryTimeUtils.getCirclePickerShowTime(list.cycleTakeTimeOutputList),
              pickerSelectTime
            ],
          }
          if (list.saleType === 'P') {
            Object.assign(listItem, {
              beginTakeTime: list.beginTakeTime,
            })
          }
          setList.push(listItem)
        }
      }
    })
    this._data.goodsDisabled = allGoodsDisabled
    this._data.deliveryLimitInfo = deliveryLimitInfo
    // 选择时间弹窗
    if (this._data.shouldShowCustomePicker) {
      const { goodsList = [] } = goodsInStockInfo || {}
      let deliveryTimeRange = []
      let firstToPicker = ''
      if (goodsList.length) {
        deliveryTimeRange = goodsInStockInfo.deliveryTimeRange
        // 现售只有一单
        firstToPicker = 'goodsInStockInfo'
      } else {
        // 预售有多单，取第一个
        deliveryTimeRange = advanceSaleInfoList[0].deliveryTimeRange
        firstToPicker = `0_advanceSaleInfoList`
      }
      this.pickerCheckStore({ currentTarget: { dataset: { item: deliveryTimeRange, index: firstToPicker } } })
      this._data.shouldShowCustomePicker = false
    }

    this.setData({
      goodsInStockInfo,
      advanceSaleInfoList,
      cycleSaleInfoList,
      allGoodsDisabled:  allGoodsDisabled || !this.data.payAmount,
      oneBuyOverrun,
      totalBasicCost,
      totalFreightCost,
      orderList,
      overweight: freightTips,
      totalOriginalPrice,
      discountFreightCost
    })
    this.setTotalCount()
    this.calculateOrderDiscount()
    // 有次日达商品时才执行下面的代码
    if (goodsInStockInfo && goodsInStockInfo.beginTakeTime) {
      // 由配送方式、门店编码、送达/提货时间三个维度来判断是否展示截单时间变更的Toast提示
      const { number } = storeOutput || {}
      const { selectedDeliveryWay } = this.data
      const beginTakeTimeData = {
        beginTakeTime: goodsInStockInfo.beginTakeTime,
        storeCode: number,
        deliveryWay: selectedDeliveryWay
      }
      const { beginTakeTime, storeCode, deliveryWay } = wx.getStorageSync('bgxxBeginTakeTime') || {}
      let showTips = storeCode !== number && deliveryWay === selectedDeliveryWay && beginTakeTime !== goodsInStockInfo.beginTakeTime
      // 配送模式下，还需判断是否选择了送达时间
      if (showTips && selectedDeliveryWay === 'deliveryToDoor') {
        showTips = goodsInStockInfo.showDeliveryTime ? true : false
      }
      const title = `门店截单时间变更，${selectedDeliveryWay === 'selfTake' ? '请重新确认自提时间' : '请重新选择配送时间'}`
      if (showTips) {
        wx.showToast({
          icon: 'none',
          title,
          duration: 3000
        })
        // 配送模式下还需清空已选的配送时间，并自动弹起配送时间选择弹窗
        if (selectedDeliveryWay === 'deliveryToDoor') {
          this.setData({
            'goodsInStockInfo.showDeliveryTime': null,
            'goodsInStockInfo.deliveryTime': null
          })
          this._data.subOrderPickerObj.goodsInStockInfo = [0, null]
          this.pickerCheckStore({ currentTarget: { dataset: { item: goodsInStockInfo.deliveryTimeRange, index: 'goodsInStockInfo' } } })
        }
      }
      wx.setStorageSync('bgxxBeginTakeTime', beginTakeTimeData)
    }
  },
  /**
   *
   * @param {object} param0
   * @param {string} param0.storeOpenTime "07:00-23:00" 营业时间
   * @param {string} param0.beginTakeDate "2022-04-20" 开始日期
   * @param {number} param0.systemTime 接口返回的系统时间
   */
  setPickerTimeRange ({ storeOpenTime, beginTakeDate, systemTime }) {
    let pickerSelectTime = []
  },
  // 获取商品总数
  setTotalCount () {
    const { goodsInStockInfo = {}, advanceSaleInfoList = [], cycleSaleInfoList = [] } = this.data

    let goodsInStockCount = goodsInStockInfo.goodsList.reduce((sum, item) => sum + item.count, 0)

    let totalCount = goodsInStockCount, advanceSaleCounts = [], cycleSaleCounts = []

    advanceSaleInfoList.forEach(item => {
      const count = item.goodsList.reduce((sum, item) => sum + item.count, 0)
      advanceSaleCounts.push(count)
      totalCount += count
    })

    cycleSaleInfoList.forEach(item => {
      const count = item.goodsList.reduce((sum, item) => sum + item.count, 0)
      cycleSaleCounts.push(count)
      totalCount += count
    })

    this.setData({
      totalCount,
      goodsInStockCount,
      advanceSaleCounts,
      cycleSaleCounts
    })
  },
  /**
   * 设置悬浮截单提示
   * @param {String} pickUpTip
   */
  setSuperVipPickUpTip(pickUpTip) {
    this.setData({
      pickUpTip
    })
  },
  /**
   * 显示失效弹窗
   * @param {Array} list 失效列表
   */
  showInvalidBox(list) {
    this.setData({
      newInValidGoodsList: list,
      popupVisible: true
    })
  },

  // 确认订单
  handleConfirmOrder: util.throttle(async function() {
    const that = this
    if (that.data.allGoodsDisabled || that.confirmOrderLoading) return
    const {
      storeInfo,
      selectedDeliveryWay,
    } = that.data

    if (selectedDeliveryWay === 'selfTake') {
      if (!storeInfo.selfTake) {
        wx.showToast({
          icon: 'none',
          title: '请选择提货门店',
          duration: 1000
        })
        return
      }
    } else {
      if (!storeInfo.deliveryToDoor || !storeInfo.deliveryToDoor.id) {
        wx.showToast({
          icon: 'none',
          title: '请添加收货地址',
          duration: 1000
        })
        return
      }
      // 配送上门检测配送时间是否全部选择
      const res = that.checkHaveDeliveryTime()
      if (!res) {
        return
      }
    }
    let addSensorsInfo = this.getAddSensorsInfo()
    sensors.track('MPClick', 'bgxxConfirmOrderNowPay', {
      scene_number: app.globalData.scene,
      ...addSensorsInfo
    })

    const handleFun = () => {
      that.confirmOrderLoading = true
      that.requestSubscribeMessage()
    }

    // 钱包支付弹确认弹窗
    if (that.data.payType === 0) {
      // 校验是否需要进入验证码环节
      const isNeedValidate = await that.checkPayDevice()

      if (!isNeedValidate) {
        // 缓存一下提交订单的请求
        that._data.tmpConfirmHandle = handleFun
        // 弹出验证码输入框
        that.showSmsValidate()
        return
      }
      const payAmount = (that.data.payAmount / 100).toFixed(2)
      const res = await app.showModalPromise({
        content: `确认使用会员钱包支付${payAmount}元吗？`,
        showCancel: true,
        confirmText: '确认',
        cancelText: '取消',
      })
      if (res) {
        // 提交订单
        handleFun()
      }
      return
    }

    handleFun()
  }, 2000),
  getAddSensorsInfo(){
    const {
      selectedDeliveryWay = 'selfTake',
      goodsInStockInfo = null,
      advanceSaleInfoList = null,
      superVipStatus = 'C',
      isAllVip = false,
      totalCount = 0,
      orderDiscount = 0,
      payAmount = 0,
      vipSaveMoney = 0,
      couponCheapMoney = 0
    } = this.data
    let OrderType = ''
    let isVip = ['T','F'].includes(superVipStatus)
    if((advanceSaleInfoList&&advanceSaleInfoList.length) && !(goodsInStockInfo.goodsList && goodsInStockInfo.goodsList.length)){
      OrderType = selectedDeliveryWay === 'selfTake' ? '次日达预售自提':'次日达预售到家'
    }else{
      OrderType = selectedDeliveryWay === 'selfTake' ? '次日达现售自提':'次日达现售到家'
    }
    let addSensorsInfo = {
      screen_type:'次日达',
      OrderType,
      commodityNum:totalCount,
      orderAmount:util.formatPrice(payAmount+orderDiscount),
      actualPaymentAmount:util.formatPrice(payAmount),
      useOrdinaryCoupon:couponCheapMoney > 0 ? 1 : 0,
      coupondiscountamount:util.formatPrice(couponCheapMoney),
      orderXinxiangSaveValue:isVip ? util.formatPrice(vipSaveMoney):0,
      xinXiangCoupon:isAllVip ? 1 : 0
    }
    return addSensorsInfo
  },
  requestSubscribeMessage() {
    const that = this
    const { selectedDeliveryWay } = that.data
    // 询问用户是否接收订阅消息，只有在用户点击以及支付成功回调中调起
    const tmplIdArr = ['rFN3af3rFLMGuS-BzF3PiJBev0tmWhRdf8AizIB7wEA', 'fE11t0uK0ugcX6MOaa0iY12sy111DIInJE8B_DM_5LI']
    if (selectedDeliveryWay === 'selfTake') {
      // 到店自提，自提提醒通知
      // tmplIdArr.push('V97E1jeQHgmdmxSd6kfdCqk6gSMOzsgKNRjiyDgwqM8')
      tmplIdArr.push('P6GPCdEUpVQ3Ha2jGrQ-Afwi9MdBoL72838n4ukcY98')
    } else {
      // 送货到家，订单发货通知
      tmplIdArr.push('HyUEMbv-0-sCSpGMgIoA-5lzT4OWKBuX-011KD8cRas')
    }
    app.requestSubscribeMessage({
      tmplIds: tmplIdArr,
    }, () => {
      console.log('确认订单请求')
      that.requestConfirmOrder(
        (data, subOrderList) => {
          // 提交订单后直接触发删除购物车选中的商品方法
          const eventChannel = this.getOpenerEventChannel()
          eventChannel.emit('bgxxCart.deleteSelected')
          app.event.emit('refreshFreshNewUserInfo')
          // 确认订单成功，调起支付
          let showCycle = false;
          if (subOrderList && subOrderList.length > 0) {
            const cycleItem = subOrderList.find(item => {
              return item.saleType === 'C'
            })
            if (!!cycleItem) {
              showCycle = true
            }
          }
          if (showCycle) {
            wx.showModal({
              title: '',
              content: '1.每笔周期购订单每月可有一次机会修改配送日期。\r\n 2.每笔周期购订单可在订单配送前修改配送时间。',
              showCancel: false,
              confirmText: '知道了',
              success(res) {
                if (res.confirm) {
                  that.handlePay(data)
                }
              }
            })
          } else {
            that.handlePay(data)
          }
        },
        () => {
          // 调用结算接口 查询有无新的失效商品
          that.saveOrderTimeInfo()
          that.requestData(that.initPage)
        }
      )
    })
  },
  /**
   * 配送上门时确认订单商品不够起送金额提示
   * @param {string} desc 起送信息
   */
  showDeliveryNoEnoughTip(desc) {
    this.selfModal.show({
      cancelText: '换门店自提',
      confirmText: '加购商品',
      content: desc,
      confirmCallBack: () => {
        wx.navigateTo({
          url: '/pages/xxshop/index/index'
        })
      },
      cancelCallBack: () => {
        this.changeToSelfTake()
      }
    })
  },
   /**
    * 根据商品id 删除已下单商品数据
    */
   deleteOrderedGoods(){
    let {goodsInStockInfo:{goodsList}} = this.data
    app.updateDelGoods(goodsList)
   },
  // 跳转订单详情
  redirectToOrderDetail(payOrderID) {
        // 跳转订单表明商品已下单，则需清空已下单的数据
    this.deleteOrderedGoods()
    wx.redirectTo({
      url: `/bgxxUser/pages/orderDetail/index?orderID=${payOrderID}&isPay=false`
    })
  },

  // 跳转支付loading
  redirectToPayLoading(paymentOrderID) {
     // 跳转支付表明已下单，则需清空已下单的数据
    this.deleteOrderedGoods()
    wx.redirectTo({
      url: `/bgxxShop/pages/payloading/index?paymentOrderID=${paymentOrderID}`
    })
  },

  // 支付
  async handlePay(data) {
    wx.showLoading({
      title: '支付中',
      mask: true
    })
    sensors.safeGuardSensor('pay') // 防黑产埋点
    if (this.data.payType === 0) {
      this.handlePayGB(data)
    } else if (this.data.payType === 1) {
      this.handlePayWechat(data)
    }
  },
  /**
   * 果币支付
   */
  async handlePayGB(data) {
    const that = this
    const { openid = '' } = wx.getStorageSync('wxSnsInfo') || {}
    const [date, time] = data.timeExpire.split(' ')
    const [year, month, day] = date.split('-')
    const [h, m, s] = time.split(':')
    try {
      await app.api.bgxxPayGB(app.globalData.customerID, data.payOrderNum, {
        payAmount: data.payAmount,
        payTitle: '心享订单',
        desc: '心享商品',
        timeExpire: `${year}${month}${day}${h}${m}${s}`,
        openID: openid
      })
      wx.hideLoading()
      this.redirectToPayLoading(data.payOrderID)
    } catch (error) {
      wx.hideLoading()
      const { errorCode = -1, description = "请求出错" } = error || {}
      if (errorCode === 59999) {
        that.deliveryTimeIsLimit({ description })
        return
      }
      if (errorCode === 56009 || errorCode === 56011) {
        // 自提时间或者配送时间选择有问题
        wx.showToast({
          title: description,
          icon: 'none',
          success() {
            that.redirectToOrderDetail(data.payOrderID)
          }
        })
      } else {
        commonObj.showModal('提示', `支付失败，请重试`, false, '', '', () => {
          that.redirectToOrderDetail(data.payOrderID)
        })
      }
    }
  },
  /**
   * 微信支付
   */
  async handlePayWechat(data) {
    const that = this
    const { openid = '' } = wx.getStorageSync('wxSnsInfo') || {}
    const { userID, phoneNumber } = wx.getStorageSync('user') || {}
    const encryptKey = wx.getStorageSync('token')
    try {
      const params = {
        channel: 'WX_MINI',
        payOrderNo: data.payOrderNum,
        payAmount: data.payAmount,
        timeExpire: data.timeExpire,
        subject: '心享订单',
        body: '心享商品',
        version: '*******',
        source: 'WX_XX',
        openID: openid,
        clientIP: '*********',
        customerID: userID,
        mobile: phoneNumber,
        orderType: 'CONSUME_GOODS'
      }
      const res = await app.api.bgxxPayWechat(userID, JSON.stringify({ data: commonObj.Encrypt(params, encryptKey) }))
      wx.hideLoading()
      const depositObj = JSON.parse(
        commonObj.Decrypt(res.data, encryptKey)
      )
      wx.requestPayment({
        timeStamp: depositObj.timeStamp,
        nonceStr: depositObj.nonceStr,
        package: `prepay_id=${depositObj.prepayId}`,
        signType: 'MD5',
        paySign: depositObj.signStr,
        success: () => {
          // 跳转支付loading
          that.redirectToPayLoading(data.payOrderID)
        },
        fail: res => {
          if (res.errMsg.indexOf('cancel') > -1) {
            wx.showToast({
              title: '支付取消',
              icon: 'loading',
              duration: 2000
            })
            setTimeout(() => {
              that.redirectToOrderDetail(data.payOrderID)
            }, 2000)
          } else {
            that.handlePayFail(data)
          }
        },
        complete: function (res) {
          that.paySuccessToShow = true;
          console.log('支付回调完成')
        }
      })
    } catch (error) {
      wx.hideLoading()
      const { errorCode = -1, description = '请求出错' } = error || {}
      if (errorCode === 56009 || errorCode === 56011) {
        // 自提时间或者配送时间选择有问题
        wx.showToast({
          title: description,
          icon: 'none',
          success() {
            that.redirectToOrderDetail(data.payOrderID)
          }
        })
      } else {
        that.handlePayFail(data)
      }
    }
  },
  handlePayFail(data) {
    wx.showToast({
      title: '支付失败，请重试',
      duration: 1500,
      icon: 'none'
    })

    setTimeout(() => {
      this.redirectToOrderDetail(data.payOrderID)
    }, 1500)
  },

  // 监听页面滚动
  handleScroll({
    detail
  }) {
    if (this.data.storeInfo[this.data.selectedDeliveryWay]) {
      if (detail.scrollTop >= this.storeInfoAreaHeight) {
        this.showFixedStoreInfo()
      } else if (this.data.isFixedStoreShow) {
        this.hideFixedStoreInfo()
      }
    }
  },

  // 跳转开发票页面
  handleApplyInvoice() {
    let invoiceAmount = Number(this.data.payAmount / 100).toFixed(2)
    if (this.data.invoiceInfo) {
      wx.navigateTo({
        url: `/bgxxUser/pages/invoice/applyInvoice/applyInvoice?fromPage=confirm&invoiceAmount=${invoiceAmount}&invoiceInfo=` +
          JSON.stringify(this.data.invoiceInfo)
      })
    } else {
      wx.navigateTo({
        url: `/bgxxUser/pages/invoice/applyInvoice/applyInvoice?fromPage=confirm&invoiceAmount=${invoiceAmount}`
      })
    }
  },

  /*
   *  选择发票
   *
   *  invoiceInfo 数据结构 （选择暂不需要时 传null）
   *
   *  {
   *    type: String // 发票类型，P:个人，C:企业
   *    title: String // 发票抬头
   *    taxCode: String // 纳税人识别号，当发票类型为P时非必填
   *    email : String // 电子发票接收邮箱
   *  }
   *
   */
  setSelectInvoice(invoiceInfo) {
    // console.log(invoiceInfo)
    this.setData({
      invoiceInfo
    })
  },

  // 跳转选择门店页面
  choiceStore(e) {
    const {
      selectedDeliveryWay,
      storeInfo: {
        deliveryToDoor = null
      } = {}
    } = this.data
    const { type = ''} = e.currentTarget.dataset
    if (selectedDeliveryWay === 'selfTake') {
      // 门店自提
      const sensorKey = !!type? 'bgxxConfirmOrderChangePickUpStore':'bgxxConfirmOrderChoosePickUpStore'
      sensors.track('MPClick', sensorKey)
      app.globalData.currStoreAddressInfo = this._data.bgxxSelectLocateInfo.selfTake
      wx.navigateTo({
        url: `/bgxxShop/pages/chooseStore/index?pageFrom=confirmOrder`
      })
    } else {
      // 配送上门
      const sensorKey = !!type? 'bgxxConfirmOrderChangeReceiveAddress':'bgxxConfirmOrderChooseReceiveAddress'
      sensors.track('MPClick', sensorKey)
      const { id = '', storeID = '' } = deliveryToDoor || {}
      const url = `/bgxxUser/pages/address/addressList/index?from=bgxxConfirmOrder&addressId=${id || -1}&storeID=${storeID}`;
      wx.navigateTo({
        url
      })
    }
  },

  /**
   * 修改门店自提门店
   * @param {Object} storeInfo
   */
  modifySelfTakeStore(bgxxSelectLocateInfo) {
    const { selectStoreInfo } = bgxxSelectLocateInfo
    this._data.bgxxSelectLocateInfo.selfTake = bgxxSelectLocateInfo
    this.setData({
        'storeInfo.selfTake': selectStoreInfo
      },
      this.setStoreInfoAreaHeight
    )
  },

  showFixedStoreInfo() {
    if (this.data.isFixedStoreShow) return

    this.setData({
        isFixedStoreShow: true,
      },
      () => {
        const query = wx.createSelectorQuery()

        query.select('#fixedAddressInfo').boundingClientRect(res => {
          if (res && res.height) {
            this.setData({
              pagePaddingBottom: res.height * 2 + defaultPagePaddingBottom + 'rpx'
            })
          }
        })

        query.exec()
      }
    )
  },

   /**
   * 关闭商品失效弹窗
   */
  hidePopup() {
    this.setData({
      popupVisible: false
    })
    // 如果普通商品全部下架了 或者刚进入页面就优惠信息变更 则这里应该弹窗提示优惠信息变更 并点击按钮后返回到上一页
    if (this._data.isNeedBack) {
      commonObj.showModal('提示', '抱歉，当前优惠信息发生变更，\r\n请重新确认订单信息', false, '我知道了', '', () => {
        // 返回上一页
        wx.navigateBack()
      })
      return
    }
    // 如果存在未弹窗的弹窗 则弹窗
    if (this._data.isNeedShowActivityChangePopup) {
      commonObj.showModal('提示', '抱歉，当前优惠信息发生变更，\r\n请重新确认订单信息', false, '我知道了', '', () => {})
      this._data.isNeedShowActivityChangePopup = false
    }
  },

  hideFixedStoreInfo() {
    this.setData({
      isFixedStoreShow: false,
      pagePaddingBottom: defaultPagePaddingBottom + 'rpx'
    })
  },

  changePayType(event) {
    const type = Number(event.currentTarget.dataset.type)
    if (!this.data.balanceInfo.enough && !type) {
      return
    }
    const sensorKey = type===0?'bgxxConfirmOrderVipWalletPay':'bgxxConfirmOrderWxPay'
    sensors.track('MPClick', sensorKey)
    this.setData({
      payType: type
    })
  },

  /**
   * @desc 根据首页门店获取配送地址
   */
  async getDeliveryAddress(setData = true) {
    const { storeInfo } = this.data
    // 有配送地址就不用继续执行下面的逻辑
    if ((storeInfo.deliveryToDoor || {}).id) { return storeInfo.deliveryToDoor }
    // 在这个接口里不管首页门店是否支持配送,交给结算接口校验
    // 能走进这里说明首页已经选了门店,无需做非空处理
    const bgxxSelectLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo')
    const { lat, lon, storeName, storeID }  = bgxxSelectLocateInfo.selectStoreInfo
    const { data } = await app.api.getAddressByDistance({
      // 能走进这里说明已经登录,无需做非空处理
      customerID: app.globalData.customerID,
      lat: String(lat),
      lon: String(lon)
    }).catch(() => ({ data: {} }))
    // 没地址
    if (!data.addressId) {
      this.setData({
        'storeInfo.deliveryToDoor': {}
      }, this.setStoreInfoAreaHeight)
      return {}
    }
    const address = {
      ...data,
      cityCode: bgxxSelectLocateInfo.selectAddressInfo.cityCode,
      id: data.addressId,
      storeID,
      storeName
    }
    // 有地址
    return setData ? this.changeDeliveryAddress(bgxxSelectLocateInfo, address) : address
  },

  /**
   * 切换自提（配送）
   */
  changeDeliveryWay(e) {
    const {
      status
    } = e.currentTarget.dataset
    this.setData({
      selectedDeliveryWay: status,
      isShowPopup: false
    })
    // 如果从配送上门切换到门店自提，存储配送上门所选择的送达时间
    if (status === 'selfTake') {
      this.saveOrderTimeInfo()
      sensors.track('MPClick', 'bgxxConfirmOrderChooseSelfTake')
    }
    if(status === 'deliveryToDoor'){
      sensors.track('MPClick', 'bgxxConfirmOrderChooseDeliver')
    }
    if (!!this.changeWayTimer) {
      clearTimeout(this.changeWayTimer)
    }
    this.changeWayTimer = setTimeout(() => {
      if (status === 'selfTake' && this.haveCycleGoods) {
        this.showSelfTakeCycleTip()
      } else {
        (status === 'deliveryToDoor' ? this.getDeliveryAddress() : Promise.resolve()).then(() => this.requestData(this.initPage))
      }
    }, 100)

    this.checkOrderHeight()
    this.closePurchaseBox()
  },

  checkOrderHeight() {
    const {
      storeInfo,
      selectedDeliveryWay,
      isFixedStoreShow
    } = this.data
    if (isFixedStoreShow && !storeInfo[selectedDeliveryWay]) {
      this.hideFixedStoreInfo()
    }
  },

  /**
   * 存储配送上门选择的送达时间
   */
  saveOrderTimeInfo() {
    this.initStoreOrderTimeInfo()
    const {
      haveStockGoods,
      haveAdvanceSaleGoods,
      haveCycleSaleGoods
    } = this.getGoodsIsHave()
    const {
      advanceSaleInfoList,
      cycleSaleInfoList
    } = this.data
    if (!!haveStockGoods) {
      const {
        deliveryTime,
        showDeliveryTime
      } = this.data.goodsInStockInfo
      this.storeOrderTimeInfo.stock = {
        deliveryTime,
        showDeliveryTime
      }
    }
    const subList = [{
      data: advanceSaleInfoList,
      haveGoods: haveAdvanceSaleGoods,
    }, {
      data: cycleSaleInfoList,
      haveGoods: haveCycleSaleGoods,
    }]
    subList.forEach(item => {
      if (!item.haveGoods) {
        return
      }
      item.data.forEach(innerIntem => {
        const {
          saleType,
          deliveryTime,
          showDeliveryTime,
          beginTakeTime = '',
          cycleGoodsId = -1
        } = innerIntem
        this.storeOrderTimeInfo[saleType === 'P' ? 'advance' : 'cycle'].push({
          saleType,
          deliveryTime,
          showDeliveryTime,
          beginTakeTime,
          cycleGoodsId
        })
      })
    })
  },

  getSaveDeliveryTime(list) {
    if (this.data.selectedDeliveryWay === 'selfTake') {
      return {}
    }
    const {
      saleType,
      beginTakeTime = '',
      cycleGoodsId = -1
    } = list
    const checkList = this.storeOrderTimeInfo[saleType === 'P' ? 'advance' : 'cycle']
    if (checkList.length > 0) {
      for (let i = 0, len = checkList.length; i < len; i++) {
        const {
          deliveryTime,
          showDeliveryTime,
          beginTakeTime: checkBeginTakeTime,
          cycleGoodsId: checkCycleGoodsId
        } = checkList[i]
        if (saleType === 'P') {
          if (beginTakeTime === checkBeginTakeTime) {
            return {
              deliveryTime,
              showDeliveryTime,
            }
          }
        } else if (saleType === 'C') {
          if (cycleGoodsId === checkCycleGoodsId) {
            return {
              deliveryTime,
              showDeliveryTime,
            }
          }
        }
      }
    }
    return {}
  },

  /**
   * 地址列表页切换地址
   * @param {object} bgxxSelectLocateInfo 首页选中的地址门店信息
   * @param {object} deliveryDoorParams 地址数据
   */
  changeDeliveryAddress(bgxxSelectLocateInfo, deliveryDoorParams) {
    this._data.bgxxSelectLocateInfo.deliveryToDoor = bgxxSelectLocateInfo
    this.setData({
      'storeInfo.deliveryToDoor': deliveryDoorParams
    })
  },

  /**
   * 配送上门有收货地址无门店提示
   */
  showDeliveryNoStore() {
    const {
      selectedDeliveryWay,
      storeInfo: {
        deliveryToDoor = null
      } = {}
    } = this.data
    if (selectedDeliveryWay === 'deliveryToDoor' && !!deliveryToDoor && !!deliveryToDoor.id) {
      this.selfModal.show({
        cancelText: '换门店自提',
        confirmText: '换收货地址',
        content: '您选择的收货地址暂时无门店可配送，请重新选择',
        confirmCallBack: () => {
          wx.navigateTo({
            url: '/bgxxUser/pages/address/addressList/index?from=bgxxConfirmOrder'
          })
        },
        cancelCallBack: () => {
          this.changeToSelfTake()
        }
      })
      return true
    }
    return false
  },

  /**
   * 确认下单前检查是否选择了配送时间
   */
  checkHaveDeliveryTime() {
    const {
      haveStockGoods,
      haveAdvanceSaleGoods,
      haveCycleSaleGoods
    } = this.getGoodsIsHave()
    const {
      goodsInStockInfo,
      advanceSaleInfoList,
      cycleSaleInfoList,
    } = this.data
    // 检查现售商品
    if (haveStockGoods) {
      // 现售和预售都要选择送达时间才行
      if (!goodsInStockInfo.deliveryTime || (advanceSaleInfoList && advanceSaleInfoList.length && !advanceSaleInfoList[0].deliveryTime)) {
        wx.showToast({
          title: '请选择送达时间',
          icon: 'none'
        })
        this.setData({
          scrollViewId: 'stock-list'
        })
        return false
      }
      const subOrderList = this.getSubOrderList()
      const { beginTakeTime='', deliveryTimeBegin='' } = subOrderList[0] || {}
      const that = this
      // 校验传给后端的送达时间是否在同一天
      if(beginTakeTime && deliveryTimeBegin && String(beginTakeTime) !== String(deliveryTimeBegin.split(' ')[0])){
        wx.showModal({
          title: '提示',
          content: '您选择的送达时间失效了，请重新选择',
          showCancel: false,
          confirmText: '重新选择',
          confirmColor: config.themeColorConfig.fruit,
          success(res){
            if(res.confirm){
              that.setData({
                goodsInStockInfo: null
              })
              that.initStoreOrderTimeInfo()
              that.requestData(that.initPage)
            }
          }
        })
        return false
      }
    }
    // 检查预售以及周期购商品
    const checkList = [{
      data: advanceSaleInfoList,
      haveGoods: haveAdvanceSaleGoods,
      viewIdAddr: 'advance-list-'
    }, {
      data: cycleSaleInfoList,
      haveGoods: haveCycleSaleGoods,
      viewIdAddr: 'cycle-list-'
    }]
    for (let index = 0, len = checkList.length; index < len; index++) {
      const {
        data,
        haveGoods,
        viewIdAddr
      } = checkList[index]
      if (haveGoods) {
        for (let dataIndex = 0, number = data.length; dataIndex < number; dataIndex++) {
          const {
            goodsList = null, deliveryTime = ''
          } = data[dataIndex]
          if (goodsList && goodsList.length) {
            if (!deliveryTime) {
              wx.showToast({
                title: `请选择${viewIdAddr.includes('advance-list') ? '' : '首次'}送达时间`,
                icon: 'none'
              })
              this.setData({
                scrollViewId: `${viewIdAddr}${dataIndex}`
              })
              return false
            }
          }
        }
      }
    }
    return true
  },

  /**
   * 选择配送时间无门店检测
   */
  pickerCheckStore(e) {
    // 现售只会有一个子单，预售可能会有多个子单，每个子单都可以单独选择时间，所以index用来标记选择的是第几个子单，-1表示现售
    const { item: deliveryTimeRange, index = 'goodsInStockInfo' } = e.currentTarget.dataset
    const { subOrderPickerObj } = this._data
    if (!deliveryTimeRange[1].length) {
      wx.showToast({
        title: '当前无配送门店，请重新选择收货地址',
        icon: 'none'
      })
      return
    }

    const parent = this.genPickerTimeParentArray(deliveryTimeRange)
    const children = this.genPickerTimeChildrenArray(deliveryTimeRange)

    // 第一列默认第一项，第二列默认不选择
    const deliveryTimePickerSeleted = (subOrderPickerObj[index] && subOrderPickerObj[index].length) ? subOrderPickerObj[index] : [0, null]

    this.setData({
      deliveryTimePickerSeleted,
      customPickerVisible: true,
      deliveryTimePickerArray: [parent, children]
    })
    this._data.currentSubOrderSelected = index

  },
  /**
   * @desc 匹配自定义Picker组件字段
   */
  genPickerTimeParentArray (deliveryTimeRange) {
    let parentPickerArray = deliveryTimeRange[0]
    return parentPickerArray.map(parent => {
      return {
        splitDate: parent.showTime
      }
    })
  },
  /**
   * @desc 匹配自定义Picker组件字段
   * @param {Array<Array<object>, Array<object>>} deliveryTimeRange
   * @param {number} currentParent 默认为1
   */
  genPickerTimeChildrenArray (deliveryTimeRange, currentParent = 0) {
    const limitTimeList = this._data.deliveryLimitInfo.reduce((acc, cur) => {
      // todo
      // cur.isFull = 0
      if (!cur.isFull) return acc
      // "2022-04-16 09:00:00"
      acc.push(cur.date)
      return acc
    }, [])
    let [ parentPickerArray, childPikcerArray ] = deliveryTimeRange
    return childPikcerArray.map(child => {
      // "09:00-10:00" => "09:00:00"
      let startTime = `${child.showTime.split('-')[0]}:00`
      // 组装成"2022-04-16 09:00:00"
      startTime = `${parentPickerArray[currentParent].time} ${startTime}`
      const disabled = limitTimeList.includes(startTime)
      return {
        dateDesc: child.showTime,
        labelType: disabled ? 'CIRIDA_DELIVERY_IS_FULL' : '',
        disabled
      }
    })
  },

  /**
   * @desc 选择时间
   * @param {object} e
   */
  handleCustomPickerPick (e) {
    const { detail: { current } } = e

    const index = this._data.currentSubOrderSelected

    let deliveryTimeRange = null
    let deliveryTimeKey = '',
      showDeliveryTimeKey = ''

    if (index === 'goodsInStockInfo') {
      // 现售
      deliveryTimeRange = this.data.goodsInStockInfo.deliveryTimeRange
      showDeliveryTimeKey = 'goodsInStockInfo.showDeliveryTime'
      deliveryTimeKey = 'goodsInStockInfo.deliveryTime'

      // 记录选择的Picker索引
      this._data.subOrderPickerObj.goodsInStockInfo = current
    } else {
      // 预售, 0_advanceSaleInfoList
      // 周期购 0_cycleSaleInfoList
      const key = index.split('_')[1] // 0_advanceSaleInfoList => advanceSaleInfoList
      const dataIndex = Number(index.split('_')[0]); // 0_advanceSaleInfoList => 0
      deliveryTimeRange = this.data[key][dataIndex].deliveryTimeRange
      deliveryTimeKey = `${key}[${dataIndex}].deliveryTime`
      showDeliveryTimeKey = `${key}[${dataIndex}].showDeliveryTime`

      // 记录选择的Picker索引
      this._data.subOrderPickerObj[index] = current
    }

    const time = deliveryTimeRange[0][current[0]].time,
      point = deliveryTimeRange[1][current[1]].showTime
    this.setData({
      [showDeliveryTimeKey]: `预计${time.split('-')[1]}-${
        time.split('-')[2]
      } ${point}送达`,
      [deliveryTimeKey]: time + ' ' + point,
      customPickerVisible: false
    })
    sensors.track('MPClick', 'bgxxConfirmOrderChangeDeliverTime')
    // 存储选择的配送时间
    this.saveOrderTimeInfo()
  },

  /**
   * @desc 改变Picker日期触发。现货、预售类型可选日期只会有一天，所以不用改变时间；周期购不需要处理
   * @param {object} e
   */
  handleCustomerPickerChangeDate (e) {
    const { detail: { current } } = e
    console.log('handleCustomerPickerChangeDate', current);
  },

  /**
   * picker时间变化设置选中的配送时间
   * @param {*} e
   */
  timeChange(e) {
  },

  /**
   * 获取当前是否有现售,预售,周期购商品
   */
  getGoodsIsHave() {
    const {
      goodsInStockInfo,
      advanceSaleInfoList,
      cycleSaleInfoList
    } = this.data
    return {
      haveStockGoods: goodsInStockInfo &&
        goodsInStockInfo.goodsList &&
        goodsInStockInfo.goodsList.length > 0,
      haveAdvanceSaleGoods: advanceSaleInfoList && advanceSaleInfoList.length > 0,
      haveCycleSaleGoods: cycleSaleInfoList && cycleSaleInfoList.length > 0
    }
  },

  /**
   * 拼装确认订单接口需要的配送上门时间信息（返回参见接口请求说明）
   */
  getSubOrderList() {
    const subOrderList = []
    const {
      goodsInStockInfo,
      advanceSaleInfoList,
      cycleSaleInfoList,
      selectedDeliveryWay
    } = this.data
    const {
      haveStockGoods,
      haveAdvanceSaleGoods,
      haveCycleSaleGoods
    } = this.getGoodsIsHave()

    const getDeliveryTime = deliveryTime => {
      const time = deliveryTime.split(' ')
      return {
        deliveryTimeBegin: `${time[0]} ${time[1].split('-')[0]}:00`,
        deliveryTimeEnd: `${time[0]} ${time[1].split('-')[1]}:00`
      }
    }

    // 现货
    if (haveStockGoods) {
      // 现货按照原始接口订单拆单拼接参数
      for (let index = 0; index < this.stockOrderNum; index++) {
        const {
          beginTakeTime,
          saleType,
        } = goodsInStockInfo
        const params = {
          saleType,
          beginTakeTime
        }
        if (selectedDeliveryWay === 'deliveryToDoor') {
          Object.assign(params, {
            storageType: goodsInStockInfo[`storageType${index}`],
            ...getDeliveryTime(goodsInStockInfo.deliveryTime)
          })
        }
        subOrderList.push(params)
      }
    }
    // 预售及周期购
    const subList = [{
      data: advanceSaleInfoList,
      haveGoods: haveAdvanceSaleGoods,
      viewIdAddr: 'advance'
    }, {
      data: cycleSaleInfoList,
      haveGoods: haveCycleSaleGoods,
      viewIdAddr: 'cycle'
    }]
    subList.forEach(item => {
      const {
        data,
        haveGoods,
        viewIdAddr
      } = item
      if (haveGoods) {
        data.forEach(innerItem => {
          const {
            goodsList = null,
          } = innerItem
          if (goodsList && goodsList.length) {
            const {
              beginTakeTime,
              saleType,
              storageType
            } = innerItem
            const params = {
              saleType,
              beginTakeTime,
            }
            if (selectedDeliveryWay === 'deliveryToDoor') {
              Object.assign(params, {
                storageType,
                ...getDeliveryTime(innerItem.deliveryTime)
              })
            }
            if (viewIdAddr === 'cycle') {
              Object.assign(params, {
                cycleGoodsId: innerItem.cycleGoodsId,
                isCombined: innerItem.isCombined,
              })
            }
            subOrderList.push(params)
          }
        })
      }
    })
    return subOrderList
  },

  /**
   * 切换到门店自提
   */
  changeToSelfTake() {
    this.setData({
      selectedDeliveryWay: 'selfTake'
    })
    this.saveOrderTimeInfo()
    if (this.haveCycleGoods) {
      this.showSelfTakeCycleTip()
    } else {
      this.requestData(this.initPage)
    }
  },

  onHide() {
    if (this.data.selectedDeliveryWay === 'deliveryToDoor') {
      this.saveOrderTimeInfo()
    }
    this.closePurchaseBox()
  },

  initStoreOrderTimeInfo() {
    // 存储配送上门不同单(现售，预售，周期购)选择的送达时间
    this.storeOrderTimeInfo = {
      stock: {},
      advance: [],
      cycle: []
    }
  },

  showSelfTakeCycleTip() {
    const that = this
    // 周期购商品只能配送上门
    wx.showModal({
      title: '',
      content: '周期购商品仅支持配送上门哦～',
      showCancel: false,
      confirmText: '知道了',
      success(res) {
        if (res.confirm) {
          that.requestData(that.initPage)
        }
      }
    })
  },
  showFullReduction() {
    wx.showModal({
      title: '',
      content: `新客专享商品，周期购商品，部分特价商品不参与，活动最高可优惠${+(this.data.currUseActivity.activityMaxFree / 100).toFixed(2)}元。`,
      showCancel: false,
      confirmText: '我知道啦',
      confirmColor: config.themeColorConfig.fruit
    })
  },
  showDiscountModal() {
    sensors.track('MPClick', 'bgxxConfirmOrderChooseActivities')
    this.setData({
      showChangeActivity: true
    })
  },
  setPayParams() {
    const { currUseActivity } = this.data
    let finalAmount = this.noActivityAmount
    // 优惠券
    this.data.selectedCouponList.forEach(item => {
      finalAmount = finalAmount - item.couponMoney
    })
    // 满折活动
    if (!!currUseActivity && currUseActivity.activityType === 'P') {
      finalAmount = finalAmount - currUseActivity.activityFree
    }
    // 一元购活动
    if(Number(this.data.oneBuyFreeAmount) !== 0 && this.data.oneBuyOverrun){
      finalAmount = finalAmount - this.data.oneBuyFreeAmount
    }
    finalAmount =  Math.max(finalAmount, 0)
    let enough = this.mainBalance >= finalAmount
    this.setData({
      payAmount: finalAmount,
      'balanceInfo.enough': enough,
      allGoodsDisabled: this._data.goodsDisabled || !finalAmount,
      payType: (app.globalData.reqPayType === 0 && enough) ? 0 : 1 // 请求到为0 则走原先的逻辑 为1则按照配置1
    })
  },
  onCloseActivityChange() {
    this.setData({
      showChangeActivity: false
    })
  },
  activityChange({ detail: { index } }) {
    this.onCloseActivityChange()
    let result = null
    this.data.joinActivityList.forEach((item, i) => {
      if (index === i) {
        item.isSelected = 'Y'
        result = item
      } else {
        item.isSelected = 'N'
      }
    })
    this.setData({
      joinActivityList: this.data.joinActivityList,
      currUseActivity: result
    })
    this.requestData(this.initPage)
    this.calculateOrderDiscount()
  },
  /**
   * 进入选择优惠券
   */
  openSelectCoupon() {
    const list = app.globalData.bgxxUseCouponList || []
    list.forEach(item => {
        const selectedItem = this.data.selectedCouponList.find(obj => {
          return String(obj.couponID) === String(item.couponID)
        })
        item.isSelected = !!selectedItem ? 1 : 0
    })
    app.globalData.bgxxUseCouponList = list
    sensors.track('MPClick', 'bgxxConfirmOrderChooseCoupon')
    wx.navigateTo({ url: '/bgxxShop/pages/selectCoupon/index' })
  },
  /**
   * 选择优惠券页修改当前选择适用的券
   */
  changeSelectCoupon(list) {
    this._data.fromSelectCouponBack = true
    this.setData({
      selectedCouponList: list,
      couponCheapMoney: this.getCouponCheapMoney(list),
    })
    this.setPayParams()
    this.getVipCouponCheapMoney(list)
    this.calculateOrderDiscount()
  },
  // 展示商品清单
  showGoodsList(e) {
    let list = e.currentTarget.dataset.goodsList || []
    // 弹窗商品总数
    const popupGoodsCount = list.reduce((sum, item) => sum + item.count, 0)
    this.setData({
      isShowPopup: true,
      goodsList: list,
      popupGoodsCount
    })
  },
  /**
   * 切换配送上门配送门店
   */
  changeDeliveryDoorStore() {
    const { storeID, storeName, id = '' } = this.data.storeInfo.deliveryToDoor
    if (!id) {
      return
    }
    const { lat = -1, lon = -1, cityID = -1, gisProvince = '',  cityName = '', gisDistrict = '', gisAddress = '', cityCode } = this.data.storeInfo.deliveryToDoor
    const switchDistriStoresParams = {
      storeID,
      storeName,
      address: `${gisProvince}${cityName}${gisDistrict}${gisAddress}`,
      lat,
      lon,
      cityID,
      cityCode
    }
    sensors.track('MPClick', 'bgxxConfirmOrderChangeDeliverStore')
    app.globalData.switchDistriStoresParams = switchDistriStoresParams
    wx.navigateTo({ url: '/bgxxShop/pages/switchDistriStores/index' });
  },
  /**
   * 修改配送门店
   */
  modifyDeliveryDoorStore(selectStoreInfo) {
    Object.assign(this._data.bgxxSelectLocateInfo.deliveryToDoor, {
      selectStoreInfo
    })
    const { deliveryToDoor = {} } = this.data.storeInfo
    if(selectStoreInfo.address){
      delete selectStoreInfo.address
    }
    Object.assign(deliveryToDoor, selectStoreInfo)
    this.setData({
      'storeInfo.deliveryToDoor': deliveryToDoor
    })
  },
  /**
   * 处理一元购未达条件弹窗的跳转
   * 未达条件弹窗：1表示放弃机会，点击会弹出二次确认弹窗，2表示去看看，点击会跳转首页
   * 二次确认弹窗：3表示直接放弃，点击会放弃参加一元购活动并重新进行结算，4表示去看看，点击会跳转首页
   */
  handleButton(e){
    const { type } = e.currentTarget.dataset
    switch(type){
      case '1':
        sensors.track('MPClick', 'bgxxConfirmOrderOneBuyGoodsGiveUp')
        break;
      case '2':
        sensors.track('MPClick', 'bgxxConfirmOrderOneBuyGoodsCheck')
        wx.navigateTo({
          url: '/pages/xxshop/index/index'
        })
        break;
      case '3':
        sensors.track('MPClick', 'bgxxConfirmOrderOneBuyGoodsGiveUpTrue')
        this.setData({
          isJoinOneBuy: 'N',
          currUseActivity: null
        })
        this.requestData(this.initPage)
        break;
      case '4':
        sensors.track('MPClick', 'bgxxConfirmOrderOneBuyGoodsFind')
        wx.navigateTo({
          url: '/pages/xxshop/index/index'
        })
        break;
    }
  },
  /**
   * 展示（关闭）费用弹窗说明
   * 1为包装费弹窗说明，2为配送费弹窗说明，3为运费规则弹窗说明
   */
  showCostTips(e){
    const { type } = e.currentTarget.dataset
    if(Number(type) === 3){
      this.setData({
        showRuleBox: !this.data.showRuleBox
      })
    }else{
      this.setData({
        showCostBox: !this.data.showCostBox,
        costBoxType: type ? Number(type) : 1
      })
    }
  },
  /**
   * 开启关闭选择包装费弹窗
   */
  closeOpeningPackingBox() {
    const { selectPackingBagBox } = this.data
    this.setData({
      selectPackingBagBox: !selectPackingBagBox,
    })
  },
  /**
   * 自提模式下选择是否需要包装费
   * @param {Number} type 0不需要，1需要
   */
  changeNeedPackingBag(e) {
    const { type } = e.currentTarget.dataset
    this.setData({
      isNeedPackingBag: Number(type)
    })
    this.requestData(this.initPage)
  },
  /**
   * 关闭上次下单方式提示
   */
  closePurchaseBox() {
    this.setData({
      showPurchaseBox: false
    })
  },
  /**
   * 1. 当刚进入此页面时 如果不满足换购活动条件 这会弹窗提示并返回上一页
   * 2. 所有的普通商品都下架时，也需要弹窗提示后返回上一页
   */
  showActiveChangeMsg(data) {
    const hasInvalidGoods = data && !!data.unSatisfyOneBuyPupop &&
        data.unSatisfyOneBuyPupop.oneBuyGoodsList
          && data.unSatisfyOneBuyPupop.oneBuyGoodsList.length
    // 后台返回的数据有失效的商品
    if (hasInvalidGoods) {
      // 如果是没有结算成功过(刚进入页面) 则直接退回到上一页
      if(!this._data.isFirstSettled) {
        this._data.isNeedBack = true
        // 如果有失效商品弹窗 则不会覆盖弹窗(等前面的弹窗先关闭)
        if (data.toastGoodsList && data.toastGoodsList.length > 0) {
          return
        }
        // 否则 需要立即弹窗
        this.hidePopup()
      } else {
        this._data.isNeedShowActivityChangePopup = true
      }
      // 如果包含下架的商品（并非售罄），则需要按照后端的要求直接返回上一页
      // 判断是否包含下架
      this._data.isNeedBack = data.toastGoodsList && data.toastGoodsList.some(item => item.isShelf === 'N' || item.shelfStatus === 'D')
    }
    this._data.isFirstSettled = true
  },
  /**
   * 当刚进入此页面时 如果不满足特价活动条件 这会弹窗提示活动失效或者特价数量不满足条件
   */
  showSpecialActiveChangeMsg(data) {
    const hasInvalidGoods = data && !!data.unAvailableSpecialGoodsList &&
        data.unAvailableSpecialGoodsList.length
    // 是否要提示活动信息变更
    let isShowActiveChangeConfirm = false
    // 后台返回的数据有特价不参与的商品
    if (hasInvalidGoods) {
      // 遍历所有的不满足特价活动的商品
      for (let index = 0; index < data.unAvailableSpecialGoodsList.length; index++) {
        // 每一个不满足的商品
        const unAvailableSpecial = data.unAvailableSpecialGoodsList[index]
        // 判断找到购物车传入的具体项
        const cartGood = this._data.specialActivityGoodsList.find(item => item.goodsID === unAvailableSpecial.id)
        if (cartGood) {
          // 修正特价活动数量
          cartGood.count = unAvailableSpecial.joinSpecialPriceCount
          // 判断是否与购物车页面的最大可参与特价活动数量是否一致 如果不一致 则需要弹窗显示
          if (unAvailableSpecial.joinSpecialPriceCount !== cartGood.maxEffectNum) {
            isShowActiveChangeConfirm = true
          }
        }
      }
    }
    // 如果要弹窗显示则显示
    // 因为同一时间，活动变更弹窗只需要弹一个（其他要弹窗的会先弹，这里就不用再弹了） 所以这里做了互斥判断处理
    if (isShowActiveChangeConfirm && !this._data.isNeedBack && !this._data.isNeedShowActivityChangePopup) {
      commonObj.showModal('提示', '抱歉，当前优惠信息发生变更，\r\n请重新确认订单信息', false, '我知道了', '', () => {})
    }
  },
  /**
   * 特价活动编码修正 如果在结算时发现有参与特价活动 则可能需要更新到最新的特价活动编码
   * 目的：因为提交订单时或者确认订单时可能活动信息发生变更，此时需要修正旧的活动编码
   */
   resetSpecialActiveCode(data) {
    const hasSpecialGoods = data && !!data.specialActivities &&
        data.specialActivities.length

    // 后台返回的商品有特价
    if (hasSpecialGoods) {
      // 遍历所有的不满足特价活动的商品
      for (let index = 0; index < data.specialActivities.length; index++) {
        const currentItem = data.specialActivities[index]
        // 1. 更改结算参数
        this.goodsList.filter(item => item.goodsID === currentItem.goodsID).forEach(good => good.activityCode = currentItem.specialCode)
        // 2. 更改提交订单参数
        this._data.specialActivityGoodsList.filter(item => item.goodsID === currentItem.goodsID).forEach(good => good.activityCode = currentItem.specialCode)
      }
    }
  },
  /**
   * 提交订单成功后埋点上报
   */
  submitOrderSuccessReport(result) {
    try {
      const { selectedCouponList = [], payAmount, orderDiscount, goodsInStockInfo = {} } = this.data
      const { goodsList = [] } = goodsInStockInfo || {}
      const { skuNum, deliveryWay, orderType } = this.getReportInfoList() || {}
      const { payOrderNum, goodsOrderDataList } = result || {}
      const { number: mainOrderNo } = goodsOrderDataList[0] || {}
      const couponNameList = selectedCouponList.map(({couponName}) => couponName)
      const SKU_Name = []
      goodsList.forEach(item => {
        const { name } = item || {}
        SKU_Name.push(name)
      })
      const reportData = {
        mainOrderNo: mainOrderNo,
        OrderType: orderType,
        DeliveryWay: deliveryWay,
        sku_cnt: skuNum,
        orderAmount: util.formatPrice(payAmount + orderDiscount),
        actualPaymentAmount: util.formatPrice(payAmount),
        couponType: [],
        couponBatchNumList: [],
        couponNameList,
        SKU_Name
      }
      sensors.track('SubmitOrder', reportData)

      // 订单详情埋点上报
      const arrOrderDetailReportData = []
      goodsList.forEach(item => {
        const { id, name, goodsLabelList = [] } = item || {}
        arrOrderDetailReportData.push({
          mainOrderNo: mainOrderNo,
          OrderType: orderType,
          SKU_ID: id,
          SKU_Name: name,
          goods_label: goodsLabelList
        })
      })
      // 上报埋点
      arrOrderDetailReportData.forEach(item => sensors.track('SubmitOrderDetail', item))
    } catch(e) {
      console.error(e)
    }
  },
  /**
   * 整合需要上报的信息
   */
  getReportInfoList() {
    const {
      goodsInStockInfo = {},
      advanceSaleInfoList = [],
      selectedDeliveryWay,
    } = this.data
    const deliveryWay = selectedDeliveryWay === 'deliveryToDoor' ? '配送上门' : '自提'
    const saleType = goodsInStockInfo.saleType || advanceSaleInfoList[0].saleType || ''
    const goodsList = saleType === 'N' ? goodsInStockInfo.goodsList : advanceSaleInfoList[0].goodsList
    const orderTypeText = saleType === 'N' ? '次日达现售' : '次日达预售'
    const orderType = selectedDeliveryWay === 'deliveryToDoor' ? `${orderTypeText}到家` : `${orderTypeText}自提`
    const skuNum = (goodsList || []).reduce((sum, cur) => {
      return sum + (cur.count || 0)
    }, 0)
    return {
      skuNum,
      deliveryWay,
      orderType
    }
  }
})
