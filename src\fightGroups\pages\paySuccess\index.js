// fightGroups/pages/paySuccess/index.js
var commonObj = require('../../../source/js/common').commonObj;
var pintuanuser = require('../../source/image-base64/pintuanuser');
const sensors = require('../../../utils/report/sensors')
const { generateShareAttr } = require('../../../utils/report/setup')
var picUrl = commonObj.PAGODA_PIC_DOMAIN;
var timeDown = null;
var app = getApp();
const drawSharePic = require('../../mixins/drawSharePic')
const { drawPosterPic, getPosterModelSize } = require('../../source/js/drawPoster/fightGroupsDrawPoster')
import { updateUserDeviceInfo } from '../../../service/userService';

import {
  drawBarcode
} from '../../../utils/services/canvasUtil'
Page({
  mixins: [drawSharePic],
  /**
   * 页面的初始数据
   */
  data: {
    bg_pintuan: pintuanuser.bg,
    bg_success: 'https://resource.pagoda.com.cn/group1/M00/22/3C/CmiWiF7fcuGAWChhAAAc721jiPg214.png',
    picHost: 'https://resource.pagoda.com.cn/miResourceMgr/',
    picUrl: picUrl,
    layh: '00',
    laym: '00',
    lays: '00',
    tipsShow: false,
    joinSuccess: true,
    picChange: '/source/images/icon_label_tick2.png',
    ct: 0, //countDown() 方法中递归次数计次，实现排除本地时间影响的倒计时
    count: 0,
    isJoin: false,
    showLayer: false,  // 引导添加我的小程序浮层页展示与否
    isShare: false,
    getUrl: commonObj.PAGODA_DSN_DOMAIN,
    goodsDetailObj: {},
    memberTakeCode: '', // 提货码
    defaultHeadPic: 'https://resource.pagoda.com.cn/group1/M0D/35/0C/CmiLkF_Rx3yAbYZOAAAxYfPnwCY517.png' // 默认用户头像
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    var that = this;
    wx.showLoading({
      title: '加载中...',
      mask: 'true',
    });

    if (options && options.payOrderIDorGoodsOrderID) {
      var isPaySuccess;
      var page = getCurrentPages();

      if (options.fromPTorderDetail) {
        console.log('从确认订单过来的');
        isPaySuccess = true;
      } else {
        console.log('从订单详情过来的');
        isPaySuccess = false;
      }
      that.setData({
        isPaySuccess: isPaySuccess,
        id: options.payOrderIDorGoodsOrderID,
      });

      if (isPaySuccess) {
        that.checkPayResultFunc(options.payOrderIDorGoodsOrderID);
      } else {
        that.getOrderDetailInfo(options.payOrderIDorGoodsOrderID, isPaySuccess);
      }
    }
    if (options && options.isAutoCreate==='Y') {
      wx.showModal({
        title: '', //弹框标题
        content: '您所参加的团已满，已为您新开了一个团', //提示内容
        showCancel:false,
        confirmText: '我知道了',
        confirmColor: '#999999',
        success: function () { }
      });
    }
    if (options.groupId > -1) {
      // 用户是参团，用于神策数据上报
      that.setData({ isJoin: true })
    }
    // 开团页支付成功展示引导页
    if (!this.data.isJoin) {
      const viewedGuidePage = wx.getStorageSync('viewedGuidePage') || {}
      if (!viewedGuidePage.paySuccessPageShow) {
        this.setData({ showLayer: true })
        wx.setStorage({
          key: 'viewedGuidePage',
          data: Object.assign(viewedGuidePage, {paySuccessPageShow: true})
        })
      }
    }
    // let user = wx.getStorageSync('user');
    // this.getOrderDetailInfo("79902063586", user, true); //这两行注释模拟拼团成功页，改第一个参数为固定paymentOrderID
    that.getHomePageInfo();
    // 设置分享海报弹窗的宽高，分享海报缩略图的宽高
    that.setPosterModelSize ()
    // 上报本次支付成功的设备信息
    updateUserDeviceInfo()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function (options) {
    var that = this
    // 扫码页面保持屏幕长亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })
    // 上报神策数据
    if (this.data.isJoin) {
      sensors.pageShow('fightGroupsJoinGroupPage', {
        groupEventID: that.data.activityID || '',
        groupEventName: that.data.activityName || ''
      })
    } else {
      sensors.pageShow('fightGroupsOpenGroupPage')
    }
  },
  onHide:function() {
    // 离开页面关闭扫码页面保持屏幕长亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
  },
  onUnload:function() {
    // 离开页面关闭扫码页面保持屏幕长亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
    try {
      wx.getStorageSync('goodsOrderID') && wx.removeStorageSync('goodsOrderID')
      wx.removeStorageSync('headPic')
    } catch (e) {

    }
  },
  /**
   * 设置分享海报弹窗的宽高，分享海报缩略图的宽高
   */
  async setPosterModelSize () {
    const { modelWidth, modelHeight, smallPosterWidth, smallPosterHeight } = await getPosterModelSize()
    this.setData({
      modelWidth,
      modelHeight,
      smallPosterWidth,
      smallPosterHeight
    })
  },
  // 用户点击右上角分享
  onShareAppMessage: function (res) {
    let that = this
    let { storeID = 0, storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
    const shareAttr = generateShareAttr()

    if (res.from === 'button') {
      let user = wx.getStorageSync('user');
      let id = res.target.dataset.goodsorderid || wx.getStorageSync('goodsOrderID');
      // console.log(
      //   'goodsorderid:' + id + ';2:' + wx.getStorageSync('goodsOrderID'),
      // );
      let paySuccessObj = {
        goodsOrderID: id,
        userId: user.userID,
        userToken: user.userToken,
        mp_shareID: shareAttr.mp_shareID,
        mp_shareTitle: this.data.groupShareTitle,
        mp_activityID: this.data.activityID,
        mp_activityName: this.data.activityName,
        mp_groupID: this.data.groupID,
        mp_openerID: this.data.openerID
      }
      let shareObj = {
        ...shareAttr,
        mp_shareTitle: that.data.groupShareTitle,
        activity_ID: that.data.activityID,
        activity_Name: that.data.activityName,
        groupID: that.data.groupInfo.groupID,
        groupSize: that.data.groupInfo.groupSize,
        openerID: that.data.groupInfo.openerID,
        currentCount: that.data.groupInfo.currentCount,
        expireTime: that.data.groupInfo.expireTime,
        storeID,
        storeName,
        storeNum: storeInfo.storeCode || ''
      }

      if (app.globalData.reportSensors) {
        app.sensors.track('MPClick', {
          element_code: '110400002',
          element_name: '邀请好友参团',
          element_content: '邀请好友参团按钮',
          screen_code:'1104'
        })
      }
      wx.reportAnalytics('paysuc_invitefriendssuc')
      if (app.globalData.reportSensors) {
        app.sensors.track('MPShare', shareObj)
      }
      let title = this.data.groupShareTitle
      return {
        title: title,
        // path: `/fightGroups/pages/inviteFriends/index?paySuccessObj=${JSON.stringify(paySuccessObj)}&shareObj=${JSON.stringify(shareObj)}`,
        path: `/pages/homeDelivery/index?home2InviteFriends=${JSON.stringify(paySuccessObj)}&shareObj=${JSON.stringify(shareObj)}`,
        // imageUrl: picUrl + this.data.groupSharePic
        imageUrl: this.data.mixinSharePic
      }
    } else {
      let shareObj = {
        ...shareAttr,
        mp_shareTitle: '有人@我 开团了~~',
        activity_ID: that.data.activityID,
        activity_Name: that.data.activityName,
        groupID: that.data.groupInfo.groupID,
        groupSize: that.data.groupInfo.groupSize,
        openerID: that.data.groupInfo.openerID,
        currentCount: that.data.groupInfo.currentCount,
        expireTime: that.data.groupInfo.expireTime,
        storeID,
        storeName,
        storeNum: storeInfo.storeCode || ''
      }
      wx.reportAnalytics('paysuc_invitefriendssuc')
      if (app.globalData.reportSensors) {
        app.sensors.track('MPShare', shareObj)
      }
      return {
        title: '有人@我 开团了~~',
        path: `/fightGroups/pages/fightGroups/index?shareObj=${JSON.stringify(shareObj)}`,
        imageUrl: 'https://resource.pagoda.com.cn/group1/M00/14/39/CmiWiF3OFXqAaqfqAACAY5XbFfo343.jpg',
        success: function (res) {
          // 转发成功
        },
        fail: function (res) {
          // 转发失败
          wx.reportAnalytics('paysuc_invitefriendsfail')
        },
      }
    }
  },
  animationShare: function () {
    var that = this;
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    that.animation = animation;
    animation.translateY(-158).step();
    that.setData({ animationShare: animation.export() });

    setTimeout(function () {
      that.setData({ animationShare: animation })
    }.bind(that), 400)
  },
  share: function (e) {
    console.log(e)
    var that = this;
    var type = String(e.currentTarget.dataset.type);
    if (type === 'share') {
      that.setData({ isShare: true })
      that.animationShare();
    } else if (type === 'thumbnail') {
      // that.setData({ isThumbnail: true })
      if (!wx.showShareImageMenu) {
        that.setData({ isThumbnail: true })
      }
      that.utilThumbnail()
      that.drawPoster()
    }
  },
  // 缩略图弹框
  utilThumbnail: function () {
    var that = this;
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    that.animation = animation;
    animation.translateY(-parseInt((that.data.modelHeight / 2))).step();
    that.setData({ animationImage: animation.export() });

    setTimeout(function () {
      this.setData({ animationImage: animation })
    }.bind(this), 300)
  },
  // 绘制canvas图
  async drawPoster () {
    const that = this
    const { shareImage, picUrl } = that.data
    // 若海报已经生成过了，不用重新生成
    if (shareImage) {
      return
    }
    wx.showLoading({
      title: '正在生成海报',
    })
    try {
      const { headPic: headPicUrl, groupShareTitle: goodsName, spec: goodsWeight, groupSize, groupPrice: currPrice, price: originalPrice } = that.data.goodsDetailObj || {}
      const headPic = `${picUrl}${headPicUrl}` // 商品头图
      const groupPrice = (currPrice / 100).toFixed(2); // 拼团价
      const originPrice = !!originalPrice ? (originalPrice / 100).toFixed(2) : 0 //商品原价
      const goodsOrderId = wx.getStorageSync('goodsOrderID')
      const { userID } = wx.getStorageSync('user') || {}
      const sceneUrl = `SCANCODE@${goodsOrderId}@${userID}` // 小程序码对应页面链接的参数
      const goodsObj = {
        headPic,
        goodsName,
        goodsWeight,
        groupSize,
        groupPrice,
        originPrice,
        sceneUrl
      }
      // 绘制海报
      const canvasId = 'posterCanvas'
      const shareImage = await drawPosterPic(canvasId, goodsObj)
      // that.setData({
      //   shareImage
      // })
      that.showSharePoster(shareImage)
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      wx.showLoading({
        title: '生成海报出错啦，请稍后重试',
      })
    }
  },
  /**
   * 展示生成的海报
   */
  showSharePoster(path) {
    if (!wx.showShareImageMenu) {
      this.setData({
        shareImage: path
      })
    } else {
      wx.showShareImageMenu({
        path: path
      })
    }
  },
  // 海报保存到本地
  savePoster: function () {//shareImage
    var that = this;
    wx.showLoading({
      title: '正在生成海报'
    })
    wx.saveImageToPhotosAlbum({
      filePath: this.data.shareImage,
      success: (res) => {
        wx.hideLoading()
        wx.showToast({ title: '保存成功，快去分享吧~', icon: 'none' });
      },
      fail: (err) => {
        this.setData({ status: 'auth denied' })
        wx.hideLoading()
        commonObj.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了', '', function (res) {
          if (res.confirm && that.data.status === 'auth denied') {
            wx.openSetting()
          }
        })
      }
    })
  },
  closeModel: function (e) {
    wx.hideLoading()
    var that = this;
    var type = e.currentTarget.dataset.type;
    if (type === 'share') {
      that.setData({ isShare: false })
    } else if (type === 'thumbnail') {
      that.setData({ isThumbnail: false })
    }
  },
  /***********************功能函数 **************************/
  // 查询订单是否支付回调完成
  checkPayResultFunc: function (id) {
    var that = this;
    var payOId = id;
    var user = wx.getStorageSync('user');
    var options = {
      data: {
        customerId: user.userID,
        payOrderId: payOId, // 商品订单ID
      },
      url: '/api/v1/groupBuy/order/checkPayResult',
    };
    commonObj.requestData(options, function (res) {

      if (Number(res.data.errorCode) === 0) {
        if (res.data.data.payResult === 'Y') {
          that.getOrderDetailInfo(payOId, true);
        } else {
          var countNum = that.data.count;

          if (countNum < 20) {
            countNum++;
            that.setData({
              count: countNum,
            });
            setTimeout(function () {
              that.checkPayResultFunc(payOId);
            }, 500);
          } else {
            that.getOrderDetailInfo(payOId, true);
            console.log('回调不成功');
          }
        }
      } else {
        commonObj.showModal(
          '提示',
          '信息获取失败,请稍后重新请求。异常错误信息：' + res.data.errorMsg,
          false,
          '确认',
          '',
          function () {
            wx.redirectTo({
              url: '/fightGroups/pages/fightGroups/index'
            });
          },
        );
      }
    });
  },

  // 订单详情 orderId是订单id
  getOrderDetailInfo: function (id, isPaySuccess) {

    var that = this;
    var city = wx.getStorageSync('timelyCity') || {}
    var user = wx.getStorageSync('user')
    if (isPaySuccess) {
      let options = {
        header: {
          userToken: user.userToken,
        },
        data: {
          customerID: user.userID,
          cityID: city.cityID,
          paymentOrderID: id, // 商品订单ID
          source: 'paySuccess',
        },
        url: '/api/v1/groupBuy/order/paySuccess',
      };
      that.handlePaySuccessOrder(options)
    } else {
      let options = {
        header: {
          'X-DEFINED-appinfo': JSON.stringify({
            "channel": "miniprogram", //渠道
            "model": "iPhone 6",
            "os": "iOS 10.1.1",
            "verName": "*******",
            "deviceID": "35EAAF0C-D590-4F40-8F73-73735FDC02E5",
          }),
          "userToken": user.userToken
        },
        url: `/api/v1/wxmini/order/detail/${user.userID}/${id}`
      }
      that.handleOtherOrder(options)
    }
  },

  handlePaySuccessOrder (options) {
    var that = this
    var user = wx.getStorageSync('user')
    commonObj.requestData(
      options,
      function (res) {

        if (Number(res.data.errorCode) === 0 && res.data.data) {
          if (res.data.data.groupInfo.isAutoCreate==='Y') {
            wx.showModal({
              title: '', //弹框标题
              content: '您所参加的团已满，已为您新开了一个团', //提示内容
              showCancel: false,
              confirmText: '我知道了',
              confirmColor: '#999999',
              success: function () { }
            })
          }

          wx.setNavigationBarTitle({
            title: '支付成功',
          })

          that.setData({
            currTime: res.data.systemTime, // 服务器当前时间
          })

          let data = res.data.data
          let goodsInfo = data.groupGoodsInfo || data.goodsList[0]
          let groupInfo = data.groupInfo

          that.setOrderDetailData (data, groupInfo, goodsInfo)

        } else if (Number(res.data.errorCode) === 30104) {
          commonObj.showModal('温馨提醒', '此团已经失效啦，下次早点来~已付的款项按原路返还', false, '开个新团', '', function () {
            wx.redirectTo({
              url: '/fightGroups/pages/fightGroups/index'
            });
          })
        } else if (Number(res.data.errorCode) === 30101) {
          commonObj.showModal('温馨提醒', '此团已经取消啦，下次早点来~已付的款项按原路返还', false, '开个新团', '', function () {
            wx.redirectTo({
              url: '/fightGroups/pages/fightGroups/index'
            });
          })
        } else if (Number(res.data.errorCode) === 30109) {
          commonObj.showModal('温馨提醒', '此团已经满啦，下次早点来~已付的款项按原路返还', false, '开个新团', '', function () {
            wx.redirectTo({
              url: '/fightGroups/pages/fightGroups/index'
            });
          })
        } else {
          commonObj.showModal('提示', '信息获取失败,请稍后重新请求。异常错误信息：' + res.data.errorMsg, false, '确认', '', function () {
            wx.redirectTo({
              url: '/fightGroups/pages/fightGroups/index'
            });
          },
          );
        }
        wx.hideLoading();
      },
      '',
      function () {
        that.setData({ isReady: true });
      },
    );
  },

  handleOtherOrder(options) {
    var that = this
    commonObj.requestNewData(
      options,
      function (res) {
        if (Number(res.data.errorCode) === 0 && res.data.data) {

          wx.setNavigationBarTitle({
            title: '邀请好友',
          })

          that.setData({
            currTime: res.data.systemTime, // 服务器当前时间
          })

          let goodsInfo = {}
          let groupMemberList = {}; // 参团用户的信息
          let groupInfo = {}
          let data = res.data.data
          let { goodsList, group = {} } = data

          Object.assign(goodsInfo, goodsList[0], {
            groupPrice: goodsList[0].price,
            price: goodsList[0].originalPrice,
            groupSize: group.groupSize,
            groupSharePic: goodsList[0].headPic,
            groupShareTitle: group.groupActivityInfo.shareTitle,
            activityID: group.groupActivityInfo.id,
            activityName: group.groupActivityInfo.name,
          })

          Object.assign(groupMemberList, {
              customerID: group.groupMemberList[0].customerId,
              wxUnionID: group.groupMemberList[0].wxUnionId,
              wxOpenID: group.groupMemberList[0].wxOpenId,
              goodsOrderID: group.groupMemberList[0].goodsOrderId,
              prePayID: group.groupMemberList[0].prePayId,
              wxProfileUrl: group.groupMemberList[0].wxProfileUrl,
            }
          )

          Object.assign(groupInfo, group, {
            groupID: group.id,
            headerID: group.headerId,
            openerID: group.openerId,
            groupMemberList: [groupMemberList]
          })

          that.setOrderDetailData (data, groupInfo, goodsInfo)

        }
        else {
          commonObj.showModal('提示', '信息获取失败,请稍后重新请求。异常错误信息：' + res.data.errorMsg, false, '确认', '', function () {
            wx.redirectTo({
              url: '/fightGroups/pages/fightGroups/index'
            });
          },
          );
        }
        wx.hideLoading();
      },
      '',
      function () {
        that.setData({ isReady: true });
      },
    )
  },

  setOrderDetailData ( data, groupInfo, goodsInfo ) {
    var that = this
    var user = wx.getStorageSync('user')

    that.setData({
      goodsDetailObj: goodsInfo
    })

    that.countDown(groupInfo.expireTime)

    let numPer = groupInfo.groupSize - groupInfo.currentCount
    if (Number(numPer) === 0) {
      that.setData({ isComposition: true });
    } else {
      that.setData({ isComposition: false });
    }

    let obj = {
      goodsName: goodsInfo.name || goodsInfo.goodsNmae,
      subTitle: goodsInfo.subTitle || '',
      spec: goodsInfo.spec,
      price: goodsInfo.groupPrice,
      oriPrice: goodsInfo.price,
      pic: picUrl + goodsInfo.headPic, // 商品图片url
      numPer: numPer,
      groupSize: groupInfo.groupSize,
      deliverySection: data.deliverySection || '',
      takeCode: data.takeCode || '',
    }

    this.canExecuteDraw()

    if (data.takeCode) {
      setTimeout(() => {
        that.drawCode(data.takeCode)
      }, 500)
    }

    let goodsOrderID
    let { groupMemberList = [] } = groupInfo
    for (let item in groupMemberList) {
      if (String(user.userID) === String(groupMemberList[item].customerID)) {
        goodsOrderID = groupMemberList[item].goodsOrderID;
      }
    }
    var num = groupInfo.groupSize - groupInfo.currentCount;
    for (var x = 0; x < num; x++) {
      groupMemberList.push({});
    }
    wx.setStorageSync('goodsOrderID', goodsOrderID);
    that.setData({
      obj: obj,
      groupSharePic: goodsInfo.groupSharePic,
      groupShareTitle: goodsInfo.groupShareTitle,
      goodsOrderID: goodsOrderID,
      groupMemberList: groupMemberList,
      activityID: goodsInfo.activityID,
      activityName: goodsInfo.activityName,
      groupInfo: groupInfo,
      goodsID: goodsInfo.id || goodsInfo.goodsID,
      joinSuccess: true,
      isReady: true,
      pickupDescribe: data.pickupDescribe || ''
    });

    var page = getCurrentPages();
    for (let item in page) {
      if (page[item] && page[item].route.indexOf('/goodsDetail/index') > -1) {
        page[item].data.showPayModal = false;
        if (groupInfo.status === 'PROCESS') {
          page[item].data.goodsOrderID = goodsOrderID;
        }

      }
    }
  },

  canExecuteDraw () {
    let {
      headPic,
      groupPrice,
      spec,
      sellCount
    } = this.data.goodsDetailObj
    this.drawSharePic({
      canvasId: 'sharePicCanvas',
      goodsInfo: {
        isStarted: true,
        goodsPicUrl: this.data.picUrl + headPic,
        price: groupPrice,
        count: sellCount,
        spec
      }
    })
  },

  drawCode (code) {
    if (!code) return
    const options = {
      code,
      cavansId: 'barcode',
      app,
    }
    drawBarcode(options, res => {
      const { tempFilePath } = res || {}
      this.setData({
        memberTakeCode: tempFilePath
      })
    })
  },
  // 倒计时 支付成功-未成团
  countDown: function (expireTime, duration) {
    // 参数：过期时间，真假团单
    let that = this,
      eTime;

    let currTime = duration
      ? that.data.currTime - -duration
      : that.data.currTime;
    let layTime = Math.round(
      (new Date(expireTime.replace(/-/g, '/')).getTime() - currTime) / 1000,
    ); // 剩余时间秒数
    timeDown = setTimeout(function () {
      if (layTime < 0) {

        clearInterval(timeDown);
        timeDown = null;
        that.setData({
          layh: '00',
          laym: '00',
          lays: '00',
        });
        return;
      }
      let h = parseInt(layTime / 3600);
      let m = parseInt(layTime / 60 - h * 60);
      let s = parseInt(layTime % 60);

      s = s < 10 ? '0' + s : s;
      m = m < 10 ? '0' + m : m;
      h = h < 10 ? '0' + h : h;
      that.setData({
        layh: h,
        laym: m,
        lays: s,
      });
      if (that.data.clearTimeDown) {
        return;
      } else {
        that.data.ct++;
        that.countDown(expireTime, that.data.ct * 1000);
      }
    }, 1000);
  },
  getHomePageInfo: function () {
    //获取拼团列表
    var that = this;
    var userCurrLoca = wx.getStorageSync('userCurrLoca');
    var timelyCity = wx.getStorageSync('timelyCity');
    var options = {
      encryptFlag: false,
      url: '/api/v1/groupBuy/homepage',
      data: {
        cityName: timelyCity.cityName || (userCurrLoca && userCurrLoca.cityName),
        lon:
        timelyCity.lon ||
        (userCurrLoca.location && userCurrLoca.location.lng),
        lat:
        timelyCity.lat ||
        (userCurrLoca.location && userCurrLoca.location.lat),
        storeID:
        '' ||
        (wx.getStorageSync('timelyCity') &&
          wx.getStorageSync('timelyCity').storeID),
        source: 'maybeLike'
      },
    };

    commonObj.requestData(
      options,
      function (res) {

        if (Number(res.data.errorCode) === 0) {
          var goodsList = res.data.data.goodsList;
          // that.formatGroupPrice(goodsList);
          that.setData({
            isReady: true,
            isRecommend: goodsList.length > 0 ? true : false,
            goodsList: goodsList
          });
        } else {
          commonObj.showModal(
            '提示',
            '信息获取失败,请稍后重新请求。异常错误信息：' + res.data.errorMsg,
            false,
            '确认',
            '',
            function () {
              wx.redirectTo({
                url: '/fightGroups/pages/fightGroups/index'
              });
            },
          );
        }
      },
      '',
      '',
    );
  },
  // formatGroupPrice: function (goodsList) {
  //   //格式化拼团价格
  //   var that = this;
  //   for (var item in goodsList) {
  //     goodsList[item].groupPrice = String(
  //       (goodsList[item].groupPrice / 100).toFixed(2),
  //     ).split('.');
  //     goodsList[item].price = (goodsList[item].price / 100).toFixed(2);
  //   }
  //   that.setData({ goodsList: goodsList });
  // },
  showTips: function () {
    //显示模板
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: 'linear',
      delay: 0,
    });
    this.animation = animation;
    animation.translateY(800).step();
    this.setData({
      animationData: animation.export(),
      tipsShow: true,
    });
    setTimeout(
      function () {
        animation.translateY(0).step();
        this.setData({
          animationData: animation.export(),
        });
      }.bind(this),
      200,
    );
  },
  hideTips: function () {
    //隐藏模板
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: 'linear',
      delay: 0,
    });
    this.animation = animation;
    animation.translateY(800).step();
    this.setData({
      animationData: animation.export(),
    });
    setTimeout(
      function () {
        animation.translateY(0).step();
        this.setData({
          animationData: animation.export(),
          tipsShow: false,
        });
      }.bind(this),
      200,
    );
  },
  /***********************End **************************/
  //  跳转链接
  navigatePTorderDetail: function () {
    var obj = JSON.stringify({
      goodsOrderID: this.data.goodsOrderID,
      isAllOrder: true,
      payStatus: 'SUCCESS',
    });
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110600001',
        element_name: '查看订单',
        element_content: '查看订单按钮',
        screen_code: '1106'
      })
    }
    wx.navigateTo({
      url: '/fightGroups/pages/PTorderDetail/index?paySuccessObj=' + obj,
    })
  },

  // 订单里跳转商品详情  新增组件里的东西
  navigateGoodsDetail: function () {
    wx.reportAnalytics("join_groupgoodclick") //参团页拼团商品click v1.4
    let obj = JSON.stringify({
      goodsID: this.data.goodsID,
      activityID: this.data.activityID
    }) // 增加了一个商品详情
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110400001',
        element_name: '商品详情',
        element_content: '商品详情',
        screen_code: '1104'
      })
    }
    wx.navigateTo({
      url: '/fightGroups/pages/goodsDetail/index?inviteFriendsObj=' + obj,
    })
  },

  // 猜你喜欢跳转商品详情  新增组件里的东西
  navigateGuestGoodsDetail: function (e) {
    wx.reportAnalytics('paysuccessbrandclick'); //支付成功页品牌click v1.4

    if (app.globalData.reportSensors) {
      if (this.data.isComposition) {
        app.sensors.track('MPClick', {
          element_code: '110600003',
          element_name: '商品详情',
          element_content: '支付成功后，团成页面商品点击跳转商品详情',
          screen_code: '1104'
        })
      } else {
        app.sensors.track('MPClick', {
          element_code: '110400001',
          element_name: '商品详情',
          element_content: '支付成功，团未成页面商品点击跳转商品详情',
          screen_code: '1104'
        })
      }
    }
    var obj = JSON.stringify({
      goodsID: e.detail.goodsID || e.currentTarget.dataset.goodsid,
      activityID: e.detail.activityID || e.currentTarget.dataset.activityid,
    });
    wx.navigateTo({
      url: '/fightGroups/pages/goodsDetail/index?paySuccessObj=' + obj,
    })
  },
  navigateFightGroupsIndex: function () {
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110600002',
        element_name: '继续逛逛',
        element_content: '继续逛逛按钮',
        screen_code: '1106'
      })
    }
    wx.redirectTo({
      url: '/fightGroups/pages/fightGroups/index'
    });
  },
  backHomeBtn: function () {
    wx.redirectTo({
      url: '/fightGroups/pages/fightGroups/index'
    });
  },
  // 防止点击过快，导致页面重复跳转蒙层
  preventEvent: function () {
    this.setData({ prevent: true });
    setTimeout(() => {
      this.setData({ prevent: false });
    }, 400);
  },

  recommandNavigate() {
    wx.reportAnalytics('pay_recommendgoodclick'); //支付页推荐商品click v1.4
  },
  // 关闭引导页浮层， template中调用
  closeLayer () {
    this.setData({
      showLayer: false
    })
  }
});
