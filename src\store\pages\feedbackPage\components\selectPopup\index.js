import request from '../../../../../utils/request'
import { OrderInfo } from '../../OrderInfo'
import locateStore from '../../../../../stores/module/locate'
import locateService from '../../../../../utils/services/locate'
import { getStorage, defaultVal } from '../../../../../utils/cyclomatic'
import coordtransform from '../../../../../utils/coordUtil'
import { boolean2YN } from '../../../../../utils/YNBoolean'
import { getAppConfig } from '../../../../../service/wxappSetting'
import sensors from '../../../../../utils/report/sensors'
import { sleep } from '~/utils/time'

const app = getApp()

Component({
  properties: {
    selectType: {
      type: String,
      value: '0',
    },
    // 立即触发选择订单事件 1 订单 2 门店
    immediateValue: {
      type: String,
      value: '0',
    },
    popupShow: {
      type: Boolean,
      value: false,
    }
  },
  observers: {
    selectType(value) {
      if (!['1', '2'].includes(value)) {
        return
      }
      sensors.exposureReport({
        blockName: '选择订单/门店弹窗',
        blockCode: '1162401',
      })
      this.setData({
        customerID: getStorage('user').userID,
        tabIndex: {
          1: 0,
          2: 1,
        }[value],
      })

      value === '1' && this.getOrderList()
      value === '2' && this.initStoreList()
    },
    /**
     * 立即选择事件，暂只支持订单
     * @param {String} value 选择类型 1 订单 2 门店
     */
    async immediateValue(value) {
      if (value === '1') {
        if (this.data.selectType !== '1' && !this.data.orderList.length) {
          this.setData({
            customerID: getStorage('user').userID
          })
          await this.getOrderList()
        } else {
          await this._data.orderReady.promise
        }
        this.triggerEvent('select', {
          immediate: true,
          data: this.data.orderList[0] || {}
        })
      }
    }
  },
  data: {
    customerID: '',
    tabList: ['订单', '门店'],
    tabIndex: 0,
    orderList: [],
    orderTips: '',
    address: {
      title: '',
      lon: '',
      lat: '',
    },
    frequentlyStores: [],
    nearbyStores: [],
    loading: {
      location: false,
      orderList: false,
      storeList: false,
    },
    appConfig: { customerServiceHours: { endTime: '23:00', startTime: '08:00' } },
  },
  methods: {
    closeShowPopup() {
      this.triggerEvent('select', {
        selectType: '0',
        data: void 0,
      })
    },
    changeTab(e) {
      const tabIndex = Number(e.currentTarget.dataset.index)
      this.setData({
        tabIndex,
      })
      tabIndex === 0 && this.getOrderList()
      tabIndex === 1 && this.initStoreList()
    },
    async getOrderList() {
      if (this.data.loading.orderList) {
        return
      }
      this.setData({
        'loading.orderList': true,
      })
      const orderListId = Math.random()
      this._data.orderListId = orderListId
      const showLoading = !this.data.orderList.length
      showLoading && wx.showLoading({ title: '加载中' })
      const { data } = await request.post({
        url: '/dskhd/api/member/feedback/v1/recentOrderList',
        isLoading: false,
        data: {
          customerID: this.data.customerID,
        },
      }).catch(() => ({ data: { tips: '', orderList: [], } }))
      const orderInfo = new OrderInfo(data)
      const orderList = await orderInfo.getOrderListWithImage()
      if (this._data.orderListId !== orderListId) {
        return
      }
      this.setData({
        'loading.orderList': false,
        orderTips: data.tips,
        orderList,
      })
      this._data.orderReady.resolve()
      showLoading && wx.hideLoading()
    },
    onOrderItemTap({ currentTarget }) {
      const { orderList } = this.data
      this.triggerEvent('select', {
        selectType:  '1',
        data: orderList[currentTarget.dataset.index]
      })
      sensors.clickReport({
        blockName: '选择订单/门店弹窗',
        blockCode: '1162401',
        element_content: '选择订单',
        element_name: '选择订单',
        element_code: '1162401001',
      })
    },
    initStoreList() {
      if (this.data.loading.storeList) {
        return
      }
      this.setData({
        'loading.storeList': true,
      })
      const needShowLoading = !this.data.frequentlyStores.length && !this.data.nearbyStores.length
      needShowLoading && wx.showLoading({ title: '加载中' })
      const timelyCity = getStorage('timelyCity')
      const { location = {}, address } = getStorage('userCurrLoca')
      this.setData({
        address: {
          title: locateStore.useDefault ? '无定位地址' : defaultVal(address, '定位失败，未获取到定位信息'),
          lon: defaultVal(timelyCity.lon, location.lon),
          lat: defaultVal(timelyCity.lat, location.lat),
        },
      })
      this.getStoreList().then(() => {
        needShowLoading && wx.hideLoading()
        this.setData({
          'loading.storeList': false,
        })
      })
    },
    async getFrequentlyList() {
      if (!app.checkSignInsStatus()) {
        return []
      }
      const { customerID } = this.data
      const { data: storeList } = await app.api.getFrequentlyStore({
        customerID,
        isNeedSaleCount: boolean2YN(false),
        isSupportTake: boolean2YN(false),
      }, { isLoading: false }).catch(() => ({ data: [] }))
      return storeList.slice(0, 5).map(item => ({
        storeCode: item.storeCode,
        storeName: item.storeName,
        address: item.address,
      }))
    },
    async getFruitNearByStores(showLoading = false) {
      const { customerID, address: { lat, lon } } = this.data
      const { data: storeList } = await app.api.getFruitNearByStores({
        lat,
        lon,
        isTimelySupport: boolean2YN(true),
        isSupportTake: boolean2YN(true),
        customerID,
      }, { isLoading: showLoading }).catch(() => ({ data: [] }))
      return storeList.map(item => ({
        storeCode: item.storeCode,
        storeName: item.storeName,
        address: item.address,
        distance: item.distance,
      }))
    },
    async getStoreList() {
      const storeListId = Math.random()
      this._data.storeListId = storeListId
      const [
        frequentlyStores,
        nearbyStores,
      ] = await Promise.all([
        this.getFrequentlyList(),
        this.getFruitNearByStores(),
      ])
      if (this._data.storeListId !== storeListId) {
        return
      }
      this.setData({
        frequentlyStores,
        nearbyStores,
      })
    },
    onStoreItemTap({ currentTarget }) {
      const {
        index,
        type,
      } = currentTarget.dataset
      const {
        frequentlyStores,
        nearbyStores,
      } = this.data
      this.triggerEvent('select', {
        selectType: '2',
        data: {
          frequentlyStores,
          nearbyStores,
        }[type][index],
      })
      sensors.clickReport({
        blockName: '选择订单/门店弹窗',
        blockCode: '1162401',
        element_content: '选择门店',
        element_name: '选择门店',
        element_code: '1162401002',
      })
    },
    nowLocate: app.subProtocolValid('shop', async function() {
      if (this.data.loading.location) {
        return
      }
      this.setData({ 'loading.location': true })
      const now = Date.now()
      const { data: locationInfo, error } = await locateService.getGeoLocation().then(data => ({ data })).catch(error => ({ error }))
      if (error) {
        this.setData({ 'loading.location': false })
        return locateService.handleWXLocationErr(error.errMsg)
      }
      await locateService.getCityName(locationInfo.latitude, locationInfo.longitude)
      const {
        location: { lng, lat },
        address,
      } = getStorage('userCurrLoca')
      const bdlocation = coordtransform.gcj02tobd09(lng, lat)
      this.setData({
        address: {
          title: address,
          lon: bdlocation[0],
          lat: bdlocation[1],
        }
      })
      locateStore.changeUseDefault({ useDefault: false })
      const nearbyStores = await this.getFruitNearByStores(!this.data.nearbyStores.length)
      await sleep(Math.max(0, 1500 - (Date.now() - now)))
      this.setData({
        'loading.location': false,
        nearbyStores,
      })
    }),
    toCustomerService() {
      app.toOnlineService({ needShopCartGoods: true })
      sensors.clickReport({
        blockName: '选择订单/门店弹窗',
        blockCode: '1162401',
        element_content: '联系在线客服',
        element_name: '联系在线客服',
        element_code: '1162401003',
      })
    },
  },
  lifetimes: {
    created() {
      let resolve
      const promise = new Promise(_resolve => (resolve = _resolve))
      this._data = {
        orderListId: 0,
        storeListId: 0,
        // 订单列表加载状态
        orderReady: { promise, resolve }
      }
    },
    attached() {
      getAppConfig().then((res) => this.setData({
        appConfig: res
      }))
    },
  }
})