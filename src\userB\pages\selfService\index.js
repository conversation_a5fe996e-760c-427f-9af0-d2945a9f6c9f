// pages/user/selfService/index.js
import { sensorInfo } from './sensorReportInfo'
const sensors = require('../../../utils/report/sensors')
const navigateToH5Mixin = require('../../../mixins/navigateToH5Mixin')
const config = require('../../../utils/config')
import { userLogout } from '../../../service/userService'
import { setCustomTabbarCartCount,
  setFreshCartCount } from '../../../utils/wx/tabbarCartCount'
import { outLoginType } from '../../../source/const/globalConst'
var app = getApp();

const pageObj = {
  "userAgreement": "/userB/pages/agreementList/agreementList",
  "contentRecommend": "/userB/pages/selfContentRecommend/index"
}
Page({
  mixins: [navigateToH5Mixin],

  /**
   * 页面的初始数据
   */
  data: {
    superVipStatus: 'C',
    isIphoneX: app.globalData.isIphoneX,
    version:config.appVersion
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {
    this.setData({
      superVipStatus: app.globalData.superVipStatus
    })
  },
  onShow: function () {
    if (app.checkSignInsStatus()) {
      // 用户已登录
      this.setData({ isLogin: true })
    } else {
      this.setData({ isLogin: false })
    }
    // 浏览页面上报神策
    sensors.pageScreenView()
  },

  navigateModifyPwd: function () {
    wx.reportAnalytics('vip_selfhelpincard');
    wx.navigateTo({
      url: '/userB/pages/modifyPwd/index',
    })
    sensors.trackClickEvent(sensorInfo['重置门店消费密码'])
  },
  /**
   * 用户点击退登按钮
   */
  async signOut() {
    let page = getCurrentPages()
    let prepage = page[page.length - 2]
    try {
      const customerID = app.globalData.customerID
      const { openid } = wx.getStorageSync('wxSnsInfo') || {}
      await userLogout({ customerID, openid, logoutCause: outLoginType.userInitiatedLogout })
      prepage.signOut({ logoutCause: outLoginType.userInitiatedLogout })
      // 等待自定义退出事件上报后再上报默认的退出事件
      app.sensors.logout()
      if (wx.getStorageSync('checkObj')) {
        wx.removeStorageSync('checkObj')
      }
      app.updateFreshGoodsCartList({})
      wx.navigateBack({
        delta: 1, // 回退前 delta(默认为1) 页面
      })
      setCustomTabbarCartCount(0)
      setFreshCartCount(0)
      wx.setStorageSync('isManuallyLogout', true)
    }
    catch (err) {
      console.log(err)
    }
  },
  /**
   * 跳转到其他页面
   */
  navigateToPage: app.subProtocolValid({
    type: 'memberService',
    conditionFn (e) {
      const { type } = e.currentTarget.dataset

      //  仅个性化内容推荐进行校验
      if (type === 'contentRecommend') {
        return true
      }
    }
  }, function(e){
    const { type } = e.currentTarget.dataset
    const url = pageObj[type]

    const sensorMap = {
      "userAgreement": '用户协议',
      "contentRecommend": '个性化内容推荐'
    }
    sensors.trackClickEvent(sensorInfo[sensorMap[type]])

    //  个性化内容推荐，如未登录，需要登录账号
    if (type === 'contentRecommend' && !this.data.isLogin) {
      return app.toSignIn({ finishToRouter: 'selfContentRecommend' })
    }
    wx.navigateTo({
      url
    })
  }),
  navigateToLogoff () {
    wx.navigateTo({
      url: '/userB/pages/logoff/logoffCheck/index',
    })
    sensors.trackClickEvent(sensorInfo['注销'])
  },
  navigateToAboutUs (ev) {
    this.navigateToCommonH5Page(ev)
    sensors.trackClickEvent(sensorInfo['关于我们'])
  },
  navigateToH5 (ev) {
    this.navigateToCommonH5Page(ev)
  },
})
