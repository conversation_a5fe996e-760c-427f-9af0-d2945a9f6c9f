<wxs module="bounce" src="../../../../utils/bounce.wxs"></wxs>
<wxs module="scrollView" src="./scrollView.wxs"></wxs>
<import src="../template/skeleton/index.skeleton.wxml" />
<!-- <custom-tab-bar tab-type="bgy" tab-index="1" id="custom-tab-bar" /> -->
<main-page currentView="{{currentView}}" useScene="vegetables" showNav="{{true}}" navBarTitle="次日达" >
  <!-- 骨架屏 -->
  <template is="skeleton" wx:if="{{currentView === 'skeletonScreen' || !loadFinished}}" data="{{ navBar, skeleton }}" />
  <block wx:else>

    <!-- 导航栏底部基准，作为吸顶是否的判断 -->
    <view id="nav-bar-bottom" class="nav-bar-bottom" style="position: absolute;top: {{navBar.height + 10}}px;height: 1rpx;width: 100%;z-index:-10;"></view>
    <scroll-view id="mall" class="mall" scroll-y="{{canScrollY}}" scroll-top="{{pageScrollViewTop}}" refresher-enabled refresher-default-style="none" scroll-with-animation="{{contentRefresh.scrollAnimate}}" scroll-into-view="{{contentRefresh.scrollInto}}"
    refresher-threshold="{{contentRefresh.threshold}}" refresher-triggered="{{contentRefresh.triggered}}" bind:scrolltoupper="{{scrollView.onScrolltoupper}}" bind:scroll="{{scrollView.onScroll}}" bind:scrolltolower="onReachBottom" bind:refresherpulling="{{scrollView.onContentPull}}" bind:refresherrestore="{{scrollView.onRestore}}" bind:refresherabort="{{scrollView.onRestore}}" bind:refresherrefresh="{{scrollView.onRefresh}}" style="--nav-bar-height:{{navBar.height}}px"
    lower-threshold="800" >
      <view class="scroll-flag scroll-view-top" id="scroll-top-0"></view>
      <!-- 自定义下拉刷新动画 -->
      <view slot="refresher" class="mall-refresher-animate">
        <view class="mall-refresher-tips mall-refresher-tips-release" data-threshold="{{contentRefresh.threshold}}"></view>
        <view class="mall-refresher-img frames-animate"></view>
      </view>
      <view style="position: relative;">
      <!-- <view class="mall-bg"></view> -->
      <block>
        <!-- 下拉刷新时隐藏上面的sticky-nav-bar,显示这个refresher-nav-bar -->
        <!-- 以实现nav-bar跟随下拉手势效果 -->

        <!-- 用来判断是否滑动基准 -->
        <view class="nav-bar-top" style="position: absolute;top:0;z-index: -1;height:1rpx;width: 100vw;"></view>
        <view style="position: sticky;top:0;z-index: 1000;" class="nav-bar">
          <refresher-nav-bar
            navBgImgStyle="{{navBgImgStyle}}"
            statusBarHeight="{{statusBarHeight}}"
            id="refresher-nav-bar"
            positon
            animation="{{navBgAnimation}}"
          />
        </view>
        <!-- 滑动内容区域 -->
        <!-- <view class="content" style=" background-position: 0 0px, 0 468rpx; background-image: url('{{middlePicUrl}}'),url('{{bottomPicUrl}}')"> -->
        <view class="content" style=" background-position: 0 -{{(navBar.height - statusBarHeight)*2}}rpx, 0 {{ (234 - navBar.height + statusBarHeight)*2}}rpx; background-image: url('{{middlePicUrl}}'),url('{{bottomPicUrl}}')">

          <!-- <view style="height: {{navBar.height}}px;"></view>  -->
          <view>
            <address-tips
              id="address-tips"
              isSelectStore="{{!userLocationAuth}}"
              addressInfo="{{bgxxStoreInfo.selectAddressInfo.address}}"
              storeName="{{bgxxStoreInfo.selectStoreInfo.storeName}}"
              bind:restartLocate="restartLocate"
              bind:searchTap="enterSearch"
              tipsIsHolder="{{isHasSaleBg}}"
            ></address-tips>
          </view>

          <!-- 大促 -->
          <view
            style="height: 320rpx;width: 100vw;"
            wx:if="{{isHasSaleBg}}"
            bind:tap="toActivityPage"
          >
          </view>
          <!-- 轮播banner -->
          <!-- <pagoda-banner-swiper wx:else imgList="{{topBannerList}}" navTopHeight="{{navBar.height + 44}}px" baseUrl="{{picDomain}}" bindbannerTap="bannerToSkip" bgColorList="{{bgColorList}}" bindonColorChanged="onColorChanged" autoplay /> -->
          <view class="banner_container {{backgroundBelowUrl ? 'sales-promotion-below' : ''}}">
            <!-- metro活动 -->
            <!-- <pagoda-metro picDomain="{{picDomain}}" metroBannerList="{{metroBannerList}}" bindbannerToSkip="bannerToSkip" bindfetchFinish="compFetchFinish" /> -->
            <!-- 自提订单模块 -->
            <!-- <view class="self-order-block" wx:if="{{!!selfOrderList && selfOrderList.length > 0}}">
              <swiper class="order-banner" indicator-dots="{{selfOrderList.length > 1}}" autoplay="{{selfOrderList.length > 1}}" circular="true">
                <swiper-item wx:for="{{selfOrderList}}" wx:key="index">
                  <view class="order-item" bindtap="orderToSkip" data-goods-count="{{item.goodsOrderNum}}" data-order-id="{{item.goodsOrderID}}">
                    <view class="order-img">
                      <image src="{{picDomain + item.picUrl}}" />
                    </view>
                    <view class="order-info">
                      <view class="order-number">您有{{item.goodsOrderNum}}个订单待自提</view>
                      <view class="pike-up-info">
                        请于今日{{item.store.openingTime}}前往{{item.store.shortName}}门店完成自提
                      </view>
                    </view>
                    <view class="arrow-btm">
                      <image src="https://resource.pagoda.com.cn/group1/M0F/34/E4/CmiWa1_XRlyAZMBtAAACJuWNmFU477.png" />
                    </view>
                  </view>
                </swiper-item>
              </swiper>
            </view> -->
            <!-- 新人专区 -->
            <!-- <block wx:if="{{newCustomerGoods.length}}">
              <view class="scroll-flag" id="scroll-flag-new"></view>
              <pagoda-new-customer picDomain='{{picDomain}}' newCustomerGoods="{{newCustomerGoods}}" bind:redirect="clickNewCustomerGoods" bindaddNewCustomerGoods="addNewCustomerGoods">
                <pagoda-card-package bindclickcoupon="newUserCouponClick" cardList="{{newUserCoupons}}" isLogin="{{isLogin}}" wx:if="{{newUserCoupons.length}}" scene="{{1}}" />
              </pagoda-new-customer>
            </block>
            <block wx:else>
              <view class="card-coupons" wx:if="{{newUserCoupons.length}}">
                <view class="customer-content {{newUserCoupons.length === 1 ? 'pogoda-coupon-height':''}}">
                  <pagoda-card-package bindclickcoupon="newUserCouponClick" cardList="{{newUserCoupons}}" isLogin="{{isLogin}}" scene="{{2}}" />
                </view>
              </view>
            </block> -->
            <!-- 品牌专栏 -->
            <!-- <pagoda-brand brandImg="{{pagodaBrand.fileUrl}}" subjectId="{{pagodaBrand.passthrough[0]['id']}}" subjectType="{{pagodaBrand.passthrough[0]['business']}}" subjectOrign='{{pagodaBrand.jumpType}}' data-src="brand" bindgetObserverRequest="getBrand" /> -->
            <!-- 热点banner(hotBanner) -->
            <!-- <block>
              <view id="hot-banner" class="hot-banner" wx:if="{{hotBanner && hotBanner.id}}">
                <view class="banner" bindtap="bannerToSkip" data-item="{{hotBanner}}" data-index="0" data-src="hotBanner">
                 <image lazy-load class="lazy_load-hotBanner" src="{{picDomain + (hotBanner.pic  ||  hotBanner.newPic)}}" />
                </view>
              </view>
            </block> -->
            <!-- <pagoda-split-banner splitList="{{splitList}}" bindbannerTap="bannerToPage" /> -->
            <!-- 新品推荐 -->
            <!-- 爆款商品专区 -->
            <!-- <view class="goods-content" wx:if="{{hotGoodsList && hotGoodsList.length}}">
                  <view wx:if="{{!!hotModule.moduleTitle}}" class="goods-title">
                    <view class="goods-main-title text-hidden">{{hotModule.moduleTitle}}</view>
                    <view class="subTitle" wx:if="{{hotModule.moduleSubTitle}}">
                      {{hotModule.moduleSubTitle}}
                    </view>
                  </view>
                  <view class="module-content module-content-single" wx:if="{{hotGoodsList && hotGoodsList.length}}">
                    <view wx:for="{{hotGoodsList}}" wx:key="index" class="module-content-item">
                      <view class="scroll-flag" id="scroll-flag-hot-{{item.goodsSn}}"></view>
                      <goods-preview goodsData="{{item}}" styleType="{{styleConfig.verticalBig}}" bindaddCart="beforeAddCart" data-module-index="{{outerIndex}}" bindopenDetail="openDetail" goods-expose goods-expose-key="bgxxShopGoodsExpose" goods-expose-data="{{ ({ moduleTitle: goodsItem.moduleTitle }) }}" goods-special></goods-preview>
                    </view>
                  </view>
            </view> --><!-- 橱窗banner -->

            <view style="position:sticky;top:{{navBar.height}}px;z-index: 1000;" class="search-box" id="search-box">
              <pagoda-search-box
                isSticky="{{searchBoxIsSticky}}"
                top="{{navBar.height}}"
                showBtn
                searchAdConfig="{{searchAdConfig}}"
                bind:searchTap="enterSearch"
              ></pagoda-search-box>
            </view>
            <block wx:if="{{showcaseList.length > 1}}">
              <showcase-banner
                bannerObj="{{showcaseObj}}"
                showcaseList="{{showcaseList}}"
                picDomain="{{picDomain}}"
              ></showcase-banner>
            </block>
            <!-- 腰部banner -->
            <waist-banner
              wx:if="{{waistBannerObj && waistBannerObj.id}}"
              bannerObj="{{waistBannerObj}}"
              picDomain="{{picDomain}}"
            >
            </waist-banner>

            <view class="scroll-flag" id="scroll-flag-waterfall" style="transform:translate3d(0,-{{navBar.height + 48}}px,0)"></view>

            <!-- 瀑布流商品新 -->
            <view id="waterfalltabs" class="waterfall {{waterfall.isFixed ? 'fixed-top' : ''}}">
              <!-- 给导航栏外层嵌套一层固定高度的盒子 -->
              <view class="goods" data-disabled-top="{{true}}" data-skip-ios="{{waterfall.isIos}}" data-reach-bottom="{{waterfall.reachBottom}}" data-on-drag="updateReleaseFlag" data-on-switch="switchNextFloor" bind:touchstart="{{bounce.onTouchStart}}" bind:touchmove="{{bounce.onTouchMove}}" bind:touchend="{{bounce.onTouchEnd}}" style="min-height:{{minHeight}}px;">
                <!-- 因为高度不一致，所以分为左右两列排放 goodsList[0] 左侧数据 / goodsList[1] 右侧数据 -->
                <waterfallGoods id="waterfallGoods" userInfo="{{userInfo}}" addressInfo="{{bgxxStoreInfo}}" distanceToTop="{{navBar.height + 48}}" bindbeforeAddCart="beforeAddCart" bindbannerToSkip="bannerToSkip" bind:fixed="tabbarIsFixed" bind:scrolltop="scrollIntoView" bind:getGoodsActivitys="reqGoodsActivitys" bind:inited="onWaterfallInited"></waterfallGoods>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
      <!-- 未开通心享服务 -->
      <!-- <noservice wx:else></noservice> -->
    </scroll-view>
    <block>
      <!-- 购物车动画/左侧购物车和回到顶部栏 -->
      <view class="good-box" hidden="{{movingBallInfo.hide}}" style="left: {{movingBallInfo.posX}}px; top: {{movingBallInfo.posY}}px;" wx:if="{{movingBallInfo &&  movingBallInfo.pic}}">
        <image src='{{picDomain + movingBallInfo.pic}}'></image>
      </view>
      <view class="sidebar-operate safe-area-inset-bottom">
        <view class="suspense-operate" hidden="{{hiddenBackTop}}" catchtap="handleBackTop">
          <image src="/source/images/icon_backtop.png" />
        </view>
      </view>
      <!-- 右下角固定banner(floating) -->
      <view wx:if="{{floating && floating.id}}" class="floating" animation="{{floatingAni}}" bindtap="bannerToSkip" data-item="{{floating}}" data-src="floating">
        <image src="{{picDomain + (floating.pic || floating.newPic)}}"></image>
      </view>
    </block>
    <!-- 首页弹窗 -->
    <!-- 弹窗遮罩层 -->
    <slide-layer isShow="{{isShowPopUp}}" bottomCloseBtn tabbar="{{true}}" isCloseByMask="{{false}}" zIndex="{{1200000}}" bind:closeLayer="hidePopup">
      <view slot="content">
        <view class="pop-up" wx:if="{{popUpData.popupType === '1'}}">
          <view wx:for="{{popUpData.popupContent}}" wx:key="index">
            <!-- 普通弹窗 -->
            <image class="pop-up-img" bindload="imgLoadSuccess" wx:if="{{popUpData.popupType === '1' && !!item.pic}}" src="{{picDomain + item.pic}}" bindtap="goDetailPage" data-item="{{item}}" />
          </view>
        </view>
        <view class="pop-up-redPaper" wx:elif="{{popUpData.popupType === '2' && popUpData.topBackGroundPic}}">
          <!-- 红包雨 -->
          <image class="background-top-pic" src="{{picDomain + popUpData.topBackGroundPic}}" bindload="imgLoadSuccess" />
          <view class="pop-up-coupon"  style="background-image: url({{picDomain + popUpData.bottomBackGroundPic}});">
          <!-- <view class="pop-up-coupon" style="height: {{ popUpData.bottomBackGroundPic ? '848rpx': ''}}"> -->
            <!-- <image class="background-bottom-pic" src="{{picDomain + popUpData.bottomBackGroundPic}}" bindload="imgLoadSuccess" /> -->
            <scroll-view class="coupons" scroll-y>
              <view class="coupon-box" wx:for="{{popUpData.popupContent}}" wx:key="index" data-coupon="{{item}}" bindtap="jumpDetailPage">
                <dialog-coupon couponInfo="{{item}}"></dialog-coupon>
              </view>
            </scroll-view>
          </view>
        </view>
        <view class="pop-up" wx:else="{{popUpData.popupType === '2' && popUpData.backGroundPic}}">
          <view class="pop-up-coupon"style="height: 780rpx;max-height: 780rpx;padding: 0;">
            <image class="background-pic" src="{{picDomain + popUpData.backGroundPic}}" bindload="imgLoadSuccess" />
            <scroll-view scroll-y style="position: absolute;bottom: 140rpx;left: 0;width: 100%;height: 500rpx;text-align: center;">
              <view class="coupon" wx:for="{{popUpData.popupContent}}" wx:key="index" data-coupon="{{item}}" bindtap="jumpDetailPage">
                <dialog-coupon couponInfo="{{item}}"></dialog-coupon>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </slide-layer>
    <!-- canvas 绘图 -->
    <canvas class="canvas" style="width: 400px; height: 660px;" canvas-id="posterCanvas"></canvas>
  </block>
</main-page>
<dialog id='dialog' bind:cancelEvent="_cancelEvent" bind:confirmEvent="_confirmEvent"></dialog>
<!-- 用户指引 -->
<!-- <user-guide guideName="{{guideName}}" showUserGuide="{{showUserGuide}}" guideZone="{{guideZone}}" guideTips="{{guideTips}}" bind:tapUserGuide="tapUserGuide">
  <template is="userGuideAllCate" data="{{guideZone}}"></template>
</user-guide> -->

<!-- 全部分类 -->
<!-- <template name="userGuideAllCate">
  <view slot="zone" class="userguide_allcate" style="right: {{guideZone.width + 10}}px">
    <image src="https://resource.pagoda.com.cn/dsxcx/images/c1ef6d4840699e027ed7eea3719fb2c1.png"></image>
  </view>
</template> -->

<confirm-modal id="globalModal" globalModalInfo="{{globalLocateModalInfo}}"></confirm-modal>

<!-- 购物车结算栏 -->
<cart-popup wx:if="{{ showCart && currentView === 'content'}}" orderType="{{ ORDER_TYPE.FRESH }}" />

<common-loading />
