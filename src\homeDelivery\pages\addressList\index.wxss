page{
  background: #f5f5f5;
}
nearby-store.flex {
  display: flex;
}

.flex1 {
  flex: 1
}

.public{
  flex-flow: row nowrap;
  justify-content: space-between;
  display: flex;
}

.public-Center{
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  align-items: initial;
  height: auto;
  position: relative;
  padding-bottom: 94rpx;
  background-color: #F5F5F5;
}

.search-box {
  z-index: 13;
  background-color: #fff;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid #EEEEEE;
}

.search-outer {
  display: flex;
  margin: 16rpx 0;
  padding: 0 24rpx;
  font-size: 26rpx;
  height: 64rpx;
  line-height: 64rpx;
  width: 702rpx;
  border-radius: 31rpx;
  background-color: rgb(242, 242, 242);
  padding-right: 0;
}

.search-city {
  display: flex;
  text-align: center;
  padding-right: 14rpx;
  align-content: center;
  align-items: center;
  font-size: 28rpx;
  width: 158rpx;
  position: relative;
  /* border-right: 1rpx solid rgb(220,220,220); */
}

.search-city::after {
  content: '';
  display: inline-block;
  position: relative;
  height: 30rpx;
  width: 1rpx;
  background-color: #CCCCCC;
  transform: translateX(10rpx);
}

.search-city>text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 4em;
}

.icon-down {
  /* margin-left: 4rpx; */
  width: 28rpx;
  height: 24rpx;
}

.search-input {
  padding: 0 16rpx;
  height: 62rpx;
  line-height: 62rpx;
  width: calc(100% - 158rpx - 30rpx);
}

.icon-middle {
  display: flex;
  align-items: center;
  width: 60rpx;
  height: 100%;
  /* padding: 10rpx; */
}

.icon-close {
  width: 30rpx;
  height: 30rpx;
  padding: 10rpx 15rpx;
}

.result-box .item {
  padding: 30rpx;
  border-bottom: 1rpx solid rgb(229, 229, 229);
  background: #fff;
  color: rgb(53, 53, 53);
}

.item-address {
  font-size: 30rpx;
  word-break: break-all;
  font-weight: 500;
}

.item-title {
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
  color: rgb(178, 178, 178);
}
.nearby-store-title {
  padding-right: 25rpx;
  display: flex;
  justify-content: space-between;
}
.collect-title {
  font-size: 28rpx;
  color: #888888;
  margin-right: 4rpx;
  display: flex;
  align-items: center;
}
.title {
  font-size: 28rpx;
  color: #555;
  padding-left: 32rpx;
}
/* .nearby-store-title {
  padding-top: 12rpx;
} */
.cur-location{
  background-color: #fff;
  padding-top: 24rpx;
}
.cur-location-item {
  align-items: center;
  padding: 0 0 23rpx 32rpx;
  font-size: 30rpx;
  height: 80rpx;
  color: #333;
}

.cur-location-item>.txt {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 28rpx;
  font-weight: bold;
}

.relocate {
  align-items: center;
  padding-right: 24rpx;
  color: #3FA865;
  font-size: 28rpx;
}

.relocate>image {
  width: 28rpx;
  height: 28rpx;
  margin-left: 6rpx;
}

.my-address-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-bottom: 1rpx solid #f2f2f2;
  font-size: 30rpx;
  /* white-space: nowrap; */
  overflow: hidden;
  /* height: 136rpx; */
  padding: 23rpx 24rpx;
  color: #333;
}

.address-item-address {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 28rpx;
  font-weight: bold;
  flex: 1;
}

.my-address-item>.flex {
  align-items: center;
  font-size: 28rpx;
  color: #999;
  line-height: 40rpx;
}

.address-item-label {
  line-height: 1;
  margin-right: 16rpx;
  padding: 6rpx 8rpx 5rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.address_item-labeltext {
  /* transform: translateY(1rpx); */
}

.address-item-phone {
  /* padding-left: 16rpx; */
  font-size: 24rpx;
  color: #999999;
}

.more {
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  height: 72rpx;
  color: #666;
}

.address-more {
  margin-left: -8rpx;
  display: inline-flex;
  align-items: center;
}

.address-more .text {
  width: 24rpx;
  font-size: 24rpx;
  color: #999999;
  line-height: 28rpx;
  white-space: pre-wrap;
}

.address-more .icon-arrow {
  width: 20rpx;
  height: 20rpx;
  margin-right: 8rpx;
}

.btn-down {
  width: 26rpx;
  height: 14rpx;
  margin-left: 20rpx;
}

.nearby-address-item {
  margin-left: 24rpx;
  height: 90rpx;
  line-height: 90rpx;
  border-bottom: 1rpx solid #f2f2f2;
  font-size: 28rpx;
  color: #666;
}

.bottom-box {
  margin-top: 96rpx;
}

.address-scroll-box {
  margin: 17rpx 0rpx 0rpx 32rpx;
  width: calc(103% - 30rpx - 32rpx);
  height: 120rpx;
  white-space: nowrap;
}

.address-block {
  display: flex;
}

.address-block-item {
  /* border-radius: 16rpx; */
  /* width: 382rpx; */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 119rpx;
  background: #F5F5F5;
  margin-right: 20rpx;
  padding: 20rpx 30rpx 17rpx;
  max-width: 354rpx;
  border-radius: 12rpx;
}
.Check{
  border: 2rpx solid #00A34F;
  padding: 22rpx 0rpx 22rpx 30rpx !important;
}
.Check-address{
  border: 2rpx solid #00A34F;
}
.address-itemname {
  margin-right: 12rpx;
  max-width: 168rpx;
  font-size: 24rpx;
  color: #999999;
  overflow: hidden;
  text-overflow: ellipsis;
}

.my-address-title {
  display: flex;
  justify-content: space-between;
  margin-top: 24rpx;
}

.more-address {
  font-size: 28rpx;
  color: #888888;
  padding-right: 24rpx;
  /* line-height: 60rpx; */
  display: flex;
  align-items: center;
}

.my-address {
  background-color: #FFF;
  border-top: 1px solid #eee;
  padding-bottom: 24rpx;
}

.nearby-store {
  border-top: 24rpx solid #f5f5f5;
  background: #F5F5F5;
}

.nearby-store-item {
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  background: #FFFFFF;
}

.image_icon {
  width: 32rpx;
  height: 32rpx;
}

.store_name {
  font-size: 30rpx;
  color: #222222;
  font-weight: bold;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 400rpx;
}

.address_block {
  display: flex;
  align-items: center;
}

.icon-nostore {
  margin-bottom: 12rpx;
}

.fix-bottom {
  width: 100%;
  background: #fff;
  position: fixed;
  bottom: 0;
}

.add-address {
  height: 96rpx;
  color: #00A34F;
  font-weight: 500;
  justify-content: center;
  display: flex;
  align-items: center;
  border-top: 1rpx solid #e9e9e9;
  font-size: 32rpx;
}

.add-address__icon {
  margin-right: 12rpx;
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  background-size: 100%;
  background-image: url('https://resource.pagoda.com.cn/0/84921656558682417/ec2ec368f996f72d4e10b1bd49d25c11.png');
}

.border{
  width: 1rpx;
  height: 130rpx;
  border-left: 1rpx solid #EBEBEB;
  margin-left: 15rpx;
}
.stroe-distance-text{
  font-weight: 400;
  font-size: 24rpx;
  color: #888888;
  text-align: left;
  margin: auto;
}
.image_icon-stote{
  width: 21rpx;
  height: 21rpx;
  margin-left: 4rpx;
}
.mask {
  width: 100%;
  height: 100%;
  position: fixed;
  /* top: 168rpx; */
  left: 0;
  background: #000000;
  z-index: 9;
  opacity: 0.5;
  overflow: hidden;
}
.arrow-right {
  margin-left: 10rpx;
  margin-right: 10rpx;
  width: 14rpx;
  height: 14rpx;
  border-top: 3rpx solid #888;
  border-right: 3rpx solid #888;
  transform: rotate(45deg);
}

