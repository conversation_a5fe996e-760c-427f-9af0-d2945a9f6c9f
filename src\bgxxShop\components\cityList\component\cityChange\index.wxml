<view class='list-warpper' wx:if="{{data.length > 0}}">
  <view wx:if="{{config.search}}" class='list-search'>
    <view class='list-search-box'>
      <icon type="search" size="13" color="#DCDCDC" />
      <input placeholder-style="color:#888888;font-size:26rpx;" placeholder="请输入城市进行搜索" bindinput='input' bindfocus="focusHandle" value="{{inputValue}}"/>
      <icon bindtap="clear" type="clear" size="15" color="#888888" wx:if="{{inputValue!=''}}"/>
    </view>
  </view>
  <!-- 我的位置  -->
  <block wx:if="{{myCity.cityName && inputValue === ''}}">
    <view class="paragraph-title">请选择</view>
    <view class='myaddress'>
      <view class="myaddress-current">
        <view class="myaddress-desc">当前所在地区：</view>
        <view class='myaddress-text'>
          {{myCity.cityName}}
        </view>
      </view>
      <view class="locate" catchtap="currentLocation" data-type="reset">
        <view class="locate-text">重新定位</view>
        <image class="locateimage" src="/source/images/icon_position.png"></image>
      </view>
    </view>
  </block>
  <block wx:if="{{list.length != 0 }}">
    <scroll-view style="{{inputValue === ''?'height:74%':'height:85%'}}" class="list-scroll {{config.search?'top':''}}" scroll-y="true" scroll-into-view="{{jumpNum}}" scroll-with-animation="{{config.animation}}" bindscroll="scrollEvent">
      <!-- 热门城市  -->
      <block wx:if="{{hotCityList.length>0 && inputValue === ''}}">
        <view class="paragraph-title">热门城市</view>
        <view class='hot-city'>
          <view class='hot-city-item' wx:for="{{hotCityList}}" wx:key="index" data-detail="{{item}}" catchtap='clickCity'>{{item.cityName}}</view>
        </view>
      </block>
      <!-- 所有城市 -->
      <view class="paragraph-title" wx:if="{{inputValue === ''}}">已开通城市</view>
      <view id="{{'index'+index}}" wx:for="{{list}}" wx:key="key">
        <view class='list-title'>{{item.title}}</view>
        <view>
          <view class='list-name {{currentClickCity.cityName == city.cityName?"colorCityName":""}}' wx:for="{{item.item}}" wx:for-item="city" wx:for-index="idx" wx:key="city" data-detail="{{city}}" catchtap='clickCity'>
            {{city.cityName}}
            <!-- <image src="/source/images/btn_tick_small_sel.png" class="active-icon" wx:if="{{currentClickCity.cityName == city.cityName}}"></image> -->
          </view>
        </view>
      </view>
    </scroll-view>
    <view class='list-right-wrapper safe-area-inset-bottom'>
      <view class="right-item {{(selectedChar=='index'+index)?'act':''}}" wx:for="{{rightArr}}" wx:key="rightArr" data-id="{{'index'+index}}" catchtap='clickCapital'>
        <text class="text">{{rightArr[index]}}</text>
      </view>
    </view>
  </block>
  <block wx:else>
    <view class="empty">
      <image src='https://resource.pagoda.com.cn/dsxcx/images/e309da62bb0b6a506afd8157de79cdc0.png' class='empty-img'></image>
      <view class='empty-text'>未查询到相关城市</view>
    </view>
  </block>
</view>
