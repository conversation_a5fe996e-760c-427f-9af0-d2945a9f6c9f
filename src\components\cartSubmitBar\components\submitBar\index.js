import sensors from '../../../../utils/report/sensors'
import utils from '../../../../utils/util'
const { properties } = require('../props.js')

Component({
  data: {
    repaint: false
  },
  options: {
    multipleSlots: true
  },
  properties: {
    ...properties,
    showBillPopup: {
      type: Boolean,
      value: false
    }
  },
  observers: {
    totalPrice() {
      this.forceRepaint()
    }
  },
  methods: {
    onCheckAll() {
      const { showBillPopup } = this.data
      showBillPopup && this.triggerEvent('showBillChange', { showBillPopup: false })
      this.triggerEvent('checkAll')
    },
    getReportData() {
      const reportData = {}
      const { screen_code, screen_name } = utils.isSensorsPageConfigPages() || {}
      if (screen_code && screen_name) {
        Object.assign(reportData, {
          screen_code,
          screen_name
        })
      }
      return reportData
    },
    showSubmitBill() {
      if (!this.data.showSaveMoney) {
        return
      }
      const { billDetailKey, showBillPopup } = this.data
      const reportData = this.getReportData()
      // 展开明细时埋点
      showBillPopup || (billDetailKey && sensors.track('MPClick', billDetailKey, reportData))
      this.triggerEvent('showBillChange', { showBillPopup: !showBillPopup })
    },
    onSubmit() {
      const { submitKey, submitReportData } = this.data
      const reportData = this.getReportData()
      Object.assign(submitReportData, reportData)
      submitKey && sensors.track('MPClick', submitKey, submitReportData || {})
      this.triggerEvent('submit')
    },
    forceRepaint() {
      this.setData({
        repaint: !this.data.repaint
      })
    },
    async getSubmitHeight() {
      const height = await new Promise(resolve => {
        this.createSelectorQuery()
          .select('.cart-submit-wrap')
          .boundingClientRect(res => {
            resolve(Number(res ? res.height : 0) || 0)
          }).exec()
      })
      this.triggerEvent('getHeight', { height })
    },
  },
  lifetimes: {
    created() {
      [
        { name: 'onCheckAll', gap: 500 },
        { name: 'showSubmitBill', gap: 1000 },
        { name: 'onSubmit', gap: 1000 },
        { name: 'forceRepaint', gap: 100 }
      ].forEach(({ name, gap }) => {
        this[name] = utils.throttle(this[name], gap)
      })
    },
    ready() {
      this.getSubmitHeight()
    }
  }
})
