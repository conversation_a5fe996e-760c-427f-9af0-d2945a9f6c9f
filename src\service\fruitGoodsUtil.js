const { SALE_TYPE_ENUM } = require('~/utils/goods/enum');
const { forMatDate } = require('../utils/deliveryTime')
const { deepClone, formatPrice } = require('../utils/util')
const goodsLabelStyleConfig = {
  // 门店推荐
  storeRecommend: {
    key: "storeRecommend",
    color: "FF7387",
    name: "店长推荐"
  },
  // 不可用券
  disbaledCoupon: {
    key: "disbaledCoupon",
    color: "888888",
    name: "不可用券"
  },
  // x倍积分
  integralNumber: {
    key: "integralNumber",
    color: "FF7387",
    name: "倍积分"
  },
  // 快递到家
  b2cDelivery: {
    key: "b2cDelivery",
    color: "019B50",
    name: "快递到家"
  },
  // 配送专享
  onlyDelivery: {
    key: "onlyDelivery",
    color: "019B50",
    name: "配送专享"
  },
  // 自提专享
  onlySelfTake: {
    key: "onlySelfTake",
    color: "019B50",
    name: "自提专享"
  },
  stockNum: {
    key: "stockNum",
    color: "888888",
    name: "仅剩1份"
  }
}
/**
 * 商品详情对应的服务说明
 */
const goodsSpecialServiceList = {
  及时达: [
    {
      label: "三无退货",
      icon: `https://resource.pagoda.com.cn/group1/M04/34/95/CmiWiF-9zAKAJ3ayAAAGguseIwc015.png`,
      content:
        "您对商品有任何不满意，可享受7天内无实物无小票无理由，三无退货。",
      httpUrl: `https://resource.pagoda.com.cn/group1/M0A/34/02/CmiWa1-9y0-AZk1cAAAPmY50N78724.png`
    },
    {
      label: "称重退差价",
      icon: `https://resource.pagoda.com.cn/group1/M04/34/95/CmiWiF-9zAKAJ3ayAAAGguseIwc015.png`,
      content:
        "此商品按%sg计价，若实际收到小于%sg将按实付金额同等比例退还差价；购买多份时，按总重量计算是否缺重。",
      httpUrl: `https://resource.pagoda.com.cn/group1/M11/34/63/CmiLkF-9ytqASaw3AAAMy_KtwF0875.png`
    }
  ],
  B2C: [
    {
      label: "三无退货",
      icon: `https://resource.pagoda.com.cn/group1/M04/34/95/CmiWiF-9zAKAJ3ayAAAGguseIwc015.png`,
      content:
        "您对商品有任何不满意，可享受7天内无实物无小票无理由，三无退货。",
      httpUrl: `https://resource.pagoda.com.cn/group1/M0A/34/02/CmiWa1-9y0-AZk1cAAAPmY50N78724.png`
    },
    {
      label: "快递到家",
      icon: `https://resource.pagoda.com.cn/group1/M04/34/95/CmiWiF-9zAKAJ3ayAAAGguseIwc015.png`,
      content:
        "此商品由百果园大仓直发，三方快递公司负责配送，物流轨迹可在订单详情页查看",
      httpUrl: `https://resource.pagoda.com.cn/group1/M21/6C/20/CmiLkGGwbguALJbnAAAC-xTClfo215.png`
    }
  ]
};

/**
 * 获取商品标签列表
 * @param {object} goodsObj 商品数据对象
 */
function getGoodsLableList(goodsObj) {
  const {
    storeRecommend = 0,
    activityPrice = 0,
    activitySource = '',
    isSupportCoupon = 'N',
    couponStacking = 0,
    takeawayAttr = '及时达',
    isDeliveryOnly,
    eshopDeliveryType,
    isStockShow,
    stockNum
  } = goodsObj
  const {
    storeRecommend: storeRecommendStyle,
    disbaledCoupon,
    b2cDelivery,
    onlyDelivery,
    onlySelfTake,
    stockNum: stockNumStyle
  } = goodsLabelStyleConfig
  let recommendLabel = [] // 推荐类标签
  let tipsLabel = [] // 提示类标签
  let serviceLabel = [] // 服务类标签
  const priceLabel = [] // 价格类标签
  // 限件类标签
  let goodsLimitList = []
  const goodsLabelList = []
  // 推荐类标签（目前只有店长推荐【门店推荐的商品 && （没有特价 || 有特价但不是门店特价）】）
  if (storeRecommend && (!activityPrice || activitySource !== 'S')) {
    recommendLabel = [storeRecommendStyle]
  }
  // 不可用券【1.不支持使用优惠券 2.有特价活动时不支持叠加使用优惠券】）
  if (isSupportCoupon !== 'Y' || (!!activityPrice && !couponStacking)) {
    tipsLabel = [disbaledCoupon]
  }
  // 服务类标签
  if (isDeliveryOnly) {
    // 配送专享
    serviceLabel = [onlyDelivery]
  } else if (eshopDeliveryType && Number(eshopDeliveryType) === 1) {
    // 自提专享
    serviceLabel = [onlySelfTake]
  }
  // 价格类标签
  if (isStockShow === 'Y' || (stockNum && stockNum <= 10)) {
    const stockNumLabel = { ...stockNumStyle }
    stockNumLabel.name = `仅剩${stockNum}份`
    goodsLimitList = [stockNumLabel]
  }
  // 商品标签
  if (takeawayAttr && takeawayAttr.toUpperCase() === 'B2C') {
    goodsLabelList.push(b2cDelivery, ...goodsLimitList)
  } else {
    // 前一版本规则：有特价时，标签优先级：不可用券 > 价格 > 服务类 > 推荐类标签
    //            无特价时，标签优先级：推荐类标签 > 价格 > 服务类 > 不可用券
    // 当前版本规则v3.6.2：有特价时：门店特价（包含限x件）>库存标签>提示标签>服务类标签>价格类标签（现网暂无）>推荐类标签（现网暂无）
    //                 非特价商品：提示标签>服务类标签>库存标签/限购标签（谁小显示谁）>价格类标签（现网暂无）>推荐类标签（现网暂无）
    // 线上专享特价标签优先级为：线上专享特价/配送专享特价/自提专享特价>不可用券>店长推荐>仅剩x件
    // 新人特价商品标签按已有顺序不变：新人价>不可用券>配送专享/自提专享>店长推荐>仅剩x件
    // 非特价商品标签按已有顺序不变：不可用券>配送专享/自提专享>店长推荐>限购/仅剩x件
    goodsLabelList.push(...tipsLabel, ...serviceLabel, ...recommendLabel, ...goodsLimitList)
  }
  return goodsLabelList
}

/**
   * 处理及时达商品名称标签(商品无等级标签，不做处理)
   * 将商品名称中的 A级- B级- C级- 招牌- 等信息替换为空)(忽略-前后空格，A级 - 也替换)
   * 特殊情况：
   * 【招牌稀有-xxx】改为【稀有-xxx】
   * 【稀有-xxx】不变
   */
function handleGoodsName(goodsObj) {
  const {
    takeawayAttr = '',
    goodsLevel,
    goodsName
  } = goodsObj
  if (takeawayAttr.toUpperCase() === "B2C" || !goodsLevel || !goodsName) {
    return goodsName
  }
  if (!/([ABC]级|招牌)?(稀有)?\s*-\s*/.test(goodsName)) return goodsName
  // goodsObj.origionGoodsName = goodsName
  return goodsName.replace(/([ABC]级|招牌)\s*-\s*|招牌/, "");
}
/**
* 处理b2c商品发货时间展示
*/
function handleB2CGoodsDeliveryTime(goodsObj) {
  // b2cDeliveryType 配送时间类型( 1 默认 ; 2 相对；3 固定 )
  try {
    const {
      b2cDeliveryType,
      b2cDeliveryDate
    } = goodsObj;
    let b2cDeliveryTime = "";
    if (b2cDeliveryDate) {
      switch (String(b2cDeliveryType)) {
        case "1":
          b2cDeliveryTime = `${b2cDeliveryDate}小时内发货`;
          break;
        case "2":
          b2cDeliveryTime = `${b2cDeliveryDate}日内发货`;
          break;
        case "3": {
          // 预售固定发货时间 判断当天时间距离发货时间是否两天内，两天内则自动转为现售48h内发货
          const date = forMatDate(b2cDeliveryDate);
          const days = (new Date(date) - new Date()) / 86400000
          if (days < 2) {
            // 修改商品原始发货时间配置
            Object.assign(goodsObj, {
              b2cDeliveryType: 1,
              b2cDeliveryDate: 48,
            });
            b2cDeliveryTime = "48小时内发货";
          } else {
            b2cDeliveryTime = `${new Date(date).getMonth() + 1}月${new Date(date).getDate()}日前发货`;
          }
          break;
        }
        default:
      }
    }
    return b2cDeliveryTime;
  } catch(err) {
    return ''
  }
}
function processStockDisplay(goods, stockNum, takeawayAttr) {
  const isB2C = takeawayAttr?.toUpperCase() === "B2C";
  
  if (isB2C) {
    // 暂时不用于全国送，后续用的话在开发
    goods.b2cDeliveryTime = handleB2CGoodsDeliveryTime(goods);
    // 库存小于10
    goods.isStockShow = stockNum && stockNum <= 10 ? "Y" : "N";
    return;
  }
  // 库存小于10并且库存小于限购数显示库存
  const shouldShowStock = !!stockNum && 
    stockNum <= 10 &&
    (!goods.purchaseLimit || goods.purchaseLimit > stockNum);
  
  goods.isStockShow = shouldShowStock ? "Y" : "N";
}

/**
 * @desc 适配商品字段
 * @param {Object} goodsObj
 * @param { Object } options
 * @returns {Object}
 */
function forMatGoods(goodsObj) {
  if (!goodsObj.takeawayAttr) goodsObj.takeawayAttr = '及时达'
  let {
    resourceList = [],
    stockNum = 0,
    saleStatus = 0,
    associationList = [],
    takeawayAttr,
    memberPrice,
    heartPrice,
    specWeight,
    isSupportDisparityRefund,
    spLabelList = [],
    goodsSn,
  } = goodsObj;
  // 头图/轮播图等相关映射
  const headPicObj = resourceList.find(item => item.type === 0) || {}
  // http图片替换为https, 线上域名替换，支持图片裁剪
  goodsObj['headPic'] = headPicObj?.url?.replace(
    /^http:\/\//,
    "https://"
  ).replace("fastdfs-download.pagoda.com", "resource.pagoda.com");
  // goodsObj.resourceList = void 0;
  // 是否显示库存提示（仅剩xx件）
  processStockDisplay(goodsObj, stockNum, takeawayAttr);

  // 添加商品标签
  const goodsLabelList = getGoodsLableList(goodsObj);
  goodsObj['goodsLabelList'] = goodsLabelList;
  const filterList = filterAssociationList(associationList)
  // 添加商品spu编码
  goodsObj['spuNumber'] = filterList ?
    filterList[0]?.basicGoodsSn || '' : ''
  // 添加商品是否显示心享九五折icon标示(memberPrice * 0.95 = heartPrice)，有小数，直接舍弃
  goodsObj['vipNineFiveDiscount'] = memberPrice && heartPrice && parseInt(memberPrice * 0.95) === heartPrice;

  const specialServiceList = deepClone(goodsSpecialServiceList[takeawayAttr]) || []
  const weightObj = specialServiceList.find(
    (item) => item.label === "称重退差价"
  );
  if (weightObj) {
    if (isSupportDisparityRefund) {
      const {
        content = ""
      } = weightObj;
      weightObj.content = content.replace(/%s/g, specWeight);
    } else {
      specialServiceList.splice(1, 1);
    }
  }
  // 添加百果园服务说明
  goodsObj['specialServiceList'] = specialServiceList;
  goodsObj.goodsName = handleGoodsName(goodsObj)
  delGoodsNoUseKey(goodsObj)
  goodsObj['spLabelList'] = spLabelList
  goodsObj['labelIds'] = spLabelList.map( item => item.id)
  goodsObj.wxKey = goodsSn
  return goodsObj
}
const goodsDelKey = [
  'channelId',
  'goodsCombinationAssociationGroupId',
  'frontCategoryList',
  'customRetailPrice',
  'linePrice',
  'nutritionalValue',
  'organizationCode',
  'secondSysCategoryCode',
  'storageMethod',
  'sysCategoryCode',
  'vipSpringFilter',
  'resourceList',
  'placeOrigin',
  'dataDesc',
  'vipNoneFrozenFilter',
  'isCombined',
  'isGift',
  'isPurchaseLimit',
  'isRecommended',
  'mainDescription',
  'multiSpecSortNo',
  'organizationType',
  'refThirdEntityList',
]
function delGoodsNoUseKey(goods) {
  goodsDelKey.forEach( key => {
    if (goods.hasOwnProperty(key)) {
      delete goods[key]
    }
  })
}
/**
 * @desc 计算库存，兼容主投入品和备选投入品的场景
 * @param {object} params
*/
function calculateDosageStock(params) {
  const { associationAllList = [], basicGoodsStockMap = {} } = params || {};
  // 主投入品和备选投入品累加的库存
  let totalGoodsStock = 0;
  // 是否存在备选投入品
  let hasAlternateGoods = false;
  // 库存列表
  let goodsStockList = [];
  associationAllList.forEach( association => {
    const { inputType = 0, dosage, basicGoodsSn } = association || {};
    // inputType：2 主投入品，3 备选投入品
    if (Number(inputType) === 2 || Number(inputType) === 3) {
      const basicGoodsStock = basicGoodsStockMap[basicGoodsSn] >= 0 ? basicGoodsStockMap[basicGoodsSn] : 0;
      hasAlternateGoods = true;
      totalGoodsStock += basicGoodsStock /
        (dosage / 10000);
    } else {
      goodsStockList.push(Math.floor(
        (basicGoodsStockMap[basicGoodsSn] || 0) /
        (dosage / 10000)
      ));
    }
  });
  if (hasAlternateGoods) {
    goodsStockList.push(Math.floor(totalGoodsStock));
  }
  const dosageStock = goodsStockList.length > 0 ? Math.min(...goodsStockList) : 0;
  return dosageStock;
}

/**
 * @desc 处理specificationGoodsList
 * @param {object} goodsObj
 */
function handleSpecAndService(goods) {
  const goodsObj = deepClone(goods)
  const {
    specificationGoodsList = [], serviceList = []
  } = goodsObj;
  if (specificationGoodsList.length || serviceList.length) {
    // 这里要注意避免循环引用
    // 然后删除goodsObj中的specificationGoodsList 属性属性
    // goodsObj.specificationGoodsList = void 0;
    delete goodsObj.specificationGoodsList;

    const cloneObj = deepClone(goodsObj)
    // 如果是多拼商品且有多拼信息multiSpecValueJson字段，去除
    if (cloneObj.multiSpecValueJson) cloneObj.multiSpecValueJson = void 0
    specificationGoodsList.push(cloneObj);
    delete goodsObj.serviceList;
  }
  const allSkuStockNum = specificationGoodsList.reduce((acc, cur) => {
    acc += (cur.stockNum || 0)
    return acc
  }, 0)
  specificationGoodsList.forEach(el => el.allSkuStockNum = allSkuStockNum)
  // *****
  // 3.6.3之后一品多规的排序均在前端完成
  // *****
  Object.assign(goodsObj, {
    allSkuStockNum: allSkuStockNum || goodsObj.stockNum,
    specificationGoodsList,
  });
  return goodsObj
}
/**
 * @desc 匹配多拼商品spu库存
 * @param {object} goodsObj
 * @param {{} | {string: number}} spuStock
 */
function matchMutiGoodsSpuStock (goodsObj, spuStock) {
  const { multiSpecValueJson, verifyInventory = 1, specificationGoodsList = [] } = goodsObj || {}
  const spuStockMap = {};
  const handleMultiSpuStock = (good) => {
    const totalSpuList = [];
    const { associationList = [] } = good || {};
    let mainBasicGoodsSn;
    associationList.forEach( association => {
      const { basicGoodsSn, inputType = 0 } = association || {};
      if (Number(inputType) === 2 || Number(inputType) === 3) {
        totalSpuList.push(basicGoodsSn);
      }
      if (Number(inputType) === 2) {
        mainBasicGoodsSn = basicGoodsSn;
      }
    });
    if (mainBasicGoodsSn !== void 0 && totalSpuList.length > 0) {
      spuStockMap[mainBasicGoodsSn] = totalSpuList;
    }
  }
  handleMultiSpuStock(goodsObj)
  specificationGoodsList.forEach(item => {
    handleMultiSpuStock(item)
  });
  if (!Array.isArray(multiSpecValueJson)) return
  const defaultMaxStock = 999999
  const isNotConsumable = (basicGoodsSn) => /^(?!5)/.test(basicGoodsSn) && /^(?!99)/.test(basicGoodsSn)
  multiSpecValueJson.forEach(spec => {
    if (!Array.isArray(spec.specValueList)) return
    spec.specValueList.forEach(subSpec => {
      const { spuNumber } = subSpec
      if (isNotConsumable(spuNumber)) {
        // 不校验库存时，stockNum默认不为0
        const hasAlternateSpuList = spuStockMap[spuNumber];
        let stockNum = 0;
        if (hasAlternateSpuList === void 0) {
          stockNum = spuStock[spuNumber] || 0;
        } else {
          hasAlternateSpuList.forEach( spuNum => {
            if (spuStock[spuNum] !== void 0) {
              stockNum += spuStock[spuNum];
            }
          });
        }
        subSpec.stockNum = verifyInventory ? (stockNum || 0) : defaultMaxStock
      } else {
        // 耗材默认不售罄
        subSpec.stockNum = defaultMaxStock
      }
    })
  })
}
/**
 * @desc 格式化多拼商品信息
 * 1. 将multiSpecGoodsList替换成specificationGoodsList字段，方便处理
 * 2. 只保留外层的multiSpecValueJson，并格式化，列表的去掉
 * @param {object} goodsObj
 */
function formatMultiGoods (goodsObj) {
  const { multiSpecValueJson: json, multiSpecGoodsList = [] } = goodsObj
  let multiSpecValueJson = []
  try {
    multiSpecValueJson = (typeof json === 'string') ? JSON.parse(json) : json
    if (Array.isArray(multiSpecValueJson)) {
      multiSpecValueJson.forEach(item => {
        if (!Array.isArray(item.specValueList)) return
        item.specValueList.forEach(spec => {
          const { spuInfo = {}} = spec
          const spuNumber = spuInfo.spuNumber || ''
          Object.assign(spec, {
            spuNumber,
            spuInfo: void 0
          })
        })
      })
    }
  } catch (error) {
    console.error('multiSpecValueJson parse error:', error)
  }
  if (Array.isArray(multiSpecGoodsList)) {
    multiSpecGoodsList.forEach(item => {
      item.multiSpecValueJson = void 0
    })
  }
  Object.assign(goodsObj, {
    multiSpecValueJson,
    specificationGoodsList: multiSpecGoodsList
  })
  // 替换字段
  goodsObj.multiSpecGoodsList = void 0
}

// 过滤耗材（basicGoodsSn 以 5/99 开头的是耗材）和备选投入品（inputType为3）
// 目前只有果切类型(saleType===2)才有备选品
function filterAssociationList(associationList) {
  if (!associationList || !associationList.length) {
    return []
  }
  return associationList.filter(item => {
    const { basicGoodsSn, inputType = 0 } = item
    return /^(?!5)/.test(basicGoodsSn) && /^(?!99)/.test(basicGoodsSn) && Number(inputType) !== 3
  })
}

// 定义一个策略对象，用来存储不同的策略实例
class ActivityItem {
  // 限时特价策略
  static activityPrice(goodsObj) {
    const params = {
      name: '限时特价',
      desc: '',
      color: '#FF7387',
      borderColor: '#FF7387',
      key: 'activityPrice',
    }
    const descList = [];
    const {
      activitySource,
      effectOrderNo, // 特价每人限购几单
      effectOrderGoodsNumber, // 特价每单限购商品数量
      dayEffectOrderNumber, // 单人单品每天限购数量
      effectOrderNumber, // 单人单品总限购数量
    } = goodsObj;
    if (activitySource === 'S' || !activitySource) {
      effectOrderNo && descList.push(`每人限购${effectOrderNo}单`)
      effectOrderGoodsNumber && descList.push(`每单限购${effectOrderGoodsNumber}份`)
      // 门店特价
    } else {
      effectOrderNumber && descList.push(`每人限购${effectOrderNumber}份`)
      dayEffectOrderNumber && descList.push(`每日限购${dayEffectOrderNumber}份`)
      effectOrderGoodsNumber && descList.push(`每单限购${effectOrderGoodsNumber}份`)
    }
    if (!descList.length) {
      return null
    }
    params.desc = descList.join('，')
    return params
  }
  // 不可用券策略
  static noCoupon(goodsObj) {
    const params = {
      name: '不可用券',
      desc: '',
      color: '#888888',
      borderColor: '#888888',
      key: 'activityPrice',
    }
    const { couponStacking, isSupportCoupon } = goodsObj;
    // 特价商品是否可用券 0不可用 1可用
    // 如果不可用
    if (!couponStacking) {
      // 特价活动不可用券
      if (isSupportCoupon === 'Y') {
        // 商品可用券
        params.desc = '特价份数不适用优惠券'
      } else {
        // 商品不可用券
        params.desc = '该商品不可用券'
      }
    } else if (isSupportCoupon !== 'Y') { // 特价活动可用券
      // 商品不可用券
      params.desc = '该商品不可用券'
    } else {
      return null
    }
    return params
  }
  // 配送专享策略
  static deliveryOnly() {
    return {
      name: '配送专享',
      desc: '配送专享',
      color: '#269D59',
      borderColor: '#269D59',
      key: 'deliveryOnly',
    }
  }
  // 自提专享策略
  static selfPickup() {
    return {
      name: '自提专享',
      desc: '自提专享',
      color: '#269D59',
      borderColor: '#269D59',
      key: 'selfPickup',
    }
  }
  // 全国送不可用券策略
  static b2cNoCoupon() {
    return {
      name: '不可用券',
      desc: '全国送商品不可用券',
      color: '#888888',
      borderColor: '#888888',
      key: 'b2cNoCoupon',
    }
  }
  // 商品不可用券策略
  static goodsNoCoupon(goodsObj) {
    const params = {
      name: '不可用券',
      desc: '',
      color: '#888888',
      borderColor: '#888888',
      key: 'goodsNoCoupon',
    };
    const { isNewCustomerAvailable } = goodsObj;
    params.desc = `${isNewCustomerAvailable ? '此新人专享' : '该'}商品不可用券`;
    return params;
  }
}

// 定义一个上下文类，用来调用不同的策略
class ActivityList {
  constructor(goodsObj) {
    this.goodsObj = goodsObj;
    this.activityList = [];
  }

  // 根据商品对象的属性，选择合适的策略
  chooseGoodsStrategy() {
    const activityList = this.activityList
    const {
      takeawayAttr,
      activityPrice,
      isSupportCoupon,
    } = this.goodsObj
    // 特价商品
    if (activityPrice) {
      const activityPriceItem = ActivityItem.activityPrice(this.goodsObj)
      activityPriceItem && activityList.push(activityPriceItem)
      const noCouponItem = ActivityItem.noCoupon(this.goodsObj)
      noCouponItem && activityList.push(noCouponItem)
    } else {
      // 全国送商品
      if (takeawayAttr && takeawayAttr.toUpperCase() === 'B2C') {
        activityList.push(ActivityItem.b2cNoCoupon());
      } else if (isSupportCoupon !== 'Y') { // 商品不支持使用优惠券商品
        activityList.push(ActivityItem.goodsNoCoupon(this.goodsObj));
      }
    }
  }

  deliveryTypeStrategy() {
    const activityList = this.activityList
    const {
      eshopDeliveryType,
      isDeliveryOnly,
      takeawayAttr,
    } = this.goodsObj
    const objDeliveryType = /** @type { [boolean, any][] } */([
      ...(takeawayAttr === '及时达' ? [
        [isDeliveryOnly, ActivityItem.deliveryOnly()],
        [eshopDeliveryType && Number(eshopDeliveryType) === 1, ActivityItem.selfPickup()],
      ] : []),
      [true, {}],
    ]).find(v => v[0])[1]
    // 组装一下activityList 因为配送方式后面如果有数据 则需要合并
    // 如果是及时达 且为 自提/配送专享商品
    if (objDeliveryType.name) {
      // 如果有其他的限制条件 则需要把第1条限制合并
      if (activityList.length) {
        activityList[0].desc = [
          objDeliveryType.name,
          activityList[0].desc
        ].filter(v => v).join(' · ')
      } else {
        // 如果没有限制条件  则自提/配送专享 也是限制条件
        activityList.push(objDeliveryType);
      }
    }
  }

  // 返回优化后的活动列表
  getActivityList() {
    this.chooseGoodsStrategy()
    this.deliveryTypeStrategy()
    return this.activityList;
  }
}

function mapGoodsActivityList(goodsObj) {
  const activityList = new ActivityList(goodsObj);
  return activityList.getActivityList();
}

function getCitySaleStatus(goodsDetail) {
  // 旧数据可能没有citySaleStatus，没有的话以saleStatus判断
  const { citySaleStatus, saleStatus } = goodsDetail
  return 'citySaleStatus' in goodsDetail ? citySaleStatus : saleStatus
}

/**
 * @desc 新的商品接口只返回商品三要素，查商品详情时，多拼或者一品多规商品只需要查一个，这里进行聚合
 *
 * 果切多拼，一品多规，多个goodsSn进行聚合，传其中一个即可
 * 一品多规组合条件：
 *   组合品一品多规逻辑：先判断isMultiSpec等于0，然后multiSpecGroupId相同
 *   及时达商品: 用 eshopGroupNumber eshopGoodsId
 *   全国送商品：用 groupNumber goodsSn
 *   eshopGoodsId 是老电商编码，其实和 goodsSn 都可以用来做唯一标识
 *   eshopGoodsId != null && saleType in (1 标准份,2 果切)
 *   若为果切，投入品 associationList 剔除5开头的spu后，且投入品只有1个的
 * 分组依据：
 *   saleType + basicGoodsSn + eshopGroupNumber(>0)
 * 果切多拼的组合条件：
 *   isMultiSpec = 1， multiSpecGroupId
 * @param {*} goodsSnList
 */
function collectionGoodsSn(goodsSnList, needSort = true) {
  if (needSort)
    goodsSnList.sort((a, b) => {
      // 用eshopGroupSort排序
      return (a.eshopGroupSort || 0) - (b.eshopGroupSort || 0)
    })
  const goodsInfoList = []
  const setFilter = new Set()

  goodsSnList.forEach(item => {
    const {
      isMultiSpec,
      multiSpecGroupId,
      eshopGoodsId,
      goodsSn,
      saleType,
      eshopGroupNumber,
      groupNumber,
      associationList = [],
      saleStatus = 1,
      takeawayAttr
    } = item
    const isB2C = takeawayAttr && takeawayAttr.toUpperCase() === "B2C"
    const skuCode = isB2C ? goodsSn : eshopGoodsId
    const groupNumberKey = isB2C ? groupNumber : eshopGroupNumber
    // 旧数据可能没有citySaleStatus，没有的话以saleStatus判断
    const checkSaleStatus = getCitySaleStatus(item)
    // 果切多拼 or 组合品
    if (isMultiSpec === 1 || isCombineGroup(item)) {
      if (!setFilter.has(multiSpecGroupId) && checkSaleStatus === 1) {
        setFilter.add(multiSpecGroupId)
        goodsInfoList.push(item)
      }
    }
    //一品多规,单一投入品
    else if (skuCode && (saleType === 1 || saleType === 2) && groupNumberKey > 0) {
      const associationListNew = filterAssociationList(associationList)

      if (associationListNew.length === 1 && associationListNew[0] && associationListNew[0].basicGoodsSn) {
        const spuNumber = associationListNew[0].basicGoodsSn

        const key = saleType + spuNumber + groupNumberKey;
        if (!setFilter.has(key) && checkSaleStatus === 1) {
          setFilter.add(key)
          goodsInfoList.push(item)
        }
      } else {
        if (!setFilter.has(skuCode) && checkSaleStatus === 1) {
          setFilter.add(skuCode)
          goodsInfoList.push(item)
        }
      }
    } else {
      if (checkSaleStatus === 1) {
        goodsInfoList.push(item)
      }
    }
  })
  return goodsInfoList
}

function isCombineGroup(goods){
  const {
    isMultiSpec,
    multiSpecGroupId,
    saleType
  } = goods
  const checkSaleStatus = getCitySaleStatus(goods)
  return saleType === 7 && isMultiSpec === 0 && checkSaleStatus && multiSpecGroupId
}

/**
 * @desc 商详页果切类型商品，剔除5开头的耗材外，投入品数量大于1 的商品，商品标题不展示规格描述
 * @param {object} goods
 */
function showFruitCutSpecDesc(goods) {
  const { associationList = [], saleType } = goods
  const filtered = filterAssociationList(associationList)
  return !(saleType === SALE_TYPE_ENUM.果切 && filtered.length > 1)
}

function flatGoodsList(goodsList) {
  const flattedList = []
  const noFlatGoodsSnMap = new Map()
  const noFlatEshopGoodsIdMap = new Map()
  const goodsSnMap = new Map()
  const eshopGoodsIdMap = new Map()
  goodsList.forEach((goods) => {
    const {
      specificationGoodsList = [], goodsSn, eshopGoodsId
    } = goods;
    flattedList.push(goods);
    noFlatGoodsSnMap.set(goodsSn, goods)
    noFlatEshopGoodsIdMap.set(String(eshopGoodsId), goods)
    goodsSnMap.set(goodsSn, goods);
    eshopGoodsIdMap.set(String(eshopGoodsId), goods);

    (specificationGoodsList || []).forEach((spec) => {
      goodsSnMap.set(spec.goodsSn, spec);
      eshopGoodsIdMap.set(String(spec.eshopGoodsId), spec);
      flattedList.push(spec);
    })
  })
  return {
    flattedList,
    goodsSnMap,
    eshopGoodsIdMap,
    noFlatGoodsSnMap,
    noFlatEshopGoodsIdMap
  }
}
const SUB_TYPES = {
  /**
   * 运营特价
   */
  YUN_YING_TE_JIA: 'S010',
  /**
   * 新人特价
   */
  XIN_REN_TE_JIA: 'S012',
  /**
   * 门店特价
   */
  MEN_DIAN_TE_JIA: 'S033'
}
// 过滤特价活动商品
function filterNoPriceActivityGoods(goodsList) {
  return goodsList.filter(goods => {
    const { activityCode } = goods
    return !activityCode
  })
}
// 过滤特价活动商品
function filterPriceActivityGoods(goodsList) {
  return goodsList.filter(goods => {
    const { activityCode } = goods
    return activityCode
  })
}
function formatDiscount(value) {
  const num = Number(value/100);
  if (isNaN(num)) return '0.0'; // 处理无效输入
  const truncated = Math.floor(num * 10) / 10; // 截断至一位小数
  return truncated.toFixed(1); // 强制补零，如 2 -> "2.0"
}
/**
 * @desc 通过商品的零售价和活动价计算出折扣文案
 * @param {object} goods
 * @return {string}
 */
function getSaveMoneyText (goods) {
  const { retailPrice , activityPrice } = goods
  if(!activityPrice) {
    return ''
  }
  const saveMoney = retailPrice - activityPrice
  if (saveMoney < 0) { return ''}
  const discount = calculateDiscountRate(retailPrice, activityPrice)
  if (discount <= 8.5) {
    return `低至${discount}折`
  } else {
    return `立省${formatDiscount(saveMoney)}元`
  }
}

/**
 * @desc 过滤新人特价商品
 * @param {Array} goodsList 商品列表
 * @return {Array} 过滤后的商品列表
 */
function fliterNewPriceActGoods(goodsList) {
  return goodsList.filter(goods => {
    const { subtype } = goods
    return subtype !== SUB_TYPES.XIN_REN_TE_JIA
  })
}
/**
 * @description 计算折扣
 * @param {*} originalPrice 
 * @param {*} salePrice 
 * @returns 
 */

function calculateDiscountRate(originalPrice, salePrice) {
  // 参数安全校验
  if (typeof originalPrice !== 'number' || 
      typeof salePrice !== 'number' ||
      originalPrice <= 0 ||
      salePrice <= 0 ||
      salePrice > originalPrice
  ) return NaN;

  // 浮点精度处理（放大1000倍避免精度丢失）
  const precision = 1000;
  const scaledOriginal = originalPrice * precision;
  const scaledSale = salePrice * precision;

  // 计算精确折扣率
  const exactRate = scaledSale / scaledOriginal;

  // 向上取整保留一位小数
  const scaledRate = exactRate * 100;
  const ceilingRate = Math.ceil(scaledRate);
  const finalRate = ceilingRate / 10; // 确保不超过100%

  // 格式化输出（整数去零）
  return finalRate % 1 === 0 
    ? Math.trunc(finalRate)
    : Number(finalRate.toFixed(1));
}
module.exports = {
  collectionGoodsSn,
  forMatGoods,
  calculateDosageStock,
  matchMutiGoodsSpuStock,
  handleSpecAndService,
  formatMultiGoods,
  filterAssociationList,
  mapGoodsActivityList,
  getCitySaleStatus,
  handleGoodsName,
  isCombineGroup,
  showFruitCutSpecDesc,
  flatGoodsList,
  filterPriceActivityGoods,
  filterNoPriceActivityGoods,
  SUB_TYPES,
  getSaveMoneyText,
  fliterNewPriceActGoods,
}


