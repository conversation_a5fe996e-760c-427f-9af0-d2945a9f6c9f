import { recharge } from '~/giftCard/business/recharge';
import { GiveStatus } from '~/sub-common/backendApi/giftCard/detailQuery';
import { Card } from '~/sub-common/backendApi/giftCard/listQuery';
import { StatusType } from '~/sub-common/types/giftCard/type';
const sensors = require('~/utils/report/sensors')
const srnsorsOptions = {
  recharge: { 
    'blockName': '我的礼品卡列表',
    'blockCode': 1161601,
    'element_code': 1161601003,
    'element_name': '待使用列表点击充值',
    'element_content': '待使用列表点击充值'
  },
  share: { 
    'blockName': '我的礼品卡列表',
    'blockCode': 1161601,
    'element_code': 1161601004,
    'element_name': '待使用列表点击转增',
    'element_content': '待使用列表点击转增'
  },
  giftDetail: { 
    'blockName': '我的礼品卡列表',
    'blockCode': 1161601,
    'element_code': 1161601005,
    'element_name': '待使用列表点击获赠详情',
    'element_content': '待使用列表点击获赠详情'
  },
  sendingGiftDetail: { 
    'blockName': '转赠中礼品卡列表',
    'blockCode': 1161602,
    'element_code': 1161602002,
    'element_name': '转赠中列表点击转赠详情',
    'element_content': '转赠中列表点击转赠详情'
  },
  giftedDetail: { 
    'blockName': '已赠出礼品卡列表',
    'blockCode': 1161604,
    'element_code': 1161604003,
    'element_name': '已赠出列表点击赠送详情',
    'element_content': '已赠出列表点击赠送详情'
  },
}
function clickGiftDetail(type?: string) {
  if (type === StatusType.NOTUSED) {
    sensors.clickReport(srnsorsOptions.giftDetail)
  } else if (type === StatusType.SENGDING) {
    sensors.clickReport(srnsorsOptions.sendingGiftDetail)
  } else if(type === StatusType.GIFTED) {
    sensors.clickReport(srnsorsOptions.giftedDetail)
  }
}
type BtnOptions = {
  getCode: number,
  cardNumbers?: string[],
  instance: object,
  itemType?: string,
  origion?: string, // 来源：详情或者列表
}

export const btnEnum = {
  SHARE: {
    text: '送给好友',
    /**
     * 绿色按钮
     */
    primary: true,
    fn: (order: BtnOptions) => {
      if (!order) {
        // log...
        return
      }
      wx.navigateTo({
        url: `/giftCard/pages/send/index?from=${StatusType.NOTUSED}&getCode=${order.getCode}`
      })
      if (order.origion && order.origion === 'detail') {
        sensors.clickReport('11619_1161900004')
      } else {
        sensors.clickReport(srnsorsOptions.share)
      }
    }
  },
  RECHARGE: {
    text: '充值',
    fn: (order: BtnOptions) => {
      // todo 唤起充值弹窗
      (order.instance as any).showRechargePopup()
      if (order.origion && order.origion === 'detail') {
        sensors.clickReport('11619_1161900003')
      } else {
        sensors.clickReport(srnsorsOptions.recharge)
      }
    }
  },
  GIFT_DETAIL: {
    text: '赠送详情',
    fn: (order: BtnOptions) => {
      const { itemType } = order
      if (!order || !Array.isArray(order.cardNumbers) || !order.cardNumbers.length) {
        // log...
        return
      }
      // 赠送行，只会包含一张卡
      wx.navigateTo({
        url: `/giftCard/pages/forward/index?from=${StatusType.SENGDING}&cardNumber=${order.cardNumbers[0]}`
      })
      clickGiftDetail(itemType)
    }
  },
  CANCEL_GIFT: {
    text: '取消转赠',
    fn: (order: BtnOptions) => {
      if (!order || !Array.isArray(order.cardNumbers) || !order.cardNumbers.length) {
        // log...
        return
      }
      // 赠送行，只会包含一张卡
      wx.navigateTo({
        url: `/giftCard/pages/forward/index?from=SENGDING&cardNumber=${order.cardNumbers[0]}`
      })
    }
  }
}


/**
 * 底部：左边信息+右边按钮
 */
export const footerMap = {
  [StatusType.NOTUSED]: {
    btn: (card: Card | null, isOwnerBuyer?: boolean) => {
      // 没有可充值的卡时，针对赠送的卡要显示赠送详情
      const giftDetailBtn = { ...btnEnum.GIFT_DETAIL, text: '获赠详情' }
      if (!card) {
        if (!isOwnerBuyer) {
          return [giftDetailBtn]
        }
        return []
      }
      const base = [btnEnum.RECHARGE]
      /**
       * 获赠 => 赠送详情
       * 非获赠 => 送给好友
       */
      base.push(card.giveStatus === GiveStatus.已领取 ? giftDetailBtn : btnEnum.SHARE)
      return base
    },
    msg: () => ''
  },
  [StatusType.SENGDING]: {
    btn: () => [btnEnum.GIFT_DETAIL],
    // todo 倒计时
    // giftExpireTime
    msg: () => '',
  },
  [StatusType.USED]: {
    btn: () => [],
    // todo
    msg: () => ''
  },
  [StatusType.GIFTED]: {
    btn: () => [btnEnum.GIFT_DETAIL],
    // todo
    // giftExpireTime
    msg: () => ''
  }
}