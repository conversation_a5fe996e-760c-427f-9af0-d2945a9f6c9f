/**
 * @desc 神策配置
 */

const sensorsConfig = require('../sensorsdata_conf')
const sensors = require('../../source/js/sensorsdata.min.js')
const srSdk = require('../../source/js/sr-sdk-wxapp.js')
const { appId } = require('../config')
import { getTimelyDistributor, isFromTimelyDistributor } from '../../service/userService.js';
import { getPromiseObj } from '../promise.js';
import { SCREENVIEW } from './sensors.js';

import sensorsBlockConfigMap from './sensorsBlockConfigMap'
import {
  getSensorsPageConfig,
  getSensorInfoByUrl,
  getSensorInfoByCurrentPage,
  setLandingPage,
  superLog,
} from './utils/sensorUtils.js'
import {
  SHOW_SENSORS_SUPER_LOG
} from '../config.js'

/**
 * 根据业务渠道获取所属城市及门店信息
 * @returns
 */
function getCityAndStoreInfoByChannel() {
  /**及时达定位 */
  const timelyCity = wx.getStorageSync('timelyCity')
  /**次日达定位 */
  const bgxxSelectLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo') || {}

  //  渠道为及时达，且存在及时达定位城市时
  if (sensorsChannel === TIMELY && timelyCity) {
    return {
      cityID: timelyCity.cityID || 0,
      cityName: timelyCity.cityName || '',
      storeAddress: timelyCity.address || '',
      storeID: timelyCity.storeID || '',
      storeNum: timelyCity.storeCode || '',
      storeName: timelyCity.storeName || '',
    }
  }
  //  渠道为次日达模块，且存在次日达定位城市时
  else if (sensorsChannel === FRESH && bgxxSelectLocateInfo.selectAddressInfo) {
    const info = {}
    const {
      /**定位信息 */
      selectAddressInfo,
      /**门店信息 */
      selectStoreInfo
    } = bgxxSelectLocateInfo

    if (selectAddressInfo) {
      Object.assign(info, {
        cityID: selectAddressInfo.cityID || 0,
        cityName: selectAddressInfo.cityName || '',
        storeAddress: selectAddressInfo.address || '',
      })
    }

    if (selectStoreInfo) {
      Object.assign(info, {
        storeID: selectStoreInfo.storeID || '',
        storeNum: selectStoreInfo.storeCode || '',
        storeName: selectStoreInfo.storeName || '',
      })
    }
    return info
  }
  return {}
}

/**
 * 获取神策公共上报属性
 */
async function getSensorsPlublicAttr (eventName, data = {}) {
  const user = wx.getStorageSync('user') || {}
  const { unionid = '' } = wx.getStorageSync('wxSnsInfo') || {}
  const traceId = wx.getStorageSync('traceId')
  const isLogin = Boolean(user.userID)

  /**根据渠道获取的当前城市及门店信息 */
  const cityAndStoreInfo = getCityAndStoreInfoByChannel()

  const result = {
    /**微信unionID */
    wechatUnionID: unionid || '',
    /**是否登录状态 */
    is_login: isLogin ? '是' : '否',
    /**终端名 */
    Terminal: '百果园+小程序',

    ...cityAndStoreInfo,

    /**轨迹id */
    traceId,

    /** @desc 分享来源团长ID */
    fromRecommenderId: getTimelyDistributor(),

    /** @desc 是否从及时达分销分享进入 */
    isFromRecommenderLink: isFromTimelyDistributor(),
  }

  /**是否神策内置事件 */
  const isPrivateEvent = eventName?.startsWith('$MP')
  /**
   * 神策内置事件，需要按照触发时sdk提供的路由及参数判断才能正确。
   */
  const pageConfig = await (isPrivateEvent ? getSensorInfoByUrl(`${data.$url_path}?${data.$url_query}`) : getSensorInfoByCurrentPage())

  if (pageConfig) {
    if (!data.$url_path) {
      /**页面路径 */
      result.$url_path = pageConfig.route
    }

    if (!data.screen_name) {
      /**页面名称 */
      result.screen_name = pageConfig.screen_name
    }

    if (!data.screen_code) {
      /**页面编号 */
      result.screen_code = pageConfig.screen_code
    }
  }

  if (isLogin) {
    const memberlevelId_levelName = ['', '1_普卡会员', '2_银卡会员', '3_金卡会员', '4_钻卡会员', '5_绿卡会员'][user.levelId]

    Object.assign(result, {
      /**是否集团新客 */
      GroupNewUsers: user.isGroupNewCustomer === 1 ? '是' : '否',
      /**是否及时达新客 */
      QuicklyDeliveryUsers: user.isEshopNewCustomer ? '是' : '否',
      /**是否次日达新客 */
      NextDeliveryNewUsers: user.isFreshNewCustomer ? '是' : '否',

      /**是否导购好友 */
      wecomIsGuideFriend_a: user.isAddFriend ? '好友' : '非好友',
      /**是否入群 */
      wecomInGroupchat_a: (function() {
        const { isAddGroup, isQuitAddGroup } = user

        if (isQuitAddGroup) {
          return '退群'
        } else if (isAddGroup) {
          return '在群'
        } else {
          return '未加群'
        }
      })(),

      /**心享会员编码 */
      paymemberTypeCode: user.currentTypeCode || '',
      /**心享会员类型 */
      paymemberType: user.currentTypeTag || '',
      /**会员标识 */
      paymemberIdentifyCode: ('identifyCode' in user) ? user.identifyCode : '',
      /**会员等级名称 */
      memberlevelId_levelName: memberlevelId_levelName || '',
    })
  }

  return result
}

/**
 * 当上报事件为SCREENVIEW时，获取前向页面上报属性
 * @param {*} eventName
 * @returns
 */
async function getReferrerAttr () {
  const pages = getCurrentPages()
  const pagesLength = pages.length
  const result = {}
  let pageConfig

  //  页面栈大于1个的情况
  if (pagesLength > 1) {
    const prevPage = pages[pagesLength - 2]
    const { route, options } = prevPage
    pageConfig = await getSensorInfoByUrl(route, options)
  }
  //  页面栈只有1个页面的情况
  else {
    try {
      /**
       * https://developers.weixin.qq.com/community/develop/doc/000eca2dad41c82c141d19fa55e400
       * 在开发者社区2021年的帖子找到的属性，可以获取页面的来源页值
       * 此属性未记录在文档中，属于不稳定属性，后面说不定哪个版本将会被干掉
       * 由于目前没有其他更好的方式去获取来源页，所以一级页面使用此属性来获取来源页。并做捕获异常处理
       */
      const route = getCurrentPages()[0].__displayReporter.showReferpagepath.replace('.html', '')
      const sensorsPageConfigMap = await getSensorsPageConfig
      pageConfig = sensorsPageConfigMap[route]
    } catch (error) {
      console.info('setup.js', '未进行前向页面埋点，原因是', `获取__displayReporter.showReferpagepath属性时报错了`, error)
    }
  }

  if (pageConfig) {
    Object.assign(result, {
      '$referrer': pageConfig.route,
      'referringScreenName': pageConfig.screen_name,
      'referringScreenCode': pageConfig.screen_code
    })
  }

  return result
}

//
export function logoutSensors() {
  sensors && sensors.logout()
}

const TIMELY = 'TIMELY'
const FRESH = 'FRESH'

let sensorsChannel = TIMELY

export function changeTimelyChannel() {
  sensorsChannel = TIMELY
}
export function changeFreshChannel() {
  sensorsChannel = FRESH
}

let cacheFromShareId = ''

export function setFromShareId(shareObjStr) {
  let shareObj = {}
  try {
    shareObj = JSON.parse(shareObjStr)
  } catch {
    shareObj = {}
  }
  cacheFromShareId = shareObj.mp_shareID || ''
}

export function generateShareAttr(eventName = '', checkEvent = false) {
  const openid = (wx.getStorageSync('wxSnsInfo') || {}).openid
  return (!checkEvent) || eventName === 'MPShare' ? Object.assign({
    shareScene: '',
    /** 默认分享小程序卡片,需要修改的话,在埋点数据里覆盖 */
    shareWay: ShareWay.分享小程序卡片,
  }, openid ? {
    mp_shareID: String(Date.now()) + openid,
    mp_shareTime: new Date(),
  } : {}) : {}
}

export const ShareScene = /** @type { const } */({
  及时达商品: '1',
  次日达商品: '2',
  全国送商品: '3',
  接龙商品: '4',
  接龙活动: '5',
  及时达综合专题页: '6',
  次日达综合专题页: '7',
  接龙首页: '8',
})

export const ShareWay = /** @type { const } */({
  分享小程序卡片: '1',
  分享海报: '2',
})

export function setupSensors() {
  const startReportPointPromise = getPromiseObj()

  const reportSensors = true; // 神策埋点埋点开关

  // const protocolVersion = wx.getStorageSync('protocol_version_cache') // 协议版本号
  // const user = wx.getStorageSync('user') || {}; // 用户信息
  // const ISVISITOR = getUserIsVisitor() // 是否为游客模式
  // const isStartReportPoint = Boolean(user.userToken || (protocolVersion && !ISVISITOR)) // 是否开启埋点上报
  const isStartReportPoint = false
  // 不在这里进行初始化
  // 避免提前初始化导致启动事件无用户id的问题
  // 初始化的逻辑变成了从locateMixin的onShow里
  // 通过getApp().startReportPoint()来初始化埋点
  // 目前没发现启动后直接进入无locateMixin的页面
  // 如果有这种情况的话
  // 直接判断登录态app.checkSignInsStatus()之后
  // 再调用loginMixin.startReportPointAfterLogin()进行开启埋点
  // if (isStartReportPoint) {
  //   sensors.setPara(sensorsConfig);
  //   sensors.init();
  //   reportSensors = true
  // }

  const sr = // isStartReportPoint ?
    srSdk.init({
      appid: appId,
      token: 'bi91c4c0cb3b194e5b',
      proxyPage: true,
      usePlugin: true,
      proxyComponent: true,
      openSdkShareDepth: true,
      autoTrack: false,
      openAutoTrackOpenId: true,
      autoStart: true
    })
    // : {
    //   startReport() {},
    //   track() {}
    // };

  // 对sensors的track方法进行拦截，添加公共属性
  const originalMethods = sensors.track;
  sensors.track = async function (eventName, data) {
    // 神策SDK并未按照文档中所描述的那样,等待sa.init后再发送数据
    // 现在在这里加一个等待,等待静默登录完成(包括登录成功/登录失败)后,再发送数据
    await startReportPointPromise.promise
    /**公共属性 */
    const sensorsPlublicAttr = await getSensorsPlublicAttr(eventName, data)
    /**页面来源属性 */
    const referrerAttr = eventName === SCREENVIEW ? await getReferrerAttr() : {}
    /** 分享事件属性 */
    const shareInfo = generateShareAttr(eventName, true)
    /**落地页埋点信息 */
    const landingInfo = await setLandingPage(eventName, data)

    const trackData = {
      fromShareId: cacheFromShareId
    }
    let blockObj = {}
    if (data && data.element_code && !data.blockName) {
      blockObj = sensorsBlockConfigMap[data.element_code] || {}
    }

    Object.assign(
      trackData,
      sensorsPlublicAttr,
      referrerAttr,
      shareInfo,
      data,
      blockObj,
      landingInfo,
    );

    if (SHOW_SENSORS_SUPER_LOG) {
      superLog(eventName, {
        trackData,
        data,
      })
    }
    originalMethods(eventName, trackData);
  };

  return { sr, reportSensors, isStartReportPoint, startReportPointPromise }
}

/**
 * 整合公共属性，给需要的地方用
 * needAll 是否需要全部字段
 */
export async function getPublicProperty(needAll = false) {
  const app = getApp()
  const publicObj = await getSensorsPlublicAttr() || {}
  const referrerObj = await getReferrerAttr() || {}
  // 会员等级弹窗是否已经弹过通知h5
  const lastUpdateLevelTime = wx.getStorageSync('lastUpdateLevelTime') || ''
  const finallyObj = Object.assign(publicObj, referrerObj, {
    $latest_scene: app.globalData.scene,
    lastUpdateLevelTime
  })
  if (needAll) {
    return finallyObj
  } else {
    const propList = ['cityID', 'cityName', 'storeID', 'storeName', 'storeNum']
    propList.forEach(item => {
      if (typeof finallyObj[item] !== 'undefined') {
        delete finallyObj[item]
      }
    })
    return finallyObj
  }
}
