/* index.wxss */
page {
  background-color: rgb(242, 242, 242);
  position: relative;
}
.main {
  /* background: rgba(255,255,255,1); */
  margin-top: 21rpx;
}
.info-header-container{
  margin-bottom: 20rpx;
}
.info-item {
  height: 90rpx;
  line-height: 90rpx;
  background: rgba(255,255,255,1);
  display: flex;
  align-items: center;
  padding-right: 30rpx;
  padding-left: 36rpx;
  border-bottom: 1px solid #F2F2F2;
}
.info-item-label {
  font-size: 32rpx;
  color: #000000;
}
.info-item-value {
  flex: 1;
  color:#212121;
  /* color: #999999; */
  font-size: 28rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.info-item-value>image {
  height: 20rpx;
  width: 20rpx;
  margin-left: 8rpx;
}
.info-item-value .avatar-img{
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
}
.margin-right{
  margin-right: 35rpx;
}
.btn-submit {
  margin-top: 40rpx;
  text-align: center;
  line-height: 88rpx;
  background-color:#008C3C;
  color: rgb(255, 255, 255);
  width:684rpx;
  height:88rpx;
  border-radius:40rpx;
  font-size: 34rpx;
  margin-left: 32rpx;
}
.cant-save{
  background-color: #ccc;
}
.gender-mask {
  position: fixed;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.3);
}
.gender-box {
  width: 100%;
  height: 180rpx;
  background-color: #fff;
  border-radius: none;
  position: fixed;
  bottom: -90px;
  z-index: 98;
}
.gender-box>view {
  text-align: center;
  height: 90rpx;
  line-height: 90rpx;
}
picker {
  width: 100%;
  text-align: right;
}

.info-bot-container{
  margin-top: 20rpx;
}
.mask{
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  top: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 99;
}
.guide_pic{
  margin-top: 303rpx;
  width:720rpx;
  /* height: 380rpx; */
}
.guide_pic>image{
  width:720rpx;
  /* height: 380rpx; */
}
.know_pic{
  margin-top: 35rpx;
  width: 209rpx;
  height: 84rpx;
}
.know_pic>image{
  width: 209rpx;
  height: 84rpx;
}
.input_page{
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: #f7f7f7;
  top: 0;
  z-index: 9;
}
.input_container{
  margin-top: 20rpx;
  height: 90rpx;
  background:rgba(255,255,255,1);
  padding-right: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.input_style {
  padding-left: 36rpx;
  color:#999999;
}
.nofinish{
  width:152rpx;
  height:56rpx;
  background:rgba(204,204,204,1);
  border-radius:28rpx;
  font-size: 24rpx;
  color: #fff;
  text-align: center;
  line-height: 56rpx;
}
.nofinish.finish{
  background-color: #008C3C;
}
.name_input{
  text-align: right;
}
.popup-btn{
  width: 100%;
  background: #00A644;
  border-radius: 48rpx;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  padding: 18rpx 0;
  margin-top: 20rpx;
}
.button{
  line-height: 50rpx;
  box-sizing: border-box;
  border: none;
}
.popup-btn-cancel{
  width: 100%;
  background: #fff;
  border-radius: 48rpx;
  font-size: 32rpx;
  color: #8C8C8C;
  text-align: center;
  padding: 18rpx 0;
}
.tips-link {
  color: #019B50;
  position: relative;
  z-index: 10;
}
.popup-content{
  font-size: 28rpx;
}


.item-avatar {
  position: relative;
}
.item-avatar-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 120rpx;
  height: 70rpx;
  opacity: 0;
}

