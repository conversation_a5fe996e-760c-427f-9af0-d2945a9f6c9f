/**
 * 小程序
 */
const miniprogramCI = require('miniprogram-ci')
const config = require('../../src/utils/config')
const { envMap, descMap, mpPreviewImagePath, platformNameMap, robotMap } = require('./const')
const { appVersion: version } = config
const { newProject, handleSetting } = require('./commonService')
const path = require('path')
const { handlePreviewImage } = require('./imageHandler')
const { robotSendErrMsg, qywxRobot } = require('../notify')
const { handleEnvFileAOP } = require('./handleEnv')
const basePath = path.resolve(__dirname, '../../')
/**
 * @desc 预览
 * @param {object} payload
 * @param {string} payload.basePath
 * @param {string} payload.env
 */
async function preview(payload) {
  const { basePath, env } = payload
  const desc = descMap[env]
  const previewProject = newProject(basePath)
  const previewResult = await miniprogramCI.preview({
    project: previewProject,
    version, // 自定义版本号
    desc: desc, // 自定义备注
    setting: handleSetting(),
    qrcodeFormat: 'image',
    robot: robotMap[env],
    qrcodeOutputDest: mpPreviewImagePath,
    onProgressUpdate: console.log,
  })
  // console.log(`mp-weixin preview`, previewResult)
  return previewResult
}

/**
 * @desc 上传
 * @param {object} payload
 * @param {string} payload.basePath
 */
async function upload(payload) {
  const { basePath } = payload
  const uploadProject = newProject(basePath)
  const uploadResult = await miniprogramCI.upload({
    project: uploadProject,
    version, // 自定义版本号
    desc: '自动发布', // 自定义备注
    setting: handleSetting(),
    scene: 1001,
    onProgressUpdate: console.log,
  })
  console.log('mp-weixin upload', uploadResult)
  return uploadResult
}

async function mpCD(env) {
  async function mpPreview(env) {
    try {
      await Promise.all([preview({basePath, env})])
      console.log('预览图片生成')
      const result = await handlePreviewImage({previewImagePath: mpPreviewImagePath, env, platform: platformNameMap.mp})
      if (!result.hash) throw result
      const { base64, hash } = result
      qywxRobot({
        msgtype: 'image',
        image: {
          base64: base64,
          md5: hash,
        },
      })
    } catch (error) {
      console.log('preview error', error)
      robotSendErrMsg(`预览失败: ${JSON.stringify(error)}`)
    }
  }
  async function mpUpload() {
    if (env !== envMap.prod) return
    try {
      await upload({basePath})
      robotSendErrMsg('微信小程序上传成功')
    } catch (error) {
      robotSendErrMsg(`微信小程序上传失败： ${JSON.stringify(error)}`)
    }
  }
  await Promise.all([
    mpPreview(env),
    mpUpload(env)
  ])
}
(async function main(){
  try {
    const env = process.env.NODE_ENV
    const cd = handleEnvFileAOP(mpCD, env)
    await cd(env)
    // process.exit(0)
  } catch (error) {
    // process.exit(1)
  }
})()
