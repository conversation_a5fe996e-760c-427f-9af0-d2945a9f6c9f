const request = require('../utils/request')
const config = require('../utils/config')

/**门店社群信息映射 */
const storeManagerCodeInfoArr = []
/**门店编码与其对应的接口请求映射 */
const storeManagerCodeRequestMap = {}

/**
 * 心享用户相关接口（用户订单，地址相关接口）
 */
module.exports = {
  // 获取用户状态
  bgxxGetCustomerInfo({ data: { customerID }, options: { isReturnLoginOutError } }) {
    return request.get({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/customer/detail/information/${customerID}`, isLoading: false, isEncryptRes: true, isReturnLoginOutError })
  },
  // 获取订单最近配送方法及收货地址
  getBgxxLatelyAddress(params) {
    const {
      userID,
      cityID
    } = params
    return request.get({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v3/customer/latelyAddress/${cityID}/${userID}`, isEncryptRes: true, encryptType: 'token' })
  },
  // 心享订单结算
  bgxxOrderSettle(params, data) {
    const { customerID, cityID, storeID, lon, lat, deliveryWay } = params
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v2/order/settlement/${customerID}/${cityID}/${storeID}/${lon}/${lat}/${deliveryWay}`, data, isLoading: false})
  },
  // 提交心享订单
  submitBgxxOrder(params, data) {
    const { customerID, cityID, storeID, deliveryWay } = params
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v2/order/submit/${customerID}/${cityID}/${storeID}/${deliveryWay}`, data, isLoading: false})
  },
  // 检查订单支持结果
  checkBgxxPayResult(customerID, paymentOrderID) {
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/checkPayStatus/${customerID}/${paymentOrderID}`, isLoading: false})
  },
  // ----------------------------------- 原心享项目 cartApi ------------------------
  // 再来一单
  bgxxBuyAgain(params, isLoading = true) {
    return request.post({ url: `/wxapp/cart/fresh/v1/repeatOrder`, data: params, isLoading })
  },

  // ----------------------------------- 原心享项目部分 mallApi ------------------------
  // 获取订单列表
  bgxxGetOrderList(userID, tapStatus, action, count = 10, lastUpdate, isCycle = false) {
    return request.get({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/${userID}/${tapStatus}/${action}/${count}/C/${lastUpdate}?isCycle=${isCycle ? 'SUPER_VIP_CYCLE' : ''}`, isLoading: false })
  },
  // 取消未支付订单
  bgxxChanelNoPayOrder(userID, payOrderID) {
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/cancel/unpaid/${userID}/${payOrderID}/1`, data: {}})
  },
  // 取消已支付订单
  bgxxRefundOrder(params) {
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/refund/paid`, data: params })
  },
  // 更新未支付订单配送时间范围
  bgxxUpdateDeliveryTime(params) {
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/payorder/deliveryTime/update`, data: params })
  },
  // 确认收货
  bgxxConfirmReceipt(customerID, goodsOrderNo) {
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/confirmReceipt/${customerID}/${goodsOrderNo}`, data: {} })
  },
  // 确认提货
  bgxxConfirmPickUp(customerID, goodsOrderNo) {
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/confirmPickUp/${customerID}/${goodsOrderNo}`, data: {} })
  },
  // 果币支付
  bgxxPayGB(userID, payOrderNo, params, isLoading = false) {
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/pay/request/${userID}/${payOrderNo}/gb`, data: params, isLoading })
  },
  // 微信支付
  bgxxPayWechat(userID, params, isLoading = false) {
    return request.post({ url: `${config.baseUrl.PAGODA_SMS_DOMAIN}/customer/api/v1/pay/request/${userID}`, data: params, isLoading })
  },
  // 查询周期购每期详情
  bgxxGetEachDetail(params) {
    const {
      customerID,
      goodsOrderID,
      selectedDate
    } = params
    return request.get({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/cycleBuy/eachDetail/${customerID}/${goodsOrderID}?selectedDate=${selectedDate}` })
  },
  // 修改周期购订单配送时间
  bgxxUpdateCycleBuyTime(params) {
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/cycleBuy/deliveryTime/update`, data: params })
  },
  // 获取订单详情
  bgxxGetOrderDetailInfo(isPay, userID, orderID) {
    let url = ''
    if (isPay) {
      url = `/api/v1/order/detail/${userID}/${orderID}`
    } else {
      url = `/api/v1/payorder/detail/${userID}/${orderID}` //没拆单
    }
    return request.get({ url: `${config.baseUrl.BGXX_API_DOMAIN}${url}` })
  },
  bgxxGetOrderCancelReason(data) {
    return request.post({ url:`${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/cancel/reason/list`, data})
  },
  // 获取限单量信息 http://wiki.pagoda.com.cn/pages/viewpage.action?pageId=19562761
  bgxxQueryOrderLimitInfo (data) {
    return request.post({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/order/limit/query`, data })
  },

  // ----------------------------------- 原心享项目 signInApi ------------------------

  // 获取邀请用户试用会员列表
  qrcodeInvite(customerID, qrcodeUrl) {
    return request.get({ url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/superVip/info/${customerID}?codeParam=${qrcodeUrl}` })
  },
  // 邀请列表
  getInviteList(customerID) {
    return request.get({url: `${config.baseUrl.BGXX_API_DOMAIN}/api/v1/superVip/inviteRecord/${customerID}`})
  },
  // 心享会员纯签约支付
  bgxxContractPay(params, isLoading = false) {
    return request.post({ url: `${config.baseUrl.PAGODA_SMS_DOMAIN}/customer/api/v1/contract/request`, data: params, isLoading })
  },
  // 绑定分享邀请者与被邀请者关系
  bindUserRelation(params, isLoading = false) {
    return request.post({ url: `/wxapp/user/v1/bindRelation`, data: params, isLoading })
  },
  // 获取会员配置信息
  getMemberConfig(params, isLoading = false){
    return request.post({ url: `/wxapp/user/v1/getMemberConfig`, data: params, isLoading })
  },
  // 编辑会员配置信息
  editMemberConfig(params, isLoading = false){
    return request.post({ url: `/wxapp/user/v2/editMemberConfig`, data: params, isLoading })
  },
  // 根据门店code获取门店成员企微二维码
  async getManagerCodeByStoreCode(params){
    const storeCode = params.storeCode

    //  存在相同门店正在请求中的请求，返回这个Promise实例
    if (storeManagerCodeRequestMap[storeCode]) {
      return storeManagerCodeRequestMap[storeCode]
    }

    const hasStoreInfo = storeManagerCodeInfoArr.find(res => {
      const data = res.data || {}
      return String(data.store_code) === String(storeCode)
    })

    //  存在相同的门店社群信息直接返回
    if (hasStoreInfo) {
      return hasStoreInfo
    } else {
      const _request = request.post({ url: `/wxapp/user/v1/getStoreManagerCode`, data: params, isLoading: false })
      //  缓存该门店本次的请求，在请求结束前。后续发起的相同门店的请求，将会被复用
      storeManagerCodeRequestMap[storeCode] = _request

      const result = await _request

      storeManagerCodeInfoArr.unshift(result)
      //  不在内存中缓存太多门店社群信息
      if (storeManagerCodeInfoArr.length > 5) {
        storeManagerCodeInfoArr.length = 5
      }

      //  移除本次请求的缓存
      delete storeManagerCodeRequestMap[storeCode]

      return result
    }
  }
}
