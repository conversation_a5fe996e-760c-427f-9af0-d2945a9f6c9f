/**
 * @desc 订单支付。接龙，及时达共用
 */

const app = getApp()

const noop = () => {}

export const PAY_TYPE_ENUM = {
  // 及时达确认订单支付
  FRUIT_CONFIRM_PAY: 'FRUIT_PAY',
  // 及时达订单页支付
  FRUIT_ORDER_PAY: 'FRUIT_ORDER_PAY',
  // 接龙确认订单支付
  RELAY_CONFIRM_PAY: 'RELAY_PAY',
  // 接龙订单页支付
  RELAY_ORDER_PAY: 'RELAY_ORDER_PAY'
}

export const WE_CHAT= 'WE_CHAT' // 微信渠道
export const UNIONPAY = 'UNIONPAY' // 微信渠道
export const PAGODA = 'PAGODA'  // 百果园渠道
export const MIXIN_PAY = 'MIXIN_PAY'  // 混合支付

// 支付方式
const PAYMENT_WAY_ENUM = {
  [WE_CHAT]: 'WX_MINI',
  [PAGODA]: 'BGY_APP',
  [UNIONPAY]: 'UNION_WX_MINI',
}

// 旧的支付渠道类型
const OLD_PAYMENT_CHANNEL_ENUM = {
  [WE_CHAT]: 'W',
  [PAGODA]: 'M'
}
// 新的支付渠道类型
const NEW_PAYMENT_CHANNEL_ENUM = {
  [WE_CHAT]: 7,
  [PAGODA]: 1,
  [UNIONPAY]: 22,
}

const businessInfo = {
  subject: '百果园订单支付',
  description: '百果园订单支付'
}

class FruitOrderPay {
  constructor() {
    this.payChannel = ''
    this.paymentWay = ''
    this.mainOrderNo = ''
    this.tradeNo = ''
    this.isUnionPay = false
    // this.isGrayScale = false
    this.succCb = noop
    this.failCb = noop
  }
  /**
   * 判断是否是第三方支付
   * @param {string} payType 支付类型
   * @param {object} [options] 选项
   * @param {boolean} options.excludeMixinPay 是否排除混合支付
   * @return {boolean}
   */
  isThirdPay(payType, options = {}) {
    const { excludeMixinPay } = options
    const payTypes = [WE_CHAT, UNIONPAY]
    excludeMixinPay || payTypes.push(MIXIN_PAY)
    return payTypes.includes(payType)
  }

  /**
   * 判断是否是主钱包支付
   * @param {string} payType 支付类型
   * @param {object} [options] 选项
   * @param {boolean} options.includeMixinPay 是否包含混合支付
   * @return {boolean}
   */
  isPagodaPay(payType, options = {}) {
    const { includeMixinPay } = options
    const payTypes = [PAGODA, 'zeroPay']
    includeMixinPay && payTypes.push(MIXIN_PAY)
    return payTypes.includes(payType)
  }
  /**
   *
   * @param {object} payInfo
   * @param {boolean} payInfo.usePagodaPay 是否使用主钱包支付
   * @param {boolean} payInfo.useWxPay 是否使用微信支付
   * @param {boolean} payInfo.useUnionPay 是否使用云闪付
   * @param {number} payInfo.paymentAmount 支付金额
   * @param {string=} payInfo.tradeNo 交易号
   * @param {string} payInfo.mainOrderNo 主订单号
   * @param {string=} payInfo.isSelectVoucher 是否用了代金券
   * @param {string=} payInfo.isSelectExchangeCard 是否用了兑换卡
   * @param {object} extraInfo
   * @param {boolean=} extraInfo.isGrayScale 是否灰度用户
   * @param {function} extraInfo.succCb 成功回调
   * @param {function} extraInfo.failCb 失败回调
   * @param {string=} extraInfo.payType 方法调用的类型
   */
  handlePay (payInfo = {}, extraInfo = {}) {
    console.log(payInfo)
    const {
      usePagodaPay,
      paymentAmount,
      tradeNo,
      mainOrderNo,
      useWxPay,
      useUnionPay,
      mainBalance,
      isSelectVoucher = false,
      isSelectExchangeCard = false,
      rechargePayInfo,
    } = payInfo
    const { succCb = noop, failCb = noop,} = extraInfo
    this.succCb = succCb
    this.failCb = failCb
    this.mainOrderNo = mainOrderNo
    this.tradeNo = tradeNo
    this._setPaymentInfo({ usePagodaPay, useWxPay, useUnionPay })
    const hasThirdPay = useWxPay || useUnionPay
    const noThirdPay = !hasThirdPay
    const handlePay = ([
      {
        condition: rechargePayInfo,
        callback: () => this._handleInlineOrderRechargePay({
          paymentAmount,
          rechargePayInfo,
          isSelectVoucher,
          isSelectExchangeCard,
        }),
      },
      {
        condition: [
          // 0元支付，并且用了代金券
          isSelectVoucher && paymentAmount <= 0,
          // 使用了兑换卡
          isSelectExchangeCard && paymentAmount <= 0,
        ].some(v => v),
        callback: () => this._handleZeroPay(paymentAmount),
      },
      {
        // 只用会员钱包
        condition: usePagodaPay && noThirdPay,
        callback: () => this._handlePagodaPay(paymentAmount)
      },
      {
        // 只用三方支付
        condition: hasThirdPay && !usePagodaPay,
        callback: () => this._handleThirdPay(paymentAmount)
      },
      {
        // 会员钱包和三方支付混合
        condition: usePagodaPay && hasThirdPay,
        callback: () => this._handleMixinPay({ paymentAmount, mainBalance })
      },
    ].find(v => v.condition) || { callback: () => Promise.resolve() }).callback
    return handlePay()
    // usePagodaPay ? await this._handlePagodaPay(paymentAmount) : await this._handleThirdPay(paymentAmount)
  }
  _setPaymentInfo ({ usePagodaPay, useWxPay, useUnionPay }) {
    // 判断是余额支付还是微信支付，后面app可能会增加其他支付方式
    const type = [
      {
        condition: usePagodaPay,
        value: PAGODA,
      },
      {
        condition: useWxPay,
        value: WE_CHAT,
      },
      {
        condition: useUnionPay,
        value: UNIONPAY,
      },
      // 默认值，微信支付
      {
        condition: true,
        value: WE_CHAT,
      },
    ].find(v => v.condition).value
    this.payChannel = NEW_PAYMENT_CHANNEL_ENUM[type]
    this.paymentWay = PAYMENT_WAY_ENUM[type]
    this.isUnionPay = useUnionPay
  }
  _getOpenID() {
    const { openid } = wx.getStorageSync('wxSnsInfo') || { openid: '' }
    return openid || app.globalData.wxOpenId
  }
  _getAppShowCallback(preOrderInfo, callback) {
    return function onAppShow(params = {}) {
      const { referrerInfo = {} } = params
      if (referrerInfo.appId !== preOrderInfo.cqpMpAppId) {
        return
      }
      wx.offAppShow(onAppShow)
      callback.success()
    }
  }
  async _requestPaySDK(paymentInfos, payType) {
    const fetchRes = await this._fetchPayApi(paymentInfos)
    if (!fetchRes) return this.failCb({ payType })
    const { preOrderInfo } = fetchRes.jsonObject || {}
    const { timeStamp, nonceStr, package: packageVal, paySign, signType, sign } = preOrderInfo || {}
    return new Promise((resolve) => {
      const callback = {
        success: () => {
          // that.reportPay({
          //   type: 'wxPay',
          //   data: orderData
          // })
          resolve()
          this.succCb({ payType, data: fetchRes })
        },
        fail: err => {
          console.log('支付失败回调', err)
          // that.reportTxCanalePay(orderData)
          // that.openOrderDetail(orderData)
          resolve()
          this.failCb({ payType, err: err })
        }
      }
      if (this.isUnionPay) {
        const { cqpMpPath, cqpMpAppId } = preOrderInfo
        const onAppShow = this._getAppShowCallback(preOrderInfo, callback)
        wx.openEmbeddedMiniProgram({
          appId: cqpMpAppId,
          path: cqpMpPath,
          success() {
            wx.onAppShow(onAppShow)
          },
          fail: () => callback.fail({ errMsg: 'fail cancel openEmbeddedMiniProgram' }),
        })
      } else {
        wx.requestPayment({
          timeStamp,
          nonceStr,
          package: packageVal,
          signType: signType || 'HMAC-SHA256',
          paySign: paySign || sign,
          ...callback,
        })
      }
    })
  }
  /**
   *
   * @param {number} paymentAmount
   */
  async _handleThirdPay (paymentAmount) {
    const paymentInfos = [{
      paymentChannel: this.payChannel,
      paymentAmount,
      paymentWay: this.paymentWay,
      openID: this._getOpenID()
    }]
    return this._requestPaySDK(Object.assign({
      paymentInfos,
      paymentInfo: paymentInfos[0],
      businessInfo,
    }, { parentOrderNo: this.mainOrderNo }), this.isUnionPay ? UNIONPAY : WE_CHAT)
  }
  async _handleZeroPay(parentOrderAmount) {
    try {
      // 灰度用户用新接口
      const postData = {
        parentOrderNo: this.mainOrderNo,
        parentOrderAmount
      }
      const fetchRes = await app.api.orderZeroPay(postData)
      this.succCb({ payType: 'zeroPay', data: fetchRes
      })
    } catch (error) {
      this.failCb({ payType: 'zeroPay' })
      console.log('FruitOrderPay _fetchPayApi: ', error);
    }
  }

  /**
   * 处理随单充值支付
   * @param {*} param0 
   * @returns 
   */
  async _handleInlineOrderRechargePay({
    paymentAmount,
    rechargePayInfo,
    isSelectVoucher,
    isSelectExchangeCard,
  }) {
    const openID = this._getOpenID()
    let paymentInfos = []

    //  代金券、兑换卡支付金额为0时，只有微信支付
    if (paymentAmount <= 0 && (isSelectVoucher || isSelectExchangeCard)) {
      paymentInfos = [
        {
          paymentChannel: NEW_PAYMENT_CHANNEL_ENUM.WE_CHAT,
          paymentAmount: rechargePayInfo.thirdPayAmount,
          paymentWay: PAYMENT_WAY_ENUM.WE_CHAT,
          openID
        },
      ]
    }
    //  其余情况为混合支付，前端组装微信扣款和钱包扣款金额
    else {
      paymentInfos = [
        {
          paymentChannel: NEW_PAYMENT_CHANNEL_ENUM.PAGODA,
          paymentAmount: rechargePayInfo.balancePayAmount,
          paymentWay: PAYMENT_WAY_ENUM.PAGODA,
          openID
        },
        {
          paymentChannel: NEW_PAYMENT_CHANNEL_ENUM.WE_CHAT,
          paymentAmount: rechargePayInfo.thirdPayAmount,
          paymentWay: PAYMENT_WAY_ENUM.WE_CHAT,
          openID
        },
      ]
    }

    return this._requestPaySDK(Object.assign({
      paymentInfos,
      businessInfo,
    }, { parentOrderNo: this.mainOrderNo }), MIXIN_PAY)
  }
  
  async _handleMixinPay({mainBalance, paymentAmount}) {
    const openID = this._getOpenID()
    const paymentInfos = [
      {
        paymentChannel: NEW_PAYMENT_CHANNEL_ENUM.PAGODA,
        paymentAmount: mainBalance,
        paymentWay: PAYMENT_WAY_ENUM.PAGODA,
        openID
      },
      this.isUnionPay ? {
        paymentChannel: NEW_PAYMENT_CHANNEL_ENUM.UNIONPAY,
        paymentAmount: paymentAmount - mainBalance,
        paymentWay: PAYMENT_WAY_ENUM.UNIONPAY,
        openID,
      } : {
        paymentChannel: NEW_PAYMENT_CHANNEL_ENUM.WE_CHAT,
        paymentAmount: paymentAmount - mainBalance,
        paymentWay: PAYMENT_WAY_ENUM.WE_CHAT,
        openID
      }
    ]
    return this._requestPaySDK(Object.assign({
      paymentInfos,
      businessInfo,
    }, { parentOrderNo: this.mainOrderNo }), MIXIN_PAY)
  }
  async _handlePagodaPay (paymentAmount) {
    const paymentInfo = {
      paymentChannel: this.payChannel,
      paymentAmount,
      paymentWay: this.paymentWay,
    }
    const fetchRes = await this._fetchPayApi(Object.assign({
      paymentInfos: [paymentInfo],
      paymentInfo: paymentInfo,
      businessInfo,
    }, { parentOrderNo: this.mainOrderNo } ))
    return fetchRes && !fetchRes.payMessage ? this.succCb({ payType: PAGODA, data: fetchRes }) : this.failCb({ payType: PAGODA, payMessage: fetchRes.payMessage })
  }
  /**
   *
   * @param {object} postData
   * @returns { Promise<false|object> } 失败返回false;成功返回支付信息
   */
  async _fetchPayApi (postData) {
    // 4.7.0 入参需要增加门店编码
    const { storeCode } = wx.getStorageSync('timelyCity') || {}
    if (storeCode && postData.businessInfo) {
      Object.assign(postData.businessInfo, {
        merchantNo: storeCode
      })
    }
    try {
      // 灰度用户用新接口
      const res = await app.api.orderPayCombine(postData)
      return res.data || false
    } catch (error) {
      console.log('FruitOrderPay _fetchPayApi: ', error);
      getApp().monitor.report({
        tag: 'fetchPayApi',
        err: error
      })
      return false
    }
  }
}

export const fruitOrderPay = new FruitOrderPay()
