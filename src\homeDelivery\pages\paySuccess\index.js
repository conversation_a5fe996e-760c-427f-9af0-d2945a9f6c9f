// pages/paySuccess/index.js
import styleConfig from '../../../utils/goodsStyleConfig'
const operateCartMixin = require('../../../mixins/bgxx/operateCartMixin')
const cartAnimate = require('../../../mixins/bgxx/cartAnimate')
const conFnService = require('../../../utils/services/conFnService')
const config = require('../../../utils/config')
const {commonObj} = require('../../../source/js/common.js')
const {throttle, toSubShopCart} = require('../../../utils/util')
const navigateToH5Mixin = require('../../../mixins/navigateToH5Mixin')
const sensors = require('../../../utils/report/sensors')
const { getRecommendGoods } = require('../../../source/js/requestData/getFreshRecommendGoods')
import storeBinding from '../../../mixins/bgxx/storeBinding'
import { updateUserDeviceInfo, updateVipStatus } from '../../../service/userService'
import { getPromiseObj } from '../../../utils/promise'
const activity = require('../../../utils/activity');
const wxbarcode = require.async('../../../sourceSubPackage/barcode/index.js');
const turingReport = require.async('../../../sourceSubPackage/commonUtils/turing/report.js');

const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    orderInfo: {},
    saveMoney: -1,
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    showMembrane: false,
    isShowActivityLayer: false,
    goodsList: [],
    cartVisible: false,
    bannerOptions: {},
    picDomain: config.baseUrl.PAGODA_PIC_DOMAIN,
    styleConfig,
    showLikeGoods: false, // 是否展示猜你喜欢商品
    bgxxTips: {},  // 心享会员提示语
    qrCode:"", // 企微头像二维码
    lineText:[], // 二维码模块文案
    storeInfo:'', // 门店信息,
    orderStoreCode:'', // 下单的门店code
    cemParams: {}, // cem上报参数
    storePhone: '',
    navBarBgColor: '#28BC4F', // 导航栏背景颜色
    navBarColor: '#fff', // 导航栏字体颜色
    backFilter: 1, // filter brightness
    navBarTitle: '支付成功',
    barcodeImgSrc: '', // 条形码对应图片
    // 是否随单充订单
    isRecharge: 0,
  },
  _data: {
    ecmInit: {},
    orderNo: '',
    subOrderNo: '',
    selectMonthCard: false, // 是否选择在确认订单页开通月卡
    // 次日达推荐商品对象
    freshGoodsObj: {
      freshGoods: null
    }
  },
  mixins: [operateCartMixin, cartAnimate, navigateToH5Mixin, storeBinding],
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad (options) {
    console.log('paySuccess options',options)
    const subOrderNo = options?.subOrderNo
    if (options && options.paySuccessObj) {
      const {
        paySuccessObj,
        needShowCode,
        storeCode,
        storePhone,
        selectMonthCard,
        isMix,
        isTimelyOrder,
        deliveryWay,
        timelyGoodsIsOverWeight,
        isRecharge,
      } = options
      Object.assign(this._data, {
        selectMonthCard: !!Number(selectMonthCard),
        subOrderNo,
        orderNo: paySuccessObj,
        isMix
      })
      this.setData({
        isRecharge: isRecharge && ['0', '1'].includes(isRecharge) ? Number(isRecharge) : 0,
        timelyGoodsIsOverWeight: timelyGoodsIsOverWeight === 'Y',
        storePhone,
        cemParams: {
          orderID: paySuccessObj,
          orderType: {
            deliveryToDoor: '及时达到家订单',
            selfTake: '及时达自提订单',
          }[deliveryWay] || ''
        },
        orderStoreCode:storeCode
      })
      // needShowCode 且 有orderNo 则请求二维码
      this.getPaySuccessInfo(needShowCode === "true" && paySuccessObj)
      this.paySuccessPopup()
      // 获取banner
      this.getBanner()
      // 获取推荐商品
      this.setRecommendGoods()
      if (Number(isTimelyOrder)) {
        this._data.CEMParameters = getPromiseObj()
        this._data.ecmInit = Object.assign({}, getPromiseObj(), {
          erpAreaInfoPreset: app.api.getERPAreaInfo({
            storeCode: storeCode
          })
        })
      }
    }
    const { shareActivityPic, shareActivityFloatingPic } = wx.getStorageSync('eshopSysConfig') || {}
    this.setData({
      redpacketBackground: shareActivityPic, // 红包背景图
      smallRedpacketBackground: shareActivityFloatingPic // 侧边红包悬浮图
    })
    // 支付成功后更新用户数据
    this.refreshUserStatus()
    // 上报本次支付成功的设备信息
    updateUserDeviceInfo()
    // 上报埋点 
    // turingReport.then( report => {
    //   report.reportOrderDeviceFn({
    //     orderNo: subOrderNo,
    //     sceme: 1,
    //     orderType: options.isTimelyOrder === '1' ? '10' : '130',
    //   })
    // })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    //  上报页面浏览事件
    sensors.pageScreenView()
  },

  refreshUserStatus () {
    // 只在开通心享的时候请求
    if (!this._data.selectMonthCard) return
    setTimeout(() => {
      updateVipStatus()
    }, 2000);
  },

  // 业务不想要这个数据了，先注释掉
  // async getSupCategoryInfo(goodsSnList) {
  //   const { data } = await app.api.getSPUERPCategory({ goodsSnList, storeCode: this.data.orderStoreCode }).catch(() => ({ data: {} }))
  //   this._data.CEMParameters.resolve(data)
  // },

  /**
   * 获取支付成功页信息
   */
  async getPaySuccessInfo(paySuccessflag) {
    try {
      const { userID } = wx.getStorageSync('user') || {}
      const { cityCode } = wx.getStorageSync('timelyCity') || {}
      const params = {
        cityCode,
        orderNo: this._data.orderNo,
        customerID: userID || -1
      }
      const res = await app.api.getPaySuccessInfoCombine(params)
      // const { orderInfo, existReparationActivity, vipFree, goodsSnList } = res.data || {}
      const { orderInfo, existReparationActivity, vipFree, CEMParameters } = res.data || {}
      // this.getSupCategoryInfo(goodsSnList)
      this._data.CEMParameters && this._data.CEMParameters.resolve(CEMParameters)
      // 替换desc里面的换行符
      if(orderInfo && orderInfo.desc){
        Object.assign(orderInfo,{
          desc:orderInfo.desc.replace(/\n/g,'')
        })
      }
      this.setData({
        // 在确认订单页开通月卡不显示心享提示
        saveMoney: this._data.selectMonthCard ? -1 : (vipFree || 0),
        orderInfo: orderInfo || {},
        existReparationActivity,
      })
      paySuccessflag && this.getManagerCodeByStoreCode()
      const { pickUpCode = '' } = orderInfo || {}
      if (pickUpCode.replace('-', '') !== '') {
        this.getBarcodeImg(pickUpCode)
      }
    } catch (error) {
      app.apiErrorDialog(error)
    }
  },

  /**
   * 获取条形码图片
   */
  getBarcodeImg(pickUpCode) {
    const that = this
    let failAgainRequest = 5
    const getCanvasImage = () => {
      wxbarcode.then((module) => module.barcode('barcode', pickUpCode, 600, 175, () => {
        setTimeout(() => {
          wx.canvasToTempFilePath({
            canvasId: 'barcode',
            success: (res) => {
              that.setData({
                barcodeImgSrc: res.tempFilePath
              })
            },
            fail: () => {
              // 华为手机偶现条形码加载失败，如果失败，重新绘制
              if (failAgainRequest > 0) {
                failAgainRequest--
                getCanvasImage()
              }
            }
          }, this)
        }, 1000)
      }));
    }
    getCanvasImage()
  },

  /**
   * 获取广告弹窗/红包弹窗
   */
  async paySuccessPopup() {
    try {
      const { userID } = wx.getStorageSync('user') || {}
      const { cityID, storeID } = wx.getStorageSync('timelyCity') || {}
      if (!cityID) {
        this.CEMResolve()
        return
      }
      const params = {
        cityID,
        storeID: storeID || -1,
        customerID: userID || -1,
        orderNo: this._data.orderNo
      }
      const res = await app.api.paySuccessPopup(params)
      console.log('paySuccessPopup res', res)
      const { advertiseInfo, redEnvelopeInfo } = res.data || {}
      const activityType = advertiseInfo && !!Object.keys(advertiseInfo).length ? 1 : redEnvelopeInfo && !!Object.keys(redEnvelopeInfo).length ? 2 : 0 // 【1：广告弹窗， 2：红包弹窗】 (优先展示广告弹窗，若广告弹窗不存在，再判断红包弹窗是否显示)
      if (!activityType) {
        this.CEMResolve()
        return
      }
      this.setData({
        activityType,
        advertiseInfo,
        redEnvelopeInfo
      })
      this.openActivityLayer()
    } catch (error) {
      this.CEMResolve()
      app.apiErrorDialog(error)
    }
  },

  /**
   * 点击广告弹窗跳转
   */
  navigateToActivity (e) {
    const { advertise } = e.currentTarget.dataset
    activity.toActivityPage(advertise)
    const { bannerID, bannerName } = advertise
    sensors.trackClickEvent({
      element_code: '130403002',
      element_name: '打开广告',
      element_id: '002',
      element_content: '打开广告',
      screen_code: '1304',
      banner_id: bannerID,
      banner_name: bannerName,
    })
  },

  // 关闭弹窗（广告弹窗、红包弹窗）
  closeActivityLayer (e) {
    this.CEMResolve()
    const { activityType } = this.data

    this.setData({
      isShowActivityLayer: false
    })
    // 若关闭红包弹窗，则打开侧边红包
    e && e.type === 'closeLayer' && activityType === 2 && this.openSmallHB ()
    activityType === 1 && sensors.trackClickEvent({
      element_code: '130403003',
      element_name: '关闭广告',
      element_id: '003',
      element_content: '关闭广告',
      screen_code: '1304'
    })
  },

  // 打开弹窗（广告弹窗、红包弹窗）
  openActivityLayer () {
    this.setData({
      isShowActivityLayer: true
    })
    this.data.activityType === 1 && sensors.trackClickEvent({
      element_code: '130403001',
      element_name: '弹出广告',
      element_id: '001',
      element_content: '弹出广告',
      screen_code: '1304'
    })
  },

  // 打开红包弹窗
  showHongBao() {
    this.openActivityLayer()
    this.closeSmallHB()
  },

  // 打开侧边红包
  openSmallHB () {
    this.setData({
      havaSmallHB: true
    })
  },

  // 关闭侧边红包
  closeSmallHB () {
    this.setData({
      havaSmallHB: false
    })
  },

  /**
   * 查看订单，混合单跳转订单列表，其余跳转订单详情
   */
  navigateAllOrderDetail() {
    const { orderType: { desc = '' } = {}, hasInTime = false, mixOrderTypeList = [] } = this.data.orderInfo || {}
    if (desc === '混合单') {
      // 区分是不同发货时间的b2c订单的混合还是b2c与及时达的混合
      // 新订单：hasInTime为true说明有及时达
      // 老订单：mixOrderTypeList > 1 b2c与及时达的混合
      // if (!mixOrderTypeList || mixOrderTypeList.length > 1) {
      const hasTimely = hasInTime ? hasInTime : (!mixOrderTypeList || mixOrderTypeList.length > 1)
      if (hasTimely) {
        wx.navigateTo({
          url: '/userB/pages/orderList/index'
        })
      } else {
        // 不同发货时间的b2c订单的混合
        wx.navigateTo({
          url: '/userB/pages/orderList/index?type=F'
        })
      }
    } else {
      // 有月卡的情况不区分，直接跳订单列表
      const isMixOrder = this._data.isMix === 'true'
      isMixOrder ? wx.navigateTo({
        url: '/userB/pages/orderList/index'
      }) :
      wx.navigateTo({
        url: `/userB/pages/allOrderDetail/index?myAllOrderObj=${JSON.stringify({
          goodsOrderID: this._data.subOrderNo,
        })}`
      })
    }
    if (app.globalData.reportSensors) {
      app.sensors.track("MPClick", {
        element_code: 130400002,
        element_name: '查看订单',
        element_id: '',
        element_content: '查看订单',
        screen_code:'1304',
      })
    }
  },

  // 返回首页
  navigateHome() {
    if (app.globalData.reportSensors) {
      app.sensors.track("MPClick", {
        element_code: 130400001,
        element_name: '回首页',
        element_id: '',
        element_content: '回首页',
        screen_code:'1304',
      })
    }
    app.getCartCount()
    wx.switchTab({
      url: '/pages/homeDelivery/index',
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (e) {
    const { storeID = 0, storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
    if (e.from === 'button') {
      if (app.globalData.reportSensors) {
        app.sensors.track("MPClick", {
          element_code: 130400005,
          element_name: '发红包',
          element_id: '',
          element_content: '发红包',
          screen_code:'1304',
        })
      }

      const url = e.target.dataset.shareurl
      const title = e.target.dataset.sharetitle
      const params = url.split('&')
      const querys = {}
      for (let i = 0; i < params.length; i++) {
        const arr = params[i].split('=')
        querys[arr[0]] = arr[1]
      }
      // title = querys.title.split('】')[0] + '+】' + querys.title.split('】')[1]

      if (app.globalData.reportSensors) {
        app.sensors.track("MPClick", {
          element_code: 130401001,
          element_name: '发红包',
          element_id: '',
          element_content: '发红包',
          screen_code:'1304',
        })
      }
      wx.reportAnalytics('paysuc_invitefriendssuc')
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/homeDelivery/index',
        })
      }, 500)

      if (app.globalData.reportSensors) {
        app.sensors.track('MPShare', {
          mp_shareTitle: title,
          activity_ID: 0,
          activity_Name: '',
          groupID: 0,
          groupSize: '',
          openerID: '',
          currentCount: '',
          storeID,
          storeName,
          storeNum: storeInfo.storeCode || ''
        })
      }

      return {
        title: querys.title,
        path: `/h5/pages/hongbao/index?paySuccessObj=${JSON.stringify({ url: querys.link, title: querys.title })}`,
        imageUrl: `${querys.picture}`
      }
    } else {
      // let shareObj = {
      //   mp_shareTitle: '拼的是好吃，团的是实惠',
      //   activity_ID: 0,
      //   activity_Name: '',
      //   groupID: 0,
      //   groupSize: "",
      //   openerID: "",
      //   currentCount: "",
      // }

      return {
        title: '全球好水果 直供百果园',
        path: `/pages/homeDelivery/index`,
        imageUrl: 'https://resource.pagoda.com.cn/dsxcx/images/f569db27ac02b34b9e040ccb05893424.png'
      }
    }
  },

  redirectToDownLoadH5(){
    const pageUrl = 'http://mp.weixin.qq.com/s?__biz=MjM5ODAwMTYwMA==&mid=521139153&idx=1&sn=375d762d3186d7596f297023a08d813b&chksm=3c2a3d7e0b5db468c113e25d4421397dd4f5f5bc810e024e8bc5dcc478ace022c9df3aafbb35#rd'
    wx.redirectTo({
      url: '/h5/pages/commonLink/index?pageUrl=' + encodeURIComponent(pageUrl),
    })
  },

  // banner点击事件
  handleBannerTap: throttle(function () {
    activity.toActivityPage(this.data.bannerOptions)
  }),

  // 获取banner
  async getBanner () {
    const { userID: customerID = -1} = wx.getStorageSync('user') || {}
    const { cityID, storeID = -1} = wx.getStorageSync('timelyCity') || {}
    if (!cityID) return
    const params = {
      cityID,
      storeID,
      customerID
    }
    try {
      const res = await app.api.getPaySuccessBannerList(params)
      const bannerOptions = res.data
      bannerOptions && this.setData({bannerOptions})
    } catch (e) {
      conFnService.failFunction()
    }
  },

  // 设置推荐商品
  async setRecommendGoods () {
    try {
      // 先取次日达定位城市，没有则取及时达定位城市
      // let finalCityID
      // const { selectAddressInfo = {} } = wx.getStorageSync('bgxxSelectLocateInfo') || {}
      // if (selectAddressInfo && selectAddressInfo.cityID) {
      //   finalCityID = selectAddressInfo.cityID
      // }
      const goodsList = await getRecommendGoods(this._data.freshGoodsObj) || []
      this.setData({
        goodsList,
        showLikeGoods: true,
        cartVisible: goodsList.length > 0,
      })
      if (goodsList.length) {
        this.setCartBusPos()
      }
    } catch (error) {
      this.setData({
        showLikeGoods: true
      })
    }
  },
  /**
   * 获取企微二维码
   */
  async getManagerCodeByStoreCode(){
    console.log('获取门店二维码')
    const {orderStoreCode} = this.data
    const result = await app.api.getManagerCodeByStoreCode({storeCode:orderStoreCode})
    const { qr_code = '',lineText} =  result.data
    this.setData({
      qrCode:qr_code,
      lineText
    })
  },
  callStore () {
    wx.makePhoneCall({
      phoneNumber: this.data.storePhone
    })
    // 联系门店埋点
    sensors.trackClickEvent({
      element_code: '130400006',
      element_name: '联系门店',
      element_id: '006',
      element_content: '联系门店',
      screen_code: '1304'
    })
  },
  // 子组件事件：获取自定义导航栏高度
  getNavBarHeight(ev) {
    // 这里的高度单位是px
    this.setData({
      navBarHeight: ev.detail,
    });
  },
  onPageScroll: function (ev) {
    this.setNavBarStyle(ev);
  },
  setNavBarStyle(ev) {
    const { scrollTop } = ev;
    const { navBarHeight } = this.data;
    const ratio = Math.min(scrollTop / (navBarHeight / 2), 1);
    if (this.data.ratio === ratio) return;
    if (ratio <= 0) {
      // 没有滚动距离
      this.setData({
        ratio,
        navBarBgColor: '#28BC4F',
        navBarColor: '#fff',
        backFilter: 1,
      });
    } else {
      this.setData({
        ratio,
        navBarBgColor: `rgba(255,255,255, ${ratio})`,
        navBarColor: '#222222',
        backFilter: 1 - ratio,
      });
    }
  },
  //自定义左上角返回
  back() {
    const pages = getCurrentPages();
    const lastPage = pages[pages.length - 2] || '';
    if (lastPage) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/homeDelivery/index',
      });
    }
  },
  toSubShopCart,
  async CEMResolve() {
    if (!this._data.ecmInit.promise) { return }
    const [done, CEMParameters] = await Promise.all([
      this._data.ecmInit.promise,
      this._data.CEMParameters.promise,
    ])
    const cemParams = this.data.cemParams
    this.setData({
      'cemParams': Object.assign(cemParams, CEMParameters)
    }, () => {
      done({ erpAreaInfoPreset: this._data.ecmInit.erpAreaInfoPreset })
    })
  },
  onECMInit({ detail: { done } }) {
    this._data.ecmInit.resolve && this._data.ecmInit.resolve(done)
  },
})
