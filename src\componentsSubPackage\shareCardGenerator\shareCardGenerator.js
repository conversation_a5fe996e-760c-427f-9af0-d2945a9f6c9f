import util from '~/utils/util'
import { shareImage } from './const'

const app = getApp()

Component({
  properties: {
    // 业务类型：delivery(及时达)、nextDay(次日达)、b2c(全国送)
    bizType: {
      type: String,
      value: 'delivery'
    },
    // 价格类型：normal(普通)、special(特价)、newUser(新人特价)
    priceType: {
      type: String,
      value: 'normal'
    },
    // 自定义背景图 如果有此背景图 则会进行自定义渲染模式
    customBgImg: {
      type: String,
      value: ''
    },
    // 商品信息
    // {String} goodsImage - 商品图片
    // {String} storeName - 店铺名称
    // {String} memberPrice - 商品价格
    // {String} retailPrice - 划线价格
    // {String} activityPrice - 活动价格
    goodsInfo: {
      type: Object,
      value: {},
      observer() {
        this.init()
      }
    },
    // 画布宽度
    width: {
      type: Number,
      value: 496,
      observer(newVal) {
        if (newVal) {
          this.init()
        }
      }
    },
    // 画布高度
    height: {
      type: Number,
      value: 392,
      observer(newVal) {
        if (newVal) {
          this.init()
        }
      }
    },
    // 样式配置
    styleConfig: {
      type: Object,
      value: {},
      observer() {
        this.init()
      }
    }
  },

  data: {
    shareImage: '',
    pixelRatio: 1,
    // dpr计算后的实际画布宽度
    canvasWidth: 496,
    // dpr计算后的实际画布高度
    canvasHeight: 392,
    // 默认样式配置
    defaultStyleInfo: {
      // 业务类型区域
      delivery: {
        width: 142,
        height: 52,
        lineHeight: 1.2,
        fontSize: 28,
        color: '#FFFFFF',
        borderRadius: 8,
        normal: {
          backGroundImage: shareImage.deleveryStandard,
          backgroundColor: '#33CA75',
          text: '及时达'
        },
        special: {
          backGroundImage: shareImage.deleverySpecial,
          backgroundColor: '#F14337',
          text: '线上特价'
        },
        newUser: {
          backGroundImage: shareImage.deleveryNewUser,
          backgroundColor: '#FFA944',
          text: '新人价'
        }
      },
      // 次日达
      nextDay: {
        backGroundImage: shareImage.nextDay,
        width: 142,
        height: 52,
        lineHeight: 1.2,
        fontSize: 28,
        color: '#FFFFFF',
        backgroundColor: '#FF7387',
        borderRadius: 8,
        text: '次日达'
      },
      // 全国送
      b2c: {
        backGroundImage: shareImage.b2c,
        width: 142,
        height: 52,
        lineHeight: 1.2,
        fontSize: 28,
        color: '#FFFFFF',
        backgroundColor: '#FFA006',
        borderRadius: 8,
        text: '全国送'
      },
      // 商品图片
      goodsImg: {
        // 默认宽高
        width: 310,
        height: 310,
        // 及时达
        delivery: {
          width: 276,
          height: 276
        }
      },
      // 店铺名称
      storeName: {
        fontSize: 20,
        height: 34,
        color: '#06B24C'
      },
      // 价格样式
      price: {
        normal: {
          currencyHeight: 72,
          priceHeight: 78,
          priceTypeHeight: 66,
          fontSize: 48,
          color: '#FFFFFF',
          typeText: '果粉价'
        },
        special: {
          currencyHeight: 72,
          priceHeight: 78,
          priceTypeHeight: 66,
          fontSize: 48,
          color: '#fff',
          tagColor: 'rgba(255, 255, 255, 0.5)',
          retailPrice: '¥0',
          typeText: '¥0'
        },
        newUser: {
          currencyHeight: 72,
          priceHeight: 78,
          priceTypeHeight: 66,
          fontSize: 48,
          color: '#fff',
          tagColor: 'rgba(255, 255, 255, 0.5)',
          retailPrice: '¥0',
          typeText: '¥0'
        }
      }
    }
  },

  lifetimes: {
    attached() {
      this.init()
    }
  },

  methods: {
    /**
     * 初始化组件
     * 获取系统信息并设置画布尺寸和像素比
     *
     * @returns {Promise} 返回一个Promise对象，在初始化完成后resolve
     */
    init() {
      const initStartTime = Date.now()
      console.log(`[ShareCardGenerator Performance] 开始初始化组件`)

      return new Promise(resolve => {
        wx.getSystemInfo({
          success: res => {
            // 限制像素比例不超过2，避免过高的像素比影响性能
            const limitedPixelRatio = Math.min(res.pixelRatio, 2)
            const canvasWidth = this.properties.width * limitedPixelRatio
            const canvasHeight = this.properties.height * limitedPixelRatio

            console.log(`[ShareCardGenerator Performance] 系统信息获取完成 - 像素比: ${res.pixelRatio} -> ${limitedPixelRatio}, 画布尺寸: ${canvasWidth}x${canvasHeight}`)

            this.setData({
              pixelRatio: limitedPixelRatio,
              canvasWidth: canvasWidth,
              canvasHeight: canvasHeight
            })

            const initEndTime = Date.now()
            console.log(`[ShareCardGenerator Performance] 组件初始化完成 - 耗时: ${initEndTime - initStartTime}ms`)
            resolve()
          },
          fail: err => {
            console.error(`[ShareCardGenerator Performance] 获取系统信息失败 - 耗时: ${Date.now() - initStartTime}ms`, err)
            // 使用默认值
            this.setData({
              pixelRatio: 2,
              canvasWidth: this.properties.width * 2,
              canvasHeight: this.properties.height * 2
            })
            resolve()
          }
        })
      })
    },

    /**
     * 根据像素比例计算实际像素值
     *
     * @param {Number} px - 原始像素值
     * @returns {Number} 根据设备像素比计算后的实际像素值
     */
    getRatioPx(px) {
      return px * this.data.pixelRatio
    },

    /**
     * 计算文字宽度
     * 根据不同字符类型（中文、数字、符号等）计算文本的实际宽度
     *
     * @param {String} text - 需要计算宽度的文本
     * @param {Number} fontSize - 字体大小（单位：px）
     * @returns {Number} 文本的实际宽度（已应用像素比）
     */
    getTextWidth(text, fontSize) {
      text = String(text)
      // 中文字符平均宽度约为fontSize，英文和数字约为fontSize/2
      let width = 0
      // 匹配中文字符
      const chineseRegex = /[\u4e00-\u9fa5]|（|）/
      // 匹配数字
      const numRegex = /[0-9]/
      // 匹配.
      const dotRegex = /\./

      for (let i = 0; i < text.length; i++) {
        const char = text.charAt(i)
        if (chineseRegex.test(char)) {
          // 中文字符
          width += fontSize
        } else if (numRegex.test(char)) {
          // 数字
          width += fontSize * 0.66
        } else if (dotRegex.test(char)) {
          // 其他字符（英文、符号等）
          width += fontSize * 0.2
        } else {
          // 其他字符（英文、符号等）
          width += fontSize * 0.5
        }
      }

      return this.getRatioPx(width)
    },

    /**
     * 生成分享图片
     * 根据当前的业务类型、价格类型和商品信息生成分享卡片图片
     *
     * @returns {void} 无返回值，生成完成后会触发generated事件，失败则触发error事件
     */
    generateShareCard() {
      const startTime = Date.now()
      const { goodsSn } = this.data.goodsInfo
      console.log(`[ShareCardGenerator Performance] 开始生成分享图片 - goodsSn: ${goodsSn}, 时间: ${startTime}`)

      // 获取wxml-to-canvas组件实例
      const componentStartTime = Date.now()
      const widget = this.selectComponent('.canvas')
      if (!widget) {
        console.error(`[ShareCardGenerator Performance] 未找到canvas组件 - 耗时: ${Date.now() - startTime}ms`)
        this.triggerEvent('error', { error: new Error('Canvas组件未找到') })
        return
      }
      console.log(`[ShareCardGenerator Performance] 获取canvas组件完成 - 耗时: ${Date.now() - componentStartTime}ms`)

      // 合并样式配置
      const styleStartTime = Date.now()
      const mergedStyleInfo = this.getMergedStyleInfo()
      console.log(`[ShareCardGenerator Performance] 样式配置合并完成 - 耗时: ${Date.now() - styleStartTime}ms`)

      // 根据业务类型和价格类型生成不同的WXML和样式
      const templateStartTime = Date.now()
      const { wxml, style } = this.generateTemplate(mergedStyleInfo)
      console.log(`[ShareCardGenerator Performance] 模板生成完成 - 耗时: ${Date.now() - templateStartTime}ms`)

      // 渲染WXML到canvas
      const renderStartTime = Date.now()
      console.log(`[ShareCardGenerator Performance] 开始渲染到Canvas`)

      widget
        .renderToCanvas({
          wxml: wxml,
          style: style
        })
        .then(() => {
          const renderEndTime = Date.now()
          console.log(`[ShareCardGenerator Performance] Canvas渲染完成 - 耗时: ${renderEndTime - renderStartTime}ms`)

          // 导出图片
          const exportStartTime = Date.now()
          console.log(`[ShareCardGenerator Performance] 开始导出图片`)

          widget
            .canvasToTempFilePath({
              destWidth: this.data.canvasWidth,
              destHeight: this.data.canvasHeight,
              fileType: 'jpg', // 优化：使用jpg格式减少文件大小
              quality: 0.8 // 优化：降低质量以提升性能
            })
            .then(res => {
              const exportEndTime = Date.now()
              const totalTime = exportEndTime - startTime
              console.log(`[ShareCardGenerator Performance] 图片导出完成 - 导出耗时: ${exportEndTime - exportStartTime}ms`)
              console.log(`[ShareCardGenerator Performance] 分享图片生成完成 - 总耗时: ${totalTime}ms`)

              this.setData({
                shareImage: res.tempFilePath
              })
              // 触发生成完成事件
              this.triggerEvent('generated', { shareImage: res.tempFilePath, goodsSn })
            })
            .catch(err => {
              console.error(`[ShareCardGenerator Performance] 导出图片失败 - 耗时: ${Date.now() - exportStartTime}ms`, err)
              this.triggerEvent('error', { error: err })
            })
        })
        .catch(err => {
          console.error(`[ShareCardGenerator Performance] 渲染失败 - 耗时: ${Date.now() - renderStartTime}ms`, err)
          this.triggerEvent('error', { error: err })
        })
    },

    /**
     * 合并样式配置
     * 将默认样式配置与用户自定义样式配置合并
     *
     * @returns {Object} 合并后的样式配置对象，包含以下属性：
     *   - bizType {String} 业务类型，如：'delivery'、'nextDay'、'b2c'
     *   - priceType {String} 价格类型 如：'normal'、'special'、'newUser'
     *   - bizStyle {Object} 业务类型对应的样式
     *   - priceStyle {Object} 价格类型对应的样式
     *   - storeStyle {Object} 店铺名称样式
     *   - goodsImgStyle {Object} 商品图片样式
     */
    getMergedStyleInfo() {
      const mergeStartTime = Date.now()
      const { bizType, priceType, styleConfig } = this.properties
      const { defaultStyleInfo } = this.data

      // 优化：使用浅拷贝代替深拷贝，提升性能
      const mergedStyle = { ...defaultStyleInfo }

      // 深拷贝嵌套对象
      Object.keys(defaultStyleInfo).forEach(key => {
        if (typeof defaultStyleInfo[key] === 'object' && defaultStyleInfo[key] !== null) {
          mergedStyle[key] = { ...defaultStyleInfo[key] }
          // 处理更深层的嵌套
          Object.keys(defaultStyleInfo[key]).forEach(subKey => {
            if (typeof defaultStyleInfo[key][subKey] === 'object' && defaultStyleInfo[key][subKey] !== null) {
              mergedStyle[key][subKey] = { ...defaultStyleInfo[key][subKey] }
            }
          })
        }
      })

      // 合并用户自定义样式
      if (styleConfig && Object.keys(styleConfig).length > 0) {
        const customMergeStartTime = Date.now()
        Object.keys(styleConfig).forEach(key => {
          if (mergedStyle[key]) {
            if (typeof mergedStyle[key] === 'object' && !Array.isArray(mergedStyle[key])) {
              mergedStyle[key] = { ...mergedStyle[key], ...styleConfig[key] }
            } else {
              mergedStyle[key] = styleConfig[key]
            }
          }
        })
        console.log(`[ShareCardGenerator Performance] 自定义样式合并完成 - 耗时: ${Date.now() - customMergeStartTime}ms`)
      }

      const result = {
        bizType,
        priceType,
        bizStyle: mergedStyle[bizType] || mergedStyle.delivery,
        priceStyle: mergedStyle.price[priceType] || mergedStyle.price.normal,
        storeStyle: mergedStyle.storeName,
        goodsImgStyle: mergedStyle.goodsImg
      }

      console.log(`[ShareCardGenerator Performance] 样式配置合并完成 - 总耗时: ${Date.now() - mergeStartTime}ms`)
      return result
    },

    /**
     * 根据业务类型和价格类型生成模板
     * 生成分享卡片的WXML结构和样式
     *
     * @param {Object} styleInfo - 样式信息对象，包含以下属性：
     *   - bizType {String} 业务类型
     *   - priceType {String} 价格类型
     *   - bizStyle {Object} 业务类型对应的样式
     *   - priceStyle {Object} 价格类型对应的样式
     *   - storeStyle {Object} 店铺名称样式
     *   - goodsImgStyle {Object} 商品图片样式
     * @returns {Object} 包含wxml和style的对象
     *   - wxml {String} 分享卡片的WXML结构
     *   - style {Object} 分享卡片的样式对象
     */
    generateTemplate(styleInfo) {
      const templateStartTime = Date.now()
      const { bizType, priceType, bizStyle, priceStyle, storeStyle, goodsImgStyle } = styleInfo
      const { customBgImg, goodsInfo } = this.properties
      const { canvasWidth, canvasHeight } = this.data

      console.log(`[ShareCardGenerator Performance] 开始生成模板 - bizType: ${bizType}, priceType: ${priceType}, 自定义背景: ${!!customBgImg}`)

      let wxml = ''
      if (customBgImg && bizType === 'delivery') {
        // 如果有自定义背景图 则使用自定义背景图
        const customTemplateStartTime = Date.now()
        const result = this.getCustomTemplate(bizType, priceType, priceStyle)
        console.log(`[ShareCardGenerator Performance] 自定义模板生成完成 - 耗时: ${Date.now() - customTemplateStartTime}ms`)
        return result
      }
      wxml = `
        <view class="share-card">
          ${this.getBizBGTemplate(bizType, priceType, bizStyle)}
          <view class="image-box">
            <image class="goods-image" src="${goodsInfo.headPic}"></image>
          </view>
          <view class="biz-box">
            <view class="biz-back"></view>
            <view class="biz-left-back"></view>
            ${this.getBizNameTemplate(bizType, priceType, bizStyle)}
          </view>
          ${this.getStoreTemplate(bizType, this.getStoreName(goodsInfo.storeName))}
          ${this.getPriceTemplate(bizType, priceType, goodsInfo)}
        </view>
      `
      // 如果是及时达 需要获取各个业务类型下的背景色
      const bizBackGroundColor =
        bizType === 'delivery' ? bizStyle[priceType].backgroundColor : bizStyle.backgroundColor
      const goodsImgConfig = bizType === 'delivery' ? goodsImgStyle[bizType] : goodsImgStyle
      // 构建样式
      const style = {
        // 外层卡片样式
        shareCard: {
          width: canvasWidth,
          height: canvasHeight,
          position: 'relative'
        },
        // 背景图样式
        bgImage: {
          position: 'absolute',
          top: '0',
          left: '0',
          width: canvasWidth,
          height: canvasHeight,
          zIndex: 2
        },
        // 业务类型样式
        bizBox: {
          position: 'absolute',
          top: '0',
          left: '0',
          width: this.getRatioPx(bizStyle.width),
          height: this.getRatioPx(bizStyle.height),
          borderRadius: this.getRatioPx(bizStyle.borderRadius),
          textAlign: 'center',
          backgroundColor: bizBackGroundColor
        },
        // 业务类型左侧需要遮住圆角的样式
        bizLeftBack: {
          position: 'absolute',
          top: 0,
          width: this.getRatioPx(10),
          height: this.getRatioPx(bizStyle.height),
          backgroundColor: bizBackGroundColor
        },
        // 业务类型背景样式，目的是为了遮住商品头图
        bizBack: {
          width: this.getRatioPx(bizStyle.width),
          height: this.getRatioPx(10),
          backgroundColor: bizBackGroundColor
        },
        // 业务类型文字样式
        bizType: {
          width: this.getRatioPx(bizStyle.width),
          height: this.getRatioPx(bizStyle.height),
          fontSize: this.getRatioPx(bizStyle.fontSize),
          lineHeight: bizStyle.lineHeight + 'em',
          color: bizStyle.color
        },
        // 商品头图容器样式
        imageBox: {
          width: canvasWidth,
          height: canvasHeight,
          alignItems: 'center'
        },
        // 商品头图样式
        goodsImage: {
          width: this.getRatioPx(goodsImgConfig.width),
          height: this.getRatioPx(goodsImgConfig.height),
          textAlign: 'center'
        },
        // 店铺名称容器样式
        store: {
          position: 'absolute',
          left: 0,
          bottom: this.getRatioPx(78)
        },
        // 店铺名称样式
        storeName: {
          width: canvasWidth,
          height: this.getRatioPx(storeStyle.height),
          fontSize: this.getRatioPx(storeStyle.fontSize),
          color: storeStyle.color,
          textAlign: 'center'
        },

        // 添加价格相关样式
        ...this.getPriceStyle(bizType, priceType, priceStyle, goodsInfo)
      }

      const templateEndTime = Date.now()
      console.log(`[ShareCardGenerator Performance] 默认模板生成完成 - 总耗时: ${templateEndTime - templateStartTime}ms`)
      return { wxml, style }
    },

    /**
     * 自定义封面获取模板
     * @param {String} bizType - 业务类型：delivery(及时达)、nextDay(次日达)、b2c(全国送)
     * @param {String} priceType - 价格类型：normal(普通)、special(特价)、newUser(新人特价)
     * @param {Object} priceStyle - 格类型对应的样式
     * @returns {Object} 包含wxml和style的对象
     *   - wxml {String} 分享卡片的WXML结构
     *   - style {Object} 分享卡片的样式对象
     */
    getCustomTemplate(bizType, priceType, priceStyle) {
      const customTemplateStartTime = Date.now()
      console.log(`[ShareCardGenerator Performance] 开始生成自定义模板`)

      const { customBgImg, goodsInfo } = this.properties
      const { canvasWidth, canvasHeight } = this.data

      const priceStartTime = Date.now()
      const { firstPrice } = this.getPrice(bizType, priceType, goodsInfo)
      console.log(`[ShareCardGenerator Performance] 价格计算完成 - 耗时: ${Date.now() - priceStartTime}ms`)
      const wxml = `
        <view class="share-card">
          <image class="bg-image" src="${customBgImg}"></image>
          <view class="custom-price-group">
            <view class="price">
              <text class="price-space"></text>
              <text class="price-currency">¥</text>
              <text class="price-text">${firstPrice}</text>
            </view>
          </view>
          ${
            this.getStoreName(goodsInfo.storeName) ? 
          `<view class="location-group">
            <text class="location-space"></text>
            <image class="location-image" src="https://resource.pagoda.com.cn/dsxcx/images/e83c8b03685d45ed6cac635ca0cf996e.png"></image>
            <text class="location-name">${this.getStoreName(goodsInfo.storeName)}</text>
          </view>`
            : ''
          }
        </view>
      `
      const style = {
        // 外层卡片样式
        shareCard: {
          width: canvasWidth,
          height: canvasHeight,
          position: 'relative'
        },
        // 背景图样式
        bgImage: {
          position: 'absolute',
          top: '0',
          left: '0',
          width: canvasWidth,
          height: canvasHeight,
          zIndex: 2
        },
        // 价格容器样式
        customPriceGroup: {
          position: 'absolute',
          left: 0,
          bottom: this.getRatioPx(34),
          width: this.data.canvasWidth,
          height: this.getRatioPx(78),
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between'
        },
        // 左侧价格样式
        priceSpace: {
          width: this.getRatioPx(28),
          height: this.getRatioPx(78)
        },
        // 价格样式
        price: {
          width: (this.data.canvasWidth / 3) * 2,
          textAlign: 'left',
          flexDirection: 'row',
          verticalAlign: 'bottom'
        },
        // 价格符号样式
        priceCurrency: {
          width: this.getRatioPx(20),
          height: this.getRatioPx(priceStyle.currencyHeight),
          fontSize: this.getRatioPx(32),
          color: priceStyle.color
        },
        // 价格文字样式
        priceText: {
          width: this.getTextWidth(firstPrice, priceStyle.fontSize),
          height: this.getRatioPx(priceStyle.priceHeight),
          fontSize: this.getRatioPx(priceStyle.fontSize),
          color: priceStyle.color
        },
        locationGroup: {
          position: 'absolute',
          left: 0,
          bottom: this.getRatioPx(0),
          width: this.data.canvasWidth,
          height: this.getRatioPx(35),
          flexDirection: 'row',
          alignItems: 'center',
          verticalAlign: 'middle',
          backgroundColor: 'rgba(0, 0, 0, .5)'
        },
        locationSpace: {
          width: this.getRatioPx(28),
          height: this.getRatioPx(21)
        },
        locationImage: {
          width: this.getRatioPx(17),
          height: this.getRatioPx(21)
        },
        locationName: {
          left: this.getRatioPx(10),
          width: this.getTextWidth(this.getStoreName(goodsInfo.storeName), 20),
          height: this.getRatioPx(35),
          fontSize: this.getRatioPx(20),
          color: '#fff'
        }
      }

      const customTemplateEndTime = Date.now()
      console.log(`[ShareCardGenerator Performance] 自定义模板生成完成 - 总耗时: ${customTemplateEndTime - customTemplateStartTime}ms`)

      return {
        wxml,
        style
      }
    },
    /**
     * 获取业务类型背景图模板
     * 根据业务类型生成不同的业务类型背景图模板
     *
     * @param {String} bizType - 业务类型：delivery(及时达)、nextDay(次日达)、b2c(全国送)
     * @param {String} priceType - 价格类型：normal(普通)、special(特价)、newUser(新人特价)
     */
    getBizBGTemplate(bizType, priceType) {
      switch (bizType) {
        case 'delivery':
          return `<image class="bg-image" src="${shareImage[bizType][priceType]}"></image>`
        case 'nextDay':
          return `<image class="bg-image" src="${shareImage[bizType]}"></image>`
        case 'b2c':
          return `<image class="bg-image" src="${shareImage[bizType]}"></image>`
        default:
          return '<image class="bg-image" src="${shareImage[bizType][priceType]}"></image>'
      }
    },
    /**
     * 获取业务类型名称模板
     * 根据业务类型和价格类型生成不同的业务类型名称模板
     *
     * @param {String} bizType - 业务类型：delivery(及时达)、nextDay(次日达)、b2c(全国送)
     * @param {String} priceType - 价格类型：normal(普通)、special(特价)、newUser(新人特价)
     * @returns {String} 业务类型名称模板
     */
    getBizNameTemplate(bizType, priceType, bizStyle) {
      return bizType === 'delivery'
        ? `<text class="biz-type">${bizStyle[priceType].text}</text>`
        : `<text class="biz-type">${bizStyle.text}</text>`
    },
    /** 设置门店名称 */
    getStoreTemplate(bizType, storeName) {
      const templateStoreName = this.getStoreName(storeName)
      return bizType === 'delivery' && templateStoreName
        ? `<view class="store">
        <text class="store-name">${templateStoreName}</text>
      </view>`
        : ''
    },
    /**
     * 获取价格模板
     * 根据价格类型生成不同的价格展示模板
     *
     * @param {String} bizType - 业务类型：delivery(及时达)、nextDay(次日达)、b2c(全国送)
     * @param {String} priceType - 价格类型：normal(普通)、special(特价)、newUser(新人特价)
     * @param {Object} goodsInfo - 商品信息对象，包含价格等数据
     * @returns {String} 价格部分的WXML模板
     */
    getPriceTemplate(bizType, priceType, goodsInfo) {
      // 价格描述模板
      const priceDesc = () => {
        if (bizType !== 'delivery') {
          const price = Number(goodsInfo.activityPrice || goodsInfo.memberPrice)
          if (isNaN(price) || price <= 0) {
            throw new Error(`[次日达或全国送分享卡片]商品价格错误: ${JSON.stringify(goodsInfo)}`)
          }
          return `<text class="price-text">${util.formatPrice(price)}</text>`
        }
        // 特价和新人价需要添加划线价
        if (priceType === 'special' || priceType === 'newUser') {
          if (isNaN(Number(goodsInfo.activityPrice)) || Number(goodsInfo.activityPrice) <= 0) {
            throw new Error(`[特价、新人特价]商品特价价格错误: ${JSON.stringify(goodsInfo)}`)
          }
          if (isNaN(Number(goodsInfo.retailPrice)) || Number(goodsInfo.retailPrice) <= 0) {
            throw new Error(`[特价、新人特价]商品价格错误: ${JSON.stringify(goodsInfo)}`)
          }
          return `
            <text class="price-text">${util.formatPrice(goodsInfo.activityPrice)}</text>
            <view class="retail-price">
              <text class="price-type">¥${util.formatPrice(goodsInfo.retailPrice)}</text>
              <view class="line"></view>
            </view>
          `
        } else {
          if (isNaN(Number(goodsInfo.memberPrice)) || Number(goodsInfo.memberPrice) <= 0) {
            throw new Error(`[及时达分享卡片]商品会员价错误: ${JSON.stringify(goodsInfo)}`)
          }
          if (isNaN(Number(goodsInfo.retailPrice)) || Number(goodsInfo.retailPrice) <= 0) {
            throw new Error(`[及时达分享卡片]商品零售价价格错误: ${JSON.stringify(goodsInfo)}`)
          }
          return `
            <text class="price-text">${util.formatPrice(goodsInfo.memberPrice)}</text>
            ${Number(goodsInfo.memberPrice) <= Number(goodsInfo.retailPrice) ? `<text class="price-type">果粉价</text>` : ''}`
        }
      }
      // 基础价格模板
      const basePriceTemplate = `
        <view class="price-group">
          <view class="price">
            <text class="price-space"></text>
            <text class="price-currency">¥</text>
            ${priceDesc()}
          </view>
          <view class="buy-now">
            <text class="buy-text">立即抢购</text>
          </view>
        </view>
      `

      return basePriceTemplate
    },

    /**
     * 获取价格样式
     * 根据价格类型生成不同的价格样式对象
     *
     * @param {String} bizType - 业务类型：delivery(及时达)、nextDay(次日达)、b2c(全国送)
     * @param {String} priceType - 价格类型：normal(普通)、special(特价)、newUser(新人特价)
     * @param {Object} priceStyle - 价格样式对象，包含以下属性：
     *   - currencyHeight {Number} 货币符号高度
     *   - priceHeight {Number} 价格文字高度
     *   - priceTypeHeight {Number} 价格类型文字高度
     *   - fontSize {Number} 价格字体大小
     *   - color {String} 价格文字颜色
     *   - typeText {String} 价格类型文本
     *   - tagColor {String} 标签背景颜色(仅特价和新人价有效)
     *   - retailPrice {String} 划线价文本(仅特价和新人价有效)
     * @param {Object} goodsInfo - 商品信息对象，包含价格等数据
     * @returns {Object} 价格相关的样式对象
     */
    getPriceStyle(bizType, priceType, priceStyle, goodsInfo) {
      const { firstPrice, secondPrice } = this.getPrice(bizType, priceType, goodsInfo)
      const baseStyle = {
        // 价格容器样式
        priceGroup: {
          position: 'absolute',
          left: 0,
          bottom: this.getRatioPx(0),
          width: this.data.canvasWidth,
          height: this.getRatioPx(78),
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between'
        },
        // 左侧价格样式
        priceSpace: {
          width: this.getRatioPx(20),
          height: this.getRatioPx(78)
        },
        // 价格样式
        price: {
          width: (this.data.canvasWidth / 3) * 2,
          textAlign: 'left',
          flexDirection: 'row',
          verticalAlign: 'bottom'
        },
        // 价格符号样式
        priceCurrency: {
          width: this.getRatioPx(20),
          height: this.getRatioPx(priceStyle.currencyHeight),
          fontSize: this.getRatioPx(32),
          color: priceStyle.color
        },
        // 价格文字样式
        priceText: {
          width: this.getTextWidth(firstPrice, priceStyle.fontSize),
          height: this.getRatioPx(priceStyle.priceHeight),
          fontSize: this.getRatioPx(priceStyle.fontSize),
          color: priceStyle.color
        },
        // 划线价格类型样式
        priceType: {
          width: this.getTextWidth(secondPrice, 28),
          height: this.getRatioPx(priceStyle.priceTypeHeight),
          fontSize: this.getRatioPx(28),
          color: priceStyle.color
        },
        // 立即抢购按钮样式
        buyNow: {
          justifyContent: 'flex-end',
          width: this.getRatioPx(156),
          height: this.getRatioPx(45),
          fontSize: this.getRatioPx(32),
          textAlign: 'center'
        },
        // 立即抢购按钮文字样式
        buyText: {
          width: this.getRatioPx(128),
          height: this.getRatioPx(45),
          fontSize: this.getRatioPx(32),
          color: '#FFFFFF'
        }
      }
      // 特价和新人价需要添加标签样式
      if (priceType === 'special' || priceType === 'newUser') {
        return {
          ...baseStyle,
          // 划线价格标签文字样式
          retailPrice: {
            position: 'relative',
            width: this.getTextWidth(secondPrice, 28),
            height: this.getRatioPx(priceStyle.priceTypeHeight),
            fontSize: this.getRatioPx(28),
            color: priceStyle.tagColor,
            textAlign: 'center'
          },
          line: {
            position: 'absolute',
            width: this.getTextWidth(secondPrice, 28) - this.getRatioPx(4),
            height: this.getRatioPx(1),
            backgroundColor: priceStyle.tagColor,
            bottom: this.getRatioPx(priceStyle.priceTypeHeight / 3 - 2),
            left: this.getRatioPx(2),
            zIndex: 1
          },
          // 价格标签样式
          priceType: {
            width: this.getTextWidth(secondPrice, 28),
            height: this.getRatioPx(priceStyle.priceTypeHeight),
            fontSize: this.getRatioPx(28),
            color: priceStyle.tagColor
          }
        }
      }
      return baseStyle
    },

    /**
     * 获取价格信息
     * @param {String} bizType - 业务类型：delivery(及时达)、nextDay(次日达)、b2c(全国送)
     * @param {String} priceType - 价格类型：normal(普通)、special(特价)、newUser(新人特价)
     * @param {Object} goodsInfo 商品信息
     * @returns 价格信息
     */
    getPrice(bizType, priceType, goodsInfo) {
      // 非及时达
      if (bizType !== 'delivery') {
        const price = Number(goodsInfo.activityPrice || goodsInfo.memberPrice)
        return {
          firstPrice: util.formatPrice(price),
          secondPrice: ''
        }
      }
      // 及时达特价和新人价需要添加划线价
      if (priceType === 'special' || priceType === 'newUser') {
        return {
          firstPrice: util.formatPrice(goodsInfo.activityPrice),
          secondPrice: '¥' + util.formatPrice(goodsInfo.retailPrice)
        }
      }
      // 及时达普通价
      return {
        firstPrice: util.formatPrice(goodsInfo.memberPrice),
        secondPrice: Number(goodsInfo.memberPrice) <= Number(goodsInfo.retailPrice) ? '果粉价' : ''
      }
    },

    /**
     * 保存图片到相册
     * 检查授权并将生成的分享图片保存到用户相册
     *
     * @returns {void} 无返回值，保存成功或失败会显示对应提示
     * @throws {Error} 可能抛出授权失败或保存失败的错误
     */
    saveImage() {
      const { shareImage } = this.data
      if (!shareImage) {
        wx.showToast({
          title: '图片生成中，请稍候',
          icon: 'none'
        })
        return
      }
      console.log('保存图片到相册中...')
      // 检查授权状态
      wx.getSetting({
        success: res => {
          if (res.authSetting['scope.writePhotosAlbum']) {
            this.saveImageToPhotosAlbum(shareImage)
          } else {
            wx.authorize({
              scope: 'scope.writePhotosAlbum',
              success: () => {
                this.saveImageToPhotosAlbum(shareImage)
              },
              fail: err => {
                if (err.errMsg.indexOf('auth deny') >= 0) {
                  wx.showModal({
                    title: '提示',
                    content: '需要您授权保存图片到相册',
                    showCancel: true,
                    cancelText: '取消',
                    confirmText: '去授权',
                    success: res => {
                      if (res.confirm) {
                        wx.openSetting()
                      }
                    }
                  })
                } else {
                  wx.showToast({
                    title: '保存失败',
                    icon: 'none'
                  })
                }
                console.error('授权失败', err)
              }
            })
          }
        },
        fail: err => {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          })
          console.error('获取设置失败', err)
        }
      })
    },

    /**
     * 将图片保存到相册
     * 调用微信API将临时文件路径的图片保存到用户相册中
     *
     * @param {String} filePath - 需要保存的图片临时路径
     * @returns {void} 无返回值，保存成功或失败会显示对应提示
     * @throws {Error} 可能抛出保存失败的错误
     */
    saveImageToPhotosAlbum(filePath) {
      wx.saveImageToPhotosAlbum({
        filePath: filePath,
        success: () => {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })
        },
        fail: err => {
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          })
          console.error('保存图片失败', err)
        }
      })
    },

    /**
     * 分享给朋友
     * 触发分享事件，将分享图片传递给父组件
     *
     * @returns {void} 无返回值，触发share事件并传递shareImage参数
     */
    shareToFriend() {
      // 触发分享事件
      this.triggerEvent('share', { shareImage: this.data.shareImage })
    },

    /**
     * 预览大图
     * 使用微信预览图片API预览生成的分享图片
     *
     * @returns {void} 无返回值，打开微信图片预览
     * @throws {Error} 当图片未生成完成时会提示用户等待
     */
    previewImage() {
      const { shareImage } = this.data
      if (!shareImage) {
        wx.showToast({
          title: '图片生成中，请稍候',
          icon: 'none'
        })
        return
      }
      wx.previewImage({
        current: shareImage, // 当前显示图片的链接
        urls: [shareImage] // 需要预览的图片链接列表
      })
    },
    /**
     * 获取门店名称
     */
    getStoreName(storeName) {
      const isDrawStore = this.checkIsCarryShareStore()
      if (isDrawStore) {
        return storeName
      }
      return ''
    },
    /**
     * 是否携带分享门店,携带isCarryStore=N进入小程序的，都不携带分享门店
     */
    checkIsCarryShareStore () {
      const { query } = app.globalData.globalLaunchOptions || {}
      const { isCarryStore = 'Y' } = query || {}
      return isCarryStore !== 'N'
    }
  }
})
