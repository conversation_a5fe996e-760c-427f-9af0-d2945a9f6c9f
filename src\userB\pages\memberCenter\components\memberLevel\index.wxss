.user-info, .name-level .level, .name-level .name, .name-level .record {
  display: flex;
  justify-content: flex-start;
}
.user-info {
  align-items: center;
}
.user-info-avatar {
  position: relative;
  height: 96rpx;
}
.user-info-avatar .user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
}
.record .super-vip-type {
  margin-right: 8rpx;
}
.name-level {
  margin-left: 20rpx;
}
.name-level .name {
  align-items: center;
  margin: 2rpx 0 6rpx 0;
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}
.name-level .name .user-name {
  margin-right: 8rpx;
}
.name-level .level view {
  width: 93rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
}
.name-level .level .level-name {
  width: 93rpx;
  height: 32rpx;
}
.name-level .level .group>view {
  position: relative;
}
.name-level .level .group .group-name {
  width: 63rpx;
  height: 22rpx;
  position: absolute;
  left: 35rpx;
  bottom: 26rpx;
}
.name-level .record {
  font-size: 24rpx;
  color: #fff;
  opacity: 0.7;
}
.no-login {
  margin-left: 20rpx;
  font-weight: bold;
  font-size: 32rpx;
  color: #fff;
}
.no-login view:last-child {
  margin-top: 16rpx;
  font-weight: 400;
  font-size: 24rpx;
}
.no-data-container {
  width: 320rpx;
  height: 96rpx;
}
.no-data-container image {
  width: 100%;
  height: 100%;
}
.level-border {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  padding: 2rpx 8rpx;
  font-size: 20rpx;
  font-weight: 400!important;
  opacity: 0.8;
  white-space: nowrap;
  transform: rotateZ(360deg);
}
.level-border:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  content: '';
  border: 2rpx solid;
  border-radius: 8rpx;
  transform: scale(0.5) rotateZ(360deg);
  transform-origin: top left;
  box-sizing: border-box;
  opacity: 0.5;
}