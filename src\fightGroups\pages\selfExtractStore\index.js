  var coordtransform = require('../../../utils/coordUtil')
import wxappMap from '../../../service/wxappMap'
var app = getApp()
const locateService = require('../../../utils/services/locate')
const sensors = require('../../../utils/report/sensors')
import { deepClone, getStoreBusinessTime } from '../../../utils/util'
import locateStore from '../../../stores/module/locate'
import { createStoreBindings } from 'mobx-miniprogram-bindings'
import { updateCollectStore } from '../../../service/userService'
import { boolean2YN } from '../../../utils/YNBoolean'
Page({
  data: {
    showSearchList: false, //地址联想列表
    hasSelectAddress: false, // 是否手动选择地址
    storeListBoxShowFlag: false, //提货门店列表
    noResultBoxShowFlag: false, //搜索结果为空
    fromPTorderDetail: false, //记录是从确认订单页过来
    timelyCity: '', //记录下提货门店
    curAddress: {}, // 记录当前选择的地址,包含地址名，经纬度
    storeList: [],
    frequentlyStoreList: [],
    cityID: '',
    locateCity: {},
    goodsID: '', // 商品id，从确认订单页过来才有该字段
    isHaveStore: true, // 有门店
    selectedStore: {}, // 选择的门店
    formPage: 'fightGroupsPage', // 页面入口： fightGroupsPage 拼团首页， fightGroupsConfirmOrder 拼团确认订单页 selfTakeConfirmOrder 及时达自提确认订单页
    pageOrgin: '', // 页面来源
    activityCode: '', // 用于门店的接龙
    storeCodes:[], // 门店编码列表
    noStoreContent: '' // 无门店的提示
},
  _data: {
    isClearInput: false,
    isToCollectStore: false
  },
  async onLoad (options) {
    console.log(options)
    this.setInitData(options)
    this.getFrequentlyStore()
    this.getNearByStores({
      status: 'init'
    })
    this._data.locateStoreBinds = createStoreBindings(this, {
      store: locateStore,
      fields: ['useDefault']
    })
  },
  onUnload() {
    const locateStoreBinds = this._data.locateStoreBinds
    locateStoreBinds && locateStoreBinds.destroyStoreBindings()
  },
  /**
   * 设置页面初始数据
   */
  setInitData(options) {
    let { formPage = 'fightGroupsPage', PTorderDetailObj = '', pageOrgin = '',activityCode = '',isfromRelayConfirmOrder = 'false' } = options || {}
    if (!!PTorderDetailObj) {
      formPage = 'fightGroupsConfirmOrder'
    }
    const pageData = {
      noStoreContent: ([
        { condition: locateStore.useDefault, tips: '请重新定位或输入提货地址' },
        { condition: pageOrgin === 'relay', tips: '附近没有活动门店' }
      ].find(v => v.condition) || { tips: '该地址附近暂时没有提货门店' }).tips
    }
    switch(formPage) {
      case 'fightGroupsConfirmOrder':
        // 拼团确认订单
        this.handleFightGroupsConfirmOrder({ PTorderDetailObj })
        break;
      case 'selfTakeConfirmOrder':
        // 及时达自提确认订单
        this.handleSelfTakeConfirmOrder(options)
        // 如果存在页面来源
        if( pageOrgin === 'relay'){
            Object.assign(pageData,{
                pageOrgin,
                activityCode,
                isfromRelayConfirmOrder: isfromRelayConfirmOrder === 'true'
            })
            wx.setNavigationBarTitle({
              title: '切换接龙门店',
            })
            wx.setNavigationBarColor({
                backgroundColor:'#EE3C3C',
                frontColor:'#ffffff'
            })
        }
        break;
      case 'fightGroupsPage':
        // 拼团首页
        this.handleFightGroupsPage()
        break;
      default:
        this.handleFightGroupsPage()
    }
    this.setData(pageData)
  },

  /**
   * 处理拼团确认订单进入
   */
  handleFightGroupsConfirmOrder({ PTorderDetailObj }) {
    try {
      const { goodsID }  = JSON.parse(PTorderDetailObj) || {}
      this.setData({
        goodsID,
        formPage: 'fightGroupsConfirmOrder'
      })
    } catch (error) {}
    this.setInitAddressInfo()
  },

  /**
   * 处理及时达自提进入确认订单
   */
  handleSelfTakeConfirmOrder(options) {
    this.setData({
      formPage: 'selfTakeConfirmOrder'
    })
    const { storeInfo = '' } = options || {}
    if (!!storeInfo) {
      try {
        const storeData = JSON.parse(storeInfo)
        this.setInitAddressInfo(storeData)
        return
      } catch (error) {}
    }
    this.setInitAddressInfo()
  },

  /**
   * 拼团首页进入
   */
  handleFightGroupsPage() {
    this.setData({
      formPage: 'fightGroupsPage'
    })
    this.setInitAddressInfo()
  },

  /**
   * 设置页面初始化时门店地址数据
   * 1.有传addressInfo，优先取addressInfo门店地址数据
   * 2.无地址，按照首页逻辑，有收货地址取收货地址，无收货地址则取定位地址
   */
  setInitAddressInfo(addressInfo) {
    if (addressInfo) {
      const { address, storeID, lat, lon, cityName, cityID, cityCode } = addressInfo
      this.setData({
        curAddress: {
          address,
          lat,
          lon
        }
      })
      this.setData({
        cityName,
        cityID,
        cityCode,
        selectedStore: {
          storeID
        }
      })
    } else {
      // 逻辑和及时达一样，有收货地址取收货地址，无收货地址则取定位地址
      const selectedAddress = wx.getStorageSync('selectedAddress') || {}
      const timelyCity = wx.getStorageSync('timelyCity') || { locateCity: {} }
      const locateCity = timelyCity.locateCity || {}
      const userCurrLoca = wx.getStorageSync('userCurrLoca') || {}
      if (selectedAddress.addressId) {
        const { gisAddress, lat, lon } = selectedAddress
        this.setData({
          curAddress: {
            address: gisAddress,
            lat: lat,
            lon: lon
          }
        })
      } else {
        const { address, lat, lon } = timelyCity
        const { location = {} } = userCurrLoca
        this.setData({
          curAddress: {
            address: address || userCurrLoca.address,
            lat: lat || location.lat,
            lon: lon || location.lon
          }
        })
      }
      this.setData({
        cityName: locateCity.cityName || timelyCity.cityName || userCurrLoca.cityName,
        cityID: locateCity.cityID || timelyCity.cityID || '',
        selectedStore: {
          storeID: timelyCity.storeID
        },
      })
    }
  },

  onShow: function(option) {
    // 浏览页面上报神策
    sensors.pageShow('fightGroupsExtrStorePage')
    if (this._data.isToCollectStore) {
      this._data.isToCollectStore = false
      this.getNearByStores({})
    }
  },
  /**
   * @description 选择城市页调用，传参
   * @param {*} city
   */
  refreshCityInfo: function (city = {}) {
    this.setData({
      cityName: city.cityName,
      cityID: city.cityID,
      inputValue:''
    })
  },
  /**
   *
   * @param {*} lat
   * @param {*} lon
   * @param {*} goodsID 商品id，确认订单页过来的时候会传该字段，获取附近上架该商品的门店。
   * @description 获取附近门店信息
   */
  async getNearByStores (scene) {
    const { goodsID, formPage, pageOrgin, isfromRelayConfirmOrder, hasSelectAddress } = this.data
    const locateAuth = await locateStore.checkHasAuth()
    // 如果是接龙详情进入的,有定位权限的情况下,使用定位附近的地址
    if (scene.status === 'init' && pageOrgin === 'relay' && !isfromRelayConfirmOrder) {
      if (locateAuth === true) { return this.nowLocate() }
    }
    // 使用默认定位的情况下并且没有选择门店,不查询门店列表
    if (locateAuth !== true && !hasSelectAddress) { return this.setDefaultStore([], scene) }
    const { cityID, curAddress = {}, cityName } = this.data
    const { lon, lat } = curAddress || {}
    if (!cityID || !lon) { return }
    const param = {
      isNeedSupportSuperVipShop: 'Y',
      cityName,
      cityID,
      lon: lon,
      lat: lat,
      goodsID: goodsID || '', // 从确认订单页过来，只查该商品上架的门店。
      radius: 3000,
      isNeedAddDemoteStoreFun: boolean2YN(true),
      isNeedSaleCount: boolean2YN(true),
      isNeedScore: boolean2YN(true),
    }
    // 及时达自提，只取支持自提的门店
    if (formPage === 'selfTakeConfirmOrder') {
      Object.assign(param, {
        isSupportTake: 'Y'
      })
    } else {
      // 拼团相关，只获取支持拼团的门店
      Object.assign(param, {
        groupSupport: 'Y'
      })
    }
    if (app.checkSignInsStatus()) {
      param.customerID = app.globalData.customerID
      const { addressId = ''} = wx.getStorageSync('selectedAddress') || {}
      if (addressId) {
        param.receiveAddressID = addressId
      }
    }
    try{
      const response = await app.api.getFruitNearByStores(param)
      // 没有附近门店
      if (!response.data || !response.data.length) {
        this.setData({
          isHaveStore: false
        })
        return
      }
      // 有附近门店
      this.setDefaultStore(response.data, scene)
    }catch(error){
      console.error('getNearbyStoreFail', error)
    }
  },
  // 获取常用门店
 getFrequentlyStore() {
    if (!app.checkSignInsStatus()) {
      return
    }
    const { cityCode: initCityCode, goodsID, formPage, selectedStore = {},activityCode,pageOrgin } = this.data
    const { cityCode, storeID } = wx.getStorageSync('timelyCity') || {}
    const { userID } = wx.getStorageSync('user') || {}
    if (!cityCode && !initCityCode) { return }
    const param = {
      cityCode: initCityCode || cityCode,
      customerID: userID,
      goodsID: goodsID || '', // 从确认订单页过来，只查该商品上架的门店。
      isNeedSaleCount: 'Y'
    }
    // 及时达自提，只取支持自提的门店
    if (formPage === 'selfTakeConfirmOrder' || pageOrgin === 'relay') {
      Object.assign(param, {
        isSupportTake: 'Y'
      })
    }
    app.api.getFrequentlyStore(param).then(async({ data }) => {
      for (let i = 0; i < data.length; i++) {
        const store = data[i]
        if (Number(store.storeID) === Number((selectedStore || {}).storeID || storeID)) {
          data.splice(i, 1)
          data.unshift(store)
          break
        }
      }
         // 如果存在门店列表
    if(data.length && pageOrgin === 'relay'){
        let storeCodes = []
        const {cityCode} = wx.getStorageSync('timelyCity') || {};
        storeCodes = data.map((item)=>{
            return item.storeCode
        })
       const params =  {
            channel: '10001',
            storeCodes,
            activityType: 'X', // 活动类型固定值 X-消费
            actionType: 'V', // 活动优惠动作类型固定值, V-接龙
            activityCode,
            dualOrgCode: cityCode
          }
          data = await this.getStorePresenceActivity(params, data)
    }
      this.setData({
        frequentlyStoreList: data || []
      })
    })
  },
  /**
   * 分割数组
   * @param {*} arr
   * @param {*} N
   */
   spliteArray(arr, N) {
    let result = [];
    for (let i = 0; i < arr.length; i += N) {
      result.push(arr.slice(i, i + N));
    }
    return result
  },
  // 选择附近的门店
  selectNearbyStore (e) {
    sensors.track('MPClick', 'fightGroupsSwitchStore')
    wx.reportAnalytics('store_click')
    const { selectedStore = {} } = this.data
    console.log('e.currentTarget',e.currentTarget)
    const { store: targetStore } = e.currentTarget.dataset
    if (app.globalData.reportSensors) {
      app.sensors.track('MPSwitch', {
        mp_screen_name: '提货门店',
        mp_switchObject: '门店',
        mp_from: selectedStore.storeName,
        mp_to: targetStore.storeName
      })
    }
    // city传空对象,主要是想直接通过storeInfo2City取出selectStore里的city信息
    const { store: storeInfo, cityInfo } = locateService.storeInfo2City(targetStore, {})
    this.handleSelectStore({
      cityInfo,
      storeInfo,
    })
  },
  // 选择常用门店
  async selectFrequentlyStore (e) {
    const { store: targetStore } = e.currentTarget.dataset || {}
    // city传空对象,主要是想直接通过storeInfo2City取出selectStore里的city信息
    const { store: storeInfo, cityInfo } = locateService.storeInfo2City(targetStore, {})
    this.handleSelectStore({
      cityInfo,
      storeInfo,
    })
  },

  /**
   * 设置默认选择的门店
   */
  async setDefaultStore (storeList = [], { status = '' } = {}) {
    const { formPage, pageOrgin, activityCode, locateCity } = this.data
    const timelyCity = wx.getStorageSync('timelyCity') || {}
    const { storeID: cacheStoreID, storeName: cacheStoreName } = timelyCity || {}
    let selectedStore = {
      storeID: cacheStoreID,
      storeName: cacheStoreName
    } // 默认选中缓存门店
    if (formPage === 'selfTakeConfirmOrder') {
      // 及时达自提确认订单，默认选中带进来的门店
      const { selectedStore: initSelectedStore = {} } = this.data
      Object.assign(selectedStore, {
        storeID: (initSelectedStore || {}).storeID || cacheStoreID
      })
    }
    const cacheNearStoreIndex = storeList.findIndex(store => Number(store.storeID) === Number(selectedStore.storeID))
    // 缓存门店不在附近门店列表，默认选中附近门店第一个
    if (cacheNearStoreIndex === -1 ) {
      selectedStore = storeList[0]
      if (!selectedStore) return
      // 及时达自提确认订单进入，不需要切换首页定位地址信息
      if (formPage !== 'selfTakeConfirmOrder') {
        const { address, lat, lon } = selectedStore
        if (status === 'againLocate') {
          // 重新定位，清除选择的地址。
          wx.removeStorageSync('selectedAddress')
        }
        // city传空对象,主要是想直接通过storeInfo2City取出selectStore里的city信息
        const { store, cityInfo } = locateService.storeInfo2City(selectedStore, {})
        selectedStore = store
        locateService.updateCityServerStatus(cityInfo)
        app.changeTimelyCityInfo(locateService.getTimelyCity({
          city: cityInfo,
          store: selectedStore,
          address: { lat, lon, address },
          locateCity,
        }))
      }
    } else {
      const deleteStores = storeList.splice(cacheNearStoreIndex, 1)
      storeList.unshift(deleteStores[0])
    }
    // 如果存在门店列表
    if(storeList.length && pageOrgin === "relay"){
        let storeCodes = []
        const {cityCode} = wx.getStorageSync('timelyCity') || {};
        storeCodes = storeList.map((item)=>{
            return item.storeCode
        })
       const aplitedData = this.spliteArray(storeCodes,20)
       const reqList = []
       aplitedData.forEach(async(item)=>{
           const params =  {
                channel: "10001",
                storeCodes:item,
                activityType: "X", // 活动类型固定值 X-消费
                actionType: "V", // 活动优惠动作类型固定值, V-接龙
                activityCode,
                dualOrgCode: cityCode
              }
              reqList.push(this.getStorePresenceActivity(params, storeList))
       })
       const promiseRes = await Promise.all(reqList)
       storeList = promiseRes.flat(Infinity)
    }
    storeList.forEach(item=>{
      item.storeBusinessTime = getStoreBusinessTime({
        startTime:item.startTime||'',
        endTime:item.endTime||'',
        openingTime:item.openingTime||''
      })
    })
   if(pageOrgin === 'relay'){
    //  如果是接龙详情过来的 默认勾选的门店还是取选择的门店
    this.setData({
        storeList: storeList || [],
        isHaveStore: !!storeList.length
      })
   }else{
    this.setData({
         selectedStore,
         storeList: storeList || [],
         isHaveStore: !!storeList.length
       })
   }
  },
  // 输入提货地址
  bindInputTap(e) {
    var value = e.detail.value,
      that = this
    if (this._data.isClearInput) {
      return
    }
    that.setData({
      inputValue: value
    })
    this.toSearchAddress(value)
  },
  async toSearchAddress(value) {
    const res = await wxappMap.getSuggestion({
      keyword: value,
      region: this.data.cityName,
    })
    this.setData({
      regionList: res.data
    })
    this.setData({
      showSearchList: true,
    })

  },
  // 清空搜索地址
  bindCloseTap: function () {
    var that = this
    this.setData({
      inputValue: '',
      showSearchList: false,
      storeListBoxShowFlag: that.data.storeList.length > 0 ? true : false,
      noResultBoxShowFlag: that.data.storeList.length > 0 ? false : true
    })
    this._data.isClearInput = true
  },
  //聚焦显示清除按钮
  bindFocusHandler: function (e) {
    wx.reportAnalytics("searchstore")
    this.setData({
      closeShowFlag: true
    })
    this._data.isClearInput = false
  },
  //失焦隐藏清除按钮
  bindBlurHandler: function (e) {
    this.setData({
      closeShowFlag: false
    })
  },
  // 选择搜索出来的地址，并获取改地址附近的门店。
  async selectSearchAddress (e) {
    wx.reportAnalytics('searchresult_click')
    let { lat, lon, title } = e.currentTarget.dataset
    let location = coordtransform.gcj02tobd09(lon, lat)
    this.setData({
      hasSelectAddress: true,
      curAddress: {
        address: title,
        lat: location[1],
        lon: location[0]
      },
      showSearchList: false
    })
    this.getFrequentlyStore()
    this.getNearByStores({
      status: 'selectAddress'
    })
  },

  //跳转地图定位页面
  toStoreMapPage: function (e) {
    var that = this,
      lat = e.currentTarget.dataset.lat,
      lon = e.currentTarget.dataset.lon,
      name = e.currentTarget.dataset.name,
      address = e.currentTarget.dataset.address,
      x = coordtransform.bd09togcj02(lon, lat)
    wx.openLocation({
      latitude: x[1],
      longitude: x[0],
      name: name,
      address: address
    })
  },

  // 门店资质页
  toStoreCredentials (e) {
    let jumpUrl = e.currentTarget.dataset.url;
    wx.navigateTo({
      url: '/h5/pages/activityTemp/index?url=' + encodeURIComponent(jumpUrl)
    })
  },

  // 定位当前位置
  nowLocate: app.subProtocolValid('shop', async function() {
    this.setData({ 'curAddress.loading': true })
    try {
      const locationInfo = await locateService.getGeoLocation()
      await locateService.getCityName(locationInfo.latitude, locationInfo.longitude)
      const { location: { lng, lat }, address, cityName = '' } = wx.getStorageSync('userCurrLoca') || {}
      const bdlocate = coordtransform.gcj02tobd09(lng, lat);
      const param = {
        lat: bdlocate[1],
        lon: bdlocate[0],
        address,
      }
      this.setData({
        curAddress: { ...param, loading: false },
        cityName
      })
      // 根据定位信息获取cityId, 然后获取附近门店
      this.getCityId(Object.assign(param, { cityName, isMatchAddress: boolean2YN(false), }), () => {
        this.getNearByStores({
          status: 'againLocate'
        })
      })
    } catch (error) {
      this.setData({ 'curAddress.loading': false })
      locateService.handleWXLocationErr(error.errMsg)
    }
    sensors.track('MPClick', 'fightGroupsLocation')
  }),
  // 根据定位信息获取cityId, 然后获取附近门店
  async getCityId (param, callback) {
    try {
      const res = await app.api.checkCity(param)
      const resData = res.data || { locateCity: {} }
      this.setData({
        cityID: locateService.getLocateCity(resData).id || '',
        locateCity: resData.locateCity,
      }, () => callback && callback.call(this))
    } catch (err) {
      console.log(err)
    }
  },
  toStoreDetail(e) {
    let i = e.currentTarget.dataset.index
    console.log(i)
    // let txLocation = coordtransform.bd09togcj02(this.data.storeList[i].lon, this.data.storeList[i].lat)
    let curStore = this.data.storeList[i] || {}
    let { storeCode, distance } = curStore
    let pageInfo = {
      distance,
      storeCode
    }
    wx.navigateTo({
      url: '/homeDelivery/pages/storeDetail/index?storeInfo=' + encodeURIComponent(JSON.stringify(pageInfo))
    })
    sensors.track('MPClick', 'fightGroupsStoreDetail')
  },

  // 切换城市上报神策
  handleSwitchCity () {
    sensors.track('MPClick', 'fightGroupsSwitchCity')
  },
  setTakeStoreInfoWithEmit(storeInfo) {
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.emit('fightGroups.selectedAddress', { detail: storeInfo })
  },
  invokeLastPageMethod(methodName, storeInfo) {
    const pages = getCurrentPages()
    if (!pages || pages.length < 2) {
      return
    }
    const prevPage = pages[pages.length - 2]
    const prevMethod = prevPage[methodName]
    return prevMethod
      ? prevMethod.call(prevPage, storeInfo)
      : this.setTakeStoreInfoWithEmit(storeInfo)
  },
  /**
   * 处理选择门店
   * @param {Object} options
   */
   handleSelectStore(options) {
    const { formPage, pageOrgin, locateCity } = this.data
    const { storeInfo, cityInfo } = options
    const { cityID, cityCode, deliveryCenterCode, cityName } = cityInfo
    const timelyCityInfo = locateService.getTimelyCity({
      city: cityInfo,
      store: storeInfo,
      address: storeInfo,
      locateCity,
    })
    if (formPage === 'selfTakeConfirmOrder') {
      // 及时达自提确认订单
      this.invokeLastPageMethod('setTakeStoreInfo', {
        ...storeInfo,
        cityID,
        cityCode,
        deliveryCenterCode,
        cityName,
      })
      if(pageOrgin === 'relay'){
        wx.removeStorageSync('selectedAddress')
        locateService.updateCityServerStatus(cityInfo)
        app.changeTimelyCityInfo(timelyCityInfo, {
          useDefaultAddress: false,
          isFreshPageGoods: true
        })
      }
    } else {
      locateService.updateCityServerStatus(cityInfo)
      app.changeTimelyCityInfo(timelyCityInfo)
      // 选择了拼团门店则清除选择的地址。
      wx.removeStorageSync('selectedAddress')
    }
    const { isfromRelayConfirmOrder } = this.data
    const pages = getCurrentPages();
    const beforePage = pages[pages.length - 2]
    wx.navigateBack({
        success:() => {
            if(isfromRelayConfirmOrder){
                beforePage.onLoad()
                if (pages.length > 0) {
                    beforePage.setData({
                        store: deepClone(storeInfo)
                    })
                    beforePage.refresh(deepClone(storeInfo))
            }
          }
      }
    })
  },
  /**
   * 获取活动匹配的门店，并且过滤
   * @param params 接口入参
   * @param target 目标门店列表数据
   */
  async getStorePresenceActivity(params,target){
        try {
          /** @type { { data: string[] } } */
           const {data = [] } = await app.api.getStorePresenceActivity(params)
           return data.length ? target.filter(v => data.includes(v.storeCode || v.number)) : []
        } catch (error) {
            return target
        }
  },
  async toCollection(e) {
    const { storeCode, isUserCollection } = e.currentTarget.dataset.store
    const param = {
      storeCode: storeCode,
      type: isUserCollection === 'Y' ? 0 : 1
    }
    await updateCollectStore(param)
    this.getNearByStores({})
  },
  navigateToCollectStore() {
    wx.navigateTo({
      url: '/homeDelivery/pages/collectStore/index'
    })
    this._data.isToCollectStore = true
  },
  /**
   * 拨打电话
   */
  toCall(e) {
    const { mobile, phone } = e.currentTarget.dataset
    wx.makePhoneCall({
      phoneNumber: mobile || phone
    })
  }
})
