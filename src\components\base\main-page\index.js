// components/base/main-page/index.js
// const subProtocol = require('./mixins/subProtocol')

Component({
  // mixins: [subProtocol],
  /**
   * 组件的属性列表
   */
  properties: {
    currentView: {
      type: String,
      value: null,
      observer(cur){
        console.log('currentView', cur)
      }
    },
    hasB2CServers: {
      type: Boolean,
      value: true
    },
    useScene: { // 使用场景 fruit： 水果(及时达) vegetables： 蔬菜（次日达）
      type: String,
      value: "fruit"
    },
    // 按钮文案
    btnText: {
      type: String,
      value: ""
    },
    // 是否展示导航栏
    showNav: {
      type: <PERSON>olean,
      value: false
    },
    navBarTitle: {
      type: String,
      value: ""
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },
  methods: {
    /**
     * 刷新一下按钮
     */
    handleBtnClick() {
      this.triggerEvent('btnClick')
    }
  }
})
