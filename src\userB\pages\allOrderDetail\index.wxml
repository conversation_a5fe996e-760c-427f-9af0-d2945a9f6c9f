
<wxs module="filter" src="../../../utils/common.wxs"></wxs>
<import src="/pages/template/index.wxml" />
<import src="./template/index.wxml" />
<!--  bgColorDuration="0.1s"-->
<nav-bar
  normalBack
  background="{{navBarBgColor}}"
  color="{{navBarColor}}"
  navBarTitle="订单详情"
  bindnavBarHeight="getNavBarHeight"
  style="position: {{ (isStoreOrder || isB2COrder || isTimelyOrder) ? 'absolute' : 'relative'}}"
>
  <view slot="back" class="nav-back" style="filter: brightness({{backFilter}})">
    <image src="/source/images/arrow_back.png" />
  </view>
</nav-bar>

<main-page currentView="content" />

<!-- 门店订单详情 start -->
<block wx:if="{{isStoreOrder && isReady}}">
  <template
    is="header-section"
    data="{{ navBarHeight, titleInfo, showGiftSection: storeOrderData.giftBindStatus.isGiftOrderBind, showBindPopup: posGiftOrderPopup.show }}"></template>
  <!--商品状态 start  -->
  <!-- <view class='top highTop' style='background-image:url({{orderStateBgMap[orderFlowState]}})'> -->
    <!-- 后端数据先注释，前端写死 -->
    <!-- <view class="top-status">
      {{titleInfo.title}}
    </view>
    <view class="tips" wx:if="{{titleInfo.desc}}">{{titleInfo.desc}}</view> -->
    <!-- <view class="top-status">交易成功</view> -->
    <!-- <view class="tips">感谢您对百果园的信任，欢迎下次光临！</view> -->
     <!-- 退款失败 -->
    <!-- <view class="tips" wx:if="{{refundRejectReason}}" data-show="{{true}}" bindtap="handleRefundReasonModel">退款不通过原因<image src='/source/images/icon_remind_circle_white.png' class='icon-remind'></image></view> -->
  <!-- </view> -->
  <!--商品状态 end  -->
  <block wx:if="{{!(storeOrderData.giftBindStatus.isGiftOrderBind && posGiftOrderPopup.show)}}">
  <block wx:if="{{receiver}}">
    <!-- 门店订单转配送地图 start -->
    <view class="offline-delivery-map-section">
      <delivery-map
        orderNo="{{orderTicket}}"
        offline-order
        model:show-map="{{showOfflineDeliveryMap}}"
        dispatch-time="{{offlineDispatchTime}}"
        receiver="{{receiver}}"
        content-height="65vh"
      >
        <view slot="info-tag" class="offline-delivery-tag">需要配送上门</view>
      </delivery-map>
    </view>
    <!-- 门店订单转配送地图 end -->
    <view class="receive-section section">
      <view class="receive__list">
        <view class="list-item list-item--paddingRight">
          <view>
            <view class="list-item__left">收货人</view>
            <view class="list-item__right">
              <view class="list-item__right-title row-ellipsis"><!--
                -->{{ receiver.name }}<!--
                --><text class="receiver-mobile"><!--
                -->{{ receiver.mobile }}<!--
                --></text><!--
              --></view>
              <view class="list-item__right-content row-ellipsis"><!--
                -->{{ receiver.province }}<!--
                -->{{ receiver.city }}<!--
                -->{{ receiver.hometown }}<!--
                -->{{ receiver.address }}<!--
                -->{{ receiver.addressNote }}<!--
              --></view>
            </view>
          </view>
          <template is="icon-list" data="{{ index: 0, isClosed: store.isClosed }}"></template>
        </view>
        <view class="list-item list-item--paddingRight">
          <view>
            <view class="list-item__left">配送门店</view>
            <view class="list-item__right">
              <view class="list-item__right-title row-ellipsis">{{store.storeName}}</view>
            </view>
          </view>
          <template is="icon-list" data="{{ index: 1, isClosed: store.isClosed }}"></template>
        </view>
      </view>
    </view>
  </block>
  <block wx:else>
    <!-- 门店信息 start -->
    <view class="receive-section section">
      <view class="receive__notice">
        <view class="receive-tip" wx:if="{{refundInfo && (orderFlowState == 'REFUNDED' || !isDmOrder)}}">
          <span class="receive-tip__label">{{refundInfo.title}}：</span>
          <span class="receive-tip__content">{{refundInfo.desc}}</span>
        </view>
      </view>
      <view class="receive__list">
        <view class="list-item list-item--paddingRight">
          <view>
            <view class="list-item__left">门店地址</view>
            <view class="list-item__right">
              <view class="list-item__right-title row-ellipsis">{{store.storeName}}</view>
              <view class="list-item__right-content row-ellipsis">{{store.address}}</view>
            </view>
          </view>
          <template is="icon-list" data="{{ index: 0, isClosed: store.isClosed }}"></template>
        </view>
        <view class="list-item list-item--paddingRight">
          <view>
            <view class="list-item__left">营业时间</view>
            <view class="list-item__right">
              <view class="list-item__right-title row-ellipsis">{{store.openingTime}}</view>
            </view>
          </view>
          <template is="icon-list" data="{{ index: 1, isClosed: store.isClosed }}"></template>
        </view>
      </view>
    </view>
    <!-- 门店信息 end -->
    <block wx:if="{{options.isOfflineDelivery}}">
      <!-- 选择地址 start -->
      <view class="info-title">需要配送上门</view>
      <view class="address-section">
        <view wx:if="{{offlineDeliveryAddress.addressId}}" class="address-section-info">
          <view class="list-item" bind:tap="openAddressList">
            <view>
              <view class="list-item__left">收货人</view>
              <view class="list-item__right">
                <view class="list-item__right-title row-ellipsis"><!--
                  -->{{ offlineDeliveryAddress.name }}<!--
                  --><text class="receiver-mobile"><!--
                  -->{{ offlineDeliveryAddress.mobile }}<!--
                  --></text><!--
                --></view>
                <view class="list-item__right-content row-ellipsis"><!--
                  -->{{ offlineDeliveryAddress.province }}<!--
                  -->{{ offlineDeliveryAddress.city }}<!--
                  -->{{ offlineDeliveryAddress.hometown }}<!--
                  -->{{ offlineDeliveryAddress.address }}<!--
                  -->{{ offlineDeliveryAddress.addressNote }}<!--
                --></view>
              </view>
            </view>
          </view>
        </view>
        <choice-address
          wx:else
          tips="请选择收货地址"
          bind:choiceAddressInfo="openAddressList">
        </choice-address>
      </view>
      <view class="info-title info-title-address">
        <view class="info-title-address-text">送达时间</view>
        <view class="info-title-address-action {{offlineDeliveryTimePopup.selectDeliveryInfo.desc ? 'info-title-address-action-selected' : ''}}" bind:tap="showOfflineDeliveryTimePicker">{{offlineDeliveryTimePopup.selectDeliveryInfo.desc ? filter.exchangeText(offlineDeliveryTimePopup.selectDeliveryInfo.desc) : '请选择时间'}}</view>
      </view>
      <view class="address-section-submit">
        <view class="address-section-submit-btn" bind:tap="submitOfflineDeliveryAddress">{{ offlineDeliveryAddressSubmit.loading ? '提交中...' : '提交' }}</view>
      </view>
      <!-- 选择地址 end -->
    </block>
  </block>
  <!--商品详情 start -->
  <view class="info-title">商品信息</view>
  <view class="store-goods-box">
    <view class="store-goods" wx:for="{{goodsItem}}" wx:key="index">

      <view class="goods-top">
        <image class="store-goods-pic" src="{{item.headPic || filter.getSpuHeadPic(item.goodsNumber, headPicList, 'spuNumber', picUrl) || defaultHeadPic}}"></image>
        <view class="store-goods-msg">
          <view class="store-goods-msg-top">
            <view class="store-goods-name text-hidden-line2">{{filter.goodsLevelFormat(item.goodsName)}}</view>
          </view>
          <view class="store-goods-spec">
            <text class="store-goods-single-price">单价：¥{{filter.filtersYuan(item.goodsPrice)}}{{filter.filtersFen(item.goodsPrice) || '.00'}}</text>
            <text>数量：{{item.goodsValue}}{{item.goodsSpec}}</text>
          </view>
        </view>
        <view class="store-goods-price">
          <view>¥{{filter.formatPrice(item.goodsActualAmount)}}</view>
          <view wx:if="{{isDmOrder}}" class="actual-pay">实付 ¥{{filter.formatPrice(item.actualMoney)}}</view>
        </view>
        <view wx:if="{{item.discountInfo && item.discountInfo.saveTips}}" class="store-goods-discount">
          <view class="store-goods-discount-body">
            <view class="store-goods-discount-left">
              <view wx:for="{{item.discountInfo.saveTips}}" wx:key="index" wx:for-item="saveTipsItem" class="store-goods-discount-text">{{saveTipsItem}}</view>
            </view>
            <view class="store-goods-discount-right store-goods-discount-text">折合约：<text class="store-goods-discount-final">{{item.discountInfo.finalPrice}}</text></view>
          </view>
        </view>
      </view>

      <view class="goods-operate">
        <!-- 如果商品行有三无退详情，并且是退款关闭 则需要单独处理成重新申请 -->
        <view
          wx:if="{{ item.canRefund && item.afterSaleInfo && item.afterSaleInfo.refundStatus === 111 }}"
          class="btn-primary"
          data-order-type="3"
          data-goods="{{ item }}"
          data-index="{{index}}"
          data-goods-index="{{index}}"
          catchtap="goDetailHandle">重新申请</view>
        <block wx:else>
        <view
          wx:if="{{ item.canRefund }}"
          data-order-type="3"
          data-goods="{{ item }}"
          data-index="{{index}}"
          data-goods-index="{{index}}"
          catchtap="sanwuBtnHandleSingle">三无退货</view>

        <view
          wx:elif="{{ item.afterSaleInfo || item.hasThreeNoExchange }}"
          data-order-type="3"
          data-goods="{{ item }}"
          data-index="{{index}}"
          data-goods-index="{{index}}"
          catchtap="goDetailHandle">{{ item.btnText || '售后详情' }}</view>
        </block>
      </view>
    </view>
  </view>
  <!-- 商品详情 end -->
  <view class="info-title">订单信息</view>
  <view wx:if="{{storeOrderBill}}" class="total-price">
    <view wx:for="{{storeOrderBill.items}}" wx:key="index" class="price-com {{item.highlight ? 'price-com--highlight' : ''}}">
      <view class="detail-pack-left"><text>{{item.title}}</text></view>
      <text>{{item.prefix || ''}}¥{{filter.formatPriceWidthZero(item.value)}}</text>
    </view>
  </view>
  <view wx:else class="total-price">
    <!-- 商品金额：商品身份价 + 餐盒费 + 包装费 -->
    <view class="price-com"><text>商品金额</text> <text>￥{{filter.formatPriceWidthZero(goodsTotalAmount)}}</text></view>
    <view class="price-com" wx:if="{{packageAmount > 0}}">
      <view class="detail-pack-left">
        <text>餐盒费</text>
        <!-- <image class="detail-pack-icon" src="../../source/images/icon_qm_circle_black_22.png" /> -->
      </view>
      <text>¥{{filter.formatPriceWidthZero(packageAmount)}}</text>
    </view>
    <view class="price-com" wx:if="{{storeOrderCouponAmount && storeOrderCouponAmount != '0'}}">
      <text>优惠券</text><text>-￥{{filter.formatPriceWidthZero(storeOrderCouponAmount)}}</text>
    </view>
    <view class="price-com" wx:if="{{memberDiscountAmount && memberDiscountAmount != '0'}}">
      <text>活动优惠</text><text>-￥{{filter.formatPriceWidthZero(memberDiscountAmount)}}</text>
    </view>
  </view>
  <view class="pay-info">
    <view class="info-wrap">
      <view class="price-com2">
        <text class="total-pay">支付总额：</text> <text class="total-money">￥<text class="big-money">{{filter.filtersYuan(orderActualAmount)}}</text>{{filter.filtersFen(orderActualAmount)}}</text>
      </view>
    </view>
  </view>
  <view class="order-info-wrap">
    <view class="order-info flex" wx:if="{{isPay}}">
      <view>
        <text class="title">订单编号</text>{{orderTicket}}
      </view>
      <view class="btn-copy" bindtap="copyNoTap" data-no="{{orderTicket}}">复制</view>
    </view>
    <view class="order-info"><text class="title">付款时间</text>{{tradeTime}}</view>
    <view class="order-info" wx:if="{{orderFlowState == 'REFUNDED'}}"><text class="title">退款时间</text>{{refundApplyTime}}</view>
    <view class="order-info" wx:if="{{orderFlowState == 'REVIEWING'}}"><text class="title">申请时间</text>{{refundApplyTime}}</view>
    <block wx:if="{{!options.isOfflineDelivery}}">
      <block wx:if="{{storeOrderData.giftAuthStatus.openAuth}}">
        <view class="order-info"><text class="title">售后状态</text>售后已转移</view>
      </block>
      <block wx:else>
        <view class="order-info" wx:if="{{!(isAllowComplaint == 'Y' && isAllowRefund == 'Y') && !(refundInfo && refundInfo.desc)}}"><text class="title">售后状态</text>已超过7天无法三无退货</view>
        <view class="order-info" wx:if="{{orderFlowState == 'REFUNDED' && !(refundInfo && refundInfo.title === '三无退货')}}"><text class="title">售后状态</text>无可退金额，无法三无退货</view>
      </block>
      <view class="order-info" wx:if="{{invoiceDetailContent}}"><text class="title">发票信息</text><text class="{{storeOrderData.giftBindStatus.isGiftOrderBind ? 'order-info--heightlight' : ''}}">{{invoiceDetailContent}}</text></view>
      <view class="order-info" wx:if="{{storeOrderData.giftBindStatus.isGiftOrderBind && storeOrderData.canNoReasonRefund}}"><text class="title">售后状态</text>{{storeOrderData.allowNoReasonRefundLastTime}} 前可进行售后</view>
    </block>
  </view>

  <view class="adaption {{storeOrderData.giftBindStatus.isGiftOrderBind || options.isOfflineDelivery ? 'adaption-gift-order' : ''}}"></view>
  <!-- 门店订单页脚按钮  -->
  <view class="footer-btn {{storeOrderData.giftBindStatus.isGiftOrderBind || options.isOfflineDelivery ? 'footer-btn--hide' : ''}}">
    <view class="store-order-more-btn">
      <!-- 更多按钮组件 -->
      <order-detail-more-btn
        wx:if="{{ leftBtnList && leftBtnList.length }}"
        menuList="{{ leftBtnList }}"
        >
      </order-detail-more-btn>
      <!-- 更多按钮组件 -->

    </view>

    <!--v2.4开具发票-->
    <!-- 售后转移按钮 -->
    <block wx:if="{{storeOrderData.giftAuthStatus.showAuth}}">
      <button wx:if="{{storeOrderData.giftAuthStatus.hasBind}}" class="footer-btn-disabled" plain size="mini">售后已转移</button>
      <button wx:elif="{{storeOrderData.giftAuthStatus.openAuth}}" plain size="mini" data-sensors-key="160200007" open-type="share">通知好友获取售后</button>
      <button wx:else plain size="mini" bind:tap="showPosGiftOrderAuthPopup" data-sensors-key="160200006" data-status="openAuth">让好友进行售后</button>
    </block>
    <button wx:if="{{invoiceBtnMap && invoiceBtnMap.isAllow}}"  plain="true"  size="mini"  bindtap="onInvoiceBtnTap">{{invoiceBtnMap.text}}</button>
    <button wx:if="{{isAllowComplaint == 'Y' && isAllowRefund == 'Y' && !isDmOrder }}" size="mini" plain="true" bindtap="sanwuBtnHandle" data-orderTicket="{{orderTicket}}">三无退货</button>
    <button wx:if="{{isFromScanRefund}}" size="mini" plain="true"  bindtap="service">在线客服</button>
    <button wx:if="{{isSupportComment == 'Y'}}" size="mini" plain="true" data-orderid="{{ orderTicket }}" data-new-pos-order="{{1}}" data-ordertype="100" catchtap="commentBtnHandle">评价</button>
  </view>
  </block>
  <!-- 送礼订单弹窗 -->
  <gift-order-popup
    show="{{posGiftOrderPopup.show}}"
    status="{{posGiftOrderPopup.status}}"
    hide-close="{{storeOrderData.giftBindStatus.showBind}}"
    storeOrderData="{{storeOrderData}}"
    bind:keepPopupOpen="onKeepPopupOpen"
    bind:changeStatus="onPosGiftOrderPopupChangeStatus"
    bind:close="onPosGiftOrderPopupClose" />

  <!-- 自定义时间选择组件 -->
  <pagoda-popup
    wx:if="{{offlineDeliveryTimePopupShow}}"
    model:visible="{{offlineDeliveryTimePopupShow}}"
    position="bottom"
    title="选择送达时间"
    round="{{true}}"
    head-class="head-class"
    title-class="title-class"
    z-index="{{1000}}"
    height="48vh">
    <pagoda-picker
      time-list="{{offlineDeliveryTimePopup.timeList}}"
      bindpick="offlineDeliveryTimePick"
      bindchangeDate="offlineDeliveryTimePickChangeDate"
      currentIndex="{{offlineDeliveryTimePopup.currentIndex}}">
    </pagoda-picker>
  </pagoda-popup>
</block>
<!-- 门店订单详情 end -->

<!-- 及时达订单详情 -->
<block wx:if="{{isTimelyOrder && isReady}}">
  <!-- 及时达配送中骑手地图 -->
  <delivery-map wx:if="{{ orderStatus.orderStatusDesc === '配送中' && orderNo }}"
                orderNo="{{ orderNo }}"
                receiver="{{ receiver }}"
                store="{{ store }}"
                navBarHeight="{{ navBarHeight }}">
  </delivery-map>
  <!-- 及时达配送中骑手地图 -->

<scroll-view scroll-y>
  <view class="order-container safe-area-inset-bottom {{ orderStatus.orderStatusDesc === '配送中' && orderNo ? 'show-map' : '' }}">
    <!-- 详情状态、头部信息 -->
    <template
      wx:if="{{ orderStatus.orderStatusDesc !== '配送中' }}"
      is="header-section"
      data="{{ navBarHeight, titleInfo, showCountDown, laym, lays }}"></template>
    <!-- 详情状态、头部信息 -->

    <!-- 收件人/配送信息区域 -->
    <view class="receive-section section">
      <!-- 提示内容区域 -->
      <view class="receive__notice">
        <view wx:if="{{ dispatchType.desc === '自提配送' && orderStatus.orderStatusDesc !== '已取消' }}" class="receive-tip">
            助力环保，人人有责，提货请自备购物袋或到店购买哦~
        </view>

        <!-- 差额退款提示 -->
        <view wx:if="{{ refundInfo.differenceRefundMoney }}" class="receive-tip receive-tip--green">
            <span class="receive-tip__label">
                差额退款：
            </span>
            <span class="receive-tip__content">
                {{ refundInfo.differenceRefundMoney }}
            </span>
        </view>
        <!-- 差额退款提示 -->

        <!-- 三无退货提示 已去掉 -->
      </view>
      <!-- 提示内容区域 -->

      <view class="receive__list">
        <view wx:for="{{ receiveList }}" wx:key="index" wx:for-item="receiveItem" class="list-item {{ index < 2 ? 'list-item--paddingRight' : '' }}">
          <!-- 送达时间 -->
          <view wx:if="{{ receiveItem === '送达时间' }}">
              <view class="list-item__left">
                {{ orderStatus.orderStatusDesc === '交易成功' ? '送达时间' : '预计送达' }}
              </view>

              <view class="list-item__right right-padding">
                  <!-- 送达时间 -->
                  <view class="list-item__right-title row-ellipsis">
                      {{ deliveryInfo.time }}
                  </view>
                  <!-- 送达时间 -->

                  <view class="delivery-label row-ellipsis">
                    <!-- 最快xx分钟送达 -->
                    <view wx:if="{{ deliveryInfo.label }}" class="arrive-time">{{ deliveryInfo.label }}</view>
                    <!-- 最快xx分钟送达 -->

                    <!-- 超时赔券说明icon -->
                    <span class="over-time"
                        wx:if="{{ overTimeText }}"
                        bindtap="navigateToCommonH5Page"
                        data-urlType="1">{{ overTimeText }}<image src="{{ QUESTION_ICON }}" /></span>
                    <!-- 超时赔券说明icon -->
                  </view>
              </view>
          </view>
          <!-- 送达时间 -->

          <!-- 收货人 -->
          <view wx:if="{{ receiveItem === '收货人' }}">
              <view class="list-item__left">
                  收货人
              </view>

              <view class="list-item__right">
                  <!-- 送达时间 -->
                  <view class="list-item__right-title row-ellipsis">
                      {{ receiver.name }}

                      <!-- 收货人手机号 -->
                      <span wx:if="{{ receiver.mobile }}" class="receiver-mobile">
                        {{ filter.formatPhoneNumber_344(receiver.mobile) }}
                      </span>
                      <!-- 收货人手机号 -->
                  </view>
                  <!-- 送达时间 -->

                  <!-- 收货地址 -->
                  <view class="list-item__right-content row-ellipsis">
                    {{ receiver.province }} {{ receiver.city }} {{ receiver.hometown }} {{ receiver.address }} {{ receiver.addressNote }}
                  </view>
                  <!-- 收货地址 -->
              </view>
          </view>
          <!-- 收货人 -->

          <!-- 配送门店 -->
          <view wx:if="{{ receiveItem === '配送门店' }}">
              <view class="list-item__left">
                  配送门店
              </view>

              <view class="list-item__right">
                  <view class="list-item__right-title row-ellipsis">
                      {{ store.shortName || store.storeName }}
                  </view>
              </view>
          </view>
          <!-- 配送门店 -->

          <!-- 自提门店名称 -->
          <view wx:if="{{ receiveItem === '自提门店' }}">
              <view class="list-item__left">
                  自提门店
              </view>

              <view class="list-item__right">
                  <view class="list-item__right-title row-ellipsis">
                      {{ store.shortName || store.storeName }}
                  </view>
                  <view class="list-item__right-content row-ellipsis">
                      {{ store.detailAddress }}
                  </view>
              </view>
          </view>
          <!-- 自提门店名称 -->

          <!-- 营业时间 -->
          <view wx:if="{{ receiveItem === '营业时间' }}">
              <view class="list-item__left">
                  营业时间
              </view>

              <view class="list-item__right">
                  <view class="list-item__right-title row-ellipsis">
                      {{ store.storeBusinessTime }}
                  </view>
                  <template is="icon-concactStore"></template>
              </view>
          </view>
          <!-- 营业时间 -->

          <!-- 提货时间 -->
          <view wx:if="{{ receiveItem === '提货时间' && deliveryInfo }}">
              <view class="list-item__left">
                  提货时间
              </view>

              <view class="list-item__right">
                  <view class="list-item__right-title row-ellipsis">
                    {{ deliveryInfo.time }}
                  </view>
              </view>
          </view>
          <!-- 提货时间 -->

          <template is="icon-list" data="{{ index, receiveList, isClosed: store.isClosed }}"></template>
        </view>
      </view>
    </view>
    <!-- 收件人/配送信息区域 -->

    <!-- 仅自提时展示的提货码 -->
    <view wx:if="{{ orderStatus.orderStatusDesc === '待自提' }}" class="scan-section section">
      <view class="scan-section__desc">
        *请于计划时间内到 <span>{{ store.shortName || store.storeName }}</span> 提货
      </view>
      <image class="scan-section__code" wx:if="{{ barcodeImgSrc }}" src="{{ barcodeImgSrc }}"></image>
      <view class="scan-section__number">
          <span>提货码：</span>
          <span>{{ filter.formatTakeCode(pickUpCode) }}</span>
      </view>
    </view>
    <!-- 仅自提时展示的提货码 -->

    <!-- 商品列表-->
    <template is="goods-section" data="{{ goodsInfoList, blessCardContent, formattedBlessCardContent, dispatchTimeRanage, orderIsFalse, overTimeText, QUESTION_ICON, isTimelyOrder }}"></template>
    <!-- 商品列表-->

    <!-- 月卡信息 -->
    <card-info wx:if="{{vipCardInfo.type}}" card-info="{{vipCardData}}"></card-info>
    <!-- 月卡信息 -->

    <!-- 充值信息 -->
    <card-info wx:if="{{rechargeCardInfo.title}}" card-info="{{rechargeCardInfo}}"></card-info>
    <!-- 充值信息 -->

    <!-- 金额优惠及减免信息列表 -->
    <template is="price-section" data="{{ addList, minusList, payment,vipCardInfo, vouchersPayAmount, totalGoodsAmount }}"></template>
    <!-- 金额优惠及减免信息列表 -->
    <view class="pay-mode-box" wx:if="{{orderStatus && orderStatus.orderStatusDesc !== '已取消' && orderStatus.orderStatusDesc !== '交易成功' && orderStatus.orderStatusDesc !== '待付款' }}">
          <text class="pay-mode-text" wx:if="{{noInvoiceDesc}}">{{noInvoiceDesc}}</text>
          <view wx:else>
            <text class="pay-mode-text" wx:if="{{dispatchTypeDesc === '自提配送'}}">*自提后可在订单详情页中开具电子发票</text>
            <text class="pay-mode-text" wx:else>*收货后可在订单详情页中开具电子发票</text>
          </view>
    </view>

    <view wx:if="{{ showThridPayDiscount }}" class="thrid-pay-discount">订单最高可退金额不含部分三方支付优惠部分，如有疑问请咨询<text bindtap="serviceBtn"> 400-181-1212</text></view>

    <!-- 心享会员优惠信息 -->
    <view
      wx:if="{{orderStatus.orderStatusDesc !== '已取消' && payment.freeInfoList && payment.freeInfoList.length}}"
      class="vip-section">
      <vip-section
        static-text
        tips="本单已省{{filter.formatPrice(payment.heartSaveAmount)}}元，了解更多省钱权益"
        btn="查看"
        bind:vipTipsClick="showVipDetail" />
    </view>
    <!-- 心享会员优惠信息 -->

    <!-- 订单信息列表 -->
    <template is="info-section" data="{{ orderNo, timeLine, otherOrderInfo }}"></template>
    <!-- 订单信息列表 -->

    <!-- 页脚按钮 -->
    <view wx:if="{{ btnList.length }}" class="footer-btn-list safe-area-inset-bottom">
        <view class="store-order-more-btn">
            <!-- 更多按钮组件 -->
            <order-detail-more-btn
                wx:if="{{ leftBtnList && leftBtnList.length }}"
                menuList="{{ leftBtnList }}"
                >
            </order-detail-more-btn>
            <!-- 更多按钮组件 -->
        </view>

        <block wx:for="{{btnList}}" wx:key="btnType" wx:for-item="btnItem">
            <view
              data-type="{{btnItem.btnType}}"
              data-orderInfo="{{item}}"
              class="{{btnItem.class}}"
              catchtap="handleBtn"><!--
              --><view
                wx:if="{{ btnItem.btnType === 'payImmediatelyBtn' && showCountDown }}"
                class="pay-timer-btn"><!--
                -->{{rechargeInfo && rechargeInfo.actualAmount ? '充值并支付' : '立即支付'}}{{laym}}:{{lays}}<!--
              --></view><!--
              --><block wx:else><!--
                -->{{ btnItem.btnName }}<!--
              --></block>
            </view>
        </block>
    </view>
    <!-- 页脚按钮 -->

  </view>

</scroll-view>
</block>
<!-- 及时达订单详情 -->

<!-- 全国送b2c订单详情 start -->
<view wx:if="{{isB2COrder && isReady}}" class="b2c-detail safe-area-inset-bottom">
  <!-- 详情状态、头部信息 -->
  <template
    wx:if="{{ orderStatus.orderStatusDesc !== '配送中' }}"
    is="header-section"
    data="{{ navBarHeight, titleInfo, showCountDown, laym, lays, orderType: 'b2c', isAllSent }}"></template>
  <!-- 详情状态、头部信息 -->

  <!-- 收件人/配送信息区域 -->
  <view class="receive-section section">
    <!-- 提示内容区域 -->
    <view class="receive__notice">
      <!-- 差额退款提示 -->
      <view wx:if="{{ refundInfo.differenceRefundMoney }}" class="receive-tip receive-tip--green">
          <span class="receive-tip__label">
              差额退款：
          </span>
          <span class="receive-tip__content">
              {{ refundInfo.differenceRefundMoney }}
          </span>
      </view>
      <!-- 差额退款提示 -->

      <!-- 三无退货提示 已去掉 -->
    </view>
    <!-- 提示内容区域 -->

    <view class="receive__list">
      <view
        wx:for="{{ receiveList }}"
        wx:key="index"
        wx:for-item="receiveItem"
        class="list-item">
        <!-- 发货时间 -->
        <view wx:if="{{ receiveItem === '发货时间' }}">
            <view class="list-item__left">
                发货时间
            </view>

            <view class="list-item__right right-padding">
                <view class="list-item__right-title row-ellipsis">
                    {{ dispatchTimeDesc }}
                </view>
            </view>
        </view>
        <!-- 发货时间 -->

        <!-- 配送门店 -->
        <view wx:if="{{ receiveItem === '配送门店' }}">
            <view class="list-item__left">
                配送门店
            </view>

            <view class="list-item__right">
                <!-- 配送门店 -->
                <view class="list-item__right-title row-ellipsis">
                    {{ store.shortName }}
                </view>
                <!-- 配送门店 -->
            </view>
        </view>
        <!-- 配送门店 -->

        <!-- 配送快递 -->
        <block wx:if="{{ receiveItem === '配送快递' && expressInfo.length }}">
          <view>
              <view class="list-item__left">
                  配送快递
              </view>

              <view class="list-item__right right-padding">
                <view
                  wx:if="{{ expressInfo.length }}"
                  class="delivery-info">
                  <view style="height: {{expressBoxH}}" bindtap="navigateToLogisticsDetail">
                    <template is="delivery-info" data="{{expressInfo, expectedReceiveTime, hasMutiplePackage}}"></template>
                  </view>
                </view>
              </view>
          </view>

          <view class="dots-box" wx:if="{{expressInfo.length}}">
            <view wx:for="{{expressInfo}}" wx:key="index" class="dot {{index === currentSwiper ? 'active' : ''}}"></view>
          </view>
        </block>
        <!-- 配送快递 -->

        <!-- 收货人 -->
        <view wx:if="{{ receiveItem === '收货人' }}">
            <view class="list-item__left">
                收货人
            </view>

            <view class="list-item__right">
                <!-- 送达时间 -->
                <view class="list-item__right-title row-ellipsis">
                    {{ receiver.name }}

                    <!-- 收货人手机号 -->
                    <span wx:if="{{ receiver.mobile }}" class="receiver-mobile">
                      {{ filter.formatPhoneNumber_344(receiver.mobile) }}
                    </span>
                    <!-- 收货人手机号 -->
                </view>
                <!-- 送达时间 -->

                <!-- 收货地址 -->
                <view class="list-item__right-content row-ellipsis">
                  {{ receiver.province }} {{ receiver.city }} {{ receiver.hometown }} {{ receiver.address }}
                </view>
                <!-- 收货地址 -->
            </view>
        </view>
        <!-- 收货人 -->
      </view>

    </view>

  </view>
  <!-- 收件人/配送信息区域 -->

  <!-- 商品列表-->
  <template is="goods-section" data="{{ goodsInfoList, blessCardContent, formattedBlessCardContent, dispatchTimeRanage, orderIsFalse, overTimeText, QUESTION_ICON, titleInfo, orderType: 'b2c', hasMutiplePackage, isTimelyOrder }}"></template>
  <!-- 商品列表-->

  <!-- 月卡信息 -->
  <card-info wx:if="{{vipCardInfo.type}}" card-info="{{vipCardData}}"></card-info>
  <!-- 月卡信息 -->

  <!-- 充值信息 -->
  <card-info wx:if="{{rechargeData.title}}" card-info="{{rechargeData}}"></card-info>
  <!-- 充值信息 -->

  <!-- 金额优惠及减免信息列表 -->
  <template is="price-section" data="{{ addList, minusList, payment,vipCardInfo, vouchersPayAmount, totalGoodsAmount }}"></template>
  <!-- 金额优惠及减免信息列表 -->
    <!-- 及时达提示信息 -->
    <view class="pay-mode-box" wx:if="{{ orderStatus && orderStatus.orderStatusDesc !== '已取消' && orderStatus.orderStatusDesc !== '交易成功' && orderStatus.orderStatusDesc !== '待付款' }}">
        <text class="pay-mode-text" wx:if="{{noInvoiceDesc}}">{{noInvoiceDesc}}</text>
      <view wx:else>
          <text class="pay-mode-text" wx:if="{{dispatchTypeDesc === '自提配送'}}">*自提后可在订单详情页中开具电子发票</text>
          <text class="pay-mode-text" wx:else>*收货后可在订单详情页中开具电子发票</text>
      </view>
    </view>
  <!-- 心享会员优惠信息 -->
  <view
    wx:if="{{orderStatus.orderStatusDesc !== '已取消' && payment.freeInfoList && payment.freeInfoList.length}}"
    class="vip-section">
    <vip-section
      static-text
      tips="本单已省{{filter.formatPrice(payment.heartSaveAmount)}}元，了解更多省钱权益"
      btn="查看"
      bind:vipTipsClick="showVipDetail" />
  </view>
  <!-- 心享会员优惠信息 -->

  <!-- 订单信息列表 -->
  <template is="info-section" data="{{ orderNo, timeLine, otherOrderInfo }}"></template>
  <!-- 订单信息列表 -->

  <!--页脚按钮  -->
  <view wx:if="{{ btnList.length }}" class="footer-btn-list safe-area-inset-bottom">
    <view class="store-order-more-btn">
      <!-- 更多按钮组件 -->
      <order-detail-more-btn
        wx:if="{{ leftBtnList && leftBtnList.length }}"
        menuList="{{ leftBtnList }}"
        >
      </order-detail-more-btn>
      <!-- 更多按钮组件 -->
    </view>

    <block wx:for="{{btnList}}" wx:key="btnType" wx:for-item="btnItem">
      <view data-type="{{btnItem.btnType}}"
            data-orderInfo="{{item}}"
            class="{{btnItem.class}}"
            catchtap="handleBtn">
            <view
                wx:if="{{ btnItem.btnType === 'payImmediatelyBtn' && showCountDown }}"
                class="pay-timer-btn">
                立即支付{{laym}}:{{lays}}
            </view>
            <block wx:else>
                {{ btnItem.btnName }}
            </block>
      </view>
    </block>
  </view>
</view>
<!-- 全国送b2c订单详情 end -->

<!-- 底部占位空间，非及时达与试吃都展示 -->
<view wx:if="{{ !isTimelyOrder && isReady }}" class="page-bottom"></view>


<!-- 弹窗 -->
<view class='detail-mask' wx:if="{{isShowSuperVipDetail}}" bindtap="hideVipDetail" catchtouchmove="touchMove"></view>
<view class='xinxiang-detail' wx:if="{{isShowSuperVipDetail}}" catchtouchmove="touchMove">
  <view class='xinxiang-detail-title'>百果心享节省明细</view>
  <image src='/source/images/btn_close_gray.png' class='close-img' bindtap="hideVipDetail"></image>

  <view class="super-vip radius-16" >
    <vip-section
      static-text
      tips="本单已省{{filter.formatPrice(payment.heartSaveAmount)}}元，了解更多省钱权益"
      btn="查看"
      bind:vipTipsClick="jumpVipPage" />
  </view>
  <view>
    <view class='xinxiang-detail-item' wx:for="{{payment.freeInfoList}}" wx:key="index">
      <text>{{item.freeName}}</text>
      <text>省{{filter.formatPrice(item.freeValue)}}元</text>
    </view>
  </view>
</view>

<!-- 支付方式选择框 -->
<paymentDialog
  paymentDialog="{{paymentDialog}}"
  target="{{target}}"
  selectPagodaPay="{{pagodaMoney}}"
  selectWxPay="{{selectWxPay}}"
  selectUnionPay="{{selectUnionPay}}"
  disableUnionPay="{{disableUnionPay}}"
  hideUnionPay="{{hideUnionPay}}"
  mainBalance="{{mainBalance}}"
  rechargeText="{{rechargeText}}"
  mainBalanceIsNotEnough="{{lack}}"
  forbidPdPay="{{forbidPdPay}}"
  disablePay="{{disablePay}}"
  rechargePayInfo="{{rechargePayInfo}}"
  bind:hidePaymentWay="hidePaymentWay"
  bind:setWaySelect="setWaySelectHandle"
  bind:switchPagodaPayChange="switchPagodaPayChangeHandle"
  bind:payrightNow="payrightNow">
</paymentDialog>

<!-- 餐盒费明细  -->
<popup popupTitle="餐盒费明细" showTitleLine="true" isShowPopup="{{showPackDetail}}" selfClose="{{false}}" bindcloseShowPopup="closePackListPopu">
  <scroll-view class="pack-list" scroll-y="true">
    <view class="pack-list-item" wx:for="{{serviceList}}" wx:if="{{serviceList.length}}" wx:key="index">
      <view class="pack-item-desc text-hidden">{{item.goodsName}} {{item.serviceName}}</view>
      <view class="pack-item-value">¥{{filter.formatPrice(item.serviceAmount)}}</view>
    </view>
  </scroll-view>
  <view class="pack-count">
    <view class="pack-count-desc">合计</view>
    <view class="pack-count-value">¥{{filter.formatPrice(totalServiceAmount)}}</view>
  </view>
  <view class="pack-explain">
    <view class="pack-explain-desc">说明</view>
    <text class="pack-explain-value">需要切块和取肉装盒等服务的水果，每份需要收取餐盒费，收费情况见明细。</text>
  </view>
  <view class="close-tip-btn" bindtap="closePackListPopu">我知道了</view>
</popup>

<!-- 包装费说明 -->
<popup popupTitle="包装费说明" showTitleLine="{{false}}" isShowPopup="{{showPackFeeTip}}"  selfClose="{{false}}" bindcloseShowPopup="closeFeeTip">
  <scroll-view class="pack-list" scroll-y="true">
    <view class="pack-list-item">
      <view class="pack-item-desc">包装费</view>
      <view class="pack-item-value">¥{{filter.formatPrice(packAmount)}}</view>
    </view>
  </scroll-view>
  <view class="pack-count">
    <view class="pack-count-desc">合计</view>
    <view class="pack-count-value">¥{{filter.formatPrice(packAmount)}}</view>
  </view>
  <view class="pack-explain">
    <view class="pack-explain-desc">说明</view>
    <view class="pack-explain-value point">本单需要袋装才能配送，敬请谅解。</view>
    <view class="pack-explain-value point">根据国家“限塑令”相关法律法规及政策要求，百果园有义务推广使用可降解塑料袋且不得免费提供给消费者。感谢您与百果园一起为生态环境、公众安全保驾护航。</view>
  </view>
  <view class="close-tip-btn" catchtap="closeFeeTip">我知道了</view>
</popup>

<!-- 祝福卡内容 -->
<popup popupTitle="祝福语" showTitleLine="{{false}}" isShowPopup="{{blessPopupIsShow}}"  selfClose="{{true}}" bindcloseShowPopup="closeBlessPopup">
  <view class="bless-popup">
    <view class="bless-receiver">{{blessCardToName}}</view>
    <view class="bless-content">{{blessCardContent}}</view>
    <view class="bless-sender">{{blessCardFromName}}</view>
  </view>
  <view class="close-tip-btn" catchtap="closeBlessPopup">我知道了</view>
</popup>

<!-- 侧边红包 用cover-view代替view，原生组件canvas层级最高，真机上会遮挡红包-->
<red-packet
  wx:if="{{ !orderCancelIsShow && (showRedEnvelope || fissionActivityInfo) }}"
  redEnvelopeInfo="{{ redEnvelopeInfo }}"
  hongBaobg="{{ hongBaobg }}"
  bindshare="sensorsRed"
  bindnavigate="redPacketNavigate"
  >
</red-packet>

<!--取消订单原因弹窗-->
<order-cancel wx:if="{{orderCancelIsShow}}" showedCancelTitle="{{showedCancelTitle}}" bind:select="onOrderCancelSelected" orderSource="{{orderSource}}" bind:close="onOrderCancelClosed"></order-cancel>
<!-- 退款失败原因弹窗 -->
<template is="refundRejectReasonModel" wx:if="{{isShowRefundReason}}" data="{{refundRejectReason}}" />
<!-- 蒙层 -->
<template is="mask" data='{{prevent}}' />

<confirm-modal
  titleText="{{modalContent.title}}"
  contentText="{{modalContent.text}}"
  isShowConfirmModal="{{ showCustomConfirmModal }}"
  showCustomBtn="{{true}}">
  <button size="mini" plain="true" slot="leftBtn" class="confirm-btn" bindtap="customConfirmModalCancel">取消</button>
  <button size="mini" plain="true" slot="rightBtn" class="confirm-btn" bindtap="handleOnlineService">在线客服</button>
</confirm-modal>

<user-error-modal isShow="{{showUserErrorModal}}"></user-error-modal>

<canvas canvas-id="barcode" class="barcode-canvas"/>

<!-- 验证码弹窗组件 -->
<pagoda-popup
  model:visible="{{visibleSMS}}"
  showLeftArrow="{{true}}"
  showClose="{{false}}"
  round="{{true}}"
  z-index="{{2000}}"
  clickOverlayClose="{{false}}"
  position="bottom"
  title="更换支付方式"
  head-class="sms-head-class"
  title-class="sms-title-class"
  height="600rpx"
  bind:onBack="onBack">
    <sms-validate
      model:visible="{{visibleSMS}}"
      bind:validated="validated"
    />
</pagoda-popup>

<!-- 全国送未全部发货状态下确认收货弹窗 -->
<confirm-modal
  contentText="{{ b2cConfirmModalText }}"
  isShowConfirmModal="{{ showB2cConfirmModal }}"
  contentTextColor="#000000a6"
  contentTextSize="24"
  showCustomBtn="{{true}}">
  <view class="confirm-title" slot="title">部分商品尚未发货，您可联系客服<text bindtap="handleCustomerService">400-181-1212</text>了解详情。</view>
  <button size="mini" plain="true" slot="leftBtn" class="confirm-btn" bindtap="closeB2cConfirmModel">我知道了</button>
  <button size="mini" plain="true" slot="rightBtn" class="confirm-btn b2c-btn-confirm" bindtap="handleB2cConfirm">确认收货</button>
</confirm-modal>

<!-- 1. 全国送待收货时点击三无退按钮触发弹窗 -->
<!-- 2. 门店订单完成换货点击已退货按钮弹窗触发 -->
<confirm-modal
  contentTextAlign="center"
  showCancel="{{false}}"
  clickMaskToClose="{{false}}"
  confirmText="我知道了"
  isShowConfirmModal="{{ showTip }}"
  bindconfirm="toggleTip">
  <view slot="content">
    <text>{{ tipText }}</text>
  </view>
</confirm-modal>

<!-- 门店订单转配送确认弹窗 -->
<confirm-modal
  contentTextAlign="center"
  isShowConfirmModal="{{ offlineDeliveryAddressSubmit.showConfirm }}"
  clickMaskToClose="{{false}}"
  bind:confirm="onOfflineDeliveryAddressConfirm">
  <view slot="content">
    <view class="offline-delivery-address-title">是否使用该地址进行配送？</view>
    <view class="offline-delivery-address-title">提交后无法更改，请谨慎操作</view>
  </view>
</confirm-modal>

<!-- 退款原因弹窗 -->
<!-- 当及时达时+配送时 或者 是全国送时 isDelivery为true -->
<reason-confirm-modal
  wx:if="{{ visibleReasonConfirmModal }}"
  popupTitle="请问您遇到了什么问题"
  desc="很抱歉给您带来不好的体验"
  confirmText="去三无退货"
  isDelivery="{{ (isTimelyOrder && dispatchType.code === 10) || isB2COrder }}"
  showModal="{{ showReasonConfirmModal }}"
  reasonType="{{ isDmOrder ? 3 : 100 }}"
  bindconfirm="reasonConfirmModalSubmit"
  bindclose="closeReasonConfirmModal"
  bindreasonLoaded="reasonLoaded"
/>

<common-loading />
<captcha id="comp-captcha"/>
