const { toHttps } = require('../../../../utils/util');
const sensors = require('../../../../utils/report/sensors')

const app = getApp();

Component({
  properties: {
    addressInfo: {
      type: Object,
      value: {
        cityID: -1,
        storeID: -1,
        cityCode: '',
        storeCode: '',
        deliveryCenterCode: '',
        supportBToCService: false
      }
    },
  },
  data: {
    goodsList: [],
    avatarList: [],
    saleCount: 0,
  },
  lifetimes: {
    attached() {
      this._data = {
        extendObj: {},
        titleIndex: 0,
        titleArr: ['接龙特惠', '热销爆款', '当季热卖'],
      }
    }
  },
  methods: {
    toRelayhome() {
      sensors.clickReport('1800_180026001', this._data.extendObj)
      wx.navigateTo({
        url: '/relay/pages/home/<USER>',
      })
    },
    hideEntry() {
      this.setData({
        goodsList: [],
      })
    },
    async init() {
      this._data.titleIndex = 0
      const activityList = await this.getRelayEntry()
      if (!activityList.length) {
        this.hideEntry()
        return
      }
      // const [avatarList, { total: saleCount }] = await Promise.all([
      //   this.getAvatarList(activityList),
      //   this.getRelaySalesInfo(activityList),
      // ])
      // const avatarListAfterSlice = avatarList.slice(0, 3)
      // this.setData({
      //   avatarList: avatarListAfterSlice.length !== 3 ? [] : avatarListAfterSlice,
      //   saleCount,
      // })
    },
    exposureActivity() {
    },
    getTitle() {
      const index = this._data.titleIndex++ % this._data.titleArr.length
      return this._data.titleArr[index]
    },
    caclDiscountText({ goodsPricePer, goodsPrice }) {
      if (goodsPricePer >= goodsPrice) {
        return this.getTitle()
      }

      const discount = goodsPricePer / goodsPrice;

      if (discount <= 0.85) {
          // 处理低至xx折逻辑
          const discountRate = discount * 10; // 转换为百分比形式
          const roundedRate = Math.ceil(discountRate * 10) / 10; // 向上取1位小数
          const formattedRate = roundedRate % 1 === 0 
              ? roundedRate.toFixed(0)
              : roundedRate.toFixed(1);
          return `低至${formattedRate}折`;
      } else {
          // 处理立省xx元逻辑
          const saveCents = goodsPrice - goodsPricePer;
          const saveYuan = saveCents / 100;
          const truncated = Math.floor(saveYuan * 10) / 10; // 抹零处理保留1位小数
          
          const formattedSave = truncated % 1 === 0 
              ? truncated.toFixed(0) 
              : truncated.toFixed(1);
          return `立省${formattedSave}元`;
      }
    },
    async getRelayEntry() {
      const { storeCode } = this.data.addressInfo
      if (!storeCode) { return [] }
      const { data: { activityNames = [], goodsList = [], activityCodes = [] } } = await app.api.getRelayEntry({ storeCode }).catch(() => ({ data: {} }))
      this._data.extendObj = {
        activity_Name: activityNames.join(','),
        activity_ID: activityCodes.join(','),
      }
      activityCodes.length && sensors.exposureReport('1800_180026001', this._data.extendObj)
      this.setData({
        activityNames,
        activityCodes,
        goodsList: goodsList.map(item => ({
          ...item,
          goodsPictures: toHttps(item.goodsPictures),
          discountText: this.caclDiscountText(item),
        })),
      })
      return activityCodes
    },
    getAvatarList(activityList) {
      return app.api.getRelayAvatar({
        activityList,
      }).then(({ data }) => data || []).catch(() => ([]))
    },
    getRelaySalesInfo(activityList) {
      return app.api.getRelaySalesInfo({
        activityList,
      }).then(({ data }) => data || { total: 0 }).catch(() => ({ total: 0 }))
    }
  },
})
