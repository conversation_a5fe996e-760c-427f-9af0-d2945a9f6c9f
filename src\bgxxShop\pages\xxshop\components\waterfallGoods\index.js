import {
  storeBindingsBehavior
} from 'mobx-miniprogram-bindings'
import tabbarStore from 'store/index'
const app = getApp()
// const act = require('~/utils/activity')
const sensors = require('~/utils/report/sensors')
const {
  prdUrl,
  baseUrl
} = require('~/utils/config');
import styleConfig from '~/utils/goodsStyleConfig'
import { allTrue, defaultVal } from '~/utils/cyclomatic'

// 底部tabbar的高度
const TABBAR_HEIGHT = 48
// 请求数据的最小有效条数
const LOAD_MIN_NUM = 6
// 插入广告的位置
const INSERT_ADVERTISEMENT_INDEX = [0, 5, 8, 13, 16, 21, 24, 29]
const {
  FreshGoods
} = require('~/service/freshGoods')
const activity = require('~/utils/activity')
const adExposureMap = {}
Component({
  behaviors: [storeBindingsBehavior],
  storeBindings: {
    store: tabbarStore,
    fields: {
      active: "active",
      tabbarList: "tabbarList",
    },
    actions: {
      updateTabbar: 'updateTabbar',
      updateActive: 'updateActive'
    },
  },
  properties: {
    userInfo: {
      type: Object,
      value: {
        customerID: -1
      }
    },
    addressInfo: {
      type: Object,
      value: {
        cityID: -1,
        storeID: -1,
        cityCode: "",
        storeCode: "",
        deliveryCenterCode: ""
      }
    },
    // 滚动吸顶的距离
    distanceToTop: {
      type: Number,
      value: 0
    }
  },

  data: {
    containerHeight: 0,
    picDomain: baseUrl.PAGODA_PIC_DOMAIN,
    noGoodsImage: `${prdUrl.PAGODA_PIC_DOMAIN}/group1/M21/60/78/CmiWa2FUUXmAZgcQAAAsIbZOqXM880.png`, // 商品缺省图
    commonGoodsType: 'double',
    tabs: [],
    styleConfig: styleConfig,
    isFixed: false, // 是否吸顶
    isLoading:true, // 是否加载中
    adExposureMap: {},
    /**是否显示瀑布流 */
    isShowWaterfall: false,

    /**是否展示售罄商品折叠按钮 */
    isShowWaterfallFold: false,
    /**售罄商品是否已展开 */
    isOpenWaterfallFold: false,
    /**售罄商品数量 */
    selloutCount: 0,
  },
  _data: {
  },
  observers: {
    addressInfo(address) {
      if (!address || !address.selectAddressInfo) {
        return
      }
      if(JSON.stringify(address) === JSON.stringify(this._data.address)){
        return
      }
      this._data.address = address
      // console.log('地址变了',this._data.address,address)
      this.inintWaterfalltabbar()
    },
    tabsList(list) {
      this.initTabs(list)
    },
    active(idx) {
      if (!idx && idx !== 0) return
      if (!this.data.tabs || !this.data.tabs.length) return
      this.resetSelloutStatus()
      this.resetGoodsData(idx)
      this.getGoodsData()
      this.scrollToTop()
    },
    isShowWaterfall(isShow) {
      if (!isShow) {
        return
      }
      if (this._data.initedRect) {
        return
      }
      wx.nextTick(() => {
        this.initWaterfallSize()
        this.waterfallTabbarObserver()
        this._data.initedRect = true
      })
    },
    isFixed(val) {
      this.triggerEvent('fixed', val)
    },
    tabs(val){
        // console.log('tabs',val[0])
    }
  },

  lifetimes: {
    created() {
      this._data = {
        requestDataFlag: false,
        requestDataID: 0,
        storageObj: {},
        initedRect: false,
        initPromise: (function() {
          let resolveFn
          const promise = new Promise(resolve => (resolveFn = resolve))
          return { resolve: resolveFn, promise }
        })()
      }
    },
    attached() {
      // 注册当前组件下拉刷新时需要刷新
      this.triggerEvent('registerRefreshComp', {
        name: 'waterfallGoods'
      })
    },
    detached() {
      ['tabbarObserve'].forEach(name => {
        const ob = this._data[name]
        ob && ob.disconnect()
      })
    }
  },

  methods: {
    /**
     * 切换tab后，重置各tab的数据
     */
    resetGoodsData(idx) {
      // 取消请求正在请求的接口
      const {
        goodsRequestObj
      } = this._data
      if (goodsRequestObj && goodsRequestObj.requestTask && typeof goodsRequestObj.requestTask.abort === 'function') {
        goodsRequestObj.requestTask.abort()
      }
      // 重置数据
      const {
        tabID
      } = this.data.tabs[idx]
      const tabs = this.data.tabs.map(item => Object.assign(item, {
        goodsList: [
          [],
          []
        ],
        isLoadDone: false
      }))
      this.setData({
        tabs,
        containerHeight: this.data.minHeight
      })
      this._data.storageObj = {
        [tabID]: {
          setDataPage: 1
        }
      }
      this.updatePageInfo(tabID, {}, true)
    },

    /**
     * 初始化瀑布流商品列表
     */
    initTabs(list) {
      // 重新初始化
      if (!list.length) {
        this.setData({
          tabs: []
        })
        return
      }
      // 为了避免初始化active或者下拉刷新时，同个active值不会触发store更新，需先将active置为空
      this.updateActive(null)
      this.setData({
        tabs:list
      })
      this.updateActive(0)
    },

    /**
     * 重置售罄信息
     */
    resetSelloutStatus() {
      this.setData({
        /**是否展示瀑布流售罄商品折叠按钮 */
        isShowWaterfallFold: false,
        /**售罄商品是否已展开 */
        isOpenWaterfallFold: false,
        /**售罄商品数量 */
        selloutCount: 0,
      })
    },

    /**
     * 初始化瀑布流tabbar
     */
    async inintWaterfalltabbar() {
      let tabsList = await this.getWaterfallTabsData()
      // tabbar标题支持显示图片
      tabsList = tabsList.map(item => {
        const {
          titlePicDefault = {}, titlePicSelected = {}
        } = item
        if (!!Object.keys(titlePicDefault).length && !!Object.keys(titlePicSelected).length) {
          const setObj = {
            titlePicDefault: titlePicDefault.picUrl,
            titlePicSelected: titlePicSelected.picUrl,
            hasTitlePic: true
          }
          return {
            ...item,
            ...setObj
          }
        }
        return item
      })
      const isShowWaterfall = !!tabsList.length
      this.setData({
        tabsList,
        isDeterminedWidth: tabsList.length > 4, // 少于4个tab平分屏幕宽度，超过4个tab，则tab定宽
        isShowWaterfall,
      })
      this.updateTabbar(tabsList)
      isShowWaterfall && !this._data.initedRect && await this._data.initPromise.promise
      this.triggerEvent('inited', {
        tabsList
      })
    },

    /**
     * 获取瀑布流tabbar(仅获取接口数据)
     */
    async getWaterfallTabsData() {
      const {
        selectAddressInfo: {
          cityCode,
        } = {}
      } = this.data.addressInfo || {}
      const {
        storeCode
      } = app.globalData.bgxxCityInfo || {}
      const params = {
        cityCode,
        storeCode
      }
      if (!cityCode) return []
      try {
        const {
          data = []
        } = await app.api.getWaterFallTabbar(params)
        return data
      } catch (error) { }
      return []
    },
    // 获取品类商品
    async getCategoryGoodsList(params) {
      try {
        const res = await app.api.getFreshCategoryGoodsList(params)
        return { 
          data: {
            goodsList: res.data,
            page: {
              totalPages: 0,
              page: 0
            }
          }
        }
      } catch(err) {
        return {
          data: {
            goodsList: [],
            page: {
              totalPages: 0,
              page: 0
            }
          }
        }
      }
    },
    /*
     * 请求商品列表数据
     */
    async getGoodsData() {
      const { data } = this
      const {
        userInfo: {
          customerID = -1
        },
        addressInfo: {
          selectAddressInfo: {
            cityID = -1,
            cityCode,
            deliveryCenterCode
          }
        },
        active
      } = data

    const {
      storeCode
    } = defaultVal(app.globalData.bgxxCityInfo, {})
      const {
        tabID,
        tabType,
        goodsList:currGoodsList,
      } = data.tabs[active]
      const currList = defaultVal(currGoodsList, [[],[]]) // 当前已经setData的商品列表
      // 请求瀑布流商品的接口
      this._data.requestDataFlag = true
      this._data.requestDataID = Number.parseInt(Math.random() * 1000)
      const requestDataID = this._data.requestDataID
      try {
        const params = {
          customerID,
          cityID,
          cityCode,
          deliveryCenterCode,
          tabID,
          tabType: tabType,
          pageNumber: defaultVal(this._data.storageObj[tabID].pageInfo.pageNumber, 1),
          pageSize: 20,
          storeCode
        }
        const res = tabType !== '999' ? await app.api.getFreshWaterFallGoodsList(params) : await this.getCategoryGoodsList({
          deliveryCenterCode,
          cityCode,
          categoryCodeList: [tabID]
        })
        // 请求瀑布流商品的接口
        this._data.requestDataFlag = false
        // 防止在下拉刷新时,旧请求重复执行setData,导致插入到旧请求数据插入到现有的数量中
        if (this._data.requestDataID !== requestDataID) { return }
        const {
          goodsList: configGoodsList = [],
          adList: recommendAdData,
          page: {
            totalPages,
            page: pageNumber
          }
        } = defaultVal(res.data, {})
        let recommendAd = defaultVal(recommendAdData, [])
        recommendAd.forEach( (ad) => {
          ad.goodsType = 'ad'
        })
        const complateGoodsList = await new FreshGoods({
          storeCode,
          cityCode,
          deliveryCenterCode
        }).getGoodsComplateInfoList(configGoodsList, { filterGift: true, filterNewCustomer: true, filterSaleStatus: true })
        // console.log(complateGoodsList)
        let sellOutGoodsList = []
        let recommendGoodsMoudelList = []
        complateGoodsList.forEach( goods => {
          if (goods.stock) {
            recommendGoodsMoudelList.push(goods)
          } else {
            sellOutGoodsList.push(goods)
          }
        })

        // console.log(recommendGoodsMoudelList)
        // 在售商品先做一层去重处理
        recommendGoodsMoudelList = recommendGoodsMoudelList.reduce((pre,item)=>{
             const equalObj =  pre.find((preItem)=> preItem.id === item.id)
             if(!equalObj){
                    pre.push(item)
             }
             return pre
        },[])
        //  售罄商品先做一层去重处理
        sellOutGoodsList = sellOutGoodsList.reduce((pre,item)=>{
             const equalObj =  pre.find((preItem)=> preItem.id === item.id)
             if(!equalObj){
                    pre.push(item)
             }
             return pre
        },[])
        // 第一个tab下的商品打平数组
        const allGoodsList = this.data.tabs[0].goodsList.flat(Infinity)
        for (let i = 0; i < allGoodsList.length; i++) {
            // 再去重在售的
            for (let j = 0; j < recommendGoodsMoudelList.length; j++) {
                if (String(allGoodsList[i].id) === String(recommendGoodsMoudelList[j].id)) {
                    recommendGoodsMoudelList.splice(j, 1);
                }
            }
        }
        let goodsList = defaultVal(recommendGoodsMoudelList, []) // 下一页的商品列表

        if(allTrue(Number(tabType) === 6, Number(pageNumber) === 1)){
          const tabskey = 'tabsList[0].adList'
          this.setData({
              [tabskey]:recommendAd
          })
        }
        // 如果是第一个tab 那就拼接图片广告位
        recommendAd = active === 0 ? recommendAd : []
        if(Number(tabType) === 6){
          goodsList = this.insertAdList({
            currList,
            newList: recommendGoodsMoudelList,
            adList: this.data.tabsList[0].adList,
            insertIdxs:INSERT_ADVERTISEMENT_INDEX
          })
        }
      // 拼接售罄商品列表
      if (sellOutGoodsList.length) {
        const currSoldOutList = defaultVal(this._data.storageObj[tabID].soldOutGoodsList, [])
        this._data.storageObj[tabID].soldOutGoodsList = [...currSoldOutList, ...sellOutGoodsList]
      }
      // 设置商品列表
      this.setGoodsListData({
        active,
        tabID,
        currList,
        newList: goodsList,
        isFirtTabFirstPage: pageNumber === 1
      })
        // 第一页，更新总页数
        pageNumber === 1 && this.updatePageInfo(tabID, { totalPage: totalPages || 0 })
        if(allTrue(totalPages === pageNumber, this._data.storageObj[tabID].soldOutGoodsList.length === 0)){
            const tabs = `tabs[${active}].isLoadDone`
            this.setData({
                 [tabs] :true
              })
        }
        // 加载的有效数据少于10条，自动加载下一页
        this.isPreloadNextPageData(tabID, recommendGoodsMoudelList, LOAD_MIN_NUM) && this.loadNextPageData()
      } catch (error) {
        console.log(error)
        const tabs = `tabs[${active}].isLoadDone`
        this.setData({
          [tabs] :true
        })
      }
    },

    /**
     * 设置商品列表数据
     */
     setGoodsListData ({active, tabID, currList, newList, isFirtTabFirstPage }) {
      if (!newList.length) return this.hideLoading()
      // 获取商品买赠标签
      this.triggerEvent('getGoodsActivitys', {
        allGoods:newList
      })
      // 商品列表分两列
      const { col1, col2 } = this.separateGoodsList(currList, newList)
      const currentPage = this._data.storageObj[tabID].setDataPage++
      const key1 = `tabs[${active}].goodsList[${0}][${currentPage - 1}]`;
      const key2 = `tabs[${active}].goodsList[${1}][${currentPage - 1}]`;
      if (isFirtTabFirstPage) {
        const firstCol1 = col1.slice(0, 2)
        const firstCol2 = col2.slice(0, 2)
        this.setData({
          [key1]: firstCol1,
          [key2]: firstCol2,
        })

        this.setData({
          [key1]: col1,
          [key2]: col2,
        })

      } else {
        this.setData({
          [key1]: col1,
          [key2]: col2,
        })
      }
      this.hideLoading()
      // 在当前同步流程结束后，下一个时间片执行
      wx.nextTick(() => {
        this.refreshRectSize()
        this.observerAdNode()
      })
    },
    /**
     * 监听广告位出现在可视区域中
     */
    observerAdNode() {
      if (this.observerAd) {
        this.observerAd.disconnect()
      }

      const query = '.ad-item';
      const observer = this.createIntersectionObserver({
        observeAll: true,
      }).relativeToViewport({
        bottom: 0,
      })

      observer.relativeToViewport().observe(query, res => {
        const {
          intersectionRatio,
          dataset: {
            item
          },
        } = res

        if (intersectionRatio !== 0) {
          //  此广告位未曾曝光，执行曝光上报埋点
          if (!adExposureMap[item.id]) {
            adExposureMap[item.id] = true
            this.adExposure(item)
          }
        }
      })

      this.observerAd = observer
    },
    
    /**
     * 广告位曝光
     * @param {*} banner 
     */
    adExposure(banner) {
      sensors.adExposure({
        banner_id: banner.id || '',
        banner_name: banner.name || '',
        Position: banner.positionIndex,
        element_code: 1130025001,
      })
    },

    /**
     * 商品列表分成两列（瀑布流展示需要）
     */
     separateGoodsList (currList, newList) {
      const getLength = (total, item) => total + item.length
      const colLen1 = currList[0].reduce(getLength, 0)
      const colLen2 = currList[1].reduce(getLength, 0)
      const diff = colLen1 > colLen2 ? 1 : 0
      let col1 = []
      let col2 = []
      newList.forEach((val, index) => {
        const key = (index + diff) % 2
        val.positionIndex = colLen1 + colLen2 + index
        key === 0 ? col1.push(val) : col2.push(val)
      })
      return {col1, col2}
    },

    /**
     * 页面触底事件
     */
    onReachCallBack() {
      //  已展示瀑布流售罄商品折叠按钮
      if (this.data.isShowWaterfallFold) {
        //  并且已打开折叠按钮，此时可触底自动加载
        if (this.data.isOpenWaterfallFold) {
          this.loadNextPageData()
        }
      } else {
        this.loadNextPageData()
      }
    },

    /**
     * 加载下一页数据
     * 1. 加载tab商品
     * 2. tab商品加载完后，展示折叠按钮
     * 3. 展开折叠按钮，继续加载售罄商品
     */
    loadNextPageData() {
      // 瀑布流不展示时不触发下拉加载（主要是为了避免组件的父页面数据更新没有用户下拉操作快，瀑布流不展示了还继续触发了下拉加载）
      if (!this.data.isShowWaterfall) {
        return
      }
      const {
        active
      } = this.data
      const {
        tabType,
        tabID,
        goodsList: currGoodsList,
        isLoadDone,
        adList:recommendAd = []
      } = this.data.tabsList[active]
      const { pageNumber, totalPage } = this._data.storageObj[tabID].pageInfo
      // 当前tab数据加载完毕，如果有接口正在请求，直接返回
      if (defaultVal(isLoadDone, this._data.requestDataFlag)) return
      this.showLoading()
      // 加载下一页商品数据
      if (pageNumber < totalPage) {
          this.updatePageInfo(tabID, { pageNumber: pageNumber + 1 })
          this.getGoodsData()
          return
      }

      if (allTrue(Number(tabType) === 6, !this._data.storageObj[tabID].loadStoreGoods)) {
        this._data.storageObj[tabID].loadStoreGoods = true
        this.storagePreviousGoods()
        this.updatePageInfo(tabID, {}, true)
        // this.getGoodsData()
        return
      }

      //  未曾展示售罄折叠按钮，计算在售商品数量
      if (!this.data.isShowWaterfallFold) {
        /**
         * 当前在售商品数量
         */
        this.onsaleGoodsCount = currGoodsList.reduce(((prev, current) => {
          return prev + current.reduce((p, c) => p + c.length, 0)
        }), 0)
      }

      //  最后加载售罄商品
      const soldOutGoodsList = defaultVal(this._data.storageObj[tabID].soldOutGoodsList, [])

      if (soldOutGoodsList.length) {
        let newGoodsList

        /**当前展示售罄商品的数量：在售商品为偶数时，展示2个售罄商品，为奇数时，展示1个售罄商品 */
        const showSelloutCount = this.onsaleGoodsCount % 2 === 0 ? 2 : 1
        /**可展示售罄折叠按钮 */
        const canShowToggleBtn = soldOutGoodsList.length > showSelloutCount

        //  满足折叠隐藏的情况
        if (canShowToggleBtn) {
          //  未展示售罄折叠按钮时，将可展示的售罄商品拼接到瀑布流展示
          if (!this.data.isShowWaterfallFold) {
            this.setData({
              selloutCount: soldOutGoodsList.length - showSelloutCount,
              isShowWaterfallFold: true,
            })

            newGoodsList = soldOutGoodsList.splice(0, showSelloutCount)

            soldOutGoodsList.forEach(current => {
              current.isSellOutGoods = true
            })
          }
          //  已展示售罄商品折叠按钮时，拼接剩余商品到瀑布流展示
          else {
            newGoodsList = soldOutGoodsList.splice(0, 20)
          }
        }
        //  售罄商品不足以折叠的情况，直接全部展示
        else {
          newGoodsList = soldOutGoodsList.splice(0, soldOutGoodsList.length)
        }

        // 第一个tab商品全部售罄，第一个位置放广告位
        if (allTrue(Number(tabType) === 6, !currGoodsList[0].length, recommendAd, recommendAd.length)) {
          newGoodsList.unshift(recommendAd[0])
        }

        this.setGoodsListData({
          active,
          tabID,
          currList: defaultVal(currGoodsList, [[],[]]),
          newList: newGoodsList,
        })
        this.hideLoading()
        return
      }
      this.hideLoading()
      this.loadDataDone()
    },

    /**
     * 把第一个tab列表存储起来，供门店所有商品接口去重
     */
    storagePreviousGoods() {
      const { active } = this.data
      const { tabID } = this.data.tabbarList[active]
      const soldOutGoodsList = this._data.storageObj[tabID].soldOutGoodsList || []
      const goodsList = this.data.tabbarList[active].goodsList || [[], []]
      const getList = (totalList, item) => totalList.concat([...item])
      const col1 = goodsList[0].reduce(getList, [])
      const col2 = goodsList[1].reduce(getList, [])
      this._data.storageObj[tabID].previousGoods = [].concat(col1, col2, soldOutGoodsList).map(item => item.goodsSn)
    },
    /**
     * 累计预加载的数据条数，少于一定数目，自动加载下一页数据
     */
    isPreloadNextPageData(tabID, list, count) {
      const preloadLength = this._data.storageObj[tabID].preloadLength || 0
      if (preloadLength + list.length < count) {
        this._data.storageObj[tabID].preloadLength = preloadLength + list.length
        return true
      }
      this._data.storageObj[tabID].preloadLength = 0
      return false
    },

    /**
     * 展示加载中的loading
     */
    showLoading() {
      if (this.data.isLoading) return
      this.setData({
        isLoading: true
      })
    },

    /**
     * 隐藏加载中的loading
     */
    hideLoading() {
      if (!this.data.isLoading) return
      this.setData({
        isLoading: false
      })
    },

    /**
     * 当前tab数据加载完
     */
    loadDataDone() {
      const {
        active
      } = this.data
      const key = `tabs[${active}].isLoadDone`;
      if (this.data.tabs[active].isLoadDone) return
      this.setData({
        [key]: true
      })
    },

    /**
     * 更新页码信息
     */
    updatePageInfo(tabID, pageInfo = {}, init = false) {
      const that = this
      if (init) {
        that._data.storageObj[tabID].pageInfo = {
          pageNumber: 1,
          pageSize: 10,
          totalPage: 0
        }
        return
      }
      that._data.storageObj[tabID].pageInfo = Object.assign(that._data.storageObj[tabID].pageInfo, pageInfo)
    },

    /**
     * 左右滑动切换tab
     */
    switchTab(e) {
      const {
        current,
        source
      } = e.detail
      if (!!source && source === "touch") {
        this.updateActive(current)
      }
    },

    /**
     * 瀑布流tabbar切换，商品列表滑动到顶部
     */
    scrollToTop() {
      this.data.isFixed && this.triggerEvent('scrolltop', {
        id: 'scroll-flag-waterfall'
      })
    },
    /**
     * 为你优选需要插入广告
     */
    insertAdList ({ currList, newList, adList, insertIdxs }) {
           const that = this
           let adverList = adList
           let insertIndexs = insertIdxs
           // 商品列表为空
           if (!newList.length) {
             return [];
            }
            // 没有广告或者没有插入index
            if (!adverList.length || !insertIndexs.length) {
              return newList;
            }
            // 前面分页的商品列表的长度（包括广告）
            const currGoodsList = currList || [[],[]]
            const getLength = (total, item) => total + item.length
            const colLen1 = currGoodsList[0].reduce(getLength, 0)
            const colLen2 = currGoodsList[1].reduce(getLength, 0)
            const currLength = colLen1 + colLen2
            // 插入到第几个广告
            const cutIdx = insertIndexs.findIndex(item => item >= currLength)
            // 广告已经插完了
            if (cutIdx === -1 || cutIdx >= adverList.length) {
              return newList;
            }
            // 裁剪新的广告列表和插入index
            if (cutIdx !== 0) {
            adverList = adverList.slice(cutIdx)
            insertIndexs = insertIndexs.slice(cutIdx).map(item => item - currLength)
          }
          // 插入广告
          return that.insertOnLoop(newList, adverList, insertIndexs)
        },

        /**
         * @description 插入广告循环
         * @param array1 商品列表
         * @param array2 广告列表
         * @param insertIndexs 插入广告的index
         * 把array2有序插入array1，每间隔 num（insertIndexs固定的次序） 个插入一次，直到插入完毕
         */
        insertOnLoop (array1, array2, insertIndexs) {
          let startIdx = 0;
          let newArray = [];
          for (var i = 0; i < insertIndexs.length; i++) {
            const length = i > 0 ? insertIndexs[i] - insertIndexs[i - 1] - 1 : insertIndexs[i];
            const endIdx = Math.min(startIdx + length, array1.length);
            const sliceArray1 = length !== 0 ? array1.slice(startIdx, endIdx) : [];
            newArray.push(...sliceArray1)
            // 达到足够的商品数（并且不是最后一个）才插入广告
            if (endIdx === startIdx + length && !!array1[endIdx]) {
              newArray.push(array2[i])
            }
            startIdx = endIdx;

            // 商品或者广告，其中一个已经完了，结束插广告的循环
            if (endIdx === array1.length || i === array2.length - 1) {
              break;
            }
          }
          // 广告插完了，还剩余商品，则拼接剩余的商品
          if (startIdx !== array1.length) {
            newArray.push(...array1.slice(startIdx, array1.length))
          }
          return newArray;
        },
    /**
     * 动态设置商品列表容器的高度
     */
    async refreshRectSize() {
      const {
        active
      } = this.data
      const bottomTipHeight = await this.getBottomTipHeight()
      const sellOutItemHeight = this.data.isShowWaterfallFold ? 32 : 0
      const query = wx.createSelectorQuery().in(this)
      query.selectAll('.goods-container').boundingClientRect(res => {
        this.setData({
          containerHeight: Math.ceil(res[active].height) + bottomTipHeight + sellOutItemHeight
        })
      }).exec()
    },

    /**
     * 初始化商品列表容器的高度
     */
    initWaterfallSize() {
      const {
        windowHeight
      } = wx.getStorageSync('systemInfo') || {}
      const {
        distanceToTop
      } = this.data
      const query = wx.createSelectorQuery().in(this)
      query.select('#navbar').boundingClientRect(res => {
        // 瀑布流容器高度 = 可使用窗口宽度 - 吸顶距离 - 瀑布流tabbar - 底部tabbar
        const containerHeight = parseInt(windowHeight - distanceToTop - Math.floor(res.height) - TABBAR_HEIGHT)
        this.setData({
          minHeight: containerHeight
        }, this._data.initPromise.resolve)
      }).exec()
    },

    /*
     * 获取底部加载tips的高度
     */
    getBottomTipHeight() {
      if (this._data.bottomTipHeight) {
        return this._data.bottomTipHeight
      }
      return new Promise((resolve, reject) => {
        const query = wx.createSelectorQuery().in(this)
        query.select('.bottom-tip').boundingClientRect(res => {
          const {
            height = 30
          } = res || {}
          this._data.bottomTipHeight = Math.ceil(height)
          resolve(this._data.bottomTipHeight)
        }).exec()
      })
    },

    /**
     * 监听瀑布流tabbar滚动到顶部
     */
    waterfallTabbarObserver() {
      console.log("监听瀑布流是否fixed")
      const {
        windowHeight
      } = wx.getStorageSync('systemInfo') || {}
      const {
        distanceToTop
      } = this.data
      this._data.tabbarObserve && this._data.tabbarObserve.disconnect()
      const tabbarObserve = wx.createIntersectionObserver(this)
      tabbarObserve.relativeToViewport({
        bottom: distanceToTop - windowHeight
      }).observe('.waterfall-container', res => {
        // 瀑布流曝光埋点
        if (res.intersectionRatio !== 0) {
          sensors.goodsExpose('showWaterfall')
        }
        this.setData({
          isFixed: res.intersectionRatio !== 0
        })
      })
      this._data.tabbarObserve = tabbarObserve
    },
    /**
     * 组件业务逻辑更新
     */
    refresh() {
      return this.inintWaterfalltabbar()
    },

    /**
     * 售罄商品展开/收起
     * @param {*} isShow
     */
    selloutIsShow(event) {
      const isShow = event.detail

      this.setData({
        isOpenWaterfallFold: isShow,
      })
      this.refreshRectSize()
      if (isShow) {
        this.loadNextPageData()
      }
    },

    /**
     *
     */
    beforeAddCart(item) {
      const clickType = item.currentTarget.dataset.fallwater || ''
      if (clickType === "fallwater") { // 瀑布流商品点击
        sensors.track('MPClick', 'bgxxHomeWaterFallGoodsAdd')
      }
      this.triggerEvent('beforeAddCart', item.detail)
    },
    /**
     * 打开详情
     */
    openDetail(item) {
      let detail = item.type === 'openDetail' ? item.detail : item
      const clickType = item.currentTarget.dataset.fallwater || ''
      this.reportSensor({
          element_code: '1130004002',
          element_name: '首页推荐商品商品图',
          element_content: '首页推荐商品商品图',
          spu_id: detail.spuNumber || '',
          SKU_ID: detail.number || ''
      })
      if(clickType === "fallwater"){ // 瀑布流商品点击
          sensors.track('MPClick', 'bgxxHomeWaterFallGoodsClick')
          sensors.track('ClickGoods', 'bgxxHomeWaterFallGoodsClickGoods', {
            SKU_ID: detail.goodsSn,
            SKU_Name: detail.goodsName,
            takeawayAttr: '次日达',
            saleStatus: '门店上架',
            availableQuantity: detail.stock,
            goodsPrice: (detail.retailPrice/100).toFixed(2),
            Position: detail.positionIndex
          })
      }
    },
    reportSensor(props, type = 'MPClick') {
      if (!app.globalData.reportSensors) return false
      app.sensors.track(type, {
          screen_code: '11300',
          screen_name: '次日达首页',
          ...props
      })
  },
  /**
   * 点击瀑布流广告事件
   * @param {} item
   */
  bannerToSkip(e) {
      console.log('123', e)
      const item = e.currentTarget.dataset.item
      // item.bannerType = 'freshBanner'
      activity.toActivityPage(item)
      const { id, name } = item
      sensors.adClick({
        banner_id: id,
        banner_name: name,
        Position: item.positionIndex,
        element_code: 1130025001,
      })
    }
  }
})
