
const commonObj = require('../../../source/js/common').commonObj
const coordtransform = require('../../../utils/coordUtil')
import QQMapWX from '../../../source/js/qqmap-wx-jssdk.min.js'
const { addressLabels, defaultCityInfo } = require('../../../utils/config')
const locateService = require('../../../utils/services/locate')
const app = getApp()
import { debounce } from '../../../utils/util'

var qqmapsdk = new QQMapWX({
  key: commonObj.qqMapKey
})
const mapIconInfo = { // 地图控制点size
  width: 33,
  height: 38
}

Page({

  /**
   * 页面的初始数据
   */
  data: {
    currCity: {
      cityID: '',
      cityName: '',
    },
    showTips: true,
    locationList: [],
    selectedAddress: {}, // 已选地址信息
    controls: [],
    searchStr: '',
    cityName: '',
    mapLocation: null,
    poisList: [],
    searchList: [],
    loadSearch: false,
    showMap: true,   //展示地图
    showCityList: false,  //展示城市列表
    isSearch: false,   //点击搜索框跳转进来
    temporarySelectionAddress: '',   //临时选中的地址
    Mantle: false, //搜索框蒙层
  },
  _data: {
    pageFrom: '',
    isClearInput: false,
    isSupportAddrCityVip: true, // 城市开通服务
  },
  onLoad(options) {
    // 展示过授权弹窗，可以进行定位；没有授权过的不走
    if (locateService.hasShowLocateAuthModal()) {
      this.setCurrLocate()
    }
    this.setInitStoreAddress(options)
    //this.useScene = options.useScene
    //Object.assign(this, JSON.parse(options.addAddressInfo))
    if(options.search){
      this.setData({
        showAddrList: true,
        isSearch: true
      })
      return
    }
    this.mapCtx = wx.createMapContext('addressMap')
    if (locateService.hasShowLocateAuthModal()) {
      this.setLocation()
    }
  },
  onShow() {
    this.getAddressList();
  },

  /**
   * 设置当前定位
   */
  setCurrLocate() {
    const { address = ''}  = wx.getStorageSync("userCurrLoca") || {}
    this.setData({
      currentAddr: address || '无法获取当前定位地址'
    })
    if (!address) {
      this.locateCurrAddress(false)
    }
  },
  /**
   * 定位当前地址
   * @ params isShowLocateFailModal Boolean 是否弹未开启位置权限弹窗
   */
  async locateCurrAddress(isShowLocateFailModal = true) {
    try {
      const locationInfo = await locateService.getGeoLocation()
      await locateService.getCityName(locationInfo.latitude, locationInfo.longitude)
      const { address = ''} = wx.getStorageSync('userCurrLoca') || {}
      this.setData({
        currentAddr: address || '无法获取当前定位地址'
      })
    } catch (error) {
      if (!isShowLocateFailModal) {
				return
			}
      if (!app.globalData.userLocationAuth) {
        const res = await locateService.handleWXLocationErr(error.errMsg)
        if (res) {
          this.currentLocation()
          //在切换地址页开启定位权限
          wx.setStorageSync('openLocationPermission', true)
        }
        throw new Error()
      }
    }
  },
  /**
   * 设置初始门店地址相关信息
   */
  setInitStoreAddress(options) {
    const { pageFrom = '' } = options || {}
    this._data.pageFrom = pageFrom
    let { selectStoreInfo = {}, selectAddressInfo = {} } = wx.getStorageSync("bgxxSelectLocateInfo") || {}
    const { cityName: currCityName = '', cityID: currCityID = '' } = wx.getStorageSync("userCurrLoca") || {}
    if (this._data.pageFrom === 'confirmOrder') {
      const { selectStoreInfo: currStoreInfo, selectAddressInfo: currAddressInfo } = app.globalData.currStoreAddressInfo || {}
      selectStoreInfo = (!currStoreInfo || !currStoreInfo.storeID) ? {} : currStoreInfo
      if (currAddressInfo && currAddressInfo.cityID) {
        selectAddressInfo = currAddressInfo
      }
      app.globalData.currStoreAddressInfo = null
    }
    const { cityName, cityID } = selectAddressInfo
    const { cityName: defaultCityName, cityID: defaultCityID} = defaultCityInfo
    this.setData({
      currCity: {
        cityName: (cityName || currCityName) || defaultCityName,
        cityID: (cityID || currCityID) || defaultCityID
      },
      selectedAddress: selectAddressInfo,
      currStore: selectStoreInfo,
    })
    //展示缓存地址
    const currCity = wx.getStorageSync('showCurrentCity')
    if(!!currCity){
      this.setData({
        currCity: {
          cityName:currCity.cityName,
          cityID: currCity.cityID
        },
      })
    }
    const temporarySelectionAddress = wx.getStorageSync('temporarySelectionAddress')
    if(!!temporarySelectionAddress){
      this.setData({ temporarySelectionAddress })
    }
  },

  // 我的地址
  async getAddressList() {
    // 未登录不请求我的地址
    if (!app.checkSignInsStatus()) return
    const userID = app.globalData.customerID || -1
    try {
      const res = await app.api.getBgxxAddressList(userID)
      if (!res.data) {
        return
      }
      let resData = res.data || []
      // 解密及格式化处理手机号
      let addressList = resData
      const addressLabelMap = {};
      addressLabels.forEach(item => {
        if (!addressLabelMap[item.labelName]) {
          addressLabelMap[item.labelName] = item;
        }
      })
      addressList.forEach(item => {
        item.labelObj = addressLabelMap[item.label] || {};
      })
      this.setData({
        addressList
      })
    } catch (e) {}
  },
  /**
   * 点击新增地址
   */
  addReceivingAddress () {
    if (app.checkSignInsStatus()) {
      // 用户已登录
      wx.navigateTo({
        url: `/bgxxUser/pages/address/addAddress/index`,
      })
    } else {
      app.signIn()
    }
  },
  /**
   * 选中收获地址
   */
  clickReceivingAddress(e) {
    const { gisAddress, address } = e.currentTarget.dataset.address
    const pagesTracks = getCurrentPages()
    const prepage = pagesTracks[pagesTracks.length - 2]
    prepage.clickReceivingAddress(e)
    wx.navigateBack()
    // 临时选中的地址
    wx.setStorageSync('temporarySelectionAddress', `${gisAddress}${address}`)
  },
  /**
   * 处理已选地址的展示
   */
  async handleAddress(params) {
    try {
      const { cityName, lat, lon } = params
      const res = await app.api.checkBgxxIsSupportVip({
        cityName,
        lat,
        lon
      })
      const { city, supportSuperVipShop = 'N' } = res.data || {}
      if (supportSuperVipShop === 'Y' && !!city.cityID) {
        const { deliveryCenterCode, code: cityCode, cityID } = (city || {})
        const selectAddressInfo = {
          ...params,
          cityID,
          supportSuperVipShop: 'Y',
          deliveryCenterCode,
          cityCode
        }
        this.setData({
          selectedAddress: selectAddressInfo
        })
        this._data.isSupportAddrCityVip = true
      } else {
        this._data.isSupportAddrCityVip = false
        await app.showModalPromise({
          content: '当前城市未开通服务，请切换其他城市',
          confirmText: '我知道了',
        })
        throw new Error()
      }
    } catch (error) {
    }
  },
  /**
   * 重新定位
   */
  currentLocation: app.subProtocolValid('shop', async function() {
    try {
      await this.locateCurrAddress()
      const { cityName = '', location: { lng = '', lat = ''} = {}, address = '' } = wx.getStorageSync('userCurrLoca') || {}
      const bdLocation = coordtransform.gcj02tobd09(lng, lat) || []
      const params = {
        cityName,
        lat: bdLocation[1] || -1,
        lon: bdLocation[0] || -1,
        address,
      }
      const info = coordtransform.bd09togcj02(params.lon, params.lat)
      this.setMapLocation({
        latitude: info[1],
        longitude: info[0]
      })
      await this.handleAddress(params)
      this.setData({
        selectedIdx: null
      })
    } catch (error) {
      this.setData({
        currentAddr: '无法获取当前定位地址'
      })
    }
  }),
  /**
   * 输入框聚焦
   */
  inputFocus() {
    this.setData({
      showAddrList: true,
      Mantle: true
    })
    setTimeout(() => {
      this.setData({
        showTips: false,
      })
    }, 2000)
  },
  /**
   * 监听地址输入框
   */
  inputChange(e) {
    this.setData({
      inputValue: e.detail.value,
    })
    this.qqmapsdkSelect(this.data.inputValue)
  },
  /**
   * 根据用户输入信息获取附近地址
   */
  qqmapsdkSelect: debounce(function(inputAddr) {
    qqmapsdk.getSuggestion.call(this)
    qqmapsdk.getSuggestion({
      keyword: inputAddr,
      region: this.data.currCity.cityName,
      region_fix: 1, //限定在当前城市
      success: res => {
        res.data&&res.data.forEach(item=>{
          item.titleLight = this.brightenKeyword(item.title,this.data.inputValue)
        })
        this.setData({
          locationList: res.data
        })
      }
    })
  }, 300),
  // 高亮搜索词
  brightenKeyword(val, keyword) {
    const Reg = new RegExp(keyword, 'i');
    let res = '';
    if (val) {
      res = val.replace(Reg, `<span style="color: #ff1f3a;">${keyword}</span>`);
    }
    return res;
  },
  /**
   * 选择城市
   */
  chooseCity() {
    this.setData({
      showCityList: true
    })
  },
  /**
   * 选择地址
   */
  chooseLocation(e) {
    const { title } = e.currentTarget.dataset.item
    const pagesTracks = getCurrentPages()
    const prepage = pagesTracks[pagesTracks.length - 2]
    prepage.chooseLocation(e)
    wx.navigateBack()
    // 临时选中的地址
    wx.setStorageSync('temporarySelectionAddress', title)
  },
  /**
   * 隐藏地址
   */
  hideLocation() {
    this.setData({
      showAddrList: false
    })
  },
  /**
   * 清除地址输入框信息
   */
  clearSearchMes() {
    //加定时器，兼容iphone6s手机偶现点击清除之后关键词还存在的情况
    setTimeout(() =>{
      this.setData({
        showAddrList: this.data.isSearch ? true : false,
        inputValue: ''
      })
    }, 500)
  },
  /**
   * 城市列表切换当前城市
   */
  refreshCityInfo(city) {
    const { cityID = '', cityName = ''} = city

    this.setData({
      currCity: {
        cityID,
        cityName
      }
    })
  },
  /**
   * 设置位置信息
   */
  setLocation(locationInfo) {
    if (!locationInfo) {
      const that = this
      const currCity = wx.getStorageSync('showCurrentCity')
      // 如果存在临时选中的城市
      if(!!currCity){
        const info = coordtransform.bd09togcj02(currCity.lon, currCity.lat)
        that.setMapLocation({
          latitude: info[1],
          longitude: info[0]
        })
      }else{
        // 定位当前位置
        wx.getLocation({
          type: 'gcj02',
          success(res) {
            const {
              latitude,
              longitude
            } = res
            that.setMapLocation({
              latitude,
              longitude
            })
          },
          fail() {
            // wx.showToast({
            //   title: '定位当前位置失败',
            //   icon: 'none'
            // })
            // 没有开通定位权限，默认展示深圳市
            const info = coordtransform.bd09togcj02('114.064552', '22.548457')
            that.setMapLocation({
              latitude: info[1],
              longitude: info[0]
            })
          }
        })
      }
    } else {
      // 当前编辑的地址
      this.setMapLocation(locationInfo)
    }
  },
  /**
   * 设置地图信息
   */
  setMapLocation(location) {
    this.setData({
      mapLocation: location
    }, () => {
      this.setMapControls()
      this.centerLocation = JSON.parse(JSON.stringify(location))
      this.getNearAddress(this.centerLocation)
    })
  },
  /**
   * 设置地图控制点
   */
  setMapControls() {
    const selectQuery = wx.createSelectorQuery();
    selectQuery.select("#addressMap").boundingClientRect();
    selectQuery.exec((res) => {
      const {
        width: iconWidth,
        height: iconHeight
      } = mapIconInfo
      this.setData({
        controls: [{
          id: 1,
          iconPath: '../../source/images/map_icon_location.png',
          position: {
            left: (res[0].width - iconWidth) / 2,
            top: (res[0].height - iconHeight) / 2,
            width: iconWidth,
            height: iconHeight
          }
        }]
      })
    })
  },
  /**
   * 获取附近地址信息
   */
  getNearAddress({
    latitude,
    longitude
  }) {
    const that = this
    qqmapsdk.reverseGeocoder({
      location: {
        latitude,
        longitude
      },
      get_poi: 1,
      poi_options: 'policy=2;page_size=20;page_index=1',
      success(res) {
        that.dealNearResult(res)
      },
      fail() {
        // wx.showToast({
        //   title: '获取附近地址失败',
        //   icon: 'none'
        // });
      }
    })
  },
  /**
   * 处理附近地址信息
   */
  dealNearResult(res) {
    const {
      status = -1, result = null
    } = res || {}
    if (status === 0 && !!result) {
      const {
        pois = [], ad_info: {
          city = ''
        } = {}
      } = result
      // 将初始当前传入定位的地址放在列表首位
      if (pois.length > 0) {
        let index = -1
        if (this.mapCityID !== -1) {
          index = pois.findIndex(item => item.id === this.mapCityID)
        } else if (!!this.gisAddress) {
          index = pois.findIndex(item => this.gisAddress.indexOf(item.title) > -1)
        }
        if (index > 0) {
          const item = pois.splice(index, 1)[0]
          pois.unshift(item)
        }
      }
      this.setData({
        cityName: city,
        poisList: pois
      })
    } else {
      this.setData({
        poisList: []
      })
    }
  },
  /**
   * 区域改变
   */
  regionChange(res) {
    if (res.type === "end") {
      !!this.addressTimer && clearTimeout(this.addressTimer)
      const that = this
      this.addressTimer = setTimeout(() => {
        this.mapCtx.getCenterLocation({
          success: function (res) {
            const {
              latitude,
              longitude
            } = res
            that.centerLocation = {
              latitude,
              longitude
            }
            that.getNearAddress(that.centerLocation)
          }
        })
      }, 200)
    }
  },
  /**
   * 输入框搜索
   */
  inputSearch(e) {
    if (this._data.isClearInput) {
      return
    }
    this.setData({
      searchStr: e.detail.value,
    });
    if (!e.detail.value) {
      this.setData({
        searchList: []
      })
      this.refreshMapLocation()
    } else {
      this.searchRequest()
    }
  },
  /**
   * 输入框聚焦
   */
  bindFocus () {
    this._data.isClearInput = false
  },
  /**
   * 输入框查询
   */
  searchRequest() {
    const that = this
    const {
      cityName,
      searchList
    } = this.data
    this.setData({
      loadSearch: !!searchList.length
    });
    !!this.addressTimer && clearTimeout(this.addressTimer)
    this.addressTimer = setTimeout(() => {
      qqmapsdk.getSuggestion({
        keyword: this.data.searchStr,
        region: cityName,
        region_fix: 1,
        policy: 1,
        success(res) {
          const {
            data = []
          } = res || {}
          that.setData({
            loadSearch: true,
            searchList: data
          })
        },
        fail() {
          that.setData({
            loadSearch: true,
            searchList: []
          })
        }
      })
    }, 400)
  },
  /**
   * 清除输入框
   */
  clearSearch() {
    this.setData({
      searchStr: '',
      searchList: []
    })
    this._data.isClearInput = true
    this.refreshMapLocation()
  },
  /**
   * 刷新地图位置
   */
  refreshMapLocation() {
    this.addressTimer = setTimeout(() => {
      this.setData({
        mapLocation: this.centerLocation
      })
    }, 1000)
  },
  /**
   * 控制地图的显示与隐藏
   */
  switchMap(){
    this.setData({
      showMap: !this.data.showMap
    })
  },
  /**
   * 选择城市
   */
  changeCityPopup(cityInfo){
    console.log('选择城市成功----',cityInfo)
    const {
      cityName,
      lat,
      lon,
      cityId
    } = cityInfo.detail
    // 百度坐标转化为腾讯坐标
    const info = coordtransform.bd09togcj02(lon, lat)
    this.centerLocation = {
      latitude: info[1],
      longitude: info[0]
    }
    // apple真机上改变地图mapLocation时不会立马触发regionChange事件，所以延迟加载
    const time = !!this.data.searchStr ? 0 : 200
    this.addressTimer = setTimeout(() => {
      this.setData({
        cityName: cityName,
        mapLocation: this.centerLocation,
      })
      this.cityId = cityId
      if (!!this.data.searchStr) {
        this.searchRequest()
      }
    }, time);
    this.refreshCityInfo(cityInfo.detail)
    this.setData({
      poisList: [],
      inputValue: ''
    })
  },
  /**
   * 关闭选择城市弹窗
   */
  closePopup(){
    this.setData({
      showCityList:false
    })
  },
  /**
   * 点击取消，返回选择门店页
   */
  handleCancel(){
    wx.navigateBack()
  }
})
