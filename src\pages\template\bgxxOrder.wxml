 <!-- pages/orderList和bgxxUser/pages/cycleBuy下心享订单公用订单模板 -->
<!-- 次日达订单 -->
<wxs module='common' src="../../utils/common.wxs"></wxs>
<template name="bgxxOrder">
  <view>
    <block wx:for="{{mix_bgxx_orderList}}" wx:key="goodsOrderID">
      <view class="order-item" data-orderid="{{item.payStatus === 'SUCCESS' ? item.goodsOrderID : item.payOrderID}}" data-ispay="{{item.payStatus === 'SUCCESS'}}" data-orderInfo="{{ item }}" bindtap='mix_bgxx_toBgxxOrderDetail'>
        <view class="item-top pad-lr-24">
          <text class="time">{{item.orderFlowState === 'WAIT_PAY' ? common.splitDate(item.createTime) : common.splitDate(item.payTime)}}</text>
          <image class="s-label" src="{{'/source/images/' + orderHeadIconMap['次日达'] + '.png'}}"></image>
          <text class="status" bindtap='toInvoice'>{{item.orderStatusDesc}}</text>
        </view>
        <view class="item-img">
          <scroll-view class="img-box" scroll-x="true">
          <block wx:for="{{item.goodsList}}" wx:key="goodsID">
            <image wx:if="{{item.headPic}}" class="bgxx-icon-fruit"  src="{{common.minifyImg(picUrl + item.headPic, 144, 144)}}"></image>
            <image wx:else class="bgxx-icon-fruit"  src="https://resource.pagoda.com.cn/group1/M21/8C/51/CmiLkGJnsu-AKE_SAAAIGee3pVE401.png"></image>
          </block>
          </scroll-view>
        </view>
        <view class="item-middle pad-lr-24">实付:
          <text class="item-amount">
            <text class="f-big">￥{{item.payAmountArr[0]}}</text>
            <text wx:if="{{item.payAmountArr[1]}}">.{{item.payAmountArr[1]}}</text>
          </text>
        </view>
        <view wx:if="{{common.parseInt(item.doubleRefundAmount) && item.isAllDoubleRefund === 'N'}}" class="goods-refund-tips pad-lr-24">部分商品缺货，退款￥{{common.formatPrice(item.doubleRefundAmount)}}</view>
        <view wx:if="{{common.parseInt(item.doubleRefundAmount) && item.isAllDoubleRefund === 'Y'}}" class="goods-refund-tips pad-lr-24">商品缺货，退款￥{{common.formatPrice(item.doubleRefundAmount)}}</view>
        <view wx:if="{{ item.btnList.length }}" class="item-bottom">
          <!-- 按钮组 -->
          <view class="bgxx-lay-between">
            <view class="" >
              <!--待自提-->
              <view class="pick-code" wx:if="{{item.orderFlowState === 'WAIT_PICKUP'}}">提货码：{{item.takeCode}}</view>
            </view>

            <view class="bgxx-lay-end">
              <!--交易成功-->
              <view
                class="btn-white"
                data-orderInfo='{{item}}'
                wx:if="{{item.isRecurOrder === 'Y'}}"
                catchtap='bgxxOrderAgain'
                data-sensorsKey="xxshopOrderListOneMore">
                再来一单
              </view>

              <!--非补发单-->
              <block wx:if="{{item.orderSubType !== 'I' && item.orderSubType !== 'J'}}">
                <view
                  class="btn-white"
                  data-orderid="{{item.goodsOrderID}}"
                  wx:if="{{item.isEachDetails === 'Y'}}"
                  catchtap='toinstallmentDetail'>
                  每期详情
                </view>

                <view
                  class="btn-white"
                  data-orderid="{{item.goodsOrderID}}"
                  data-amount="{{item.payAmount}}"
                  data-totalrefundamount="{{item.totalRefundAmount}}"
                  catchtap='applyInvoice'
                  wx:if="{{(item.invoice.hasInvoiced === 'N' && item.invoice.enableOpenInvoice === 'Y') &&  item.isAllGift !== 'Y' && item.channel !== 'gb' && item.orderFlowState == 'TRADE_SUCCESS' && (item.payAmount- item.totalRefundAmount > 0)}}"
                  >
                  开具发票
                </view>

                <view
                  class="btn-white"
                  data-orderid="{{item.goodsOrderID}}"
                  catchtap='toInvoiceDetail'
                  wx:if="{{item.invoice.hasInvoiced === 'Y' &&  item.isAllGift !== 'Y' && item.channel !== 'gb' && item.orderFlowState === 'TRADE_SUCCESS'}}">
                  发票详情
                </view>

                <view
                  class="btn-white"
                  catchtap='bindRefund'
                  wx:if="{{item.orderFlowState !== 'PICKED_UP' && item.isAllowRefund === 'Y' && item.isAllowComplaint === 'Y' && item.isAllGift !== 'Y' && item.orderSubType !== 'H' }}"
                  data-orderInfo="{{ item }}"
                  data-goodsorderid="{{item.goodsOrderID}}"
                  data-goodsordernum="{{item.goodsOrderNum}}">
                  三无退货
                </view>

                <!--待付款-->
                <block wx:if="{{item.orderFlowState === 'WAIT_PAY'}}">
                  <view
                    class="btn-white"
                    data-orderInfo='{{item}}'
                    data-modalflag='tips'
                    data-status='nopay'
                    catchtap='popModal'
                    data-sensorsKey="xxshopOrderListToCancel">
                    取消订单
                  </view>

                  <view
                    class="btn-green"
                    data-modalflag="payPop"
                    data-orderInfo='{{item}}'
                    catchtap='popModal'
                    data-sensorsKey="xxshopOrderListToPay">
                    立即支付{{ mix_countDown_timeDate[item.payOrderID] }}
                  </view>
                </block>
              </block>

              <!--已取消/已关闭-->
              <view
                wx:if="{{(item.orderFlowState === 'CANCELED' || item.orderFlowState === 'CLOSED') && item.isAllGift !== 'Y'}}"
                class="btn-white"
                data-orderInfo='{{item}}'
                catchtap='bgxxOrderAgain'
                data-sensorsKey="xxshopOrderListRePurchase">
                重新购买
              </view>

              <!--配送中-->
              <view
                wx:if="{{item.orderFlowState === 'SENDING' && item.orderSubType !== 'H'}}"
                class=" btn-green"
                data-orderInfo='{{item}}'
                data-modalflag='confirm'
                catchtap='popModal'>
                确认收货
              </view>

              <!--已自提-->
              <block wx:if="{{item.orderFlowState === 'PICKED_UP'}}">
                <!--非补发单-->
                <block wx:if="{{item.orderSubType !== 'I' && item.orderSubType !== 'J'}}">
                  <view
                    class="btn-white"
                    data-info='{{item}}'
                    wx:if="{{item.isAllowRefund === 'Y' && item.isAllowComplaint === 'Y'}}"
                    catchtap='toRequestPostSale'>
                    申请售后
                  </view>
                </block>
                <view
                  class="btn-green"
                  data-orderInfo='{{item}}'
                  data-modalflag='pickedUp'
                  catchtap='popModal'>
                  确认提货
                </view>
              </block>

              <!-- 待自提 -->
              <block wx:if="{{ item.orderFlowState === 'WAIT_PICKUP' }}">
                <view
                  class="btn-white"
                  data-orderInfo='{{ item }}'
                  catchtap='connectStore'>
                  联系门店
                </view>

                <view
                  class="btn-white"
                  data-orderInfo='{{ item }}'
                  catchtap='openStoreLoaction'>
                  导航到店
                </view>
              </block>
          </view>
          </view>
        </view>
      </view>
    </block>
  </view>
</template>
