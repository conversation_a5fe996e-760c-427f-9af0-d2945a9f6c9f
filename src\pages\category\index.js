import locateAfterCheckCity, { NEXT_PAGE_LOAD } from '~/mixins/locateAfterCheckCity';
import { Lazyload } from '~/utils/services/lazyload'
import emitter from '~/source/js/emitter'
import { getDefaultGoods } from '~/utils/goods/getDefaultGoods'
import { parseTimelyGoodsDetailScene, isFromURLScheme, decodeURLSchemeQuery } from '~/pages/homeDelivery/utils/forwardNavigate';
const commonObj = require('~/source/js/common').commonObj;
const util = require('~/utils/util')
const app = getApp();
const sensors = require('~/utils/report/sensors')
const cartMinxins = require('~/mixins/cartMixin')
const locateMixin = require('~/mixins/locateMixin')
const checkInsertCoupon = require('~/mixins/checkInsertCoupon')
import vegetablesLocateMixin from '~/mixins/vegetablesLocateMixin'
import { getAllInfoB2cGoodsList, goodsListUniqueHandle } from '~/utils/goods/goodsData';
import { FruitGoods, getOnlinePriceGoodsList } from '~/service/fruitGoods';
import { getPromiseObj } from '~/utils/promise';
import { collectionGoodsSn, filterPriceActivityGoods, filterNoPriceActivityGoods } from '~/service/fruitGoodsUtil';

const DEFAULT_PART_SIZE = 10  // 二维数组swiperList[1]的子项默认长度
const DEFAULT_GOODS_ITEM_HEIGHT = 240 // 默认商品卡片高度 rpx
const STANDARD_PIXEL_WIDTH = 750 // 以iphone6为标准的屏幕像素宽
const DEFAULT_SKELETON_LENGTH = 4 //  默认骨架图列表数量，不要超过一屏，写死4个

const REENCENT_CATE_NAME = 'recentBuy'

function getShareHomeDeliveryObj(option) {
  const { homeDeliveryObj: homeDeliveryJSON = null } = util.isEmptyObject(option) ? {} : option
  try {
    const homeDeliveryObj = homeDeliveryJSON && typeof homeDeliveryJSON === 'string' ? JSON.parse(homeDeliveryJSON) : homeDeliveryJSON
    return {
      homeDeliveryObj,
      isB2C: homeDeliveryObj && homeDeliveryObj.takeawayAttr === 'B2C',
    }
  } catch {
    return {
      homeDeliveryObj: {},
      isB2C: false,
    }
  }
}

/**
 * @desc 处理及时达分佣分享跳转
 */
function handleTimelyGoodsDetailLading(option) {
  const optionsObj = isFromURLScheme(app.globalData.scene)
    // 此处直接使用option见parseTimelyGoodsDetailScene中注释
    // weixin://dl/business/?appid=wx1f9ea355b47256dd&path=pages/homeDelivery/index&query=distributor%3D1911302906%26goodsSn%3D1010110006
    ? decodeURLSchemeQuery(option)
    // 此处的q参数,是扫描普通二维码唤起小程序后的启动参数
    : util.toQueryObj(decodeURIComponent(option.q || '').split('?')[1] || '', { decodeOnEach: true })
  const { navigateTo, } = optionsObj
  if (navigateTo) {
    wx.navigateTo({ url: navigateTo })
    return {}
  }
  return parseTimelyGoodsDetailScene({ query: option, scene: app.globalData.scene })
}

Page({
  mixins: [ cartMinxins, locateAfterCheckCity, locateMixin, vegetablesLocateMixin, checkInsertCoupon],
  data: {
    categoryList: [],
    goodsList: [],
    currCategory: {},
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    imgUrl: '',
    componentClass: 'category-goods', // 混入中使用
    intoViewId: '',
    couponList: [],
    simpleCouponList: [],
    searchGoodsPlaceholder: '',
    menuButtonStyle: {
      top: 36,
      height: 28
    },
    hasNotReceiveCoupons: false, // 是否有未领取的券
    cardCoupon: {},
    activityObj: {},
    showRequestError: false,
    showNoGoodsDefault: false,
    showNextCateTip: false,
    dataLoading: true,
    /********** */
    // 切换动效相关
    showSoldOutBtn: false,
    canScroll: true,
    /**品类滚动条位置 */
    scrollTop: 0,
    swiperDuration: 0,
    currentSwiper: 0, // 当前swiper
    goodsContentScrollBound: { // 商品内容区滚动边界
      isTop: false,
      isBottom: false
    },
    skeletonList: Array.from({length: DEFAULT_SKELETON_LENGTH}, (item, index) => index),
    swiperList: [
      [],
    ],
    bottomHeight: 64,
    /********** */

    spLabelList: [],
  },
  _data: {
    // 是否时分享商品的sku弹窗
    isShareGoodsSku: false,
    platform: 'ios',
    defaultSearch: '搜搜想吃的水果',
    option: {},
    storeRecommendGoods: [],
    storeSoldOutGoods: [],
    storeShareGoods: [], // 推荐商品中的分享商品
    needRefresh: true,
    categoryList: [],
    recentList: [],
    currCategory: {},
    lastCurrentView: '',
    /********** */
    // 切换动效相关
    scrollContentObserver: null,
    scrollOuterObserver: null,
    lazyLoadBoxObserver: null,
    categoryOuterBound: {
      isTop: false,
      isBottom: false
    },
    currentGoodsList: [],
    lazyloadListPartLength: 0,
    lazyloadListFirstPartLength: 10,
    lazyloadInstance: null, // 懒加载实例
    lazyloadLength: 0, // 懒加载列表长度
    lazyloadCurrentIdx: 0,
    soldOutLen: 0, // 售罄商品列表长度
    soldOutIndex: 0, // renderList里第几个开始是售罄
    /********** */
    pageProtocol: true,
    // 是否在定位流程的onlyShowData期间
    // 如果是,那就不在onHide里清除一些信息
    // 否则会导致toSharePage不正常
    duringOnlyShowData: false,
    // 分享的商品sku
    shareGoodsSn: '',
    // 需要shareGoodsSn的分类
    needShareGoodsSn: {},
    // 是否提示过分享商品已在城市下架
    hasShowShareGoodsToast: false,
    /**品类数据临时缓存 */
    categoryDataCache: {},
    categoryLabelDataCache: {},
    /** onShow事件上报的数据 */
    onShowReportData: {},
    onlinePriceGoodsList: [], // 线上专享特价商品
    isClickTag: false,
  },
  onLoad: function(option) {
    console.log('category-option', option)
    this.getPlatform()
    this._data.option = option
      ? (
        option.scene || option.q || isFromURLScheme(app.globalData.scene)
          // 从普通的及时达商品详情分享海报进入,会在这有scene参数
          // 从及时达分佣分享进入,会在这里有q参数(是一个encodeURIComponent后的二维码链接)
          // 或者在及时达分佣场景,通过非微信扫二维码,从而唤起小程序,启动参数会直接写在option里
          ? { homeDeliveryObj: handleTimelyGoodsDetailLading(option) }
          : option
      )
      : {}
    app.event.on('refreshPageGoods', this.setRefreshGoodsParams)
    this.setLazyloadListInitLen()
    this._data.lazyloadInstance = new Lazyload({
      context: this,
      renderListNameStr: 'swiperList[0]'
    })
  },

  setRefreshGoodsParams() {
    this._data.needRefresh = true
  },

  getPlatform () {
    // platform: devtools, ios, android
    // 默认取ios
    const { platform = 'ios' } = wx.getStorageSync('systemInfo') || {}
    this._data.platform = platform
  },

  isIOS () {
    return this._data.platform.includes('ios')
  },

  onUnload: function () {
    app.event.off('refreshPageGoods', this.setRefreshGoodsParams)
    sensors.track(sensors.MPClick, '1300_130000006')
    this.disconnectCateSwitchObserver()
  },

  onShow() {
    this.setPageParams()
    this.reportPageShow()
  },

  /**
   * 设置页面参数
   */
  setPageParams() {
    const { userID = -1 } = wx.getStorageSync('user') || {}
    this.setData({
      userID
    })
  },

  /**
   * 上报页面显示事件
   */
  async reportPageShow() {
    this._data.onShowReportData = getPromiseObj()
    const { promise } = this._data.onShowReportData
    const { shareGoodsList } = await promise
    sensors.goodsDetailShow(shareGoodsList[0])
  },

  onReady() {
    this.setMenuButtonStyle()
  },

  /**
   * 设置页面胶囊style
   */
  setMenuButtonStyle() {
    const { top = 36, height = 28 } = wx.getMenuButtonBoundingClientRect() || {}
    this.setData({
      menuButtonStyle: {
        top,
        height
      }
    })
  },

  onHide() {
    if (this._data.duringOnlyShowData) { return }
    // 离开时关闭弹窗
    this.setData({
      isShowBounce: false,
      showCouponMore: false
    })
    Object.assign(this._data, {
      option: {},
      lastCurrentView: this.data.currentView,
      // 如果第一次查询分享商品,没显示过城市下架的toast
      // 那就不用再显示了
      hasShowShareGoodsToast: true
    })
  },

  /**
   * 定位完成
   */
  onLocateReady({ onlyShowData = false } = {}) {
    this._data.duringOnlyShowData = onlyShowData
    const { currentView = '', categoryList = [] } = this.data
    if (currentView === 'content') {
      this.locate()
      onlyShowData || this.toSharePage()
      if (this._data.needRefresh) {
        onlyShowData || (this._data.needRefresh = false)
        this.updatePageData()
      } else {
        if (this._data.lastCurrentView === 'noStore') {
          this.setData({
            currentView: 'noStore'
          })
          return
        }
        // ?
        if (!categoryList || !categoryList.length) {
          // 无品类列表，展示无门店缺省页
          this.setData({
            currentView: 'noStore'
          })
          return
        }
        // 不刷新的情况下，如果仍需要切换品类
        this.jumpToCate()
      }
    }
  },

  /**
   * 分享页面分发
   */
  async toSharePage() {
    const option = this._data.option
    const { homeDeliveryObj, isB2C } = getShareHomeDeliveryObj(option)
    const needShareGoods = homeDeliveryObj && !isB2C
    Object.assign(this._data, {
      // 保持上次分享的商品goodsSn
      ...(needShareGoods ? { shareGoodsSn: homeDeliveryObj.goodsSn } : {}),
      hasShowShareGoodsToast: !needShareGoods
    })
    isB2C && this._data.onShowReportData.resolve({ shareGoodsList: [] })
    // B2C商品分享,继续跳转到商品详情页
    homeDeliveryObj && (isB2C ? wx.navigateTo({ url: '/homeDelivery/pages/goodsDetail/index?' + util.createQueryParams({
      ...Object.assign(option, {
        homeDeliveryObj: typeof homeDeliveryObj === 'string' ? homeDeliveryObj : JSON.stringify(homeDeliveryObj)
      }),
      fromShareDetail: Number([1007, 1008, 1036, 1047, 1048, 1049, 1160].includes(app.globalData.scene))
    }, ['txsrShareInfoSdk', 'sampshare']) }) : emitter.emit(NEXT_PAGE_LOAD, { needShowDialog: false }))
  },

  /**
   * 更新页面信息
   */
  async updatePageData() {
    this.getSearchWord()
    if (this.data.storeCode) {
      // 有门店
      Promise.all([this.getCategorylist(), this.getStoreRecommendGoods(), this.getRecentBuyGoods(), this.getOnlinePriceGoodsList()]).then(async () => {
        // setCateToJump对点击跳转做处理
        this.setCateToJump()
        // checkTXSensorEnter是处理携带参数直接进入品类页的，与jumpToCate不冲突
        await this.checkTXSensorEnter()
        this.setFullCategory()
        this.initCateSwitchOvserver()
      })
    } else {
      // 无门店，是否还有b2c商品
      const goodsList = await this.getB2CGoods()
      if (!!this.data.swiperList[0].length && goodsList.length) {
        this.setB2CCateList()
        this.initCateSwitchOvserver()
      } else {
        // 无门店，无全国送商品，页面展示无门店缺省页
        this.setData({
          currentView: 'noStore'
        })
        // 此时节点移除，需要销毁Observer
        this.disconnectCateSwitchObserver()
      }
    }
  },
  /**
   * @desc
   * 1. 获取商品基本数据，返回的是90天内已经剔除耗材的sku
   * 2. 获取商品基本信息，只取上架商品
   * 3. 然后聚合前，对sku进行去重(collectionGoodsSn)
   * 4. 查询库存后，剔除库存为0的商品
   * 5. 若分享商品展示在最近购买类目，则要相同的sku
   */
  async getRecentBuyGoods () {
    const { userID } = wx.getStorageSync('user') || {}
    let recentList = []
    try {
      let res = await app.api.getFruitRecentBuy({ data: {
        customerID: userID
      }})
      recentList = res.data || []
    } catch(err) {
      console.log('getCategorylist err', err);
    }
    if (!Array.isArray(recentList) || !recentList.length) return
    const { storeCode, cityCode, deliveryCenterCode } = wx.getStorageSync('timelyCity') || {}
    const fruitGoods = new FruitGoods({
      storeCode,
      cityCode,
      deliveryCenterCode,
      isNeedMergeByGroup: true,
      isNeedMergeMultiSpec: true,
      filterCitySaleStatus: true,
      filterSaleStatus: true
    })
    // console.log('recentList before', recentList.length);
    try {
      const baseList = await fruitGoods.getGoodsBaseInfoList(recentList.reduce((list, item) => {
        return [...list, { goodsSn: item.goodsSn, takeawayAttr: '及时达' }]
      }, []))
      // const skuSets = collectionGoodsSn(baseList, false)
      const complateInfoList = await fruitGoods.setGoodsDynamicInfo(collectionGoodsSn(baseList, false))
      this._data.recentList = complateInfoList.filter(item => item.allSkuStockNum).slice(0, 5)
    } catch (error) {
      console.log('getRecentBuyGoods 本地获取商品详情异常:', error);
    }
  },

  /**
   * @description
   * 组合分享商品和最近购买商品
   */
  setRecentBuyGoods() {
    const { recentList, storeShareGoods, needShareGoodsSn } = this._data
    const {  currCategory: { categoryID } } = this.data
    const shareGoodsSn = needShareGoodsSn[categoryID]
    const hasShareGoodsList = shareGoodsSn && storeShareGoods.length
    const list = hasShareGoodsList ? storeShareGoods : []
    const recent = goodsListUniqueHandle([...list, ...recentList])
    this.lazyloadList({
      type: REENCENT_CATE_NAME,
      onSaleGoodsList: recent,
      soldOutGoodsList: [],
      allGoodsList: recent
    })
  },

  /**
   * 获取当前品类所属类型
   * @returns
   */
  getCurrentCateGoryType() {
    const { currCategory } = this.data
    if (currCategory.categoryID === '-9999') {
      return 'appstorerecommend'
    } else if (currCategory.categoryID === REENCENT_CATE_NAME) {
      return REENCENT_CATE_NAME
    } else if (currCategory.categoryName === '全国送') {
      return 'appb2cgoods'
    } else {
      return currCategory.categoryID
    }
  },

  /**
   * 设置品类数据缓存
   * @param {*} key
   * @param {*} data
   */
  setCategoryDataCache(key, data) {
    this._data.categoryDataCache[key] = data
  },

  /**
   * 请求品类数据
   * @param {*} data
   * @returns
   */
  getCategoryGoodsData(data) {
    const requestTask = this._data.requestTask
    if (requestTask && typeof requestTask.abort === 'function') {
      requestTask.abort()
    }

    const req = app.api.getCategoryGoodsList({
      data,
      loading: false
    })
    this._data.requestTask = req
    return req
  },

  /**
   * 获取全国送B2C商品数据
   */
  async getB2CGoods() {
    const { cityCode, currCategory } = this.data
    const { deliveryCenterCode } = wx.getStorageSync('timelyCity') || {}
    const param = {
      cityCode,
      deliveryCenterCode,
      customerID: app.globalData.customerID || -1,
      categoryCode: 'appb2cgoods'
    }
    let goodsList = []
    try {
      const res = await this.getCategoryGoodsData(param)
      const {
        data
      } = res

      const {
        onSaleList,
        saleOutList
      } = await getAllInfoB2cGoodsList({
        goodsSimpleList: data,
        cityCode,
        deliveryCenterCode,
        nextPage: false
      })
      goodsList = [...onSaleList, ...saleOutList]
      // 全国送商品没有售罄
      this.lazyloadList({
        type: param.categoryCode,
        onSaleGoodsList: onSaleList,
        soldOutGoodsList: saleOutList,
        allGoodsList: goodsList
      })
    } catch(err) {
      console.log('getB2CGoods', err)
      this.toggleLoadingStatus(false)
      this.toggleRequestError(true)
    }
    // 不管实际商品状态如何,如果是从分享进来,需要在全国送下显示分享商品的
    // 一律按城市下架场景处理
    ;(this._data.needShareGoodsSn[currCategory.categoryID] || (this._data.shareGoodsSn && !this.data.storeCode)) && this.showCityOffShelfTips()
    return goodsList
  },

  /**
   * 设置全国送品类
   */
  setB2CCateList () {
    const currCategory = {
      categoryID: 0,
      categoryName: '全国送'
    }
    const categoryList = [
      currCategory
    ]
    this.updateCateList(categoryList)
    this.setData({
      categoryList,
      currCategory
    })
  },

  /**
   * 获取专区优惠券
   */
  getCoupons() {
    if (!app.checkSignInsStatus() || !this.data.userID) {
      this.setData({
        couponList: [],
        simpleCouponList: [],
      })
      return
    }
    Promise.all([this.getCanReceiveCoupons(), this.getCanUseCoupons()]).then(res => {
      const myCouponList = Array.isArray(res[1]) ? res[1] : (res[1].list || [])
      const couponList = res[0].concat(myCouponList).map(item => {
        const { couponWay, limitValue, couponValue } = item
        item.couponValueShow = couponValue
        const formatLimitValue = Number((limitValue / 100).toFixed(1)) || 0
        if(['1', '3'].includes(couponWay)) { // 满减/立减
          const value = Number((couponValue/100).toFixed(1)) || 0
          item.couponValueShow = value
          if (couponWay === '1') {
            item.cheapDesc = `满${formatLimitValue}减${value}`
          } else {
            item.cheapDesc = `立减${value}元`
          }
        }
        if(['2', '4'].includes(couponWay)) { // 满折/立折
          const value = Number((couponValue/10).toFixed(1))
          item.couponValueShow = value
          if (couponWay === '2') {
            item.cheapDesc = `满${formatLimitValue}享${value}折`
          } else {
            item.cheapDesc = `立享${value}折`
          }
        }
        if(['5'].includes(couponWay)) { // 免运
          item.cheapDesc = `配送券`
        }
        return item
      })
      this.setCouponParams(couponList)
    }).catch(error => {
      console.log(error)
    })
  },

  setCouponParams(couponList) {
    const obj = couponList.find(item => item.receiveStatus === 'N')
    this.setData({
      couponList,
      simpleCouponList: couponList.slice(0, 10),
      hasNotReceiveCoupons: !!obj
    })
  },

  /**
   * 获取可领取的优惠券
   */
  getCanReceiveCoupons() {
    const { cityID = -1 } = wx.getStorageSync('timelyCity') || {}
    const params = {
      customerID: this.data.userID,
      cityID
    }
    return new Promise((resolve) => {
      app.api.getActivityCanReceiveCoupons(params).then(res => {
        resolve(res.data || [])
      }).catch(() => {
        resolve([])
      })
    })
  },

  /**
   * 获取小程序可以使用的券
   */
  getCanUseCoupons() {
    const params = {
      customerID: this.data.userID,
      // 以下是中台接口参数
      isWxCanUse: true,
      page: 1,
      pageSize: 50,
      // 门店/及时达
      applicableBizTypes: [0, 2],
      // 查优惠券和运费券（代金券过滤）
      defineIds: [1000, 1003, 1004, 1005],
      // 展示招牌果品券
      showFruitGradeCoupon: 'Y'
    }
    return new Promise((resolve) => {
      app.api.getNotUsedCouponList(params).then(res => {
        resolve(res.data || [])
      }).catch(() => {
        resolve([])
      })
    })
  },

  /**
   * 获取搜索关键词
   */
  async getSearchWord () {
    let searchGoodsPlaceholder = this._data.defaultSearch
    try {
      const res = await app.api.getSearchHistory(this.data.cityID)
      const { searchKeyword = ''} = res.data || {}
      searchGoodsPlaceholder = searchKeyword
    } catch(error) {}
    this.setData({
      searchGoodsPlaceholder
    })
  },
  /**
   * @desc 赋值之后清空全局变量
   */
  setCateToJump () {
    const { navigateToCategoryPageId } = app.globalData
    console.log('navigateToCategoryPageId', navigateToCategoryPageId);
    if (!navigateToCategoryPageId) return
    this._data.currCategory = {
      categoryID: navigateToCategoryPageId
    }
    console.log('jumpToCate', this._data.currCategory);
    app.globalData.navigateToCategoryPageId = null
  },

  jumpToCate () {
    const { navigateToCategoryPageId: categoryID } = app.globalData
    const currCategory = this.data.categoryList.find(item => {
      return String(item.categoryID) === String(categoryID)
    })
    if (currCategory) {
      const e = {currentTarget: {dataset: {item: {categoryID: categoryID}}}}
      this.clickCategory(e)
    }
    app.globalData.navigateToCategoryPageId = null
  },


  /**
   * 检查是否智慧零售携带商品信息进入
   */
  async checkTXSensorEnter() {
    const { categoryList = [], option } = this._data
    if (!categoryList.length) {
      return
    }
    const { goodsID = ''} = option || {}
    if (goodsID && app.globalData.txSensorData) {
      const { storeCode } = wx.getStorageSync('timelyCity') || {}
      if (!storeCode) return
      const params = {
        eshopGoodsId: goodsID,
        storeCode
      }
      try {
        const res = await app.api.getCategoryIdByGoodsId(params)
        const currCategory = categoryList.find(item => {
          return String(item.categoryID) === String(res.data || 0)
        })
        if (currCategory) {
          Object.assign(this._data, {
            currCategory
          })
        } else {
          this._data.option.goodsID = ''
        }
      } catch(err) {
        this._data.option.goodsID = ''
        console.error('根据goodsID获取categoryID失败', err)
      }
    }
  },

  /**
   * 获取详细信息的商品列表，并按库存状态/分享商品进行分类返回
   * @param {*} param0
   * @returns
   */
  async getAllInfoGoodsList({
    data = [],
    storeCode,
    cityCode,
    deliveryCenterCode,
    shareGoodsSn = '',
    isNeedMergeByGroup = true,
    isNeedMergeMultiSpec = true
  }) {
    const fruitGoods = new FruitGoods({
      storeCode,
      cityCode,
      deliveryCenterCode,
      isNeedMergeByGroup,
      isNeedMergeMultiSpec
    })

    const options = []
    /**传入数据存在分享商品 */
    let isHasShareGoods = false

    data.forEach(goods => {
      if (!isHasShareGoods && goods.goodsSn === shareGoodsSn) {
        isHasShareGoods = true
      }

      options.push({
        goodsSn: goods.goodsSn,
        eshopGoodsId: String(goods.eshopGoodsId),
        takeawayAttr: '及时达'
      })
    })

    const complateInfoList = await fruitGoods.getGoodsComplateInfoList(options)
    // console.log(complateInfoList)

    const result = goodsListUniqueHandle(complateInfoList)
    //  页面携带分享商品进入。但返回列表中不存在分享商品，请求构造一个分享商品并推入展示列表
    if (shareGoodsSn && !isHasShareGoods) {
      const fruitGoods = new FruitGoods({
        storeCode,
        cityCode,
        deliveryCenterCode,
        isNeedMergeByGroup: true,
        isNeedMergeMultiSpec: true
      })

      const shareGoodsList = await fruitGoods.getGoodsComplateInfoList([{
        goodsSn: shareGoodsSn,
        takeawayAttr: '及时达'
      }])
      result.unshift(...shareGoodsList)
    }

    const { goodsList, goodsList_saleOut, shareGoodsList } = result.reduce((prev, current) => {
      if (shareGoodsSn && current.goodsSn === shareGoodsSn) {
        prev.shareGoodsList.push(current)
      }

      if (current.allSkuStockNum) {
        prev.goodsList.push(current)
      } else {
        prev.goodsList_saleOut.push(current)
      }
      return prev
    }, {
      goodsList: [],
      goodsList_saleOut: [],
      shareGoodsList: []
    })

    return {
      goodsList,
      goodsList_saleOut,
      shareGoodsList
    }
  },

  /**
   * 获取门店推荐商品
   */
  getStoreRecommendGoods() {
    return new Promise(async (resolve) => {
      const { shareGoodsSn } = this._data
      const { cityCode, deliveryCenterCode, storeCode } = wx.getStorageSync('timelyCity') || {}
      const params = {
        storeCode,
        cityCode,
        deliveryCenterCode,
        customerID: app.globalData.customerID || -1,
        categoryCode: 'appstorerecommend'
      }
      let storeRecommendGoods = []
      let storeSoldOutGoods = []
      let storeShareGoods = []
      try {
        const res = await this.getCategoryGoodsData(params)
        const { data, systemTime } = res
        const { goodsList, goodsList_saleOut, shareGoodsList } = await this.getAllInfoGoodsList({
          data: data || [],
          storeCode,
          cityCode,
          deliveryCenterCode,
          shareGoodsSn,
          isNeedMergeByGroup: false,
          isNeedMergeMultiSpec: false
        })
        storeShareGoods = shareGoodsList

        const { onSaleGoodsList, soldOutGoodsList } = this.filterGoodsList({
          onSaleGoodsList: goodsList,
          soldOutGoodsList: goodsList_saleOut,
          shareGoodsList
        })
        storeSoldOutGoods = soldOutGoodsList || []
        storeRecommendGoods = (onSaleGoodsList || []).map(item => ({...item, systemTime}))
      } catch(err) {
        console.log('getStoreRecommendGoods', err);
      }
      // 说不清的历史原因返回的门店推荐商品包含了分享商品，此处是有问题的，需要在插入门店的时候过滤分享商品
      Object.assign(this._data, { storeRecommendGoods, storeSoldOutGoods, storeShareGoods })
      resolve()
    })
  },
  /**
   * Get the list of online price goods.
   * @returns {Promise<Array>} A promise that resolves to an array of online price goods.
   */
  async getOnlinePriceGoodsList() {
    const { storeCode, cityCode, deliveryCenterCode } = wx.getStorageSync('timelyCity') || {}
    const onlinePriceGoodsList = await getOnlinePriceGoodsList({
      storeCode, 
      deliveryCenterCode, 
      cityCode, 
      isNeedMerge: true,
      isFliterNewGoods: false,
    })
    this._data.onlinePriceGoodsList = onlinePriceGoodsList
  },
  /**
   * @description 检查是否为特价分类
   * @param {*} categoryID 
   */
  checkIsOnlinePriceCategory(categoryID) {
    const onlinePriceFlag = '100107a00'
    return categoryID.includes(onlinePriceFlag)
  },
  /**
   * @description 检查是否有特价分类
   * @param {*} categoryID 
   */
  checkHasOnlinPriceCategpry(categoryList) {
    const onlinePriceIndex = categoryList.findIndex(item => {
      if (this.checkIsOnlinePriceCategory(item.categoryID)) {
        item.categoryType = 'onlinePrice'
        return item
      }
    });
    if (onlinePriceIndex === -1) {
      return
    }
    
    // // 如果找到了，将该元素从原位置移除
    // const item = categoryList.splice(onlinePriceIndex, 1)[0];
    // // 将该元素插入到数组的开头
    // categoryList.unshift(item);
    // 有特价商品
    if (this._data.onlinePriceGoodsList.length > 0) {
      return
    }
    // 无特价商品删除该分类
    categoryList.splice(onlinePriceIndex, 1)
  },
  /**
   * 组装完整品类列表
   * 排序：门店推荐 > 近期常买 > 普通商品类目
   * 分享商品会固定在第一类目的第一个商品
   */
  setFullCategory() {
    const { categoryList = [], storeRecommendGoods = [], storeSoldOutGoods = [], storeShareGoods = [], shareGoodsSn, recentList = [] } = this._data
    // 城市不支持b2c服务，剔除掉全国送品类
    if (!!categoryList.length) {
      const { supportBToCService = false } = wx.getStorageSync('timelyCity') || {}
      if (!supportBToCService) {
        const index = categoryList.findIndex(item => item.categoryName === '全国送')
        if (index !== -1) {
          categoryList.splice(index, 1)
        }
      }
    }
    
    const addCate = []
    // 追加门店推荐品类(storeRecommendGoods里全是推荐商品的情况,不显示门店推荐)
    // if ((storeRecommendGoods.length && (storeRecommendGoods.length > storeShareGoods.length)) || storeSoldOutGoods.length) {
    //   addCate.push({
    //     categoryName: '门店推荐',
    //     categoryID: '-9999',
    //     goodsCount: storeRecommendGoods.length
    //   })
    // }
    // 分类增加插入门店推荐商品标识
    this.insertStoreRecommendFlag(categoryList)
    // 检查是否有线上专享特价分类
    this.checkHasOnlinPriceCategpry(categoryList)
    if (recentList.length) {
      addCate.push({
        categoryName: '近期买过',
        categoryID: REENCENT_CATE_NAME,
        goodsCount: recentList.length
      })
    }
    categoryList.unshift(...addCate)
    this.updateCateList(categoryList)
    this.setData({
      categoryList
    })
    if (!categoryList.length) {
      // 无品类列表，展示无门店缺省页
      this.setData({
        currentView: 'noStore'
      })
      // 此时节点移除，需要销毁Observer
      this.disconnectCateSwitchObserver()
      return
    }
    const currCategory = categoryList.find(item => item.categoryID === this._data.currCategory?.categoryID)
    if (!currCategory) {
      const defaultCategory = categoryList[0]
      this._data.needShareGoodsSn = shareGoodsSn ? { [defaultCategory.categoryID]: shareGoodsSn } : {}
      this.setData({
        currCategory: defaultCategory
      })
      this.initSplabelList(defaultCategory)
    } else {
      const index = categoryList.findIndex(item => item.categoryID === currCategory.categoryID)  
      this.setData({
        intoViewId: index,
        currCategory
      })
      this.initSplabelList(currCategory)
    }
    this.getGoodsList(true)
  },

  initSplabelList(initCategory) {
    // 设置当前品类下标签
    if (initCategory.spLabelList) {
      this._data.spLabelList = initCategory.spLabelList || []
    }
  },
  /**
   * 设置门店推荐商品
   */
  setStoreRecommendGoods() {
    const { storeRecommendGoods = [], storeSoldOutGoods = [] } = this._data
    this.setData({
      storeSoldOutGoods
    })
    this.lazyloadList({
      type: 'appstorerecommend',
      onSaleGoodsList: storeRecommendGoods,
      soldOutGoodsList: storeSoldOutGoods,
      allGoodsList: [...storeRecommendGoods, ...storeSoldOutGoods]
    })
  },

  showCityOffShelfTips() {
    this._data.hasShowShareGoodsToast || wx.showToast({ icon: 'none', title: '商品已失效，看看别的吧~' })
    this._data.hasShowShareGoodsToast = true
  },

  /**
   * 分享商品时,过滤掉按一品多规/多拼规则后显示商品sku一致的商品
   */
  filterGoodsList({ onSaleGoodsList, soldOutGoodsList, shareGoodsList } = {}) {
    const { resolve: onShowResolve } = this._data.onShowReportData
    if (!(shareGoodsList && shareGoodsList.length)) {
      onShowResolve({ shareGoodsList: [] })
      return { onSaleGoodsList, soldOutGoodsList }
    }
    const shareDefaultList = shareGoodsList.map(goods => {
      goods.shareGoods = true
      return getDefaultGoods(goods).goodsObj
    })
    const shareDefaultSkus = shareDefaultList.map(goods => goods.goodsSn)
    const [soldOutList] = [ soldOutGoodsList].map(list => (list || []).filter(goods => !shareDefaultSkus.includes(getDefaultGoods(goods).goodsObj.goodsSn)))
    const onSaleList = onSaleGoodsList
    const isCityOffShelf = shareDefaultList.some(goods => (('citySaleStatus' in goods) ? goods.citySaleStatus : goods.saleStatus) === 0)
    if (isCityOffShelf) {
      this.showCityOffShelfTips()
    } else {
      // 不是城市下架,才显示在商品列表里
      onShowResolve({ shareGoodsList: shareGoodsList })
      onSaleList.unshift(...shareGoodsList)
    }
    return {
      onSaleGoodsList: goodsListUniqueHandle(onSaleList),
      soldOutGoodsList: soldOutList,
    }
  },

  /**
   * 获取品类列表
   */
  async getCategorylist() {
    return new Promise(async (resolve) => {
      const { storeCode, cityCode } = wx.getStorageSync('timelyCity') || {}
      const param = {
        storeCode,
        cityCode
      }
      let categoryList = []
      try {
        const res = await app.api.getCategoryList({ data: param, isLoading: true})
        categoryList = res.data || []
      } catch(err) {
        console.log('getCategorylist err', err);
      }
      this.updateCateList(categoryList)
      this._data.categoryList = categoryList
      resolve()
    })
  },

  updateCateList (list) {
    list.forEach((item, index) => {
      item.cateIdx = index
    })
  },

  /**
   * 加载商品数据
   */
  async getGoodsList(flag = false) {
    const { currCategory } = this.data
    // if (currCategory.categoryID === '-9999') {
    //   // 门店推荐
    //   if (flag) {
    //     this.setStoreRecommendGoods()
    //   } else {
    //     await this.getStoreRecommendGoods()
    //     this.setStoreRecommendGoods()
    //   }
    // } else if (currCategory.categoryID === REENCENT_CATE_NAME) { 
    if (this.checkIsOnlinePriceCategory(currCategory.categoryID)) { // 门店特价
      this.getOnlinePriceCategoryGoodsList(currCategory.categoryID)
    } else if (currCategory.categoryID === REENCENT_CATE_NAME) {
      this.setRecentBuyGoods()
    } else if (currCategory.categoryName === '全国送') {
      // 全国送品类
      this.getB2CGoods()
    } else {
      // 其他品类
      this.getCategoriesGoods()
    }
    this.isIOS() && this.setData({
      canScroll: true
    })
  },

  /**
   * 获取品类列表商品
   */
  async getCategoriesGoods() {
    const { storeShareGoods, needShareGoodsSn } = this._data
    const { storeCode, cityCode, deliveryCenterCode } = wx.getStorageSync('timelyCity') || {}
    const {  currCategory: { categoryID, _categoryType } } = this.data
    const shareGoodsSn = needShareGoodsSn[categoryID]
    const hasShareGoodsList = shareGoodsSn && storeShareGoods.length
    const param = {
      storeCode,
      cityCode,
      categoryCode: categoryID,
      deliveryCenterCode,
    }
    if (storeCode) {
      Object.assign(param, {
        storeCode
      })
    }
    try {
      const res = await this.getCategoryGoodsData(param)
      const { data, systemTime } = res
      let shareGoodsList = []
      if (data && hasShareGoodsList) {
        shareGoodsList = storeShareGoods
      }
      let { goodsList, goodsList_saleOut } = await this.getAllInfoGoodsList({
        data: data || [],
        storeCode,
        cityCode,
        deliveryCenterCode,
        shareGoodsSn
      })
      // 插入门店推荐商品到有标识的分类商品里面
      if (_categoryType === 'insertStoreRecommend') {
        const { onSaleList, soldOutList } = this.insertStoreRecommendGoods({
          goodsList,
          goodsList_saleOut,
        })
        goodsList = onSaleList || []
        goodsList_saleOut = soldOutList || []
      }
      let { onSaleGoodsList, soldOutGoodsList } = this.filterGoodsList({
        onSaleGoodsList: goodsList,
        soldOutGoodsList: goodsList_saleOut,
        shareGoodsList
      })
      onSaleGoodsList = onSaleGoodsList || []
      soldOutGoodsList = soldOutGoodsList || []
      const allGoodsList = [...onSaleGoodsList, ...soldOutGoodsList].map(item => ({...item, systemTime}))
      /*** 智慧零售相关逻辑 ***/
      const { goodsID = ''} = this._data.option || {}
      if (!!goodsID) {
        // 将该商品放在商品列表首个位置
        const itemIndex = allGoodsList.findIndex(item =>{
          return String(item.eshopGoodsId) === String(goodsID)
        })
        if (itemIndex !== -1) {
          const item = allGoodsList.splice(itemIndex, 1)
          allGoodsList.unshift(item[0])
        }
        this._data.option.goodsID = ''
      }
      /*** 智慧零售相关逻辑 ***/

      this.lazyloadList({
        type: param.categoryCode,
        onSaleGoodsList,
        soldOutGoodsList,
        allGoodsList
      })
      this._data.cachGoods = {
        type: param.categoryCode,
        onSaleGoodsList,
        soldOutGoodsList,
        allGoodsList
      }
      // 此处要把品类商品拆分成各个标签
      this.splitTagGoods(util.deepClone(onSaleGoodsList), util.deepClone(soldOutGoodsList))
    } catch(err) {
      console.log('getCategoriesGoods', err)
      this.toggleLoadingStatus(false)
      this.toggleRequestError(true)
    }
  },

  /**
   * 设置位置参数
   */
  locate() {
    const currAddr = wx.getStorageSync('selectedAddress')
    const { cityID, cityCode, storeName, storeCode, cityName, lat, lon, address } = wx.getStorageSync('timelyCity') || {}
    // 定位成功，且附近有门店.有收货地址则取收货地址，无收货地址则取定位地址
    if (currAddr) {
      const { cityName, lat, lon, gisAddress } = currAddr
      this.setData({
        cityName,
        chargeLocalat: lat,
        chargeLocalon: lon,
        locaDetailInfo: gisAddress
      })
    } else {
      this.setData({
        cityName,
        chargeLocalat: lat,
        chargeLocalon: lon,
        locaDetailInfo: address
      })
    }

    //  切换门店后清空缓存数据
    if (storeCode !== this.data.storeCode) {
      this._data.categoryDataCache = {}
    }
    this.setData({
      cityID,
      cityCode,
      storeCode: storeCode || '',
      storeName: storeName || ''
    })
    // 获取优惠券数据
    this.getCoupons()
  },

  /**
   * 点击品类
   */
  clickCategory(e) {
    const currCategory = this.data.currCategory
    const clickCategory = e.currentTarget.dataset.item
    this._data.isClickTag = false
    if (currCategory.categoryID === clickCategory.categoryID) return
    this.setData({
      currCategory: clickCategory,
    })
    this.selectCategory()
    if (app.globalData.reportSensors && clickCategory.categoryID !== '-9999') {
      app.sensors.track('MPClick', {
        element_code: '130001001',
        element_name: clickCategory.categoryName,
        element_content: clickCategory.categoryName,
        screen_code: '1300',
      })
      app.sensors.track("MPSwitch", {
        mp_screen_name: '及时达首页',
        mp_switchObject: '品类',
        mp_from: currCategory.name || currCategory.categoryName,
        mp_to: clickCategory.categoryName
      })
    }
  },

  /**
   * 切换品类
   */
  selectCategory() {
    const { categoryID, spLabelList } = this.data.currCategory
    const index = this.data.categoryList.findIndex(item => item.categoryID === categoryID)
    this.toggleLoadingStatus(true)
    this.setData({
      intoViewId: Math.max(index - 4, 0)
    })
    // 请求新品类数据前先清空当前展示数据
    this.setData({
      'swiperList[0]': []
    })
    this.makeSwiperItem2Loading()
    this.isIOS() && this.setData({
      canScroll: false
    })
    this.getGoodsList()
    this._data.spLabelList = spLabelList || []
    // 切换品类，置空标签
    this.clearTag()
    const list = this.getCategoryLabel(categoryID)
    this.setData({
      spLabelList: list,
      tagScrollIntoView: '',
    })
  },

  refreshCategoryGoods () {
    this.selectCategory()
  },

  /**
   * 修改加载状态
   */
  toggleLoadingStatus (bool) {
    this.setData({
      dataLoading: bool
    })
    // loading时
    if (bool) {
      // 隐藏售罄按钮
      this.toggleSoldOutBtn(false)
      // 隐藏请求错误提示
      this.toggleRequestError(false)
      // 隐藏无商品缺省
      this.toggleGoodsListDefault(false)
    }
    // loading 时不展示 nexttip
    this.toggleNextCateTip(!bool)
  },

  /**
   * 选择地址
   */
  selectAddress () {
    sensors.track('MPClick', 'categoryDeliveryAddress')
    const homeDeliveryObj = {
      lat: this.data.chargeLocalat,
      lon: this.data.chargeLocalon,
      locaDetailInfo: this.data.locaDetailInfo
    }
    util.navigateTo({
      url: '/homeDelivery/pages/addressList/index',
      param: {
        homeDeliveryObj
      }
    })
  },

  /**
   * 分享
   */
  onShareAppMessage: function() {
    const { storeID = 0, storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
    wx.reportAnalytics('share_success')
    if (app.globalData.reportSensors) {
      app.sensors.track('MPShare', {
        mp_shareTitle: '全球好水果 直供百果园',
        activity_ID: '',
        activity_Name: '',
        groupID: '',
        openerID: '',
        currentCount: '',
        storeID,
        storeName,
        storeNum: storeInfo.storeCode || ''
      })
    }
    return {
      title: '全球好水果 直供百果园',
      path: 'pages/homeDelivery/index',
      imageUrl: 'https://resource.pagoda.com.cn/dsxcx/images/f569db27ac02b34b9e040ccb05893424.png'
    }
  },
  /**
   * 跳转搜索
   */
  navigateToSearch(e){
    sensors.track('MPClick', 'categorySearch')
    const { type = 'normal' } = e.detail
    wx.navigateTo({
      url: `/userA/pages/searchGoods/index?keywordsSearch=${type}&source=及时达品类页`,
    })
  },

  /**
   * 倒计时完成handler
   */
  handleCountComplete: util.throttle(function () {
    this.getGoodsList()
  }),

  /**
   * 打开优惠券弹窗
   */
  openCouponPopup(e) {
    this.setData({
      showCouponMore: true
    })
    // 上报神策
    const { sensorskey } = e.currentTarget.dataset
    sensorskey && sensors.track('MPClick', sensorskey)
  },

  /**
   * 关闭优惠券弹窗
   */
  closeCouponPopup(e) {
    this.setData({
      showCouponMore: false
    })
    // 上报神策
    const { sensorskey } = e.currentTarget.dataset
    sensorskey && sensors.track('MPClick', sensorskey)
  },

  /**
   * 上报神册埋点
   */
  sensorsReport({ type, item, index }) {
    if (!app.globalData.reportSensors) return
    // 点击弹窗上报神策
    if (type === "banner") {
      app.sensors.track('MPClick', {
        element_code: `13000700${index+1}`,
        element_name: `轮播banner0${index + 1}`,
        element_content: `轮播banner0${index + 1}`,
        banner_id: item.bannerID,
        banner_name: item.name,
        screen_code: '1300',
        screen_name: '水果外卖'
      })
    } else if (type === 'bounceWindow') {
      sensors.track('MPClick', 'categoryPopup')
    }
  },


  /**
   * 点击展开售罄商品列表
   */
  showSoldOutGoodsList () {
    const current = this._data.lazyloadInstance.lazyloadNextPart()
    this._data.lazyloadCurrentIdx = current
    this.toggleSoldOutBtn()
  },

  /**
   * 切换下一个分类
   */
  changeNextCategory() {
    const { categoryList } = this.data
    let { index, hasNext } = this.hasNextCategory()
    if (!hasNext) return
    index++
    this.setData({
      currCategory: categoryList[index],
      intoViewId: index >= categoryList.length - 5 ? index : index - 4
    })
    this.selectCategory()
  },

  /**
   * 切换上一个分类
   */
  changePreCategory() {
    const { categoryList } = this.data
    let { index, hasPre } = this.hasPreCategory()
    if (!hasPre) return
    index--
    this.setData({
      currCategory: categoryList[index],
      intoViewId: Math.max(index - 4, 0)
    })
    this.selectCategory()
  },

  hasNextCategory () {
    const { categoryList, currCategory } = this.data
    const index = categoryList.findIndex(item => item.categoryID === currCategory.categoryID)
    return {
      hasNext: index !== categoryList.length - 1,
      index
    }
  },

  hasPreCategory () {
    const { categoryList, currCategory } = this.data
    const index = categoryList.findIndex(item => item.categoryID === currCategory.categoryID)
    return {
      hasPre: index !== 0,
      index
    }
  },

  /**
   * category-coupon组件领取回调
   */
  receiveCoupon(e) {
    const {
      detail: {
        batchNum
      }
    } = e
    this.receiveCoupons([batchNum])
  },

  /**
   * 一键领取
   */
  receiveAll() {
    // 上报神策
    sensors.track('MPClick', 'categoryCouponGetAll')
    const batchList = []
    this.data.couponList.forEach(item => {
      const { batchNum, receiveStatus } = item
      if (receiveStatus === 'N') {
        batchList.push(batchNum)
      }
    })
    if (!batchList.length) {
      return
    }
    this.receiveCoupons(batchList)
  },

  receiveCoupons: util.throttle(async function(batchList) {
    const params = {
      batchList,
      customerID: this.data.userID
    }
    try {
      const res = await app.api.receiveActivityCoupons(params)
      const { receiveCount = 0, wxminiCardCoupon = {}, coupons = [] } = res.data || {}
      console.log('category_receiveCoupons_wxminiCardCoupon=' + wxminiCardCoupon ? JSON.stringify(wxminiCardCoupon) : wxminiCardCoupon)
      if (!!receiveCount) {
        const { success, cardCoupon = {} } = this.checkIsInsertCoupon(wxminiCardCoupon)
        if (success) {
          this.couponOpen(cardCoupon)
        } else {
          wx.showToast({
            icon: 'none',
            title: `已为您领取成功${receiveCount}张优惠券`,
          })
        }
      }
      this.updateCoupons(batchList, coupons)
    } catch (error) {
      console.log(error)
      app.apiErrorDialog(error)
    }
  }, 1000),

  /**
   * 更新优惠券信息
   */
  updateCoupons(batchList, coupons) {
    // 领取单张券，不去刷新接口，只更新已领取券信息，多张券，刷新接口（刷新接口，券排序会更改）
    if (batchList.length === 1 && !!coupons && coupons.length > 0) {
      const { couponList } = this.data
      const index = couponList.findIndex(item => {
        return item.batchNum === batchList[0] && item.receiveStatus === 'N'
      })
      if (index !== -1) {
        const { couponCode, couponId, expireTime } = coupons[0]
        Object.assign(couponList[index], {
          receiveStatus: 'Y',
          defineId: couponId,
          expireTime,
          couponCode
        })
        this.setCouponParams(couponList)
      }
    } else {
      this.getCoupons()
    }
  },

  /**
   * 插入微信卡包
   */
  couponOpen(cardCoupon) {
    try {
      console.log("category_couponOpen:", JSON.stringify(cardCoupon))
      this.setData({
        cardCoupon,
        cardCouponIsShow: true,
      })
    } catch (error) {}
  },

  /**
   * 关闭微信卡包
   */
  closeCardCoupon() {
    this.setData({
      cardCoupon: {},
      cardCouponIsShow: false,
    })
  },

  /**
   * 确认插入卡包回调
   */
  comfirmCardCoupon({detail}) {
    console.log('categort_comfirmCardCoupon', detail)
    const { send_coupon_result = [] } = detail;
    // 发券结果
    const hasSuccessCoupon = send_coupon_result.some(item => {
      return item.code === 'SUCCESS' || item.code === 'DUPREQUEST'
    })
    if (hasSuccessCoupon) {
      wx.showToast({
        title: '已加入卡包',
        duration: 1000
      })
    } else {
      const { message = '插入卡包失败，请稍后再试' } = send_coupon_result[0] || {}
      wx.showToast({
        icon: 'none',
        title: message,
        duration: 1000
      })
    }
  },

  /********* */
  // 懒加载处理

  /**
   * @desc 计算初次渲染的列表个数，要保证渲染之后，lazyLoadBox模块在视窗之外
   * 例外情况：非售罄商品加一个售罄商品不足一屏，此时lazyLoadBox模块在视窗内，这种时候点击展开售罄会继续加载
   */
  setLazyloadListInitLen () {
    const { windowWidth, windowHeight } = wx.getStorageSync('systemInfo') || {}
    if (!windowWidth || !windowHeight) {
      Object.assign(this._data, {
        lazyloadListPartLength: DEFAULT_PART_SIZE,
        actualGoodsItemHeight: DEFAULT_GOODS_ITEM_HEIGHT/2
      })
      return
    }
    const ratio = windowWidth / STANDARD_PIXEL_WIDTH
    const actualGoodsItemHeight = DEFAULT_GOODS_ITEM_HEIGHT * ratio // 实际高度px

    const partLen = Math.ceil(windowHeight / actualGoodsItemHeight)
    console.log('setLazyloadListInitLen', partLen);
    Object.assign(this._data, {
      // 先不用动态计算，直接取第一部分为10个
      lazyloadListFirstPartLength: 10,
      lazyloadListPartLength: partLen,
      actualGoodsItemHeight
    })
  },

  /**
   * @desc 生成商品列表的子项长度
   * 默认第一项最少为10个，不足则取非售罄数量;
   * 展示列表包含售罄的，在非售罄列表最后拼接一个售罄商品，点击展开再加载售罄，售罄商品同样需要懒加载;
   * eg: onSaleLen = 25; soldOutLen = 9; size = 5
   * return [10, 5, 5, 6, 8]
   * eg: onSaleLen = 15; soldOutLen = 9; size = 5
   * return [10, 6, 8]
   * eg: onSaleLen = 10; soldOutLen = 10; size = 5
   * return [11, 9]
   * eg: onSaleLen = 14; soldOutLen = 10; size = 5
   * return [10, 5, 9]
   * eg: onSaleLen = 8; soldOutLen = 9; size = 5
   * return [9, 8]
   * eg: onSaleLen = 2; soldOutLen = 10; size = 7
   * return [3, 9]
   *
   * @param {number} onSaleLen 非售罄长度
   * @param {number} soldOutLen 售罄长度
   * @returns { { subSize: Array<number>; soldOutLen: number } }
   */
  genSubListSize (onSaleLen, soldOutLen) {
    console.log('onSaleLen, soldOutLen', onSaleLen, soldOutLen);
    if (soldOutLen) {
      soldOutLen-= 1
      onSaleLen+=1
    }
    const arr = []
    const { lazyloadListPartLength, lazyloadListFirstPartLength } = this._data
    let firstSize = 0
    if (onSaleLen < (lazyloadListFirstPartLength + lazyloadListPartLength)) {
      firstSize = onSaleLen
      onSaleLen = 0
    } else {
      firstSize = lazyloadListFirstPartLength
      onSaleLen -= lazyloadListFirstPartLength
    }
    arr.push(firstSize)

    // 子项长度
    const size = lazyloadListPartLength
    const remainder = onSaleLen % size
    arr.push(
      ...Array.from({length: Math.floor(onSaleLen/size) - 1}, () => size),
    )
    if (onSaleLen) {
      arr.push(onSaleLen <= size ? onSaleLen : size + remainder)
    }
    // 记录此时售罄项的索引
    const soldOutIndex = arr.length
    if (soldOutLen) {
      const remainder = soldOutLen % size
      arr.push(
        ...Array.from({length: Math.floor(soldOutLen/size) - 1}, () => size),
        soldOutLen <= size ? soldOutLen : size + remainder
      )
    }

    console.log('genSubListSize', arr)

    return {
      subSize: arr,
      soldOutLen,
      soldOutIndex
    }
  },

  lazyloadList ({
    type,
    onSaleGoodsList = [],
    soldOutGoodsList = [],
    allGoodsList = []
  }) {
    /**当前品类类型 */
    const currentCateGoryType = this.getCurrentCateGoryType()
    //  设置了当前品类类型，但是执行lazyloadList时，传入type不为当前品类类型
    if (currentCateGoryType && currentCateGoryType !== type) {
      //  无需往下执行，因为这种场景属于频繁切换品类。此刻currentCateGoryType已经是新的品类了
      return
    }

    if (allGoodsList.length) {
      const { lazyloadInstance } = this._data
      const {
        subSize, soldOutLen, soldOutIndex
      } = this.genSubListSize(onSaleGoodsList.length, soldOutGoodsList.length)
      lazyloadInstance.resetPartSize(subSize)
      const { length, current } = lazyloadInstance.showNewList(allGoodsList)
      Object.assign(this._data, {
        soldOutLen,
        soldOutIndex,
        lazyloadLength: length,
        lazyloadCurrentIdx: current
      })
    } else {
      // 没有商品时
      this.toggleGoodsListDefault(true)
    }

    wx.nextTick(() => {
      this.toggleLoadingStatus(false)
      this.toggleSoldOutBtn()
    })

    //  更新当前品类的缓存
    this._data.categoryDataCache[currentCateGoryType] = allGoodsList.slice(0, 6)
  },

  /**
   * @desc 判断售罄按钮是否显示
   * 显示条件：
   * 1. loading时不展示
   * 2. soldOutLen为0时不展示
   * 3. 懒加载的索引没到售罄时不展示：lazyloadCurrentIdx < soldOutIndex
   */
  toggleSoldOutBtn (bool) {
    const setData = (bool) => {
      if (this.data.showSoldOutBtn === bool) return
      this.setData({ showSoldOutBtn: bool })
    }
    // 第一种情况：loading时不展示
    if (typeof bool !== "undefined") return setData(bool)
    // 第二三种情况
    const { soldOutIndex, lazyloadCurrentIdx, soldOutLen } = this._data
    setData(
      !!soldOutLen &&
      soldOutIndex - 1 === lazyloadCurrentIdx
    )
  },

  onShowChoiceLayer(e) {
    // 在这判断弹出的加购弹窗是不是分享商品的弹窗
    this._data.isShareGoodsSku = !!e.currentTarget.dataset.shareGoods
    this.setData({
      shareGoods: this._data.isShareGoodsSku
    })
  },

  skuAddCart({ detail: { goodsInfo: { goodsSn, goodsName } } }) {
    this._data.isShareGoodsSku && sensors.trackClickEvent({
      element_code: 130003005,
      element_name: '分享加购弹窗',
      element_content: '分享加购弹窗',
      screen_code: 1300,
      SKU_ID: goodsSn,
      SKU_NAME: goodsName
    })
  },

  /**
   * @desc 判断网络异常提示图是否展示
   */
  toggleRequestError (bool) {
    this.setData({
      showRequestError: bool
    })
  },

  /**
   * @desc 商品缺省判断
   */
  toggleGoodsListDefault (bool) {
    this.setData({
      showNoGoodsDefault: bool
    })
  },

  toggleNextCateTip (bool) {
    this.setData({
      showNextCateTip: bool
    })
  },

  /********* */

  /************************ */
  /* 见 `切换动效.md` */
  /**
   * @desc wxs调用
   */
   setSwiper () {
    // 判断提示语区域是否在可视区域
    const { isTop, isBottom } = this._data.categoryOuterBound

    // 判断 前/后 是否还有品类列表
    const { hasNext } = this.hasNextCategory()
    const { hasPre } = this.hasPreCategory()
    if (isTop) {

      hasPre && this.changePreCategory()
    } else if (isBottom) {

      hasNext && this.changeNextCategory()
    }
  },
  /**
   * @desc 将swiperItem2设为骨架屏
   */
  makeSwiperItem2Loading () {
    const cacheKey = this.getCurrentCateGoryType()
    const data = this._data.categoryDataCache[cacheKey]

    //  存在缓存数据
    if (data) {
      const { lazyloadInstance } = this._data
      lazyloadInstance.resetPartSize(6)
      //  先设置展示的缓存数据
      lazyloadInstance.showNewList(data, () => {
        this.setData({
          //  通过三元触发数据变化，回到顶部
          scrollTop: this.data.scrollTop === 0 ? 1 : 0
        })
      })
    } else {
      const key = 'swiperList[0]'
      this.setData({
        [key]: []
      })
    }
    return Boolean(data)
  },

  /**
   * @desc 禁止swiper-item滑动
   */
  forbid () {},

  /**
   * @desc 初始化内容模块的监听，边界isTop，isBottom用于wxs里的逻辑判断
   */
  initContentObserver () {
    const keyMap = {
      top: 'isTop',
      bottom: 'isBottom'
    }
    this._data.scrollContentObserver = ['top', 'bottom'].map(el => {
      const ob = wx.createIntersectionObserver().relativeTo('.goods-box_swiper-item_scroll-wrapper_scrollview');
      // content-top content-bottom
      ob.observe(`#content-${el}`, (ev) => {
        // console.log('initContentObserver', el, !!ev.intersectionRatio);
        const key = `goodsContentScrollBound.${keyMap[el]}`
        this.setData({
          [key]: !!ev.intersectionRatio
        })
      })
      return ob
    })
  },

  /**
   * @desc 初始化提示语模块的监听
   */
  initOuterObserver () {
    const keyMap = {
      top: 'isTop',
      bottom: 'isBottom'
    }
    this._data.scrollOuterObserver = ['top', 'bottom'].map(el => {
      const ob = wx.createIntersectionObserver().relativeTo('#cover-mask');
      // top-outer bottom-outer
      ob.observe(`#${el}-outer`, (ev) => {
        const ratio = Number((ev.intersectionRatio).toFixed(5))
        console.log('outer-intersectionRatio', el, !!ratio)
        this._data.categoryOuterBound[keyMap[el]] = !!ratio;
        // 在出现提示语模块的时候阻止scroll-view滚动
        // ios下在这里设置canScroll为false，会导致scroll-view卡顿一下
        // 所以只在非ios下设置为false，ios下要在清空数据时设置为false
        // 为true时，不限制
        if (this.isIOS()) {
          !ratio && this.setData({
            canScroll: !ratio
          })
        } else {
          // this.setData({
          //   canScroll: !ratio
          // })
        }
      })
      return ob
    })
  },

  /**
   * @desc 初始化懒加载区域的监听，只有当 dataset.skeleton为false 且 intersectionRatio不为0时才触发懒加载
   */
   initlazyLoadObserver () {
    this._data.lazyLoadBoxObserver = wx.createIntersectionObserver().relativeToViewport({
      bottom: this._data.actualGoodsItemHeight
    }).observe('#lazyLoadBox', (ev) => {
      // 标签下面不下拉刷新
      if (this.data.currCategoryTagId) {
        return
      }
      const { intersectionRatio, dataset } = ev
      const { skeleton = false } = dataset
      // console.log('lazyLoadBox', !!intersectionRatio, skeleton, this.data.dataLoading)
      const { lazyloadCurrentIdx, soldOutIndex } = this._data

      // 剩售罄项时，只能通过点击售罄按钮触发加载
      if (soldOutIndex - 1 === lazyloadCurrentIdx) return
      if (!skeleton && !!intersectionRatio && !this.data.dataLoading) {
        console.log('should load');
        const current = this._data.lazyloadInstance.lazyloadNextPart()
        this._data.lazyloadCurrentIdx = current
        this.toggleSoldOutBtn()
      }
    })
  },

  initCateSwitchOvserver () {
    const {
      scrollContentObserver,
      scrollOuterObserver,
      lazyLoadBoxObserver
    } = this._data
    // 如果不为空则说明未销毁
    if (scrollContentObserver) {
      console.log(`scrollContentObserver 未销毁`)
      return
    }
    if (scrollOuterObserver) {
      console.log(`scrollOuterObserver 未销毁`)
      return
    }
    if (lazyLoadBoxObserver) {
      console.log(`lazyLoadBoxObserver 未销毁`)
      return
    }
    this.initContentObserver()
    this.initOuterObserver()
    this.initlazyLoadObserver()
  },

  /**
   * @desc 销毁
   */
  disconnectCateSwitchObserver() {
    if (Array.isArray(this._data.scrollContentObserver)) {
      this._data.scrollContentObserver.forEach(el => el.disconnect())
    }
    if (Array.isArray(this._data.scrollOuterObserver)) {
      this._data.scrollOuterObserver.forEach(el => el.disconnect())
    }
    this._data.lazyLoadBoxObserver && this._data.lazyLoadBoxObserver.disconnect()
  },
  /**
   * @description 门店推荐商品含有分享商品，需要过滤掉
   */
  filterShareGoods(goodsList) {
    return goodsList.filter( goods => !goods.shareGoods)
  },
  // 将门店推荐商品放在自定义分类的第一个商品里面,并且过滤重复商品(单规格过滤)
  insertStoreRecommendGoods({  goodsList, goodsList_saleOut }) {
    let { storeRecommendGoods = [], storeSoldOutGoods = [] } = this._data
    storeRecommendGoods = filterNoPriceActivityGoods(this.filterShareGoods(storeRecommendGoods))
    storeSoldOutGoods = filterNoPriceActivityGoods(this.filterShareGoods(storeSoldOutGoods))
    if (storeRecommendGoods.length || storeSoldOutGoods.length) {
      const recommondGoodsSns = storeRecommendGoods.concat(storeSoldOutGoods).map(goods => goods.goodsSn)
      const [onSaleList, soldOutList] = [goodsList, goodsList_saleOut].map(list => (list || []).filter( goods => {
        if (goods.specificationGoodsList && goods.specificationGoodsList.length <= 1)  {
          return !recommondGoodsSns.includes(goods.goodsSn)
        }
        return true
     }))
     
      return {
        onSaleList: storeRecommendGoods.concat(onSaleList),
        soldOutList: storeSoldOutGoods.concat(soldOutList)
      }
    }
    return {
      onSaleList: goodsList, soldOutList: goodsList_saleOut
    }
  },
  // 插入门店推荐标识
  insertStoreRecommendFlag(categoryList) {
    const insertCategory = categoryList.find(item => !this.checkIsOnlinePriceCategory(item.categoryID))
    if (insertCategory) {
      insertCategory._categoryType = 'insertStoreRecommend'
    }
  },
    /**
   * @description 为了不影响正常分类加载商品逻辑，此处重写
   * @param {*} categoryCode 
   */
  async getOnlinePriceCategoryGoodsList(categoryCode) {
    const { storeShareGoods, needShareGoodsSn } = this._data
    const { storeCode, cityCode, deliveryCenterCode } = wx.getStorageSync('timelyCity') || {}
    const {  currCategory: { categoryID } } = this.data
    const shareGoodsSn = needShareGoodsSn[categoryID]
    const hasShareGoodsList = shareGoodsSn && storeShareGoods.length
    try {
      const data = this._data.onlinePriceGoodsList || []
      let shareGoodsList = []
      if (data && hasShareGoodsList) {
        shareGoodsList = storeShareGoods
      }
      const { goodsList, goodsList_saleOut } = await this.getAllInfoGoodsList({
        data: data || [],
        storeCode,
        cityCode,
        deliveryCenterCode,
        shareGoodsSn
      })
      let { onSaleGoodsList, soldOutGoodsList } = this.filterGoodsList({
        onSaleGoodsList: goodsList,
        soldOutGoodsList: goodsList_saleOut,
        shareGoodsList
      })
      onSaleGoodsList = filterPriceActivityGoods(onSaleGoodsList) || []
      soldOutGoodsList = filterPriceActivityGoods(soldOutGoodsList) || []
      const allGoodsList = [...onSaleGoodsList, ...soldOutGoodsList].map(item => ({...item, systemTime: new Date().getTime() }))
      
      this.lazyloadList({
        type: categoryCode,
        onSaleGoodsList,
        soldOutGoodsList,
        allGoodsList
      })
    } catch(err) {
      console.log('getCategoriesGoods', err)
      this.toggleLoadingStatus(false)
      this.toggleRequestError(true)
    }
  },
  clearTag() {
    this.setData({
      spLabelList: [],
      currCategoryTagId: '',
    })
  },
  getAllTagGoods() {
    this.makeSwiperItem2Loading()
    this.getCategoriesGoods()
  },
  /**
   * @description 点击标签
   */
  clickTag(e) {
    const { item } = e.currentTarget.dataset
    const { currCategoryTagId } = this.data
    if (item.id === currCategoryTagId) {
      return
    }
    // 点击全部加载品类下商品
    if (item.id === 0) {
      this.setData({
        currCategoryTagId: 0,
      })
      this.getAllTagGoods()
      this._data.isClickTag = false
      return
    }
    this._data.isClickTag = true
    // 设置标签商品，只在第一次切换标签时调用
    if (!currCategoryTagId) {
      // this.setData({
      //   'swiperList[0]': []
      // })
      this.setData({
        scrollTop: 0,
        'swiperList[0]': [this._data.tagGoodsList]
      })
    }
    this.setData({
      currCategoryTagId: item.id,
      tagScrollIntoView: `tag-${item.id}`,
      goodsScrollIntoView: `tag-${item.id}`,
      showSoldOutBtn: false,
    })

    const { categoryName } = this.data.currCategory
    sensors.clickReport({
      blockName: '品类标签',
      blockCode: '01',
      element_code: '130001002',
      element_name: '卖点标签',
      category_name: categoryName,
    })
    // const { lazyloadInstance } = this._data
    // lazyloadInstance.showNewList(newGoodsList)
  },
  filterTagGoodsList(goodsList, labelId) {
    return goodsList.filter( goods => {
      goods.wxKey = goods.goodsSn + '_' + labelId
      
      const { goodsObj } = getDefaultGoods(goods)
      const { spLabelList = [] } = goodsObj
      if (!spLabelList || !spLabelList.length) {
        return false
      }
      // 标签下商品不展示分类样式
      if (goods.shareGoods) {
        goods.shareGoods = false
      }
      const labelIds = spLabelList.map(tag => tag.id)
      return labelIds.includes(labelId)
    })
  },
  // 过滤售罄分享品
  filterSellOutShareGoods(goodsList = []) {
    if (!goodsList.length) {
      return
    }
    return goodsList.filter( goods => {
      if (goods.shareGoods) {
        const goodsObj = getDefaultGoods(goods).goodsObj
        if (!goodsObj.allSkuStockNum) {
          return false
        }
      }
      return true
    })
  },
  splitTagGoods(optionsSellGoodsList, sellOutGoodsList) {
    const { spLabelList } = this._data
    if (!spLabelList || !spLabelList.length) {
      this.clearTag()
      return
    }
    const sellGoodsList = this.filterSellOutShareGoods(optionsSellGoodsList)
    const tagGoodsList = spLabelList.map( tag => {
      // 在售商品
      const sellLabelGoodsList = this.filterTagGoodsList(sellGoodsList, tag.id)
      // 售罄商品
      const sellOutLabelGoodsList = this.filterTagGoodsList(sellOutGoodsList, tag.id)
      if (sellOutLabelGoodsList.length > 0) {
        const firstSellout= sellOutLabelGoodsList.shift()
        sellLabelGoodsList.push(firstSellout)
      }
      let goodsList = sellLabelGoodsList
      if (sellOutLabelGoodsList.length) {
        goodsList = sellLabelGoodsList.concat([{
          sellOutGoodsList: sellOutLabelGoodsList,
          type: 'sellout'
        }])
      }
      return {
        ...tag,
        goodsList: util.deepClone(goodsList) 
      }
    })
    let newTagGoodsList = []
    const newspLabelList = []
    tagGoodsList.forEach( item => {
      if (item.goodsList.length) {
        newTagGoodsList = newTagGoodsList.concat({
          id: item.id,
          labelName: item.labelName,
          goodsSn: item.id,
          type: 'tag',
          wxKey: item.id,
        }).concat(item.goodsList)
        newspLabelList.push({
          id: item.id,
          labelName: item.labelName,
        })
      }
    })
    this._data.tagGoodsList = newTagGoodsList
    // 有可展示的标签，才追加全部标签
    if (newspLabelList.length > 0) {
      const { categoryName, categoryID } = this.data.currCategory
      const list = [{
        id: 0,
        labelName: '全部',
        wxKey: 0,
      }].concat(newspLabelList)
      this.setData({
        spLabelList: list,
        currCategoryTagId: 0,
      })
      this.cacheCategoryLabel(categoryID, list)
      sensors.exposureReport({
        blockName: '品类标签',
        blockCode: '01',
        element_code: '130001002',
        element_name: '卖点标签',
        category_name: categoryName,
      })
    } else {
      this.clearTag()
    }
  },
  cacheCategoryLabel(category, list) {
    if (!category || !list.length) {
      return
    }
    this._data.categoryLabelDataCache[category] = list
  },
  getCategoryLabel(category) {
    return this._data.categoryLabelDataCache[category] || []
  },
  touchHandle() {},
  isScroll: util.debounce(function() {
    console.log('isScroll')
    const { currCategoryTagId } = this.data
    if (this._data.isClickTag) {
      this._data.isClickTag = false
      return;
    }
    if (!currCategoryTagId) {
      return
    }
    // 创建节点查询器
    const query = wx.createSelectorQuery();
    // 获取所有标签元素
    query.selectAll('.goods-list-item-tag-id').boundingClientRect((rects) => {
      if (!rects || !rects.length) return;
      
      // 找到第一个在可视区域内的标签
      const visibleTag = rects.find(rect => {
        return rect.top >= 0 && rect.top <= 100; // 设置一个合适的判断范围
      });
  
      if (visibleTag) {
        const tagId = visibleTag.id;
        // 更新选中的标签和滚动位置
        this.setData({
          currCategoryTagId: tagId.replace('tag-', ''),
          tagScrollIntoView: tagId,
        });
      }

      // 找到最后一个已经完全离开可视区域的标签
      const lastVisibleTag = rects.reduce((prev, curr) => {
        // 标签完全进入可视区域时才更新选中状态
        if (curr.top <= 50) {
          return curr;
        }
        return prev;
      }, null);

      if (lastVisibleTag) {
        const tagId = lastVisibleTag.id;
        // 更新选中的标签和滚动位置
        this.setData({
          currCategoryTagId: tagId.replace('tag-', ''),
          tagScrollIntoView: tagId
        });
      }
    }).exec();
  },50),
});
