<import src="/bgxxShop/template/suspenseCart/index.wxml"></import>

<wxs module="moduleFn">
  var getNavHeight = function(navList) {
    if (!navList.length) {
      return 0
    }
    var name = navList[0].name
    var pic = navList[0].pic
    if (name && pic) {
      return 140
    }
    return 70
  }
  var checkModuleIsEmpty = function (module) {
    if (module.type === '999') {
      return false
    }
    if ((module.type === '2' || module.type === '3' || module.type === '6')) {
      var goodsList = module.showList.filter( function(goods) {
        return goods
      })
      return goodsList.length
    }
    return true
  }
  module.exports.getNavHeight = getNavHeight
  module.exports.checkModuleIsEmpty = checkModuleIsEmpty
</wxs>
<main-page currentView="{{currentView}}" useScene="vegetables">
  <view wx:if="{{isShowBlank}}" class="scroll-view-blank">
    <image mode="widthFix" src="https://resource.pagoda.com.cn/dsxcx/images/79f174dd8dab1b7f00492faa21607e0f.png"/>
  </view>
	<view id="ob-reference"></view>
	<block wx:if="{{navList.length > 1}}">
		<!-- sticky-nav在scroll-view中使用fixed定位，当使用锚点定位跳转到对应位置时，sticky-nav会先隐藏再出现 -->
		<!-- 所以把fixed定位的导航放到scroll-view外部，就会一直吸顶 -->
		<!-- <sticky-nav id="fixNav" name="fixNav" navList="{{navList}}" sticky="{{false}}" style="z-index: {{showFixNav ? 99 : -99}}" styleConfig="{{pageConfig}}" bind:stickyNav="stickyNav" scrollInfo="{{scrollInfo}}"></sticky-nav> -->
	</block>
	<scroll-view 
    scroll-with-animation="{{isScrollAnimation}}"
    scroll-y="true" style="background: {{pageConfig.backgroundColor}}" class="subject-activity" scroll-into-view="{{toView}}" scroll-top="{{scrollViewTop}}" bindscroll="isScroll"
  >
		<view class="content" wx:if="{{hasData}}">
			<view wx:for="{{subjectData}}" wx:key="index" wx:for-index="outerIndex" wx:for-item="subjectItem" class="subject-item {{subjectData[index].type == 1 && index !== 0 && !!subjectData[index + 1] && subjectData[index + 1].type == 3 ? 'subject-item-pic' : ''}}"
        style="{{subjectItem.type === '999' && subjectItem.navigationList.length > 2 ? 'position: sticky;z-index:1000;top:0px;':''}}"
      >
				<!-- type:7 导航组件模板 -->
				<view class="sv-anchor" wx:if="{{moduleFn.checkModuleIsEmpty(subjectItem)}}">
					<view class="inner-anchor end-anchor {{subjectItem.type === '7' ? 'inner-anchor-nav': ''}}" data-endid="{{subjectItem.navItemIdx - 1}}"></view>
					<view id="{{subjectItem.id}}" style="height:{{moduleFn.getNavHeight(navList)}}rpx" class="{{subjectItem.type === '7' ? 'inner-anchor-nav': ''}} inner-anchor start-anchor" data-startid="{{subjectItem.modulePriority}}"></view>
				</view>
				<!--图片模版-->
				<block wx:if="{{subjectItem.type === '1'}}">
					<view wx:for="{{subjectItem.activityPicList}}" wx:key="index" wx:for-item="subjectImage" class="subject-image" data-item="{{subjectImage}}" bindtap="activityToSkip">
            <subject-pic
              picUrl="{{picDomain + subjectImage.pic}}"
              goodsObj="{{subjectImage.goodsInfo}}"
              goodsTemplate="{{subjectImage.goodsTemplate}}"
              isPurchased="{{subjectImage.isPurchased}}"
              hotZoneStyle="{{subjectImage.hotZoneStyle}}"
              goodsExposeData="{{goodsExposeData}}"
            >
            </subject-pic>
          </view>
				</block>
				<!--优惠券模版-->
				<block wx:elif="{{subjectItem.type === '4'}}">
					<view wx:for="{{subjectItem.couponList}}" wx:key="index" wx:for-item="couponItem" class="subject-image" data-item="{{couponItem}}" bindtap="handleTapCoupon">
						<image src="{{picDomain + couponItem.imgUrl}}" mode="widthFix" show-menu-by-longpress />
					</view>
				</block>
				<view wx:if="{{subjectItem.type === '999' && subjectItem.navigationList.length > 2}}">
					<sticky-nav id="stickyNav" name="stickyNav" navList="{{subjectItem.navigationList}}" styleConfig="{{pageConfig}}" sticky="{{false}}" showFull="{{false}}" bind:stickyNav="stickyNav" bind:noStickyNavHide="noStickyNavHide" scrollInfo="{{scrollInfo}}"></sticky-nav>
				</view>
				<!--商品-->
				<block wx:else>
					<view class="subject-goods {{previewArrangeObject[subjectItem.type]}}">
						<goods-preview wx:for="{{subjectItem.showList}}" wx:key="index" class="{{subjectItemArrangeObject[subjectItem.type]}}" goodsData="{{item}}" styleType="{{styleTypeNum[subjectItem.type]}}" bindaddCart="addCartCall" bindopenDetail="openDetail" moduleIndex="{{outerIndex}}" pageOrgin="topic" goods-expose goods-expose-key="bgxxActivityGoodsExpose" goods-expose-data="{{goodsExposeData}}" goods-special="{{goodsSpecial}}"></goods-preview>
					</view>
					<block wx:if="{{subjectItem.showListStatus}}">
						<pagoda-sellout-item style="flex:1;" sellOutNum="{{subjectItem.hideListNum}}">
							<view class="{{previewArrangeObject[subjectItem.type]}} sellout_goods-box" hover-class="none" hover-stop-propagation="false">
								<goods-preview wx:for="{{subjectItem.sellOutList}}" wx:key="index" class="{{subjectItemArrangeObject[subjectItem.type]}}" goodsData="{{item}}" styleType="{{styleTypeNum[subjectItem.type]}}" bindaddCart="addCartCall" bindopenDetail="openDetail" moduleIndex="{{outerIndex}}" pageOrgin="topic" goods-expose goods-expose-key="bgxxActivityGoodsExpose" goods-expose-data="{{goodsExposeData}}" goods-special="{{goodsSpecial}}" />
							</view>
						</pagoda-sellout-item>
					</block>
				</block>
			</view>
		</view>
    <view wx:else class="no-data">
      <view>
        <image class="icon-no-data" src="https://resource.pagoda.com.cn/dsxcx/images/6709774a863a5234a5aecc00aaa7c21e.png"></image>
        <view class="l-t">这里没有任何内容哦~</view>
        <view class="back-btn" bindtap='backPage'>回首页</view>
      </view>
    </view>

    <!-- 购物车结算栏 -->
    <cart-popup wx:if="{{ showCart }}" orderType="{{ ORDER_TYPE.FRESH }}" />
    <!-- 购物车结算栏 -->
	</scroll-view>

	
</main-page>

<confirm-modal id="globalModal" globalModalInfo="{{globalLocateModalInfo}}"></confirm-modal>

<common-loading />