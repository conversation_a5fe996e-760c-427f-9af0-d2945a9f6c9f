// user/pages/complaints/refundGoods/index.js
import firstRefundMixin from '~/utils/refund/firstRefundMixin'
import { refundSubscript, reportTmplIds } from '../../../../mixins/ticketEntry'
import { refundPathEnum } from '../../../../source/const/order'
import { outLoginType } from '~/source/const/globalConst'

const common = require('../../../../source/js/common').commonObj
const config = require('../../../../utils/config')
const sensors = require('../../../../utils/report/sensors')
const util = require('../../../../utils/util')
const sysInfo = wx.getSystemInfoSync()
const app = getApp()

Page({
  mixins: [firstRefundMixin],

  /**
   * 页面的初始数据
   */
  data: {
    BGXX_PIC_DOMAIN: config.baseUrl.PAGODA_PIC_DOMAIN,
    canApply: false, // 是否可提交
    orderDetail: {}, //订单详情
    complaintType: '1', // 投诉类型
    goodsOrderID: '', // 商品订单ID
    goodsOrderNum: '', // 商品订单编号
    refundAmount: 0, // 申请退款金额
    reasonServiceDissatisfy: [], // 服务不满意原因id列表
    otherReasonServiceDissatisfy: '', // 服务不满意的其它原因
    goodsList: [], // 显示商品
    refundGoodsObj: {}, // 退款商品
    isBack: false,
    isLoading: false,
    isRefundAmount: 0,
    isShowRefundWarning: false, // 是否显示退款预警弹窗
    isShowedWarningTips: false, // 是否弹过退款预警弹窗
    refundWarning:null, // 退款提醒
    isVegetablesPage:true, // 页面来源
    showActivityAmount:false, //是否显示一元购活动金额
    // 订阅消息相关
    subscribe: {
      // 是否展示订阅
      show: false,
      // 模板列表
      tmplIds: refundSubscript
    },
    /**当前订单提交的退款路径 */
    refundPath: '',
    /**@type { Array<{ refundPath: string; refundPathNo: string; }> } 该订单可退的退款路径 */
    refundPaths: []
  },
  _data: {
    submitParams: {},
    // 门店号码
    contact: ''
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let isIphoneX = sysInfo.model.indexOf("iPhone X") > -1 ? true : false
    let {
      goodsOrderID,
      goodsOrderNum,
      complaintType,
      otherReasonServiceDissatisfy = '',
      contact
    } = options
    let reasonServiceDissatisfy = JSON.parse(options.reasonServiceDissatisfy || '[]')
    let detailObj = options.detailObj?JSON.parse(decodeURIComponent(options.detailObj)):null
    if (!!detailObj && detailObj.refundAmount === "0") detailObj.refundAmount = ""
    this._data.contact = contact
    this.setData({
      isIphoneX,
      goodsOrderID,
      goodsOrderNum,
      complaintType,
      reasonServiceDissatisfy,
      otherReasonServiceDissatisfy,
      detailObj: detailObj ? detailObj : null
    })
    if (this.data.detailObj) {  //如果带参数 说明是周期购退款
      let goodsList=[];
      const { goodsOrderID, goodsOrderNum, detailObj } = this.data
      let orderDetail = {
        ...this.data.detailObj,
        totalBargainGoodsCoupon,
        goodsOrderID,
        goodsOrderNum,
        goodsTotalAmount: detailObj.price * detailObj.count
      }
      goodsList.push(this.data.detailObj)
      this.setData({
        totalBargainGoodsCoupon,
        goodsList,
        orderDetail,
        isRefundAmount: orderDetail.doubleRefundAmount && orderDetail.doubleRefundAmount / 2
      })
    } else {
      this.getOrderDetail()
    }
    wx.removeStorageSync('refundGoodsList')
  },
  /**
   * 生命周期函数--监听页面显示
   * 读取商品不满意原因页填写内容
   */
  onShow: function () {
    // 如果是从提交成功页返回来，则返回到订单列表页或者订单详情页.商品和服务都不满意需要返回三个页面
    if (this.data.isBack) {
      // let detal = this.data.complaintType == '1' ? 2 : 3
      wx.navigateBack()
    }
    let goodsList = this.data.goodsList
    let refundGoodsObj = this.data.refundGoodsObj // 商品不满意原因页设置该值
    let refundAmount = 0 // 退款总结（各商品退款价之和）
    goodsList.forEach((goods, index) => {
      //  同一商品
      const isSame = goods.goodsID === refundGoodsObj.goodsID;
      //  同一下标
      const isSameIndex = refundGoodsObj.goodsIndex === index
      if (isSame && isSameIndex) {
        goods = Object.assign(goods, refundGoodsObj)
      }
      if (goods.refundAmount) {
        refundAmount += Number(goods.refundAmount)
      }
    })
    let canApply = false
    if (refundAmount !== 0) {
      canApply = true
    }
    this.setData({
      canApply,
      goodsList,
      refundAmount
    })
    this.getOrderRefundPath()
  },
  /**
   * 获取订单退款路径
   */
  async getOrderRefundPath() {
    const {
      goodsOrderNum
    } = this.data

    try {
      const { data } = await app.api.getOrderRefundPath({
        orderNo: goodsOrderNum
      })

      this.setData({
        refundPaths: data
      })
    } catch (error) {
      console.info('getOrderRefundPath error', error)
    }
  },
  // 去商品不满意原因填写页
  toGoodsDissatisfy(e) {
    let full = e.currentTarget.dataset.fullrefund
    const clickGoods = e.currentTarget.dataset.goods

    //  增加index属性用于退款页面返回做标识
    clickGoods.goodsIndex = e.currentTarget.dataset.index
    let refundGoods = JSON.stringify(clickGoods)

    if (full !== 'Y') {
      wx.navigateTo({
        url: `/bgxxUser/pages/complaints/goodsDissatisfy/index?complaintType=3&refundGoods=${encodeURIComponent(refundGoods)}`
      })
    }
  },
  /**
   * 只复制target中source属性不为空的值
   *
   */
  copyObjValue(source, target) {
    for (let key in source) {
      source[key] = target[key]
    }
    return Object.assign({}, source)
  },
  // 提交售后
  async submitApply() {
    let {canApply,isShowedWarningTips} = this.data
    if (!canApply) {
      wx.showToast({
        title: '请先申请需退款商品',
        icon: 'none',
        duration: 1000
      })
      return
    }

    const _firstRefundCheck = await this.firstRefundCheck()
    //  触发新客首单退弹窗逻辑，跳转审核详情或者实名页
    if (!_firstRefundCheck) {
      return
    }

    const res = await this.checkVerify()
    // 如果校验不通过 则不跳转
    if (!res) {
      return
    }
    // 未弹过退款预警弹窗
    if ( !isShowedWarningTips ) {
      return this.showRefundWarningPopup()
    }
    this.refundPathConfirm()
  },
  /**
   * 提交三无退前检查是否需要实名认证
   */
  async checkVerify() {
    const params = this.getSearchParams()
    const { data } = await app.api.threeRefundRatioCheck(params)
    // true表示需要实名认证
    if (!data) {
      return true
    }
    // 查询当前用户是否已经实名认证了
    const { data: { certified, reviewStatus } } = await app.api.getVerifyStatus({
      customerID: app.globalData.customerID || -1
    })
    // 实名认证状态 0:未实名 1:已实名 2:审核中
    if (certified === 1) {
      return true
    }
    // 如果审核被拒绝，则需要跳转到审核结果页
    if (reviewStatus === 'R') {
      // 先去结果页 通过结果页来决定是否应该换绑
      const params = JSON.stringify({
        pageType: 'binding',
        storePhone: this._data.contact || '',
        isDirectBack: true,
        reviewStatus
      })
      wx.navigateTo({
        url: '/userB/pages/certification/applyResult/index?params=' + params
      })
      return
    }
    // 如果是未实名 则跳转到实名认证页
    if (certified === 0) {
      wx.navigateTo({
        url: '/userB/pages/certification/applyCertification/index?storePhone=' + this._data.contact || ''
      })
      return false
    }
    // 审核中 则跳转到审核中页面
    if (certified === 2) {
      // 先去结果页 通过结果页来决定是否应该换绑
      const params = JSON.stringify({
        pageType: 'binding',
        storePhone: this._data.contact || '',
        reviewStatus
      })
      wx.navigateTo({
        url: '/userB/pages/certification/applyResult/index?params=' + params
      })
      return false
    }
    return true
  },

  /**
   * 是否同意退到钱包——弹窗确认
   * @returns
   */
  async refundWalletConfirm() {
    let _resolve
    const refundPathResolve = new Promise(resolve => {
      _resolve = resolve
    })
    this.refundPathResolve = _resolve
    this.setData({
      showRefundPathConfirm: true,
    })
    const result = await refundPathResolve

    return result
  },

  /**
   * 退款路径确认
   * @param {*} event 
   */
  refundPathChoose(event) {
    const type = event.detail
    if (this.refundPathResolve) {
      this.refundPathResolve(type)
      this.setData({
        showRefundPathConfirm: false,
      })
      delete this.refundPathResolve
    }
  },

  /**
   * 发起退款路径确认流程
   */
  async refundPathConfirm() {
    const refundPaths = Array.isArray(this.data.refundPaths) ? this.data.refundPaths : []

    //  不存在退款路径的情况，接口可能报错了。做个兜底处理，给他退到钱包
    if (!refundPaths.length) {
      this.requestSubmitApply(refundPathEnum.M)
      return
    }

    //  退款流程仅有一个，直接进行三无退。否则给他选择退款路径
    if (this.data.refundPaths.length === 1) {
      this.requestSubmitApply(this.data.refundPaths[0].refundPathNo)
      return
    }

    /**是否同意钱包支付 */
    const refundType = await this.refundWalletConfirm()

    //  退款到钱包
    if (refundType === '0') {
      this.requestSubmitApply(refundPathEnum.M)
    }
    //  退款到原路
    else if (refundType === '1') {
      this.requestSubmitApply(refundPathEnum.O)
    }
  },

  /**
   * 提交申请
   * @param { typeof refundPathEnum } refundChannelType 退款路径
   * @returns
   */
  requestSubmitApply(refundPath) {
    // 触发订阅弹窗
    this.setData({
      refundPath,
      'subscribe.show': true
    })
  },
  /**
   * 订阅消息关闭事件
   */
  onSubscribeClose({ detail }) {
    // 关闭订阅弹窗
    this.setData({ 'subscribe.show': false })
    const {
      complaintType,
      reasonServiceDissatisfy,
      otherReasonServiceDissatisfy,
      orderDetail,
      refundAmount,
      goodsList
    } = this.data
    const user = wx.getStorageSync('user')
    const goodsObj = {
      goodsID: '', // 商品ID
      count: '', // 商品购买数量
      goodsPic: [], // 证据图
      goodsTotalPrice: '', // 退款商品总金额（单价*数量）
      selStrPrimaryReason: '', // 不满意原因大类
      reason: '', // 商品不满意：不满意商品的原因小类/用户主动输入的原因
      goodsName: '', // 商品名称
      refundRatio: '', // 退款比率
      refundAmount: '', // 退款金额
      maxRefundAmount: '', // 商品最高可退金额
      goodsType: 1,  // 商品类型 1 普通商品 2 换购商品
      goodsNumber: '', //商品编号
    }
    const refundGoodsList = []
    goodsList.forEach(goods => {
      if (goods.refundAmount) {
        goods.goodsTotalPrice = String(goods.goodsTotalAmount || goods.price) // 商品价格
        const postData = this.copyObjValue(goodsObj, goods)
        // 动态设置大类和小类中文
        // 如果用户没有选择大类 则默认给“其他” 虽然这个做了判断 这里为了严谨
        postData.primaryReason = goods.selStrPrimaryReason
        // 如果选择的是“其他”选项 则优先取用户输入的值，否则就取用户选择小类
        postData.reason = goods.selStrPrimaryReason === '其他' ? goods.otherReason : goods.selArrReasonText.join('$')
        postData.goodsTotalPrice = String(postData.maxRefundAmount || postData.price) // 商品价格
        postData.goodsType = goods.isTradeInGoods ? 2 : 1
        refundGoodsList.push(postData)
      }
    })
    const data = {
      customerID: user.userID,
      phoneNumber: user.phoneNumber,
      goodsOrderID: orderDetail.goodsOrderID,
      goodsOrderNo: orderDetail.goodsOrderNum,
      complaintType,
      orderMaxRefundAmount: orderDetail.maxRefundAmount, // 商品订单最高可退金额
      refundAmount: String(refundAmount), // 申请退款金额
      goodsList: refundGoodsList,
      refundPath: this.data.refundPath,
      // 三无退版本
      v: 2
    }
    // 以下参数为空的时候不传
    if (reasonServiceDissatisfy.length) { // 服务不满意原因id列表,
      data.reasonServiceDissatisfy = reasonServiceDissatisfy
    }
    if (otherReasonServiceDissatisfy) { // 服务不满意的其它原因
      data.otherReasonServiceDissatisfy = otherReasonServiceDissatisfy
    }
    let goodsName = refundGoodsList.map(item => item.goodsName).join(',')
    let goodsId = refundGoodsList.map(item => item.goodsID).join(',')
    let extraParam = {
      refundTime: Date.now() + '',
      refundAmount: refundAmount,
      goodsOrderId: orderDetail.goodsOrderID, //退款订单ID
      goodsName,
      goodsId,
      isStoreOrder: 'N'
    }
    const req = app.api.submitBgxxApply(data.customerID, data.goodsOrderID, data)
    util.countDownLoading({
      loadingText: '审核中',
      promiseInstance: req
    })

    req.then(res => {
      if (!!res.data) {
        const { applyInfo = '', complaintCompletedTip = '' } = res.data
        wx.redirectTo({
          url: `/bgxxUser/pages/complaints/submitSuccess/index?applyInfo=${applyInfo}&complaintCompletedTip=${complaintCompletedTip}&goodsOrderID=${orderDetail.goodsOrderID}&complaintType=${complaintType}`,
          complete:function(){
            wx.hideLoading()
          }
        })
      }else{
        common.showModal('提示', res.description, false)
      }
    }, error => {
      wx.hideLoading()
      //  用户异常单独提示文案处理
      if(error.errorCode === 550001) {
        common.showModal('提示', '抱歉，当前账户异常，请重新登录', true, '我知道了', '', function (res) {
          if (res.confirm) {
            app.signOut({
              logoutCause: outLoginType.accountErrorLogout
            })
            app.signIn()
          } else if (res.cancel) {
            app.signOut({
              logoutCause: outLoginType.accountErrorLogout
            })
            wx.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
      }
      //  以下几类取后端错误信息提示
      else if([550002, 550003, 550004].includes(error.errorCode)) {
        wx.showModal({
          content: error.description,
          showCancel: false
        })
      }
      //  如果三无退过了 则提示
      else if([101].includes(error.errorCode)) {
        wx.showModal({
          content: '该商品已经申请过三无退了，请勿重复申请',
          showCancel: false
        })
      }
      //  如果门店换货过了 则提示
      else if([430041].includes(error.errorCode)) {
        wx.showModal({
          content: '商品已完成换货，无需再提交三无退款',
          showCancel: false
        })
      }
      //  如果门店换货过了 则提示
      else if([550005].includes(error.errorCode)) {
        wx.showModal({
          content: '退款校验数据过时,请求重新校验',
          showCancel: false
        })
      }
      // 触发短信上行验证
      else if ([550006].includes(error.errorCode)) {
        this._data.submitParams = detail
        wx.navigateTo({
          url: `/userB/pages/selfSupportComplaints/smsVerification/index`,
          events: {
            reSubmit: () => {
              this.onSubscribeClose({ detail: this._data.submitParams })
            }
          }
        })
      }
      // 短信上行验证超时
      else if ([550007].includes(error.errorCode)) {
        wx.showModal({
          content: '短信上行验证已超时，请稍后重试',
          showCancel: false
        })
      }
      //  其他情况保持原样
      else {
        common.showModal('提示', error.description, false)
      }
    }).then(()=>{
      sensors.safeGuardSensor('part', extraParam)
    })
    // 上报到触达域
    detail && !Object.keys(this._data.submitParams).length && reportTmplIds(detail.resultStatus)
  },
  // 获取订单详情
  getOrderDetail() {
    let customerID = app.globalData.customerID || -1
    let goodsOrderID = this.data.goodsOrderID
    app.api.bgxxGetOrderDetailInfo(true, customerID, goodsOrderID).then(res => {
      if (!!res.data) {
        let orderDetail = res.data
        let goodsList = res.data.goodsList
        if (!!goodsList && goodsList.length > 0) {
          // 赠品不能退款
          goodsList = goodsList.filter(item => item.isGift !== 'Y' && Number(item.price) !== 0)
        }
        this.setData({
          goodsList,
          orderDetail,
          isRefundAmount: orderDetail.doubleRefundAmount && orderDetail.doubleRefundAmount / 2
        })
        this.showActivityAmount(this.data.goodsList)
      }
    }, error => {
      common.showModal('提示', error.description, false)
    })
  },
  /**
   * 验证是否展示一元购活动金额
   * @param { goodsList } Object 商品列表
   */
  showActivityAmount(data){
    //有一元购活动信息&&商品享受一元购活动数量!==0 && 商品购买数量>商品享受一元购活动数量
    const showActivityAmount = data.filter(item=>item.activityInfo&&item.activityInfo.enjoyOneBuyNum!==0).some(res => Number(res.count)>Number(res.activityInfo.enjoyOneBuyNum))
    this.setData({showActivityAmount})
  },
  /**
   * 电话联系
  */
  handleContact (e) {
    var { phone } = e.currentTarget.dataset
    if (phone)
      wx.makePhoneCall({
        phoneNumber: phone
    })
    this.setData({
      isShowRefundWarning: false
    })
  },
/**
 * 确认弹窗
 */
  handleConfirm () {
    this.setData({
      isShowRefundWarning: false
    })
    this.refundPathConfirm()
  },
  /**
   * 获取预警弹窗参数/当前订单是否达到三无退管理台百分比参数
   * @returns 参数对象
   */
  getSearchParams () {
    const {
      refundAmount,
      goodsOrderNum,
      goodsList
    } = this.data
    // const params = {
    //   customerID:app.globalData.customerID, // 用户id
    //   // orderTicket:goodsOrderID, // 小票号
    //   goodsOrderNo:goodsOrderNum,//商品订单编号
    //   refundAmount // 退款金额
    // }
    // wx.showLoading()
    // app.api.getRefundWarning(params).then(res => {
    //   let data = res.data
    //   wx.hideLoading()
    //   if (data.isNeedWindow === 'N') {
    //     return this.requestSubmitApply()
    //   }
    //   this.setData({
    //     refundWarning: Object.assign({},data,{isVegetablesPage:this.data.isVegetablesPage}),
    //     isShowRefundWarning: true,
    //     isShowedWarningTips: true
    //   })
    // })
    // 获取spuList 过滤组合品 次日达无耗材 暂无须过滤
    const spuList = Array.from(new Set(goodsList.filter(item => item.refundAmount && item.isCombined !== 'Y').map(item => item?.spuGoods?.spuNumber || '')))
    let refundGoodsList = wx.getStorageSync('refundGoodsList')
    let goodsCode = []
    if(refundGoodsList && refundGoodsList.length){
      goodsCode = refundGoodsList.map(item=>{
        return item.goodsID
      })
    }

    //  微信unionId
    const unionId = wx.getStorageSync('wxSnsInfo').unionid;
    //  获取用户坐标
    const { longitude, latitude } = util.getUserLocation()

    const params = {
      "refundChannelNo":10001, // 渠道编码
      "refundPath":"O", // 退款路径
      "refundAmount":refundAmount, // 退款金额
      "channelOrderNumber":goodsOrderNum , // 原交易渠道商品订单号
      "originalOrderChannelNo":10001, // 渠道编码
      spuList, // 商品对应的spu列表
      goodsCode, // 商品编码
      refundApplyTime:new Date().getTime()+'',
      customerID:app.globalData.customerID, // 会员id
      //  终端信息
      terminalInfo: {
        unionId,
        longitude,
        latitude
      },
      // 次日达订单传50
      orderType: 50
    }
    return params
  },
  /**
   * 退款预警弹窗
   */
  async showRefundWarningPopup () {
      const {store} = this.data.orderDetail
      const params = this.getSearchParams()
      const req = app.api.selfOperatedRefundWarning(params)

      util.countDownLoading({
        promiseInstance: req
      })

      req.then(res => {
        wx.hideLoading()
        let {data,errorCode,description} = res
        if (errorCode === 0) {
          let { pass, topThree } = data
          if (pass) {
            return this.refundPathConfirm()
          } else {
            this.setData({
              refundWarning:Object.assign({},data,{showtype:130,topThree,phoneNumber: store.storePhone || '4001811212',serverPhone:'4001811212'}),
              isShowRefundWarning: true,
              isShowedWarningTips: true
            })

          }
        }else{
          wx.showToast({
            title: description,
          })
        }
      }).catch(res=>{
        wx.hideLoading()
      })
    },
    /**
     * 关闭弹窗
     */
    closeRefundWarning(){
      this.setData({
        isShowRefundWarning:false,
        isShowedWarningTips:false
      })
    },
    onUnload(){
      wx.removeStorageSync('refundGoodsList')
    }
})
