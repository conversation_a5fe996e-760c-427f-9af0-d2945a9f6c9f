const locateService = require('../utils/services/locate')
const { LOCATE_STORAGE_ENUM } = locateService
import locateStore from '../stores/module/locate'
import { getUserIsVisitor, clientLoginParallel } from '../service/userService'
import { actionType, appLaunchCacheProtocolParallel, checkIsShowProtocolParallel, checkPrivacyPopupParallel, pagodaProtocolStore } from '../components/base/userProtocol/protocol'
const SHOW_USER_PROTOCOL = 'showUserProtocol'
const SKELETON_SCREEN = 'skeletonScreen'
const CONTENT_SCREEN = 'content'
const app = getApp()

function isFreshPage (ctx) {
  const {currentRoute, mixFruitLocate} = ctx
  if (!mixFruitLocate) return true
  return [
    'bgxxShop/pages/xxshop/index/index',
    'bgxxShop/pages/goodDetail/index'
  ].includes(currentRoute)
}

function getCurPages (ctx){
  const pages = getCurrentPages()
  const {route} = pages[pages.length - 1]
  ctx.currentRoute = route
}

function checkIsCacheStore(currCityName, currCityID, cityID, isFresh) {
  return currCityName && String(currCityID) !== String(cityID) && isFresh
}
module.exports = {
  data: {
    currentView: SKELETON_SCREEN,
    freshMainViews: { // 页面缺省页可能展示不同，可自由配置，要区分locateMixin的mainViews字段
      noLocationAuth: 'noLocationAuth',
      noCity: 'noCity',
      noStore: 'content',
      content: 'content'
    },
    freshProtocolActionFn: {
      [actionType.default]: function () {
        console.log('freshProtocolActionFn default');
        this.getDefaultCity(false)
      },
      [actionType.checkAuth]: function () {
        console.log('freshProtocolActionFn checkAuth');
        // 减少开发量，直接沿用之前的方法
        this.freshCloseUserProtocol({ agree: true})
      },
      [actionType.checkPagodaProtocol]: function () {
        console.log('freshProtocolActionFn checkPagodaProtocol');
        this.freshCheckPagodaProtocol()
      },
      [actionType.noAction]: function () {
        console.log('freshProtocolActionFn noAction');
        // 此时已经定位过，直接取缓存
        this.noShowProtocolHandle()
      }
    },
  },
  mixVegLocate: true, // 标记次日达定位混入
  onLoad() {
    this._data || (this._data = {
      skipLocateDialog: false // 是否不显示定位错误对话框(用在个人中心页只需要显示加购数的逻辑)
    })
  },
  // 次日达还是保留先执行默认城市再取实际定位
  async onShow () {
    getCurPages(this)
    await appLaunchCacheProtocolParallel()
    const type = await checkPrivacyPopupParallel()
    console.log('vege checkPrivacyPopup type', type);
    this.data.freshProtocolActionFn[type].call(this)
  },
  async getDefaultCity(onlyShowData = true) {
    const params = {
      locateScene: 'vegetables',
      requestAuth: false
    }
    await locateService.getLocationByLocate(params)
    // 取默认城市展示数据
    this.currLocateCityHandle({ onlyShowData })
    app.globalData.isGetBgxxCacheLocation = false // 弹授权弹窗后不取缓存地址
    app.globalData.isHandlebgxxCacheStore = false
  },
  async freshCheckPagodaProtocol() {
    // 展示隐私协议弹窗
    if ((this._data || {}).pageProtocol) {
      const { show, protocol } = await checkIsShowProtocolParallel()
      // console.log('freshCheckPagodaProtocol show', show);
      if (show) {
        isFreshPage(this) && pagodaProtocolStore.toggleShow(true)
        protocol.then((res) => this.freshCloseUserProtocol(res))
        return
      }
      this.noShowProtocolHandle()
    }
  },
  noShowProtocolHandle (){
    // 不展示协议弹窗的情况：
    // 1. 已经展示过，【获取位置信息提示】弹窗未展示(此时一定是同意了协议弹窗)
    // 2. 访客模式
    // 情况1需要再次展示【获取位置信息提示】
    const isVisitorMode = getUserIsVisitor()
    console.log('noShowProtocolHandle', isVisitorMode);
    if (!isVisitorMode) {
      this.freshCloseUserProtocol({ agree: true})
    } else {
      // 访客模式
      // 1. 用户手动选择了A地址，下次重新进入小程序时，需要取A地址
      // 2. 否则使用默认地址
      const bgxxSelectLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo')
      if (bgxxSelectLocateInfo) {
        app.globalData.isGetBgxxCacheLocation = true
        this.onLocate(false)
      } else {
        // 第一次进入
        this.getDefaultCity(false)
      }
    }
  },
  /**
   * @desc 关闭隐私弹窗，同意不同意都会走到这个逻辑
   */
  async freshCloseUserProtocol (e) {
    if (this.data.currentView !== CONTENT_SCREEN) {
      isFreshPage(this) &&this.setData({
        currentView: SKELETON_SCREEN
      })
    }
    // 不同意百果园隐私弹窗
    if (!e.agree) {
      app.signOut()
    }
    // 不同意隐私弹窗的情况，直接使用默认地址定位
    //  双倍退协议同意时，不走后续位置授权逻辑
		// if (!e.agree && e.protocolType === 'firstProtocol') {
    //   this.getDefaultCity(false)
		// 	return
    // }
    // if (e.agree && e.protocolType === 'firstProtocol') {
    //   await clientLoginParallel()
    // }
    // 同意隐私弹窗的情况
    this.beforeLocate()
  },
  async beforeLocate() {
    // 未弹过授权弹窗取默认深圳市 本次取默认城市仅作为数据展示使用
    let isGetDefaultCity = true
    if (!wx.getStorageSync(LOCATE_STORAGE_ENUM.HAS_SHOW_LOCATE_AUTH_MODAL)) {
      const bgxxSelectLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo')
      // 第二次进来判断有无缓存定位信息，有的话，不再走获取默认城市逻辑
      // 因为未授权的情况下，都是取的缓存定位信息
      if (!bgxxSelectLocateInfo) await this.getDefaultCity()
      else {
        // 有缓存的情况下，若未同意微信隐私协议，取缓存即可
        app.globalData.isGetBgxxCacheLocation = true
      }
      isGetDefaultCity = false
    } else {
      // 已经点击过【授权弹窗】
      // 拒绝：展示默认城市；如果手动选择了地址，下次冷启动进入小程序，展示选择地址

      // 接受：走实际定位
      // 接受后，如果冷启动进入，需要重新走实际定位逻辑，为了判断当前定位城市是否与缓存定位城市一致。展示过一次之后，后面都取缓存定位
      if (locateStore.hasLocationAuth === true) {
        // isGetBgxxCacheLocation => true: 走缓存 false: 不走缓存
        app.globalData.isGetBgxxCacheLocation = app.globalData.hasShowChangeBgxxCityModal
        app.globalData.hasShowChangeBgxxCityModal = true
        isGetDefaultCity = false
      } else {
        isGetDefaultCity = false
      }
    }
    // 之前未开启位置权限，下次进入判断是否有开启, 如果开启了则不取缓存重新定位
    console.log('app.globalData.lastLocateAuth', app.globalData.lastLocateAuth);
    if (!app.globalData.lastLocateAuth) {
      const curLocateAuth = await locateStore.checkHasAuth()
      console.log('本次进入是否开启权限',curLocateAuth)
      console.log('locateStore.hasLocationAuth', locateStore.hasLocationAuth)
      console.log('locateStore.useFreshDefaultLocation', locateStore.useFreshDefaultLocation)
      if (curLocateAuth === true) {
        locateStore.changeAuth({ auth: true })
        if( locateStore.useFreshDefaultLocation) {
          console.log('权限开启啦')
          app.globalData.isGetBgxxCacheLocation = false
        }
      }
    }
    this.onLocate(isGetDefaultCity)
  },
  async onLocate(isGetDefaultCity) {
    try {
      console.log('isGetDefaultCity', isGetDefaultCity)
      // requestAuth
      // -false 走默认
      // -true 实际定位

      await locateService.requestLocation({locateScene: 'vegetables', requestAuth: isGetDefaultCity ? false : true })
    } catch (e) {
      console.log('onLocate', e)

      if (e.err && e.err.code === 1) {
        // 没授权就按之前取默认城市
        // isGetDefaultCity
        console.log('locateStore.hasLocationAuth', locateStore.hasLocationAuth);
        if (locateStore.hasLocationAuth !== true) {
          // 拒绝授权且没有缓存城市时，需要取默认城市
          const bgxxSelectLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo')
          if (!bgxxSelectLocateInfo) await this.getDefaultCity()
          this.freshLocateFinish()
          !app.globalData.isGetBgxxCacheLocation && (app.globalData.isGetBgxxCacheLocation = true)
          return
        }
        // 微信或者系统位置权限没开 并且没有地址则去默认城市
        const bgxxSelectLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo')
        if (e.err.errMsg && e.err.errMsg.indexOf("system permission denied") && !bgxxSelectLocateInfo) {
          this.getDefaultCity()
          return
        }
      }
    }
    // 是否需要判断缓存门店城市逻辑，只有小程序冷启动才需要
    console.log('app.globalData.isHandlebgxxCacheStore', app.globalData.isHandlebgxxCacheStore)
    if (app.globalData.isHandlebgxxCacheStore) {
      app.globalData.isHandlebgxxCacheStore = false
      const cacheLocateInfo = wx.getStorageSync('bgxxSelectLocateInfo')
      // console.log('cacheLocateInfo', cacheLocateInfo)
      if (!!cacheLocateInfo) {
        this.cacheCityHandle(cacheLocateInfo)
        return
      }
    }
    // 取默认城市后的第二次定位需要强制更新地址信息
    this.currLocateCityHandle( { isUpdateLocate: app.globalData.isGetBgxxCacheLocation ? false: true })
    !app.globalData.isGetBgxxCacheLocation && (app.globalData.isGetBgxxCacheLocation = true)
  },
  /**
   * 缓存城市门店逻辑处理
   * 1.缓存城市是否开通业务
   * 2.判断缓存门店是否营业
   *   营业：缓存城市与当前城市是否一致
   *      一致：直接使用缓存门店
   *      不一致：提示是否更换城市为当前定位城市
   *            是：当前定位城市是否开通业务
   *                是：查找城市附近3公里门店
   *                否：展示缺省页
   *            否：使用缓存门店
   *    不营业：
   *        是：查找城市附近3公里门店
   *        否：展示缺省页
   */
  async cacheCityHandle(cacheLocateInfo) {
    const { selectAddressInfo = {},  selectStoreInfo = {} } = cacheLocateInfo
    const { storeCode = '' } = selectStoreInfo || {}
    const { cityID = '', lat = -1, lon = -1, cityName = '', cityCode } = selectAddressInfo || {}
    if (!(storeCode && cityCode)) {
      this.currLocateCityHandle()
      return
    }
    const isFresh = isFreshPage(this)
    const bgxxCurrLocateInfo = wx.getStorageSync("bgxxCurrLocateInfo") || {}
    const {
      selectAddressInfo: { cityID: currCityID = '', supportSuperVipShop: currLocateSupportVipShop, cityName: currCityName  = '', deliveryCentercode } = {},
      // selectStoreInfo: bgxxCurSelectStoreInfo
    } = bgxxCurrLocateInfo

    const supportSuperVipShop = String(cityID) === String(currCityID) ? currLocateSupportVipShop : await this.checkCitySupportVipShop({ cityName, lat, lon})
    if (supportSuperVipShop !== 'Y') {
      // v3.5.1无城市服务更新缓存
      app.changeBgxxCityInfo({
        selectAddressInfo: bgxxCurrLocateInfo.selectAddressInfo,
        selectStoreInfo: {}
      })
      if (!isFresh) return
      this.setData({
        currentView: 'noCity'
      })
      this.freshLocateFinish()
      return
    }
    const { status = '', openingTime = '', address, isSupportVip, isSupportVipDelivery, lat: storeLat, lon: storeLon, shortName, name, openTimeOnDay: startTime = '', closeTimeOnDay: endTime = '', storeCode: newStoreCode } = await this.getStoreInfoById({ storeCode: storeCode })

    // 缓存门店不可用情况： 不接单/不营业/既不支持配送/也不支持自提
    if (status !== 'A' || ![isSupportVip, isSupportVipDelivery].includes('Y')) {
      // 城市可用，门店不可用
      app.changeBgxxCityInfo({
        selectAddressInfo,
        selectStoreInfo: {}
      })
      app.globalData.vegetablesNoStoreTip = true
      if (!isFresh) return
      const res = !(this._data.skipLocateDialog || !await app.showModalPromise({
        content: '您所选的门店暂时关闭线上服务,去其他门店看看吧',
        confirmText: '我知道啦',
      }))
      res && wx.navigateTo({ url: '/bgxxShop/pages/chooseStore/index' });
      return
    }

    // 缓存门店营业
    if (checkIsCacheStore(currCityName, currCityID, cityID, isFresh)) { // 缓存城市和当前城市不一致
      const res = !(this._data.skipLocateDialog || !await app.showModalPromise({
        content: `当前您在${currCityName}是否切换到该城市`,
        showCancel: true,
        confirmText: '切换',
        cancelText: '暂不切换',
      }))
      if (res) { // 切换
        app.changeBgxxCityInfo(bgxxCurrLocateInfo)
        this.currLocateCityHandle()
        return
      }
    }
    // 更新缓存门店详情信息
    Object.assign(cacheLocateInfo.selectStoreInfo, {
      openingTime,
      address,
      isSupportVip,
      isSupportVipDelivery,
      lat: storeLat,
      lon: storeLon,
      storeName: shortName || name,
      startTime,
      endTime,
      storeCode: newStoreCode
    })
    // 3.5.1 新增 更新缓存城市是否支持心享服务
    Object.assign(cacheLocateInfo.selectAddressInfo, {
      supportSuperVipShop,
      // 更新配送中心编码
      deliveryCentercode: deliveryCentercode
    })
    app.changeBgxxCityInfo(cacheLocateInfo)
    wx.removeStorageSync('bgxxCurrLocateInfo')
    isFresh && this.setData({
      currentView: 'content'
    })
    this.freshLocateFinish()
  },
  /**
   * 检查城市是否支持心享商城服务
   */
  async checkCitySupportVipShop(params) {
    let supportSuperVipShop = 'N'
    try {
      const res = await app.api.checkBgxxIsSupportVip(params)
      if (res.data) {
        supportSuperVipShop = res.data.supportSuperVipShop || 'N'
      }
    } catch (error) {}
    return supportSuperVipShop
  },
  /**
   * 当前定位城市门店逻辑处理
   * 当前城市是否开通业务
   *    是：查找城市附近3公里门店
   *    否：展示缺省页
   * @params onlyShowData 仅展示数据，传到onLocateReady中去
   * @params isUpdateLocate 是否强制更新数据
   */
  async currLocateCityHandle({ onlyShowData = false, isUpdateLocate = false } = {}) {
    console.log('currLocateCityHandle', isUpdateLocate)
    const isFresh = isFreshPage(this)
    const { viewKey } = locateService.getBgxxViewType(isUpdateLocate)
    if (viewKey === 'noStore' && !app.globalData.vegetablesNoStoreTip && isFresh) {
      app.globalData.vegetablesNoStoreTip = true
      const res = !(this._data.skipLocateDialog || !await app.showModalPromise({
        content: '啊哦，当前定位地址附近3公里内没有门店，换个地址看看吧～',
        confirmText: '我知道啦',
      }))
      res && wx.navigateTo({ url: '/bgxxShop/pages/chooseStore/index' });
      return
    }
    if (!isFresh) return
    this.setData({
      currentView: this.data.freshMainViews[viewKey]
    })
    this.freshLocateFinish(onlyShowData)
  },
  /**
   * @description 城市门店逻辑处理完回调
   * @params onlyShowData true 仅仅展示数据，不做跳转等逻辑操作
   */
  freshLocateFinish(onlyShowData = false) {
    console.log('freshLocateFinish', onlyShowData);
    if ("function" === typeof this.onLocateReady && isFreshPage(this)) this.onLocateReady({ onlyShowData })
  },
  /**
   * 门店id获取门店详情
   * @param {object} params
   */
  async getStoreInfoById(params) {
    try {
      const res = await app.api.getStoreDetail(params)
      return res.data || {}
    } catch (error) {}
    return {}
  }
}
