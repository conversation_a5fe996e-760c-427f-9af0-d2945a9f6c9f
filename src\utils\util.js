const sensors = require('../source/js/sensorsdata.min.js');

const commonObj = require('../source/js/common').commonObj
import sensorsPageConfigMap from './report/utils/sensorsPageConfigMap'

/**
 * 将日期对象格式化为指定字符串格式
 * @param {String} fmt - 格式化字符串，支持 'M+' 月份 'd+' 日 'h+' 小时(24小时制) 'H+' 小时(12小时制) 'm+' 分 's+' 秒 'q+' 季度 'S' 毫秒
 * 例如：'yyyy-MM-dd hh:mm:ss.S' 输出：'2016-01-02 03:04:05.007'
 * @return {String} 格式化后的字符串
 */
Date.prototype.Format = function (fmt) {
  var o = {
    "M+": this.getMonth() + 1, // 月份
    "d+": this.getDate(), // 日
    "h+": this.getHours(), // 小时
    "H+": this.getHours(), // 小时
    "m+": this.getMinutes(), // 分
    "s+": this.getSeconds(), // 秒
    "q+": Math.floor((this.getMonth() + 3) / 3), // 季度
    "S": this.getMilliseconds() // 毫秒
  };
  // 判断 fmt 是否匹配 'y+'，如果匹配，将 this.getFullYear() 截取后面的字符串
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  // 遍历 o 对象，判断 fmt 是否匹配对象的键，如果匹配，将对象的值替换 fmt 中的匹配值
  for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
  return fmt;
}

function isUndefined(o) {
  return Object.prototype.toString.call(o) === '[object Undefined]'
}

function isString(o) {
  return Object.prototype.toString.call(o) === '[object String]'
}

function isArray(o) {
  return Object.prototype.toString.call(o) === '[object Array]'
}

function isObject(o) {
  return Object.prototype.toString.call(o) === '[object Object]'
}

function isNumber(o) {
  return Object.prototype.toString.call(o) === '[object Number]'
}

function isEmptyObject(o) {
  for (var k in o) return false
  return true
}

function isEmpty(o) {
  if (isUndefined(o)) {
    return true
  } else if (isString(o)) {
    return o.trim().length === 0
  } else if (isObject(o)) {
    return Object.keys(o).length === 0
  } else if (isArray(o)) {
    return o.length === 0
  }
  return false
}
/**
 *
 * @param {*fn} fn 要执行的函数
 * @param {*gapTime} gapTime 间隔时间
 * @description 节流函数，防重复点击可用
 */
function throttle(fn, gapTime) {
  if (gapTime === null || gapTime === void 0) {
    gapTime = 1000
  }
  let lastTime = 0
  // 返回新的函数
  return function () {
    let nowTime = new Date().getTime()
    if (nowTime - lastTime > gapTime) {
      fn.apply(this, arguments)
      lastTime = nowTime
    }
  }
}
/**
 *
 * @param {*fn} fn 要执行的回调函数
 * @param {*wait} wait 间隔时间
 * @param {*immediate} immediate 表示第一次是否立即执行
 */
function debounce(fn, wait = 50, immediate) {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    let later = () => {
      timer = null
      if (!immediate) {
        fn.apply(this, args)
      }
    }
    let immediateCall = immediate && !timer
    timer = setTimeout(later, wait)
    if (immediateCall) {
      fn.apply(this, args)
    }
  }
}
/**
 * function
 * @param {obj} obj
 * @description 将对象转化为字符串
 */
function toQueryString(obj = {}) {
  if (!obj) {
    return ''
  }
  let arr = []
  for (let key in obj) {
    let value = isObject(obj[key]) || isArray(obj[key]) ? JSON.stringify(obj[key]) : obj[key]
    arr.push(`${key}=${value}`)
  }
  let str = arr.length ? `${arr.join('&')}` : ''
  return str
}
// 将url后面参数转化为obj
function toQueryObj(s = '', { decodeOnEach = false } = {}) {
  var str = decodeOnEach ? s : decodeURIComponent(s)
  var arr = str.split('&');
  var obj = {};
  arr.forEach(item => {
    var a = item.split('=');
    obj[a[0]] = decodeOnEach ? decodeURIComponent(a[1]) : a[1]
  })
  return obj || {}
}
/**
 * function
 * @param {string} url 目标页面的路由
 * @param {Object} param 传递给目标页面的参数
 * @description 封装了小程序的跳转
 */
function navigateTo({
  url,
  param
}) {
  let margeUrl = param ? url + `?${toQueryString(param)}` : url
  wx.navigateTo({
    url: margeUrl,
    fail(err) {
      console.log('navigateTo跳转出错', err)
    },
  })
}

function each(arr, fn) {
  for (let i = 0; i < arr.length; i++) {
    if (fn(arr[i], i, arr) === false) return
  }
}
/**
 * 将source中的与target相同的属性赋值到target中去
 * 应用场景：详情获取的数据填充到表单中去，部分数据是表单不需要的
 */
function copyObjValue(target = {}, source = {}) {
  for (let key in target) {
    target[key] = source[key] ? source[key] : ''
  }
  return {
    ...target
  }
}
/**
 * @description 从url解析参数
 * @param {@} url
 */
function getUrlParam(url = '') {
  if (url.indexOf("?") < 0) return {}
  let queryParts = url.slice(url.indexOf("?") + 1).split('&');
  let params = queryParts.length > 0 ? {} : null;
  queryParts.map(function (item) {
    let a = item.split('=')
    params[a[0]] = a[1]
  })
  return params
}

function formatPrice(price) {
  if (!price) return 0
  var p = price / 100
  return Number(p.toFixed(2))
}

/**
 * 比较微信版本号大小, v1<v2 返回-1
 * @param {*} v1
 * @param {*} v2
 */
function compareVersion(v1, v2) {
  v1 = v1.split('.')
  v2 = v2.split('.')
  var len = Math.max(v1.length, v2.length)

  while (v1.length < len) {
    v1.push('0')
  }
  while (v2.length < len) {
    v2.push('0')
  }

  for (var i = 0; i < len; i++) {
    var num1 = parseInt(v1[i])
    var num2 = parseInt(v2[i])

    if (num1 > num2) {
      return 1
    } else if (num1 < num2) {
      return -1
    }
  }
  return 0
}
/**
 * 根据商品id和服务id生成商品唯一标识，满足一品多规校验
 * @param {*} goods
 */
function getGoodsOnlyId(goods) {
  let { selectSpecsServiceList = [], goodsSn } = goods || {}
  if (!selectSpecsServiceList.length) {
    return String(goodsSn)
  }
  let serviceId = isObject(selectSpecsServiceList[0]) ? (selectSpecsServiceList[0].serviceSn || '') : selectSpecsServiceList[0]
  return String(goodsSn) + serviceId
}

/**
 * 存储选中的商品
 * isSetCartGoodsSelected 当前商品如果已经在购物车里面了且没有选中，是否给选中
 */
function storageCheckGoods(goodsList = [], isSetCartGoodsSelected = false) {
  if (!goodsList.length) return
  let { onSaleGoodsCheckedIds = [], b2cGoodsCheckedIds = [] } = wx.getStorageSync('checkObj') || {}
  const cartGoods = wx.getStorageSync('cartGoods') || []
  goodsList.forEach(v => {
    const goodsCode = getGoodsOnlyId(v)
    const selected = isSetCartGoodsSelected ? true : cartGoods.indexOf(goodsCode) === -1
    if (selected) {
      if (v.takeawayAttr.toUpperCase() === "B2C") {
        b2cGoodsCheckedIds.indexOf(goodsCode) === -1 && b2cGoodsCheckedIds.push(goodsCode)
      } else {
        onSaleGoodsCheckedIds.indexOf(goodsCode) === -1 && onSaleGoodsCheckedIds.push(goodsCode)
      }
    }
  })
  wx.setStorageSync('checkObj',{
    onSaleGoodsCheckedIds,
    b2cGoodsCheckedIds
  })
}
/**
 * @description: canvas绘制圆角矩形
 * @param {CanvasContext} ctx canvas上下文
 * @param {Number} x 左上角x坐标
 * @param {Number} y 左上角y坐标
 * @param {Number} w 矩形宽度
 * @param {Number} h 矩形高度
 * @param {Number || Object} r 圆角配置
 * @param {String || CanvasGradent} c 填充颜色
 */
function fillRoundRect (ctx, x, y, w, h, r) {
  let {lt = r, rt = r, lb = r, rb = r} = isObject(r) ? r : {}

  ctx.beginPath()

  // 左上角
  ctx.arc(x + lt, y + lt, lt, Math.PI, Math.PI * 1.5)

  // border-top
  ctx.moveTo(x + lt, y)
  ctx.lineTo(x + w - rt, y)
  ctx.lineTo(x + w, y + rt)
  // 右上角
  ctx.arc(x + w - rt, y + rt, rt, Math.PI * 1.5, Math.PI * 2)

  // border-right
  ctx.lineTo(x + w, y + h - rb)
  ctx.lineTo(x + w - rb, y + h)
  // 右下角
  ctx.arc(x + w - rb, y + h - rb, rb, 0, Math.PI * 0.5)

  // border-bottom
  ctx.lineTo(x + lb, y + h)
  ctx.lineTo(x, y + h - lb)
  // 左下角
  ctx.arc(x + lb, y + h - lb, lb, Math.PI * 0.5, Math.PI)

  // border-left
  ctx.lineTo(x, y + lt)
  ctx.lineTo(x + lt, y)

  ctx.fill()
  ctx.closePath()
}
/**
 * @description: canvas文本超出长度省略处理
 * @param {CanvasContext} ctx canvas上下文
 * @param {String} content 待处理内容
 * @param {Number} limitWidth 限宽
 * @param {Number} size 字号
 * @return: 处理后字符
 */
function fitContent (ctx, content, limitWidth, size) {
  ctx.setFontSize(size)
  let ret
  let ellipsis = '...'
  let originWidth = ctx.measureText(content).width
  let ellipsisWidth = ctx.measureText(ellipsis).width
  if (originWidth <= limitWidth) {
    ret = content
  } else {

    let length = content.length
    while (originWidth > (limitWidth - ellipsisWidth) && (length --) > 0) {
      content = content.substring(0, length)
      originWidth = ctx.measureText(content).width
    }
    ret = content + ellipsis
  }
  ctx.restore()
  return ret
}
/**
 * 跳转app下载引导页
 */
function navigateToDownLoadH5() {
  let pageUrl = 'http://mp.weixin.qq.com/s?__biz=MjM5ODAwMTYwMA==&mid=521139153&idx=1&sn=375d762d3186d7596f297023a08d813b&chksm=3c2a3d7e0b5db468c113e25d4421397dd4f5f5bc810e024e8bc5dcc478ace022c9df3aafbb35#rd'
  wx.navigateTo({
    url: '/h5/pages/commonLink/index?pageUrl=' + encodeURIComponent(pageUrl),
  })
}

/**
 * 对象拼接成url参数
 * @param {Object} options
 * @param {Array} excludeKeyList 不需要拼接的属性列表
 */
function createQueryParams(options, excludeKeyList = []) {
  let queryString = ''
    for (let key in options) {
      if (options.hasOwnProperty(key)) {
        const excludeKey = excludeKeyList.find(keyStr => String(keyStr) === String(key))
        if (!excludeKey) {
          queryString += '&' + key + '=' + options[key]
        }
      }
    }
    let str = ''
    if (queryString.length > 0) {
      str = queryString.substring(1, queryString.length)
    }
    return str
}
/**
 * 对象取值
 * @param {Object} source 取值对象
 * @param {String} path 取值路径
 */
function getObjectValue (source, path, defaultValue = void 0) {
  // a[3].b -> a.3.b
  const paths = path.replace(/\[(\d+)\]/g, '.$1').split('.')
  let result = source
  for (const p of paths) {
      result = Object(result)[p]
      if (result === void 0) {
      return defaultValue
      }
  }
  return result
}
/**
 * @description 表情包编码
 * @param {*} str
 */
function utf16toEntities(str = '') {
  const patt = /[\ud800-\udbff][\udc00-\udfff]/g; // 检测utf16字符正则
  str = str.replace(patt, (char) => {
      let H;
      let L;
      let code;
      let s;
      if (char.length === 2) {
          H = char.charCodeAt(0); // 取出高位
          L = char.charCodeAt(1); // 取出低位
          code = (H - 0xD800) * 0x400 + 0x10000 + L - 0xDC00; // 转换算法
          s = `&#${code};`;
      } else {
          s = char;
      }
      return s;
  });
  return str;
}
/**
 * @params list 需要过滤的列表
 */
function filterChannelSeparation (list, property = "channelSeparation", permuteInfo) {
  try {
    return list.filter(item => {
      return !permuteInfo.includes(item[property]);
    });
  } catch (error) {
    return list;
  }
}
/**
 * 根据数据生成排列组合
 * @param {*} nums
 */
function permute(nums) {
  const result = []
  backTrack(nums, result, [])
  return result.map((item)=>{
      return item.join(',')
  })
};
function backTrack (nums, result, track) {
  if (track.length === nums.length) {
      // 更改引用类型的指针。目的；防止回溯行为影响到当前数组状态。
      result.push([...track])
      return
  }
  for (let i = 0; i < nums.length; i++) {
      if (track.includes(nums[i])) {
          continue
      }
      track.push(nums[i])
      backTrack(nums, result, track)
      track.pop()
  }
}
/**
 * 数组转2维数组
 * @param {*} objArray
 * @param {*} length
 * @param {*} num
 */
function arraySplite2Arry(objArray,length = 2,num = 1){
  let len = objArray.length;
  let lineNum = len % length === 0 ? len / length : Math.floor( (len / length) + 1 );
  let res = [];
  for (let i = 0; i < lineNum; i++) {
    let temp = objArray.slice(i*length, i*length+length);
    res.push(JSON.parse(JSON.stringify(temp)));
  }
  let formatRes =  res.map((item)=>{
      if(item.length === length){
          return item
      }
  })
  // 过滤出正常的值 并截取想要的长度
  return formatRes.filter((item)=>{
    if(!!item){
      return item
    }
  }).slice(0,num)
}

/**
 * @typedef NavigateConfig
 * @property {string} NavigateConfig.lon
 * @property {string} NavigateConfig.lat
 * @property {string} NavigateConfig.address
 * @property {string} NavigateConfig.name
 */

/**
 * 打开导航
 * @param {NavigateConfig} config 导航配置
 */
function openNavigate(config) {
  if (Object.keys(config).length) {
    const coordtransform = require('./coordUtil')
    const x = coordtransform.bd09togcj02(parseFloat(config.lon), parseFloat(config.lat))
    wx.openLocation({
      latitude: x[1],
      longitude: x[0],
      name: config.name,
      address: config.address
    })
  } else {
    commonObj.showModal('提示', '找不到门店坐标', false, '我知道了')
  }
}

/**
 * @typedef { Object } Position 坐标
 * @property { string } latitude 纬度
 * @property { string } longitude 经度
 */

/**
 * 根据经纬度调用腾讯地图服务，获取路线
 * https://lbs.qq.com/service/webService/webServiceGuide/webServiceRoute#4
 * @param { Position } from
 * @param { Position } to
 * @returns
 */
 function getLine(from, to) {
  return new Promise((resolve, rej) => {
      let url = `https://apis.map.qq.com/ws/direction/v1/bicycling/?from=${from.latitude},${from.longitude}&to=${to.latitude},${to.longitude}&output=json&key=${commonObj.qqMapKey}`
      wx.request({
          url: url,
          method: 'get',
          success(res) {
              let coors = res.data.result.routes[0].polyline
              for (let i = 2; i < coors.length; i++) {
                  coors[i] = coors[i - 2] + coors[i] / 1000000
              }
              let points = [];
              for (let j = 0; j < coors.length; j++) {
                  points.push({
                      latitude: parseFloat(coors[j]),
                      longitude: parseFloat(coors[j + 1])
                  })
                  j++;
              }
              res.points = points
              resolve(res)
          }
      })
  })
}

/**
 * 传入一个门店列表，格式化门店信息
 * @param {*} data
 */
function setStoreListInfo(data) {
  data.forEach(el => {
    el.storeBusinessTime = getStoreBusinessTime({
      startTime:el.startTime || '',
      endTime:el.endTime || '',
      openingTime:el.openingTime || ''
    })
    const labelList = []

    const { isSupportTake, isTimelySupport } = el
    //  均支持时，无需展示“仅xx”文案
    if (isTimelySupport === 'Y' && isSupportTake === 'Y') {
      return
    }

    labelList.push({ text: isTimelySupport === 'Y' ? '仅支持配送' : '仅支持自提' })

    el.labelList = labelList
  })
}

/**
 * @description 返回门店营业时间
 * @param { String } startTime 07:20 门店开始营业时间
 * @param { String } endTime 20:00 门店结束营业时间
 * @param { String } openingTime 20:00 当前城市营业时间(没有门店营业时间取用)
 * @returns
 */
 function getStoreBusinessTime({startTime='', endTime='',openingTime=''}) {
   if(startTime&&endTime){
    const startHour = startTime ? parseFloat(startTime.split(':')[0]) : 0
    const endHour = endTime ? parseFloat(endTime.split(':')[0]) : 0
    return `${startTime}-${endHour < startHour ? '次日' : '' }${endTime}`
   }
   return openingTime
}

/**
 * 获取用户坐标
 * @returns { Position }
 */
 function getUserLocation() {
  let latitude = ''
  let longitude = ''
  const userCurrLoca = wx.getStorageSync('userCurrLoca');

  if (userCurrLoca && userCurrLoca.location) {
    const { location: { lat = '', lng = '' } } = userCurrLoca
    latitude = lat
    longitude = lng
  }

  return {
    latitude,
    longitude
  }
}

/**
 * 一个不断显示倒计时的loading
 * @param { Object } param0
 * @param { number } param0.second 倒计时多少秒
 * @param { Promise } param0.promiseInstance Promise实例
 * @param { string } param0.loadingText 加载时toast文案
 * @param { string } param0.timeoutTip 倒计时结束提示文案
 * @param { boolean } param0.isTimeDown 是否有倒计时效果
 * @param { Function } param0.callback 倒计时结束回调
 * @returns { Function }
 */
function countDownLoading ({ second = 10, promiseInstance, loadingText = '提交中', timeoutTip = '系统有点繁忙，请再次提交售后~', isTimeDown = false, callback } = {}) {
  let count = second
  const timer = setInterval(() => {
    if (count <= 0) {
      clear()
      if (timeoutTip) {
        wx.showModal({
          content: timeoutTip,
          showCancel: false
        })
      }

      if (callback) {
        callback()
      }

      const abortFn = promiseInstance && promiseInstance.requestTask && promiseInstance.requestTask.abort
      if (typeof abortFn === 'function') {
        //  中断请求
        promiseInstance.requestTask.abort()
      }
      return
    }

    wx.showLoading({
      title: `${loadingText}` + (isTimeDown ? `(${count})` : ''),
      mask: true
    })

    count--
  }, 1000)

  const clear = () => {
    wx.hideLoading()
    clearInterval(timer)
  }

  if (promiseInstance && promiseInstance instanceof Promise) {
    promiseInstance.finally(() => clear())
  }

  return clear
}
// 检查是否为企业微信
const checkIsQy = (function() {
  let environment = ''
  return function() {
    if (environment === '') {
      environment = typeof wx !== 'undefined' && wx.qy
    }
    return environment
  }
})()

function deepClone(obj) {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  let clone;

  if (Array.isArray(obj)) {
    clone = [];
    for (let i = 0; i < obj.length; i++) {
      clone.push(deepClone(obj[i]));
    }
  } else {
    clone = {};
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        clone[key] = deepClone(obj[key]);
      }
    }
  }

  return clone;
}
/**
 * 获取神策上报使用唯一id
 * @returns
 */
function getSensorsUniqueID () {
  const app = getApp()
  const isSignIn = app.checkSignInsStatus()
  const time = new Date().getTime()

  if (isSignIn) {
    const { userID } = wx.getStorageSync('user') || {}
    return `${userID}${time}`
  } else {
    const distinctId = sensors.store.getDistinctId()
    return `${distinctId}${time}`
  }
}

/**
 * emoji匹配正则
 */
const emojiReg = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi

function checkEmoji (value) {
  emojiReg.lastIndex = 0;
  return emojiReg.test(value)
}

/**
 * 移除内容中的emoji
 * @param {*} value
 */
function removeEmoji (value) {
  if (!value) {
    return ''
  }
  return value.replace(emojiReg, '')
}
function deepCloneMap(obj) {
  if (obj === null || typeof obj !== 'object' || obj instanceof Date || obj instanceof RegExp) {
    // 如果obj是基本数据类型（null、字符串、数字、布尔、日期、正则等），直接返回
    return obj;
  }

  if (obj instanceof Map) {
    // 处理Map类型数据
    const copyMap = new Map();
    obj.forEach((value, key) => {
      copyMap.set(deepCloneMap(key), deepCloneMap(value));
    });
    return copyMap;
  }

  if (Array.isArray(obj)) {
    // 处理数组
    return obj.map(deepCloneMap);
  }

  // 处理普通对象
  const copyObj = {};
  Object.keys(obj).forEach((key) => {
    copyObj[key] = deepCloneMap(obj[key]);
  });
  return copyObj;
}

/**
 * 获取当前页面
 * @returns
 */
function getCurrentPageSync() {
  const pages = getCurrentPages()
  if (pages.length) {
    const page = pages[pages.length - 1]
    return page
  }
  return {}
}

/**
 * 根据传入路由路径，判断当前页面是否当前路由路径
 * @param { string } route 路由路径
 * @returns
 */
function isRoute(route) {
  return getCurrentPageSync().route === route
}

/**
 * 跳转二级购物车
 */
function toSubShopCart() {
  wx.navigateTo({
    url: '/homeDelivery/pages/subShopCart/index'
  })
}

/**
 * 判断当前页面是否为sensorsPageConfigMap配置中的页面
 */
function isSensorsPageConfigPages() {
  const curPages = getCurrentPageSync().route || ''
  const pageConfig = sensorsPageConfigMap[curPages] || {}
  return pageConfig
}

function toHump(str) {
  return str.replace(/_(\w)/g, function (all, letter) {
    return letter.toUpperCase()
  })
}

function toUnderLine(str) {
  return str.replace(/([A-Z])/g, '_$1').toLocaleLowerCase()
}

// 将字符串编码为 Uint8Array
function encodeString(str) {
  const utf8Array = []
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i) // 获得字符的ASCII
    if (charCode < 0x80) {
      utf8Array.push(charCode)
    } else if (charCode < 0x800) {
      utf8Array.push(0xc0 | (charCode >> 6))
      utf8Array.push(0x80 | (charCode & 0x3f))
    } else if (charCode < 0x10000) {
      utf8Array.push(0xe0 | (charCode >> 12))
      utf8Array.push(0x80 | ((charCode >> 6) & 0x3f))
      utf8Array.push(0x80 | (charCode & 0x3f))
    } else {
      utf8Array.push(0xf0 | (charCode >> 18))
      utf8Array.push(0x80 | ((charCode >> 12) & 0x3f))
      utf8Array.push(0x80 | ((charCode >> 6) & 0x3f))
      utf8Array.push(0x80 | (charCode & 0x3f))
    }
  }
  return new Uint8Array(utf8Array)
}

/**
 * 字符串转为base64
 * @param {String} inputString 文本
 * @returns base64编码的字符串
 */
function base64Encode(inputString) {
  const keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-/='
  if (inputString) {
    const inputBytes = encodeString(inputString)
    let output = ''
    let chr1, chr2, chr3, enc1, enc2, enc3, enc4
    let i = 0

    while (i < inputBytes.length) {
      chr1 = inputBytes[i++]
      chr2 = inputBytes[i++]
      chr3 = inputBytes[i++]

      enc1 = chr1 >> 2
      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4)
      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6)
      enc4 = chr3 & 63

      if (isNaN(chr2)) {
        enc3 = enc4 = 64
      } else if (isNaN(chr3)) {
        enc4 = 64
      }

      output += keyStr.charAt(enc1) + keyStr.charAt(enc2) + keyStr.charAt(enc3) + keyStr.charAt(enc4)
    }
    let str = output.replace(/\//g, '_')
    str = str.replace(/\+/g, '-')
    output = str
    return output
  }
}


// 定义一个函数用于处理重试逻辑
async function retryRequest(request, initialDelay = 2000, maxRetries = 3) {
  let delay = initialDelay;
  let attempts = 0;

  while (attempts < maxRetries) {
    try {
      const isFans = await request(); // 尝试请求
      if (isFans) break;
    } catch (error) {
      console.log('请求失败:', error);
    }
    attempts++;
    console.log(`第${attempts}次重试...`);
    if (attempts >= maxRetries) {
      console.log('达到最大重试次数，停止重试');
      break
    }
    await new Promise(resolve => setTimeout(resolve, delay)); // 等待一段时间后重试
    delay += Math.floor(delay * 2); // 下一次的延迟时间是当前的两倍
  }
}

const { _getApp, _setApp } = (function() {
  let app
  return {
    _getApp() {
      return app || (app = getApp())
    },
    _setApp(appContext) {
      app = appContext
    },
  }
})()

function omitFields(object, ignoreList) {
  return Object.keys(object).reduce((acc, cur) => {
    if (ignoreList.includes(cur)) return acc
    acc[cur] = object[cur]
    return acc
  }, {})
}

function pickFields(object, pickList) {
  return Object.keys(object).reduce((acc, cur) => {
    if (!pickList.includes(cur)) return acc
    acc[cur] = object[cur]
    return acc
  }, {})
}

function toHttps(url) {
  return url ? url.replace(/^http:\/\//, 'https://') : ''
}

const util = {
  _getApp,
  _setApp,
  toHttps,
  navigateTo,
  throttle,
  debounce,
  each,
  copyObjValue,
  isEmptyObject,
  toQueryObj,
  getUrlParam,
  isNumber,
  isArray,
  isEmpty,
  formatPrice,
  compareVersion,
  getGoodsOnlyId,
  storageCheckGoods,
  fillRoundRect,
  fitContent,
  navigateToDownLoadH5,
  createQueryParams,
  toQueryString,
  getObjectValue,
  utf16toEntities,
  filterChannelSeparation,
  permute,
  arraySplite2Arry,
  openNavigate,
  getLine,
  getUserLocation,
  getStoreBusinessTime,
  setStoreListInfo,
  countDownLoading,
  checkIsQy,
  getSensorsUniqueID,
  emojiReg,
  removeEmoji,
  checkEmoji,
  deepClone,
  deepCloneMap,
  getCurrentPageSync,
  isRoute,
  toSubShopCart,
  isSensorsPageConfigPages,
  toHump,
  toUnderLine,
  base64Encode,
  retryRequest,
  omitFields,
  pickFields
}

export default util

export {
  _getApp,
  _setApp,
  toHttps,
  navigateTo,
  throttle,
  debounce,
  each,
  copyObjValue,
  isEmptyObject,
  toQueryObj,
  getUrlParam,
  isNumber,
  isArray,
  isEmpty,
  formatPrice,
  compareVersion,
  getGoodsOnlyId,
  storageCheckGoods,
  fillRoundRect,
  fitContent,
  navigateToDownLoadH5,
  createQueryParams,
  toQueryString,
  getObjectValue,
  utf16toEntities,
  filterChannelSeparation,
  permute,
  arraySplite2Arry,
  openNavigate,
  getLine,
  getUserLocation,
  getStoreBusinessTime,
  setStoreListInfo,
  countDownLoading,
  checkIsQy,
  getSensorsUniqueID,
  emojiReg,
  removeEmoji,
  checkEmoji,
  deepClone,
  deepCloneMap,
  getCurrentPageSync,
  isRoute,
  toSubShopCart,
  isSensorsPageConfigPages,
  toHump,
  toUnderLine,
  base64Encode,
  retryRequest,
  omitFields,
  pickFields
}
