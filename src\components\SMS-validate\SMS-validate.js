// components/SMS-validate.js
const sms = require.async('../../sourceSubPackage/commonUtils/sms/index')
// 默认的倒计时时间
const COUNTDOWN_SECONDS = 60

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示了当前验证码提示框
    visible:{
      type: Boolean,
      value: false
    }
  },
  observers:{
    /**
     * 监听是否显示了验证码弹窗
     * @param {Boolean} isShow 状态
     */
    visible: function(isShow) {
      // 可见发送
      if (isShow) {
        this.sendCode()
      }
    },
    /**
     * 监听倒计时秒数的变化
     * @param {Number} countdownSeconds 剩余的秒数
     */
    seconds: function(countdownSeconds){
      if (countdownSeconds > 0 && countdownSeconds < COUNTDOWN_SECONDS) {
        this.setData({
          sendText: `${countdownSeconds}s后重新获取`,
          disabledSendBtn: true
        })
      }

      // 如果秒数为0了 则重置按钮状态
      if (countdownSeconds <= 0) {
        this.setData({
          sendText: '重新获取验证码',
          disabledSendBtn: false,
          seconds: COUNTDOWN_SECONDS
        })
        // 结束倒计时
        if (this._data.timeout) {
          clearInterval(this._data.timeout)
          this._data.timeout = null
        }
      }
    }
  },
  _data: {
    // 倒计时 定时器对象
    timeout: null,
    // 是否正在发送中，目的是为了防止visible连续多次为true触发多次短信
    isSending: false
  },
  lifetimes: {
    created () {
      console.log('created');
      this._data = {}
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    // 输入的验证码
    SMSCode: '',
    // 发送、倒计时文案
    sendText: '获取验证码',
    // 发送按钮是否置灰
    disabledSendBtn: false,
    // 倒计时 s
    seconds: COUNTDOWN_SECONDS,
    // 当前用户的手机尾号（后4位）
    mobileSuffix: '',
    // 是否验证不通过
    isValidateError: false,
    // 是否聚焦
    isFocus: false
  },
  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 发送验证码到手机
     * @param {Object} e 按键默认事件
     * @returns {Promise<Boolean>} 是否发送成功
     */
    async sendCode(e){
      // 倒计时未结束不允许发送
      // 不可见 || 不禁用 || 已开始倒计时
      if (!this.data.visible || this._data.isSending || this.data.disabledSendBtn && this.data.seconds !== COUNTDOWN_SECONDS) {
        return false
      }
      const user = wx.getStorageSync('user') || {phoneNumber: ''};
      const mobileSuffix = user.phoneNumber.slice(-4)

      this.setData({
        sendText: '验证码发送中',
        disabledSendBtn: true,
        mobileSuffix
      })
      this._data.isSending = true
      const res = await this.requestSendRes(user.phoneNumber)
      setTimeout(() => {
        this.setData({
          isFocus: true
        })
      }, 500)
      this._data.isSending = false
      if (!res) {
        this.setData({
          sendText: '重新获取验证码',
          disabledSendBtn: false,
          seconds: COUNTDOWN_SECONDS
        })
        return
      }
      // 如果成功 开始倒计时
      if (this._data.timeout) {
        clearInterval(this._data.timeout)
      }
      this._data.timeout = setInterval(() => {
        this.setData({
          seconds: this.data.seconds - 1
        })
      }, 1000)
    },
    /**
     * @desc 请求API 发送 并 获取是否发送成功
     * @returns {Promise<Boolean>} 发送结果
     */
    async requestSendRes(phoneNumber) {
      if (!phoneNumber) return false
      const { sceneType, sendSms } = await sms
      const sendRes = await sendSms({ scene: sceneType.confirmOrder, phoneNumber })
      return sendRes
    },
    /**
     * @desc 请求API 验证结果是否正确
     */
    async requestVerifyRes() {
      const user = wx.getStorageSync('user') || {phoneNumber: ''};
      if (!this.data.SMSCode || this.data.SMSCode.length !== 4) {
        return { succ: false }
      }
      const app = getApp()
      try {
        const { sceneType } = await sms
        const resp = await app.api.smsVerify({
          smsScene: sceneType.confirmOrder,
          phoneNumber: user.phoneNumber,
          code: this.data.SMSCode
        })
        const { succ } = resp.data
        console.log('验证码验证结果', succ)
        return { succ, msg: '验证码错误' }
      } catch (error) {
        console.log('验证码验证异常', error)
        return { succ: false }
      }
    },
    /**
     * 点击清除输入的内容
     * @param {Object} e 按键默认事件
     */
    clearBtnClick(e) {
      this.setData({
        SMSCode: ''
      })
    },
    /**
     * 点击确定校验验证码是否正确
     * @param {Object} e 按键默认事件
     */
    async validateCode(e) {
      // 不满足4位 不允许点击
      if (!this.data.SMSCode || this.data.SMSCode.length !== 4) {
        return
      }
      this.setData({
        isValidateError: false
      })

      // 请求验证码接口
      const { succ, msg } = await this.requestVerifyRes()
      if (!succ) {
        wx.showToast({
          title: msg,
          icon: 'none'
        })
        this.setData({
          isValidateError: true
        })
        return
      }
      // 验证通过直接记录设备信息到服务端
      // 把上报设备信息的逻辑移到success页面去了，原因是因为使用其他支付方式也需要上报，而不仅仅是使用钱包支付
      // updateUserDeviceInfo()
      this.triggerEvent('validated', { validated: true })
    },
    /**
     * 验证码框输入变化
     * @param {Object} e 按键默认事件
     */
    inputCode(e) {
      const SMSCode = (e.detail.value || '').replace(/\D/g, '')
      this.setData({
        SMSCode
      })
    }
  }
})
