Component({
  options: {
    multipleSlots: true
  },
  /**
   * 组件的属性列表
   */
  properties: {
    navBarTitle: {
      type: String,
      value: ''
    },
    background: {
      type: String,
      value: '#FFFFFF'
    },
    color: {
      type: String,
      value: '#222222'
    },
    isShowBack: {
      type: Boolean,
      value: true
    },
    // 默认返回事件
    normalBack: {
      type: Boolean,
      value: false
    },
    // 背景渐变效果动画时长，默认不设置
    bgColorDuration: {
      type: Number,
      value: 0
    },
    position: {
      type: String,
      value: 'fixed'
    },
    display: {
      type: String,
      value: 'block'
    },
    /**
     * 是否自定义导航栏高度
     */
    customHeight: {
      type: Boolean,
      value: false
    },
    /** 是否需要占位高度 */
    withPlaceholderHeight: {
      type: Boolean,
      value: true,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 20, //px
    paddingRight: 281, //px
    titleHeight: 44, //px
    navBarHeight: 64 //px
  },

  /**
   * 组件的方法列表
   */
  methods: {
    back() {
      if (this.data.normalBack) {
        wx.navigateBack({
          fail() {
            wx.reLaunch({
              url: '/pages/homeDelivery/index'
            })
          }
        })
      } else {
        this.triggerEvent('back')
      }
    },
    setNavStyle({ statusBarHeight, screenWidth }) {
      let { top = 26, height = 32, left = 0 } = wx.getMenuButtonBoundingClientRect() || {}
      let titleHeight = height + (top - statusBarHeight)*2
      let navBarHeight = statusBarHeight + titleHeight
      this.triggerEvent('navBarHeight', navBarHeight)
      this.triggerEvent('statusBarHeight', statusBarHeight)
      this.triggerEvent('titleHeight', titleHeight)
      this.setData({
        paddingRight: screenWidth - left,
        statusBarHeight,
        titleHeight,
        navBarHeight
      })
    }
  },
  lifetimes: {
    attached () {
      //  为保证screenWidth与left获取的值均为屏幕正确值，增加异步回调
      wx.nextTick(() => {
        wx.getSystemInfo({
          success: (res) => {
            this.setNavStyle(res)
          }
        })
      })
    }
  }
})
