//  地图刷新定时器
let mapRefreshTimer = null;
//  地图刷新时间间隔
const mapRefreshInterval = 60000;

//  默认maker属性
const DEFAULT_MARKER_ATTR = {
    //  因为小程序map组件必须要指定iconPath，这里用了个1px透明图
    iconPath: 'https://resource.pagoda.com.cn/group1/M21/8C/BA/CmiWa2JroP-AMBV9AAAAK7cKkkM759.gif',
    //  给定1像素宽高是为了使浮动气泡能够正确定位
    width: 1,
    height: 1,
    customCallout: {
        display: 'ALWAYS'
    }
}

const app = getApp();
const util = require('../../../../../utils/util');
const coordtransform = require('../../../../../utils/coordUtil');
const { commonObj } = require('../../../../../source/js/common');
import { getPromiseObj } from '../../../../../utils/promise';
import wxappMap from '../../../../../service/wxappMap'

const PIC_MAP = {
    //  用户头像
    userAvatar: 'https://resource.pagoda.com.cn/group1/M21/90/AD/CmiWa2KCEwmABt8pAAAP_h8iqw8837.png',
    //  骑手头像
    deliveryAvatar: 'https://resource.pagoda.com.cn/group1/M21/91/0E/CmiLkGKCEySAVBr9AAAS_gNW5og695.png',
    //  骑手信息气泡
    bubbleURL: 'https://resource.pagoda.com.cn/group1/M21/8D/19/CmiLkGJrmguAOZi4AAAe5g8madU219.png',
    //  刷新按钮地址
    refreshURL: 'https://resource.pagoda.com.cn/group1/M21/8E/6E/CmiLkGJ0lU-AQfUfAAAGWsr-dow226.png'
}

Component({
    options: {
        multipleSlots: true, //  启用多slot支持
    },
    properties: {
        //  订单编号
        orderNo: {
            type: String,
            value: ''
        },

        //  收货人信息
        receiver: {
            type: Object,
            value: {}
        },

        //  门店信息
        store: {
            type: Object,
            value: {}
        },
        
        //  导航栏高度
        navBarHeight: {
            type: Number,
            value: 0
        },

        //  内容高度
        contentHeight: {
            type: String,
            value: ''
        },

        //  是否显示地图
        showMap: {
            type: Boolean,
            value: true
        },

        //  是否门店订单
        offlineOrder: {
            type: Boolean,
            value: false,
        },

        dispatchTime: {
            type: String,
            value: ''
        }
    },

    observers: {
        deliveryInfo() {
            wx.nextTick(async () => {
                await this.setComponentHeight()
                this.setMapHeight()
            });
        }
    },

    lifetimes: {
        created() {
            this._data = {
                setComponentHeightPromise: getPromiseObj(),
                componentHeightNum: 0, //  组件高度数值
            }  
        },
        //  组件实例进入页面节点树时，进行初始化行为
        async attached() {
            const isIphone = wx.getSystemInfoSync().model.includes('iPhone');
            this.setData({
                isIphone
            })

            this.setComponentHeight()
                .then(() => {
                    this._data.setComponentHeightPromise.resolve();
                });

            this.setData({
                mapctx: wx.createMapContext('map', this)
            });

            this.setUserMaker();
            await this.setDeliveryMarker();
            this.setTimer();

            //  因为cover-image会存在偶现图片加载成功但不显示的缺陷
            //  这边假定图片加载必定出现问题，默认重新加载一次。即使浪费
            setTimeout(() => {
                this.refreshCoverImage();
            }, 1000);
        },
        //  组件实例被从页面节点树移除时，销毁定时器
        detached() {
            this.clearMapRefreshTimer();
        }
    },

    pageLifetimes: {
        //  组件所在的页面被展示时，重新设置定时器
        show() {
            this.setDeliveryMarker();
            this.setTimer();
        },
        //  组件所在的页面被隐藏时，销毁定时器
        hide() {
            this.clearMapRefreshTimer();
        }
    },


    data: {
        //  坐标数组
        markers: [],
        //  配送员距离用户距离
        distance: '',

        //  组件高度
        componentHeight: '',

        //  配送信息高度
        deliverySectionHeight: 0,

        //  图片常量对象
        PIC_MAP: Object.assign({}, PIC_MAP),
        //  用户信息
        user: {},
        //  骑手信息
        delivery: {},

        // 配送信息
        deliveryInfo: {
            title: [
              { text: '预计' },
              { text: '', highlight: true, },
              { text: '送达' },
            ],
            desc: '您的订单配送中',
            showDeliveryGuy: true,
            showMap: true,
            showTimeout: true,
        },

        //  上一次滚动距离
        lastScrollTop: 0,
        //  地图轨迹数组
        polyline: [
            {
                points: [],
                color: "#00A34F",
                width: 4,
                arrowLine: true
            }
        ],

        /**是否苹果手机 */
        isIphone: false
    },
    methods: {
        /**
         * 设置组件高度
         */
        setComponentHeight() {
            const { contentHeight } = this.data;
            //  如果传入了vh/rem高度，则直接使用
            if (contentHeight && [/vh$/, /rem$/].some(unitReg => unitReg.test(contentHeight))) {
                this.setData({
                    componentHeight: contentHeight
                });
                const containerQuery = wx.createSelectorQuery()
                                .in(this);
                containerQuery.select('.map-container').boundingClientRect();
                return new Promise(resolve => containerQuery.exec(res => {
                    const [mapContainer] = res;
                    this._data.componentHeightNum = mapContainer.height;
                    resolve();
                }));
            }
            const query = wx.createSelectorQuery()

            query.select(".footer-btn-list").boundingClientRect();

            return new Promise((resolve) => query.exec(res => {
                const windowHeight = wx.getSystemInfoSync().windowHeight;
                const [footerBtnList] = res;
                const footerBtnHeight = footerBtnList.height;

                this._data.componentHeightNum = windowHeight - footerBtnHeight - 12

                this.setData({
                    componentHeight: `${this._data.componentHeightNum}px`
                }, resolve);
            }))
        },

        /**
         * 设置地图组件高度
         */
        async setMapHeight() {
            await this._data.setComponentHeightPromise.promise;
            const query = wx.createSelectorQuery().in(this);

            query.select(".delivery-section").boundingClientRect();
            query.exec(res => {
                const [deliverySection] = res;

                this.setData({
                    deliverySectionHeight: deliverySection.height,
                    mapHeight: this._data.componentHeightNum - deliverySection.height
                });
            });
        },

        /**
         * 图片加载事件，因为cover-view不支持widthFix这样的自适应高度，所以只能动态计算并设置
         * @param {*} e 
         */
        imgLoad(e) {
            console.info('头像加载成功', e.target.dataset.type)
        },

        /**
         * 刷新头像
         * @param { string } type 头像
         */
        refreshAvatar(type) {
            const retryKey = `retry${type}`;

            if (!this.data[retryKey]) {
                this.setData({
                    [retryKey]: 1
                });
            }
            const retryCount = this.data[retryKey];

            if (retryCount < 3) {
                console.info(`图片${type}强制加载第${retryCount}次`)
                //  加个版本号重试
                this.setData({
                    [retryKey]: retryCount + 1,
                    [`PIC_MAP.${type}`]: PIC_MAP[type] + `?v=${retryCount}`
                });
            }
        },

        /**
         * 图片失败重试加载
         * @param {*} e 
         */
        imgErrorRetry(e) {
            console.info('头像加载失败', e.target.dataset.type)

            this.refreshAvatar(e.target.dataset.type);
        },

        /**
         * 获取骑手坐标信息
         * @param { number } delay 延迟时间
         */
        getPositionInfo(delay) {
            return new Promise(resolve => {
                setTimeout(async () => {
                    const { data } = await app.api.getTimelyDeliveryInfo({
                        orderNo: this.data.orderNo,
                        offlineOrder: this.data.offlineOrder,
                    }).catch(() => ({ data: {} }));

                    if (data.deliveryInfo) {
                        this.setData({
                            deliveryInfo: data.deliveryInfo,
                            showMap: data.deliveryInfo.showMap,
                        });
                    }

                    //  存在骑手信息
                    if (data.deliveryName) {
                        let longitude;
                        let latitude;

                        //  返回数据存在骑手经纬度
                        if (data.longitude && data.latitude) {
                            longitude = data.longitude;
                            latitude = data.latitude;
                        }
                        //  因为没有返回真实骑手坐标，将门店的经纬度设置代替为骑手坐标
                        else {
                            //  后端返回的是百度地图经纬度规则，小程序使用的是腾讯地图。需要转换经纬度
                            const txLocation = coordtransform.bd09togcj02(this.data.store.longitude, this.data.store.latitude);
                            longitude = txLocation[0];
                            latitude = txLocation[1];
                        }

                        data.longitude = longitude;
                        data.latitude = latitude;

                        resolve(data);
                    }
                    //  无数据返回
                    else {
                        resolve({});
                    }

                }, delay);
            });
        },


        /**
         * 设置用户坐标
         */
        async setUserMaker() {
            const receiver = this.data.receiver;
            const txLocation = coordtransform.bd09togcj02(receiver.longitude, receiver.latitude);
            const user = {
                latitude: txLocation[1],
                longitude: txLocation[0]
            }

            this.setData({
                user,
                markers: [{
                    id: 1,
                    ...DEFAULT_MARKER_ATTR,
                    latitude: user.latitude,
                    longitude: user.longitude
                }]
            });
        },

        /**
         * 设置骑手坐标
         */
        async setDeliveryMarker() {
            //  骑手信息
            let delivery = {};
            //  允许请求的最大次数
            let maxCount = 3;

            /**
             * 因为TMS转调其他系统获取数据，所以做了异步查询。
             * 第一次调用接口的时候，TMS异步去查另一个系统，所以先返回缓存中的数据，第一次可能会存在没有数据的情况
             * 这边判断返回数据为空，继续查询
             */
            while (!Object.keys(delivery).length && maxCount > 0) {
                /**
                 * 延迟时间
                 * 因为短时间内调用接口，他们可能还是没法返回真实数据。所以在重试时加个延迟处理
                 * 第一次为0，之后每次等待1秒，最多3次
                 */
                const delay = maxCount === 3 ? 0 : 1000;

                const data = await this.getPositionInfo(delay);
                delivery = data;
                maxCount--;
            }

            if (Object.keys(delivery).length) {
                this.setData({
                    delivery
                });
            }

            //  存在骑手坐标
            if (delivery.latitude) {
                //  骑手坐标配置
                const marker = {
                    id: 2,
                    ...DEFAULT_MARKER_ATTR,
                    latitude: delivery.latitude,
                    longitude: delivery.longitude
                };

                const user = this.data.user;

                //  计算骑手轨迹路线坐标
                const res = await wxappMap.bicycling({
                    latitude: delivery.latitude,
                    longitude: delivery.longitude
                }, {
                    latitude: user.latitude,
                    longitude: user.longitude
                });

                //  设置标记及路线
                this.setData({
                    'markers[1]': marker,
                    'polyline[0].points': res.points
                });

                //  计算骑手距离
                await this.countDistance();
                //  地图缩放，包含用户及骑手
                this.setIncludePoints();
            }
            //  不存在骑手，地图定位回用户本身
            else {
                const user = this.data.user;
                this.data.mapctx.moveToLocation({
                    longitude: user.longitude,
                    latitude: user.latitude
                });
            }

        },

        /**
         * 清除地图刷新定时器
         */
        clearMapRefreshTimer() {
            clearInterval(mapRefreshTimer);
            mapRefreshTimer = null;
        },

        isDeliveryResolve() {
            const { deliveryStatus } = this.data.delivery || {}
            return [70].includes(deliveryStatus)
        },

        /**
         * 设置定时刷新定位定时器
         */
        setTimer() {
            this.clearMapRefreshTimer();
            if (this.isDeliveryResolve()) {
                return
            }
            mapRefreshTimer = setInterval(() => {
                if (this.isDeliveryResolve()) {
                    this.clearMapRefreshTimer();
                    return
                }
                this.setDeliveryMarker();
            }, mapRefreshInterval);
        },

        /**
         * 计算骑手距离用户距离
         */
        async countDistance() {
            const markers = this.data.markers;
            const { latitude: latFrom, longitude: lonFrom } = markers[0];
            const { latitude: latTo, longitude: lonTo } = markers[1];

            let distance = await commonObj.deliveryDistance(
                { lat: latFrom, lon: lonFrom, isNeedTransform: false },
                { lat: latTo, lon: lonTo, isNeedTransform: false }
            );
            let unit = 'm';

            if (distance >= 1000) {
                distance = (distance / 1000).toFixed(2);
                unit = 'km';
            }

            this.setData({
                distance: `${distance}${unit}`
            })
        },

        /**
         * 更新地图地图缩放，以包含所有坐标
         */
        setIncludePoints() {
            let padding = [100, 10, 10, 10];
            const isIphone = this.data.isIphone

            if (isIphone) {
                padding = [100, 50, 10, 50];
            }

            this.data.mapctx.includePoints({
                points: this.data.markers,
                padding
            });
        },

        /**
         * 设置刷新动画
         */
        setRefreshAnimate() {
            this.setData({
                refreshClassName: 'refresh-icon--rotate'
            });
            setTimeout(() => {
                this.setData({
                    refreshClassName: ''
                });
            }, 1000);
        },

        /**
         * 联系骑手
         */
        concatDelivery() {
            const phoneNumber = this.data.delivery.deliveryMobile;

            wx.makePhoneCall({
                phoneNumber
            })
        },

        /**
         * 因为cover-image会存在偶现图片加载成功但不显示的缺陷
         */
        refreshCoverImage() {
            this.refreshAvatar('userAvatar');
            this.refreshAvatar('deliveryAvatar');
            this.refreshAvatar('bubbleURL');
        },

        /**
         * 节流处理点击刷新按钮
         */
        refresh: util.throttle(function () {
            //  这边假定图片加载出现问题，所以在点击刷新的时候进行补偿刷新
            this.refreshCoverImage();
            //  设置刷新动画
            this.setRefreshAnimate();
            //  更新骑手位置信息
            this.setDeliveryMarker();
            //  更新定时地图更新定时器
            this.setTimer();
        }, 1000)
    }
})