/* components/coupon/index.wxss */
@import '/source/style/border.wxss';
.one-line {
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.coupon {
  display: flex;
  width: 702rpx;
  margin: 25rpx 21rpx 0rpx 27rpx;
  position: relative;
  flex-direction: column;
}

/* 头部信息栏 */
.body {
  /* background-image: url(https://resource.pagoda.com.cn/group1/M00/30/82/CmiWiF9bSOGABWwZAAAEm36G5ws928.png);
  background-size: 100% 100%; */
  background-color: #F5F9F7;
  border-radius: 16rpx;
  display: flex;
  width: 100%;
  height: auto;
  position: relative;
}
.body::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 24rpx;
  height: 24rpx;
  margin-top: -12rpx;
  border-radius: 50%;
  background-color: #fff;
  left: -12rpx;
}
.vip-body {
  /* background-image: url(https://resource.pagoda.com.cn/group1/M21/6C/26/CmiWa2GzHBmAG1rXAAAEq46cQzM999.png); */
  background: #FFFAF2;
  border-radius: 16rpx;
}
.header {
  width: 178rpx;
  height: 188rpx;
  position: relative;
  color: rgba(0,140,60,1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 8rpx;
}
.name {
  font-size: 72rpx;
  font-weight: 700;
  line-height: 76rpx;
}
.name.small {
  font-size: 44rpx;
}
.name-decorate {
  font-size: 24rpx;
  font-weight: 600;
}
.append {
  margin: 10rpx 0 10rpx;
  font-size: 22rpx;
  width: 100%;
  text-align: center;
  line-height: 30rpx;
}

.split {
  width: 100%;
  height: 16rpx;
  position: absolute;
  top: -10rpx;
  left: 0;
}

.qrcode {
  width: 20rpx;
  height:20rpx;
  margin-right: 4rpx;
}
.stamp {
  width: 110rpx;
  height: 110rpx;
}

.content {
  flex: 1;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 20rpx 24rpx 20rpx 12rpx;
}
.title-info {
  display: flex;
  align-items: center;
}
.title-box {
  width: 100%;
  flex: 1;
  overflow: hidden;
}
.title {
  color: #222222;
  line-height: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}
.type-str {
  font-size: 22rpx;
  line-height: 26rpx;
  height: 26rpx;
  padding: 0 4rpx;
  display: inline-block;
  color: #555;
  max-width: 234rpx;
}
.limit-tag {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
}
.limit-time {
  height: 26rpx;
  font-size: 22rpx;
  font-weight: 400;
  line-height: 26rpx;
  margin-right: 8rpx;
  color: #F64742;
}
.limit-time.pre {
  color: #00A644;
}
.period {
  font-size: 22rpx;
  color:#555;
  line-height: 30rpx;
}
.type-str:not(:last-child) {
  margin-right: 8rpx;
}


/* 详细信息栏 */
.more {
  font-size: 22rpx;
  position: relative;
  padding: 6rpx 24rpx 0 0;
  line-height: 30rpx;
  color:#888;
}
.more.fold {
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.fold view {
  display: inline;
  margin-right: 10rpx;
}

.arrow {
  top: 4rpx;
  right: 0;
  width: 20rpx;
  height: 20rpx;
  position: absolute;
  display: inline-block;
  transform: rotate(-90deg);
}
.fold .arrow { transform: rotate(90deg) }


.vip-sign {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}
.coupon-name {
  flex: 1;
}
.vip-gold {
  color: #5C3414;
}
.title-vip-gold{
  color: #222222;
}
.open-renewal {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vip-tips{
  font-size: 24rpx;
  color: #B8935A;
}

.vip-gold-str{
  color: #5C3414;
}

.operator {
  width: 120rpx;
  height: 56rpx;
  border-radius: 28rpx;
  border: 1px solid #008C3C;
  /* line-height: 56rpx; */
  text-align: center;
  font-size: 24rpx;
  color: #008C3C;
  /* margin-left: 20rpx; */
  display: flex;
  justify-content: center;
  align-items: center;
}

.receive {
  color: #FFFFFF;
  background: #3EA965;
  border-color: transparent;
}

.btn-gold{
  border-color: #DCBB86;
  color: #DCBB86;
}

.vip-right-icon .receive {
  background: #DCBB86;
}

.vip-right-icon .operator {
  border-color: #5C3414;
  color: #5C3414;
}

.time-box {
  margin-top: 6rpx;
  line-height: 30rpx;
}
