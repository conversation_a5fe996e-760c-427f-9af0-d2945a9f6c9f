<!--homeDelivery/pages/signIn/phoneNumber/index.wxml-->
<!-- 手机号登录注册页 -->
<view class="phoneNumber-wrapper">
  <nav-bar
    background="#fff"
    color="#000"
    navBarTitle="欢迎登录百果园"
    style="position: relative"
    normalBack
  >
  </nav-bar>
  <view class="title-wrapper">
    <image class="logo" src="https://resource.pagoda.com.cn/dsxcx/images/c8509dfe9a34d7222985c9eafe0574a7.png"></image>
    <image class="slogan" src="https://resource.pagoda.com.cn/dsxcx/images/996e718bde528911288f27a42041f8ab.png" />
  </view>
  <view class="form-wrapper">
    <view class="form-row {{isFocus ? 'focus-color' : ''}}">
      <input placeholder="输入手机号" type="number" class="form-input" bindinput="inputPhoneNumber" bindblur="checkPhoneNumberBlur" bindfocus="handleFocus" value="{{phoneNumber}}" cursor-spacing="200" auto-focus maxlength="11"/>
      <view class="btn-close" catch:tap="clearPhoneNumber" wx:if="{{phoneNumber}}">
        <image src="../../../../source/images/btn_close.png" />
      </view>
    </view>
    <view class="form-row {{isCodeFocus ? 'focus-color' : ''}}">
      <input placeholder="输入短信验证码" type="number" bindinput="inputCode" bindblur="checkAll" bindfocus="handleCodeFocus" maxlength="4" cursor-spacing="140" class="form-input" />
      <view wx:if="{{!sendCodeDisabled}}" class="btn-sendCode {{phoneNumberIsCorrect ? '' : 'sendcode-disabled'}}" bindtap="sendCode">{{sendCodeText}}</view>
      <view wx:else class="btn-sendCoding">{{sendCodeText}}s后可重发</view>
    </view>
    <view class="error-wrapper">
      <block wx:if="{{showErrorInfo}}">
        <image class="icon-error" src="/source/images/icon-warning.png"></image>
        <text class="error-info">{{errorInfo}}</text>
      </block>
    </view>
    <view class="btn-row">
      <button class="btn-sign {{submitDisabled ? 'disabled' : ''}}" bindtap="signIn">注册/登录</button>
    </view>
    <view class="tips-wrapper">
      <view bindtap="radioTapHandle" class="tips-radio">
        <radio value="r1" checked="{{isAgree}}"/>
      </view>
      <protocolLabel />
    </view>
  </view>
</view>
<protocolPopup loginType="{{loginType}}" />
<user-error-modal isShow="{{showUserErrorModal}}" isSuperVip="{{isSuperVip}}" isNavigate="{{false}}"></user-error-modal>

<common-loading />

<captcha id="comp-captcha"/>
