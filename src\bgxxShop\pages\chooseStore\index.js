// mall/pages/chooseStore/index.js
const commonObj = require('../../../source/js/common').commonObj
const coordtransform = require('../../../utils/coordUtil')
import wxappMap from '../../../service/wxappMap'
const { addressLabels, defaultCityInfo } = require('../../../utils/config')
const locateService = require('../../../utils/services/locate')
const app = getApp()
import { debounce } from '../../../utils/util'
import locateStore from '../../../stores/module/locate'
import { getPromiseObj } from '../../../utils/promise'
import sensors from '../../../utils/report/sensors'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    curTab: 'select',
    tabs: [
      { label: '选择门店', type: 'select' },
      { label: '常用门店', type: 'usually' },
    ],
    currCity: {
      cityID: '',
      cityName: '',
    },
    openPosition: true,   //开启定位
    showTips: true,
    locationList: [],
    selectedAddress: {}, // 已选地址信息
    oftenUseStore: [], // 常用门店
    nearbyStoreList: [], // 附近门店
    currStore: {}, // 当前门店
    showCityList:false, //显示门店列表
    mantle: false, //搜索框蒙层
    pageFormConfirmOrder: false, // 是否是确认订单页进来的
    nearbyWhetherHaveCurr:false,  //是否展示当前门店
    showInputContent:false,  // 是否展示选择门店的内容
    storeSelectedIdx: null,  //选中常用门店的下标
    checkStore: false, // 是否选中门店
    isIncreaseAddressPage: false, //是否从新增地址页过来
    addressList: [], // 常用地址列表
    selectedIdx: null, //  选中常用地址的下标
    checkAddress: false, // 是否选中常用地址
    isEmptyAddressId: false,  // 是否清空选中地址下标
    commonStoreList: [],
  },
  _data: {
    waitAddressIdByAddressList: {},
    // 是否手动选择过门店
    hasSelectStore: false,
    pageFrom: '',
    isSupportAddrCityVip: true, // 城市开通服务
    isSetDefaultCurStore: false, // 是否默认第一个门店作为当前门店
    initStore: {}
  },
  onLoad(options) {
    this._data.hasSelectStore = false
    this._data.asyncHomeStore = Boolean(Number(options.asyncHomeStore))
    this.setInitStoreAddress(options)
    // 展示过授权弹窗，可以进行定位；没有授权过的不走
    if (locateStore.hasLocationAuth === true) {
      this.setCurrLocate()
    }
    if (this._data.pageFrom !== 'noCityService') {
      // const addressId = wx.getStorageSync('bgxx_chooseStore_selectAddressId')
      //  没有默认选中常用地址的时候，才会请求附近的门店列表
      // if (!addressId) {
      this.getNearbyStoreList()
      // }
      this.getOftenStoreList()
    }
    if(this._data.pageFrom === 'confirmOrder') {
      this.setData({
        pageFormConfirmOrder: true,
      })
    }

  },
  async onShow() {
    this.getAddressList()
    //展示缓存地址
    const currCity = wx.getStorageSync('showCurrentCity');
    if(!!currCity){
      this.setData({
        currCity: {
          cityName:currCity.cityName,
          cityID: currCity.cityID
        },
      })
    }
    if(!app.globalData.userLocationAuth){
      this.setData({
        openPosition: false
      })
    }
    // 在切换地址页开启定位权限，返回选择门店页，重新定位
    const locationPermission = wx.getStorageSync('openLocationPermission')
    if(locationPermission){
      this.currentLocation()
    }
    sensors.pageScreenView()
  },
  onHide() {
    this.setData({
      isIncreaseAddressPage: false
    })
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  async onUnload(){
    // 在首页进入选择地址页时,同步首页门店
    this.isFromXXShop() && !this._data.hasSelectStore && this.selectStoreAuto()
  // const isEmptyAddressId = this.data.isEmptyAddressId
    wx.removeStorageSync('showCurrentCity')
    wx.removeStorageSync('openLocationPermission')
    wx.removeStorageSync('temporarySelectionAddress')
    //  当点击完重新定位后再选择清空储存下标
    // if(isEmptyAddressId){
      // wx.removeStorageSync('bgxx_chooseStore_selectAddressId')
    // }

  },
  isFromXXShop() {
    /** @type { boolean } */
    const asyncHomeStore = this._data.asyncHomeStore // 目前只有从次日达购物车页的noAddressConfirm调用,才会把asyncHomeStore设置为true
    const pages = asyncHomeStore ? [] : getCurrentPages()
    const prePage = pages[pages.length - 2] || {}
    return asyncHomeStore || prePage.route === 'bgxxShop/pages/xxshop/index/index'
  },
  /**
   * 切换tab
   */
  // 3.10.0版本统一选择门店的ui,去除顶部tab选择
  // switchTab(e){
  //   const { type } = e.currentTarget.dataset
  //   this.setData({
  //     curTab: type
  //   })
  // },
  /**
   * 设置当前定位
   */
  setCurrLocate() {
    console.log('setCurrLocate');
    const { address = ''}  = wx.getStorageSync("userCurrLoca") || {}
    this.setData({
      currentAddr: address || '定位失败，未获取定位信息'
    })
    if (!address) {
      this.locateCurrAddress(false)
    }
  },
  /**
   * 定位当前地址
   * @ params isShowLocateFailModal Boolean 是否弹未开启位置权限弹窗
   */
  async locateCurrAddress(isShowLocateFailModal = true) {
    try {
      const locationInfo = await locateService.getGeoLocation()
      await locateService.getCityName(locationInfo.latitude, locationInfo.longitude)
      const { address = ''} = wx.getStorageSync('userCurrLoca') || {}
      this.setData({
        currentAddr: address || '定位失败，未获取定位信息'
      })
    } catch (error) {
      if (!isShowLocateFailModal){
        return
      }
      const res = await locateService.handleWXLocationErr(error.errMsg)
      if (res) {
        this.currentLocation()
      }
      throw new Error()
    }
  },
  /**
   * 设置初始门店地址相关信息
   */
  setInitStoreAddress(options) {
    const { pageFrom = '' } = options || {}
    this._data.pageFrom = pageFrom
    let { selectStoreInfo = {}, selectAddressInfo = {} } = wx.getStorageSync("bgxxSelectLocateInfo") || {}
    const { cityName: currCityName = '', cityID: currCityID = '' } = wx.getStorageSync("userCurrLoca") || {}
    if (this._data.pageFrom === 'confirmOrder') {
      const { selectStoreInfo: currStoreInfo, selectAddressInfo: currAddressInfo } = app.globalData.currStoreAddressInfo || {}
      selectStoreInfo = (!currStoreInfo || !currStoreInfo.storeID) ? {} : currStoreInfo
      if (currAddressInfo && currAddressInfo.cityID) {
        selectAddressInfo = currAddressInfo
      }
      this._data.lastOftenStoreID = selectStoreInfo.oftenStore ? Number(selectStoreInfo.storeID) : null
      app.globalData.currStoreAddressInfo = null
    } else {
      if (locateStore.useFreshDefaultLocation) selectAddressInfo.address = '无定位地址'
    }
    const { cityName, cityID, isDefaultLocation } = selectAddressInfo
    const { cityName: defaultCityName, cityID: defaultCityID} = defaultCityInfo
    this._data.initCityId = cityID
    this.setData({
      currCity: {
        cityName: (cityName || currCityName) || defaultCityName,
        cityID: (cityID || currCityID) || defaultCityID
      },
      selectedAddress: selectAddressInfo,
      currStore: isDefaultLocation ? {} : selectStoreInfo, // 默认地址不显示当前门店
    })
    this._data.initStore = this.data.currStore
  },
  /**
   * 获取常用门店
   */
  getOftenStoreList() {
    if (!app.checkSignInsStatus()) return
    let { userID } = wx.getStorageSync('user') || {}
    const { cityID = '' } = this.data.selectedAddress || {}
    if (!cityID) { return }
    let param = {
      cityID,
      customerID: userID,
      isNeedSaleCount: 'Y',
    }
    app.api.loadBgxxOftenUseList(param).then(res => {
      const commonStoreList = res.data
      this.setData({
        commonStoreList,
      })
      this.handleStoreData(res.data || [], 1)
    }).catch(err => {})
  },
  /**
   * 获取附近门店
   */
  getNearbyStoreList() {
    const { lon, lat, cityCode = '' } = this.data.selectedAddress || {}
    console.log(this.data.selectedAddress)
    if (!cityCode) {
      return
    }
    const params = {
      lon: lon || -1, // 经度
      lat: lat || -1, // 纬度
      cityCode,
      isNeedScore: 'N',
      isSupportVip: 'Y'
    }
    if (this._data.pageFrom === 'confirmOrder') {
      // 确认订单要支持门店自提的门店
      params.isSupportVipTake = 'Y'
    }
    app.api.getFreshNearByStores(params).then( ({data}) => {
      this.handleStoreData(data || [])
    }).catch(() => {
      this.setData({
        isEmpty: true
      })
    })
  },
  /**
   * 处理门店数据
   * @param {Array} data 门店列表
   * @param {Number} source 1 常用门店 0 附近门店
   */
  handleStoreData(data, source) {
    let nearbyWhetherHaveCurr = this.data.nearbyWhetherHaveCurr
    const key = !!source ? 'oftenUseStore' : 'nearbyStoreList'
    let currStore = this.data.currStore || {}
    if (!!data.length) {
      const { storeID = '', } = currStore

      if (source !== 1) {
        //  筛选数组里边是否有当前门店
        for (let i = 0,len = data.length; i < len; i++) {
          nearbyWhetherHaveCurr = false
          if (data[i].storeID === storeID) {
            nearbyWhetherHaveCurr = true
            break;
          }
        }

        //  附近门店没有当前门店
        if (!nearbyWhetherHaveCurr) {
          //  常用门店
          const commonStoreList = this.data.commonStoreList
          if(commonStoreList.length) {
            //  将最新下单的门店排列在最上方
            commonStoreList.reverse()
            //  匹配常用门店是否在附近门店当中
            const arrRes = data.filter((b) => commonStoreList.some((a) => Number(a.storeID) === Number(b.storeID)))
            if(arrRes.length){
              const index = data.findIndex(store => Number(store.storeID) === Number(arrRes[0].storeID))
              data.splice(index, 1)
              nearbyWhetherHaveCurr = true
              currStore = arrRes[0]
            }
          }
          //  常用门店不在附近门店中
          if(!nearbyWhetherHaveCurr && this._data.isSetDefaultCurStore) {
            const nearbyStores = data[0]

            data.splice(0, 1)
            nearbyWhetherHaveCurr = true

            currStore = nearbyStores
          }
        }
      }
      //  当有当前门店时
      if (!!currStore.storeID) {
        //  拿到在附近门店里与当前门店相同门店的下标
        const index = data.findIndex(store => Number(store.storeID) === Number(currStore.storeID))
        if (index !== -1) {
          // 如果当前门店营业时间不存在，附近门店里面有当前门店，就给当前门店增加营业时间
          if (!currStore.openingTime || !currStore.distance) {
            const { openingTime: findOpenTime, distance } = data[index]
            findOpenTime && (currStore.openingTime = findOpenTime)
            distance && (currStore.distance = distance)
          }
          if(source !== 1 && data){
            //  截取数组内的当前门店
            data.splice(index, 1)
            nearbyWhetherHaveCurr = true
          }
        }
      }
      data = data.filter(item => item.isSupportVip === 'Y' || item.isSupportVipDelivery === 'Y')
      if (!!source) {
        // 确认订单常用门店筛选支持自提的门店
        if (this._data.pageFrom === "confirmOrder") {
          data = data.filter(item => item.isSupportVip === 'Y')
          this.getOftenUseDistance(data)
        }
        // 常用门店截取
        data = data.slice(0, 3)
        // data = data.slice(-1)
      }
    }else if( source !== 1 ) {
      nearbyWhetherHaveCurr = false
    }
    const lastOftenStore = source ? data.findIndex(v => Number(v.storeID) === this._data.lastOftenStoreID) : -1
    this.setData({
      [key]: data,
      currStore,
      nearbyWhetherHaveCurr,
      ...(source ? { storeSelectedIdx: lastOftenStore === -1 ? null : lastOftenStore } : { isEmpty: data.length === 0 })
    })
  },
  // 我的地址
  async getAddressList() {
    // 未登录不请求我的地址
    if (!app.checkSignInsStatus()) return
    const userID = app.globalData.customerID || -1
    try {
      const [res, selectAddressIdByAddressList] = await Promise.all([
        app.api.getBgxxAddressList(userID),
        this._data.waitAddressIdByAddressList.promise || Promise.resolve(''),
      ])
      if (!(res && res.data)) {
        return
      }
      let addAddressId
      let addressList = this.data.addressList
      const resData = res.data || []
      if(this.data.isIncreaseAddressPage){
        const arrRes = res.data.filter((b) => !addressList.some((a) => a.addressId === b.addressId))
        addAddressId = arrRes[0].addressId
      }

      // 解密及格式化处理手机号
      addressList = resData
      const addressLabelMap = {};
      addressLabels.forEach(item => {
        if (!addressLabelMap[item.labelName]) {
          addressLabelMap[item.labelName] = item;
        }
      })
      addressList.forEach(item => {
        item.labelObj = addressLabelMap[item.label] || {};
      })
      let { selectAddressInfo = {} } = wx.getStorageSync("bgxxSelectLocateInfo") || {}
      const { addressId: lastSelesctedAddressId } = selectAddressInfo
      //  拿新增地址页新增地址的下标或者上次点击选择地址的下标
      const addressId = addAddressId || selectAddressIdByAddressList || lastSelesctedAddressId
      if (addressId){
        const index = addressList.findIndex(address => String(address.addressId) === String(addressId))
        //  将新增的地址置换到第一项
        if (index !== -1) {
          //  如果点击添加新地址后新增完地址返回直接选中第一个
          const chooseAddress = addressList[index]
          addressList.splice(index, 1)
          addressList.unshift(chooseAddress)
          this.setData({
            addressList,
            selectedIdx: 0,
            lastSelesctedAddressId: addressId
          })
          this.clickReceivingAddress(0)
        } else {
          this.setData({
            selectedIdx: -1,
            lastSelesctedAddressId: '',
          })
        }
      }
      this.setData({
        addressList
      })

    } catch (e) {
      console.log(e)
    }
  },
  // 点击新增地址
  addReceivingAddress () {
    if (app.checkSignInsStatus()) {
      // 用户已登录
      wx.navigateTo({
        url: `/bgxxUser/pages/address/addAddress/index`,
      })
    } else {
      app.signIn()
    }
    sensors.clickReport({
      blockCode: '00',
      element_name: '新增地址',
      element_code: '1140600007'
    })
  },

  clickReceivingContent(e) {
    //  接收下标
    const { index = '', address } = e.currentTarget.dataset
    console.log(e)
    this.clickReceivingAddress(index)
    this.setData({
      isEmptyAddressId: false,
      lastSelesctedAddressId: address.addressId
    })
    sensors.clickReport({
      blockCode: '00',
      element_name: '常用地址',
      element_code: '1140600004'
    })
  },
  /**
   * 选中收获地址
   */
  async clickReceivingAddress(index) {
    //  addressList地址数组
    const addressList = this.data.addressList
    const receivingAddress = addressList[index]
    // if (selectedIdx === index) return
    try {
      const { cityID, cityName, lat, lon, address, gisAddress,addressId } = receivingAddress
      const params = {
        cityID,
        cityName,
        lat,
        lon,
        address: `${gisAddress}`,
        addressId
      }
      await this.handleAddress(params)
      // 当前选择地址有开通城市服务则选中
      if(this._data.isSupportAddrCityVip) {
        this.setData({
          selectedIdx: index,
          addressId,
          checkAddress:true
        })
      }
    } catch (error) {
      console.log(error)
    }
  },
  async handleAddress(params) {
    try {
      const { cityName, lat, lon } = params
      const res = await app.api.checkBgxxIsSupportVip({
        cityName,
        lat,
        lon
      })
      const { city, supportSuperVipShop = 'N' } = res.data || {}
      if (supportSuperVipShop === 'Y' && !!city.cityID) {
        const { deliveryCenterCode, cityCode, cityID } = (city || {})
        const selectAddressInfo = {
          ...params,
          cityID,
          supportSuperVipShop: 'Y',
          deliveryCenterCode,
          cityCode
        }
        this.setData({
          selectedAddress: selectAddressInfo,
          inputValue: ''
        })
        this.refreshCityInfo(params)
        this._data.isSupportAddrCityVip = true
        await this.getOftenStoreList()
        setTimeout(() =>{
          this._data.isSetDefaultCurStore = true
          this.getNearbyStoreList()
        }, 100)
        return true
      } else {
        this._data.isSupportAddrCityVip = false
        //加定时器，兼容ios系统手机
        setTimeout(() => {
          app.showModalPromise({
            content: '当前城市未开通服务，请切换其他城市',
            confirmText: '我知道了',
          })
        }, 300)
        return false
      }
    } catch (error) {
      return false
    }
  },
  /**
   * 重新定位
   */
  async currentLocation() {
    try {
      await this.locateCurrAddress()
      const { cityName = '', location: { lng = '', lat = ''} = {}, address = '' } = wx.getStorageSync('userCurrLoca') || {}
      const bdLocation = coordtransform.gcj02tobd09(lng, lat) || []
      const params = {
        cityName,
        lat: bdLocation[1] || -1,
        lon: bdLocation[0] || -1,
        address,
      }
      await this.handleAddress(params)
      this.setData({
        selectedIdx: null,
        isEmptyAddressId: true,
      })
    } catch (error) {
      this.setData({
        currentAddr: '定位失败，未获取定位信息'
      })
    }
    sensors.clickReport({
      blockCode: '00',
      element_name: '重新定位',
      element_code: '1140600003'
    })
  },
    /**
   * 输入框聚焦
   */
  inputFocus() {
    this.setData({
      showAddrList: true,
      mantle: true
    })
    setTimeout(() => {
      this.setData({
        showTips: false,
      })
    }, 2000)
    sensors.clickReport({
      blockCode: '00',
      element_name: '搜索框',
      element_code: '1140600002'
    })
  },
    /**
   * 监听地址输入框
   */
  inputChange(e) {
    this.setData({
      inputValue: e.detail.value,
      //  当输入框有输入时搜索结果展示其他内容隐藏
      showInputContent:true,
    })
    this.qqmapsdkSelect(this.data.inputValue)
  },
  qqmapsdkSelect: debounce( async function (inputAddr) {
    const res = await wxappMap.getSuggestion({
      keyword: inputAddr,
      region: this.data.currCity.cityName,
    })
    res.data.forEach((item)=>{
      item.showTitle = item.title.replace(inputAddr,`<span style="color:rgba(0, 163, 79, 1);">${inputAddr}</span>`)
    })
    this.setData({
      locationList: res.data,
      mantle: !inputAddr,
    })
  }, 300),

  // 选择城市
  chooseCity() {
    this.setData({
      showCityList: true
    })
    sensors.clickReport({
      blockCode: '00',
      element_name: '城市',
      element_code: '1140600001'
    })
  },
  // 选择地址
  async chooseLocation(e) {
    const { title = '', location: { lng = '', lat = ''} = {}, city = '', ad_info = {} } = e.currentTarget.dataset.item
    const bdLocation = coordtransform.gcj02tobd09(lng, lat) || []
    const params = {
      cityName: city || ad_info.city,
      lat: bdLocation[1] || -1,
      lon: bdLocation[0] || -1,
      address: title,
    }
    try {
      await this.handleAddress(params)
      this.setData({
        locationList:[],
        selectedIdx: null,
        showInputContent:false,
      })
    } catch (error) {}
    this.hideLocation()
  },
  // 隐藏地址
  hideLocation() {
    this.setData({
      showAddrList: false
    })
  },
    /**
   * 清除地址输入框信息
   */
  clearSearchMes() {
    //加定时器，兼容iphone6s手机偶现点击清除之后关键词还存在的情况
    setTimeout(() =>{
      this.setData({
        showAddrList: false,
        inputValue: '',
        showInputContent:false,
        locationList:[],
      })
    }, 500)
  },
  // // 清除地址
  // clearSearch() {
  //   this.setData({
  //     showAddrList: false,
  //     inputValue: ''
  //   })
  // },
  /**
   * 选择当前门店
   */
  chooseCurrStore() {
    if (this._data.pageFrom !== 'confirmOrder') {
      app.changeBgxxCityChangeStatus(true)
    } else {
      this.selectStoreAuto()
    }
    wx.navigateBack()
    this.clickNearStoreReport()
  },
  /**
   * 选择门店点击事件
   */
  chooseStore(e){
    const { index = '', oftenStore } = e.currentTarget.dataset
    const { storeID, storeName, address, isSupportVip, isSupportVipDelivery, distance, openingTime, lat, lon, shortName, cityID, storeCode, startTime, endTime, number, isStarStore, phone } = e.currentTarget.dataset.address || e.detail
    const selectStoreInfo = {
      storeID,
      storeName: shortName || storeName,
      address,
      cityID,
      openingTime,
      lat,
      lon,
      isSupportVip,
      isSupportVipDelivery,
      distance,
      storeCode: storeCode || number || '', // number是为了兼容选择常用门店number就是门店编码的问题
      startTime,
      endTime,
      oftenStore,
      isStarStore,
      phone
    }
    this.setData({
      selectedIdx: void 0,
      storeSelectedIdx: index
    })
    this.handlechooseStore(selectStoreInfo, true)
    this.clickNearStoreReport()
  },
  /**
   * 点击附近门店上报神策
   */
  clickNearStoreReport() {
    sensors.clickReport({
      blockCode: '00',
      element_name: '点击附近门店去下单',
      element_code: '1140600006'
    })
  },
  /**
   * 处理选择门店数据
   */
  async handlechooseStore(selectStoreInfo,isReturn) {
    this._data.hasSelectStore = true
    let bgxxSelectLocateInfo = {
      selectAddressInfo: this.data.selectedAddress,
      selectStoreInfo
    }
    if (this._data.pageFrom === 'confirmOrder') {
      const pagesTracks = getCurrentPages()
      const prepage = pagesTracks[pagesTracks.length - 2]
      if (prepage && prepage.modifySelfTakeStore) {
        prepage.modifySelfTakeStore(bgxxSelectLocateInfo)
      }
    } else {
      // 选择了门店，则首页不再展示无定位地址
      if(bgxxSelectLocateInfo.selectAddressInfo) {
        bgxxSelectLocateInfo.selectAddressInfo.isDefaultLocation = false
        this.setData({
          checkStore:true
        })
        // this.checkAddressStore()
      }
      const { storeCode: curStoreCode } = this.data.currStore
      app.changeBgxxCityInfo(bgxxSelectLocateInfo, {
        // 进入页面是A城市,切换到B城市,默认选中了一个门店(此时会把城市同步到首页,但是此时首页未显示,不执行刷新数据逻辑)
        // 若此时再手动选中另一家门店,此时,缓存中的城市已经是B城市,会导致mallCityChange变为false
        // 最终导致首页不刷新
        // 3.14.0切换门店就会刷新
        // cityHasChange: String(this._data.initCityId) !== String(bgxxSelectLocateInfo.selectAddressInfo.cityID)
        cityHasChange: String(curStoreCode) !== String(selectStoreInfo.storeCode)
      })
    }
    app.getBgxxStoreInfo(bgxxSelectLocateInfo)
    isReturn && wx.navigateBack()
  },
  /**
   * 城市列表切换当前城市
   */
  refreshCityInfo(city) {
    const { cityID = '', cityName = ''} = city
    this.setData({
      currCity: {
        cityID,
        cityName
      }
    })
  },
  /**
   * 跳转切换地址页
   */
  goToChangeAddress() {
    const { selectAddressInfo = {} } = wx.getStorageSync("bgxxSelectLocateInfo") || {}
    wx.navigateTo({
      url: `/bgxxUser/pages/address/addressList/index?from=homeAddressList&addressId=${this.data.lastSelesctedAddressId || -1}`
    })
    sensors.clickReport({
      blockCode: '00',
      element_name: '更多地址',
      element_code: '1140600005'
    })
  },
  //选择城市
  changeCityPopup(cityInfo){
    console.log('选择城市成功----',cityInfo)
    this.refreshCityInfo(cityInfo.detail);
  },
  //关闭选择尝试弹窗
  closePopup(){
    this.setData({
      showCityList:false
    })

  },
  //  点击蒙层取消蒙层展示
  greyCoating(){
    this.setData({
      mantle:false,
      showAddrList: false
    })
  },
  async getOftenUseDistance(storeList) {
    const distanceMap = {}
    const { lat: curLat, lon: curLon} = this.data.selectedAddress
    storeList.forEach( async item => {
      const distance = commonObj.getLineDistance(curLat, curLon, item.lat, item.lon)

      distanceMap[item.id] = distance
      this.setData({
        distanceMap
      })
    })
  },
  /**
   * 处理首页选择地址页地址展示
   */
  checkAddressStore(){
    const { addressId , checkAddress ,checkStore } = this.data
    if(checkAddress && checkStore){
      wx.setStorageSync('bgxx_chooseStore_selectAddressId', addressId)
    }
  },
  /**
   * 更多地址点击事件
   * @param {*} e
   */
  async selectAddress(e) {
    this._data.waitAddressIdByAddressList = getPromiseObj()
    const { lon, lat, addressId, cityId, cityName, gisAddress} = e.currentTarget.dataset
    //  常用地址列表
    const addressList = this.data.addressList
    //  更多地址中某个选中地址的下标
    const index = addressList.findIndex(address => address.addressId === addressId)
    const params = {
      cityId,
      cityName,
      lat,
      lon,
      address: `${gisAddress}`,
      addressId
    }
    //  请求该地址附近的门店
    const checkResult = await this.handleAddress(params)
    //  如果不在选择地址页常用地址列表的展示当中的话，就不选中
    this._data.waitAddressIdByAddressList.resolve(checkResult ? addressId : '')
    checkResult && this.setData({
      selectedIdx: index || -1,
    })
  },
/**
 * 同步选择地址后的当前门店至首页
 */
  selectStoreAuto(){
    const { storeID, storeName, address, isSupportVip, isSupportVipDelivery, distance, openingTime, lat, lon, shortName, cityID, storeCode, startTime, endTime, isStarStore, phone } = this.data.currStore
    const { storeID: initStoreID } = this._data.initStore
    // 门店未发生变化不同步数据
    if (!storeID || storeID === initStoreID) {
      return
    }
    const selectStoreInfo = {
      storeID,
      storeName: shortName || storeName,
      address,
      cityID,
      openingTime,
      lat,
      lon,
      isSupportVip,
      isSupportVipDelivery,
      distance,
      storeCode: storeCode || '',
      startTime,
      endTime,
      isStarStore,
      phone
    }

    this.handlechooseStore(selectStoreInfo,false)
  }
})
