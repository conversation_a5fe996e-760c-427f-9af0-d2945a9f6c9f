<nav-bar
  normal-back
  bg-color-duration="0.3"
  with-placeholder-height="{{false}}"
  background="{{navBar.background}}"
  nav-bar-title="{{navBar.title}}"
  bind:navBarHeight="getNavBarHeight"
/>
<view wx:if="{{loading.page}}" class="gift-card-send-scroll">
  <common-loading />
</view>
<block wx:else>
  <view wx:if="{{!hideGiftCardCover}}" class="gift-card-cover {{giftCardCoverHiding ? 'gift-card-cover--hiding' : ''}}" style="--placeholder-top:{{placeholder.top}}px;" bind:animationend="onGiftCardCoverHide">
    <image class="gift-card-cover-image" src="https://resource.pagoda.com.cn/dsxcx/images/7274d8c06e93357de2a811779fb6a89b.png" mode="aspectFit"></image>
    <view class="gift-card-giver">
      <image class="gift-card-giver-headimg" mode="aspectFill" src="{{senderInfo.headImg}}"></image>
      <view class="gift-card-giver-phone">{{senderInfo.phone}}</view>
    </view>
    <view class="gift-card-cover-title">这是我精心为你准备的礼物！</view>
    <view class="gift-card-cover-btn">
      <button class="gift-card-submit-btn" catch:tap="onHideGiftCardCover">查看礼物</button>
    </view>
  </view>
  <scroll-view wx:else scroll-y="{{hideGiftCardCover}}" class="gift-card-send-scroll">
    <view class="gift-card-send-scroll-placeholder" style="height:{{placeholder.top}}px;"></view>
    <block wx:if="{{!sendInfo.isSelfSend && (OperateTypeNum[2] === sendInfo.status || OperateTypeNum[3] === sendInfo.status || (OperateTypeNum[4] === sendInfo.status && !sendInfo.isSelfReceive))}}">
      <view class="gift-card-error">
        <image class="gift-card-error-image" src="https://resource.pagoda.com.cn/dsxcx/images/9df34f1a53f848f5072ddb7fb0155639.png" mode="aspectFit"></image>
        <view class="gift-card-error-info">
          <view wx:if="{{OperateTypeNum[4] === sendInfo.status}}" class="gift-card-error-text">该礼品卡已被领取</view>
          <view wx:elif="{{OperateTypeNum[2] === sendInfo.status}}" class="gift-card-error-text">该礼品卡已取消赠送</view>
          <view wx:elif="{{OperateTypeNum[3] === sendInfo.status}}" class="gift-card-error-text">该礼品卡已超时</view>
          <view class="gift-card-error-text">无法领取~</view>
        </view>
        <view class="gift-card-error-btn">
          <button class="gift-card-submit-btn" bind:tap="toHomePage">去逛逛</button>
        </view>
      </view>
    </block>
    <view wx:else class="gift-card-send">
      <block wx:if="{{sendInfo.isSelfSend}}">
        <card-section
          wx:if="{{OperateTypeNum[1] === sendInfo.status}}"
          card-info="{{cardInfo}}"
          title="赠送中"
          desc="好友未领取丨{{expireTimeCountDown}} 后自动取消 "
          icon="clock" />
        <card-section
          wx:if="{{OperateTypeNum[2] === sendInfo.status}}"
          card-info="{{cardInfo}}"
          title="已取消"
          desc="您已取消赠送"
          icon="fail" />
        <card-section
          wx:if="{{OperateTypeNum[3] === sendInfo.status}}"
          card-info="{{cardInfo}}"
          title="已取消"
          desc="超时未被领取，赠送已自动取消"
          icon="fail" />
        <card-section
          wx:if="{{OperateTypeNum[4] === sendInfo.status}}"
          card-info="{{cardInfo}}"
          title="好友已领取"
          icon="success" />
      </block>
      <view wx:else class="gift-card-detail">
        <card-base card-info="{{cardInfo}}" />
      </view>
      <view class="gift-card-send-form">
        <view class="gift-card-send-body">
          <view wx:if="{{sendInfo.isSelfSend}}" class="gift-card-send-title">赠言</view>
          <card-wish
            received="{{OperateTypeNum[4] === sendInfo.status && !sendInfo.isSelfSend}}"
            sender-info="{{sendInfo.isSelfSend ? false : senderInfo}}"
            wish="{{sendInfo.wish}}"/>
        </view>
      </view>
    </view>
    <view class="gift-card-send-scroll-placeholder" style="height:{{placeholder.bottom}}px;"></view>
  </scroll-view>
  <view class="gift-card-bottom">
    <block wx:if="{{sendInfo.isSelfSend}}">
      <view class="gift-card-submit-btn-list">
        <block wx:if="{{OperateTypeNum[1] === sendInfo.status}}">
          <button class="gift-card-submit-btn gift-card-submit-btn-plain" hover-class="none" bind:tap="showCancelSend">{{loading.cancelSend ? '取消中...' : '取消赠送'}}</button>
          <button wx:if="{{loading.refresh}}" class="gift-card-submit-btn" hover-class="none">分享好友领取</button>
          <button wx:else class="gift-card-submit-btn" hover-class="none" open-type="share" bind:tap="showSubscribe">分享好友领取</button>
        </block>
        <button wx:elif="{{OperateTypeNum[4] === sendInfo.status}}" class="gift-card-submit-btn" hover-class="none" bind:tap="toGiftCardList">我的礼品卡</button>
        <button wx:else class="gift-card-submit-btn" hover-class="none" bind:tap="toGiftCardDetail">订单详情</button>
      </view>
    </block>
    <block wx:else>
      <block wx:if="{{OperateTypeNum[1] === sendInfo.status}}">
        <view wx:if="{{!loading.receiving}}" class="gift-card-status">
          <view class="gift-card-status-text">{{expireTimeCountDown}}后未领取自动退回对方账户</view>
        </view>
        <button class="gift-card-submit-btn" hover-class="none" bind:tap="receiveCard">{{loading.receiving ? '领取中...' : '领取礼品卡'}}</button>
      </block>
      <button wx:elif="{{OperateTypeNum[4] === sendInfo.status && sendInfo.isSelfReceive}}" class="gift-card-submit-btn" hover-class="none" bind:tap="toGiftCardDetail">查看礼品卡</button>
    </block>
  </view>
  <share-canvas wx:if="{{sendInfo.isSelfSend}}" cover-image="{{cardInfo.cover}}" bind:imageLoaded="onShareImageLoaded" />
</block>
<popup is-show-popup="{{showCancelPopup}}" popup-title="取消赠送" show-close="{{false}}">
  <image class="gift-card-popup-image" mode="aspectFill" src="https://resource.pagoda.com.cn/dsxcx/images/f0d8bf73786298e816309c990c7f6871.png" />
  <view class="ift-card-popup-dialog-text">是否确定取消赠送该礼品卡?</view>
  <view class="gift-card-bottom gift-card-bottom-static gift-card-send-padding-bottom">
    <view class="gift-card-submit-btn-list">
      <button class="gift-card-submit-btn gift-card-submit-btn-plain" hover-class="none" data-type="cancel" bind:tap="onCancelPopupTap">暂不取消</button>
      <button class="gift-card-submit-btn" hover-class="none" data-type="confirm" bind:tap="onCancelPopupTap">确认取消</button>
    </view>
  </view>
</popup>
<request-subscribe custom-nav title="打开提醒，获得礼物领取通知" show="{{subscribe.show}}" tmpl-ids="{{subscribe.tmplIds}}" bind:close="onSubscribeClose" />