<!--index.wxml-->
<wxs module="filter" src="../../utils/common.wxs"></wxs>
<custom-tab-bar tab-index="{{accountPageIndx}}" />
<main-page currentView="content" />
<view class="container safe-area-inset-bottom">
  <!--登录部分 -->
  <view class="login {{superVipStatus != 'C' ? 'login-vip' : ''}}" style="--status-bar-height:{{statusBarHeight}}">
    <view class="btnToLogin" hover-class="none" >
      <!--个人信息-->
      <view class='user-info'>
        <!--个人信息头部-->
        <view class="user-info-header">
          <!-- 头像 -->
        <view class="user-info-avatar" bindtap='navigateModifyUserInfo'>
            <image class="user-avatar" src="{{!isLogin ? defaultUnLoginAvatar : imgUrl}}"></image>
          </view>
          <view class='user-info-base'>
            <view class="user-name-box">
              <view class="user-name" bindtap="navigateModifyUserInfo">
                <text class="user-name-txt">{{!isLogin ? '请登录' : userName}}</text>
                <!--<view>立即开启水果之旅</view>-->
              </view>
            </view>
          </view>
        </view>
      </view>

      <!--账户信息-->
      <view class="vip-info" wx:if="{{isLogin}}">
        <view class="info" catchtap="navigateDuiba" data-from="0">
          <view class="info-detail">
            <text class="info-detail2">{{balanceInfo.integralAmount || 0}}</text>
          </view>
          <view class="member-info">积分商城</view>
        </view>
        <view class="info" catchtap="navigateToCoupon">
          <view class="info-detail">
            <text class="info-detail2">{{balanceInfo.couponsAmount || 0}}</text>
            <!-- <view class='member-info-icon' wx:if='{{balanceInfo.expireSoonNum}}'>
              {{balanceInfo.expireSoonNum}}张券即将过期
            </view> -->
            <view wx:if='{{balanceInfo.expireSoonNum && !isClickedCouponDot}}' class="dot"></view>
          </view>
          <view class="member-info">卡券</view>
        </view>
        <view class="info " catchtap="navigateToDeposit">
          <view class="info-detail">
            <text class="info-detail2">{{filter.formatPrice(balanceInfo.walletAmount) || 0}}</text>
            <view class='member-info-icon' wx:if='{{balanceInfo.activityTitleName}}'>
              {{balanceInfo.activityTitleName}}
            </view>
          </view>
          <view class="member-info">钱包·充值</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单模块 -->
  <view class="box order-box {{!isLogin ? 'nologin-order-box' : ''}}">
    <view class="box-header">
      <view class="box_left">
        <text class="box_word">我的订单</text>
      </view>
      <view class="box_right" bindtap="toOrderList">
        <text class="box_word--right">全部订单</text>
        <image class="right_arrow" src="/source/images/arrow-right-gary.png"></image>
      </view>
    </view>
    <view class="box-content order-info">
      <view class="order-list">
        <view
          wx:for="{{orderSortList}}"
          class="order-sort"
          wx:key="index"
          data-type="{{item.type}}"
          data-sensors-key="{{item.sensorsKey}}"
          data-report-click="{{item.reportClick}}"
          bindtap="toOrderTab">
          <image class="order-pic" src="{{item.orderIcon}}"></image>
          <view class="order-text">{{item.label}}</view>
          <view class="order-count text-hidden {{item.count > 99 ? 'order-count-more' : 'order-count-normal'}}" wx:if="{{item.count}}">{{item.count > 99 ? '99+' : item.count }}</view>
        </view>
      </view>
      <!-- 订单流转条幅 -->
      <order-bar class="{{isShowOrderBar ? 'unself-order' : ''}}" bind:showOrderBar="showOrderBar"></order-bar>
    </view>
  </view>

  <!-- 任务 -->
  <view class="box" wx:if='{{isLogin && taskList.length && showTaskMode}}'>
    <view class="box-header">
      <view class="box_left">
          <text class="box_word">做任务，赚积分</text>
      </view>
      <view class="box_right" bindtap="toTaskCenter">
          <text class="box_word--right">领取积分</text>
          <image class="right_arrow" src="/source/images/arrow-right-gary.png"></image>
      </view>
    </view>
    <view class="box-content notice">
      <view class="task-list">
        <block wx:for="{{taskList}}" wx:key="index">
          <view class="task">
            <image class="task-icon {{ item.status === 'N' || item.status === 'F' ? 'task-icon-red' : '' }}" bindtap="toTaskCenter" src="{{ picUrl + '/' + item.icon}}"></image>
            <text class="task-txt" bindtap="toTaskCenter">{{item.name}}</text>
            <text
              class="task-btn task-btn--sign {{item.status === 'Y' ? 'task-btn-finish' : ''}}"
              data-status="{{item.status}}"
              data-path="{{item.openValue}}"
              data-type="{{item.openType}}"
              data-index="{{index}}"
              bindtap="onTaskBtnTap">{{item.buttonName}}
            </text>
          </view>
        </block>
      </view>
      <!-- 通知 -->
      <swiper
        class="advices"
        wx:if="{{isLogin && notifyList && notifyList.length}}"
        vertical='true' autoplay="true"  circular="true"
        display-multiple-items='1' autoplay interval="4500" duration="500"
        bindchange="onNotifychange">
        <swiper-item class="advice-item" wx:for="{{notifyList}}" wx:key="index">
          <view style="width:100%"
            bindtap="onNotifyTap"
            data-notifytype="{{item.notifyType}}"
            data-path="{{item.path}}"
            wx:if="{{index === notifyCurIndex}}"
            catchtouchmove="forbidSwiperScroll"
          >
            <v-notice-bar
              text="{{item.notifyMsg}}"
              multipleTexts="0"
              delay="{{500}}"
              color="#FF9D00"
            >
              <view slot="left">
                <image class="advice-icon" src="/source/images/advice_icon.png"></image>
              </view>
            </v-notice-bar>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>

  <!--运营位可配置  -->
  <view class="operateDefine" wx:if="{{bannerList && bannerList.length}}">
    <view class="banner-swiper">
      <block wx:for="{{bannerList}}" wx:key="index">
        <image src="{{picUrl + item.pic}}" class="swiper-item-image" bindtap="onBannerTap" data-item="{{item}}" data-index="{{index}}" />
      </block>
    </view>
  </view>
  <!-- 积分兑好礼 -->

  <!-- 我的服务 -->
  <view class="box">
    <view class="box-header">
      <view class="box_left">
          <text class="box_word">我的服务</text>
      </view>
      <view class="box_right">
      </view>
    </view>
    <view class="box-content">
      <view class='other-tools'>
        <navigator data-sensors-key="1200_120000007" class='tool' url='/pages/topic/index?homeDeliveryObj={"activityID": 1737003}' bind:complete="topicPageSensor" hover-class="none">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/4c2b69e43d4308d6a986c81d870cfb94.png"></image>
          <view>礼品卡</view>
        </navigator>
        <view class='tool'  bindtap='navigateToPage' data-type="eduAuth">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/2a134f3c2eeb3041644f2a1e1e0197f3.png"></image>
          <view>学生有礼</view>
        </view>
        <view class='tool' bindtap='navigateToTryEat'>
          <view class="dot" wx-if="{{showTryEatTip}}"></view>
          <image src="https://resource.pagoda.com.cn/dsxcx/images/beb23d483e90f8316bbc13f07a7bfa41.png"></image>
          <view>试吃中心</view>
        </view>
        <navigator data-sensors-key="1200_120000012" class='tool' url='/pages/topic/index?homeDeliveryObj={"activityID": 20297}' bind:complete="topicPageSensor" hover-class="none">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/af99169e34617f92c6734a7958cccef1.png"></image>
          <view>福利中心</view>
        </navigator>
        <!-- 注释拼团入口，但保留代码，方便后续需要下拼团单可以放开，如完全无必要时再删掉标签及逻辑代码及图片资源
        <view class='tool' bindtap='navigateToGroupList'>
          <image src="/source/images/service_group.png"></image>
          <view>超值拼团</view>
        </view>
        -->
        <navigator data-sensors-key="1200_120000020" class='tool' url='/pages/topic/index?homeDeliveryObj={"activityID": 19309}' bind:complete="topicPageSensor" hover-class="none">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/93710934ef9efdd9420f3cf12ad8576e.png"></image>
          <view>企业团购</view>
        </navigator>
        <!-- <view class='tool' data-index="0" bindtap='navigateTo' wx:if="{{ scanCodeBuyToolVisible }}">
          <image src="/source/images/scan_code_buy.png"></image>
          <view>扫码购</view>
        </view>
        <view class='tool' data-index="1" bindtap='navigateTo' wx:if="{{ scanCodeBuyToolVisible }}">
          <image src="/source/images/scan_code_buy_order.png"></image>
          <view>扫码购订单</view>
        </view> -->
        <!-- <navigator class='tool' url="../../bgxxUser/pages/cycleBuy/index">
          <image src="/source/images/mine_icon_cycle-buy.png"></image>
          <view>周期购订单</view>
        </navigator> -->
        <navigator data-sensors-key="1200_120000021" class='tool' style="background-color:#fff; opacity: 1;" url='/pages/topic/index?homeDeliveryObj={"activityID": 13814}' bind:complete="topicPageSensor" hover-class="none">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/bcb4c3b9df7b015d69e14ef75ca59bb5.png"></image>
          <view>加盟申请</view>
        </navigator>
        <view class='tool' bindtap='navigateToPage' data-type="invoiceService">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/b36af21315f3b627aadd155fb4eac409.png"></image>
        <view>开具发票</view>
        </view>
        <view class='tool' bindtap='navigateToPage' data-type="addressManage">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/5a87aec43531ccb5e0d4831641f05bf1.png"></image>
          <view>收货地址</view>
        </view>
        <view class='tool' bindtap='navigateToPage' data-type="collectStore">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/727f410dcd7bc3dc3cc0dfeb98486f98"></image>
          <view>收藏门店</view>
        </view>
        <view class='tool' bindtap='navigateToPage' data-type="serviceHelp">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/6a843bec136f4e7f138ee07fcf82f433.png"></image>
          <view>客服帮助</view>
        </view>
        <view class='tool' bindtap='navigateToPage' data-type="storeFeedback">
          <view class="tool-image">
            <view wx:if="{{isShowFeedbackRedDot}}" class="tool-red-dot"></view>
            <image class="tool-image-body" src="https://resource.pagoda.com.cn/dsxcx/images/573bab15ae0a05979be05fa90dd85da8.png"></image>
          </view>
          <view>意见反馈</view>
        </view>
        <view class='tool'  bindtap='navigateToPage' data-type="selfService">
          <image src="https://resource.pagoda.com.cn/dsxcx/images/75d5340020f5192589a57a6917b5e172.png"></image>
          <view>设置</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 关注公众号组件 -->
  <official-account class="official"></official-account>
  
</view>

<view class="prevent-screen" hidden="{{!prevent}}"></view>
<!-- 弹窗 -->
<view class="modal-mask" wx:if="{{ modalVisible }}" catchtap="closeModal">
  <view class="modal-container" animation="{{ modalAnimationData }}">
    <image src="{{ picUrl+modalObj.picUrl }}" class="modal-img" data-item="{{modalObj}}" catchtap="modalNavigateTo"></image>
  </view>
  <image src="/source/images/btn_close.png" class="modal-close-btn" animation="{{ modalAnimationData }}"></image>
</view>
<!-- 新注册用户礼包弹窗 -->
<new-customer-layer coupon="{{couponData}}"></new-customer-layer>
<request-subscribe title="打开提醒，获得优惠券过期提醒" show="{{subscribe.show}}" tmpl-ids="{{subscribe.tmplIds}}" with-tab-bar custom-nav bind:close="afterTicketEntrySubscribe" />


<confirm-modal id="globalModal" globalModalInfo="{{globalLocateModalInfo}}"></confirm-modal>

<common-loading />
