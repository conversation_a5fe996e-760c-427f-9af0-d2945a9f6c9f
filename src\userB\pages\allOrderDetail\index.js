// 该页面包括  及时达订单详情  与  门店订单详情
var commonObj = require('../../../source/js/common').commonObj;
const { setTime, splitTrans, textTransf } = require('../../../utils/deliveryTime.js')
const wxbarcode = require.async('../../../sourceSubPackage/barcode/index.js');
import util, { getStoreBusinessTime, formatPrice } from '../../../utils/util'
import { addMinus, handleGoodsItemPrice, traverseGoodsInfoList } from '../orderLogic/orderPrice';
import { addBtn, liveBtnHandle, recordBtnHandle } from '../orderLogic/storeRecord';
import { customerSubTypeEnum } from "../../../userB/pages/orderLogic/customer";
import { pageTransfer } from '~/utils/route/pageTransfer';
import { SUB_TYPES } from '~/source/const/market';
import { getPromiseObj } from '~/utils/promise';
// import { isScanQRCode } from '../../../pages/homeDelivery/utils/forwardNavigate';
const loginMixin = require('../../../mixins/loginMixin');
const locateService = require('../../../utils/services/locate');
const app = getApp();
let bg_order_close = 'https://resource.pagoda.com.cn/group1/M21/81/2D/CmiLkGIvTh-AY-2GAAAZ0wB3DfQ586.png'

//  配送中图片状态对象
const deliveryPNG = {
  //  配送中，开启定位
  locationAuth: 'https://resource.pagoda.com.cn/group1/M21/90/20/CmiLkGJ85YKAHnEsAABMrM96lqw101.png',
  //  配送中，未开启定位
  unLocationAuth: 'https://resource.pagoda.com.cn/group1/M21/80/CC/CmiWa2IvTlKAdK29AAAkSXWA76w537.png'
}

// 配送费-D、包装费-P、餐盒费-B、贺卡费用-H

let bg_order_delivery = deliveryPNG.unLocationAuth;
let bg_order_success = 'https://resource.pagoda.com.cn/group1/M21/81/2D/CmiLkGIvTmKANsILAAAXractQQ0923.png';
let bg_order_wait_pay = 'https://resource.pagoda.com.cn/group1/M21/80/CC/CmiWa2IvTnmAYtrDAAAh4iSE5fM489.png';
var coordtransform = require('../../../utils/coordUtil')
var ct = 0;
var timeDown = null;
var countDownIsRun = false;
const orderShow = require('../../../common/orderBtn')
const { B2CDesc, timelyDesc } = require('../orderLogic/orderDetailDesc')
const { refundInfo } = require('../orderLogic/refundInfo')
const sensors = require('../../../utils/report/sensors')
const orderMixin = require('../orderLogic/orderMixin')
const navigateToH5Mixin = require('../../../mixins/navigateToH5Mixin')
const { baseUrl } = require('../../../utils/config')
const {
  addPaymentInfoToItems,
  dealLabel,
  setConsumablesAvatar,
  addGoodsSnMap,
  handleReceiveTime,
  handleOrderGoodsName,
  addB2CDeliveryTime,
  mapSnapShotDetail
} = require('../orderLogic/orderGoods')

const act = require('../../../utils/activity')
const { wrapSensorReportFn } = require('../../../utils/report/index')
const { genClickSensor } = require('../../../utils/report/index')
const { clickParamsMap } = require('./sensorClickParams')

const clickSensorParamsFn = genClickSensor(clickParamsMap)
const { QUESTION_ICON } = require('../../../source/const/goodsImage')

const orderSourceMap = {
  A: 'wxMiniOrder',
  F: 'b2cOrder'
}

// 原有及时达按钮逻辑
// isAllowRefund 是否允许退款
// isAllowComplaint 是否允许投诉
// isTake 自提单类型且配送方式是自提
// 发票这两个按钮是交易完成才有的

// 开具发票 isAllowInvoice判断
// 发票详情 isAllowInvoice判断，互斥
// 重新购买 (已取消 && 未支付) || (已支付 && 已取消 && !isTake)
// 再来一单 已支付 && !isTake && 未取消
// 超时取消订单 (备货中 || 配送中) && isAllowRefund
// 取消订单 !isTake && !isAllowRefund && 允许取消 && 未取消 && 不是配送中
// 三无退货 isAllowComplaint && isAllowRefund
// 评价 订单状态不是试吃 && 允许评论
// 立即付款 未支付 && 未取消


// 3.2 B2C 按钮逻辑
// 立即付款 订单状态为待付款
// 取消订单 待付款 || 待发货
// 查看物流 待收货
// 确认收货 待收货
// 开具发票 交易成功 && 可开具发票
// 发票详情 交易成功 && 已开具发票
// 三无退货 交易成功 && 可三无退货
// 评价 交易成功 && 可评价
// 再来一单 交易成功 && 不可三无退
// 重新购买 已取消

let globalPickupCode = ''

Page({
  mixins: [orderMixin, navigateToH5Mixin],
  data: {
    isAllSent: false,
    pickUpCode: '',
    goodsInfoList: [],
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    store: '',
    isPay: true,                           //是否付款
    isStoreOrder: false,
    isTimelyOrder: false,
    isB2COrder: false,
    isRefund: false,                       // 是否退款
    isCanceled: false,                     //当前订单状态是否为已取消
    isCancel: false,                       // 是否可以取消
    isTake: false,								         // 是否已提货
    isSelfLift: false,                      //是否自提
    isComplaintRefund: false ,             //是否投诉退款
    goodsType:'', // 商品类型是否是预售 P为预售
    orderTypeMap: {     // 订单类型：混合单（M）,及时达（T），全国送（C），试吃（S）
      T: "及时送",
      C: "全国送"
    },
    layh: '00',
    laym: '00',
    lays: '00',
    isReady: false,                        // 是否加载完数据
    showRedEnvelope: false,                //是否显示红包
    isShowSuperVip: false,   //心享会员 v1.7.1
    isShowSuperVipDetail: false, //心享会员 v1.7.1
    saleTotalPrice: 0,
    paymentDialog: false,
    pagodaMoney: true,
    goodsOrderID: '', // 订单ID
    orderTicket: '', // 订单小票号
    isSupportComment: '', // 是否可评论 Y:可评论 N:不可评论
    payOrderCompose: '', //订单组成 3--多单配送
    titleInfo: '', // 顶部订单状态及提示信息
    isShowRefundReason: false, // 是否显示退款原因弹窗
    refundRejectReason: '', // 退款不通过原因
    deliveryWay: '', // 配送方式，1 自提， 2 配送
    // 对应老电商接口字段
    orderStateBgMap: { // 当前流转状态对应的背景图
      'WAIT_PAY': bg_order_wait_pay,  // 待付款
      'STOCKING': bg_order_delivery, // 备货中
      'SENDING': bg_order_delivery, // 配送中
      'WAIT_PICKUP': bg_order_delivery, // 待自提
      'CANCELED': bg_order_close, // 已取消
      'RECEIVED': bg_order_success, // 已收货
      'PICKED_UP': bg_order_success, // 已自提
      'COMPLAINED': bg_order_success, // 已投诉
      'REVIEWING': bg_order_success, // 退款审核中
      'NOT_REFUND': bg_order_close, // 退款失败
      'REFUNDED': bg_order_success, // 已退款
    },
    // 对应新中台接口字段，只针对及时达订单
    middleOrderbgMap: {
      '待付款': bg_order_wait_pay,
      '已付款': bg_order_delivery,
      '备货中': bg_order_delivery,
      '待自提': bg_order_delivery,
      '退款中': bg_order_success,
      '已自提': bg_order_success,
      '已取消': bg_order_close,
      '配送中': bg_order_delivery,
      '已收货': bg_order_success,
      '已拒收': bg_order_close,
      '交易成功': bg_order_success
    },
    orderCancelIsShow: false, // 取消订单弹窗是否显示
    orderCancelParams: null, // 取消订单（未超时）, 保存请求接口的参数（待合并弹窗取消原因）
    invoiceBtnMap: null, // "开具发票"按钮{isAllow, status, text} - text显示文本
    orderDetail: {}, // 订单信息
    showPackDetail: false,
    orderSubType: '',  // 订单子类型：P：预售订单，N:自提，A：及时送，B：定时达，C：次日达（已停用）, D：货柜订单，E：极速达（半小时达）F: 心享现售 G：心享预售 H：心享周期购 K: 蔬菜购 S:试吃
    headPicList: [], // 商品头图列表
    defaultHeadPic: 'https://resource.pagoda.com.cn/group1/M21/8C/51/CmiLkGJnsu-AKE_SAAAIGee3pVE401.png', // 无商品头图时展示默认图片
    userInfo: {},
    isFromScanRefund: false, // 是否从扫码退进来
    currentSwiper: 0,
    navBarBgColor: '#fff', // 导航栏背景颜色
    navBarColor: '#000', // 导航栏字体颜色
    backFilter: 0, // filter brightness
    bgImg: 'https://resource.pagoda.com.cn/group1/M1D/40/D9/CmiLkGBvytqAOyTOAABGeWxVI8s080.png', //b2c订单详情顶部背景图片
    showCustomConfirmModal: false,
    barcodeImgSrc: '', // 条形码对应图片
    storeOrderBill: null,
    //  用户定位授权
    userLocationAuth: false,
    // 祝福语
    blessCardContent: '',
    blessCardFromName: '',
    blessCardToName: '',
    blessPopupIsShow: false,
    // 格式化后的祝福语
    formattedBlessCardContent: '',
    //  问号图标地址
    QUESTION_ICON,
    orderStatus:{},
    showMouthCardMode:false,
    showB2cConfirmModal: false, // b2c未全部发货状态下确认收货弹窗
    b2cConfirmModalText: '1、如商品延迟发货，请您耐心等待几天哦~ \n 2、如商品无法发出，请您先确认收货后申请退款。',
    vipCardData: {},
    rechargeInfo: {},
    rechargeCardInfo: {},
    vipCardInfo:{},
    /**是否展示三方优惠说明信息 */
    showThridPayDiscount: false,
    // 是否展示三无退提示弹窗
    showTip: false,
    // 弹窗提示的文字
    tipText: '',
    // 默认是旧订单
    isDmOrder: false,
    // 发票文案
    invoiceDetailContent: '',
    /** 不满意原因确认弹窗是否显示 */
    showReasonConfirmModal: false,
    /** 是否渲染不满意原因确认弹窗 */
    visibleReasonConfirmModal: false,
    /** 门店送礼订单弹窗 */
    posGiftOrderPopup: { show: false, status: '', },
    rechargePayInfo: null,
    /** 门店转配送时间选择弹窗 */
    offlineDeliveryTimePopupShow: false, // 单独拎出来是因为model:visible的双向绑定写法,不支持`offlineDeliveryTimePopup.show`
    offlineDeliveryTimePopup: {
      timeList: [],
      currentIndex: [0, 0],
      selectDeliveryInfo: {},
    },
    offlineDeliveryAddress: {
      addressId: 0,
    },
    offlineDeliveryAddressSubmit: {
      showConfirm: false,
      loading: false,
    },
    offlineDispatchTime: '',
    showOfflineDeliveryMap: false,
  },
  _data: {
    initNavBarStyle: false, // 是否初始化过自定义navBar
    curOrderInfo: {},
    changeNavBar: false, // 导航栏是否根据滚动改变
    isCombineOrder: false,
    // 点击按钮三无退时 goodsInfoList[下标] 记录第一个 大索引
    clickFirstIndex: -1,
    // 第二个 goodsInfoList[0][下标] 大索引
    clickSecondIndex: -1,
    isAutoImmediatelyPay: true,
    mclzUrl: '',
    // 是否包含果粉的订单信息
    customer: {
      /** @type {typeof customerSubTypeEnum[keyof typeof customerSubTypeEnum]} */
      customerSubType: 0
    },
    // 点击要三无退的订单类型'1'|'3'|'5'
    clickOrderType: '1',
    // 点击的即将进行三无退的商品行
    clickRefundGoods: {},
    /** 选择的不满意类型索引 */
    primaryReasonIndex: -1,
    /** 选择的不满意原因索引列表 */
    reasonIndex: [],
    // 订单的完结时间 存起来将在重新申请时传值给售后详情页
    finishTime: '',
    // 服务器与本地偏移时间ms
    // 由于本地时间与服务器时间可能存在误差，计算三无退倒计时时可能存在一定的时间偏差
    // 本地时间与服务器时间偏差值 所以这里记录的是 服务器-本地时间 的偏差值
    // 如果服务器的时间快于客户端，则偏差值为正
    // 如果服务器的时间慢于客户端 则偏差值为负
    // 如果要获取准确的服务时间 则  服务器时间 = 客户端时间 + 偏差值
    offSetTime: 0,
    deliveryTimeSplit: [],
  },
  onLoad: function (onLoadoptions){
    let that = this;
    wx.hideShareMenu()
    const { shareActivityFloatingPic } = wx.getStorageSync('eshopSysConfig') || {}
    var isIphoneX = wx.getSystemInfoSync().model.indexOf('iPhone X') !== -1 ? true : false;
    // const user = wx.getStorageSync('user') || {}
    // 暂时取消场景值判断
    // https://h5-web.pagoda.com.cn/posGiftReceive?orderTicket=241030008796911161543801
    const isScanPosGiftReceive = onLoadoptions.q && onLoadoptions.q.includes('posGiftReceive') // && isScanQRCode(app.globalData.scene)
    // const isScanPosGiftReceive = isScanQRCodeScene && onLoadoptions.q.includes('posGiftReceive')
    // 从分享卡片进入 (暂时取消场景值判断)
    const isFromShareCard = Boolean(onLoadoptions.orderTicket) // && [1007, 1008, 1036].includes(app.globalData.scene)
    const options = isScanPosGiftReceive || isFromShareCard ? {
      myAllOrderObj: JSON.stringify({
        isAllOrder: true,
        goodsOrderID: isFromShareCard
          ? onLoadoptions.orderTicket
          : (util.toQueryObj(decodeURIComponent(onLoadoptions.q).split('?')[1], { decodeOnEach: true })).orderTicket,
        type: 'C',
        isDmOrder: 1,
      }),
      isScanQRCodeScene: isScanPosGiftReceive,
      isReceivePosGift: isFromShareCard || isScanPosGiftReceive,
    } : onLoadoptions
    options.isOfflineDelivery = Boolean(Number(options.isOfflineDelivery))
    that.setData({
      isIphoneX,
      options,
      userInfo: wx.getStorageSync("userNameAndImg") || {},
      // user,
      timelyCity: wx.getStorageSync('timelyCity') || {},
      hongBaobg: shareActivityFloatingPic, // 侧边红包悬浮图
      // 是否是新订单
      isDmOrder: options.myAllOrderObj ? !!JSON.parse(options.myAllOrderObj).isDmOrder : false,
      // 是否是从扫码退进入
      isFromScanRefund: !!options.isScan
    });
  },
  async onShow () {
    await this.initData()
  },

  async initData () {
    if (this._data.lockOnShowReload) {
      this._data.lockOnShowReload = false
      return
    }
    ct = 0; // 重置倒计时计次
    let that = this;
    let obj;
    let options = that.data.options;
    console.log(options)
    this.setData({
      showCustomConfirmModal: false
    })

    if (!app.checkSignInsStatus()) {
      app.showSignInModal(
        [
          // 门店订单转配送
          options.isOfflineDelivery,
          // 门店订单售后转移
          options.isReceivePosGift,
        ].some(v => v)
          ? {}
          : { finishToRouter: 'homeDelivery' }
      )
      return
    } else {
      // 如果是接收门店订单礼品的场景
      // 在这尝试开启下神策上报
      options.isReceivePosGift && loginMixin.startReportPointAfterLogin(app)
    }

    const isFromTemplate = options.templateObj === 'true'
    if(isFromTemplate){
      obj = {
        goodsOrderID: options.goodsOrderID,
        payOrderID: options.payOrderID,
        payStatus: options.payStatus,
        type: options.type
      }
    }else{
      obj = JSON.parse(options.myAllOrderObj);
    }
    that.setData({
      ...(isFromTemplate && options.isDmOrder ? {
        isDmOrder: Number(options.isDmOrder),
      } : {}),
      isImmediatelyPay: obj.isImmediatelyPay,
      user: wx.getStorageSync('user') || {},
    });
    await that.getOrderInfo(obj.orderNo || obj.goodsOrderNum || obj.goodsOrderID, obj.type);
    that.setData({
      payStatus: obj.payStatus ? obj.payStatus : 'nopay'
    });
    that.setBgOrderDelivery();

    // 上报神策数据
    sensors.pageScreenView(this.getIdentityType())
  },
  onHide: function () {
    countDownIsRun = null
    clearTimeout(timeDown)
  },
  onUnload: function () {
    countDownIsRun = null
    clearTimeout(timeDown)
  },

  // 函数内部通过this调用
  clickSensorParamsFn: genClickSensor(clickParamsMap),

  // 全国送物流多个包裹切换
  swiperChange (e) {
    this.setData({
      currentSwiper: e.detail.current
    })
  },

  // 跳转物流详情页
  navigateToLogisticsDetail (info) {
    if (!info.detail) {
      var { currentSwiper = 0, orderNo, isAllSent } = info
    } else {
      var { currentSwiper, orderNo, isAllSent } = this.data
    }
    wx.navigateTo({
      url: `/homeDelivery/pages/logistics/index?expressInfo=${JSON.stringify({
        currentSwiper,
        orderNo,
        isAllSent
      })}`
    })
  },

  showVipDetail: function(){
    this.setData({
      isShowSuperVipDetail:true
    })
  },
  hideVipDetail: function(){
    this.setData({
      isShowSuperVipDetail: false
    })
  },
  touchMove () {
    /**阻止滑动穿透 */
  },
  async getOrderInfo (orderTicket, type ="A") {
    let that = this;
    const { user } = this.data
    // A 及时达
    // F 全国送
    // G 试吃
    if(['A', 'F', 'G'].includes(type)){
      // 切中台订单详情查询不需要区分已支付/未支付，不需要区分及时达/b2c
      await this.requestOrderDetail(orderTicket)


    } else if (type === 'C') { //门店订单详情
      that.initNavBarStyle('isStoreOrder')
      await that.storeOrderRequest({
        orderTicket: orderTicket,         // 门店订单编号
        customerID: user.userID,          // 顾客ID
      });
    }
  },
  /**
   * @desc 处理B2C每个商品是否发货
   */
  handleB2cExpressInfo(orderInfo) {
    const { items = [], expressInfo } = orderInfo || {}
    const { expressDetail = [] } = expressInfo || {}
    items.forEach(item => {
      expressDetail.forEach(expressItem => {
        (expressItem.deliveryGoodsItems || []).forEach(expressGoods => {
          if (String(item.goodsSn) === String(expressGoods.goodsSn)) {
            item.isSent = true
            item.deliveryNumber = expressItem.deliveryNumber || ''
          }
        })
      })
    })
  },
  /**
   * @desc 单个商品查看物流详情
   */
  checkExpressDetail (e) {
    const number = e.currentTarget.dataset.number
    const { isAllSent, orderNo, expressInfo = [] } = this.data
    const currentIndex = expressInfo.findIndex(val => String(val.deliveryNumber) === String(number))
    const currentSwiper = currentIndex >=0 ? currentIndex : 0
    wx.navigateTo({
      url: `/homeDelivery/pages/logistics/index?expressInfo=${JSON.stringify({
        currentSwiper,
        orderNo,
        isAllSent
      })}`
    })
  },
  /**
   * @desc 将serverless中与items相关的逻辑处理放到前端
   * @param {object} orderInfo
   */
  handleItemsInfo (orderInfo) {
    const goodsSnItemMap = {}
    const { items = [], mixOrderList, expressInfo, payment, createSource, orderStatus, orderType } = orderInfo
    // 是否为交易合并单
    const isTradeCombineOrder = createSource === 3;

    addGoodsSnMap(items, goodsSnItemMap);
    // 非混合单
    handleOrderGoodsName(items);
    if (Array.isArray(mixOrderList) && !!mixOrderList.length) {
      // 混合单
      mixOrderList.forEach(mix => {
        addGoodsSnMap(mix.items, goodsSnItemMap);
        handleOrderGoodsName(mix.items);
      });
    }
    // 计算预计送达时间
    orderInfo.expectedReceiveTime = handleReceiveTime(expressInfo);

    // b2c时间文案
    addB2CDeliveryTime(orderInfo);
    // 标签文字处理
    dealLabel(orderInfo)
    // 处理B2c每个商品是否发货
    const { orderStatusDesc } = orderStatus || {}
    const { desc } = orderType || {}
    if (orderStatusDesc === '待收货' && desc === 'b2c') {
      this.handleB2cExpressInfo(orderInfo)
    }

    if (isTradeCombineOrder) {
      // 新订单，只需要把餐盒费名称拿出来
      Object.keys(goodsSnItemMap).forEach(key => {
        const { itemServiceList } = goodsSnItemMap[key]
        if (!Array.isArray(itemServiceList)) return
        if (!itemServiceList.length) return

        // 餐盒费信息只有一项
        const {
          itemConsumablesName: serviceName,
          salesPrice: serviceAmount,
          quantity
        } = itemServiceList[0]

        //  给商品追加餐盒费名称及金额
        Object.assign(goodsSnItemMap[key], {
          serviceName,
          serviceAmount: quantity * serviceAmount
        })
      })
    } else {
      // 老订单
      // items 添加 payment 里的字段：serviceName等
      addPaymentInfoToItems(payment, goodsSnItemMap)
    }
  },
  // 该方法仅及时达、全国送会进入
  // 切换中台接口，请求及时达以及b2c商品
  async requestOrderDetail (orderTicket, showLoading = true) {
    const params = {
      customerID: this.data.user.userID,
      orderNo: String(orderTicket),
      // customerID: '1911235912',
      // orderNo: '23747448870003',
      // customerID: '1911236407',
      // orderNo: '23733705900001',
    }
    try {
      const { data, systemTime } = await app.api.getOrderDetail(params, showLoading)

      await mapSnapShotDetail([data], {
        scheduler({ item }) {
          //  耗材无需从快照合并名称
          if (item.isConsumable) {
            return {
              exclude: ['goodsName']
            }
          }
        }
      })
      wx.hideLoading()
      this.handleItemsInfo(data)
      const {
        createSource = 1,
        dispatchType = {},
        orderStatus = {},
        pickUpCode,
        receiver = {},
        orderType = {},
        payment,
        orderNo,
        timeLine,
        paymentStatus = {},
        expressInfo,
        packageTips,
        expectedReceiveTime,
        eshopInfo = {},
        consumables = [],
        isAllSent = false,
        vipCardInfo = {},
        vipCouponReduce = 0,
        vipGoodsReduce = 0,
        customer,
        finishTime,
        relation = [],
      } = data || {}
      this._data.finishTime = finishTime
      this._data.offSetTime = systemTime - new Date().getTime()
      const isEshopOrder = !!Object.keys(eshopInfo).length
      const {
        // amount: totalActualAmount, // 交易总金额 计算规则 http://wiki.pagoda.com.cn/pages/viewpage.action?pageId=11535514
        totalServiceAmount,
        totalCouponList,
        serviceList,
        chargeList,
        logicalExpirationTime,
        totalPrice, // 支付总额
        paymentList,
        hasThirdPartyPay,
      } = (payment || {});

      const showThridPayDiscount = (function() {
        const payPreferentialAmount = payment.payPreferentialAmount
        const orderSuccess = orderStatus.orderStatusDesc === '交易成功'
        const canRefund = data.items.some(goods => goods.canRefund)

        return payPreferentialAmount && orderSuccess && canRefund
      })()

      payment.totalPriceAddVipCardMoney = totalPrice
      const orderClass = (() => {
        const map = {
          'b2c': 'F',
          '及时达': 'A',
          '混合单': 'A'
        }
        return map[orderType.desc] || 'A'
      })();

      this.initNavBarStyle(this.showOrderType(data))

      this.showPickupCode(data)

      this.setBlessCard(consumables)

      const {
        goodsInfoList, store, dispatchTimeDesc, dispatchTimeRanage
      } = this.genGoodsInfoList(data, systemTime)
      const {
        actDiscount,
        actNewDiscount,
        totalGoodsAmount,
        fruitFansDiscount
      } = traverseGoodsInfoList({
        payment,
        customer,
        goodsInfoList
      })
      // 抵扣的费用
      const minusList = totalCouponList;
      addMinus(minusList, [
        {
          name: '新人价优惠',
          value: actNewDiscount
        },
        {
          name: '特价优惠',
          value: actDiscount
        },
        {
          name: '果粉优惠',
          value: fruitFansDiscount
        }
      ])
      // 增加心享优惠券、优惠价之后抵扣的费用
      minusList.forEach(item=>{
        item.showVipIcon = false
        if(vipCouponReduce > 0 && item.isVipCoupon){
          if(item.name === '优惠券'){
            item.showVipIcon = true
          }
          if(item.name === '免运券'){
            item.showVipIcon = true
          }
        }
      })
      if(vipGoodsReduce > 0){
        minusList.push({
          name:'心享优惠价',
          value:vipGoodsReduce,
          showVipIcon:true
        })
      }
      // 需要添加的费用 chargeList
      // 餐盒费为0时需要剔除
      // 有心享会员费用  需要剔除
      const addList = (chargeList || []).filter(charge => {
        const { costCode, costAmount } = charge || {}
        if (costCode === 'B' && !costAmount) return false
        if (costCode === 'S') return false
        return true
      });
      // 包装费
      const packAmount = addList.find(item => item.costCode === 'P')?.costAmount || 0
      // store的经纬度字段修改
      if (store.longitude && store.latitude) {
        store['lon'] = store.longitude
        store['lat'] = store.latitude
      }
      const closingTime = store.closingTime ? store.closingTime.replace(/:(\d+)$/, '') : ''
      const openingTime = store.openingTime ? store.openingTime.replace(/:(\d+)$/, '') : ''
      const storeBusinessTime  = getStoreBusinessTime({
        startTime:store.startTime||'',
        endTime:store.endTime||'',
        openingTime:openingTime?`${openingTime}-${closingTime}`:''
      })
      Object.assign(store, {
        closingTime,
        openingTime,
        storeBusinessTime
      })


      // 计算总的商品数量
      const totalGoodsCount = this.caculateGoodsCount(goodsInfoList)

      // 请求订单详情成功后
      // 若未支付 rechargeGuide
      // 若已支付 获取红包
      const { desc: paymentDesc } = paymentStatus
      const { createTime, redEnvelopeInfo} = data
      let showRedEnvelope = false
      if (paymentDesc) {
        if (paymentDesc === '支付完成') {
          // redEnvelopeInfo
          // createTime
          //如果有拆红包活动则展示悬浮的红包图标
          if (redEnvelopeInfo) {
            // createTime：2021-09-11 18:55:43.363 ios无法解析 时间格式用/代替 -，.363替换为空格
            let startTime = new Date(createTime.replace(/-/g, '/').replace(/\.[0-9]*/g, "")).getTime()
            if (systemTime <= (startTime + 86400000 * 3)) showRedEnvelope = true
          }

        } else if (paymentDesc === '未支付') {
          const { timelyCity, user } = this.data
          this.rechargeGuide(
            timelyCity.storeID || '-1',
            totalPrice,
            user.userID,
          )
        }
      }

      //  仅及时达订单执行逻辑
      if (orderType.desc === '及时达') {
        //  进行耗材订单判断
        this.markConsumablesOrder(data)
        this.setReceiveList(data)
      }
      //  全国送订单执行逻辑
      else if(orderType.desc === 'b2c' || orderType.desc === '混合单') {
        this.setB2CReceiveList(data, orderType.desc === '混合单')
      }
      // 不可开具发票文案
      this.getInvoiceDesc(paymentList, store, hasThirdPartyPay)

      //  生成订单按钮列表
      const { leftBtnList, rightBtnList } = orderShow.genBtnList.call(this, {
        orderInfo: { ...data, systemTime },
        orderType: orderType.desc,
        orderStatus: data.orderStatus.orderStatusDesc,
        isDetail: true
      })
      const isNotPay = paymentDesc === '未支付'
      // 月卡金额如果是未支付状态不加入totalPrice
      let vipCardMoney = 0
      if(vipCardInfo.type){
        vipCardMoney = isNotPay ? 0 : vipCardInfo.actualAmount
        payment.totalPriceAddVipCardMoney += vipCardMoney
      }

      const rechargeRelationInfo = relation.find(function(v) {
        return v.relationType === 3
      })
      const rechargeInfo = rechargeRelationInfo ? JSON.parse(rechargeRelationInfo.extData) : null
      if (rechargeInfo) {
        minusList.push({
          name: '充值金额',
          value: rechargeInfo.actualAmount,
          notMinus: true,
        })
        // 已支付的情况,totalPriceAddVipCardMoney中不会有充值金额
        // 需要加上rechargeInfo中的actualAmount
        const actualAmount = isNotPay ? 0 : rechargeInfo.actualAmount
        payment.totalPriceAddVipCardMoney += actualAmount
      }

      this._data.curOrderInfo = Object.assign(data,
        {
          rechargeInfo,
          originPayAmount: totalPrice,
          payAmount: String((totalPrice / 100).toFixed(2)).split('.'), //获取支付价格
        }
      )
      this._data.changeNavBar = ['及时达', '试吃自提', 'b2c', '混合单'].includes(orderType.desc)

      // 是否展示倒计时

      const showCountDown = (() => {
        // 老电商下的自提单不展示倒计时
        if (isEshopOrder && dispatchType.desc === '自提配送') return false
        return paymentStatus.desc === '未支付' && logicalExpirationTime && orderStatus.orderStatusDesc !== '已取消'
      })();

      this._data.orderIsFalse = ['已取消', '已拒收'].includes(orderStatus.orderStatusDesc)
      // console.table(data)
      const refund = refundInfo(data)
      const otherOrderInfo = []
      if (refund.exceedRefundTimeDesc) {
        // 超过七天不可三无退
        otherOrderInfo.push({
          name: '售后状态',
          value: refund.exceedRefundTimeDesc
        })
        // 发票信息 todo
      }

      // 判断多个包裹的逻辑：1. 没有全部发货，说明是多个包裹；2. 全部发货，看物流信息长度
      const hasMutiplePackage = !isAllSent || ((expressInfo.expressDetail || []).length > 1)
      const expressBoxH = (() => {
        if (hasMutiplePackage) {
          const allNoEx = Array.isArray(expressInfo.expressDetail) ? expressInfo.expressDetail.every(el => el.statusCode === 500) : false
          return allNoEx ? '144rpx' : '210rpx'
        }
        else {
          // 单个包裹有物流=>200
          const { statusCode } = expressInfo.expressDetail[0]
          return statusCode === 500 ? '80rpx' : '160rpx'
        }
      })();
      this.setData({
        isAllSent,
        orderSource: orderSourceMap[orderClass],
        orderClass,
        expectedReceiveTime,
        orderIsFalse: this._data.orderIsFalse,
        packageTips,
        totalGoodsCount,
        dispatchTimeDesc: dispatchTimeDesc ? dispatchTimeDesc : false,
        dispatchTimeRanage,
        systemTime,
        showCountDown,
        titleInfo: this.genTitleInfo(data),
        orderNo,
        orderType,
        isEshopOrder,
        dispatchType,
        // totalActualAmount,
        totalServiceAmount,
        timeLine,
        otherOrderInfo,
        addList,
        packAmount,
        minusList,
        serviceList,
        payment,
        pickUpCode,
        receiver,
        store,
        orderStatus,
        paymentStatus,
        //  右侧正常显示的按钮
        btnList: rightBtnList,
        //  左侧菜单收起按钮
        leftBtnList,
        refundInfo: refund,
        isReady: true,
        showRedEnvelope,
        redEnvelopeInfo,
        goodsInfoList,
        expressInfo: expressInfo.expressDetail || [],
        hasMutiplePackage,
        expressBoxH,
        deliveryInfo: this.handleDeliveryInfo(data, systemTime), // 配送相关时间处理
        rechargeInfo,
        rechargeCardInfo: rechargeInfo ? {
          title: '钱包充值',
          name: '钱包充值',
          icon: 'https://resource.pagoda.com.cn/dsxcx/images/c0f2e57f815936ae8be19a4eebdb26c5.png',
          price: rechargeInfo.actualAmount,
        } : {},
        vipCardData: vipCardInfo.type ? {
          title: '心享会员',
          name: vipCardInfo.title,
          icon: `https://fastdfs-prod-1251596386.cos.ap-guangzhou.myqcloud.com/eshop/${{
            M: 'c63abe0eca6452552c25b38d4fa5437d',
            XM: 'c63abe0eca6452552c25b38d4fa5437d',
          }[vipCardInfo.type] || '8a63cbbfed7165e8bc1ce410d6b0a4a2'}.png`,
          price: vipCardInfo.amount,
        } : {},
        vipCardInfo,
        showedCancelTitle: data.showedCancelTitle,
        showThridPayDiscount,
        totalGoodsAmount,
      })
      clearTimeout(timeDown)
      countDownIsRun = true
      // 倒计时
      if (showCountDown) {
        this.countDown(logicalExpirationTime, true);
      }

      const overTimeText = await this.setPayForOverTimeText(data, store)

      this.setData({
        overTimeText
      })
      console.log('overTimeText', overTimeText, 'this.data.orderIsFalse', this.data.orderIsFalse);

      this._data.createSource = createSource
      this._data.customer = customer
      if (this.data.isImmediatelyPay && this._data.isAutoImmediatelyPay) {
        this._data.isAutoImmediatelyPay = false
        this.payImmediatelyBtnHandle && this.payImmediatelyBtnHandle(data)
      }
      this.handleVedioBtn(data)
    } catch (error) {
      console.log('requestOrderDetail', error);
      commonObj.showModal('提示', '页面超时,请刷新!', false, '确认', '', function () {
        wx.navigateBack()
      })
    }
  },
  // 获取不可开具发票文案
  async getInvoiceDesc(paymentList = [], store, hasThirdPartyPay) {
    if (!Array.isArray(paymentList) || !paymentList.length) {
      return
    }
    //  筛选出代金券支付金额
    const vouchersPayAmount = paymentList.filter( item => item.payChannel === 'V' && item.payAmount ).reduce((sum, item) => {
      return sum + item.payAmount
    }, 0)
    const {
      isPagodaPay,
      isVouchersPay,
    } = paymentList.reduce(function(res, item) {
      if (item.payChannel === 'M') { // 会员钱包支付
        res.isPagodaPay = true
      } else if(item.payChannel === 'V') { //代金券支付
        res.isVouchersPay = true
      }
      return res
    }, {
      isPagodaPay: false,
      isVouchersPay: false,
    })
    const isMixPay = isPagodaPay && hasThirdPartyPay // 混合支付
    const isOnlyVouchersPay = isVouchersPay && (!isPagodaPay && !hasThirdPartyPay) // 只有代金券支付
    const storeCheck = [store && store.isSupportInvoice === 'N', '该门店无法开具电子发票，可联系门店开具纸质发票']
    const noInvoiceDesc = ((isMixPay ? [
      storeCheck,
      // 目前混合支付的其他支付方式都支持开票,如微信支付,支付宝支付,云闪付
      // 如果后续有不支持开票的,就在下面加上[判断条件, '不可开票文案']
    ] : [
      [isOnlyVouchersPay, '该订单使用代金券支付，不可开票'],
      [
        isPagodaPay,
        isVouchersPay
          ? '该订单使用钱包余额和代金券支付，不可开票'
          : '该订单使用钱包余额支付，不可开票',
      ],
      storeCheck,
    ]).find(item => item[0]) || ['', ''])[1]
    this.setData({
      vouchersPayAmount,
      noInvoiceDesc,
      isVouchersPay
    })
  },
  /**
   * 设置订单可展示的配送/自提信息列表
   * @param {*} order
   */
  setReceiveList(order) {
    const dispatchTypeDesc = order.dispatchType.desc
    const orderStatusDesc = order.orderStatus.orderStatusDesc
    let list = []

    //  配送订单
    if (dispatchTypeDesc === '商家配送') {
      list = ['送达时间', '收货人', '配送门店']

      if (orderStatusDesc === '配送中') {
        list = ['收货人', '配送门店']
      }
      else if (orderStatusDesc === '已取消') {
        list = ['收货人', '配送门店']
      }

    }
    //  自提订单
    else {
      list = ['自提门店', '营业时间', '提货时间']

      if (orderStatusDesc === '已取消') {
        list = ['自提门店', '营业时间']
      } else if (orderStatusDesc === '交易成功') {
        list = ['自提门店', '提货时间']
      }
    }

    this.setData({
      receiveList: list,
      dispatchTypeDesc
    })
  },

  /**
   * 设置全国送/混合单展示的配送信息列表
   * @param {*} order
   * @param { boolean } isMixOrder 是否混合订单
   */
   setB2CReceiveList(order, isMixOrder) {
    const orderStatusDesc = order.orderStatus.orderStatusDesc
    let list = ['配送快递', '收货人']

    if (orderStatusDesc === '待付款') {
      list = isMixOrder ? ['配送门店', '收货人'] : ['发货时间', '收货人']
    }
    else if (orderStatusDesc === '待发货') {
      list = ['发货时间', '收货人']
    }

    this.setData({
      receiveList: list
    })
  },

  setBlessCard (consumables) {
    const blessCardInfo = {}
    let blessCardStr = ''
    if (!Array.isArray(consumables)) return blessCardInfo
    consumables.forEach(el => {
      const { goodsNote, actualAmount, goodsSn, goodsTypeMask, quantity } = el
      // goodsTypeMask: 3-贺卡 1-包装费
      if (goodsTypeMask === 3) {
        blessCardStr = goodsNote
        Object.assign(blessCardInfo, {
          actualAmount, goodsSn, goodsTypeMask, goodsCount: quantity
        })
      }
    })
    // 格式化
    if (blessCardStr) {
      const { blessCardContent, blessCardSender: blessCardFromName = '', blessCardReceiver: blessCardToName = '' } = JSON.parse(blessCardStr)
      let formattedBlessCardContent = ''
      const colonReg = /[:：]/g
      const dashReg = /[--]/g
      if (blessCardToName) {
        if (colonReg.test(blessCardToName[blessCardToName.length - 1])) {
          formattedBlessCardContent += blessCardToName
        } else {
          formattedBlessCardContent += `${blessCardToName}：`
        }
      }
      formattedBlessCardContent += blessCardContent
      if (blessCardFromName) {
        if (dashReg.test(blessCardFromName.slice(0, 2))) {
          formattedBlessCardContent += blessCardFromName
        } else {
          formattedBlessCardContent += `--${blessCardFromName}`
        }
      }
      this.setData({
        blessCardContent,
        blessCardFromName,
        blessCardToName,
        formattedBlessCardContent
      })
    }
    return blessCardInfo
  },

  /**
   * 标记耗材商品订单（逻辑同订单列表耗材判断，目前无文件存放公共逻辑，后续订单详情优化版本将抽离复用
   * （商品为纯耗材时，不允许评价）
   */
  markConsumablesOrder(order) {
    //  耗材商品列表
    const consumablesList = order.items.filter(item => {
      const combinationGoodsJson = item.combinationGoodsJson

      //  只有一个投入品
      if (combinationGoodsJson && combinationGoodsJson.length === 1) {
        const basicGoodsSn = combinationGoodsJson[0].basicGoodsSn || ''

        //  basicGoodsSn为5开头，为耗材商品
        return basicGoodsSn.startsWith('5')
      }
      return false
    })

    //  纯耗材商品订单不允许评价
    if (order.items.length === consumablesList.length) {
      order.isCanComment = false
    }
  },

  /**
   * 生成标题描述
   * @param {*} data
   * @returns
   */
  genTitleInfo (data) {
    /**
     * @type { Object }
     */
    const orderType = data.orderType

    // 订单类型映射到order
    const orderDescMap = {
      '混合单': B2CDesc,
      '及时达': timelyDesc,
      '试吃自提': timelyDesc,
      'b2c': B2CDesc
    }

    //  不存在配置，直接返回（但这是个对象？）
    if (!orderDescMap[orderType.desc]) {
      return orderType
    }
    //  执行配置函数，取返回值进行展示
    return orderDescMap[orderType.desc](data)
  },

  // 结果请求完成之后判断展示的订单类型
  showOrderType (orderInfo) {
    const { mixOrderList, orderType, isRealMixOrder } = orderInfo
    // 有混合单的跳转b2c类型订单
    const isMixOrder = Array.isArray(mixOrderList) && mixOrderList.length && isRealMixOrder
    const { desc } = orderType
    if (isMixOrder) return 'isB2COrder'
    if (!desc) return 'isTimelyOrder'
    if (['混合单', 'b2c'].includes(desc)) {
      return 'isB2COrder'
    } else {
      return 'isTimelyOrder'
    }
  },

  // 展示自提码
  showPickupCode (orderInfo) {
    const {
      orderType = {},
      paymentStatus = {},
      pickUpCode,
      orderStatus = {}
    } = orderInfo
    if (['试吃自提', '及时达'].includes(orderType.desc) &&
      paymentStatus.desc === '支付完成' &&
      orderStatus.orderStatusDesc === '待自提' &&
      pickUpCode.replace('-', '') !== '' // 中台空字段会返回'-'...
    ) {
      this.getBarcodeImg(pickUpCode)
    }
  },

  /**
   * 获取条形码图片
   */
  getBarcodeImg(pickUpCode) {
    const that = this
    let failAgainRequest = false
    globalPickupCode = pickUpCode
    const getCanvasImage = () => {
      wxbarcode.then((module) => module.barcode('barcode', pickUpCode, 600, 175, () => {
        setTimeout(() => {
          if (pickUpCode !== globalPickupCode) return
          wx.canvasToTempFilePath({
            canvasId: 'barcode',
            success: (res) => {
              that.setData({
                barcodeImgSrc: res.tempFilePath
              })
            },
            fail: () => {
              // 华为手机偶现条形码加载失败，如果失败，重新绘制一次
              if (!failAgainRequest) {
                failAgainRequest = true
                getCanvasImage()
              }
            }
          }, this)
        }, 1000)
      }));
    }
    getCanvasImage()
  },
  genDispatchTime (start, end, isTryEat) {
    try {
      const [startDate, startTime] = String(start).split(' ')
      const [endDate, endTime] = String(end).split(' ')
      // 配送/自提时间不跨天
      if(startDate===endDate){
        return `${
          isTryEat
            ? [startDate, endDate].map(date => date.replace(/^(\d{4})-(\d{2})-(\d{2})$/, '$2.$3')).join('-')
            : startDate
        } ${[startTime, endTime].map(time => time.replace(/(\d{2}):(\d{2}):(\d{2})/, '$1:$2')).join('-')}`
      }
      // 配送/自提时间跨天
      else{
        if(isTryEat){
          return `${[startDate, endDate].map(date => date.replace(/^(\d{4})-(\d{2})-(\d{2})$/, '$2.$3')).join('-')}
          ${[startTime, endTime].map(time => time.replace(/(\d{2}):(\d{2}):(\d{2})/, '$1:$2')).join('-')}`
        }else{
          return `${startDate} ${startTime.replace(/(\d{2}):(\d{2}):(\d{2})/, '$1:$2')} 至
          ${endDate} ${endTime.replace(/(\d{2}):(\d{2}):(\d{2})/, '$1:$2')}`
        }
      }
    } catch (error) {
      console.log('genDispatchTime', error);
      return ''
    }
  },

  // 混合单需要拆分成两种类型
  // 有mixOrderList表示有混合单
  // 否则只有items（即只有一种类型的订单）
  // 返回 goodsInfoList => [ { listTitle: '', list: [...], orderNote: ''}, { listTitle: '', list: [...], orderNote: ''} ]
  genGoodsInfoList (orderInfo, systemTime) {
    // mixOrderList里的数据没有做字段适配
    // orderType: 10: 及时达，130: b2c全国送
    const descMap = {
      '10': '及时达',
      '130': '全国送'
    }
    const goodsInfoList = []
    const { mixOrderList, items, orderType = {}, orderStatus = {}, endDispatchTime, isRealMixOrder } = orderInfo
    // receiver 只获取类型为及时达的
    // store 只取类型为及时达的
    // dispatchTime：非混合单时，取endDispatchTime；混合单时，及时达取 endDispatchTime，b2c取商品的b2cDeliveryTime
    // 非混合单的b2c订单，在上面展示发货时间；混合单时，在商品项展示发货时间
    let store = {}, receiver, orderNote, dispatchTimeDesc, dispatchTimeRanage
    if (Array.isArray(mixOrderList) && mixOrderList.length) {
      const b2cList = []
      const timlyList = []
      mixOrderList.forEach(mix => {
        const { items, orderType } = mix
        // 及时达
        if (String(orderType.code) === '10') {
          timlyList.push(...items)
          store = Object.keys(mix.store).length ? mix.store : orderInfo.store
          receiver = mix.receiver
          orderNote = mix.orderNote
          dispatchTimeRanage = this.getDispatchTime({
            startDispatchTime: mix.startDispatchTime,
            endDispatchTime: mix.endDispatchTime,
            systemTime
          })
        } else {
          if (['已取消'].includes(orderStatus.orderStatusDesc)) {
            items.forEach(item => item.b2cDeliveryTime = void 0)
          }
          b2cList.push(...items)
        }
      })
      // 混合单可能是两个不同发货时间的全国送类型
      if (timlyList.length) {
        goodsInfoList.push({
          listTitle: isRealMixOrder ? '及时达' : '商品信息',
          dispatchTimeRanage,
          list: timlyList,
          orderNote
        })
      }
      if (b2cList.length) {
        goodsInfoList.push({
          listTitle: '全国送',
          list: b2cList
        })
      }
    } else {
      // 不是混合单
      receiver = orderInfo.receiver || {}
      goodsInfoList.push({
        // 标题逻辑：如果是及时达，用原有逻辑：商品信息；如果是全国送，或者混合单，就用及时达/全国送
        listTitle: ['10', '90'].includes(String(orderType.code)) ? '商品信息' : descMap[orderType.code],
        list: items,
        orderNote: String(orderType.code) === '10' ? orderInfo.orderNote : ''
      })
      store = orderInfo.store
      // if (!['已取消'].includes(orderStatus.orderStatusDesc)) {
      //   items.forEach(item => item.b2cDeliveryTime = undefined)
      // }
      items.forEach(item => item.b2cDeliveryTime = void 0)
      dispatchTimeRanage = this.getDispatchTime({
        startDispatchTime: orderInfo.startDispatchTime,
        endDispatchTime: orderInfo.endDispatchTime,
        systemTime
      })
    }

    if (['待付款', '待发货'].includes(orderStatus.orderStatusDesc)) {
      dispatchTimeDesc = `预计${endDispatchTime.split(' ')[0]}日前发货, 快递配送`
    }

    goodsInfoList.forEach(goodsInfo => {
      const list = goodsInfo.list || []
      list.forEach(item => {
        //  设置耗材商品的默认头像
        setConsumablesAvatar(item)
        // 兑换卡
        const { preferentialActType} = item
        //  如未设置normalPrice字段，在此处进行拦截设置。用于界面展示
        if (!item.normalPrice) {
          //  下方展示结果，按照旧版本逻辑计算
          //  常规价格购买数量
          item.normalPriceCount = item.goodsCount
          //  常规价格
          item.normalPrice = item.actualAmount / item.goodsCount
          //  用于展示的商品总价
          item.showTotalPrice = item.actualAmount

          // 商品总价（会员价）--兼容新字段的展示
          item.showTotalMemberPrice = preferentialActType === 2 ? item.exchangeCardGoodMoney : item.showTotalPrice
          // 赋值商品单价 (保持旧逻辑，兼容老订单) --兼容新字段的展示
          item.memberPrice = item.normalPrice
        }
        if (item.marketingSubType === SUB_TYPES.XIN_REN_TE_JIA) {
          item.specialType = '新人价'
        }
        handleGoodsItemPrice(item, orderInfo.customer)
        item.btnText = '售后详情'
        // 如果有退款信息 则设置按钮文本
        if (item.refundInfo) {
          // 有4种情况 分为
          // 1.未申请 2.退款中 3.退款成功 4.退款失败
          if (['退款中', '审核中'].includes(item.afterSaleInfo.title)) {
            item.btnText = `退款中`
          }
          if (item.afterSaleInfo.title === '退款成功') {
            item.btnText = `已退款 ¥${formatPrice(item.afterSaleInfo.refundTotalMoney || item.afterSaleInfo.refundMoney)}`
          }
          if (item.afterSaleInfo.title === '退款失败') {
            item.btnText = `退款失败`
          }
          if (item.afterSaleInfo.title === '退款关闭') {
            item.btnText = `退款关闭`
          }
        }
        // 如果有三无换货 则需要展示已换货标识
        if (item.hasThreeNoExchange) {
          item.btnText = `已换货`
        }
      })
    })

    return {
      goodsInfoList,
      store,
      dispatchTimeDesc,
      dispatchTimeRanage: dispatchTimeRanage ? dispatchTimeRanage.split(' ') : ''
    }
  },

  // 按钮事件处理
  handleBtn: function (ev) {
    let { type: btnType } = ev.currentTarget.dataset
    console.log('`${btnType}Handle`', `${btnType}Handle`)
    this[`${btnType}Handle`](this._data.curOrderInfo)
  },
  // 处理配送、自提时间
  // 配送类型：未收货取预计送达时间范围；已收货取完成时间
  // 自提类型：未自提时间范围；已自提取自提时间
  handleDeliveryInfo (orderInfo, systemTime) {
    // orderStatus,dispatchType不需要默认值，后面有判断
    const {
      startDispatchTime,
      endDispatchTime,
      pickupTime,
      finishTime,
      orderStatus,
      dispatchType,
      orderType
    } = orderInfo
    const titleMap = {
      '商家配送': '送达时间',
      '自提配送': '提货时间'
    }
    const deliveryInfo = {}
    let time = ''
    if (!orderStatus || !dispatchType) return ''
    // 已取消状态不展示
    if (this._data.orderIsFalse) return ''
    // 90表示交易完成状态
    if (String(orderStatus.code) === '90') {
      switch (dispatchType.desc) {
        case '商家配送':
          time = finishTime
          break;
        case '自提配送':
          time = pickupTime
          break;
      }
    } else {
      switch (dispatchType.desc) {
        case '商家配送':
          time = this.getDispatchTime({
            startDispatchTime,
            endDispatchTime,
            systemTime
          })
          break;
        case '自提配送':
          time = `${this.genDispatchTime(startDispatchTime, endDispatchTime, (orderType || {}).desc === '试吃自提')}`
          break;
      }
    }
    Object.assign(deliveryInfo, {
      title: titleMap[dispatchType.desc],
      label: dispatchType.desc === '商家配送' ? '最快29分钟达' : '',
      time
    })
    return deliveryInfo
  },

  /**
   * 设置及时达配送时间
   */
  getDispatchTime({ startDispatchTime, endDispatchTime, systemTime }) {
    // 区分立即送达/预约
    const startTimeStamp = new Date(startDispatchTime.replace(/-/g, '/')).getTime()

    //  系统时间大于开始配送时间，说明当前时间晚于开始配送时间
    if (systemTime > startTimeStamp) {
      // 当天时间大于下单选的开始配送时间，为立即送达
      // 配送时间点为 结束配送时间-30分钟 到 结束配送时间
      return this.genDispatchTime(setTime(endDispatchTime, -30*60*1000), endDispatchTime)
    }
    // 预约单
    return this.genDispatchTime(startDispatchTime, endDispatchTime)
  },

  // 设置订单超时赔付文字
  // 1. 已经赔付的，不用再请求是否存在活动的接口
  // http://wiki.pagoda.com.cn/pages/viewpage.action?pageId=14516427
  // 取订单接口的 payment.activityInfoDTOS, activityType 为 P 的 discountAmount(优惠金额) // 免运券没有金额，所以直接判断有没有p这一项就好
  // 2. 未赔付的 && 订单状态是待付款，备货中，配送中 && 该城市有赔付活动
  async setPayForOverTimeText (orderInfo, store) {
    const {
      payment: {
        activityInfoDTOS = []
      },
      orderStatus = {},
      orderType = {},
      dispatchType = {},
      mixOrderList
    } = orderInfo
    const {
      cityCode = ''
    } = store
    // 及时达或混合单才有提示
    if (orderType.desc === 'b2c') return ''
    if (Array.isArray(activityInfoDTOS)) {
      // 有赔付项
      const hasP = activityInfoDTOS.some(ac => ac.activityType === 'P')
      if (hasP) return '超时送达，已赔付优惠券'
    }
    // 未赔付的
    // 根据订单状态判断
    if (!['待付款', '备货中', '配送中'].includes(orderStatus.orderStatusDesc)) return ''
    if (!cityCode) return ''
    const { data: { hasActivity } } = await app.api.isExistOvertimeActivity({
      cityCode
    })
    // 如果不是混合单，配送类型必须要商家配送
    if (!mixOrderList.length) {
      if (dispatchType.desc !== '商家配送') return ''

      return hasActivity ? '超时赔券' : ''
      // return '超时赔券'
    } else {
      // 如果是混合单，找到orderType是及时达的且配送方式是商家配送，没找到则return ''
      const findRes = mixOrderList.find(mix => {
        const { orderType, dispatchType } = mix
        return orderType.desc === '及时达' && dispatchType.desc === '商家配送'
      })
      return findRes && hasActivity ? '超时赔券' : ''
    }
  },

  // 计算商品总数
  caculateGoodsCount (goodsInfoList) {
    let totalGoodsCount = 0
    goodsInfoList.forEach(info => {
      info.list.forEach(good => {
        totalGoodsCount += good.quantity
      })
    })
    return totalGoodsCount
  },

  // （待付款，备货中，配送中，超时已赔付）展示超时赔付的文案
  getPayForOverTimeText (currentFlowStatus, existReparationActivity, isOverTime) {
    const overTimeText = !existReparationActivity || existReparationActivity === 'N' ? '' : ['WAIT_PAY', 'STOCKING', 'SENDING'].indexOf(currentFlowStatus) !== -1 ? '超时赔付优惠券' : isOverTime && isOverTime === 'Y' ? '超时送达，已赔付优惠券' : ''
    return  overTimeText
  },

  showPosGiftScanCodeAuthToast() {
    if (!this.data.options.isScanQRCodeScene) {
      return
    }
    const storeOrderData = this.data.storeOrderData
    const title = ([
      [storeOrderData.giftAuthStatus.outOfAuthDate, '订单完成超过7天，无法开启售后转移'],
      [storeOrderData.giftAuthStatus.hasBind, '订单已完成售后转移'],
      [storeOrderData.giftAuthStatus.openAuth, '订单已开启售后转移'],
    ].find(([condition]) => condition) || [true, ''])[1]
    title && wx.showToast({
      title,
      icon: 'none',
    })
  },

  getOfflineDeliveryTimePopupData(deliveryTimeSplit) {
    const offlineDeliveryTimePopup = this.data.offlineDeliveryTimePopup
    const dateDesc = deliveryTimeSplit.map(v => ({
      dateDesc: v.dateDesc
    }))
    // 如果没选择过时间
    if (!offlineDeliveryTimePopup.selectDeliveryInfo.desc) {
      return {
        offlineDeliveryTimePopup: Object.assign(
          offlineDeliveryTimePopup,
          {
            timeList: textTransf([
              dateDesc,
              splitTrans(deliveryTimeSplit[0].splitList)
            ]),
            currentIndex: [0, 0],
          }
        )
      }
    }
    const { deliveryTimeBegin, deliveryTimeEnd } = offlineDeliveryTimePopup.selectDeliveryInfo
    const [dateIndex, timeIndex] = deliveryTimeSplit.reduce(function(result, item, index) {
      if (result.some(v => v !== -1)) {
        return result
      }
      const timeIndex = item.splitList.findIndex(item => {
        return item.deliveryTimeBegin === deliveryTimeBegin && item.deliveryTimeEnd === deliveryTimeEnd
      })
      return timeIndex !== -1 ? [index, timeIndex] : result
    }, [-1, -1]).map(v => Math.max(v, 0))
    return {
      offlineDeliveryTimePopup: Object.assign(
        offlineDeliveryTimePopup,
        {
          timeList: textTransf([
            dateDesc,
            splitTrans(deliveryTimeSplit[dateIndex].splitList)
          ]),
          currentIndex: [dateIndex, timeIndex],
          selectDeliveryInfo: deliveryTimeSplit[dateIndex].splitList[timeIndex],
        },
      )
    }
  },

  //门店订单详情
  storeOrderRequest: function (params) {
    let that = this;
    const {
      isOfflineDelivery,
      customerID,
      weight,
      key,
    } = this.data.options
    return new Promise((resolve, reject) => {
      wx.showLoading({ title: '加载中' });
      
      app.$http.post({
        url: `/wxapp/order/${this.data.isDmOrder ? 'v4' : 'v2'}/offlineOrderDetail`,
        data: Object.assign(
          params,
          isOfflineDelivery ? {
            offlineDelivery: true,
            orderCustomerId: customerID,
            weight,
            key,
          } : {},
        ),
      }).then(res => {
        wx.hideLoading();
        let data = res.data;
        if (isOfflineDelivery && !data.isSupportDelivery) {
          app.showModalPromise({
            content: '地址填写已超时，请联系门店',
          }).then(() => {
            wx.navigateBack()
          })
          return
        }
        this._data.finishTime = data.finishTime
        this._data.offSetTime = res.systemTime - new Date().getTime()
        let goodsItem = data.goodsItem
        // 获取门店订单商品头图
        that.getSpuHeadPicList(goodsItem)
        // 判断“开发票”按钮
        let invoiceBtnMap = that.invoiceBtn(data)
        const bill = data.bill
        //  门店订单左侧按钮初始化
        this._data.changeNavBar = true
        // 如果是新门店订单 则设置三无退按钮状态和三无退必要的字段
        if (that.data.isDmOrder) {
          goodsItem.forEach(item => {
            item.btnText = '售后详情'
            // 如果有退款信息 则设置按钮文本
            if (item.afterSaleInfo) {
              // 有4种情况 分为
              // 1.未申请 2.退款中 3.退款成功 4.退款失败
              if (['退款中', '审核中'].includes(item.afterSaleInfo.title)) {
                item.btnText = `退款中`
              }
              if (item.afterSaleInfo.title === '退款成功') {
                item.btnText = `已退款 ¥${formatPrice(item.afterSaleInfo.refundTotalMoney || item.afterSaleInfo.refundMoney)}`
              }
              if (item.afterSaleInfo.title === '退款失败') {
                item.btnText = `退款失败`
              }
              if (item.afterSaleInfo.title === '退款关闭') {
                item.btnText = `退款关闭`
              }
            }
            // 如果有三无换货 则需要展示已换货标识
            if (item.hasThreeNoExchange) {
              item.btnText = `已换货`
            }
            // 如果是门店新订单 则需要增加主订单号
            item.mainOrderNo = data.mainOrderNo
          })
        }
        // v2数据兼容
        data.giftBindStatus = data.giftBindStatus || {}
        data.giftAuthStatus = data.giftAuthStatus || {}
        const leftBtnList = that.storeOrderLeftBtnInit(data);
        // 如果需要展示绑定弹窗
        const needShowBindPopup = data.giftBindStatus.showBind
        const needShowAuthShare = data.giftAuthStatus.openAuth && !data.giftAuthStatus.hasBind
        // 如果开启了授权并且没有绑定,就展示分享菜单
        needShowAuthShare && wx.showShareMenu({
          menus: ['shareAppMessage'],
        })
        const isScanQRCodeScene = that.data.options.isScanQRCodeScene
        // 未开启授权的情况下,从扫码/小程序卡片进入,自动展示授权弹窗
        const needShowAuthPopup = data.giftAuthStatus.showAuth && !data.giftAuthStatus.openAuth && isScanQRCodeScene
        const deliveryTimeSplit = data.deliveryTimeSplit || []
        this._data.deliveryTimeSplit = util.deepClone(deliveryTimeSplit)
        that.setData({
          leftBtnList,
          // 送达时间
          ...(data.offlineDispatchTime ? {
            offlineDispatchTime: data.offlineDispatchTime,
          } : {}),
          receiver: isOfflineDelivery
            ? this.data.offlineDeliveryAddress.addressId && this.data.offlineDeliveryAddressSubmit.loading
              // 如果是刚刚提交的地址
              // this.data.offlineDeliveryAddress.addressId是还有值的
              // 直接使用this.data.offlineDeliveryAddress即可
              ? util.deepClone(this.data.offlineDeliveryAddress)
              : data.receiver
            : null,
          ...(
            deliveryTimeSplit.length && isOfflineDelivery
              ? this.getOfflineDeliveryTimePopupData(deliveryTimeSplit)
              : {}
          ),
          posGiftOrderPopup: needShowBindPopup || needShowAuthPopup ? {
            show: true,
            status: needShowBindPopup ? data.giftBindStatus.bindStatus : 'openAuth',
          } : { show: false, status: '', },
          storeOrderBill: bill, // 门店订单结算数据
          storeOrderData: data, // 门店订单详情数据
          store: data.store,           // 提货门店信息
          goodsItem: goodsItem,      // 商品信息
          createTime: data.createTime,
          // groupSharePic: goodsList.groupSharePic,              // 分享图片
          // groupShareTitle: goodsList.groupShareTitle,          // 分享标题
          isReady: true,
          // isShow: true,
          orderAmount: (data.orderAmount / 100).toFixed(2),
          orderActualAmount: bill ? bill.actual : data.orderActualAmount,
          goodsTotalAmount: data.goodsTotalAmount,
          memberDiscountAmount: data.memberDiscountAmount,
          storeOrderCouponAmount: data.couponAmount,
          refundAmount: data.refundAmount,
          orderTicket: data.orderTicket,                      //订单编号
          tradeTime: data.tradeTime,                          //交易时间
          orderFlowState: data.orderFlowState,//data.orderFlowState,                // 订单当前流转状态
          isAllowComplaint: data.isAllowComplaint || '',      // 是否能投诉
          isAllowRefund: data.isAllowRefund || '',            // 是否能退款
          titleInfo: {
            title: '交易成功',
            desc: '感谢您对百果园的信任，欢迎下次光临！',
            iconName: 'success'
          },
          refundApplyTime: data.refundApplyTime,               //退款时间
          goodsType: data.orderType,   // 是否是预售商品
          isSupportComment: data.isSupportComment, // 是否可评论 Y:可评论 N:不可评论
          refundRejectReason: data.refundRejectReason || (data.refundInfo || {}).desc, // 退款不通过原因
          invoiceBtnMap,
          invoiceDetailContent: data.invoiceDetailContent,
          refundInfo: data.refundInfo,
          packageAmount: data.packageAmount, // 餐盒费
          payment: data.payment, // 付款信息（包含是否是有第三方支付）
        })
        // 在不需要展示授权弹窗的情况下，如果三无退超7天导致的,需要展示toast
        needShowAuthPopup || that.showPosGiftScanCodeAuthToast()
        that.posGiftExposureSensorsReport()
        resolve(data)
      }).catch(function (error) {
        wx.hideLoading();
        app.showModalPromise({
          content: (error.error || { message: error.description }).message || '服务异常'
        }).then(() => {
          wx.navigateBack()
        })
        reject(error)
      })
    })
  },

  posGiftExposureSensorsReport() {
    const { goodsItem, storeOrderData: { giftAuthStatus } } = this.data
    // 三无退按钮曝光埋点
    goodsItem.some(item => item.canRefund && !(item.afterSaleInfo && item.afterSaleInfo.refundStatus === 111)) &&
      sensors.exposureReport(clickParamsMap['160200001'])
    // 授权按钮按钮曝光埋点
    giftAuthStatus.showAuth && !giftAuthStatus.hasBind &&
      sensors.exposureReport(clickParamsMap[giftAuthStatus.openAuth ? '*********' : '160200006'])
  },

  showPosGiftOrderAuthPopup({ currentTarget: { dataset: { status, sensorsKey } }}) {
    // 目前只有160200006按钮会执行这个回调
    sensorsKey && sensors.clickReport(clickParamsMap[sensorsKey])
    this.setData({
      posGiftOrderPopup: { show: true, status },
    })
  },

  onPosGiftOrderPopupClose() {
    this.setData({
      posGiftOrderPopup: { show: false, status: '', },
    })
  },

  onPosGiftOrderPopupChangeStatus({ detail: { status, show = true, hasBind } }) {
    const posGiftOrderPopupData = {
      'posGiftOrderPopup.status': status,
      'posGiftOrderPopup.show': status === 'openAuthSuccess' ? !hasBind : show,
    }
    const goodsItem = ['openAuthSuccess', 'authOutOfDate'].includes(status) ? this.data.goodsItem.map(item => {
      return {
        ...item,
        // 开启售后授权后，不允许退款
        canRefund: false,
      }
    }) : []
    const getData = ([
      [
        status === 'bindTicketSuccess',
        () => ({
          // 绑定成功后,不显示自定义的头部banner
          'storeOrderData.giftBindStatus.isGiftOrderBind': false,
          // 绑定成功后,不展示授权弹窗
          'options.isScanQRCodeScene': false,
        })
      ],
      [
        status === 'openAuthSuccess',
        () => {
          const eventChannel = this.getOpenerEventChannel()
          eventChannel && eventChannel.emit && eventChannel.emit('hideSanwuRefundBtn', {
            orderTicket: this.data.orderTicket,
          })
          return {
            // 开启授权成功切换按钮状态
            'storeOrderData.giftAuthStatus.showAuth': true,
            'storeOrderData.giftAuthStatus.openAuth': true,
            'storeOrderData.giftAuthStatus.hasBind': Boolean(hasBind),
            goodsItem,
          }
        }
      ],
      [
        status === 'authOutOfDate',
        () => ({
          // 超7天隐藏按钮
          'storeOrderData.giftAuthStatus.showAuth': false,
          goodsItem,
        })
      ]
    ].find(([condition]) => condition) || [false, () => ({})])[1]
    this.setData(Object.assign(posGiftOrderPopupData, getData()), () => {  
      // 绑定小票成功后，重新获取数据
      status === 'bindTicketSuccess' && this.initData()
    })
  },

  onKeepPopupOpen() {
    this._data.lockOnShowReload = true
  },

  openAddressList() {
    wx.navigateTo({
      url: `/bgxxUser/pages/address/addressList/index?from=offlineDelivery&settleGoodsType=offline&offlineOrderStoreCode=${
        this.data.storeOrderData.store.storeCode
      }`,
      events: {
        selectAddress: ({ selectedAddress }) => {
          const {
            addressId,
            cityCode,
            gisStreet,
            gisDistrict,
            cityName,
            gisProvince,
            gisAddress,
            address,
            phoneNumber,
            name,
          } = selectedAddress
          this.setData(Object.assign(
            {
              offlineDeliveryAddress: {
                addressId,
                street: gisStreet || '',
                hometown: gisDistrict,
                city: cityName,
                province: gisProvince,
                address: `${gisAddress || ''}${address || ''}`,
                mobile: phoneNumber,
                name,
              },
            },
            this.data.offlineDeliveryTimePopup.selectDeliveryInfo.desc
              ? {}
              : {
                'offlineDeliveryTimePopup.selectDeliveryInfo': this.data.offlineDeliveryTimePopup.timeList[1][0] || {}
              }
          ))
        },
      }
    })
  },

  showOfflineDeliveryTimePicker() {
    if (!this.data.offlineDeliveryAddress.addressId) {
      wx.showToast({
        title: '请先选择地址',
        icon: 'none',
      })
      return
    }
    this.setData({
      offlineDeliveryTimePopupShow: true,
    })
  },

  offlineDeliveryTimePick(event) {
    const {
      detail: { current },
    } = event;
    const { offlineDeliveryTimePopup } = this.data
    this.setData({
      offlineDeliveryTimePopupShow: false,
      offlineDeliveryTimePopup: Object.assign(offlineDeliveryTimePopup, {
        selectDeliveryInfo: offlineDeliveryTimePopup.timeList[1][current[1]],
        currentIndex: current,
      }),
    });
  },

  /** @desc 选择左侧日期 */
  offlineDeliveryTimePickChangeDate(event) {
    const {
      detail: { current },
    } = event;
    const { offlineDeliveryTimePopup: { timeList } } = this.data;
    timeList[1] = splitTrans(
      this._data.deliveryTimeSplit[current[0]].splitList || []
    );
    this.setData({
      'offlineDeliveryTimePopup.timeList': textTransf(timeList),
    });
  },

  onOfflineDeliveryAddressConfirm({ detail }) {
    this.setData({
      'offlineDeliveryAddressSubmit.showConfirm': false
    })
    this._data.offlineDeliveryAddressSubmitConfirmPromise.resolve(detail === 'confirm')
  },

  async submitOfflineDeliveryAddress() {
    const {
      offlineDeliveryAddress,
      offlineDeliveryTimePopup: { selectDeliveryInfo },
      user,
      orderTicket,
      options,
    } = this.data
    if (!offlineDeliveryAddress.addressId) {
      wx.showToast({
        title: '请先选择地址',
        icon: 'none',
      })
      return
    }
    if (!selectDeliveryInfo.deliveryTimeBegin) {
      wx.showToast({
        title: '请先选择送达时间',
        icon: 'none',
      })
      return
    }
    const offlineDeliveryAddressSubmit = this.data.offlineDeliveryAddressSubmit
    if (offlineDeliveryAddressSubmit.loading || offlineDeliveryAddressSubmit.showConfirm) {
      return
    }
    const promise = getPromiseObj()
    this._data.offlineDeliveryAddressSubmitConfirmPromise = promise
    this.setData({
      'offlineDeliveryAddressSubmit.showConfirm': true
    })
    const confirmResult = await promise.promise
    if (!confirmResult) {
      return
    }
    this.setData({
      'offlineDeliveryAddressSubmit.loading': true,
    })

    const { data, error } = await app.$http.post({
      url: '/dskhd/api/order/delivery/v1/submitOfflineDeliveryInfo',
      data: {
        addressId: offlineDeliveryAddress.addressId,
        orderCustomerID: options.customerID,
        key: options.key,
        customerID: user.userID,
        orderTicket,
        weight: Number(options.weight),
        deliveryTimeBegin: selectDeliveryInfo.deliveryTimeBegin,
        deliveryTimeEnd: selectDeliveryInfo.deliveryTimeEnd,
      }
    }).then(data => ({
      data
    })).catch(error => ({
      error
    }))
    if (error) {
      app.showModalPromise({
        content: error.description || '网络错误',
      })
      this.setData({
        'offlineDeliveryAddressSubmit.loading': false,
      })
      return
    }

    this.setData({
      receiver: util.deepClone(offlineDeliveryAddress),
      offlineDispatchTime: selectDeliveryInfo.desc.replace('预计', ''),
    })

    await this.storeOrderRequest({
      orderTicket: orderTicket,         // 门店订单编号
      customerID: user.userID,          // 顾客ID
    }).catch(() => ({}))
    this.setData({
      'offlineDeliveryAddressSubmit.loading': false,
    })
  },

  /**
   * 门店订单左侧按钮初始化
   * @param { object } orderDetail 订单详情
   */
  storeOrderLeftBtnInit(orderDetail) {
    const leftBtnList = [];

    /**
     * 是否允许退款
     * 当isAllowRefund为N时，说明已经不能退款。包含以下情况
     * 超过7天
     * 已三无退
     */
    const storeOrderCanDelete = orderDetail.isAllowRefund === 'N';

    // 未开启售后转移授权才能删除订单
    if (storeOrderCanDelete && !orderDetail.giftAuthStatus.openAuth) {
      leftBtnList.push({
        label: '删除订单',
        callback: this.storeOrderDeleteOrder
      });
    }

    return leftBtnList
  },

  // 获取门店订单商品头图
  getSpuHeadPicList(goodsItem) {
    let spuNumberList = []
    spuNumberList.push(...goodsItem.map( item => item.goodsNumber))
    app.api.getSpuHeadPicList({spuNumberList}).then(res => {
      this.setData({
        headPicList: res.data
      })
    })
  },
  // 倒计时 已确认和未确认订单
  countDown: function (expireTime, duration = 0) {
    let that = this, eTime ;
    let nowTime = new Date(this.data.systemTime - -duration).getTime();
    eTime = new Date(expireTime.replace(/-/g, '/')).getTime();
    let layTime = Math.round((eTime - nowTime) / 1000);         // 剩余时间秒数

    if (layTime < 0) {
      that.setData({
        layh: '00',
        laym: '00',
        lays: '00',
      })
      // todo
      // if (that.data.paymentStatus.desc === '未支付') {
      //   commonObj.showModal('提示', '支付超时,订单已取消', false, '确认', '', function () {
      //     wx.navigateBack({
      //       delta: 1
      //     })
      //   })
      // }
      this.countDownFinish(this.data.orderClass, this._data.curOrderInfo)
      return;
    }
    let h = parseInt(layTime / 3600);
    let m = parseInt(layTime / 60 - h * 60);
    let s = parseInt(layTime % 60);

    s = s < 10 ? '0' + s : s;
    m = m < 10 ? '0' + m : m;
    h = h < 10 ? '0' + h : h;
    that.setData({
      layh: h,
      laym: m,
      lays: s,
    })
    if (countDownIsRun) {
      timeDown = setTimeout(() => {
        ct++
        that.countDown(expireTime, ct * 1000)
      }, 1000)
    }
  },
  //商品详情
  toGoodsDetail: wrapSensorReportFn(function(e){
    const { takeawayattr, goodssn, isconsumable } = e.currentTarget.dataset
    // 不是耗材
    if (!isconsumable) {
      wx.navigateTo({
        url: '/homeDelivery/pages/goodsDetail/index?homeDeliveryObj=' + JSON.stringify({ takeawayAttr: takeawayattr, goodsSn: goodssn }),
      })
    }
  }, /** 神策上报访问商品详情 */clickSensorParamsFn('商品详情')),
  // 跳转导航
  navigateNav: function () {
    let store = this.data.store;
    if (Object.keys(store).length) {
      let x = coordtransform.bd09togcj02(parseFloat(store.lon), parseFloat(store.lat))
      wx.openLocation({
        latitude: x[1],
        longitude: x[0],
        name: store.shortName || store.storeName,
        address: store.address
      })
    } else {
      commonObj.showModal('提示', '找不到门店坐标', false, '我知道了')
    }
    this.preventEvent();
  },

  /**
   * 门店订单——删除订单
   */
  storeOrderDeleteOrder() {
    const { orderTicket } = this.data.storeOrderData;

    //  此处直接使用混入中的删除订单逻辑
    this.deleteBtnHandle({
      orderType: {
        //  门店订单为：100，对应serverless接受的门店类型参数
        code: 100
      },
      orderNo: orderTicket
    })
  },

  getIdentityType() {
    const { giftAuthStatus, giftBindStatus } = this.data.storeOrderData || { giftAuthStatus: {}, giftBindStatus: {} }
    return giftAuthStatus.isGiftOrder || giftBindStatus.isGiftOrderBind ? {
      identityType: giftAuthStatus.isGiftOrder ? '送礼人' : '收礼人',
    } : {}
  },

  // 单品门店订单三无退
  sanwuBtnHandleSingle: app.subProtocolValid('shop', async function (e) {
    // 判断是否符合三无退时长
    if (this.hasShortTime()) {
      return
    }
    const { isNext, lat, lon } = await locateService.beforeRefundCheckLocate()
    if (!isNext) {
      return
    }
    sensors.clickReport(clickParamsMap['160200001'], { 
      ...this.getIdentityType(),
      longitu: lon,
      latitu: lat,
    })
    sensors.exposureReport(clickParamsMap['160206001'], this.getIdentityType())


    // 及时达/全国送订单三无退货跳转
    this.showGoodsRowRefund(e, '3')
  }),

  // 不好吃瞬间退
  // 订单列表三无退与订单详情的三无退不同，所以分开定义
  sanwuBtnHandle: app.subProtocolValid('shop', async function (e) {
    const that = this
    let longitu = ''
    let latitu = ''
    if (that.data.isTimelyOrder || that.data.isB2COrder) {
      // 如果是待收货 则需要弹窗
      if (that.data.isB2COrder && e.orderStatus && e.orderStatus.orderStatusDesc === '待收货') {
        // 弹窗提示 请先确认收货后才能进行三无退货哦
        that.setData({
          showTip: true,
          tipText: '请先确认收货后才能进行三无退货哦'
        })
        return
      }
      
      const { isNext, lat, lon } = await locateService.beforeRefundCheckLocate()
      longitu = lon
      latitu = lat
      if (!isNext) {
        return
      }
      // 及时达/全国送订单三无退货跳转
      that.showGoodsRowRefund(e, that.data.isTimelyOrder ? '1' : '5')
      
    }

    // 如果是扫码退方式进入 则此处不上报埋点
    if (that.data.isFromScanRefund) {
      return
    }
    // 神策上报不好吃瞬间退款
    const { isTimelyOrder } = this.data
    that.trackClickEvent({
      'element_code': '140100008',
      'element_name': '三无退货',
      'element_content': '三无退货',
      'blockCode': 140100,
      'screen_code':'1401',
      'screen_name': (isTimelyOrder ? '及时达' :'全国送') + '订单详情',
      longitu,
      latitu
    })
  }),

  /**
   * 隐藏提示弹窗
   */
  toggleTip() {
    this.setData({
      showTip: false,
      tipText: ''
    })
  },
  /**
   * 是否是短时间(30min)内申请的三无退
   * @returns {Boolean} 是否是短时间内的订单
   */
  hasShortTime() {
    const timeLimit = this.data.storeOrderData.refundLimitTime
    const refundContent = this.data.storeOrderData.refundToastContent || '请您先品尝果品，不满意可30分钟后三无退货';
    if (!timeLimit || !this.countRefundTime(timeLimit)) {
      wx.showToast({
        title: refundContent,
        icon: 'none',
        duration: 3000
      })
      return true
    }
    return false
  },
  // 计算时间
  countRefundTime(timeLimit) {
    let curTime = Date.parse(new Date())
    timeLimit = timeLimit.replace(/-/g, '/')
    let timeGap = parseInt(curTime - new Date(timeLimit).getTime());
    return timeGap > 0
  },
  service: wrapSensorReportFn(function () {
    app.toOnlineService()
  }, clickSensorParamsFn('门店订单联系客服')),
  //投诉
  bindComplaintTap: wrapSensorReportFn(function () {
    wx.reportAnalytics('groupinfo_refunbtn');
    commonObj.showModal('提示', '您可以下载百果园App->客服帮助选择相关售后服务，感谢您的理解', false);
  }, clickSensorParamsFn('投诉')),
  // 拨打客服电话
  callServicePhone: wrapSensorReportFn(function () {
    let phoneNumber
    if (this.data.store && Object.keys(this.data.store).length) {
      const {
        storePhone, phone, telephone, mobilephone
      } = this.data.store
      phoneNumber = storePhone || phone || telephone || mobilephone || '4001811212'
    }
    wx.makePhoneCall({
      phoneNumber: phoneNumber
    })
    this.preventEvent();
    return clickSensorParamsFn(
      phoneNumber === '4001811212' ? '联系客服' : '联系店家'
    )
  }),
  //联系客服
  serviceBtn: wrapSensorReportFn(async function(){
    wx.makePhoneCall({
      phoneNumber: '4001811212'
    })
    this.preventEvent();
  }, /* 神策上报联系客服 */ clickSensorParamsFn('联系客服')),
  // 防止点击过快，导致页面重复跳转蒙层
  preventEvent: function () {
    this.setData({ prevent: true });
    setTimeout(() => {
      this.setData({ prevent: false });
    }, 400)
  },
  onShareAppMessage: function ({ from, target }) {
    // 把7年前的无用代码删除了
    // 目前只有门店送礼订单用到了这个分享
    from === 'button' && sensors.clickReport(clickParamsMap[target.dataset.sensorsKey || '*********'])
    return {
      title: '您有一份售后权利待领取哦',
      path: `/userB/pages/allOrderDetail/index?orderTicket=${this.data.orderTicket}`,
      imageUrl: 'https://resource.pagoda.com.cn/dsxcx/images/0fce18572b42bb45b41dae968b48810a.png',
    }
  },

  sensorsRed: wrapSensorReportFn(function () {
    // 神策上报发红包
  }, clickSensorParamsFn('发红包')),

  /**
   * 红包跳转H5链接
   * @param {*} e
   */
  redPacketNavigate(e) {
    this.navigateToCommonH5Page(e.detail)
  },

  // 解决滚动穿透
  stopMove () {
    return
  },
  // 复制订单号
  copyNoTap(e) {
    const data = e.currentTarget.dataset.no
    wx.setClipboardData({
      data,
      success() {
        wx.showToast({
          title: '复制成功',
          icon: 'none'
        })
      }
    })
  },
  navigateToDownLoadH5(){
    let pageUrl = 'http://mp.weixin.qq.com/s?__biz=MjM5ODAwMTYwMA==&mid=*********&idx=1&sn=375d762d3186d7596f297023a08d813b&chksm=3c2a3d7e0b5db468c113e25d4421397dd4f5f5bc810e024e8bc5dcc478ace022c9df3aafbb35#rd'
    wx.navigateTo({
      url: '/h5/pages/commonLink/index?pageUrl=' + encodeURIComponent(pageUrl),
    })
  },
  //v2.4——配送中订单添加app下载引导页:开具发票、查看物流
  toDownloadApp () {
    // wx.navigateTo({
    //   url:'/userB/pages/guideToDownLoad/index'
    // })
    this.navigateToDownLoadH5();
  },
  /**
   * @description - 成功取消订单，更新列表
   *  */
  orderCancelSuccess: wrapSensorReportFn(async function () {
    let vm = this
    // 取消订单成功
    const options = this.data.options || {}
    const obj = JSON.parse(options.myAllOrderObj);
    wx.showToast({
      title: '订单取消成功',
      icon: 'success',
      duration: 2000,
      mask: true,
      success: function () {
        vm.refreshOrder(1000)
      }
    })
    return Object.assign(
      {},
      // 神策上报"残忍取消"
      clickSensorParamsFn('残忍取消'),
      //智慧零售埋点
      // {
      //   txOrderSensor: [
      //     {
      //       createTime: this.data.createTime || Date.now(),
      //       cancelTime: Date.now(),
      //       orderNumber: obj.goodsOrderID,
      //       amount: this.data.payment.amount,
      //       payAmount: this.data.payment.totalPrice,
      //       type: 3 //type等于3表示取消订单的埋点
      //     }
      //   ]
      // }
    )
  }),

  refreshOrder (timeout = 2000) {
    const vm = this
    const pageStack = getCurrentPages()
    const lastPage = pageStack[pageStack.length - 1] || {}
    // 延时执行时要判断是否在当前页面
    setTimeout(function () {
      if (lastPage.route === 'userB/pages/allOrderDetail/index') {
        vm.getOrderInfo(vm.data.orderNo);
      }
    }, timeout)
  },

  /**
   * @description - 弹窗选择-“取消订单”原因,（支付成功，才需要弹出选择原因，取消订单)
   * @param {object} params - 订单取消选项
   * @returns {undefined}
   */
   onOrderCancelSelected (e) {
    const { cancelReason = '', cancelID = '' } = e.detail
    const { orderCancelParams } = this.data
    const { paramMap } = orderCancelParams
    if (paramMap && paramMap.eshopRefund) { // 旧电商订单
      Object.assign(orderCancelParams.paramMap.eshopRefund, {
        cancelID,
        subReason: cancelReason
      })
    } else {
      Object.assign(orderCancelParams, {
        reason: cancelReason
      })
    }
    this.orderCancelPaiedSuccessFetch(orderCancelParams)
  },
  /**
   * @description - 弹窗关闭-“取消订单”原因
   */
   onOrderCancelClosed () {
    this.setData({
      orderCancelIsShow: false,
      orderCancelParams: null
    })
  },

   /**
   * @description - 判断“开发票”按钮
   * @param {object} - 订单详情数据对象
   * @returns {object || null} 按钮数据对象
   */
  invoiceBtn (order) {
    let { isAllowInvoice: isAllow } = order
    // 不允许开发票
    if (isAllow === 'I') {
      return null
    }
    // 允许开发票,显示不同文本
    // let statuses = ['PROGRESSING', 'FINISH'] // 开票中, 已开票
    const text = isAllow === 'D' ? '发票详情' : '开具发票'
    const btn = isAllow === 'D' ? 'invoiceDetailBtnHandle' : 'invoiceBtnHandle'
    const sureInvoicing = isAllow === 'D' ? 2 : 1
    return {
      isAllow,
      text,
      btn,
      sureInvoicing,
    }
  },
  /**
   * @description - 点击“开具票”按钮
   */
  async onInvoiceBtnTap () {
    // const vm = this
    // const {
      // text,
      // isAllow,
      // btn,
    // } = this.data.invoiceBtnMap
    const {invoiceBtnMap} = this.data
    if (invoiceBtnMap.isAllow === 'O') {
      //  携带参数跳转到对应页面
      const query = JSON.stringify({
        //  订单类型
        orderType: 100,
        //  开票金额
        drawableAmount: this.data.storeOrderData.invoiceDetail.drawableAmount,
        //  开票订单数
        orderQuantity: 1
      });

      //  设置开具发票页使用的开票参数
      wx.setStorageSync('createInvoice_data', {
        selectedData: [
          {
            orderChannel: 'OFFLINE',
            orgCode: this.data.store.storeCode,
            channelOrderNo: this.data.orderTicket,
            drawableAmount: this.data.storeOrderData.invoiceDetail.drawableAmount,
            ticketNo: this.data.orderTicket,
            orderAmount: this.data.storeOrderData.invoiceDetail.orderAmount,
            cashPayAmount: this.data.storeOrderData.invoiceDetail.cashPayAmount,
            finishTime: this.data.storeOrderData.tradeTime,
            store: {
              content: this.data.storeOrderData.invoiceDetail.content,
              invoicer: this.data.storeOrderData.invoiceDetail.invoicer
            }
          }
        ]
      })

      wx.navigateTo({
        url: '/userB/pages/invoice/createInvoice/index?pageParam=' + query,
      });

    }else{
      //  携带参数跳转到对应页面
      const query = JSON.stringify({
        //  门店 订单渠道为 OFFLINE
          orderChannel: 'OFFLINE',
          channelOrderNo: this.data.orderTicket
        });

        wx.navigateTo({
          url: '/userB/pages/invoice/invoiceDetail/index?pageParam=' + query,
        })
    }
    // this[btn]()
  },
  // 关闭自定义弹窗
  customConfirmModalCancel () {
    this.setData({
      showCustomConfirmModal: false
    })
  },
  // 退款不通过原因弹窗-打开/关闭
  handleRefundReasonModel(e) {
    const show = e.currentTarget.dataset.show
    this.setData({
      isShowRefundReason: show
    })
  },
  /**
   * 打开餐盒费明细
   */
  showOrHidePackDetail(e) {
    this.setData({
      showPackDetail: true
    })
  },
  /**
   * 关闭餐盒费明细
   */
  closePackListPopu () {
    this.setData({
      showPackDetail: false
    })
  },
  onPageScroll(ev) {
    this.setData({ scrollTop: ev.scrollTop })

    if (!this._data.changeNavBar) {
      return
    }
    this.setNavBarStyleOnScroll(ev)
  },
  setNavBarStyleOnScroll (ev) {

    const { scrollTop } = ev
    const { navBarHeight = 0 } = this.data
    const ratio = Math.min(scrollTop / (navBarHeight/2), 1)
    if(this._data.ratio === ratio) return
    this._data.ratio = ratio
    if (ratio <= 0) {
      // 没有滚动距离
      this.setData({
        navBarBgColor: 'transparent',
        navBarColor: '#fff',
        backFilter: 1
      })
    } else {
      this.setData({
        navBarBgColor: `rgba(255,255,255, ${ratio})`,
        navBarColor: '#222222',
        backFilter: 1 - ratio
      })
    }
  },
  initNavBarStyle (orderType) {
    if (this._data.initNavBarStyle) return
    if (!['isTimelyOrder', 'isStoreOrder', 'isB2COrder'].includes(orderType)) return
    this._data.initNavBarStyle = true
    this.setData({
      [orderType]: true,
      navBarBgColor: '',
      navBarColor: '#fff',
      backFilter: 1
    })
    // if (['isStoreOrder'].includes(orderType)) {
    //   this.setData({
    //     [orderType]: true,
    //     navBarBgColor: '#fff',
    //     navBarColor: '#000',
    //     backFilter: 0
    //   })
    // } else {
    //   this.setData({
    //     [orderType]: true,
    //     navBarBgColor: '',
    //     navBarColor: '#fff',
    //     backFilter: 1
    //   })
    // }
  },
  // 子组件事件：获取自定义导航栏高度
  getNavBarHeight (ev) {
    // 这里的高度单位是px
    this.setData({
      navBarHeight: ev.detail
    })
  },
  /**
   * 关闭包装费/运费说明弹窗
   */
   closeFeeTip() {
    this.setData({
      showPackFeeTip: false
    })
  },
  /**
   * 提示包装费说明提示
   */
   showFeeTip() {
    this.setData({
      showPackFeeTip: true
    })
  },
  // showFeeTip
  // showOrHidePackDetail
  toggleFeeTips (e) {
    const { code } = e.target.dataset
    if (code === 'B') {
      this.showOrHidePackDetail()
    } else if (code === 'P') {
      this.showFeeTip()
    }
  },

  /**
   * 及时达，设置订单配送中背景图
   */
  setBgOrderDelivery() {
    //  仅针对及时达进行背景图动态修改，不影响其他业务
    if (!this.data.isTimelyOrder) {
      return;
    }

    if (this.data.orderStatus.orderStatusDesc === '配送中') {

      const query = wx.createSelectorQuery();
      query.select(".footer-btn-list").boundingClientRect();

      query.exec(res => {
        const [footerBtnList] = res;
        const deliverySectionHeight = 180;

        //  及时达配送中，红包的位置比较特殊
        this.setData({
          redBagStyle: `bottom: ${footerBtnList.height + deliverySectionHeight}px`
        });
      });

    }

    const bg_order_delivery = deliveryPNG.locationAuth;
    this.setData({
      'orderStateBgMap.SENDING': bg_order_delivery,
      'middleOrderbgMap.配送中': bg_order_delivery
    });
  },
  showBlessPopup () {
    this.setData({
      blessPopupIsShow: true
    })
  },
  closeBlessPopup () {
    this.setData({
      blessPopupIsShow: false
    })
  },
  payrightNow: util.throttle(wrapSensorReportFn(async function () {
    if (this.isHandlePay) {
      return
    }
    this.isHandlePay = true
    await this.fetchPayApi()
    // 点击上报
    const sensorInfo = this.clickSensorParamsFn('付款')
    this.isHandlePay = false
    return sensorInfo
  }), 2000),


  /**
   * 跳转开通百果心享页
   */
  async navigateToXxVip() {
    if (!this.vipTips) {
      const { data } = await app.api.getBgxxVipTips({ customerID: app.globalData.customerID })
      this.setData({
        vipTips: data
      })
    }
    act.toActivityPage(this.data.vipTips)
  },
  /**
   * 设置对应商品退货信息 并显示退货原因弹窗
   * @param e 事件对象
   * @param orderType 订单类型
   */
  showGoodsRowRefund(e, orderType) {
    this._data.clickOrderType = orderType
    this._data.clickRefundGoods = e.currentTarget.dataset.goods
    this._data.clickFirstIndex = e.currentTarget.dataset.index
    this._data.clickSecondIndex = e.currentTarget.dataset.goodsIndex
    // 如果已经加载过数据，直接弹窗
    if (this.data.visibleReasonConfirmModal) {
      this.setData({
        showReasonConfirmModal: true
      })
      return
    }
    wx.showLoading()
    // 否则就等待加载数据完成后弹窗
    this.setData({
      visibleReasonConfirmModal: true
    })

  },
  /**
   * 加载完不满意原因数据后再弹窗
   */
  reasonLoaded(e) {
    wx.hideLoading()
    this.setData({
      showReasonConfirmModal: true
    })
  },
  /**
   * 跳转到三无退货页面(及时达/全国送/门店订单)
   */
  goRefund() {
    const refundGoods = this._data.clickRefundGoods
    // 获取订单最高可退金额
    const maxRefundAmount = Number(refundGoods.maxRefundAmount || refundGoods.orderMaxRefundAmount)

    if (maxRefundAmount === 0) {
      wx.showToast({
        title: '该商品优惠后实付金额为0，无可退金额啦~',
        icon: 'none',
        duration: 1000
      })
      return
    }
    const params = this.getRefundPageParams()
    pageTransfer.send(params)
    wx.navigateTo({
      url: `/userB/pages/selfSupportComplaints/goodsDissatisfy/index`,
    })
  },
  getRefundPageParams() {
    // 拼接一波参数
    const orderType = this._data.clickOrderType
    const { createSource = 1, customer, primaryReasonIndex, reasonIndex } = this._data
    const { store = {}, payment = {}, orderTicket, isTimelyOrder, dispatchType, isB2COrder } = this.data
    const { tradeNo = '' } = payment
    // 是否是门店订单
    const isDoorOrder = Number(orderType) === 3

    // 如果是绑定过来的送礼订单,
    // 直接认为是isOnlyWalletPay
    // 这样三无退那就不会显示是否需要原路退的弹窗
    const isOnlyWalletPay = (isDoorOrder && this.data.storeOrderData.giftBindStatus.isGiftOrderBind) || !payment.hasThirdPartyPay

    const refundGoods = this._data.clickRefundGoods
    if (isDoorOrder && !refundGoods.headPic) {
      // 匹配头图
      const objTargetImg = this.data.headPicList.find(item => `${item.spuNumber}` === `${refundGoods.goodsNumber}`)
      const { picUrl, defaultHeadPic } = this.data
      if (objTargetImg) {
        refundGoods.headPic = `${picUrl}${objTargetImg.spuHeadPic}`
      } else {
        refundGoods.headPic = defaultHeadPic
      }
    }
    const params = {
      complaintType: '1',
      refundGoods,
      orderType,
      isDmOrder: isDoorOrder,
      createSource: createSource || 1,
      contact: store.storePhone || store.phone || store.telephone || store.mobilephone || '4001811212',
      tradeNo: tradeNo || '',
      orderTicket: orderTicket,
      customer,
      primaryReasonIndex: primaryReasonIndex,
      reasonIndex,
      // 是否是配送订单
      isDelivery: (isTimelyOrder && dispatchType.code === 10) || isB2COrder,
      identityType: this.getIdentityType(),
    }
    if (isOnlyWalletPay) {
      params.isOnlyWalletPay = true
    }
    return params
  },
  /**
   * 跳转到三无退详情页
   * @param {Object} e event事件
   * @param {Boolean} isRedirect 是否是关闭当前页面并跳转
   */
  goDetailHandle(e, isRedirect) {
    this._data.clickOrderType = e.currentTarget.dataset.orderType
    this._data.clickRefundGoods = e.currentTarget.dataset.goods
    this._data.clickFirstIndex = e.currentTarget.dataset.index
    this._data.clickSecondIndex = e.currentTarget.dataset.goodsIndex
    const isDmOrder = this.data.isDmOrder
    // 接龙的不调用此方法
    const refundGood = this._data.clickRefundGoods
    // 如果进行了三无换货 则需要弹窗提示
    if (refundGood.hasThreeNoExchange) {
      this.setData({
        showTip: true,
        tipText: '商品已完成换货，无需再提交三无退款'
      })
      return
    }
    let refundOrders = []
    // 如果是门店新订单 则需要取出对应的一单多退订单 并拼接好头图
    if (isDmOrder) {
      // 判断是否存在同一单多退的情况
      const refundOrder = refundGood.refundOrder
      refundOrders = this.data.goodsItem.filter(item => item.refundOrder === refundOrder)
      refundOrders.forEach(refundGood => {
        if (isDmOrder && !refundGood.headPic) {
          // 匹配头图
          const objTargetImg = this.data.headPicList.find(item => `${item.spuNumber}` === `${refundGood.goodsNumber}`)
          const { picUrl, defaultHeadPic } = this.data
          if (objTargetImg) {
            refundGood.headPic = `${picUrl}${objTargetImg.spuHeadPic}`
          } else {
            refundGood.headPic = defaultHeadPic
          }
        }
      })
    } else {
      // 这里是及时达、全国送、接龙的订单 保持原逻辑
      refundOrders = refundGood
    }
    // 如果详情页需要直接跳转到三无退申请页 这里赋值参数
    const refundPageParams = this.getRefundPageParams()
    const params = {
      refundGood: refundOrders,
      orderType: this._data.clickOrderType,
      isDmOrder,
      customer: this._data.customer,
      finishTime: this._data.finishTime,
      offSetTime: this._data.offSetTime,
      // 三无退申请页的参数
      refundPageParams
    }
    pageTransfer.send(params)
    wx[isRedirect ? 'redirectTo' : 'navigateTo']({
      url: `/userB/pages/selfSupportComplaints/complaintsDetail/index`,
    })
  },
  /**
   * 该方法被三无退结果页跨页面调用（及时达、全国送、门店单品退）
   * 根据用户点击对应商品跳转到三无退详情页
   */
  async checkRefundDetail() {
    await this.initData()
    const { isDmOrder, goodsItem, goodsInfoList } = this.data
    const { clickFirstIndex, clickSecondIndex } = this._data
    let goods = {}
    // 如果是门店单品订单 则取值对应的数据
    if (isDmOrder) {
      goods = goodsItem[clickFirstIndex]
    } else {
      goods = goodsInfoList[clickFirstIndex].list[clickSecondIndex]
    }
    // 等待data赋值完成后执行跳转
    this.goDetailHandle({
      currentTarget: {
        dataset: {
          goods,
          orderType: this._data.clickOrderType,
          index: this._data.clickFirstIndex,
          goodsIndex: this._data.clickSecondIndex
        }
      }
    }, true)
  },
  async handleVedioBtn(orderInfo) {
    const info = await addBtn(orderInfo)
    if (!info) return
    const { btn, url, web } = info
    if (!url || !btn) return
    const { btnList } = this.data
    this._data.mclzUrl = url
    Object.assign(this._data, {
      mclzUrl: url,
      dskhdWebUrl: web
    })
    this.setData({
      btnList: [...btnList, btn]
    })
    const { btnName } = btn || {}
    if (btnName) {
      sensors.exposureReport(clickParamsMap[btnName])
    }
  },
  recordBtnHandle: recordBtnHandle,
  liveBtnHandle: liveBtnHandle,
  /**
   * 跳转到心享会员首页
   */
  jumpVipPage() {
    this.navigateToCommonH5Page({
      currentTarget: {
        dataset: {
          urltype: '7'
        }
      }
    })
  },
  /**
   * 不满意原因确认弹窗确认回调
   */
  reasonConfirmModalSubmit(event) {
    const detail = event.detail
    const {
      primaryReasonIndex,
      reasonIndex
    } = detail

    this._data.primaryReasonIndex = primaryReasonIndex
    this._data.reasonIndex = reasonIndex
    this.closeReasonConfirmModal()
    this.goRefund()
    this._data.clickOrderType === '3'
      ? sensors.clickReport(clickParamsMap['160206001'], this.getIdentityType())
      : this.trackClickEvent({
      blockCode: '140103',
      'element_code': '140103001',
      'element_name': '去三无退货',
      'element_content': '去三无退货',
    })

  },
  /**
   * 关闭不满意原因确认弹窗
   */
  closeReasonConfirmModal() {
    this.setData({
      showReasonConfirmModal: false
    })
  },
})
