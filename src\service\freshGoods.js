/**
 *
 * 先读取缓存文件，没有再通过cos读取cdn文件
 */
const { deepClone, deepCloneMap } = require('../utils/util.js')
const { ENV } = require('../utils/config.js')
const { readZipEntry, downloadFile } = require('../utils/readAndSaveFile/index')
const bgxxApi = require('../api/bgxx.api')
const cos = require('./cosInstance')

/**
 *
 * @typedef { 0 | 1 | 2 } GoodsRestrictions 限购类型(不限购 0;单人每日限购 1;单人总限购 2;)
 *
 * @typedef { Object } CosGoodsItem
 * @property { string } goodsSn goodsSn
 * @property { string[] } basicGoodsSn spu
 * @property { string } beginTakeTime 提货时间(用于判断现售/预售)
 * @property { GoodsRestrictions } goodsRestrictions
 *
 * @typedef { Object } BaseInfo cos中的基本信息
 * @property { string } goodsSn 商品SN
 * @property { string } mainImgUrl 商品头图
 * @property { string } recommendPic 商品推荐图
 * @property { { type: number; url: string; }[] } resourceList 商品头图
 * @property { string } beginTakeTime 开始自提时间
 * @property { 'N' | 'P' } saleType N: 现售/P: 预售
 * @property { 0 | 1 } saleStatus 上下架状态
 * @property { 0 | 1 } isGift 是否赠品
 * @property { { basicGoodsSn: string; }[] } associationList 商品sku列表
 * @property { number } eshopGoodsId 老电商商品ID
 * @property { number } [limitBuyDaily] 商品当日限购数量
 * @property { number } [purchaseLimit] 商品总限购数量
 * @property { GoodsRestrictions } goodsRestrictions 商品限购
 * @property { number } [retailPrice] 价格
 *
 * @typedef {Object} PromotionGoods
 * @property {string} code - 商品code
 * @property {string} name - 商品名称
 * @property {number} minPrice - 最低限价（分）
 * @property {number} price - 单价（分）
 * @property {number} amount - 金额/数量（单笔最大购买数量）(单位分：金额数量都*100 ，260 = 2.6个)
 * @property {number} dayAmount - 数量/单人单品每天限购数量（单位：10=10个）
 * @property {number} totalAmount - 数量/单人单品总限购数量（单位：10=10个）
 * @property {string} unit - 单位（个/斤/公斤）
 * @property {string} goodsId - 商品id
 * @property {string} goodsIdBack - 商品id备份
 * @property {string} spuNumber - 商品spu编码
 * @property {0 | 1} couponStacking - 是否可叠加优惠券使用。0：不能，1：能
 * @property {number} cartLimit - 单次限购(0表示不限购)
 * @property {string} activityCode - 活动编码
 * @property {boolean} hasDayLimit - 是否有日限购
 * @property {number} dayLimit - 剩余日限购数
 * @property {number} [dayAmount] - 日活动限购数
 * @property {boolean} hasTotalLimit - 是否有活动限购
 * @property {number} totalLimit - 剩余活动限购数
 * @property {number} [totalAmount] - 总活动限购数
 *
 * @typedef {Object} CriticalInfo
 * @property {number} customRetailPrice - 自定义零售价
 * @property {string} goodsSn - 商品编号
 * @property {number} heartPrice - 心享价
 * @property {number} linePrice - 划线价
 * @property {number} memberPrice - 会员价
 * @property {number} retailPrice - 零售价
 * @property {number} saleStatus - 销售状态，1为在售，0为下架
 *
 * @typedef { Object } DynamicInfo 接口查到的详情
 * @property { Record<string, { stock: number; saleType: 'N' | 'P' }> } stock
 * @property { Record<string, number> } totalLimitEffect
 * @property { Record<string, number> } dailyLimitEffect
 * @property { Record<string, number> } saleCount
 * @property { Record<string, PromotionGoods> } promotion
 * @property { Record<string, CriticalInfo> } criticalInfo
 *
 * @typedef { Object } ComplateInfoFilter
 * @property { boolean } [filterSaleStatus] 是否过滤下架商品
 * @property { boolean } [filterGift] 是否过滤赠品
 * @property { boolean } [filterPreSale] 是否过滤预售商品
 * @property { boolean } [filterStock] 是否过滤无库存商品
 * @property { boolean } [filterNewCustomer] 是否过滤掉新客专享商品
 * @property { boolean } [filterCoupon] 是否过滤掉不可用券商品
 * @property { boolean } [beginTakeTime] 按提货时间过滤商品(传在这有利于优化查询动态信息速度,因为根本不会去查提货时间不对的动态信息)
 * @property { boolean } [filterSpring] 是否过滤掉试春盘商品(默认true,设置售罄情况下强制改成false)
 * @property { boolean } [emptySpringStock] 是否将试春盘商品库存设置为0
 * @property { boolean } [filterFrozen] 是否过滤冷冻品(默认true,设置售罄情况下强制改成false)
 * @property { boolean } [emptyFrozenStock] 是否将冷冻品库存设置为0
 * @property { boolean } [sortSoldOut] 是否将售罄商品沉底(默认false,传递true时,filterStock强制转换为false)
 *
 */

class AssembleGoods {
  static frontEndFields = [
    'stock', // 库存
    'saleStatus', // 上下架状态
    'mainImgUrl', // 头图
    'recommendPic', // 推荐图
    'isNewCustomerAvailable', // 购买限制:0,无 ;1,仅限新人专享
    'goodsName', // 商品名称
    'abbreviation', // 商品简称
    'subtitle', // 副标题
    'saleType', // 现售N,预售P
    'retailPrice', // 商品交易价
    'buyerType', // 买家类型 A所有用户（all） V心享用户（vip）
    'heartPrice', // 心享会员价
    'isSupportCoupon', // 是否支持使用优惠券
    'isGift', // 是否赠品
    'goodsSn', // 商品编号
    'goodsId', // 商品SKU
    'residualEffectNum', // 剩余限购数量
    'totalEffectNum', // 总限购数
    'isCombined', // 是否组合品
    'id', // 老电商商品id
    'tradeInInfo', // 换购活动信息
    'beginTakeTime', // 预售提货时间
    'storageMethod', // 存储方式
    //新增一些所需要的参数 v3.6.4
    'updatedAt', // 更新时间
    'resourceList', // 资源列表
    'eshopGoodsId', // 电商的goodsId
    'specialInfo', // 特价活动信息
    'specDesc', // 商品规格
    'vipPriceType', // 1 系统定义， 2 用户自定义(次日达商品才有,及时达的商品是0)
    'saleCount', // 销量(人气值)
  ]

  /**
   * @desc 将接口里的详情和基本信息进行组合
   * @param { Object } params
   * @param { BaseInfo } params.baseInfo
   * @param { DynamicInfo } params.dynamicInfo
   * @param { ComplateInfoFilter } params.filter
   * @param { any } params.userInfo
   */
  constructor({ baseInfo, dynamicInfo, filter, userInfo }) {
    this.baseInfo = baseInfo
    this.dynamicInfo = dynamicInfo
    this.filter = filter
    this.userInfo = userInfo
  }

  get isVip() {
    return this.isLogin && ['F', 'T'].includes(this.userInfo.superVipStatus)
  }

  get isLogin() {
    return !!this.userInfo.userToken
  }

  getCriticalInfo(goodsSn, key) {
    return (this.dynamicInfo.criticalInfo[goodsSn] || {})[key]
  }

  getDetail() {
    const goodsItem = this.baseInfo
    const {
      goodsSn,
      goodsRestrictions,
      purchaseLimit,
      isGift,
      limitBuyDaily,
      resourceList = []
    } = goodsItem;
    const { stock: goodsSn2Stock, dailyLimitEffect, totalLimitEffect, promotion, saleCount } = this.dynamicInfo
    const hasLogin = this.isLogin
    const stockInfo = goodsSn2Stock[goodsSn] || { saleType: 'N', stock: 0 };
    const totalEffectNum = hasLogin
      ? {
        0: null,
        1: limitBuyDaily,
        2: purchaseLimit
      }[goodsRestrictions]
      : null;
    const stockNum = ((goodsItem.vipSpringFilter && this.filter.emptySpringStock) || (goodsItem.vipNoneFrozenFilter && this.filter.emptyFrozenStock) ? 0 : stockInfo.stock) || 0
    /**
     * @type { Omit<BaseInfo, "isGift"> & { stock: number; isGift: string; id: number; residualEffectNum: null | number; totalEffectNum: null | number; specialInfo?: PromotionGoods; } }
     */
    const result = {
      ...goodsItem,
      heartPrice: this.getCriticalInfo(goodsSn, 'heartPrice') || goodsItem.heartPrice,
      retailPrice: this.getCriticalInfo(goodsSn, 'retailPrice') || goodsItem.retailPrice,
      saleStatus: this.getCriticalInfo(goodsSn, 'saleStatus') || goodsItem.saleStatus,
      saleCount: saleCount[goodsSn] || 0,
      mainImgUrl: (
        resourceList.find(v => Number(v.type) === 0) || {
          url: goodsItem.mainImgUrl || ''
        }
      ).url,
      recommendPic:  (
        resourceList.find(v => Number(v.type) === 12) || {
          url: goodsItem.mainImgUrl || ''
        }
      ).url,
      isGift: { 0: 'N', 1: 'Y' }[isGift],
      saleType: stockInfo.saleType,
      stock: stockNum,
      id: goodsItem.eshopGoodsId,
      // 总限购数
      totalEffectNum: totalEffectNum,
      // 剩余限购数
      residualEffectNum:
        hasLogin && goodsRestrictions !== 0
          ? totalEffectNum -
            ({
              2: totalLimitEffect,
              1: dailyLimitEffect,
            }[goodsRestrictions][goodsSn] || 0)
          : null
    };
    /** @desc 会员身份价格 */
    const memberPrice = this.isVip ? result.heartPrice : result.retailPrice
    const specialInfo = promotion[goodsSn]
    // 会员身份价格要大于特价价格
    if (specialInfo && memberPrice > specialInfo.price) {
      result.specialInfo = specialInfo
    }
    const frontendResult = AssembleGoods.frontEndFields.reduce((_result, field) => {
      (field in result) && (_result[field] = result[field]);
      return _result;
    }, {});
    return frontendResult;
  }
}


// 用来缓存读取cos，并发只读取一次
const cacheCosByReadKey = new Map()

class ReadGoodsInfoByCos {
  constructor({ logger = false }) {
    this._logger = logger
    this.cache = cacheCosByReadKey
    this.cosPathPrefix =  '/goods-fresh-zip/'
    this.cos = cos
  }
  /**
   * @description 缓存读取操作
   * @param {*} readKey
   * @returns
   */
  readAsyncWithCache(readKey) {
    if (this.cache.has(readKey)) {
      return this.cache.get(readKey);
    }
    const promise = this.readAsync(readKey);
    this.cache.set(readKey, promise);
    return promise;
  }
  /**
   * @desc 读取文件
   * @param {string} key 文件名
   */
  async readAsync (readKey) {
    if (typeof readKey === 'string') {
      return this.getResultByReadZipEntry(`${readKey}`)
    }
  }
  /**
   * @desc 从cos生成本地路径直接读取zip
   * @param {string} key
   * @returns {object}
   */
  async getResultByReadZipEntry (readKey) {
    const cosUrl = this.cos.completeNoAuthUrl(this.cosPathPrefix + readKey + '.zip')
    const tempFilePath = await downloadFile(cosUrl)
    // 通过本地链接读取zip文件
    let result = ''
    if (tempFilePath){
      result = await readZipEntry(readKey, tempFilePath)
    }

    // 读取完成，删除缓存，防止下次读取不到
    if (this.cache.has(readKey)) {
      this.cache.delete(readKey);
    }
    return this._formatResponse(result.data)
  }
  /**
   * @desc string parse to json
   * @param {str} string
   * @returns {object}
   */
  _formatResponse (str) {
    if (!str || typeof str !== 'string') {
      return []
    }
    try {
      const data = JSON.parse(str)
      return data
    } catch (error) {
      console.log('_formatResponse error', error);
      return []
    }
  }
}

/**
 * @description 以城市维度缓存商品
 */
class CacheFreshGoods {
  constructor() {
    this.cityGoodsMap = new Map() // 定义门店商品map，门店编码作为key获取商品集合
    this.cityGoodsIdMap = new Map() // 存储id和map的关系isCacheExpired
    this.cacheSize = 5
    this.cacheTime = 2 * 60 * 1000
    this.setTimeMap = new Map();
  }
  delGoodsMap(cityCode) {
    this.cityGoodsMap.delete(cityCode);
    this.setTimeMap.delete(cityCode);
  }
  getGoodsMap(cityCode) {
    // if (this.isCacheExpired(cityCode)) {
    //   this.delGoodsMap(cityCode)
    //   return new Map()
    // }
    return deepCloneMap(this.cityGoodsMap.get(cityCode)) || new Map()
  }

  setGoodsMap(cityCode, goodsList) {
    const goodsMap = this._convertGoodsToMap(cityCode, goodsList)
    if (goodsMap.size === 0) {
      return new Map();
    }
    if (this.cityGoodsMap.size >= this.cacheSize) {
      const deleteKey = this.cityGoodsMap.keys().next().value;
      this.delGoodsMap(deleteKey)
    }

    if (this.cityGoodsMap.has(cityCode) && !this.isCacheExpired(cityCode)) {
      const existingGoodsMap = this.cityGoodsMap.get(cityCode);
      goodsMap.forEach((goods, goodsSn) => existingGoodsMap.set(goodsSn, goods));
    } else {
      this.cityGoodsMap.set(cityCode, goodsMap);
      this.setTimeMap.set(cityCode, Date.now());
    }
    return goodsMap
  }
  getGoodsSn({ eshopGoodsId, cityCode }) {
    const goodsIdMap = this.cityGoodsIdMap.get(cityCode) || new Map();
    return goodsIdMap.get(String(eshopGoodsId)) || '';
  }
  setEshopIdAndGoodsSn({ eshopGoodsId, goodsSn, cityCode }) {
    if (!eshopGoodsId) {
      return
    }
    if (!this.cityGoodsIdMap.has(cityCode)) {
      this.cityGoodsIdMap.set(cityCode, new Map());
    }
    this.cityGoodsIdMap.get(cityCode).set(String(eshopGoodsId), goodsSn);
  }
   // 转换商品列表至map
   _convertGoodsToMap(cityCode, goodsList = []) {
    const goodsMap = new Map();
    goodsList.forEach((goods) => {
      goodsMap.set(String(goods.goodsSn), goods);
      this.setEshopIdAndGoodsSn({ cityCode, eshopGoodsId: goods.eshopGoodsId, goodsSn: goods.goodsSn });
    })
    return goodsMap;
  }
  isCacheExpired(cityCode) {
    const setTime = this.setTimeMap.get(cityCode);
    if (!setTime) return false;
    return Date.now() - setTime >= this.cacheTime;
  }
}
const cacheFreshGoods = new CacheFreshGoods()

class FreshGoods {
  /** @type { Promise<{ vipNoneFrozen: string[]; vipSpring: string[]; }> } */
  static freshStoreLabel = bgxxApi.getFreshLabelStore().then(({ data }) => (data || { vipNoneFrozen: [], vipSpring: []  })).catch(() => ({ vipNoneFrozen: [], vipSpring: []  }))
  constructor({
    cityCode,
    deliveryCenterCode,
    storeCode,
  }) {
    this.cityCode = cityCode
    this.deliveryCenterCode = deliveryCenterCode
    this.storeCode = storeCode
    this.userInfo = wx.getStorageSync('user') || {}
    this.getGoodsPromiseMap = new Map()
  }
  /**
   * @description 传进商品编码，按原顺序返回商品详细信息
   * @param { { goodsSn: string }[] } goodsList
   * @param { ComplateInfoFilter } [filter]
   */
  async getComplateGoodsList(goodsList, filter = {}) {
    try {
      return this.getGoodsComplateInfoList(goodsList, filter)
    } catch(err) {
      console.err(err)
      return []
    }
  }
  /**
   * @description 传进商品编码，返回商品详细信息map
   * @param { any[] } goodsList
   * @param { ComplateInfoFilter } [filter]
   */
  async getGoodsComplateInfoMap(goodsList, filter = {}) {
    const goodsComplateList = await this.getGoodsComplateInfoList(goodsList, filter)
    return this._convertToGoodsMap(goodsComplateList)
  }
  /**
   * @description 将商品列表转换为map形式返回
   * @param {*} goodsList
   * @param {*} key goodsSn or eshopGoodsId
   * @returns
   */
  _convertToGoodsMap(goodsList = [], key = 'goodsSn') {
    const goodsMap = new Map
    goodsList.forEach( goods => {
      goodsMap.set(String(goods[key]), goods)
    })
    return goodsMap
  }
  // 设置动态信息
  async setGoodsDynamicInfo(goodsBaseInfoList, options) {
    const { vipNoneFrozen, vipSpring } = await FreshGoods.freshStoreLabel
    const filter = options
    const filterAfterDefault = {
      ...filter,
      // 默认false,设置sortSoldOut为true时,强制将filterStock转为false
      filterStock: !filter.sortSoldOut && (('filterStock' in filter) && filter.filterStock),
      // 默认true,设置售罄情况下强制改成false
      filterSpring: !filter.emptySpringStock && (!('filterSpring' in filter) || filter.filterSpring),
      // 默认true,设置售罄情况下强制改成false
      filterFrozen: !filter.emptyFrozenStock && (!('filterFrozen' in filter) || filter.filterFrozen),
    }
    const storeFilter = [
      // 是否需要执行冷冻品相关逻辑
      {
        keys: ['filterFrozen', 'emptyFrozenStock'],
        // 当前门店在vipNoneFrozen中,则需要执行冷冻品相关的过滤/售罄逻辑
        condition: vipNoneFrozen.includes(this.storeCode)
      },
      // 是否需要执行试春盘相关逻辑
      {
        keys: ['filterSpring', 'emptySpringStock'],
        // 当前门店在vipSpring中,则需要执行试春盘相关的过滤/售罄逻辑
        condition: vipSpring.includes(this.storeCode)
      },
    ].reduce(function(map, { keys, condition }) {
      keys.reduce((map, key) => {
        map[key] = condition && filterAfterDefault[key]
        return map
      }, map)
      return map
    }, {})
    const finalFilter = Object.assign(filterAfterDefault, storeFilter)
    const complateInfoList = await this.getGoodsDynamicInfo(goodsBaseInfoList, finalFilter)
    return finalFilter.sortSoldOut ? complateInfoList.reduce(function(result, item) {
      const [normal, soldOut] = result
      ;(item.stock ? normal : soldOut).push(item)
      return result
    }, [[], []]).reduce(function(list, subList) {
      list.push(...subList)
      return list
    }, []) : complateInfoList
  }
  /**
   *
   * @param { { goodsSn: string }[] } goodsSnList
   * @param { ComplateInfoFilter } options
   */
  async getGoodsComplateInfoList(goodsSnList, options = {}) {
    if (!goodsSnList.length) { return [] }
    // 获取基本信息
    const goodsBaseInfoList = await this.getGoodsBaseInfoList(goodsSnList)
    // 设置动态信息
    return this.setGoodsDynamicInfo(goodsBaseInfoList, options)

  }
  /**
   * @desc 获取Dynamic参数
   * @param { any[] } list
   * @returns { CosGoodsItem[] }
   */
  _getDynamicParams(list) {
    const { userID } = wx.getStorageSync('user') || {}
    return {
      cityCode: this.cityCode,
      storeCode: this.storeCode,
      deliveryCenterCode: this.deliveryCenterCode,
      customerID: userID || '-1',
      goodsList: list.map(({ goodsSn, associationList, beginTakeTime, eshopGoodsId, goodsRestrictions }) => ({
        goodsSn,
        basicGoodsSn: (associationList || []).map(v => v.basicGoodsSn),
        beginTakeTime,
        eshopGoodsId,
        goodsRestrictions
      }))
    }
  }
  /**
   * @desc 查询cos中没有的商品信息
   * @param { any[] } goodsBaseInfoList
   * @param { ComplateInfoFilter } [filter]
   */
  async getGoodsDynamicInfo(goodsBaseInfoList, filter) {
    const {
      filterGift,
      filterNewCustomer,
      filterSaleStatus,
      filterStock,
      filterPreSale,
      beginTakeTime,
      filterCoupon,
      filterSpring,
      filterFrozen,
    } = filter
    // console.log('goodsBaseInfoList', goodsBaseInfoList)
    const baseInfoAfterFilter = goodsBaseInfoList.filter(v => {
      return ((!filterGift) || !v.isGift) // 过滤赠品
      && ((!filterNewCustomer) || !v.isNewCustomerAvailable) // 过滤新客
      && ((!filterCoupon) || v.isSupportCoupon === 'Y') // 过滤掉不可用券
      && ((!(beginTakeTime && v.beginTakeTime)) || v.beginTakeTime.slice(0, 10) === beginTakeTime.slice(0, 10)) // 预售品(有提货时间)按提货时间过滤
      && ((!filterSpring) || !v.vipSpringFilter) // 过滤试春盘商品
      && ((!filterFrozen) || !v.vipNoneFrozenFilter) // 过滤冷冻品
      && (v.retailPrice && v.retailPrice > 0) // 过滤会员价异常的商品
    })
    // console.log('baseInfoAfterFilter', baseInfoAfterFilter)
    if (!baseInfoAfterFilter.length) {
      return []
    }
    // baseInfoAfterFilter去重
    const goodsSnUni = {}
    const uniBaseInfoList = baseInfoAfterFilter.filter(v => {
      return goodsSnUni[v.goodsSn] !== false && !(goodsSnUni[v.goodsSn] = false)
    })
    const { data: dynamicInfo } = await bgxxApi.getFreshGoodsDynamicInfo(this._getDynamicParams(uniBaseInfoList))
    const goodsListWithDynamicInfo = uniBaseInfoList.map(baseInfo => new AssembleGoods({ baseInfo, dynamicInfo, filter, userInfo: this.userInfo }).getDetail()).filter(v => {
      return ((!filterSaleStatus) || v.saleStatus) // 过滤下架
      && ((!filterStock) || v.stock) // 过滤售罄
      && ((!filterPreSale) || v.saleType === 'N') // 过滤预售
    })
    // console.log('goodsListWithDynamicInfo', goodsListWithDynamicInfo)
    return goodsListWithDynamicInfo
  }
  matchGoodsDetailList(goodsSnList) {
    const allStoreGoodsMap = cacheFreshGoods.getGoodsMap(this.cityCode)
    return goodsSnList.map( item => {
      // const goodsSn = item.goodsSn ? item.goodsSn : this.convertGoodsIdToGoodsSn(item.eshopGoodsId)
      const goodsSn = item.goodsSn
      const curGoods = deepClone(allStoreGoodsMap.get(goodsSn))
      if (!curGoods) {
        return
      }
      return curGoods
    }).filter( item => item)
  }
  /**
   * @description 从cos读取商品
   * @returns
   */
  async getGoodsMapByCos() {
    // 获取商品基本信息
    const readGoodsInfoByCos = new ReadGoodsInfoByCos({ logger: ENV !== 'prod' })
    const readKey = `simple-${this.cityCode}-10`
    try {
      const res = await readGoodsInfoByCos.readAsyncWithCache(readKey)
      return res
    } catch(err) {
      console.error('getGoodsMapByCos', err)
      return []
    }
  }
  async getCurStoreGoodsMap() {
    try {
      const handleGooodsFn = async () => {
        // 没有缓存，走网络请求获取
        const cosGoodsList = await this.getGoodsMapByCos()
        // 缓存起来，转换为map
        const goodsMap = cacheFreshGoods.setGoodsMap(this.cityCode, deepClone(cosGoodsList))
        this.getGoodsPromiseMap.delete(this.cityCode)
        return goodsMap
      }
      const promisifyHandle = () => {
        if (this.getGoodsPromiseMap.has(this.cityCode)) {
          return this.getGoodsPromiseMap.get(this.cityCode)
        }
        this.getGoodsPromiseMap.set(this.cityCode, handleGooodsFn())
        return this.getGoodsPromiseMap.get(this.cityCode)
      }
      // 先读取缓存
      const curStoreGoods = cacheFreshGoods.getGoodsMap(this.cityCode)
      if (curStoreGoods.size) {
        if (cacheFreshGoods.isCacheExpired(this.cityCode)) setTimeout(promisifyHandle)
        return curStoreGoods
      }
      return promisifyHandle()
    } catch(err) {
      return new Map()
    }
  }
    /**
   * @description 获取商品的基本信息
   * @param {*} goodsSnList
   * @returns
   */
  async getGoodsBaseInfoList(goodsSnList) {
    const curStoreGoodsMap = await this.getCurStoreGoodsMap()
    const noMatchGoodsList = []
    goodsSnList.forEach( item => {
      // const goodsSn = item.goodsSn ? item.goodsSn : this.convertGoodsIdToGoodsSn(item.eshopGoodsId)
      const goodsSn = item.goodsSn
      if (!goodsSn || !curStoreGoodsMap.has(goodsSn)) {
        noMatchGoodsList.push(item)
      }
    })
    // cos没有查到的商品走接口查询{
    if (noMatchGoodsList.length > 0) {
      const goodsList = await this.getGoodsDetailList(noMatchGoodsList)
      cacheFreshGoods.setGoodsMap(this.cityCode, deepClone(goodsList))
    }
    const goodsBaseInfoList = this.matchGoodsDetailList(goodsSnList)
    return goodsBaseInfoList
  }
  async getGoodsDetailList(goodsList) {

    const params = {
      cityCode: this.cityCode,
      storeCode: this.storeCode,
      goodsList: goodsList,
    }
    try {
      const result = await bgxxApi.getFreshGoodsDetailList(params)
      return result.data
    } catch(err) {
      console.error(err)
      return []
    }
  }
  // 为了适配goodspreview组件
  forMatGoodsToGoodsPreview(list){
    return list.map((item)=>{
      const {resourceList = null, mainImgUrl, shelfStatus ,storageMethod, isNewCustomerAvailable,retailPrice,heartPrice,goodsName,subtitle,stock} = item
      const recommendPic =  resourceList && resourceList.find( item => item.type === 12 )// 12 为商品主推图
        item['headPic'] = mainImgUrl
        // item['recommendPic'] = recommendPic.url
        item['shelfStatus'] = shelfStatus
        item['storageType'] = storageMethod
        item['isNewExclusive'] = isNewCustomerAvailable ===  0  ? 'N': 'Y'
        item['name'] = goodsName
        item['bidPrice'] = retailPrice
        item['vipPrice'] = heartPrice
        item['subTitle'] = subtitle
        item['stockNum'] = stock
        // _.unset(item,'resourceList')
      return item
     })
  }
}
module.exports = {
  FreshGoods
}
