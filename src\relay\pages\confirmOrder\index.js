import { fruitOrderPay } from '../../../utils/services/fruitOrderPay'
import { relayOrderSubmitTemplateIds, reportTmplIds } from '../../../mixins/ticketEntry'
import { storeSubscribe } from '../common/storeSubscribe'
import { pageTransfer } from '~/utils/route/pageTransfer'

const CART_LIST = 'relay.detail.cartList'
const CART_CLEAR = 'relay.confirmOrder.clearCart'
const app = getApp()
const util = require('../../../utils/util')
const {
  commonObj
} = require('../../../source/js/common')
const { handlePickupTime } = require('../common/orderGoods')
const { relayConfirmOrderMap } = require('../../sensorReportData')
const sensors = require('../../../utils/report/sensors')
const SMSMixin = require('../../../mixins/SMSMixin')
import { getStoreBusinessTime } from '../../../utils/util'
import { relayCartStore } from '../home/<USER>/inedx'
import { handleGoodsLabel } from '~/utils/goods/label'
import { getTimelyDistributor } from '../../../service/userService'
const log = require('../../../utils/log.js')
Page({
  mixins: [SMSMixin],
  /**
   * 页面的初始数据
   */
  data: {
    groupGoodsList: [], // 商品分组列表
    unAvailableGoosList: [], // 失效商品列表
    balanceInfo: { // 钱包余额
      count: 0,
      enough: true,
    },
    payType: 0, // 支付类型：0-钱包支付 1-微信支付
    orderAmount: 0, // 订单金额
    payAmount: 0, // 实付金额
    store: {}, // 门店信息
    uuid: '', // 结算标示
    cityInfo: {}, // 城市信息
    rechargeText: '', // 充值活动
    cartData: {}, // 购物车数据
    selectPagodaPay: true, // 余额支付
    selectWxPay: false, // 微信支付
    selectUnionPay: false, // 云闪付支付
    // 订阅消息相关
    subscribe: {
      // 是否展示订阅
      show: false,
      // 模板列表
      tmplIds: relayOrderSubmitTemplateIds
    },
    goodsCode2CartItem: {},
  },
  _data: {
    isOnLoadSettle: true,
    isCanSubmit: true, // 是否可以提交订单
    // 接龙的活动信息
    detailMap: {},
    // 购物车列表
    cartList: [],
    // 接龙活动信息
    activityInfo: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.on(CART_LIST, this.onGetCartList)
  },

  onUnload() {
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.off(CART_LIST, this.onGetCartList)
    // 退出确认订单页
    const sensorReporData = relayConfirmOrderMap['LEFT_PAGE']
    sensorReporData && sensors.pageShow('', sensorReporData)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    storeSubscribe.checkAllAccept()
    // 进入确认订单页
    const sensorReporData = relayConfirmOrderMap['ENTER_PAGE']
    const { element_code = '', element_content = '', element_name = '' } = sensorReporData || {}
    sensors.pageScreenView({
      element_code,
      element_content,
      element_name
    })
  },
  /**
   * 获取接龙详情返回的购物车数据
   */
  onGetCartList(data) {
    // console.log(data)
    this._data.cartList = data.cartList
    this._data.detailMap = data.detailMap
    this.buildSettleParams(data)
    this.setData({
      cartData: data
    })
  },

  /** @desc 用于商品数量改变后,重新结算 */
  reBuildSettleParams: util.debounce(function() {
    this.buildSettleParams(this.data.cartData)
  }, 500),

  /**
   * 构造结算接口的入参
   */
  buildSettleParams(data) {

    const {
      cartList = [], store = {}
    } = data || {}
    this._data.cartList = cartList
    // 取值格式：{"SOPT2024103000001": ["GQ10004234324", "GQ100042125433"]}
    this._data.activityInfo = cartList.reduce((prev, cartItem) => {
      cartItem.forEach(item => {
        if (!prev[item.activityCode]) {
          prev[item.activityCode] = []
        }
        prev[item.activityCode].push(item.goodsCode)
      })
      return prev
    }, {})
    const params = {
      customerID: app.globalData.customerID || -1,
      cityID: store.cityID,
      storeCode: store.storeCode,
      relayGoodsList: [],
      // 取值格式：{"SOPT2024103000001": ["GQ10004234324", "GQ100042125433"]}
      activityInfo: this._data.activityInfo
    }
    // 所有商品列表
    params.relayGoodsList = cartList.flatMap(actItem =>
      actItem.map(item => ({
        goodsSn: item.goodsCode,
        count: item.count
      }))
    )
    // 调用结算接口
    this.relayOrderSettle(params)
  },

  /**
   * 接龙订单结算
   */
  async relayOrderSettle(settleParams) {
    try {
      if (!this._data.isCanSubmit) {
        return
      }
      this._data.isCanSubmit = false
      const settleId = Number.parseInt(String(Math.random() * 1000))
      this._data.settleId = settleId
      const res = await app.api.relayOrderSettleCombine(settleParams)
      // 防止重复请求
      if (settleId !== this._data.settleId) {
        return
      }
      // const res = dataSettle
      const {
        mainBalance,
        payAmount,
        orderAmount,
        goodsList = [],
        unAvailableGoosList = [],
        store = {},
        uuid,
        cityCode,
        deliveryCenterCode
      } = res.data || {}
      const goodsDetailList = this.setGoodsDetailInfo(goodsList)
      // 分组
      const { goodsList: groupGoodsList, goodsCode2CartItem, emptyTips, } = this.groupGoods(goodsDetailList, unAvailableGoosList)
      const unAvailableGoodsDetailList = this.setGoodsDetailInfo(unAvailableGoosList)
      Object.assign(store,{
        storeBusinessTime:getStoreBusinessTime({
          startTime:store.startTime||'',
          endTime:store.endTime||'',
          openingTime:store.openingTime||'',
        })
      })
      const isEnough = mainBalance >= payAmount
      this.setData({
        goodsCode2CartItem,
        balanceInfo: {
          count: mainBalance,
          enough: isEnough
        },
        // payType: mainBalance >= payAmount ? 0 : 1,
        selectPagodaPay: mainBalance !== 0,
        selectWxPay: !isEnough,
        selectUnionPay: false,
        payAmount,
        orderAmount,
        store,
        uuid,
        cityInfo: {
          cityID: store.cityID,
          cityCode,
          deliveryCenterCode
        },
        groupGoodsList,
        unAvailableGoosList: unAvailableGoodsDetailList
      })
      // 余额不够时，查询充值活动
      if (mainBalance < payAmount) {
        this.rechargeGuide (store.id)
      }
      // 存在失效的商品给出弹窗
      if (unAvailableGoosList.length > 0) {
        emptyTips && !this._data.isOnLoadSettle
          ? wx.showToast({
            icon: 'none',
            title: emptyTips,
          })
          : wx.showModal({
            title: '提示',
            content: '部分商品已失效或库存不足，请确认您的订单',
            showCancel: false,
            confirmText: '我知道了'
          })
      }
    } catch (error) {
      this.handleSettleError(error)
    }
    this._data.isCanSubmit = true
    this._data.isOnLoadSettle = false
  },

  /**
   * 处理结算异常
   */
  handleSettleError(error) {
    const { errorCode } = error
    // 活动已结束
    if(Number(errorCode) === -100) {
      wx.showModal({
        title: '提示',
        content: '活动结束啦/去其他活动看看吧',
        showCancel: false,
        confirmText: '我知道了',
        success(res) {
          if (res.confirm) {
            wx.redirectTo({
              url: '/relay/pages/home/<USER>'
            })
          }
        }
      })
    } else {
      app.apiErrorDialog(error)
    }
  },

  /**
   * 将提货时间一致的商品进行分组
   * @param {Array<any>} resGoodsList 商品列表
   */
  groupGoods(resGoodsList = [], unAvailableGoosList = []) {
    const { cartList, detailMap } = this._data
    const goodsCode2CartItem = {}
    const unAvailableGoosListMap = new Map(unAvailableGoosList.map(item => [item.goodsSn, item]))
    // 扁平化并创建一个商品代码到商品对象的映射，方便后续查找
    const cartItemMap = new Map(resGoodsList.map(item => [item.goodsSn, item]))
    // 创建一个空数组来存储最终的结果
    const result = []
    let emptyTips = ''
    // 使用 Object.keys 遍历 detailMap 中的每个活动的 ID
    cartList.flat().forEach(cartItem => {
      const unAvailableGoosItem = unAvailableGoosListMap.get(cartItem.goodsCode)
      if (unAvailableGoosItem) {
        cartItem.count = Math.max(0, cartItem.count - unAvailableGoosItem.count)
        cartItem.stockNum = cartItem.count
        // 如果是store中的购物车加购项
        if(!cartItem.fromBuyNow) {
          const detailStore = relayCartStore.getDetailStore(cartItem.activityCode, { useCacheOnly: true })
          detailStore && detailStore.updateCart({
            goodsCode: cartItem.goodsCode,
            variation: Math.min(0, cartItem.count - detailStore.getCartNum({ goodsCode: cartItem.goodsCode })),
            noAddShoppingCartReport: true,
          })
        }
        // 如果cartItem.count还有数量
        // 说明是已加购的数量被别人购买掉一部分的场景
        // 按产品设计,需要toast提示库存不足
        emptyTips = emptyTips || (cartItem.count ? '库存不足' : '')
      }
      goodsCode2CartItem[cartItem.goodsCode] = cartItem

      // 如果当前商品不在有效商品列表 或者失效了 则直接返回
      if (!cartItemMap.has(cartItem.goodsCode)) {
        return
      }
      const currentActivityGood = cartItemMap.get(cartItem.goodsCode)
      const activity = detailMap[cartItem.activityCode]
      // 提取 pickupStart 的日期部分
      const pickupStartDate = activity.pickupStart.split(' ')[0]
      // 提取 pickupEnd 的日期部分
      const pickupEndDate = activity.pickupEnd.split(' ')[0]

      // 检查是否已有相同日期段的活动在结果数组中
      const existingEntry = result.find(entry => {
        const entryStartDate = entry.pickupStart.split(' ')[0]
        const entryEndDate = entry.pickupEnd.split(' ')[0]
        return entryStartDate === pickupStartDate && entryEndDate === pickupEndDate
      })
      // 如果找到了相同日期段的活动，则将当前活动的商品添加到该活动的goodsList中
      if (existingEntry) {
        existingEntry.goodsList.push(currentActivityGood)
      } else {
        // 如果没有找到，则创建一个新的对象，包含activityId和goodsList，并将其添加到结果数组中
        const pickupTimeStr = handlePickupTime(activity.pickupStart, activity.pickupEnd)
        result.push({
          activityId: cartItem.activityCode,
          pickupStart: activity.pickupStart,
          pickupEnd: activity.pickupEnd,
          pickupTimeStr,
          goodsList: [currentActivityGood]
        })
      }
    })
    // 按照提货时间进行先后排序
    return {
      emptyTips,
      goodsCode2CartItem,
      goodsList: result.sort((a, b) => {
        return new Date(a.pickupEnd.replace(/-/g, '/')).getTime() - new Date(b.pickupEnd.replace(/-/g, '/')).getTime()
      }).sort((a, b) => {
        return new Date(a.pickupStart.replace(/-/g, '/')).getTime() - new Date(b.pickupStart.replace(/-/g, '/')).getTime()
      })
    }
  },

  /**
   * 将结算的商品的部分信息补全，需要在页面上展示
   */
  setGoodsDetailInfo(goods) {
    const {
      cartList = []
    } = this._data
    goods.forEach(goods => {
      cartList.flat().forEach(item => {
        if (String(goods.goodsSn) === String(item.goodsCode)) {
          goods.goodsPricePer = item.goodsPricePer
          goods.headImg = item.headImg
          // 营销接口返回的商品原价
          goods.goodsPrice = item.goodsPrice
        }
      })
      goods.headImg = util.toHttps(goods.headImg)
    })
    return goods
  },
  /**
   * 订阅消息关闭事件
   */
  onSubscribeClose({ detail }) {
    // 关闭订阅弹窗
    this.setData({ 'subscribe.show': false })
    this.submitRelayOrder()
    // 上报到触达域
    detail && reportTmplIds(detail.resultStatus)
  },

  /**
   * 接龙订单下单
   */
  async submitRelayOrder() {
    try {
      if (!this._data.isCanSubmit) return
      this._data.isCanSubmit = false
      const submitParams = this.buildSubmitParams()
      wx.showLoading({
        title: '支付中...',
        mask: true
      })
      const res = await app.api.submitRelayOrderCombine(submitParams, true)
      const result = res.data || {}
      this.payRequest(result)
      this.submitOrderSuccessReport(result)
    } catch (error) {
      this._data.isCanSubmit = true
      wx.hideLoading()
      this.handleConfirmOrderError(error)
    }
  },

  /**
   * 构造下单接口的入参
   */
  buildSubmitParams() {
    const {
      cityInfo = {},
      uuid,
      orderAmount,
      payAmount,
      store = {},
      groupGoodsList
    } = this.data
    const { activityInfo, cartList, detailMap } = this._data
    // 构造活动提货时间
    const activityPickupTime = cartList.reduce((prev, cartItem) => {
      cartItem.forEach(item => {
        const currentAct = detailMap[item.activityCode]
        if (currentAct) {
          prev[item.activityCode] = {
            timeBegin: currentAct.pickupStart,
            timeEnd: currentAct.pickupEnd
          }
        }
      })
      return prev
    }, {})
    console.log(store)
    const submitGoodsList = []
    // 取出所有的可下单的商品并合并
    groupGoodsList.forEach(groupList => {
      groupList.goodsList.forEach(item => {
        submitGoodsList.push({
          goodsSn: item.goodsSn,
          goodsId: item.goodsId,
          quantity: item.count,
          goodsPricePer: item.goodsPricePer
        })
      })
    })
    const params = {
      customerID: app.globalData.customerID || -1,
      cityID: cityInfo.cityID,
      cityCode: cityInfo.cityCode,
      deliveryCenterCode: cityInfo.deliveryCenterCode,
      storeCode: store.storeCode || store.number,
      storeID: store.id,
      orderInfoList: [{
        goodsList: submitGoodsList
      }],
      activityInfo,
      activityPickupTime,
      payAmount,
      orderAmount,
      uuid,
      parentDistributor: getTimelyDistributor(),
    }
    // 4.0 增加分销渠道 24h有效期
    const distributionChannel = app.getCacheQueryParams('utmSource', 'channel', 86400000 );
    if (distributionChannel) {
      params.distributionChannel = distributionChannel
    }
    return params
  },

  /**
   * 处理下单异常
   */
  handleConfirmOrderError(error) {
    const { cartData } = this.data
    const { errorCode, description } = error
    // 活动已结束
    if(errorCode === 450301 || errorCode === 450024) {
      console.log(description)
    }
    // 这里的下单异常拦截,都延后到buildSettleParams中再报错
    // 避免在这报错了,重复在buildSettleParams中报错
    // app.apiErrorDialog(error)
    this.buildSettleParams(cartData)
  },
  /**
   * 提交订单
   */
  submitForm: util.throttle(async function () {
    storeSubscribe.addMessagetimes()
    const {
      payAmount,
      selectPagodaPay,
      selectWxPay,
      selectUnionPay,
    } = this.data
    if(Number(payAmount) <= 0) {
      return
    }
    const that = this
    if (selectPagodaPay && !(selectWxPay || selectUnionPay)) {
      // 校验是否需要进入验证码环节
      const isNeedValidate = await that.checkPayDevice()

      if (!isNeedValidate) {
        // 缓存一下提交订单的请求
        that._data.tmpConfirmHandle = that.submitRelayOrder
        // 弹出验证码输入框
        this.showSmsValidate()
        return
      }
      commonObj.showModal('提示', `确认使用会员钱包支付${Number((payAmount / 100).toFixed(2))}元吗？`, true, '确认', '取消', function (res) {
        if (res.confirm) {
          that.confirmHandle()
        }
      })
    } else {
      this.confirmHandle()
    }
    // 点击支付
    sensors.trackClickEvent(relayConfirmOrderMap.CLICK_PAY)
  }, 2000),

  /**
   * 调起订阅消息界面
   */
   confirmHandle(){
    // 触发订阅弹窗
    this.setData({ 'subscribe.show': true })
   },
  /**
   * 支付请求
   *
   */
  payRequest(orderData) {
    // 神策防黑产埋点
    setTimeout(()=> {
      orderData.subOrderNos.forEach(orderNo => sensors.safeGuardSensor('pay', { orderNo, orderType: 'RELAY' }))
    }, 1000)

    const { selectPagodaPay, selectWxPay, selectUnionPay, balanceInfo } = this.data;
    const { payAmount, mainOrderNo } = orderData

    const payInfo = {
      usePagodaPay: selectPagodaPay,
      useWxPay: selectWxPay,
      useUnionPay: selectUnionPay,
      paymentAmount: payAmount,
      tradeNo: mainOrderNo,
      mainOrderNo,
      mainBalance: balanceInfo.count
    }
    const succCb = this.openWaitPay.bind(this, orderData)
    const failCb = this.failCallback.bind(this, orderData)
    const extraInfo = {
      succCb,
      failCb
    }
    return fruitOrderPay.handlePay(payInfo, extraInfo)
  },

  failCallback (orderData, failReason) {
    console.log('failReason', orderData, failReason);
    const { payType, err } = failReason
    if (fruitOrderPay.isThirdPay(payType)) {
      console.log('支付失败回调')
      this.openOrderDetail(orderData)
      log.error('useWXPayFailed', err)
    } else if (fruitOrderPay.isPagodaPay(payType)) {
      wx.showToast({
        title: '支付失败，请重试',
        duration: 1500,
        icon: 'none'
      })
      setTimeout(() => {
        this.openOrderDetail(orderData)
      }, 1500)
    }
  },

  /**
   * 跳转支付结果查询页
   */
  openWaitPay(orderData, payData) {
    console.log(payData)
    const {
      mainOrderNo, subOrderNos = []
    } = orderData
    const { data: { payNo, payNos } } = payData
    const { store, groupGoodsList } = this.data
    // 获取所有活动开始结束时间
    const arrStartTime = []
    const arrEndTime = []
    groupGoodsList.forEach(item => {
      arrStartTime.push(item.pickupStart)
      arrEndTime.push(item.pickupEnd)
    })
    // 排序开始时间 第一个最早
    arrStartTime.sort((a,b)=>{
      return new Date(a.replace(/-/g, '/')).getTime() - new Date(b.replace(/-/g, '/')).getTime()
    })
    // 排序结束时间 第一个最晚
    arrEndTime.sort((a,b)=>{
      return new Date(b.replace(/-/g, '/')).getTime() - new Date(a.replace(/-/g, '/')).getTime()
    })
    const orderDetailObj = {
      customerID: app.globalData.customerID || -1,
      mainOrderNo,
      subOrderNos,
      payNo,
      payNos,
      storeName: store.shortName || store.name,
      storeCode: store.storeCode || store.number,
      pickupStart: arrStartTime[0],
      pickupEnd: arrEndTime[0],
      // 活动编码取结算可用的商品第一个商品的活动编码
      activityCode: groupGoodsList[0].activityId,
      shareImg: groupGoodsList[0].goodsList[0].headImg,
      goodsCount: groupGoodsList.reduce((sum,item)=>{
        return sum += item.goodsList.reduce((sum2,item2)=>{
          return sum2 + item2.count
        }, 0)
      }, 0)
    }
    pageTransfer.send(orderDetailObj)
    // 支付成功跳转
    wx.redirectTo({
      url: '/relay/pages/waitPay/index'
    })
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.emit(CART_CLEAR, orderDetailObj)
  },

  /**
   * 跳转订单详情
   */
  openOrderDetail(orderData) {
    const {
      mainOrderNo, subOrderNos = []
    } = orderData
    // 如果只有一个子订单，则直接跳转到订单详情
    if (subOrderNos.length === 1 && subOrderNos[0] === mainOrderNo) {
      const orderObj = {
        orderNo: mainOrderNo || '',
      }
      wx.redirectTo({
        url: '/relay/pages/orderDetail/index?orderObj=' + JSON.stringify(orderObj)
      })
    } else {
      // 跳转到接龙订单列表
      wx.redirectTo({
        url: '/userB/pages/orderList/index?type=D'
      })
    }
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.emit(CART_CLEAR)
  },

  /**
   * 获取下单充值引导活动信息
   */
  rechargeGuide(storeID) {
    const param = {
      storeID,
      amount: this.data.payAmount,
      customerID: app.globalData.customerID || -1
    }
    app.api.rechargeGuide(param).then(res => {
      const rechargeText = (res.data || {}).depositText || ''
      this.setData({rechargeText})
    }).catch((res) => {
      console.error('res', res)
    })
  },
  switchStore(){
    // 切换门店
    const activityCode = this.data.groupGoodsList[0].activityId
    wx.navigateTo({
        url: `/fightGroups/pages/selfExtractStore/index?formPage=selfTakeConfirmOrder&pageOrgin=relay&activityCode=${activityCode}&isfromRelayConfirmOrder=true`
    })
  },
  refresh(store){
      const { cartData } = this.data
      const data = Object.assign({}, cartData, { store })
      this.setData({
        store
      })
      wx.nextTick(() => {
        this.onGetCartList(data)
      })
  },
  // 余额支付开关
  switchPagodaPayChangeHandle(e) {
    const { selectPagodaPay } = e.detail
    this.setData({
      selectPagodaPay,
    })
  },
  // 设置微信支付
  setWaySelectHandle(e) {
    const { selectWxPay, selectUnionPay } = e.detail
    this.setData({
      selectWxPay,
      selectUnionPay,
    });
  },
  /**
   * 提交订单成功后埋点上报
   */
  submitOrderSuccessReport(result) {
    try {
      const { groupGoodsList } = this.data
      const submitParams = this.buildSubmitParams()
      const { orderInfoList = [], orderAmount, payAmount } = submitParams || {}
      const { goodsList = [] } = orderInfoList[0] || {}
      const { mainOrderNo, subOrderNos = [] } = result || {}
      const isSplitOrder = (subOrderNos.length > 1 || subOrderNos[0] !== mainOrderNo) ? 1 : 0
      const skuNum = goodsList.reduce((sum, cur) => {
        return sum + (cur.quantity || 0)
      }, 0)
      const SKU_ID = []
      const SKU_Name = []
      const spuId = []
      groupGoodsList.forEach(group => {
        const { goodsList = [] } = group || {}
        goodsList.forEach(item => {
          const { goodsSn, goodsName, spuNumber } = item || {}
          SKU_ID.push(goodsSn)
          SKU_Name.push(goodsName)
          spuId.push(spuNumber)
        })
      })
      const reportData = {
        mainOrderNo,
        OrderType: '接龙',
        DeliveryWay: '自提',
        sku_cnt: skuNum,
        orderAmount: util.formatPrice(orderAmount),
        actualPaymentAmount: util.formatPrice(payAmount),
        couponType: [],
        couponBatchNumList: [],
        couponNameList: [],
        SKU_ID,
        SKU_Name,
        spuId,
        // 是否拆单
        isSplitOrder
      }
      sensors.track('SubmitOrder', reportData)
      // 订单详情埋点上报
      const arrOrderDetailReportData = []
      groupGoodsList.forEach(group => {
        const { goodsList = [] } = group || {}
        goodsList.forEach(item => {
          const { goodsSn, goodsName } = item || {}
          const goods_label = handleGoodsLabel({
            goodsObj: item,
            goodsInCart: true,
            isShowVipPrice: false,
            isShowPurchaseLimit: false,
            isNeedActivityLabel: false,
            showLabelNum: 9,
            needFilterLabelTypes: []
          })
          arrOrderDetailReportData.push({
            mainOrderNo,
            OrderType: '接龙',
            DeliveryWay: '自提',
            SKU_ID: goodsSn,
            SKU_Name: goodsName,
            goods_label,
            isSplitOrder
          })
        })
      })
      // 上报埋点
      arrOrderDetailReportData.forEach(item => sensors.track('SubmitOrderDetail', item))
    } catch(e) {
      console.error(e)
    }
  },
  onCartNumChange({ detail }) {
    const { goodsCode, cartNum } = detail
    this.setData({
      [`goodsCode2CartItem.${goodsCode}.count`]: cartNum
    })
    this.reBuildSettleParams()
  },
})
