import shopCartStore from '../../../stores/shopCartStore';
import { WE_CHAT, PAGODA, MIXIN_PAY, fruitOrderPay } from '../../../utils/services/fruitOrderPay'
import { pageTransfer } from '~/utils/route/pageTransfer';
import { getOrderInlineRechargeInfo } from '../../../utils/order'
const app = getApp()
const commonObj = require('../../../source/js/common').commonObj;
const util = require('../../../utils/util')
const log = require('../../../utils/log.js')

const { wrapSensorReportFn } = require('../../../utils/report/index')
// const { clickParamsMap } = require('../orderList/sensorClickParams')
// const clickParamsFn = genClickSensor(clickParamsMap)
const { checkPayDevice } = require('../../../service/userService')
const { ORDER_TYPE, ORDER_CREATE_SOURCE } = require('../../../source/const/order')
const txSensor = require('../../../utils/report/txSensor');
const { clickReport } = require('../../../utils/report/sensors');
const sms = require.async('../../../sourceSubPackage/commonUtils/sms/index')

function toOrderDetailPay(myAllOrderObj) {
  wx.navigateTo({
    url: '/userB/pages/allOrderDetail/index?myAllOrderObj=' + JSON.stringify(myAllOrderObj)
  })
}

module.exports = {
  data: {
    // 是否显示短信验证码输入框
    visibleSMS: false,
    forbidPdPay: false,
    selectWxPay: false, // 微信支付
    selectUnionPay: false, // 云闪付支付
    disableUnionPay: false, // 禁用云闪付(目前是及时达订单有月卡的情况下)
    hideUnionPay: true, // 隐藏云闪付(全国送隐藏云闪付)
    disablePay: false //  禁用支付
  },
  _data: {
    curOrderConsumables: [],
    tmpConfirmHandle: null
  },
  hidePaymentWay() {
    this.setData({
      disableUnionPay: false,
      paymentDialog: false
    })
  },
  // 选择支付弹窗
  stopBubbling() {
    return
  },
  // v2.3 获取下单充值引导活动信息
  rechargeGuide(storeID, amount, userID) {
    const param = {
      storeID,
      amount,
      customerID: userID,
    }
    app.api.rechargeGuide(param).then(res => {
      const rechargeText = res.data.depositText
      this.setData({
        rechargeText
      })
    }).catch((res) => {
      console.error('rechargeGuide', res)
    })
  },
  // 神策上报点击事件
  trackClickEvent(params = {}) {
    if (app.globalData.reportSensors) {
      clickReport(params)
    }
  },
  // 获取余额
  async getWalletAmount(originPayAmount, rechargeInfo) {
    const { user, timelyCity } = this.data
    try {
      const { data: resData } = await app.api.getBalanceNew({
        customerID: user.userID,
        isNeedBalance: true
      })
      const {
        walletAmount = 0
      } = resData
      const totalAmount = walletAmount
      const lack = originPayAmount > totalAmount
      // 开通年卡可使用余额
      const forbidPdPay = false
      this.setData(Object.assign(
        {
          forbidPdPay,
          lack: lack,
          mainBalance: Number(totalAmount)
        },
        this.setPayWay({
          lack,
          totalAmount,
          rechargeInfo,
          forbidPdPay,
        }),
        // 随单充值情况
        rechargeInfo
          ? {
            //  必须选择钱包支付
            selectPagodaPay: true,
            //  必须选择微信支付
            selectWxPay: true,
            //  不能云闪付钱包支付
            disableUnionPay: true,
          }
          : {}
      ))
      lack && this.rechargeGuide(timelyCity.storeID, originPayAmount, user.userID)
    } catch (error) {
      console.log('getWalletAmount', error);
      wx.showModal({
        content: '钱包余额获取失败'
      })
    }
  },
  setPayWay ({lack, totalAmount, forbidPdPay }) {
    // 代金券刚好抵消支付金额
    const { payAmount } = this.data.target
    if (payAmount <= 0 && (this.data.isUseVoucher || this.data.isUseExchangeCard)) {
      return {
        pagodaMoney: false,
        selectWxPay: false,
        selectUnionPay: false,
        disablePay: true,
      }
    }
    // 余额为0 或者开通年卡, 则不支持余额支付
    if (totalAmount === 0 || forbidPdPay) {
      return {
        pagodaMoney: false,
        selectWxPay: true,
        selectUnionPay: false,
      }
    }
    // 余额不足，则支持混合支付
    if (lack) {
      return {
        pagodaMoney: true,
        selectWxPay: true,
        selectUnionPay: false,
      }
    }
    // 余额足，则默认余额支付
    // const selectPagodaPay = app.globalData.reqPayType === 0 ? true : false; // 默认余额支付，然后看余额是否足够支付
    const selectPagodaPay = true
    return {
      pagodaMoney: selectPagodaPay,
      selectWxPay: !selectPagodaPay,
      selectUnionPay: false,
    }
  },
  // 余额支付开关
  switchPagodaPayChangeHandle(e) {
    const { selectPagodaPay } = e.detail
    this.setData({
      pagodaMoney: selectPagodaPay
    })
  },
  // 设置微信支付
  setWaySelectHandle(e) {
    const { selectWxPay, selectUnionPay } = e.detail
    this.setData({
      selectWxPay,
      selectUnionPay,
    });
  },
  /**支付请求 */
  fetchPayApi: wrapSensorReportFn(async function () {
    const { pagodaMoney, orderClass, selectWxPay, selectUnionPay, mainBalance } = this.data
    const { curOrderInfo, isCombineOrder } = this._data
    // if (isCombineOrder && pagodaMoney) {
    //   wx.showToast({
    //     icon: 'none',
    //     title: '请选择支付渠道'
    //   })
    //   return
    // }
    const {
      originPayAmount,
      payment = {},
      orderTradeNo // 新订单用这个
    } = curOrderInfo

    const confirmHandle = async () => {

      const payInfo = {
        usePagodaPay: pagodaMoney,
        useWxPay: selectWxPay,
        useUnionPay: selectUnionPay,
        paymentAmount: originPayAmount,
        tradeNo: payment.tradeNo,
        mainOrderNo: orderTradeNo,
        isCombineOrder,
        mainBalance,
        isSelectVoucher: this.data.isUseVoucher,
        isSelectExchangeCard: this.data.isUseExchangeCard,
        rechargePayInfo: this.data.rechargePayInfo,
      }
      // A 及时达; F 全国送; D 接龙
      const fn = this[({
        A: 'fruitPaySuccCallback',
        F: 'fruitPaySuccCallback',
        D: 'relayPaySuccCallback'
      })[orderClass]]
      const succCb = fn.bind(this, curOrderInfo)

      const failCb = this.failCallback.bind(this, curOrderInfo)
      const extraInfo = {
        succCb,
        failCb
      }
      fruitOrderPay.handlePay(payInfo, extraInfo)


      return Object.assign(
        {},
        // 防黑产埋点
        {
          'safeGuardSensor': ['pay', { orderNo: curOrderInfo.orderNo, orderType: orderClass === 'D' ? 'RELAY' : 'FRUIT' }]
        }
      )
    }

    if (pagodaMoney && !(selectWxPay || selectUnionPay)) {
      // 校验是否需要进入验证码环节
      const isNeedValidate = await checkPayDevice()

      if (!isNeedValidate) {
        // 缓存一下提交订单的请求
        this._data.tmpConfirmHandle = confirmHandle
        // 弹出验证码输入框
        this.showSmsValidate()
        return
      }
      const showModalRes = await app.showModalPromise({
        content: `确认使用会员钱包支付${Number((originPayAmount / 100).toFixed(2))}元吗？`,
        showCancel: true,
        confirmText: '确认',
        cancelText: '取消',
      })
      if (!showModalRes) return
    }
    return confirmHandle()
  }),

  failCallback(orderData, failReason) {
    console.log('failReason', orderData, failReason);
    const { payType, err, payMessage } = failReason
    // 存在支付失败提示文案
    if (payMessage) {
      wx.showModal({
        content: payMessage,
        showCancel: false
      })
      return
    }

    if (fruitOrderPay.isThirdPay(payType)) {
      if (err.errMsg.indexOf('cancel') === -1) {
        // 非取消支付引起的支付失败
        wx.showToast({
          title: err.errMsg || '支付失败，稍后再试',
          icon: 'loading',
          duration: 2000,
          mask: true
        })
        const { phoneNumber } = wx.getStorageSync('user') || {}
        log.error('useWXPayFailed', err, phoneNumber)
      }
    } else if (fruitOrderPay.isPagodaPay(payType)) {
      wx.showToast({
        title: '支付失败，请重试',
        duration: 2000,
        icon: 'none'
      })
    }
  },

  /**
 * @desc 及时达支付成功回调;
   * 混合单详情是通过子订单号查的，跳转支付成功页需要用主订单号
   * 主订单号取 attachOrderNo,如果没有，说明不是混合单，子订单取 mainOrderNo ，此时同orderNo
   */
  fruitPaySuccCallback(orderData, payData) {
    const { attachOrderNo, mainOrderNo, rechargeInfo, orderNo: goodsOrderNo } = (orderData || {})
    const isMix = !['', '-'].includes(attachOrderNo)
    const orderNo = isMix ?  attachOrderNo : mainOrderNo
    const { data: { payNo, payNos }, payType } = payData
    wx.reportAnalytics('deposit_success')
    // 代金券0元支付直接进支付成功页
    // 并且不是随单充
    if(payType === 'zeroPay' && !rechargeInfo) {
      wx.redirectTo({
        url:
          `/homeDelivery/pages/paySuccess/index?paySuccessObj=${orderNo}&subOrderNo=${orderNo}`
      });
      return
    }
    const orderDetailObj = {
      customerID: app.globalData.customerID || -1,
      paymentOrderID: orderNo,
      // 如果是随单充值,使用商品订单的单号
      subOrderNo: rechargeInfo ? goodsOrderNo : orderNo,
      payNo: payNo,
      payNos,
      // 随单充值不算混合单
      isMix: isMix && !rechargeInfo,
      orderType: 'timelyOrder',
      isRecharge: Number(Boolean(rechargeInfo)),
    }
    wx.redirectTo({  //支付成功跳
      url: '/userA/pages/waitPay/index?orderDetailObj=' + JSON.stringify(orderDetailObj)
    })
    this.reportTXPay(orderData)
  },

  /**
   * 支付成功回调
   */
  relayPaySuccCallback(orderInfo, payData) {
    const { data: { payNo, payNos } } = payData
    const { orderTradeNo: mainOrderNo, store = {}, startDispatchTime, endDispatchTime, activityCode: randomActivityCode, preferentialList = [], items = [], mixOrderList = [] } = orderInfo
    let pickupStart = startDispatchTime
    let pickupEnd = endDispatchTime
    // 混合单，取最早开始时间和最晚结束时间
    if (mixOrderList.length) {
      // 获取所有活动开始结束时间
      const arrStartTime = []
      const arrEndTime = []
      mixOrderList.forEach(item => {
        arrStartTime.push(item.startDispatchTime)
        arrEndTime.push(item.endDispatchTime)
      })
      // 排序开始时间 第一个最早
      arrStartTime.sort((a,b)=>{
        return new Date(a.replace(/-/g, '/')).getTime() - new Date(b.replace(/-/g, '/')).getTime()
      })
      // 排序结束时间 第一个最晚
      arrEndTime.sort((a,b)=>{
        return new Date(b.replace(/-/g, '/')).getTime() - new Date(a.replace(/-/g, '/')).getTime()
      })
      pickupStart = arrStartTime[0]
      pickupEnd = arrEndTime[0]
    }
    let activityCode = randomActivityCode
    // 如果是多个活动的订单 则取第一个商品的活动信息
    if (preferentialList) {
      activityCode = preferentialList[0].preferentialCode
      if (preferentialList.length > 1) {
        try {
          const shareGoodsItemId = items[0]?.itemId
          const tmpActivityCode = preferentialList.find(item => item.relationId === shareGoodsItemId)?.preferentialCode
          activityCode = tmpActivityCode ? tmpActivityCode : activityCode
        } catch (error) {
          console.log('订单列表获取活动信息失败', error)
        }
      }
    }
    const orderDetailObj = {
      customerID: app.globalData.customerID || -1,
      mainOrderNo,
      isNotMixOrder: mixOrderList.length === 0,
      payNo,
      payNos,
      storeName: store.shortName || store.storeName || store.alias_name || store.store_name,
      storeCode: store.storeCode || store.store_code,
      pickupStart,
      pickupEnd,
      // 如果外层没有返回活动编码，则取preferentialList的第一项活动编码
      activityCode: activityCode || preferentialList[0]?.preferentialCode,
      shareImg: items[0].goodsPicture || items[0].headPic,
      goodsCount: items.reduce((sum, item) => {
        return sum + item.quantity
      }, 0)
    }
    pageTransfer.send(orderDetailObj)
    // 支付成功跳转
    wx.redirectTo({
      url: '/relay/pages/waitPay/index'
    })
  },
  /**
   * 上报智慧零售支付成功
   */
  reportTXPay(orderInfo) {
    const {
      orderNo,
      actualAmount,
      totalPrice,
      createTime,
      mixOrderList = []
    } = orderInfo
    // 混合单, 拆单上报
    if (mixOrderList.length) {
      const commonParams = {
        type: 2,
        payTime: Date.now(),
        createTime: createTime,

      }
      // 过滤月卡部分
      mixOrderList.filter(item => {
        const orderType = item.orderType || {}
        return String(orderType.code) !== '170'

      }).forEach(item => {
        const params = {
          ...commonParams,
          orderNumber: item.orderNo,
          amount: item.totalPrice,
          payAmount: item.actualAmount,
        }
        txSensor.txOrderSensor(params);
      })
      return
    }
    const param = {
      type: 2,
      orderNumber: orderNo,
      amount: totalPrice,
      payAmount: actualAmount,
      payTime: Date.now(),
      createTime: createTime,
    };
    txSensor.txOrderSensor(param);
  },
  /**
   * 点击删除按钮（此混入方法用于 及时达、试吃、全国送）
   */
  async deleteBtnHandle(orderInfo) {
    const showModalRes = await app.showModalPromise({
      title: '确认删除订单吗？',
      content: '订单删除后您无法查看该笔订单，也无法对该笔订单开具发票和评价。',
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消'
    })
    //  弹窗取消，此处可以进行行为上报
    if (!showModalRes) {
      return
    }

    const {
      user: { userID } = {}
    } = this.data


    const data = {
      orderType: orderInfo.orderType.code,
      customerID: userID,
      orderNoList: [orderInfo.orderNo]
    }

    /**
     * 订单删除后回调
     */
    function deleteCallBack () {
      const pages = getCurrentPages();
      const orederListPageName = 'userB/pages/orderList/index';
      const prevPage = pages[pages.length - 2]
      //  上个页面为订单列表页
      if (prevPage.route === orederListPageName) {
        prevPage.setData({ mix_updateOrder_singleUpdateDelete: true })
        wx.navigateBack();
      } else {
        // 去订单列表页
        wx.redirectTo({
          url: `/${orederListPageName}?type=${this.data.orderClass}`
        });
      }
    }

    try {
      await app.api.updateUserHide(data);
    } catch (error) {
      await app.showModalPromise({
        content: `订单删除失败`,
        showCancel: false,
        confirmText: '我知道了',
      })
    }
    deleteCallBack()
  },

  /**
   * 及时达/全国送开具发票（订单详情，订单列表
   */
  invoiceBtnHandle: wrapSensorReportFn(async function (orderInfo) {
    const { data } = await app.api.getOrderInvoiceInfo({
      orderNos: [orderInfo.orderNo]
    })

    const { canUseInvoiceAmount } = data[0]

    //  携带参数跳转到对应页面
    const query = JSON.stringify({
      //  订单类型
      orderType: orderInfo.orderType.code,
      //  开票金额
      drawableAmount: canUseInvoiceAmount,
      //  开票订单数
      orderQuantity: 1
    });

    //  设置开具发票页使用的开票参数
    wx.setStorageSync('createInvoice_data', {
      selectedData: [
        {
          orderChannel: 'O2O',
          orgCode: orderInfo.orgCode,
          channelOrderNo: orderInfo.orderNo,
          drawableAmount: canUseInvoiceAmount,
          finishTime: orderInfo.finishTime
        }
      ]
    })

    wx.navigateTo({
      url: '/userB/pages/invoice/createInvoice/index?pageParam=' + query,
    });
    return this.clickSensorParamsFn('开具发票')
  }),

  // 发票详情
  invoiceDetailBtnHandle: wrapSensorReportFn(function (orderInfo) {
    //  携带参数跳转到对应页面
    const query = JSON.stringify({
      //  及时达/全国送 订单渠道为 O2O
      orderChannel: 'O2O',
      channelOrderNo: orderInfo.orderNo
    });


    wx.navigateTo({
      url: '/userB/pages/invoice/invoiceDetail/index?pageParam=' + query,
    });

    return this.clickSensorParamsFn('发票详情')
  }),

  // 提示跳转h5下载页
  async showTipsToDownload(modalOptions) {
    const showModalRes = await app.showModalPromise(modalOptions)
    if (showModalRes) {
      util.navigateToDownLoadH5()
    }
  },

  // 立即付款
  async payImmediatelyBtnHandle(orderInfo) {
    // 获取余额
    // 列表用items
    // originPayAmount 支付金额
    const {
      payAmount,
      originPayAmount,
      consumables = [],
      payment = {},
      orderNo,
      items = [],
      vipCardInfo = {},
      // 充值信息
      rechargeInfo,
    } = orderInfo
    const { paymentList = [] } = payment
    const payAmountFormat = Number(`${payAmount[0]}.${payAmount[1]}`)
    // console.log(orderInfo)
    // console.log(payAmountFormat)
    // 使用了兑换卡,商品行有2表示使用了兑换卡
    const isUseExchangeCard = items.some(item => item.preferentialActType === 2)
    // 使用了代金券
    const isUseVoucher = paymentList.some(item => item.payChannel === 'V')
    const myAllOrderObj = {
      goodsOrderID: orderNo,
      type: this.data.orderClass,
      isImmediatelyPay: true
    }
    const isOrderList = this._data.isOrderList
    // 订单列表0元支付跳转详情去支付
    if (isOrderList &&  payAmountFormat === 0) {
      return toOrderDetailPay(myAllOrderObj)
    }
    /**
     * 支付金额为0时，说明详情查出的数据异常
     * 在订单详情页做重新查询金额数据处理
     */
    if ((!isUseVoucher && !isUseExchangeCard) && payAmountFormat === 0 && this.requestOrderDetail) {
      wx.showLoading({
        title: '正在重新获取'
      })
      const orderNo = orderInfo.orderNo

      //  设置定时器，5s内进行查询
      let timeout
      setTimeout(() => {
        timeout = true
      }, 5000)

      let totalPrice

      //  如未获得正确金额 且 未超时
      while (!totalPrice && !timeout) {
        await this.requestOrderDetail(orderNo, false)
        totalPrice = this.data.payment.totalPrice

        //  仍未查出金额，等待2s后再查
        if (!totalPrice) {
          await new Promise(resolve => setTimeout(() => resolve(), 2000))
        }
      }

      wx.hideLoading()

      //  获得正确金额，重新调用支付
      if (totalPrice) {
        this.payImmediatelyBtnHandle(this._data.curOrderInfo)
      }
      return
    }

    const isCombineOrder = this.checkIsCombineOrder(orderInfo)
    this.setData({
      target: {
        payAmount: payAmountFormat
      }
    })
    const walletAmount = this.getWalletAmount(originPayAmount, rechargeInfo)
    const setDataObj = {
      isUseVoucher,
      isUseExchangeCard,
      rechargePayInfo: null,
      // 开通月卡 或者 充值订单不使用云闪付
      disableUnionPay: Boolean(vipCardInfo.type || rechargeInfo),
      // 充值订单等walletAmount返回之后再展示paymentDialog
      paymentDialog: !rechargeInfo,
      // 有一种特殊场景：不同发货时间的全国送商品也是混合单,此时orderType是0
      // 所以这里改成及时达才展示云闪付
      hideUnionPay: !(orderInfo.orderType && orderInfo.orderType.desc === '及时达'),
    }
    Object.assign(this._data, {
      curOrderInfo: orderInfo,
      curOrderConsumables: consumables,
      isCombineOrder,
    })
    // 无随单充值无需后续逻辑
    if (!rechargeInfo) {
      this.setData(setDataObj)
      return
    }
    await walletAmount
    const {
      mainBalance
    } = this.data
    const rechargePayInfo = getOrderInlineRechargeInfo({
      mainBalance,
      lowerLimit: rechargeInfo.actualAmount,
      giftAmount: rechargeInfo.amount - rechargeInfo.actualAmount,
      payAmount: payment.totalPrice - rechargeInfo.actualAmount,
    })
    const noBalancePay = rechargePayInfo.balancePayAmount === 0
    if (isOrderList && noBalancePay) {
      return toOrderDetailPay(myAllOrderObj)
    }
    this.setData(Object.assign(setDataObj, {
      rechargePayInfo,
      paymentDialog: true,
    }))
  },

  /**
   * @desc 检查是否是合单；
   * orderType为0时表示混合单，但有一种特殊场景：不同发货时间的全国送商品也是混合单
   * 因此遍历items判断商品属性，判断这个单是否是合单(及时达+全国送)
   * @param {object} orderInfo
   */
  checkIsCombineOrder(orderInfo) {
    const { items = [] } = orderInfo
    const isCombineOrder = items.reduce((acc, cur) => {
      const { takeawayAttr = '及时达' } = cur
      acc.add(takeawayAttr)
      return acc
    }, new Set()).size > 1
    return isCombineOrder
  },

  // 重新下单 (再来一单/重新购买)
  async orderAgain(orderInfo) {
    const { items = [], mixOrderList = [] } = orderInfo
    const list = []
    if (mixOrderList.length) {
      mixOrderList.forEach(mix => {
        list.push(...mix.items)
      })
    } else {
      list.push(...items)
    }
    const goodsList = list.filter(item => !item.isConsumable).map(item => {
      const {
        takeawayAttr,
        quantity,
        goodsSn,
        itemServiceList = []
      } = item
      return {
        takeawayAttr: takeawayAttr || '及时达',
        goodsSn,
        count: quantity,
        selectSpecsServiceList: itemServiceList.map(el => {
          return {
            serviceSn: el.serviceSn
          }
        })
      }
    })
    shopCartStore.setTopCart({ timelyGoods: goodsList })

    const filterGoodsList = goodsList.filter(item => item.goodsSn)
    if (filterGoodsList && !!filterGoodsList.length) {
      app.globalData.repeatOrderGoods = filterGoodsList
    }
    wx.switchTab({
      url: '/pages/shopCart/index'
    })
  },

  // 及时达  再来一单
  buyMoreBtnHandle: app.subProtocolValid('shop', wrapSensorReportFn(async function (orderInfo) {
    this.orderAgain(orderInfo)
    return this.clickSensorParamsFn('再来一单')
  })),
  // 及时达  重新购买
  buyAgainBtnHandle: app.subProtocolValid('shop', wrapSensorReportFn(async function (orderInfo) {
    this.orderAgain(orderInfo)
    return this.clickSensorParamsFn('重新购买')
  })),
  /**
   * @description - 取消订单，切换中台接口
   * @param e - 组件原生数据
   *  */
  cancelBtnHandle(orderInfo) {
    // 防止点击过快
    this.preventEvent();

    //  取消订单前，设置orderSource。用于请求取消订单的理由列表
    const orderSource = {
      [ORDER_TYPE.TIMELY]: 'wxMiniOrder',
      [ORDER_TYPE.COUNTRY]: 'b2cOrder'
    }[orderInfo.orderType.code]

    this.setData({
      orderSource
    })

    // 取消订单 - 未支付
    if (orderInfo.paymentStatus.desc !== '支付完成') {
      this.orderCancelPaiedFailed(orderInfo)
    }
    // 取消订单 - 已支付 不考虑超时取消
    else {
      this.orderCancelPaiedSuccess(orderInfo)
    }
    app.event.emit('refreshNewUserInfo')
  },

  requestSubscribeMessage(callback) {
    app.requestSubscribeMessage({
      tmplIds: ['jLaaeFpXwM90AMJrCpS63A4A1tBH3zT7tVHleo621gE']
    }, () => callback())
  },

  /**
   * @description 取消订单按钮事件：未支付情况下
   *  */
  orderCancelPaiedFailed: wrapSensorReportFn(async function (orderInfo) {
    const vm = this
    // 取消订单
    const showModalRes = await app.showModalPromise({
      content: '您精挑细选的商品真的要取消吗？',
      showCancel: true,
      confirmText: '残忍取消',
      cancelText: '考虑一下',
    })
    // 神策上报"残忍取消"
    if (!showModalRes) return this.clickSensorParamsFn('考虑一下')

    // 点击“确定”操作
    wx.showLoading({
      title: '取消中',
      mask: 'true'
    })
    try {
      await this.cancelUnpaidOrderFetch(orderInfo, 100)
      wx.hideLoading()
      // 请求成功
      vm.orderCancelSuccess()
      app.judgeNewUserOrderCancel(orderInfo.orderNo)
      const {
        orderNo,
        payment,
        createTime
      } = orderInfo
      return Object.assign(
        {},
        {
          //智慧零售取消订单埋点
          txOrderSensor: [{
            createTime: createTime,
            cancelTime: Date.now(),
            orderNumber: orderNo,
            amount: payment.amount,
            payAmount: payment.totalPrice,
            type: 3 //type等于3表示取消订单的埋点
          }]
        },
        // 神策上报取消订单
        this.clickSensorParamsFn('取消订单')
      )
    } catch (errorInfo) {
      wx.hideLoading()
      const errorType = Object.prototype.toString.call(errorInfo)
      if (/error/i.test(errorType)) {
        console.log('orderCancelPaiedFailed', errorInfo);
      } else {
        commonObj.showModal('提示', errorInfo.description, false, '我知道了')
      }
    }
  }),

  // 取消未支付订单核心方法(只包含接口调用)
  async cancelUnpaidOrderFetch(orderInfo, operateNum) {
    const {
      user: { userID } = {}
    } = this.data
    const {
      eshopInfo,
      orderTradeNo,
      createSource,
      orderNo
    } = orderInfo
    const params = {
      customerID: userID,
    }
    if (util.isEmpty(eshopInfo)) { // 中台订单
      // 未支付取消：合并的订单要用orderTradeNo
      Object.assign(params, {
        subOrderNo: createSource === ORDER_CREATE_SOURCE.ORDER_TRADE_COMBINE ? orderTradeNo : orderNo,
        operate: operateNum,
        paramMap: {
          operatorCode: userID,
          launchType: '2'
        }
      })
    } else { // 电商订单按照原始传参透传
      Object.assign(params, {
        eshop: {
          customerID: userID,
          payOrderID: eshopInfo.payOrderID,
          requester: 1,
        }
      })
    }
    try {
      await app.api.cancelUnpaidOrder(params)
      return Promise.resolve()
    } catch (error) {
      console.log('cancelUnpaidOrderFetch', error);
      return Promise.reject(error)
    }
  },

  // 倒计时结束自动调用取消订单接口
  async cancelUnpaidOrderAfterCountDown(orderInfo) {
    try {
      await this.cancelUnpaidOrderFetch(orderInfo, 110)
      this.refreshOrder()
    } catch (error) {
      console.log('cancelUnpaidOrderAfterCountDown', error);
      // 小程序setTimeout有延时，如果交易域已经将订单状态修改为已取消，这个时候再调用取消接口会报错
      this.refreshOrder()
    }
  },

  /**
    *  @description - 取消订单 - 已支付(超时/未超时)；超时取消订单沿用原交互设计，不需要填写原因；未超时取消订单，需要弹窗填写原因
    *  超时取消订单暂时无法对接，先进行普通取消订单，原有逻辑如下：
    *  function isAllowRefund(isAllowRefund) {
    *   return isAllowRefund === 'Y'
    *  }
    *  let type = isAllowRefund(orderInfo.isAllowRefund) ? '2' : '3' // 退款类型  2: 超时退款 3: 取消退款
    *  let refundChannel = channel.indexOf('gb') > -1 ? '1' : '2' // 退款渠道 1:退还果币 2:退还现金 （app3.4.3后使用3、4渠道）3:原路退 4:退还果币
    *  */
  orderCancelPaiedSuccess: wrapSensorReportFn(async function (orderInfo) {
    const vm = this
    const {
      user
    } = this.data
    const {
      eshopInfo,
      orderNo,
      payment,
      createSource
    } = orderInfo
    const { amount, paymentOrderList = [] } = payment
    // if (orderEntry !== 20) {
    //   commonObj.showModal('提示', '该订单为“百果园App”订单，您可以下载“百果园App”进行取消', false)
    //   return
    // }
    // 入参参考 http://op.pagoda.com.cn/#/technical-doc?docId=871&anchor=Body-5
    const params = {
      createSource,
      customerID: user.userID,
    }
    if (util.isEmpty(eshopInfo)) { // 中台订单
      Object.assign(params, {
        subOrderNo: orderNo, // 订单号
        refundType: '1', // 退款类型
        refundModel: '5', // 退款模式
        reason: '', // 退款原因
        refundStatus: 112, // 售中取112,售后取110(即三无退),小程序默认112
        refundMoney: amount, // 退款额，即实际支付金额
        launchType: 2, // 发起方
        customerComplaintType: 0,
        refundTradeChannelNum: 10001,
      })
    } else { // 电商订单按照原始传参透传
      const { goodsOrderID } = eshopInfo || {}
      const { payChannel = '' } = (paymentOrderList || [])[0] || {}
      Object.assign(params, {
        paramMap: {
          orderEntry: 20,
          eshopRefund: {
            type: 3, // 退款类型  2: 超时退款 3: 取消退款
            customerId: user.userID,
            goodsOrderId: goodsOrderID,
            refundChannel: ['M', 'Z', 'Q', 'K', 'J'].includes(payChannel) ? 1 : 2, // 退款渠道 1 主钱包 2现金
            requester: 1, // 请求者标识 1:顾客子系统取消
            description: '' // 退款详情
          }
        }
      })
    }
    // 未超时取消订单，需要弹窗填写原因
    vm.setData({
      orderCancelParams: params,
      orderCancelIsShow: true
    })
    return Object.assign(
      {},
      // 神策上报点击事件
      this.clickSensorParamsFn('取消订单'),
      // 神策防黑产埋点
      {
        'safeGuardSensor': ['refund', { orderNo: orderNo, orderType: 'FRUIT' }]
      }
    )
  }),

  /**
   * @description - 取消订单 - 已支付
   *  */
  orderCancelPaiedSuccessFetch(params) {
    const vm = this
    app.api.cancelPaidOrder(params).then((res) => {
      wx.hideLoading()
      const {
        errorCode,
        description
      } = res
      // 请求失败
      if (Number(errorCode) !== 0) {
        wx.showToast({
          title: Number(errorCode) === 11001 ? '订单状态已更新，请稍后再试' : description,
          icon: 'none',
          duration: 3000
        })
        wx.hideLoading()
        return
      }
      vm.setData({
        orderCancelIsShow: false,
        orderCancelParams: null
      })
      // 请求成功
      vm.orderCancelSuccess()
      app.judgeNewUserOrderCancel(params.subOrderNo)
    }).catch((res) => {
      wx.hideLoading()
      wx.showToast({
        title: Number(res.errorCode) === 11001 ? '订单状态已更新，请稍后再试' : res.description,
        icon: 'none',
        duration: 3000
      })
    })
  },

  /**
   * 超时取消订单点击回调
   */
  async cancelExceedTimeBtnHandle(orderInfo) {
    const showModalRes = await app.showModalPromise({
      content: '亲，您的订单即将送达，骑手正在飞速配送中，请再耐心等候几分钟~',
      showCancel: true,
      confirmText: '取消订单',
      cancelText: '我再想想',
    })
    if (!showModalRes) {
      return
    }

    this.cancelExceedTimeBtn(orderInfo)
  },

  // 超时取消订单
  cancelExceedTimeBtn: function (orderInfo) {
    // 取消订单 - 已支付
    app.requestSubscribeMessage({
      tmplIds: ['jLaaeFpXwM90AMJrCpS63A4A1tBH3zT7tVHleo621gE']
    }, () => {
      this.orderCancelPaiedSuccess(orderInfo)
    })
  },

  // 跳转订单评价
  // 列表页：拼团(ev.currentTarget)，门店(ev.currentTarget)，及时达(info)，b2c(info)
  // 详情页：门店(ev.currentTarget)，及时达(info)，b2c(info)
  commentBtnHandle: wrapSensorReportFn(function (info) {
    let num, orderType = '', eshopOrderID = '';
    if (info.currentTarget) {
      const { orderid: orderId, ordertype = '', eshoporderid } = info.currentTarget.dataset
      num = orderId
      orderType = ordertype
      eshopOrderID = eshoporderid
    } else {
      const { orderNo, orderType: { code = '' } = {} } = info
      num = orderNo
      orderType = code
    }
    const params = {
      orderid: num,
      orderType,
      eshopOrderID
    }
    wx.navigateTo({
      url: `/userB/pages/evaluation/orderEvaluation/index?pageParams=${JSON.stringify(params)}`
    })
    return this.clickSensorParamsFn(`评价-${this.data.orderClass}`)
  }),

  getConfirmReceiveText(orderInfo) {
    if (orderInfo.orderType && orderInfo.orderType.desc === '及时达') {
      return '您确认已收到商品了吗？'
    } else {
      return orderInfo.isAllSent ? '您确认已收到全部商品了吗？' : '部分商品仍未发货，确认收货后可进入“三无退货”进行退款'
    }
  },

  /**
   * 关闭B2C确认收货弹窗（未全部发货）
   */
  closeB2cConfirmModel() {
    this.setData({
      showB2cConfirmModal: false
    })
  },

  /**
   * 处理B2C确认收货（未全部发货）
   */
  async handleB2cConfirm() {
    const { orderNo } = this.data.b2cConfirmInfo || {}
    this.setData({
      showB2cConfirmModal: false
    })
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    try {
      await app.api.confirmB2CExpress({
        customerID: this.data.user.userID,
        subOrderNo: orderNo,
        paramMap: {
          operatorCode: this.data.user.userID,
          launchType: '2'
        }
      })
      // 成功跳转
      this.refreshOrder()
    } catch (error) {
      wx.hideLoading()
      console.log('confirmReceiveBtn', error);
    }
  },

  /**
   * 处理联系客服
   */
  handleCustomerService() {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  // 确认收货
  confirmReceiveBtnHandle: wrapSensorReportFn(async function (info) {
    const { orderNo, orderType, isAllSent } = info || {}
    // b2c订单且未全部发货
    if (orderType && orderType.desc === 'b2c' && !isAllSent) {
      // 由于b2c订单且未全部发货状态下使用了自定义弹窗，这里只做唤起弹窗操作
      this.setData({
        showB2cConfirmModal: true,
        b2cConfirmInfo: info || {}
      })
      return
    } else {
      const text = this.getConfirmReceiveText(info)
      const showModalRes = await app.showModalPromise({
        content: text,
        showCancel: true,
        confirmText: '确认',
        cancelText: '取消',
      })
      if (!showModalRes) return
    }
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    try {
      await app.api.confirmB2CExpress({
        customerID: this.data.user.userID,
        subOrderNo: orderNo,
        paramMap: {
          operatorCode: this.data.user.userID,
          launchType: '2'
        }
      })
      // 成功跳转
      this.refreshOrder()
    } catch (error) {
      wx.hideLoading()
      console.log('confirmReceiveBtn', error);
    }
  }),

  // 查看物流
  checkExpressBtnHandle: wrapSensorReportFn(function (info) {
    this.navigateToLogisticsDetail(info)
  }),

  // 倒计时完成
  countDownFinish(orderClass, orderInfo) {
    // 目前只针对及时达和全国送
    if (!['A', 'F'].includes(orderClass)) return
    const vm = this
    wx.showToast({
      title: '超过15分钟未支付，订单已取消',
      icon: 'none',
      duration: 2000,
      mask: true,
      success() {
        // 目前的解决方案是倒计时结束后主动调用未支付取消订单接口
        // 取消之后刷新列表
        setTimeout(() => {
          vm.cancelUnpaidOrderAfterCountDown(orderInfo)
        }, 2000)
      }
    })
  },
  handleOnlineService() {
    this.setData({
      showCustomConfirmModal: false
    })
    app.toOnlineService({ queStr: '发票服务' })
  },

  /**
   * 父组件接受到短信验证弹窗的值
   * @param {Boolean} e Event
   */
  validated(e) {
    // 通过校验直接允许支付
    if (e.detail.validated) {
      this.setData({
        visibleSMS: false
      })
      // 使用钱包支付
      if (this._data.tmpConfirmHandle) {
        this._data.tmpConfirmHandle()
        this._data.tmpConfirmHandle = null
      }
    }
  },
  /**
   * 父组件接受到返回事件
   * @param {Boolean} e Event
   */
  onBack(e) {
    // 用户点击返回
    if (e.detail.back) {
      this.setData({
        visibleSMS: false
      })
    }
  },
  async showSmsValidate() {
    const { CaptchaHandle } = await sms
    CaptchaHandle.selectComp(this)
    this.setData({
      visibleSMS: true,
    })
  },
}
