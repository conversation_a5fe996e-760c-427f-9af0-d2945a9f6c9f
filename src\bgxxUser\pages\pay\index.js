// 心享会员续费H5页面微信支付跳转
const {commonObj} = require('../../../source/js/common')
const { redirectToH5Page } = require('../../source/js/redirectToH5Page')
var app = getApp()

const subscribeMap = {
  none: /** @type {string[]} */ ([]),
  // 会员购买通知
  superVipNotice: ['ZFmPMP28OPnDaqfeD-HJ4sZ4QYVofeWqiUB0RkbPgAg']
}

function afterPayHandle (openvalue, redirectType) {
  if (!openvalue) return
  if (redirectType === 'navigateBack') {
    return wx.navigateBack()
  }
  return redirectToH5Page(openvalue, redirectType)
}

// pageTitle=xxxx&subscribeScene=xxx&openvalue=xxxx

// pageTitle: 页面标题(默认为"百果心享续费"or"百果心享购买"),可按需传入其他标题
// subscribeScene: 支付成功后订阅消息场景值(默认为superVipNotice),暂时只有superVipNotice和none
// openvalue: 支付成功后跳转http链接页面,可传入navigateBack返回上一页

Page({
  /**
   * 页面的初始数据
   */
  data: {

  },
  _data: {
    options: {}
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    wx.hideShareMenu()
    console.log(options)
    this._data.options = options || {}
    wx.showLoading()
    this.requestPayment()
  },
    /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    wx.setNavigationBarTitle({
      title: this._data.options.pageTitle || (app.globalData.superVipStatus === 'F' ? '百果心享续费' : '百果心享购买')
    })
  },
  /**
   * @desc 调起微信支付
   */
  async requestPayment () {
    const {
      channel,
      subject,
      body,
      payOrderNo,
      payAmount,
      contractCode,
      period,
      payType,
      orderType,
      // paymentOrderID,
      // t: isRenew,
      subscribeScene = 'superVipNotice',
      openvalue,
      redirectType = '',
    } = { ...this._data.options };
    const deadDate = new Date(new Date().getTime() + 30 * 60 * 1000);
    const timeExpire = new Date(deadDate).Format('yyyyMMddHHmmss');
    const openID = wx.getStorageSync('wxSnsInfo').openid;
    const user = wx.getStorageSync('user') || {};
    const timelyCity = wx.getStorageSync('timelyCity') || {};
    const params = {
      channel,
      payOrderNo,
      payAmount,
      orderType,
      contractCode,
      period,
      timeExpire,
      clientIP: '127.0.0.1',
      subject,
      body,
      version: commonObj.appVersion,
      requestTarget: 'wx_mini',
      source: 'WX_MINI',
      openID,
      userToken: user.userToken,
      customerID: user.userID,
      mobile: user.phoneNumber,
    };
    const options = {
      data: params,
      header: {
        gpslocation: JSON.stringify({
          lat: timelyCity.lat || '',
          lon: timelyCity.lon || '',
        }),
      },
    };

    try {
        const res = await app.api.payRequest(options)
        if (Number(res.errorCode) === 0) {
          // 支付成功跳h5的页面路径
          if (payType === '1') {
            const depositObj = res.data || {}
            wx.requestPayment({
              timeStamp: depositObj.timeStamp,
              nonceStr: depositObj.nonceStr,
              package: depositObj.packageValue,
              signType: 'MD5',
              paySign: depositObj.signStr,
              success: () => {
                
                wx.hideLoading();
                // 微信支付成功，跳到 h5的支付成功页面
                console.log('微信支付成功。。。');
                const tmplIds = subscribeMap[subscribeScene] || []
                ;(tmplIds.length ? new Promise(resolve => {
                  app.requestSubscribeMessage({
                    tmplIds,
                  }, resolve)
                }) : Promise.resolve()).then(() => {
                  afterPayHandle(openvalue, redirectType);
                })
              },
              fail: (res) => {
                console.log(res)
                wx.hideLoading();
                wx.showModal({
                  title: '',
                  content: '您已取消支付！',
                  showCancel: false,
                  confirmText: '知道了',
                  success(res) {
                    if (res.confirm) {
                      wx.navigateBack();
                    }
                  },
                });
              },
            });
          } else {
            //果币支付成功，跳到 h5的支付成功页面
            console.log('果币支付成功。。。');
            afterPayHandle(openvalue, redirectType);
          }
        } else {
          wx.hideLoading();
          wx.showModal({
            title: '',
            content: res.errorMsg || res.description || '支付失败，请稍后重试',
            showCancel: false,
            confirmText: '知道了',
            success(res) {
              if (res.confirm) {
                wx.navigateBack();
              }
            },
            fail: () => {
              wx.hideLoading()
              wx.showModal({
                title: '',
                content: '已为您取消支付',
                showCancel: false,
                confirmText: '我知道了',
                success (res) {
                  if (res.confirm) {
                    wx.navigateBack()
                  }
                }
              })
            }
          })
        }
    } catch {
        wx.hideLoading();
        wx.showModal({
          title: '',
          content: '支付失败，请稍后重试',
          showCancel: false,
          confirmText: '知道了',
          success(res) {
            if (res.confirm) {
              wx.navigateBack();
            }
          },
        });
        console.error('调起微信支付request接口失败...');
    }
  },
  /**
   * @desc 返回上一页
   */
  returnPre () {
    wx.navigateBack()
  }
})
