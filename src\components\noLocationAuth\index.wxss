/* components/noLocationAuth/index.wxss */
.container {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.img{
  margin: 200rpx 0 45rpx 0;
  width: 331rpx;
  height: 232rpx;
}
.tips{
  font-size: 28rpx;
  color: #222222;
  font-weight: bold;
  padding-bottom: 10rpx;
}
.tips-WeChat {
  font-size: 24rpx;
  color: #999;
  padding: 0 76rpx;
  text-align: center;
  line-height: 40rpx;
}
.open-setting {
  margin-top: 56rpx;
}
.btn{
  width: 360rpx;
  height: 88rpx;
  background-color: #00A644;
  font-size: 30rpx;
  color: #fff;
  border-radius: 100rpx;
  text-align: center;
  line-height: 88rpx;
}

.open-address-btn {
  background-color: #fff;
  margin-top: 30rpx;
  border: 1px solid#00A644;
  color: #00A644;
}
