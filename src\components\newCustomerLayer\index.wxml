<!-- 新用户礼包弹窗 -->
<view class='redPaper-mask' wx:if="{{isShowRedPaper}}" catch:touchmove="stop" >
  <view class="redPaper {{isSlideDown ? 'hide' : ''}}" catchtap='preventClick'>
    <view class="redPaper-content {{couponType === 'groupNewCustomer' ? 'group' : ''}} {{ isGrayTheme ? 'gray-theme' : '' }}">
      <scroll-view scroll-y>
        <view class="{{couponList.length<4 ? 'coupon-list vertical':'coupon-list'}}">
          <block wx:for="{{couponList}}" wx:key="index">
            <view class="coupon">
              <view class="coupon-left">
                <text class="coupon-way" wx:if="{{item.couponWay === '1' || item.couponWay === '3'}}">¥</text>
                <text>{{item.couponValueStr}}</text>
                <text class="coupon-way" wx:if="{{item.couponWay === '2' || item.couponWay === '4'}}">折</text>
              </view>
              <view class="coupon-right">
                <view class="coupon-standard">{{item.limitValueStr}}
                </view>
                <view class="coupon-situation">{{item.expireDays}}天内有效</view>
                <view class="coupon-situation">{{item.couponUseChannelStr}}</view>
              </view>
            </view>
          </block>
        </view>
      </scroll-view>
      <view catchtap='navigateToCategory'>
        <send-coupon
          wx:if="{{isShowCardCoupon}}"
          bindcustomevent="onCouponComfirmBtnTap"
          send_coupon_params="{{couponData.send_coupon_params}}"
          sign="{{couponData.sign}}"
          send_coupon_merchant="{{couponData.send_coupon_merchant}}"
        >
          <image class='redPaper-btn' src='{{redPaperBtnBgImage}}'></image>
        </send-coupon>
        <image wx:else class='redPaper-btn' src='{{redPaperBtnBgImage}}'></image>
        <view class="redPaper-tips">为您插入微信卡包，及时获取用券提醒</view>
      </view>
    </view>
    <view class='close-btn' catchtap='closeLayer'>
      <send-coupon
        wx:if="{{isShowCardCoupon}}"
        bindcustomevent="onCouponComfirmBtnTap"
        send_coupon_params="{{couponData.send_coupon_params}}"
        sign="{{couponData.sign}}"
        send_coupon_merchant="{{couponData.send_coupon_merchant}}"
      >
        <image class='close-btn-img' src='/source/images/btn_close.png'></image>
      </send-coupon>
      <image wx:else class='close-btn-img' src='/source/images/btn_close.png'></image>
    </view>
  </view>
</view>
