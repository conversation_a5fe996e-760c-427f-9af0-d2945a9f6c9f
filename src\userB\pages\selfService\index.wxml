<!--pages/user/selfService/index.wxml-->
<main-page currentView="content" />
<view class='contain'>

    <!-- 个性相关 -->
    <view class="individuation_box">
        <view class='self-service' bindtap='navigateToPage' data-type='userAgreement'>
            <view class='service'>
                <text>用户协议</text>
            </view>
            <image src='/userB/source/images/icon_20_right_black.png'></image>
        </view>
        <view wx:if="{{ isLogin }}" class='self-service' bindtap='navigateModifyPwd' data-urlinfo="modifyPwd">
            <view class='service'>
                <text>重置门店消费密码</text>
            </view>
            <image src='/userB/source/images/icon_20_right_black.png'></image>
        </view>
        <view class='self-service' bindtap='navigateToH5' data-urlType='12'>
            <view class='service'>
                <text>隐私政策摘要</text>
            </view>
            <image src='/userB/source/images/icon_20_right_black.png'></image>
        </view>
        <view class='self-service' bindtap='navigateToH5' data-urlType='11'>
            <view class='service'>
                <text>第三方信息共享清单</text>
            </view>
            <image src='/userB/source/images/icon_20_right_black.png'></image>
        </view>
        <view class='self-service' bindtap='navigateToH5' data-urlType='10'>
            <view class='service'>
                <text>个人信息清单</text>
            </view>
            <image src='/userB/source/images/icon_20_right_black.png'></image>
        </view>
        <view class='recommend_text-box' bindtap='navigateToPage' data-type='contentRecommend'>
            <view class=''>
                <view class="bgy_prcard-constitution">个性化内容推荐</view>
                <view class="open_state-text">管理个性化推荐内容的展示</view>
            </view>
            <view>
                <image src='/userB/source/images/icon_20_right_black.png' style='width:20rpx; height:20rpx;'></image>
            </view>
        </view>
        <view class='self-service' bindtap='navigateToAboutUs' data-urlType="2">
            <view class='service'>
                <text>关于我们</text>
            </view>
            <image src='/userB/source/images/icon_20_right_black.png'></image>
        </view>
        <view class='self-service'>
            <view class='service'>
                <text>版本管理</text>
            </view>
            <view class="version_num-text">当前版本V{{version}}</view>
        </view>
        <view wx:if="{{ isLogin }}" class='self-service' bindtap='navigateToLogoff' data-urlType="2">
            <view class='service'>
                <text>注销</text>
            </view>
            <view class="self-service-logoff">
                <view class="logoff-text">注销后无法恢复，请谨慎操作</view>
                <view > 
                    <image src='/userB/source/images/icon_20_right_black.png'></image>
                </view>
            </view>
        </view>
    </view>
    <view class="btn-signOut safe-area-inset-bottom" bindtap="signOut" wx:if="{{isLogin}}">退出登录</view>
</view>
<common-loading />