<view class="remain-list">
  <view wx:if="{{goods.specialInfo && !goods.activityInfo && goods.specialInfo.maxEffectNum}}" class="remain remain-special">
    <special-label visible over-limit="{{false && goods.count > goods.specialInfo.maxEffectNum}}" cart-limit="{{goods.specialInfo.cartLimit}}" price="{{goods.count <= goods.specialInfo.maxEffectNum ? '' : goods.specialInfo.price}}" />
  </view>
  <!-- 特价活动并且活动不可用券并且还有可享受活动次数时,不展示不可用券 -->
  <view wx:if="{{goods.isSupportCoupon === 'N' || goods.saleType === 'P' || (goods.specialInfo && !goods.specialInfo.couponStacking && goods.specialInfo.maxEffectNum)}}" class="remain remain-disabled"><view class="remain-text">不可用券</view></view>
  <!-- 次日达特价情况,不需要展示限购数,所以不需要拿residualEffectNum和stockNum作比较 -->
  <view wx:if="{{goods.shelfStatus!='D'&&goods.stockShow == 'Y' && goods.stockNum > 0 && goods.stockNum < 10 && ((goods.specialInfo && goods.specialInfo.maxEffectNum) || (goods.residualEffectNum > goods.stockNum || goods.residualEffectNum ==-1 || goods.residualEffectNum ==null))}}" class="remain"><view class="remain-text">仅剩{{goods.stockNum}}份</view></view>
  <!-- 次日达特价不展示限购数 -->
  <view wx:if="{{goods.shelfStatus!='D' && !goods.activityInfo && goods.residualEffectNum > 0 && (goods.stockNum > goods.residualEffectNum || goods.stockNum == goods.residualEffectNum || goods.stockNum==-1 || goods.stockNum==null) && !(goods.specialInfo && goods.specialInfo.maxEffectNum)}}" class="remain"><view class="remain-text">限购{{goods.residualEffectNum}}份</view></view>
  <view class="remain" wx:if="{{goods.residualEffectNum==0 || goods.shelfStatus=='D'||goods.stockNum == 0}}"><view class="remain-text">{{goods.residualEffectNum==0?"购买已达上限":(goodsType === 'gift'? '今日已赠完':"今日已售罄")}}</view></view>
</view>
