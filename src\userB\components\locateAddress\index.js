var coordtransform = require('../../../utils/coordUtil')
import wxappMap from '../../../service/wxappMap'
const locateService = require('../../../utils/services/locate')
var log = require('../../../utils/log.js')
var app = getApp()
import locateStore from '../../../stores/module/locate'
import { boolean2YN } from '../../../utils/YNBoolean'
/**
 * @description 地址定位
 */
Component({
  /**
   * @description 组件的属性列表
   */
  properties: {
  },
  /**
   * @description 组件的初始数据
   */
  data: {
    cityName: '', // 城市名称
    cityID: '', // 城市ID
    showSearchList: false, //地址联想列表
    curAddress: {}, // 记录当前选择的地址,包含地址名，经纬度
    isClearInput: true // 是否清空地址输入框
  },
  /**
   * @description 组件的生命周期
   */
  lifetimes: {
    // 在组件在视图层布局完成后执行
    ready: function() {
      this.getCurAddress()
    }
  },
  /**
   * @description 组件的方法列表
   */
  methods: {
    /**
     * @description 选择城市页调用，传参
     * @param {*} city
     */
    refreshCityInfo: function (city = {}) {
      this.setData({
        cityName: city.cityName,
        cityID: city.cityID
      })
    },
    /**
     * @description 获取当前地址
    */
    getCurAddress() {
      // 逻辑和及时达一样，有收货地址取收货地址，无收货地址则取定位地址
      const selectedAddress = wx.getStorageSync('selectedAddress') || {}
      const timelyCity = wx.getStorageSync('timelyCity') || { locateCity: {} }
      const locateCity = timelyCity.locateCity || {}
      const userCurrLoca = wx.getStorageSync('userCurrLoca') || {}
      if (selectedAddress.addressId) {
        const { gisAddress, lat, lon } = selectedAddress
        this.setData({
          curAddress: {
            address: locateStore.useDefault ? '无定位地址' : gisAddress,
            lat: lat,
            lon: lon
          }
        })
      } else {
        const { address, lat, lon } = timelyCity
        const { location = {} } = userCurrLoca
        this.setData({
          curAddress: {
            address: locateStore.useDefault ? '无定位地址' : (address || userCurrLoca.address),
            lat: lat || location.lat,
            lon: lon || location.lon
          }
        })
      }
      this.setData({
        cityName: locateCity.cityName || timelyCity.cityName || userCurrLoca.cityName,
        cityID: locateCity.cityID || timelyCity.cityID || '',
        cityCode: timelyCity.cityCode || '',
        selectedStore: {
          storeID: timelyCity.storeID
        }
      })
      if (!locateCity.cityID || !timelyCity.cityID || !timelyCity.lon ) {
        const pages = getCurrentPages()
        log.error('选择门店页，拿不到位置信息', pages)
      }
      this.refreshEventTrigger()
    },
    /**
     * @description 输入提货地址
     */
    bindInputTap: function (e) {
      var value = e.detail.value,
        that = this
      if (this.data.isClearInput) {
        return
      }
      that.setData({
        inputValue: value
      })
      this.toSearchAddress(value)
    },
    async toSearchAddress(value) {
      const res = await wxappMap.getSuggestion({
        keyword: value,
        region: this.data.cityName,
      })
      this.setData({
        showSearchList: true,
        regionList: res.data
      })
    },
    /**
     * @description 清空搜索地址
    */
    bindCloseTap: function () {
      var that = this
      this.setData({
        inputValue: '',
        showSearchList: false
      })
      this.data.isClearInput = true
    },
    //聚焦显示清除按钮
    /**
     * @description 清空搜索地址
    */
    bindFocusHandler: function (e) {
      wx.reportAnalytics("searchstore")
      this.setData({
        closeShowFlag: true
      })
      this.data.isClearInput = false
    },
    /**
     * @description 失焦隐藏清除按钮
    */
    bindBlurHandler: function (e) {
      this.setData({
        closeShowFlag: false
      })
    },
    /**
     * @description 选择搜索出来的地址，并获取改地址附近的门店
    */
    selectSearchAddress: function (e) {
      wx.reportAnalytics('searchresult_click')
      const { lat, lon, title } = e.currentTarget.dataset
      const location = coordtransform.gcj02tobd09(lon, lat)
      this.setData({
        curAddress: {
          address: title,
          lat: location[1],
          lon: location[0]
        },
        showSearchList: false
      })
      this.refreshEventTrigger()
    },
    /**
     * @description 定位点或者搜索地址的附近地址
    */
    async nowLocate() {
      try {
        const locationInfo = await locateService.getGeoLocation()
        const locateRes = await wxappMap.reverseGeocoder({
          get_poi: 1,
          poi_options: 'policy=2',
          lat: locationInfo.latitude,
          lon: locationInfo.longitude,
        })
        const bdlocate = coordtransform.gcj02tobd09(locateRes.result.location.lng, locateRes.result.location.lat);
        const address = locateRes.result.formatted_addresses.recommend
        const param = {
          cityName: locateRes.result.ad_info.city,
          lon: bdlocate[0],
          lat: bdlocate[1],
          address,
          isMatchAddress: boolean2YN(false),
        }
        this.setData({
          curAddress: {
            address: address,
            lat: bdlocate[1],
            lon: bdlocate[0]
          },
          cityName: locateRes.result.ad_info.city
        })
        // 根据定位信息获取cityId, 然后获取附近门店
        this.getCityId(param, (checkCityData) => {
          this.refreshEventTrigger(checkCityData)
        })
      } catch (error) {
        locateService.handleWXLocationErr(error.errMsg)
      }

    },
    /**
     * @description 根据定位信息获取cityId, 然后获取附近门店
     * @param {Object} param  获取附近门店的参数
     * @param {fn} callback  要执行的回调函数
    */
    async getCityId (param, callback) {
      try {
        const res = await app.api.checkCity(param)
        const resultData = res.data || {}
        this.setData({
          cityID: locateService.getLocateCity(res.data).id || ''
        })
        // wx.removeStorageSync('selCity')
        callback && callback(resultData)
      } catch (err) {
        console.log(err)
      }
    },
    /**
     * @description 触发父组件的事件
    */
    refreshEventTrigger(checkCityData) {
      const {
        cityID,
        cityCode,
        cityName,
        curAddress,
        selectedStore
      } = this.data
      this.triggerEvent('refreshEvent', {
        cityID,
        cityCode,
        cityName,
        curAddress,
        selectedStore,
        checkCityData,
      } )
    }
  }
})
