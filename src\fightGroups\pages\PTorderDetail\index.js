var app = getApp();
var commonObj = require('../../../source/js/common').commonObj;
var bg_highTop = require('../../source/image-base64/bg_order_state').bg;
// var iconAdd = require('../../source/image-base64/icon_add').bg;
// var iconSub = require('../../source/image-base64/icon_subtract').bg;
var coordtransform = require('../../../utils/coordUtil')
const jsBarcodePaomise = require.async('../../../componentsSubPackage/commonUtils/JsBarcode/index')
const JsBarcode = jsBarcodePaomise.then(module => module.getJsBarcode(app))
var picUrl = commonObj.PAGODA_PIC_DOMAIN;
const drawSharePic = require('../../mixins/drawSharePic')
var sensors = require('../../../utils/report/sensors')
const { generateShareAttr } = require('../../../utils/report/setup')
const { drawPosterPic, getPosterModelSize } = require('../../source/js/drawPoster/fightGroupsDrawPoster')
const SMSMixin = require('../../../mixins/SMSMixin')
const { defaultCustomerAvatar } = require('../../../source/const/user');

function getOptions(options) {
  return (options.goodsDetailObj || options.myAllOrderObj || options.bindCardObj || options.inviteFriendsObj || options.paySuccessObj)
}
var status = {
  'WAIT_PAY': '待付款',
  'GROUPBUY_PROCESS': '待成团',
  'WAIT_PICKUP': '待提货',
  'PICKED_UP': '已提货',
  'CANCELED': '已取消',
  'REFUNDED': '已退款',
  'COMPLAINED': '已投诉',
  'REVIEWING': '退款审核中',
  'NOT_REFUND': '未退款'
};
var statusNum = {
  'WAIT_PAY': 0,  // 待付款
  'GROUPBUY_PROCESS': 1,  // 待成团
  'WAIT_PICKUP': 2, // 待自提
  'PICKED_UP': 3, // 已自提
  'CANCELED': 4,  // 已取消
  'REFUNDED': 5,  // 已退款
  'COMPLAINED': 6,  // 已投
  'REVIEWING': 7, // 退款审核中
  'NOT_REFUND': 8,  // 未退款
  '': 0
};

var week = {
  0: '周日',
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六'
}
var timeDown = null;
// 测试手机：13163303806

var delayId;
Page({
  mixins: [drawSharePic, SMSMixin],
  data: {
    // iconAdd,
    // iconSub,
    goodsCount: 1,
    highTopbg: bg_highTop,
    picHost: "https://resource.pagoda.com.cn/miResourceMgr/",
    picUrl: picUrl,
    getUrl: commonObj.PAGODA_DSN_DOMAIN,
    // 状态
    state: '',
    noEnoughPeople: false, // 是否成团，还差人就显示标志
    isShowTime: true, // 显示提货时间或是成团提示
    isComposition: false, // 是否成团，显示成团时间
    isCancel: false, // 是否取消
    isRefund: false, // 能否退款
    iscomplaints: false, // 能否投诉
    isTake: false, // 是否已提货
    isMakeOrder: true, // 是否确认订单：false-》未确认，true-》在订单详情页
    isPay: false, // 是否支付成功
    clearTimeDown: false, // 倒计时
    isReady: false, // 是否加载完数据
    cancelRefund: false,
    isShowRefundAmount: false,
    isDirect: false,
    disable: false,
    isMakeOrderFlag: true,
    layh: '00',
    laym: '00',
    lays: '00',
    payPrice: '', //实付款
    obj: "",
    ct: 0, //倒计时方法中递归次数计次，实现排除本地时间影响的倒计时
    iconCur: {
      WAIT_PAY: "icon_daifukuan_green",
      PAYED: "icon_daifukuan_green",
      STOCKING: "icon_beihuozhong_green",
      SENDING: "icon_peisongzhong_green",
      WAIT_PICKUP: "icon_beihuozhong_green",
      CANCELED: "icon_yiquxiao_green",
      RECEIVED: "icon_yishouhuo_green",
      PICKED_UP: "icon_yishouhuo_green",
      REJECT: "icon_yijushou_green",
      NOT_REFUND: "icon_weituikuan_orange_big",
      COMPLAINED: "icon_yitousu_bule_big",
      REFUNDED: "icon_yituikuan_green",
      REVIEWING: "icon_shenhezhong_green",
    },
    isAfterService: false, //是否进入售后状态
    orderAudit: false, // 订单审核状态
    isShare: false,
    orderDetail: {},
    paymentDialog: false,
    pagodaMoney: true,
    subButtonGray: true,
    confirmFlag:false, // 判断是确认订单页还是订单详情页标识
    selectTimeValue:'', // 选中的时间
    showTimeValue: '', // 选中当天提货的时间展示
    timeArray:[['2019-08-02周五', '2019-08-03周六','2019-08-04周日'], ['08:00-23:00']],
    orderCancelIsShow: false, // 取消订单弹窗是否显示
    orderCancelParams: null, // 取消订单（未超时）, 保存请求接口的参数（待合并弹窗取消原因）
    invoiceBtnMap: null, // "开具发票"按钮{isAllow, status, text} - text显示文本
    titleInfo: '', // 顶部订单状态及提示信息
    deliveryInfo: '', // 送达时间
    isShowRefundReason: false, // 是否显示退款原因弹窗
    refundRejectReason: '', // 退款不通过原因
    isShow_picker:false,
    pickUpSection:'',
    memberTakeCode: '', // 提货码
    windowWidth: 375,
    packageFeeTip: '', // 包装费用提示

    //  左侧按钮数组
    leftBtnList: [],
    //  详情页是否是第三方支付
    wechatPay: false,
  },
  _data: {
    isCanSubmit: true,
    defaultCustomerAvatar,  // 用户默认头像
  },
  onLoad: function (options) {
    let that = this;
    wx.hideShareMenu()
    wx.showLoading({
      title: '加载中',
      mask: true
    })
    wx.getSystemInfo({
      success: function (res) {
        that.setData({
          viewHeight: res.windowHeight * 2,
          windowWidth: res.windowWidth
        })
      },
    })
    that.setData({
      options: options
    });

    // 设置分享海报弹窗的宽高，分享海报缩略图的宽高
    that.setPosterModelSize ()
  },

  onShow: function () {
    // 扫码页面保持屏幕长亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })
    if (!app.checkSignInsStatus()){
      app.showSignInModal({ finishToRouter: 'homeDelivery' })
      return
    }
      // 用户已登录
      const { storeInfo = {} } = wx.getStorageSync('timelyCity') || {}

      clearInterval(timeDown)
      let that = this;
      that.setData({
        clearTimeDown: false,
        ct: 0
      })
      let store = storeInfo;
      let user = wx.getStorageSync('user')
      let options = that.data.options;
      // isAllOrder=ture -> 团单 对象：goodsDetailObj        isAllOrder=flase  -> 商品详情  对象：myAllOrderObj
      // isJoin=true -> 参团                                  isJoin=false -> 参团开团
      // v2.1 获取存储中的门店信息只在确认订单页才用到，订单详情页用不到
      // that.setData({
      //   store: store
      // })
      // bindCardObj进小程序是未登录的情况下。
      // reDirectObj是取消支付后，再进入订单详情页的情况。
      // fromTemplate   模板消息进来的情况
      // groupLeaderObj 是开团者(团长)，从分享出去的好友页链接进入的。直接跳到订单详情
      // reDirectObj  订单详情页面内状态产生变化，带参数重定向回当前页面
      if (options && options.reDirectObj) {

        var obj = JSON.parse(options.reDirectObj)
        sensors.pageShow('fightGroupsOrderDetailPage') // 浏览页面上报神策
        that.getOrderInfo(obj.goodsOrderID, obj.payOrderID, obj.payStatus) // 订单详情流程
        that.setData({
          isDirect: true
        })
      } else if (options && options.fromTemplate) {
        let goodsorderId = options.goodsOrderID || '';
        let payorderId = options.payOrderID || '';
        sensors.pageShow('fightGroupsOrderDetailPage') // 浏览页面上报神策
        that.getOrderInfo(goodsorderId, payorderId, options.orderStatus) // 订单详情流程
      } else if (options && options.groupLeaderObj) {
        if (typeof options.groupLeaderObj === 'undefined') {
          wx.hideLoading()
          commonObj.showModal('提示', '数据获取异常。', false, '回到首页', '', function () {
            wx.redirectTo({
              url: '/fightGroups/pages/fightGroups/index'
            });
          })
        } else {
          var obj = JSON.parse(options.groupLeaderObj)
          let goodsorderId = obj.goodsOrderID || '';
          let payorderId = obj.payOrderID || '';
          sensors.pageShow('fightGroupsOrderDetailPage') // 浏览页面上报神策
          that.getOrderInfo(goodsorderId, payorderId, obj.orderStatus) // 订单详情流程
        }
      } else {
        let optionsobj = getOptions(options)
        if (typeof options === 'undefined' || typeof optionsobj === 'undefined') {
          wx.hideLoading()
          commonObj.showModal('提示', '数据获取异常。', false, '回到首页', '', function () {
            wx.redirectTo({
              url: '/fightGroups/pages/fightGroups/index'
            });
          })
        } else {
          var obj = JSON.parse(getOptions(options));
          if (obj.isAllOrder) {
            sensors.pageShow('fightGroupsOrderDetailPage') // 浏览页面上报神策
            that.getOrderInfo(obj.goodsOrderID, obj.payOrderID, obj.payStatus) // 订单详情流程
            that.setData({
              goodsOrderID: obj.goodsOrderID
            }) // 设置goodsOrderID
            console.log('团单')
          } else {
            that.setData({
              store: store
            })
            if (obj.isJoin) {
              // 如果是参团，那么会有接收这三个参数 groupId openerId goodsId
              console.log('参团')
              that.setData({
                groupID: obj.groupID,
                openerID: obj.openerID,
                goodsID: obj.goodsID,
                activityID: obj.activityID
              })
            } else {
              // 如果是开团，那么只接收一个参数 goodsId
              console.log('开团')
              that.setData({
                groupID: -1,
                openerID: -1,
                goodsID: obj.goodsID,
                activityID: obj.activityID
              })
            }
            // 确认订单流程
            sensors.pageShow('fightGroupsConfirmOrderPage') // 浏览页面上报神策
            that.orderAndSettlement(user, store, obj.goodsID, obj.activityID, store.lon, store.lat, this.data.goodsCount);
            // that.getPackageFeeTip({
            //   cityID: (store || {}).cityID
            // })
          }
        }
    }
  },
  onHide:function() {
    // 离开页面关闭扫码页面保持屏幕长亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
    this.setData({
      clearTimeDown: true
    })
  },
  onUnload:function() {
    // 离开页面关闭扫码页面保持屏幕长亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
    this.setData({
      clearTimeDown: true
    })
    try {
      wx.getStorageSync('goodsOrderID') && wx.removeStorageSync('goodsOrderID')
    } catch (e) {

    }
  },

  /**
   * 设置分享海报弹窗的宽高，分享海报缩略图的宽高
   */
   async setPosterModelSize () {
    const { modelWidth, modelHeight, smallPosterWidth, smallPosterHeight } = await getPosterModelSize()
    this.setData({
      modelWidth,
      modelHeight,
      smallPosterWidth,
      smallPosterHeight
    })
  },

  //******************功能函数*******************/
  // 已确认订单的    商品订单详情    包括付款和未付款
  // goodsOrderID-订单号，payStatus-是否支付
  getOrderInfo: function (goodsOrderID, payOrderID, payStatus) {
    console.log('已确认订单的团单详情')
    wx.setNavigationBarTitle({
      title: '订单详情',
    })
    var that = this;
    var user = wx.getStorageSync('user');
    // 根据订单是否付款来请求不同的接口
    if (payStatus === 'SUCCESS') { //success为支付订单详情
      //  接口文档：http://wiki.pagoda.com.cn/pages/viewpage.action?pageId=3705687
      var options = {
        header: {
          'X-DEFINED-appinfo': JSON.stringify({
            "channel": "miniprogram", //渠道
            "model": "iPhone 6",
            "os": "iOS 10.1.1",
            "verName": "*******",
            "deviceID": "35EAAF0C-D590-4F40-8F73-73735FDC02E5",
          }),
          "userToken": user.userToken
        },
        url: `/api/v1/wxmini/order/detail/${user.userID}/${goodsOrderID}`
      }
      that.setData({
        isPay: true
      })

      wx.setStorageSync('goodsOrderID', goodsOrderID);
      that.setData({
        goodsOrderID: goodsOrderID
      })
      wx.setStorageSync('orderInfo1', {
        payOrderID: payOrderID,
        payStatus: payStatus
      });
      that.payOrderRequest(options);
    } else { //其他为商品订单详情
      // 未支付
      //  接口文档：http://wiki.pagoda.com.cn/pages/viewpage.action?pageId=7637004
      var options = {
        header: {
          'X-DEFINED-appinfo': JSON.stringify({
            "channel": "UAT\U6e20\U9053", //渠道
            "model": "iPhone 6",
            "os": "iOS 10.1.1",
            "verName": "*******",
            "deviceID": "35EAAF0C-D590-4F40-8F73-73735FDC02E5",
          }),
          "userToken": user.userToken
        },
        data: {
          customerID: user.userID,
          payOrderID: payOrderID // 支付订单ID
        },
        url: '/api/v1/groupBuy/order/queryPayOrderDetail' // 查询支付订单详情（未支付）
      }

      that.setData({
        isPay: false
      })
      wx.setStorageSync('orderInfo1', {
        payOrderID: payOrderID,
        payStatus: payStatus
      });
      that.noPayOrderRequest(options);
    }
    that.setData({
    })
  },
  // 未支付订单查询订单详情
  noPayOrderRequest: function (options) {
    let that = this;
    let obj;
    commonObj.requestData(options, function (res) {

      console.log(res);
      if (res.data && Number(res.data.errorCode) === 0) {
        let data = res.data.data;
        var goodsList = data.goodsOrderList[0].goodsList[0];
        let store = data.goodsOrderList[0].store; // 提货门店信息
        let lastTime = data.lastUpdate.substring(0, 10); // 最后操作时间

        let groupInfo = data.groupInfo;
        // V1.8 判断是开团还是参团
        if (String(groupInfo.openerID) === String(options.data.customerID)) { // 开团
          that.setData({
            groupID: -1
          })
        } else {
          that.setData({
            groupID: groupInfo.groupID
          })
        }
        switch (statusNum[data.orderFlowState]) {
          case 0:
            that.setData({
              isComposition: false,
              serverCurrTime: res.data.systemTime
            }); // 订单状态待付款
            that.countDown('', data.timeExpire, true);
            break;
          case 4:
            that.setData({
              isCancel: true,
              isComposition: false
            }); // 订单状态已取消   Y
            // var takeTime = data.goodsOrderList[0].deliverySection; //that.dealWithTime(data.pickupTime, lastTime);          // 取货时间
            break;
        }
        let takeTime = data.goodsOrderList[0].deliverySection;
        // 拼团价格
        // let groupyuan = parseInt(goodsList.groupPrice / 100)
        // let groupfen = ((goodsList.groupPrice / 100).toFixed(2) + '').slice(-2);

        // 支付总额
        // let yuan = parseInt(data.payAmount / 100)
        // let fen = ((data.payAmount / 100).toFixed(2) + '').slice(-2);

        obj = {
          goodsCount: goodsList.count,
          orderStatus: status[data.orderFlowState] || '待付款', // 订单状态
          goodsID: goodsList.goodsID,
          goodsName: goodsList.goodsName,
          spec: goodsList.spec,
          // subTitle: goodsList.subTitle, //未传该字段
          pic: picUrl + goodsList.headPic, // 商品图片url
          payAmount: data.payAmount, // 支付价格，以分为单位
          price: data.payAmount,
          groupprice: goodsList.groupPrice, // 处理后团单价格
          oriPrice: goodsList.originalPrice || '', // 处理后原价
          lastUpdate: data.lastUpdate, // 最后操作时间
          //isRefund: data.isAllowRefund, //未传该字段                            // 是否能投诉退款
          payOrderID: data.payOrderID,
          payOrderNum: data.payOrderNum,
          takeTime: takeTime || '',
          groupSize: goodsList.groupSize,
          activityID: goodsList.activityID,
          color: true // 用于在template中来区别样式
        }

        that.setData({
          noPayOrderInfo: data,
          timeExpire: data.timeExpire,
          store: store, // 提货门店信息
          obj: obj, // 商品信息
          lack: data.payAmount > data.mainBalance,
          pagodaMoney: app.globalData.reqPayType === 0 ?  !(data.payAmount > data.mainBalance):false, // 请求到为0 则走原先的逻辑 为1则按照配置1
          mainBalance: Number((data.mainBalance / 100).toFixed(2)),
          goodsID: obj.goodsID,
          createTime: data.createTime,
          lastUpdate: data.lastUpdate,
          groupShareTitle: goodsList.groupShareTitle, // 分享标题
          groupSharePic: goodsList.groupSharePic, // 分享图片该字段未传
          isReady: true,
          isShow: true,
          titleInfo: data.titleInfo, // 顶部订单状态及提示信息
          deliveryInfo: data.deliveryInfo, // 送达时间
          topCountDown: status[data.orderFlowState] === "待付款" || data.orderFlowState === "",
          pickUpSection:data.pickUpSection,
        })

        //  未支付订单设置左侧按钮列表
        that.setLeftBtnList(data);

        let timelyCity = wx.getStorageSync('timelyCity') || {}
        let user = wx.getStorageSync('user') || {}
        let params = {
          storeID: timelyCity.storeID || '-1',
          amount: data.payAmount,
          customerID: user.userID,
        }
        that.data.lack && that.rechargeGuide(params)
         // 已取消不显示包装费
        if (data.orderFlowState === 'CANCELED') {
          that.setData({
            packageFeeTip: ''
          })
        } else {
          // that.getPackageFeeTip({
          //   cityID: (store || {}).cityID
          // })
        }
      } else {
        wx.showModal({
          title: '提示',
          content: '请求超时，系统繁忙',
          showCancel: false
        })
      }
    }, '', function () {
      wx.hideLoading();
    })
  },
  // 已支付订单查询订单详情
  payOrderRequest: function (options) {
    let that = this;
    commonObj.requestNewData(options, function (res) {

      if (res.data && Number(res.data.errorCode) === 0) {
        let data = res.data.data;
        that.setData({
          wechatPay : data.payment.channel.indexOf('gb') > -1 ? '1' : '2',
          orderDetail: res.data.data,
          refundChannel: data.payment.channel.indexOf('gb') > -1 ? '1' : '2'
        })
        // 果币支付 refundChannel 传1；微信支付传2
        console.log('that.data.orderDetail')
        console.log(that.data.orderDetail)
        let goodsList = data.goodsList[0]; // 商品信息
        let store = data.store;
        store.openingTime = store.startTime + '-' + store.endTime || '';
         // 提货门店信息
        let takeCode = data.takeCode; // 提货码
        var code = /\S{5}/.test(takeCode) && takeCode.replace(/\s/g, '').replace(/(.{4})/g, "$1 ");
        let lastTime = data.lastUpdate.substring(0, 10); // 最后操作时间
        let groupInfo = data.group;
        // 处理一些数据格式，如价格，时间
        // 支付总额
        // let yuan = parseInt(data.payAmount / 100)
        // let fen = ((data.payAmount / 100).toFixed(2) + '').slice(-2);

        // 拼团总额
        // let groupyuan = parseInt(goodsList.price / 100)
        // let groupfen = ((goodsList.price / 100).toFixed(2) + '').slice(-2);

        //判断是否是支付过的已取消订单
        if (data.isAllowCancel === "N") {
          that.setData({
            cancelRefund: true,
            serverCurrTime: res.data.systemTime
          })
        } else {
          that.setData({
            cancelRefund: false,
            serverCurrTime: res.data.systemTime
          })
        }
        // 已付款情况下，根据订单状态判断各个时间的显示
        switch (statusNum[data.currentFlowStatus]) {
          case 0: // 0 订单状态待付款
          {
            that.setData({
              isComposition: false
            });
            that.countDown('', groupInfo.expireTime, true);
            break;
          }
          case 1: // 1  订单状态待成团
          {
            that.setData({
              isComposition: false,
              topCountDown: status[data.currentFlowStatus] === "待成团"
            });
            that.countDown('', groupInfo.expireTime, true);
            break;
          }
          case 2: // 2  订单状态待提货
          {
            that.setData({
              isTake: false,
              isComposition: true,
              topCountDown: status[data.currentFlowStatus] === "待提货"
            });
            var completeTime = groupInfo.completeTime; // 成团时间
            break;
          }
          case 3: // 3 订单状态已提货
          {
            var completeTime = groupInfo.completeTime; // 成团时间
            var isRefund = data.isAllowRefund === 'Y' ? true : false; // 是否能退款
            var iscomplaints = data.iscomplaints === 'Y' ? true : false; // 是否能申诉
            var confirmTime = data.confirmTime; // 提货时间
            that.setData({
              isTake: true,
              isComposition: true
            });
            break;
          }
          case 4: // 4 订单状态已取消
          {
            var completeTime = groupInfo.completeTime; // 成团时间
            var flag = groupInfo.status === 'SUCCESS' // 判断式否是已成团取消订单还是未成团取消订单
            that.setData({
              isCancel: true,
              isShowRefundAmount: false, // 2.8拼团订单取消订单不显示退款金额
              isComposition: flag
            });
            break;
          }
          case 5: // 5 订单状态已退款
          {
            var isRefund = data.isAllowRefund === 'Y' ? true : false;
            var completeTime = groupInfo.completeTime; // 成团时间
            var confirmTime = data.confirmTime; // 提货时间
            let complaintRefundAmount = [];
            let refundList = data.refundList
            complaintRefundAmount = refundList.filter(item => {
              return Number(item.type) === 2
            }).map(item => {
              let refundAggregate = item.refundAmount ? item.refundAmount : item.refundFruitCoins;
              // let refundAmountList = [parseInt(refundAggregate / 100), ((refundAggregate / 100).toFixed(2) + '').slice(-2)];
              return {
                refundAggregate,
                // refundAmountList,
                ...item
              }
            })
            that.setData({
              isTake: true,
              isComposition: true,
              isAfterService: true,
              complaintRefundAmount: complaintRefundAmount,
              isShowRefundAmount: true,
              cancelRefund: false
            });
            break;
          }
          case 6: //6 订单投诉状态
          {
            var completeTime = groupInfo.completeTime; // 成团时间
            var confirmTime = data.confirmTime; // 提货时间
            that.setData({
              isTake: true,
              isComposition: true,
              isAfterService: true,
              cancelRefund: false
            });
            break;
          }
          case 7:
          {
            var completeTime = groupInfo.completeTime; //7 订单审核状态
            var confirmTime = data.confirmTime; // 提货时间
            var auditObj = data.refundComplaint
            // auditObj.auditList = [parseInt(data.refundComplaint.refundAmount / 100), ((data.refundComplaint.refundAmount / 100).toFixed(2) + '').slice(-2)]
            that.setData({
              isTake: true,
              isComposition: true,
              isAfterService: true,
              cancelRefund: false,
              orderAudit: true,
              auditObj: auditObj
            });
            break;
          }
          case 8: //8 订单未退款
          {
            var completeTime = groupInfo.completeTime; // 成团时间
            var confirmTime = data.confirmTime; // 提货时间

            that.setData({
              isTake: true,
              isComposition: true,
              isAfterService: true,
              cancelRefund: false
            });
            break;
          }
        }
        // console.log(code)
        // console.log(that.data.cancelRefund)
        let obj = {
          goodsCount: goodsList.count,
          orderStatus: status[data.currentFlowStatus], // 订单状态
          takeCode: code, // 提货码
          goodsID: goodsList.id, // 商品ID
          goodsName: goodsList.name, // 商品名
          spec: goodsList.spec, // 商品规格
          subTitle: goodsList.subTitle, // 商品副标题
          pic: picUrl + goodsList.headPic, // 商品图片url
          payAmount: data.payAmount, // 支付价格，以分为单位
          price: data.payAmount, // 处理后支付价
          groupprice: goodsList.price, // 处理后拼团价
          oriPrice: goodsList.originalPrice , // 处理后原价
          lastUpdate: data.lastUpdate, // 最后操作时间 已取消的时候，是取消时间；已退款的时候，是退款时间
          completeTime: completeTime || '', // 成团时间

          confirmTime: confirmTime || '', // 收货时间，也就是实际提货时间
          takeTime: data.deliverySection || '', // 提货时间

          groupSize: groupInfo.groupSize,
          currentNum: (groupInfo.groupSize - groupInfo.currentCount) || '',
          show: false, // 用于在template中来区别样式
          color: true // 用于在template中来区别拼团价格样式
        }
        // 判断“开发票”按钮
        let invoiceBtnMap = that.invoiceBtn(data)

        that.setData({
          currentFlowStatus: data.currentFlowStatus, //当前订单状态
          store: store, // 提货门店信息
          obj: obj, // 商品信息
          activityID: groupInfo.groupActivityInfo.id, // 已支付/未支付 的订单详情，路由没有option，所以activityID从接口返回
          activityName: groupInfo.groupActivityInfo.name,
          createTime: data.createTime,
          groupSharePic: goodsList.headPic, // 分享图片
          groupShareTitle: groupInfo.groupActivityInfo.shareTitle, // 分享标题
          isRefund: isRefund || '', // 是否能退款
          iscomplaints: iscomplaints || '', // 是否能投诉
          isReady: true,
          isShow: true,
          number: data.number, //支付订单订单编号
          groupInfo: groupInfo,
          invoiceBtnMap,
          titleInfo: data.title, // 顶部订单状态及提示信息
          deliveryInfo: data.deliveryInfo, // 送达时间
          pickUpSection:data.pickUpSection, //v2.4新增可提货时间段 string
          refundRejectReason: data.refundComplaint && data.refundComplaint.refundRejectReason // 退款不通过原因
        })

        //  已支付订单设置左侧按钮列表
        that.setLeftBtnList(data, true);

        var goodsOrderID = groupInfo.groupMemberList.length ? groupInfo.groupMemberList[0].goodsOrderId : ''
        wx.setStorageSync('orderInfo1', {
          payOrderID: data.payment.id,
          payStatus: 'SUCCESS'
        });
        wx.setStorageSync('orderInfo2', {
          goodsOrderID: data.id,
          goodsOrderNum: data.number,
          payOrderNum: data.payment.number
        });
        // console.log('takeCode', takeCode)
        if (takeCode) {
          that.drawCode(takeCode)
        }
        that.canExecuteDraw()
        // 已取消不显示包装费
        if (data.currentFlowStatus === 'CANCELED') {
          that.setData({
            packageFeeTip: ''
          })
        } else {
          // that.getPackageFeeTip({
          //   cityID: (store || {}).cityID
          // })
        }
      } else {
        // wx.showModal({
        //   title: '提示',
        //   content: '请求超时，系统繁忙',
        //   showCancel: false
        // })
        commonObj.showModal('提示', '页面超时,请刷新!', false, '确认', '', function () {
          wx.redirectTo({
            url: '/fightGroups/pages/fightGroups/index'
          });
        })
      }
    }, '', function () {
    })
  },

  /**
   * 设置左侧按钮列表
   * @param { object } order 订单
   * @param { boolean } isPay 是否已支付订单
   */
   setLeftBtnList(order, isPay) {
    //  在交易成功后，showOrderStatus为1说明交易完成，详见接口文档
    const isAfterSuccess = order.showOrderStatus === '1';
    const notAllowRefund = order.isAllowRefund === 'N';

    const isCancel = this.data.isCancel;
    const canDeleteOrder = (isAfterSuccess && notAllowRefund)|| isCancel;

    const leftBtnList = [];

    if (canDeleteOrder) {
      leftBtnList.push({
        label: '删除订单',
        callback: () => this.deleteOrder(isPay)
      })
    }

    const { invoiceBtnMap, orderDetail } = this.data

    if (invoiceBtnMap && invoiceBtnMap.isAllow && orderDetail.isSupportComment === 'Y') {
      leftBtnList.push({
        label: invoiceBtnMap.text,
        callback: this.onInvoiceBtnTap
      })
    }

    this.setData({
      leftBtnList
    });
  },

  /**
   * 拼团订单删除订单
   * @param { boolean } isPay 是否已支付订单
   * @returns
   */
  async deleteOrder(isPay) {
    const showModalRes = await app.showModalPromise({
      title: '确认删除订单吗？',
      content: `订单删除后您无法查看该笔订单，也无法对该笔订单开具发票和评价。`,
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消'
    })
    //  弹窗取消，此处可以进行行为上报
    if (!showModalRes) {
      return
    }

    const user = wx.getStorageSync('user');

    const data = {
      /**
       * 因为后端没有返回该订单的订单类型，所以
       * orderType此处写死为40，对应的是serverless中的常量类型
       */
      orderType: 40,
      customerID: user.userID,
      orderNoList: isPay ? [this.data.orderDetail.id] : this.data.noPayOrderInfo.goodsOrderList.map(order => order.goodsOrderID)
    }

    try {
      await app.api.updateUserHide(data);

      const pages = getCurrentPages();
      const orederListPageName = 'userB/pages/orderList/index';
      const prevPage = pages[pages.length - 2]
      //  上个页面为订单列表页
      if (prevPage.route === orederListPageName) {
        prevPage.setData({ mix_updateOrder_singleUpdateDelete: true })
        wx.navigateBack();
      } else {
        // 去订单列表页
        wx.redirectTo({
          url: `/${orederListPageName}?type=B`
        });
      }
    } catch (error) {
      wx.showModal({
        content: '订单删除失败'
      })
    }
  },

  async drawCode (code) {
    let that = this
    let width = this.data.windowWidth * 0.8
    const jsBarcode = await JsBarcode
    let e = jsBarcode("#", code, {
      format: "CODE128"
    })
    let codeData = e._encodings[0][0].data
    let config = {
      height: 80,
      width: 2,
      marginTop: 10,
      marginleft: this.data.windowWidth*0.1,
      lineColor: "black"
    }
    let context = wx.createCanvasContext('barcode')
    let top = config.marginTop;
    context.strokeStyle = "#000000"
    config.width = width / codeData.length
    context.lineWidth = config.width
    let s = config.marginleft
    for (var i = 0; i < codeData.length; i++) {
      var l = s + i * config.width;
      if (codeData[i] === "1") {
        context.beginPath()
        context.moveTo(l, top)
        context.lineTo(l, top + config.height)
        context.stroke()
      }
    }
    context.draw(false, function () {
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: 750,
        height: 150,
        quality:1,
        canvasId: 'barcode',
        success: function (res) {
          console.log(res)
          that.setData({
            memberTakeCode: res.tempFilePath
          })
          wx.hideLoading()
        },
        fail: function (res) {
          wx.hideLoading()
        }
      })
    })
  },
  canExecuteDraw () {
    let {
      headPic,
      price,
      spec
    } = this.data.orderDetail.goodsList[0]
    const count = this.data.groupInfo.groupActivityInfo.sellCount
    this.drawSharePic({
      canvasId: 'sharePicCanvas',
      goodsInfo: {
        isStarted: true,
        goodsPicUrl: this.data.picUrl + headPic,
        price,
        count,
        spec
      }
    })
  },
  /*******确认订单流程***********/
  /** 1.拼团订单结算接口   在这个函数里面才加入拼团信息，就是团id和开团人id
   *  2.点击付款按钮
   *  3.获取商品详情
   *  4.确认订单
   *  5.付款
   */

  /**拼团订单结算接口 */
  orderAndSettlement: function (user, store, goodsID, activityID, lon, lat, count = 1) {
    console.log(store)
    let that = this;
    wx.setNavigationBarTitle({
      title: '确认订单',
    })
    console.log("确认订单")
    that.setData({
      isMakeOrder: false,
      isMakeOrderFlag: false
    });
    let timelyCity = wx.getStorageSync('timelyCity') || {}
    var options = {
      data: {
        customerID: user.userID,
        cityID: timelyCity.cityID,
        storeID: store.storeID,
        lon: lon,
        lat: lat,
        goodsID: goodsID,
        activityID: activityID,
        count
      },
      url: '/api/v1/groupBuy/order/settlement'
    }

    commonObj.requestData(options, function (res) {
      console.log('settlement接口',res);
      if (res.data && Number(res.data.errorCode) === 0) {
        let { unAvailableGoosList } = res.data.data
        // console.log('加入购物车拼团订单结算')
        var data = res.data.data.inTimeGoosList[0] || unAvailableGoosList[0] || {};
        var store = res.data.data.store;
        if (data && data.isGoodsValid === 'Y') {
          var saleOut = false;
          that.setData({
            disable: false
          })
        } else {
          var saleOut = true;
          that.setData({
            disable: true
          })
        }
        // let newPrice = [parseInt(res.data.data.payAmount / 100), ((res.data.data.payAmount / 100).toFixed(2) + '').slice(-2)] // payAmount 实际支付价格
        // let groupPrice = [parseInt(data.groupPrice / 100), ((data.groupPrice / 100).toFixed(2) + '').slice(-2)] // 拼团价
        // let originalP = data.originalPrice ? (data.originalPrice / 100).toFixed(2) : ''; // 原价
        // console.log(data)
        let obj = { // 团信息
          goodsCount: data.count,
          limitCount: data.limitCount || 1,
          orderStatus: '待付款',
          goodsName: data.goodsName,
          subTitle: data.subTitle,
          price: res.data.data.payAmount,
          groupprice: data.groupPrice,
          oriPrice: data.originalPrice || '',
          spec: data.spec,
          pic: picUrl + data.headPic,
          payAmount: res.data.data.payAmount,
          saleOut: saleOut,
          groupSize: data.groupSize,
          takeTime: res.data.data.pickupTime,
          show: true,
          stockNum: data.stockNum,
          realSaleCount: (that.options.goodsDetailObj && JSON.parse(that.options.goodsDetailObj) && JSON.parse(that.options.goodsDetailObj).realSaleCount) || ''
        }

        // 时间

        that.setData({
          user,
          store,
          goodsID,
          activityID,
          lon,
          lat,
          obj,
          lack: res.data.data.payAmount > res.data.data.mainBalance,
          pagodaMoney: app.globalData.reqPayType === 0 ?!(res.data.data.payAmount > res.data.data.mainBalance):false, // 请求到为0 则走原先的逻辑 为1则按照配置1
          mainBalance: Number((res.data.data.mainBalance / 100).toFixed(2)),
          isComposition: false,
          isPay: false,
          isReady: true,
          payPrice: `${obj.price[0]}.${obj.price[1]}`,
          confirmFlag: true,
          timeArray:res.data.data.pickupTimeLimits,
          serverCurrTime: res.data.systemTime,
          deliveryTimeType: data.deliveryTimeType
        })
        let params = {
          storeID: store.storeID || '-1',
          amount: res.data.data.payAmount,
          customerID: user.userID,
        }
        that.data.lack && that.rechargeGuide(params)
        var page = getCurrentPages();
        for (let item in page) {
          if (page[item] && page[item].route.indexOf('fightGroups/pages/goodsDetail/index') > -1) {
            page[item].data.showPayModal = true;
          }
        }
        if (saleOut) {
          commonObj.showModal('提示', '这个果果卖的太火爆啦，在当前门店已经卖光啦，回到首页开个新团吧！', true, '开个新团', '取消', function (res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/fightGroups/pages/fightGroups/index'
              });
            }
          })

        }
      } else if (Number(res.data.errorCode) === 30112) {
        commonObj.showModal('提示', '该果果不在拼团活动时间内了，请换个果果吧~', false, '确认', '', function () {
          wx.redirectTo({
            url: '/fightGroups/pages/fightGroups/index'
          });
        })
      } else if (Number(res.data.errorCode) === 31042) {
        commonObj.showModal('提示', '抱歉，当前账户异常', false, '确认', '', function () {
          wx.redirectTo({
            url: '/fightGroups/pages/fightGroups/index'
          });
        })
      } else if (Number(res.data.errorCode) === -1002) {
        commonObj.showModal('提示', '您的登录已过期，是否重新登录？', true, '我知道了', '', function (res) {
          if (res.confirm) {
            app.signOut()
            app.signIn()
          } else if (res.cancel) {
            app.signOut()
            wx.reLaunch({
              url: '/pages/homeDelivery/index'
            })
          }
        })
      } else {
        wx.showModal({
          title: '提示',
          content: '请求超时，系统繁忙',
          showCancel: false
        })
      }
    }, '', function () {
      wx.hideLoading()
      that.effect(activityID, goodsID, user.userID);
    })
  },
  // 付款按钮
  makeOrderBtn: function () {
    let that = this;
    if (!that._data.isCanSubmit) return
    that._data.isCanSubmit = false
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110200003',
        element_name: '立即支付',
        element_content: '立即支付按钮',
        screen_code:'1102'
      })
    }
    wx.reportAnalytics('makeorder_paybtn');
    if (!this.data.selectTimeValue) {
      // commonObj.showModal('请选择意向提货时间', '以帮助我们更准确的备货，其他提货时间段也可到店自提', false);
      this.setData({
        isShow_picker: true
      })
      that._data.isCanSubmit = true
      return
    }
    commonObj.showModal('仅可在选定门店取货哦!', '取货地址:' + that.data.store.address, true, '确认', '修改', function (res) {
      if (res.confirm) {
        if (app.globalData.reportSensors) {
          app.sensors.track('MPClick', {
            element_code: '110201002',
            element_name: '确认提货地址',
            element_content: '确认按钮',
            screen_code:'1102'
          })
        }
        wx.showLoading({
          title: '结算中...',
          mask: true
        })
        if (app.checkSignInsStatus()) {
          // 拼团进度通知[0] 自提提醒[1] 订单进度提醒[2]
          app.requestSubscribeMessage({
            tmplIds: ['w6rSd8tTLTz5ixWFxh46P9VvBSduA7HGXyWg-TSd8V0', 'V97E1jeQHgmdmxSd6kfdCtBziR5By29vOJwFOS_GrvA', 'fE11t0uK0ugcX6MOaa0iY12sy111DIInJE8B_DM_5LI'],
          }, () => {
            wx.showLoading({
              title: '结算中...',
              mask: true
            })
            sensors.safeGuardSensor('pay')
            that.makeOrderFuc()
          })
        } else {
          that._data.isCanSubmit = true
          app.signIn()
        }
      } else {
        that._data.isCanSubmit = true
        if (app.globalData.reportSensors) {
          app.sensors.track('MPClick', {
            element_code: '110201001',
            element_name: '修改提货地址',
            element_content: '修改按钮',
            screen_code:'1102'
          })
        }
        let obj = JSON.stringify({
          goodsID: that.data.goodsID
        })
        wx.navigateTo({
          url: '/fightGroups/pages/selfExtractStore/index?PTorderDetailObj=' + obj,
        })
      }
    })
    this.preventEvent();

  },
  /**确认订单 */
  makeOrderFuc: function (groupID, openerID) {
    let that = this;
    if (!wx.getStorageSync('wxSnsInfo')) {
      commonObj.showModal('提示', '账号异常，请联系管理员4001811212', false);
      that._data.isCanSubmit = true
      return;
    }
    let wxInfo = wx.getStorageSync('userNameAndImg') || {};
    let wxSnsInfo = wx.getStorageSync('wxSnsInfo');
    let timelyCity = wx.getStorageSync('timelyCity') || {};
    let store = timelyCity.storeInfo || {}
    let user = wx.getStorageSync('user');

    var options = {
      loginOutBackHomePage: true,
      data: {
        customerID: user.userID, // 参团人customerID
        cityID: timelyCity.cityID,
        storeID: store.storeID,
        payAmount: that.data.obj.payAmount, // 交易额，单位 分
        receiveAddrID: store.storeID,
        groupID: groupID ? groupID : that.data.groupID, // 拼团ID   ，首次开团 -1
        openerID: openerID ? openerID : that.data.openerID, // 开团人ID ，首次开团 -1
        wxProfileUrl: wxInfo.avatarUrl || that._data.defaultCustomerAvatar, // 参团人头像url
        wxOpenID: wxSnsInfo.openid, // 参团人openID
        wxUnionID: wxSnsInfo.unionid, // 参团人openID
        wxNickName: wxInfo.nickName || '果宝宝', // 参团人微信名
        activityID: that.data.activityID,
        deliverySection: that.data.selectTimeValue || that.data.obj.takeTime //v2.3新传的提货时间
      },
      url: '/api/v1/groupBuy/order/submit'
    }
    commonObj.requestData(options, function (res) {
      if (Number(res.data.errorCode) === 0) {
        var data = res.data.data;
        that.setData({
          paymentNo: data.payOrderNo,
          timeExpire: data.timeExpire
        })
        that.payRequest(data.payOrderNo, data.payOrderID);

      } else if (Number(res.data.errorCode) === 30102) {
        wx.hideLoading();
        commonObj.showModal('提示', '已参加过该团', false, '确认', '', function () {
          wx.redirectTo({
            url: '/fightGroups/pages/fightGroups/index'
          });
        })
      } else if (Number(res.data.errorCode) === 30101) {
        //that.groupIsFull();
        wx.hideLoading();
        that._data.isCanSubmit = true
        commonObj.showModal('', '来晚了，您所参加的团已满', true, '开个新团', '返回重选', function (res) {
          if (res.confirm) {
            that.makeOrderFuc('-1', '-1');
          } else if (res.cancel) {
            var obj = JSON.parse(that.data.options.goodsDetailObj)
            var fightGroupsIndexObj = {
              goodsID: obj.goodsID,
              activityID: obj.activityID
            };
            wx.navigateTo({
              url: '/fightGroups/pages/goodsDetail/index?fightGroupsIndexObj=' + JSON.stringify(fightGroupsIndexObj)
            })
          }
        })
      } else if (Number(res.data.errorCode) === 30103) {
        wx.hideLoading();
        commonObj.showModal('提示', '未找到参团商品信息', false, '确认', '', function () {
          wx.redirectTo({
            url: '/fightGroups/pages/fightGroups/index'
          });
        })
      } else if (Number(res.data.errorCode) === 30104) {
        wx.hideLoading();
        commonObj.showModal('提示', '所参团已失效', false, '确认', '', function () {
          wx.redirectTo({
            url: '/fightGroups/pages/fightGroups/index'
          });
        })
      } else if (Number(res.data.errorCode) === 30113) {
        wx.hideLoading();
        commonObj.showModal('提示', '您购买件数已达上限啦，去首页拼其他果品吧', false, '确认', '', function () {
          wx.redirectTo({
            url: '/fightGroups/pages/fightGroups/index'
          });
        })
      } else if (Number(res.data.errorCode) === 31000) {
        wx.hideLoading();
        commonObj.showModal('提示', '这个果果卖的太火爆啦,在当前门店已经卖光啦,回到首页开个新团吧！', false, '确认', '', function () {
          wx.redirectTo({
            url: '/fightGroups/pages/fightGroups/index'
          });
        })
      } else if (Number(res.data.errorCode) === 30117 || Number(res.data.errorCode) === 30119) {
        // commonObj.showModal('提示', '该团仅限新用户参团您可以开个新团哦~', true, '我来开团', '返回重选', function (res) {
        //   if (res.confirm == true) {
        //     that.makeOrderFuc('-1', '-1');
        //   } else if (res.cancel == true) {
        //     var obj = JSON.parse(that.data.options.goodsDetailObj)
        //     var fightGroupsIndexObj = {
        //       goodsID: obj.goodsID,
        //       activityID: obj.activityID
        //     };
        //     wx.navigateTo({
        //       url: '/fightGroups/pages/goodsDetail/index?fightGroupsIndexObj=' + JSON.stringify(fightGroupsIndexObj)
        //     })
        //   }
        // })
        commonObj.showModal('提示', '该团仅限新用户参团您可以开个新团哦~', false, '我来开团', '', function () {
          // if (res.confirm == true) {
          that._data.isCanSubmit = true
          that.makeOrderFuc('-1', '-1');
          // }
        })
      } else if (Number(res.data.errorCode) === 30124 || Number(res.data.errorCode) === 30123) {
        wx.hideLoading();
        that._data.isCanSubmit = true
        var obj = JSON.parse(that.data.options.goodsDetailObj)
        that.orderAndSettlement(user, store, obj.goodsID, obj.activityID, store.lon, store.lat, that.data.goodsCount);
      } else if (Number(res.data.errorCode) === -1002) {
        commonObj.showModal('提示', '您的登录已过期，是否重新登录？', true, '我知道了', '', function (res) {
          if (res.confirm) {
            that._data.isCanSubmit = true
            app.signOut()
            app.signIn()
          } else if (res.cancel) {
            app.signOut()
            wx.reLaunch({
              url: '/pages/homeDelivery/index'
            })
          }
        })
      } else {
        wx.hideLoading();
        wx.showModal({
          title: '提示',
          content: res.data.errorMsg || '系统繁忙,请稍后重试！',
          showCancel: false
        })
        that._data.isCanSubmit = true
      }
    }, function (res) {
      that._data.isCanSubmit = true
    }, '')
  },
  // 付款按钮,订单确认按钮 最终生成订单号，然后传到payRequest函数，请求支付
  // 支付成功，则去paySuccess页
  // 支付取消，则重新刷新页面。然后带上 goodsOrderID,payOrderID,payStatus
  payrightNow: function () {
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110300005',
        element_name: '立即付款',
        element_content: '立即付款',
        screen_code:'1103'
      })
    }
    this.payRequest();
    wx.reportAnalytics('groupinfo_paybtn');
  },
  stopBubbling() {
    return
  },
  hidePaymentWay() {
    this.setData({
      paymentDialog: false
    })
  },
  paymentWay() {
    // 获取余额
    let userID = wx.getStorageSync('user').userID
    if (this.data.mainBalance !== -1) {
      this.setData({
        paymentDialog: true
      })
    } else {
      this.payrightNow()
    }

  },
  // 选择支付方式
  pagodapay() {
    if (!this.data.lack) {
      this.setData({
        pagodaMoney: true,
      })
    }
  },
  wxpay() {
    this.setData({
      pagodaMoney: false,
    })
  },
  /**支付请求 */
  payRequest: async function (payOrderNum, payOrderID) {
    var that = this;
    let {
      pagodaMoney
    } = this.data;
    let openid = wx.getStorageSync('wxSnsInfo').openid;
    var payID = that.data.obj.payOrderID || payOrderID;
    let user = wx.getStorageSync('user');
    var timeExpire = this.data.timeExpire.replace(/[-:\s]*/g, "");
    let timelyCity = wx.getStorageSync('timelyCity') || {}
    var params;
    let options;

    const confirmHandle = () => {
      commonObj.requestData(options, function (res) {
        wx.hideLoading();
        if (Number(res.data.errorCode) === 0) {
          var depositObj = res.data.data;
          if (pagodaMoney) {
            //果币支付成功跳转
            if (that.data.isMakeOrder) {
              app.sensors.track('MPClick', {
                element_code: '110200007',
                element_name: '钱包支付',
                element_id: '',
                element_content: '钱包支付',
                screen_code:'1103'
              })
            } else {
              app.sensors.track('MPClick', {
                element_code: '110303001',
                element_name: '钱包支付',
                element_id: '',
                element_content: '钱包支付',
                screen_code:'1103'
              })
            }
            wx.reportAnalytics('deposit_success')
            var orderDetailObj = {
              customerID: wx.getStorageSync('user').userID,
              paymentOrderID: payID,
              orderType: 'groupOrder',
              groupId: that.data.groupID
            }
            wx.redirectTo({ //支付成功跳
              url: '/userA/pages/waitPay/index?orderDetailObj=' + JSON.stringify(orderDetailObj)
            })
            // 取消商品详情的未付款弹窗
            var page = getCurrentPages();
            for (let item in page) {
              if (page[item] && page[item].route.indexOf('fightGroups/pages/goodsDetail/index') > -1) {
                page[item].data.showPayModal = false;
                console.log('showPayModel:' + page[item].data.showPayModal)
              }
            }
          } else {
            depositObj = JSON.parse(commonObj.Decrypt(res.data.data, wx.getStorageSync('token')))
            // 微信支付
            wx.requestPayment({
              'timeStamp': depositObj.timeStamp,
              'nonceStr': depositObj.nonceStr,
              'package': depositObj.packageValue,
              'signType': 'MD5',
              'paySign': depositObj.signStr,
              'success': function (res) {
                if (that.data.isMakeOrder) {
                  app.sensors.track('MPClick', {
                    element_code: '110200008',
                    element_name: '钱包支付',
                    element_id: '',
                    element_content: '钱包支付',
                    screen_code:'1103'
                  })
                } else {
                  app.sensors.track('MPClick', {
                    element_code: '110303002',
                    element_name: '微信支付',
                    element_id: '',
                    element_content: '微信支付',
                    screen_code:'1103'
                  })
                }
                wx.reportAnalytics('deposit_success')
                var orderDetailObj = {
                  customerID: wx.getStorageSync('user').userID,
                  paymentOrderID: payID,
                  orderType: 'groupOrder',
                  groupId: that.data.groupID
                }
                wx.redirectTo({ //支付成功跳
                  url: '/userA/pages/waitPay/index?orderDetailObj=' + JSON.stringify(orderDetailObj)
                })
              },
              'fail': function (res) {
                console.log('支付失败回调');
                that._data.isCanSubmit = true

                if (res.errMsg.indexOf('cancel') > -1) {
                  wx.showToast({
                    title: '支付取消',
                    icon: 'loading',
                    duration: 2000
                  })
                  if (that.data.isDirect) {} else {
                    wx.redirectTo({
                      url: '/fightGroups/pages/PTorderDetail/index?reDirectObj=' + JSON.stringify({
                        goodsOrderID: '',
                        payOrderID: payOrderID || that.data.obj.payOrderID,
                        payStatus: 'PREPAY',
                        isAllOrder: true
                      }),
                    })
                  }
                } else {
                  wx.showToast({
                    title: '支付失败',
                    icon: 'loading',
                    duration: 2000
                  })
                }
              },
              'complete': function (res) {
                console.log('支付回调完成');
                // 取消商品详情的未付款弹窗
                var page = getCurrentPages();
                for (let item in page) {
                  if (page[item] && page[item].route.indexOf('fightGroups/pages/goodsDetail/index') > -1) {
                    // console.log(page[item])
                    page[item].data.showPayModal = false;
                    console.log('showPayModel:' + page[item].data.showPayModal)
                  }
                }
              }
            })
          }

        } else if (Number(res.data.errorCode) === 30101) {
          that._data.isCanSubmit = true
          that.groupIsFull()
        } else {
          console.log('支付失败，。。。');
          wx.showToast({
            title: res.data.errorMsg || '支付失败，请重试',
            duration: 2000,
            icon: 'none'
          })
          that._data.isCanSubmit = true
        }
      }, function (res) {
        console.log(res)
        that._data.isCanSubmit = true
      }, function (res) {
        wx.hideLoading()
      });
    }
    if (pagodaMoney) {
      params = {
        userToken: user.userToken,
        orderNo: that.data.obj.payOrderNum || payOrderNum,
        channel: 'wx_pub',
        payAmount: parseFloat(that.data.obj.payAmount),
        clientIP: '127.0.0.1',
        payTitle: '拼团支付',
        desc: '百果园拼团',
        timeExpire: timeExpire,
        openID: openid,
        newAccount: 'Y',
        requestTarget: 'wx_mini'
      };
      options = {
        encryptFlag: false,
        url: `/api/v1/pay/request/${user.userID}/${that.data.obj.payOrderNum || payOrderNum}/gb`,
        data: params,
        header: {
          gpslocation: JSON.stringify({
            lat: timelyCity.lat || '',
            lon: timelyCity.lon || ''
          })
        },
        page: that
      };
      // 校验是否需要进入验证码环节
      const isNeedValidate = await this.checkPayDevice()

      if (!isNeedValidate) {
        // 缓存一下提交订单的请求
        this._data.tmpConfirmHandle = confirmHandle
        // 弹出验证码输入框
        this.setData({
          visibleSMS: true
        })
        that._data.isCanSubmit = true
        return
      }
    } else {
      params = {
        userToken: user.userToken,
        payOrderNo: that.data.obj.payOrderNum || payOrderNum,
        channel: 'WX_MINI',
        payAmount: parseFloat(that.data.obj.payAmount),
        clientIP: '127.0.0.1',
        subject: '拼团支付',
        body: '百果园拼团',
        timeExpire: timeExpire,
        openID: openid,
        newAccount: 'Y',
        requestTarget: 'wx_mini',
        version: commonObj.appVersion,
        source: 'WX_MINI',
        customerID: user.userID,
        mobile: user.phoneNumber,
        orderType: 'CONSUME_GOODS'
      };
      options = {
        encryptFlag: true,
        url: `${commonObj.PAGODA_EMS_DOMAIN}/wxmini/api/v1/pay/request/${user.userID}`,
        data: params,
        header: {
          gpslocation: JSON.stringify({
            lat: timelyCity.lat || '',
            lon: timelyCity.lon || ''
          })
        },
        page: that
      };
    }
    confirmHandle()
  },
  // 查询订单是否支付回调完成
  checkPayResultFunc: function (id) {
    var that = this;
    var payOId = id;
    var user = wx.getStorageSync('user');
    var options = {
      data: {
        customerId: user.userID,
        payOrderId: payOId, // 商品订单ID
      },
      url: '/api/v1/groupBuy/order/checkPayResult',
    };
    commonObj.requestData(options, function (res) {
      // console.log('PtOrderDetail-checkPayResult')

      if (Number(res.data.errorCode) === 0) {
        if (res.data.data.payResult === 'Y') {
          that.isSuccessGroup(payOId);
        } else {
          var countNum = that.data.count;

          if (countNum < 20) {
            countNum++;
            that.setData({
              count: countNum,
            });
            setTimeout(function () {
              that.checkPayResultFunc(payOId);
            }, 500);
          } else {
            that.isSuccessGroup(payOId);
            console.log('回调不成功');
          }
        }
      } else {
        commonObj.showModal(
          '提示',
          '信息获取失败,请稍后重新请求。异常错误信息：' + res.data.errorMsg,
          false,
          '确认',
          '',
          function () {
            wx.redirectTo({
              url: '/fightGroups/pages/fightGroups/index'
            });
          },
        );
        wx.hideLoading()
      }
    });
  },
  // 该处调取paySuccess接口，来查询是否拼团成功，来判断接下来去哪一页
  isSuccessGroup: function (payID) {
    var that = this;
    var user = wx.getStorageSync('user');
    var timelyCity = wx.getStorageSync('timelyCity') || {};
    var options = {
      header: {
        "userToken": user.userToken
      },
      data: {
        customerID: user.userID,
        cityID: timelyCity.cityID,
        paymentOrderID: payID, // 商品订单ID
        source: 'submit'
      },
      url: '/api/v1/groupBuy/order/paySuccess'
    }
    // console.log(options)
    commonObj.requestData(options, function (res) {
      // console.log('查询是否拼团成功')
      // console.log(res.data)
      if (Number(res.data.errorCode) === 0) {
        wx.redirectTo({
          url: '/fightGroups/pages/paySuccess/index?payOrderIDorGoodsOrderID=' + payID + '&fromPTorderDetail=' + true + '&groupId=' + that.data.groupID + '&isAutoCreate=' + res.data.data.groupInfo.isAutoCreate
        })
      } else if (Number(res.data.errorCode) === 30104 || Number(res.data.errorCode) === 30101 || Number(res.data.errorCode) === 30109) {
        that.groupIsFull()
      } else {
        commonObj.showModal('温馨提醒', '系统繁忙，您所参的团失败，您的退款将会延原路返回！', false, '我知道了', '', function () {
          wx.redirectTo({
            url: '/fightGroups/pages/fightGroups/index'
          });
        })
      }
    }, '', function (complete) {
      wx.hideLoading()
    })
  },
  // 传进最后操作时间和提取时间，处理成最后的自提时间
  // dealWithTime: function (takeTime, lastTime) {
  //   // v1.4 版本没有用到
  //   let weekDay = week[new Date(lastTime).getDay()];
  //   let time = lastTime + '（' + weekDay + '）' + takeTime;
  //   return time;
  // },

  /** 倒计时 已确认和未确认订单
   * @params duration 倒计时持续时间 */
  countDown: function (createTime, expireTime, flag, duration) { // 参数：过期时间，真假团单
    let that = this,
      eTime;
    let cTime = new Date(createTime.replace(/-/g, '/')).getTime();
    let nowTime = duration ? (that.data.serverCurrTime - (-duration)) : that.data.serverCurrTime;
    if (flag) {
      eTime = new Date(expireTime.replace(/-/g, '/')).getTime();
    } else {
      eTime = cTime + 900000;
    }
    let layTime = Math.round((eTime - nowTime) / 1000); // 剩余时间秒数
    timeDown = setTimeout(function () {
      if (layTime < 0 && !that.data.clearTimeDown) {
        // console.log('小于0' + layTime)
        clearInterval(timeDown)
        timeDown = null;
        that.setData({
          layh: '00',
          laym: '00',
          lays: '00',
        })
        if (!that.data.isPay) {
          commonObj.showModal('提示', '支付超时,订单已取消', false, '确认', '', function () {
            if (that.data.isMakeOrder) {
              if (app.globalData.reportSensors) {
                app.sensors.track('MPClick', {
                  element_code: '110302001',
                  element_name: '确认支付超时',
                  element_content: '确认按钮',
                  screen_code:'1103'
                })
              }
              that.data.obj.orderStatus = '已取消';
              that.data.obj.lastUpdate = new Date().Format('yyyy-MM-dd hh:mm:ss');
              that.data.isCancel = true;
              that.setData({
                obj: that.data.obj,
                isCancel: that.data.isCancel
              })
            } else {
              wx.navigateBack({
                delta: 1
              })
            }
          })
        }
        if (that.data.isPay && !that.data.isComposition) {
          commonObj.showModal('提示', '组团超时,订单已取消', false, '确认', '', function () {
            // that.data.obj.orderStatus = '已取消';
            // that.data.obj.lastUpdate = new Date().Format('yyyy-MM-dd hh:mm:ss');
            // that.data.isCancel = true;
            // that.setData({
            //   obj: that.data.obj,
            //   isCancel: that.data.isCancel
            // })
            that.setData({
              layh: '00',
              laym: '00',
              lays: '00',
            })
          })
        }
        return;
      }
      let h = parseInt(layTime / 3600);
      let m = parseInt(layTime / 60 - h * 60);
      let s = parseInt(layTime % 60);

      s = s < 10 ? '0' + s : s;
      m = m < 10 ? '0' + m : m;
      h = h < 10 ? '0' + h : h;
      that.setData({
        layh: h,
        laym: m,
        lays: s,
      })
      if (that.data.clearTimeDown) {
        return;
      } else {
        that.setData({
          ct: that.data.ct - (-1)
        })
        that.countDown(createTime, expireTime, flag, that.data.ct * 1000)
      }
    }, 1000)
  },

  // 取消订单
  chanelOrder: function () {
    // 取消订单，调取取消订单接口，然后返回首页，
    wx.reportAnalytics('groupinfo_cancelorderbtn');
    this.preventEvent();
    let user = wx.getStorageSync('user')
    let orderInfo1 = wx.getStorageSync('orderInfo1')
    let orderInfo2 = wx.getStorageSync('orderInfo2')
    let options, that = this;
    // console.log(orderInfo1)
    // console.log(orderInfo2)
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110300004',
        element_name: '取消订单',
        element_content: '取消订单',
        screen_code:'1103'
      })
    }
    if (orderInfo1.payStatus !== 'SUCCESS') {
      // 未支付
      options = {
        data: {
          customerID: user.userID,
          phoneNumber: user.phoneNumber,
          userToken: user.userToken,
          payOrderID: orderInfo1.payOrderID
        },
        encryptFlag: true,
        url: '/orderManager/cancelOrder'
      }
    } else {
      // 已支付
      options = {
        data: {
          orderNo: orderInfo2.payOrderNum, //商品订单编号                      // 商户订单号（备注：业务支付订单编号）
          refundNo: orderInfo2.goodsOrderID, // 商户退款单号，即商户订单ID（备注：业务商品订单编号）
          IPAddr: '127.0.0.1', // 需要对业务请求方的IP地址做校验
          refundChannel: that.data.refundChannel, // 退款渠道 1：退还虚拟货币  2：退还现金
          refundFee: that.data.obj.payAmount, // 退款总金额, 单位为对应币种的最小货币单位，例如：人民 币为分（如订单总金额为 1 元，此处请填 100）。
          newAccount: 'wx', // 当渠道是wx或alipay有用，如果是新账号，后台使用新配置。 默认：Y[V2.2.1]
          payOrderID: orderInfo1.payOrderID, // 支付订单ID
        },
        encryptFlag: false,
        url: '/miPayService/cancelRefund'
      }

    }

    commonObj.showModal('提示', '您精挑细选的商品真的要取消吗？', true, '残忍取消', '考虑一下', function (res) {
      if (res.confirm) {
        wx.showLoading({
          title: '取消中',
          mask: 'true'
        })
        commonObj.requestData(options, function (res) {

          if (Number(res.data.errorCode) === 0) {
            // 取消订单成功
            if (app.globalData.reportSensors) {
              app.sensors.track('MPClick', {
                element_code: '*********',
                element_name: '残忍取消',
                element_content: '残忍取消按钮',
                screen_code:'1103'
              })
            }
            wx.hideLoading();
            wx.showToast({
              title: '订单取消成功',
              icon: 'success',
              duration: 2000,
              success: function () {
                setTimeout(function () {
                  that.getOrderInfo(orderInfo2.goodsOrderID, orderInfo1.payOrderID, orderInfo1.payStatus)
                }, 2000)
              }
            })
          } else {
            wx.showModal({
              title: '提示',
              content: res.data.errorMsg,
              showCancel: false
            })
            wx.hideLoading();
          }
        }, '', '')
      } else if (res.cancel) {
        if (app.globalData.reportSensors) {
          app.sensors.track('MPClick', {
            element_code: '110301001',
            element_name: '考虑一下',
            element_content: '考虑一下按钮',
            screen_code:'1103'
          })
        }
        wx.hideLoading();
        return;
      }
    })
  },
  submitTapFun: function (e) {
    let formId = e.detail.formId;
    let that = this;
    if (app.checkSignInsStatus()) {
      // 用户已登录
      that.submitTap(formId)
    } else {
      app.signIn()
    }
    // commonObj.getUserSetting();
    // app.getUserInfo(function () {
    //   that.submitTap(formId)
    // });
  },
  // 提交表单
  submitTap: function (formId) {
    // console.log('form发生了submit事件，携带数据为：')
    // let formId = e.detail.formId;
    // if (!wx.getStorageSync('wxSnsInfo')) {
    //   commonObj.showModal('提示', '账号异常，请联系管理员4001811212', false);
    //   return;
    // }
    // let wxSnsInfo = wx.getStorageSync('wxSnsInfo');
    // let user = wx.getStorageSync('user');
    // let options = {
    //   data: {
    //     openId: wxSnsInfo.openid,
    //     unionId: wxSnsInfo.unionid,
    //     customerID: user.userID,
    //     formId: formId
    //   },
    //   url: '/api/v1/form/addFormInfo'
    // }
    //
    // commonObj.requestData(options, function (res) {
    //   console.log(res);
    // })
  },
  // 申诉退款提示
  tuikuan: function () {
    wx.reportAnalytics('groupinfo_refunbtn')
    // 跳转三无退货页
    // wx.navigateTo({
    //   url: `/userB/pages/selfSupportComplaints/refundGoods/index?orderNo=${this.data.orderDetail.id}&orderType=2`
    // })
    //commonObj.showModal('退款须知', '百果园小程序暂未支持退款，您可以下载“百果园App”进行操作', false, '我知道了', '', '')
  },

  // 以下是跳转链接

  // 跳转自提门店
  navigteSelfExtractStore: function () {
    let obj = JSON.stringify({
      goodsID: this.data.goodsID
    })
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110200001',
        element_name: '修改提货地址',
        element_content: '修改',
        screen_code:'1102'
      })
    }
    wx.navigateTo({
      url: '/fightGroups/pages/selfExtractStore/index?PTorderDetailObj=' + obj,
    })
    wx.reportAnalytics('groupinfo_navstorebtn');

    this.preventEvent();
  },
  // 跳转导航
  navigateNav: function () {
    let store = this.data.store
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110300002',
        element_name: '定位门店',
        element_content: '定位门店按钮',
        screen_code:'1103'
      })
    }
    let x = coordtransform.bd09togcj02(parseFloat(store.lon), parseFloat(store.lat))
    wx.openLocation({
      latitude: x[1],
      longitude: x[0],
      name: store.storeName || store.shortName,
      address: store.address
    })
    this.preventEvent();
  },

  // 拨打客服电话
  callServicePhone: function () {
    let phoneNumber = '4001811212';
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110300001',
        element_name: '联系门店',
        element_content: '联系门店按钮',
        screen_code:'1103'
      })
    }
    if (this.data.store && this.data.store.storePhone) {
      phoneNumber = this.data.store.storePhone
    }
    wx.makePhoneCall({
      phoneNumber: phoneNumber
    })
    wx.reportAnalytics('groupinfo_callservicestorebtn');
    this.preventEvent();
  },

  // 重新购买 or 再次购买
  buyAgain: app.subProtocolValid('shop', function () {
    var page = getCurrentPages();
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110300007',
        element_name: '重新购买',
        element_content: '重新购买',
        screen_code:'1103'
      })
    }
    for (let item in page) {
      if (page[item] && page[item].route.indexOf('goodsDetail') > -1) {
        wx.navigateBack({
          delta: 1
        })
      }
    }
    wx.reportAnalytics('groupinfo_buyagainbtn');
    let timelyCity = wx.getStorageSync('timelyCity') || {};
    let store = timelyCity.storeInfo;

    if (store) {
      // 此时 this.data.activityID 是undefined
      let obj = JSON.stringify({
        goodsID: this.data.obj.goodsID,
        activityID: (this.data.activityID || this.data.obj.activityID)
      })

      wx.redirectTo({
        url: '/fightGroups/pages/goodsDetail/index?PTorderDetailObj=' + obj,
      })
    } else {
      commonObj.showModal('提示', '您附近没有自提门店，无法参与拼团购买活动', false, '我知道了', '', '')
    }

    this.preventEvent();
  }),
  // 点击商品，渠道商品详情
  navigateGoodsDetail: function () {
    let obj = JSON.stringify({
      goodsID: (this.data.goodsID || this.data.obj.goodsID),
      activityID: (this.data.activityID || this.data.obj.activityID)
    })

    if (app.globalData.reportSensors) {
      if (this.data.isMakeOrder) {
        app.sensors.track('MPClick', {
          element_code: '110300003',
          element_name: '拼团商品',
          element_content: '订单详情点击商品跳至商品详情',
          screen_code:'1103'
        })
      } else {
        app.sensors.track('MPClick', {
          element_code: '110200002',
          element_name: '拼团商品',
          element_content: '确认订单点击商品跳至商品详情',
          screen_code:'1103'
        })
      }
    }

    wx.navigateTo({
      url: '/fightGroups/pages/goodsDetail/index?inviteFriendsObj=' + obj,
    })
  },
  backHomeBtn: function () {
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110300008',
        element_name: '首页',
        element_content: '首页按钮',
        screen_code:'1103'
      })
    }
    wx.redirectTo({
      url: '/fightGroups/pages/fightGroups/index'
    });
  },

  groupIsFull: function () {
    let that = this;
    wx.hideLoading();
    commonObj.showModal('温馨提醒', '此团已经满啦，下次早点来~已付的款项按原路返还', true, '开个新团', '我知道了', function (res) {
      if (res.confirm) {
        if (app.globalData.reportSensors) {
          app.sensors.track('MPClick', {
            element_code: '110203002',
            element_name: '开个新团',
            element_content: '开个新团按钮',
            screen_code:'1102'
          })
        }
        let obj = JSON.stringify({
          goodsID: (that.data.goodsID || that.data.obj.goodsID),
          activityID: that.data.activityID
        })
        wx.navigateTo({
          url: '/fightGroups/pages/goodsDetail/index?PTorderDetailObj=' + obj,
        })
      } else if (res.cancel) {
        if (app.globalData.reportSensors) {
          app.sensors.track('MPClick', {
            element_code: '110203001',
            element_name: '我知道了',
            element_content: '我知道了按钮',
            screen_code:'1102'
          })
        }
        wx.redirectTo({
          url: '/fightGroups/pages/fightGroups/index'
        });
      }
    })
  },
  animationShare: function () {
    var that = this;
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    that.animation = animation;
    animation.translateY(-158).step();
    that.setData({
      animationShare: animation.export()
    });
    setTimeout(function () {
      that.setData({
        animationShare: animation
      })
    }.bind(that), 400)
  },
  shareAppMessage: function (e) {
    var that = this;
    var type = e.currentTarget.dataset.type;
    if (type === 'share') {
      that.setData({
        isShare: true
      })
      that.animationShare();
    } else if (type === 'thumbnail') {
      // that.setData({
      //   isThumbnail: true
      // })
      if (!wx.showShareImageMenu) {
        that.setData({ isThumbnail: true })
      }
      that.utilThumbnail()
      wx.showLoading({
        title: '正在生成海报',
      })
      that.drawPoster()
    }
  },
  // 缩略图弹框
  utilThumbnail: function () {
    var that = this;
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: "linear",
      delay: 0
    })
    that.animation = animation;
    animation.translateY(-parseInt((that.data.modelHeight / 2))).step();
    that.setData({
      animationImage: animation.export()
    });

    setTimeout(function () {
      this.setData({
        animationImage: animation
      })
    }.bind(this), 300)
  },
  closeModel: function (e) {
    wx.hideLoading()
    var that = this;
    var type = e.currentTarget.dataset.type;
    if (type === 'share') {
      that.setData({
        isShare: false
      })
    } else if (type === 'thumbnail') {
      that.setData({
        isThumbnail: false
      })
    }
  },
  // 绘制canvas图
  async drawPoster () {
    const that = this
    const { shareImage, picUrl } = that.data
    // 若海报已经生成过了，不用重新生成
    if (shareImage) {
      return wx.hideLoading()
    }
    try {
      const { goodsList, group } = that.data.orderDetail || {}
      const { groupActivityInfo: { shareTitle: goodsName }, groupSize, id: groupId } = group || {}
      const { headPic: headPicUrl, spec: goodsWeight, price, originalPrice } =  goodsList[0] || []
      const headPic = `${picUrl}${headPicUrl}` // 商品头图
      const groupPrice = (price / 100).toFixed(2); // 拼团价
      const originPrice = !!originalPrice ? (originalPrice / 100).toFixed(2) : 0 //商品原价
      const goodsOrderId = wx.getStorageSync('goodsOrderID') || groupId
      const { userID } = wx.getStorageSync('user') || {}
      const sceneUrl = `SCANCODE@${goodsOrderId}@${userID}` // 小程序码对应页面链接的参数
      const goodsObj = {
        headPic,
        goodsName,
        goodsWeight,
        groupSize,
        groupPrice,
        originPrice,
        sceneUrl
      }
      // 绘制海报
      const canvasId = 'posterCanvas'
      const shareImage = await drawPosterPic(canvasId, goodsObj)
      // that.setData({
      //   shareImage
      // })
      that.showSharePoster(shareImage)
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      wx.showLoading({
        title: '生成海报出错啦，请稍后重试',
      })
    }
  },
  /**
 * 展示生成的海报
 */
  showSharePoster(path) {
    if (!wx.showShareImageMenu) {
      this.setData({
        shareImage: path
      })
    } else {
      wx.showShareImageMenu({
        path: path
      })
    }
  },
  // 海报保存到本地
  savePoster: function () { //shareImage
    var that = this;
    wx.showLoading({
      title: '正在生成海报'
    })
    wx.saveImageToPhotosAlbum({
      filePath: this.data.shareImage,
      success: (res) => {
        wx.hideLoading()
        wx.showToast({
          title: '保存成功，快去分享吧~',
          icon: 'none'
        });
      },
      fail: (err) => {
        this.setData({
          status: 'auth denied'
        })
        wx.hideLoading()
        commonObj.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了', '', function (res) {
          if (res.confirm && that.data.status === 'auth denied') {
            wx.openSetting()
          }
        })
      }
    })
  },
  onShareAppMessage: function (res) {
    let user = wx.getStorageSync('user');
    let that = this;
    wx.reportAnalytics('groupinfo_invitefriendbtn');
    let { storeID = 0, storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}

    if (res.from === 'button') {
      let id = res.target.dataset.goodsorderid || wx.getStorageSync('goodsOrderID')
      const shareAttr = generateShareAttr()
      let paySuccessObj = {
        goodsOrderID: id,
        userId: user.userID,
        userToken: user.userToken,
        mp_shareID: shareAttr.mp_shareID,
        mp_shareTitle: that.data.groupShareTitle,
        mp_activityID: that.data.activityID,
        mp_activityName: that.data.activityName,
        mp_groupID: that.data.groupInfo.groupId,
        mp_openerID: that.data.groupInfo.openerId,
      }
      let shareObj = {
        ...shareAttr,
        mp_shareTitle: that.data.groupShareTitle,
        activity_ID: that.data.activityID,
        activity_Name: that.data.activityName,
        groupID: that.data.groupInfo.id,
        groupSize: that.data.groupInfo.groupSize,
        openerID: that.data.groupInfo.openerId,
        currentCount: that.data.groupInfo.currentCount,
        expireTime: that.data.groupInfo.expireTime,
        storeID,
        storeName,
        storeNum: storeInfo.number || ''
      }
      if (app.globalData.reportSensors) {
        app.sensors.track('MPClick', {
          element_code: '110300006',
          element_name: '邀请好友',
          element_content: '订单详情邀请好友按钮'
        })
      }
      wx.reportAnalytics('share_success')
      if (app.globalData.reportSensors) {
        app.sensors.track('MPShare', shareObj)
      }
      let title = that.data.groupShareTitle
      return {
        title: title,
        path: `/pages/homeDelivery/index?home2InviteFriends=${JSON.stringify(paySuccessObj)}&shareObj=${JSON.stringify(shareObj)}`,
        // imageUrl: picUrl + that.data.groupSharePic
        imageUrl: this.data.mixinSharePic
      }
    }
  },
  //检测限购
  effect: function (activityID, goodsID, customerID) {
    var options = {
      url: '/api/v1/groupBuy/goods/effect',
      data: {
        activityID: activityID,
        goodsID: goodsID,
        customerID: customerID
      }
    }
    commonObj.requestData(options, res => {
      if (res.data.errorCode === 0) {
        var dt = res.data.data;
        this.setData({
          residualEffectNum: dt.residualEffectNum,
          totalEffectNum: dt.effectNum,
          disableEffect: this.data.disable ? false : dt.residualEffectNum === 0
        })
      } else {
        wx.showModal({
          title: '提示',
          content: '请求超时，系统繁忙',
          showCancel: false
        })
      }
    });
  },

  /**************  一单多件 ****************/
  changeCount: function () {
    let {
      user,
      store,
      goodsID,
      activityID,
      lon,
      lat,
      goodsCount
    } = this.data;
    delayId = setTimeout(() => {
      this.orderAndSettlement(user, store, goodsID, activityID, lon, lat, goodsCount)
    }, 500)
  },

  addGoods: function () {
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110200005',
        element_name: '加号',
        element_content: '加号',
        screen_code:'1102'
      })
    }
    let {
      goodsCount,
      addButtonGray,
      disableEffect
    } = this.data;
    let limitCount = this.data.obj.limitCount;
    let stockNum = this.data.obj.stockNum;
    if (addButtonGray || disableEffect) {
        if (limitCount === '') {
          wx.showToast({
            title: `此商品仅剩${goodsCount}件哦`,
            icon: 'none',
            duration: 2000
          })
        } else if (stockNum === null) {
          wx.showToast({
            title: `很抱歉，当前果品最多购买${goodsCount}份`,
            icon: 'none',
            duration: 2000
          })
        } else {
          limitCount <= stockNum ?
          wx.showToast({
            title: `很抱歉，当前果品最多购买${goodsCount}份`,
            icon: 'none',
            duration: 2000
          }) :
          wx.showToast({
            title: `此商品仅剩${goodsCount}件哦`,
            icon: 'none',
            duration: 2000
          })
        }
      return
    } else {
      if (Number(goodsCount) === Number(limitCount) || Number(goodsCount) === Number(stockNum) || Number(goodsCount) === 99) {
        this.setData({
          goodsCount: goodsCount,
          addButtonGray: true
        })
      } else {
        this.setData({
          goodsCount: ++goodsCount,
          addButtonGray: Number(goodsCount) === Number(limitCount) || Number(goodsCount) === Number(stockNum) ? true : false,
          subButtonGray: false
        })
        clearTimeout(delayId)
        this.changeCount()
      }
    }

  },

  subGoods: function () {
    let {
      subButtonGray,
      goodsCount
    } = this.data;
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', {
        element_code: '110200006',
        element_name: '减号',
        element_content: '减号',
        screen_code:'1102'
      })
    }
    if (Number(goodsCount) === 1) {
      this.setData({
        goodsCount: goodsCount,
        subButtonGray: true
      })
    } else {
      this.setData({
        goodsCount: --goodsCount,
        subButtonGray: Number(goodsCount) === 1 ? true : false,
        addButtonGray: false
      })
      clearTimeout(delayId)
      this.changeCount()
    }
  },
  /**************  一单多件  结束 ****************/

  // 蒙层
  preventEvent: function () {
    this.setData({
      prevent: true
    });
    setTimeout(() => {
      this.setData({
        prevent: false
      });
    }, 400)
  },
  // 获取下单充值引导活动信息
  rechargeGuide(param) {
    app.api.rechargeGuide(param).then(res => {
      let rechargeText = res.data.depositText
      this.setData({ rechargeText })
    }).catch((res) => {
      commonObj.showModal('提示', res.errorMsg, false, '我知道了')
    })
  },
  /**
   * @description - 取消订单;v2.4 重构方法(bindCancelTap)
   * @param e - 组件原生数据
   *  */
  onOrderCancel: function (e) {
    // 取消订单，调取取消订单接口，然后返回首页，
    let vm = this
    wx.reportAnalytics('groupinfo_cancelorderbtn')
    vm.preventEvent()
    // 神策上报取消订单
    vm.trackClickEvent({
      element_code: '110300004',
      element_name: '取消订单',
      element_content: '取消订单',
      screen_code:'1103'
    })

    // 取消订单 - 未支付
    let orderInfo1 = wx.getStorageSync('orderInfo1')
    if (orderInfo1.payStatus !== 'SUCCESS') {
      vm.orderCancelPaiedFailed()
      return
    }
    // 取消订单 - 已支付
    app.requestSubscribeMessage({
      tmplIds: ['jLaaeFpXwM90AMJrCpS63A4A1tBH3zT7tVHleo621gE']
    }, () => {
      vm.orderCancelPaiedSuccess()
    })
  },
  /**
   * @description - 取消订单 - 已支付(超时/未超时)；超时取消订单沿用原交互设计，不需要填写原因；未超时取消订单，需要弹窗填写原因
   *  */
  orderCancelPaiedSuccess () {
    let vm = this
    let orderInfo1 = wx.getStorageSync('orderInfo1')
    let orderInfo2 = wx.getStorageSync('orderInfo2')
    let {orderDetail: orderInfo} = vm.data
    let user = wx.getStorageSync('user')
    // 非小程序端订单
    if (orderInfo.source !== 'wxMini') {
      commonObj.showModal('提示', '该订单为“百果园App”订单，您可以下载“百果园App”进行取消', false)
      return
    }

    // 请求参数
    function isAllowRefund (isAllowRefund) {
      return isAllowRefund === 'Y'
    }
    let type = isAllowRefund(orderInfo.isAllowRefund) ?  '2' : '3' // 退款类型  2: 超时退款 3: 取消退款
    let refundChannel = orderInfo.payment.channel.indexOf('gb') > -1 ? '1' : '2' // 退款渠道 1:退还果币 2:退还现金 （app3.4.3后使用3、4渠道）3:原路退 4:退还果币
    let params = {
      type, // 退款类型  2: 超时退款 3: 取消退款
      customerId: user.userID,
      goodsOrderId: orderInfo2.goodsOrderID || orderInfo.goodsOrderID || orderInfo1.goodsOrderID,
      refundChannel, // 退款渠道
      requester: 1, // 请求者标识 1:顾客子系统取消
      description: '' // 退款详情
      // cancelStatus: orderInfo.goodsOrderStatus || orderInfo.status // 取消状态
    }
    // 超时取消订单沿用原交互设计，不需要填写原因
    if (isAllowRefund(orderInfo.isAllowRefund)) {
      commonObj.showModal('提示', '您精挑细选的商品真的要取消吗？', true, '残忍取消', '考虑一下', function (res) {
        // 点击“取消”操作
        if (Boolean(res.cancel)) {
          return
        }
        // 点击“确定”操作
        vm.orderCancelPaiedSuccessFetch (params)
      })
      return
    }

    // 未超时取消订单，需要弹窗填写原因
    vm.setData({
      orderCancelParams: params,
      orderCancelIsShow: true
    })
  },
  /**
   * @description - 取消订单 - 已支付(超时取消订单)
   *  */
  orderCancelPaiedSuccessFetch (params) {
    let vm = this
    sensors.safeGuardSensor('refund', { goodsOrderId: params.goodsOrderId })
    app.api.orderRefundPaid(params).then((res) => {
      wx.hideLoading()
      let { errorCode, description } = res
      // 请求失败
      if (Number(errorCode) !== 0) {
        // commonObj.showModal('提示', description, false, '我知道了')
        wx.showToast({
          title: description,
          icon: 'none',
          duration: 3000
        })
        wx.hideLoading()
        return
      }
      vm.setData({
        orderCancelIsShow: false,
        orderCancelParams: null
      })
      // 请求成功
      vm.orderCancelSuccess()
    }).catch((res) => {
      // commonObj.showModal('提示', res.description, false, '我知道了')
      wx.hideLoading()
      wx.showToast({
        title: res.description,
        icon: 'none',
        duration: 3000
      })
    })
  },
  /**
   * @description - 取消订单 - 未支付
   *  */
  orderCancelPaiedFailed () {
    let vm = this
    // 取消订单
    commonObj.showModal('提示', '您精挑细选的商品真的要取消吗？', true, '残忍取消', '考虑一下', function (res) {
      // 点击“取消”操作
      if (Boolean(res.cancel)) {
        // 神策上报取消订单
        vm.trackClickEvent({
          element_code: '110301001',
          element_name: '考虑一下',
          element_content: '考虑一下按钮',
          screen_code:'1103'
        })
        wx.hideLoading()
        return
      }

      // 点击“确定”操作
      wx.showLoading({
        title: '取消中',
        mask: 'true'
      })
      let user = wx.getStorageSync('user')
      let orderInfo1 = wx.getStorageSync('orderInfo1')
      let params = {
        customerID: user.userID,
        payOrderID: orderInfo1.payOrderID,
        requester: 1, // 请求者标识 1:顾客子系统取消
      }
      app.api.orderCancelUnpaid(params).then((res) => {
        wx.hideLoading()
        let { errorCode, description } = res
        // 请求失败
        if (Number(errorCode) !== 0) {
          commonObj.showModal('提示', description, false, '我知道了')
          return
        }

        // 请求成功
        vm.orderCancelSuccess()
      }).catch((res) => {
        commonObj.showModal('提示', res.description, false, '我知道了')
        wx.hideLoading()
      })
    })
  },
  /**
   * @description - 成功取消订单，更新列表
   *  */
  orderCancelSuccess () {
    let vm = this
    let orderInfo1 = wx.getStorageSync('orderInfo1')
    let orderInfo2 = wx.getStorageSync('orderInfo2')
    // 取消订单成功
    vm.trackClickEvent({
      element_code: '*********',
      element_name: '残忍取消',
      element_content: '残忍取消按钮',
      screen_code:'1103'
    })
    wx.hideLoading();
    wx.showToast({
      title: '订单取消成功',
      icon: 'success',
      duration: 2000,
      success: function () {
        setTimeout(function () {
          vm.getOrderInfo(orderInfo2.goodsOrderID, orderInfo1.payOrderID, orderInfo1.payStatus)
        }, 2000)
      }
    })
  },
  /**
   * @description - 弹窗选择-“取消订单”原因,（支付成功，才需要弹出选择原因，取消订单)
   * @param {object} params - 订单取消选项
   * @returns {undefined}
   */
  onOrderCancelSelected (e) {
    let vm = this
    let {cancelID, cancelReason} = e.detail
    let {orderCancelParams} = vm.data
    let orderCencel = {cancelID, subReason: cancelReason}
    let params = Object.assign({}, orderCancelParams, orderCencel)
    vm.orderCancelPaiedSuccessFetch (params)
  },
  /**
   * @description - 弹窗关闭-“取消订单”原因
   */
  onOrderCancelClosed () {
    this.setData({
      orderCancelIsShow: false,
      orderCancelParams: null
    })
  },
  // 神策上报点击事件
  trackClickEvent(params = {}) {
    if (app.globalData.reportSensors) {
      app.sensors.track('MPClick', params)
    }
  },
   /**
   * @description - 判断“开发票”按钮
   * @param {object} - 订单详情数据对象
   * @returns {object || null} 按钮数据对象
   */
  invoiceBtn (order) {
    let { isAllowInvoice: isAllow } = order
    // 不允许开发票
    if (isAllow === 'I') {
      return null
    }
    // 允许开发票,显示不同文本
    // let statuses = ['PROGRESSING', 'FINISH'] // 开票中, 已开票
    // isAllow取值 I:隐藏 O:开具发票 D:查看发票详情
    let text = isAllow === 'D' ? '发票详情' : '开具发票'
    const modalContent = isAllow === 'D' ?
      '小程序暂不支持查看电子发票，可前往在线客服获取查看方法' :
      '小程序暂不支持线上开票服务，可前往在线客服获取开票说明'
    return {
      isAllow,
      text,
      modalContent
    }
  },
  navigateToDownLoadH5(){
    let pageUrl = 'http://mp.weixin.qq.com/s?__biz=MjM5ODAwMTYwMA==&mid=521139153&idx=1&sn=375d762d3186d7596f297023a08d813b&chksm=3c2a3d7e0b5db468c113e25d4421397dd4f5f5bc810e024e8bc5dcc478ace022c9df3aafbb35#rd'
    wx.navigateTo({
      url: '/h5/pages/commonLink/index?pageUrl=' + encodeURIComponent(pageUrl),
    })
  },
  //v2.4——配送中订单添加app下载引导页
  toDownloadApp () {
    // wx.navigateTo({
    //   url:'/userB/pages/guideToDownLoad/index'
    // })
    this.navigateToDownLoadH5();
  },
  // v2.4——点击“开具票”按钮
  async onInvoiceBtnTap (e) {
    const {invoiceBtnMap} = this.data
    const goodsOrderID = this.data.goodsOrderID;
    // 当isAllow===d等于开过发票 跳转详情
    if (invoiceBtnMap.isAllow === 'O') {
      const { userID } = wx.getStorageSync('user') || {};
      const { data } = await app.api.getScrabbleDoughDetails(userID,goodsOrderID)
      //  携带参数跳转到对应页面
      const query = JSON.stringify({
        //  订单类型
        orderType: 40,
        //  开票金额
        drawableAmount: data.invoiceDetail.drawableAmount,
        //  开票订单数
        orderQuantity: 1
      });

      //  设置开具发票页使用的开票参数
      wx.setStorageSync('createInvoice_data', {
        selectedData: [
          {
            orderChannel: 'O2O',
            orgCode: this.data.store.number,
            channelOrderNo: this.data.number,
            drawableAmount: data.invoiceDetail.drawableAmount,
            finishTime: data.createTime
          }
        ]
      })

    wx.navigateTo({
      url: '/userB/pages/invoice/createInvoice/index?pageParam=' + query,
    });
    }else{

      //  携带参数跳转到对应页面
      const query = JSON.stringify({
        // 拼团 订单渠道为 O2O
        orderChannel: 'O2O',
        channelOrderNo: this.data.number
      });

      wx.navigateTo({
        url: '/userB/pages/invoice/invoiceDetail/index?pageParam=' + query,
      })
    }
  },
  // 复制订单号
  copyNoTap(e) {
    const data = e.currentTarget.dataset.no
    wx.setClipboardData({
      data
    })
  },
  // 退款不通过原因弹窗-打开/关闭
  handleRefundReasonModel(e) {
    const show = e.currentTarget.dataset.show
    this.setData({
      isShowRefundReason: show
    })
  },
  // 跳转订单评价
  navigateOrderEvaluation(e) {
    const { orderdetail } = e.currentTarget.dataset
    const { number, id } = orderdetail || {}
    wx.navigateTo({
      url: `/userB/pages/evaluation/orderEvaluation/index?orderid=${number}&orderType=40&eshopOrderID=${id}`
    })
    // 上报神策点击拼团订单详情页评价按钮
    this.trackClickEvent({
      'element_code': '110300009',
      'element_name': '评价',
      'element_content': '评价',
      'screen_code': '103',
    })
  },
  // 拼团提货组件相关方法
  showPicker() {
    this.setData({
      isShow_picker:true
    })
  },
  sureCallBack (e) {
    let {selectTimeValue, showTimeValue} = {...e.detail.data}
    this.setData({
      isShow_picker: false,
      selectTimeValue,
      showTimeValue
    })
  },
  cancelCallBack () {
    this.setData({
      isShow_picker: false,
    })
  },
  /**
   * 非已取消状态的订单获取包装费用提示
   */
  async getPackageFeeTip({ cityID = 1}) {
    try {
      const res = await app.api.getPackageFeeTip({
        cityID,
        customerID: app.globalData.customerID
      })
      const { packageFeeTip = '' } = res.data || {}
      this.setData({
        packageFeeTip
      })
    } catch (error) {}
  }
})
