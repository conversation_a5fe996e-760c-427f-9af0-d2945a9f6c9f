{"pages": ["pages/homeDelivery/index", "pages/category/index", "pages/xxshop/index/index", "pages/shopCart/index", "pages/index/index", "pages/duibaPay/duibaPay", "pages/duibaRedirect/duibaRedirect", "pages/topic/index", "pages/topic/components/priceGoods/index"], "subPackages": [{"root": "fightGroups", "pages": ["pages/paySuccess/index", "pages/inviteFriends/index", "pages/PTorderDetail/index", "pages/selfExtractStore/index", "pages/goodsDetail/index", "pages/topic/index", "pages/fightGroups/index"]}, {"root": "homeDelivery", "pages": ["pages/category/index", "pages/goodsDetail/index", "pages/addressList/index", "pages/paySuccess/index", "pages/confirmOrder/index", "pages/topic/index", "pages/storeList/index", "pages/storeDetail/index", "pages/orderGap/index", "pages/sharePage/index", "pages/orderNote/index", "pages/nationalDelivery/index", "pages/logistics/index", "pages/limitActivity/index", "pages/noSignal/index", "pages/signIn/phoneNumber/index", "pages/signIn/index/index", "pages/signIn/infoCertification/index", "pages/blessCard/index", "pages/waterGoods/index", "pages/collectStore/index", "pages/memberMission/index", "pages/selectVipCoupon/index", "pages/subShopCart/index", "pages/prepay/confirm/index", "pages/prepay/orderList/index", "pages/newUserGoods/index", "pages/newUserDetail/index", "pages/wikiImage/index"]}, {"root": "userA", "pages": ["pages/searchGoods/index", "pages/checkIn/index", "pages/myAward/index", "pages/deposit/index", "pages/withdrawList/index", "pages/myPrivlg/index", "pages/baldetail/index", "pages/privlgDetail/index", "pages/berryValue/index", "pages/integrationDetail/index", "pages/selectCoupon/index", "pages/invoiceManage/index", "pages/editInvoice/index", "pages/waitPay/index", "pages/payError/index", "pages/storePunch/index", "pages/myGrade/index", "pages/taskCenter/index", "pages/coupon/index", "pages/couponsHistory/index", "pages/couponGoods/index", "pages/selectVoucher/index", "pages/selectExchangeCard/index", "pages/eduAuth/main/index", "pages/eduAuth/rules/index"], "plugins": {"studentVerify": {"version": "latest", "provider": "wxa16657d57059e0f0"}}}, {"root": "componentsSubPackage", "pages": ["index"]}, {"root": "giftCard", "pages": ["pages/home/<USER>", "pages/detail/index", "pages/confirmOrder/index", "pages/waitPay/index", "pages/paySuccess/index", "pages/send/index", "pages/forward/index"]}, {"root": "sourceSubPackage", "pages": ["index"], "plugins": {"surveyPlugin": {"version": "1.2.5", "provider": "wx11defe095afaf247"}, "captcha": {"version": "2.1.0", "provider": "wx1fe8d9a3cb067a75"}, "tdfp-plugin": {"version": "1.9.4", "provider": "wxc3b909c3d24c5417"}, "turingCore": {"version": "2.0.3", "provider": "wx63af045606be281d"}}}, {"root": "userB", "pages": ["pages/barcode/index", "pages/modifyPwd/index", "pages/changeCard/index", "pages/orderDetail/index", "pages/modifyUserInfo/index", "pages/selfService/index", "pages/memberCode/index", "pages/memberCenter/index", "pages/memberService/index", "pages/newCustomerMissionDetail/index", "pages/levelMissionDetail/index", "pages/missionHistory/index", "pages/evaluation/myEvaluation/index", "pages/evaluation/orderEvaluation/index", "pages/evaluation/evaluationSuccess/index", "pages/evaluation/evaluationDetail/index", "pages/evaluation/myEvaluationDetail/index", "pages/evaluation/evaluationRule/index", "pages/codePaySuccess/index", "pages/noReasonRefund/index", "pages/miniProgramChange/index", "pages/serviceHelp/index", "pages/tryEatStore/index", "pages/selfSupportComplaints/goodsDissatisfy/index", "pages/selfSupportComplaints/complaintsDetail/index", "pages/selfSupportComplaints/submitSuccess/index", "pages/selfSupportComplaints/smsVerification/index", "pages/agreementList/agreementList", "pages/orderList/index", "pages/selfContentRecommend/index", "pages/logoff/logoffCheck/index", "pages/logoff/logoffResult/index", "pages/logoff/logoffSelectReason/index", "pages/logoff/logoffVerifyCode/index", "pages/logoff/logoffResultVip/index", "pages/allOrderDetail/index", "pages/invoice/invoiceService/index", "pages/invoice/createInvoice/index", "pages/invoice/invoiceHistory/index", "pages/invoice/invoiceSuccess/index", "pages/invoice/invoiceAmountDetail/index", "pages/invoice/invoiceDetail/index", "pages/certification/applyCertification/index", "pages/certification/applyResult/index", "pages/certification/applyChangeBind/index", "pages/updateNickName/index", "pages/giftOrderList/index"]}, {"root": "store", "pages": ["pages/storeOrderList/index", "pages/refundGoodsList/index", "pages/goodsUnsatisfy/index", "pages/addOrder/index", "pages/storeFeedback/index", "pages/feedbackEntry/index", "pages/feedbackPage/index", "pages/submitSuccess/index", "pages/historyFeedback/index", "pages/feedbackDetail/index"]}, {"root": "h5", "pages": ["pages/cemSurveys/index", "pages/activityTemp/index", "pages/memberRuleInfo/index", "pages/intergraRuleInfo/index", "pages/hongbao/index", "pages/vipRights/index", "pages/myPrivlgDetail/index", "pages/welfareActivity/index", "pages/duiba/index", "pages/aboutUs/index", "pages/refundInstruction/index", "pages/commonLink/index", "pages/commonh5/index", "pages/sharePage/index", "pages/shareVipPage/index", "pages/sharePageNoPoster/index", "pages/bgxxSkipH5/index", "pages/tryEat/goodsDetailShare/index", "pages/tryEat/reportShare/index", "pages/h5toActivity/index", "pages/subscribeMessage/index", "pages/toMiniprogram/index"]}, {"root": "bgxxShop", "pages": ["pages/xxshop/index/index", "pages/activityPage/index", "pages/subjectActivity/index", "pages/collectBills/index", "pages/paySuccess/index", "pages/goodDetail/index", "pages/confirmOrder/confirmOrder", "pages/payloading/index", "pages/chooseStore/index", "pages/changeAddress/index", "pages/cityList/index", "pages/category/index", "pages/search/index", "pages/couponGoods/index", "pages/selectCoupon/index", "pages/switchDistriStores/index", "pages/newArrivals/index", "pages/exchangeCollect/index"]}, {"root": "scancodeTobuy", "pages": ["pages/scan/index", "pages/confirmOrder/index", "pages/paySuccess/index", "pages/payOrder/index", "pages/refundDetail/index", "pages/orderList/index", "pages/orderDetail/index", "pages/refundSubmit/index", "pages/orderRefund/index", "pages/RefundSuccess/index"]}, {"root": "bgxxUser", "pages": ["pages/cycleBuy/index", "pages/installmentDetail/index", "pages/modificationTime/index", "pages/orderDetail/index", "pages/address/addAddress/index", "pages/address/addressList/index", "pages/address/editeAddress/index", "pages/address/locationAdress/index", "pages/invoice/applyInvoice/applyInvoice", "pages/invoice/invoiceDetail/invoiceDetail", "pages/complaints/submitSuccess/index", "pages/complaints/refundGoods/index", "pages/complaints/goodsDissatisfy/index", "pages/complaints/complaintsDetail/index", "pages/refundSuccess/index", "pages/requestPostSale/index", "pages/pay/index", "pages/autoRenew/index", "pages/qrcodeInvite/index", "pages/inviteList/index"]}, {"root": "activity", "pages": ["pages/helpCoupon/index", "pages/helpDetail/index", "pages/inviteDetail/index", "pages/helpRecord/index", "pages/checkDetail/index"]}, {"root": "relay", "pages": ["pages/home/<USER>", "pages/detail/index", "pages/confirmOrder/index", "pages/waitPay/index", "pages/paySuccess/index", "pages/orderDetail/index"]}], "window": {"backgroundTextStyle": "dark", "navigationBarBackgroundColor": "#ffffff", "navigationBarTitleText": "百果园", "navigationBarTextStyle": "black"}, "tabBar": {"custom": true, "list": [{"pagePath": "pages/homeDelivery/index"}, {"pagePath": "pages/category/index"}, {"pagePath": "pages/shopCart/index"}, {"pagePath": "pages/index/index"}]}, "resolveAlias": {"~/*": "/*"}, "networkTimeout": {"request": 120000}, "plugins": {"sendCoupon": {"version": "1.1.5", "provider": "wxf3f436ba9bd4be7b"}, "live-player-plugin": {"version": "1.3.4", "provider": "wx2b03c6e691cd7370"}}, "embeddedAppIdList": ["wx3cbe919f36710d1c"], "navigateToMiniProgramAppIdList": ["wxc08e66e4f11eb4e1", "wx64168858244cb3d1", "wxf912553adb49c3b2", "wxdfcaa44b1aa891a7", "wx60d176f873ca2d67", "wxbd687630cd02ce1d", "wxd947200f82267e58", "wx82df6e5e2d77615e"], "permission": {"scope.userLocation": {"desc": "您的位置信息将用于获取最近的百果园门店服务"}}, "usingComponents": {"common-loading": "./components/bgxx/common-loading", "image-lazy-load": "/components/bgxx/image-lazy-load/index", "refundWarningPop": "/components/refundWaring/refundWaring", "custom-tab-bar": "/components/customTabBar/index", "pagoda-num-tag": "/components/pagoda-num-tag/pagoda-num-tag", "nav-bar": "/components/navBar/index", "main-page": "/components/base/main-page/index", "confirm-modal": "/components/confirmModal/index", "pdm-switch": "/components/form/pdm-switch/index", "user-protocol": "./components/base/userProtocol/index", "exposure": "./components/exposure/index"}, "sitemapLocation": "sitemap.json", "requiredPrivateInfos": ["<PERSON><PERSON><PERSON><PERSON>", "getLocation"], "__usePrivacyCheck__": true}