// import { WE_CHAT, PAGODA } from '../../utils/services/fruitOrderPay'
import { COSAction } from '../../utils/cos/index'
import { COS_UNIONPAY } from '../../source/const/cosConfig'
import { WE_CHAT, UNIONPAY, } from '../../utils/services/fruitOrderPay'
import { paymentSelectProps } from './paymentSelectProps'
const sensors = require('~/utils/report/sensors')

const unionPayConfigCos = new COSAction({
  cosType: COS_UNIONPAY,
})

// 默认钱包支付在前
const defaultPayWayList = () => [
  [
    // 钱包支付
    'payment-item-m',
  ],
  [
    // 微信支付
    'payment-item-w',
    // 云闪付
    'payment-item-u',
  ]
]

/**
 * 将多维数组list展平成一维数组
 * @template T
 * @param {T[][]} list
 * @returns {T[]}
 */
const flatPaymentList = (list) => {
  return list.reduce((pre, cur) => {
    pre.push(...cur)
    return pre
  }, /** @type { T[] } */([]))
}

const genPdFirstPayWayList = () => flatPaymentList(defaultPayWayList())

const genWXFirstPayWayList = () => flatPaymentList(defaultPayWayList().reverse())

Component({
  /**
   * 组件的属性列表
   */
  options: {
    pureDataPattern: /^_/ // 指定所有 _ 开头的数据字段为纯数据字段
  },
  properties: paymentSelectProps,
  observers: {
    'forbidPdPay': function (forbidPdPay) {
      this.setPayWayList(forbidPdPay)
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    payWayList: genPdFirstPayWayList(),
    unionPayConfig: {},
  },

  /**
   * 组件的方法列表
   */
  methods: {
    setUnionPayDiscountInfo: async function() {
      const defaultBody = '{"hide": true}'
      const { Body } = await unionPayConfigCos.getObject('dsxcx/unionpay/config.json').catch(() => ({ Body: defaultBody }))
      const unionPayConfig = JSON.parse(Body || defaultBody)
      const expireTime = (unionPayConfig.expireTime || '').replace(/-/g, '/')
      if (expireTime) {
        const timeDiff = Number.parseInt(`${(new Date(expireTime).getTime() - Date.now()) / 1000}`)
        if (timeDiff < 0) {
          unionPayConfig.discountText = ''
        } else if (timeDiff < (30 * 60)) { // 如果倒计时小于30分钟，则开始倒计时
          // 倒计时到达expireTime后，清空discountText
          this._timer = setTimeout(() => {
            this.setData({
              unionPayConfig: {
                ...unionPayConfig,
                discountText: '',
              }
            })
          }, timeDiff * 1000)
        }
      }
      this.setData({
        unionPayConfig,
      })
    },
    reportPay(payType = '') {
      const { code, name } = [
        [payType === WE_CHAT, { code: '1161000002', name: '微信支付' }],
        [payType === UNIONPAY, { code: '1161000003', name: '云闪付支付' }],
        [true, { code: '1161000001', name: '钱包支付' }],
      ].find((item) => item[0])[1];
      return sensors.clickReport({
        element_code: code,
        element_name: name,
        element_content: name,
      });
    },
    selectThirdPay ({ currentTarget: { dataset: { type } } }) {
      // 余额足的时候，如果果币支付开启，微信支付禁用状态
      const {
        mainBalanceIsNotEnough,
        disableUnionPay,
        selectWxPay: currentSelectWxPay,
        selectUnionPay: currentSelectUnionPay,
      } = this.data
      const selectWxPay = type === 'wechat'
      const selectUnionPay = type === 'unionpay'
      if (selectUnionPay && disableUnionPay) {
        wx.showToast({
          title: '此服务不支持云闪付支付',
          icon: 'error',
        })
        return
      }
      const selectWay = ([
        [selectWxPay && !currentSelectWxPay, WE_CHAT],
        [selectUnionPay && !currentSelectUnionPay, UNIONPAY],
      ].find((item) => item[0]) || [])[1]
      selectWay && this.reportPay(selectWay)
      // if (!mainBalanceIsNotEnough && selectPagodaPay) {
      //   return
      // }
      // 钱包足的时候，开启微信支付，则关闭钱包支付
      if (!mainBalanceIsNotEnough) {
        this.triggerEvent('switchPagodaPayChange', { selectPagodaPay: false })
      }
      this.triggerEvent('setWaySelect', {
        selectWxPay,
        selectUnionPay,
      })
    },
    pdpay() {
      const { mainBalance, disablePay, forbidPdPay } = this.data
      if (mainBalance === 0 || forbidPdPay || disablePay) {
        return
      }
      this.data.selectPagodaPay || this.reportPay()
      this.triggerEvent('setWaySelect', {
        selectWxPay: false,
        selectUnionPay: false,
      })
      this.triggerEvent('switchPagodaPayChange', { selectPagodaPay: true })
    },
    setPayWayList (forbidPdPay) {
      // 禁用钱包支付时，微信支付要放在前面，且设置选中
      // console.log('reversePayWayList');

      let payWayList = []
      if (forbidPdPay) {
        payWayList = genWXFirstPayWayList()
      } else {
        payWayList = genPdFirstPayWayList()
      }
      this.setData({
        payWayList
      })
    },
    // 切换余额支付开关
    switchChange(e) {
      const isOpen = e.detail.value
      this.triggerEvent('switchPagodaPayChange', { selectPagodaPay: isOpen })
      // 如果余额足,余额支付关闭则自动打开微信支付, 余额开启则关闭微信支付
      // console.log(isOpen)
      // console.log('mainBalanceIsNotEnough', this.data.mainBalanceIsNotEnough)
      // 现在mainBalanceIsNotEnough为true的时候才会在wxml中触发执行这段逻辑
      // 会导致下面的逻辑不执行,所以无需下面的逻辑了
      // if (!this.data.mainBalanceIsNotEnough) {
      //   this.triggerEvent('setWaySelect', !isOpen)
      // }
    },
    showUnionPayDiscountRule() {
      const { discountRule } = this.data.unionPayConfig
      if (!discountRule) {
        return
      }
      wx.showModal({
        content: discountRule,
        showCancel: false,
        cancelText: '知道了'
      })
    },
  },

  lifetimes: {
    attached() {
      this.setUnionPayDiscountInfo()
    },
    detached() {
      this._timer && clearTimeout(this._timer)
    }
  }
})
