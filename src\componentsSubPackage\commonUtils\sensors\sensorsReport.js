module.exports = {
  "bgxxShopToSearch": {
    "blockName": '页面顶部',
    "blockCode": '1130020',
    "element_name": '搜索',
    "element_code": '1130000002',
  },
  "bgxxShopGoodsDetailAddCart": {
    "element_code": 1130400001,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页",
    "blockName": "底部操作栏",
    "blockCode": "1130413",
  },
  "bgxxShopGoodsDetailPlus": {
    "element_code": 1130400002,
    "element_name": "加号",
    "element_content": "加号",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页",
    "blockName": "底部操作栏",
    "blockCode": "1130413",
  },
  "bgxxShopGoodsDetailMinus": {
    "element_code": 1130400003,
    "element_name": "减号",
    "element_content": "减号",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页",
    "blockName": "底部操作栏",
    "blockCode": "1130413",
  },
  "bgxxShopGoodsDetailToshare": {
    "element_code": 1130400004,
    "element_name": "分享",
    "element_content": "分享",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页",
    "blockName": "底部操作栏",
    "blockCode": "1130413",
  },
  "bgxxShopGoodsDetailToCart": {
    "element_code": 1130400005,
    "element_name": "跳转购物车",
    "element_content": "跳转购物车",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页",
    "blockName": "底部操作栏",
    "blockCode": "1130413",
  },
  "bgxxShopGoodsDetailToHomepage": {
    "element_code": 1130400006,
    "element_name": "跳转首页",
    "element_content": "跳转首页",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页"
  },
  "bgxxShopGoodsDetailToXxMember": {
    "element_code": 1130400007,
    "element_name": "心享会员开卡条",
    "element_content": "心享会员开卡条",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页"
  },
  "bgxxShopGoodsDetailSharePoster": {
    "element_code": 1130401002,
    "element_name": "分享海报",
    "element_content": "分享海报",
    "screen_code": 11304,
    "screen_name": "商品详情页"
  },
  "bgxxShopGoodsDetailShareCancel": {
    "element_code": 1130401005,
    "element_name": "取消分享",
    "element_content": "取消分享",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页"
  },
  "bgxxShopCouponGoodsDetail": {
    "element_code": 1131000001,
    "element_name": "商品图",
    "element_content": "商品图",
    "screen_code": 11310,
    "screen_name": "次日达优惠券适用商品页"
  },
  "bgxxShopCouponGoodsAddCart": {
    "element_code": 1131000002,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 11310,
    "screen_name": "次日达优惠券适用商品页"
  },
  "bgxxShopCouponGoodsToCart": {
    "element_code": 1131000003,
    "element_name": "悬浮购物车",
    "element_content": "悬浮购物车",
    "screen_code": 11310,
    "screen_name": "次日达优惠券适用商品页"
  },
  "bgxxShopCouponGoodsPriceRange": {
    "element_code": 1130600004,
    "element_name": "顶部价格",
    "element_content": "顶部价格",
    "screen_code": 11310,
    "screen_name": "次日达优惠券适用商品页"
  },
  "bgxxCategorySearchInput": {
    "element_code": 1130100001,
    "element_name": "搜索",
    "element_content": "搜索",
    "screen_code": 11301,
    "screen_name": "次日达品类页"
  },
  "bgxxCategoryCommonKey": {
    "screen_code": 11301,
    "screen_name": "次日达品类页"
  },
  "bgxxCategoryGoodsAdd": {
    "element_code": 1130102001,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 11301,
    "screen_name": "次日达品类页"
  },
  "bgxxCategoryGoodsDetail": {
    "element_code": 1130102002,
    "element_name": "查看商品详情",
    "element_content": "查看商品详情",
    "screen_code": 11301,
    "screen_name": "次日达品类页"
  },
  "bgxxCategoryGoodsPlus": {
    "element_code": 1130102003,
    "element_name": "加购",
    "element_content": "加购",
    "screen_code": 11301,
    "screen_name": "次日达品类页"
  },
  "bgxxCategoryGoodsMinus": {
    "element_code": 1130102004,
    "element_name": "减购",
    "element_content": "减购",
    "screen_code": 11301,
    "screen_name": "次日达品类页"
  },
  "bgxxInputSearch": {
    "element_code": 1130800001,
    "element_name": "搜索按钮",
    "element_content": "搜索按钮",
    "screen_code": 11308,
    "screen_name": "熊猫搜索页"
  },
  "bgxxHistorySearch": {
    "element_code": 1130800003,
    "element_name": "点击历史搜索",
    "element_content": "点击历史搜索",
    "screen_code": 11308,
    "screen_name": "熊猫搜索页"
  },
  "bgxxClearHistorySearch": {
    "element_code": 1130800002,
    "element_name": "清空历史搜索",
    "element_content": "清空历史搜索",
    "screen_code": 11308,
    "screen_name": "熊猫搜索页"
  },
  "bgxxRecommendSearch": {
    "element_code": 1130800004,
    "element_name": "点击热门搜索",
    "element_content": "点击热门搜索",
    "screen_code": 11308,
    "screen_name": "熊猫搜索页"
  },
  "bgxxShopGoodsExpose": {
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxCategoryGoodsExpose": {
    "screen_code": 11301,
    "screen_name": "次日达品类页"
  },
  "bgxxInputSearchGoodsExpose": {
    "screen_code": 11308,
    "screen_name": "熊猫搜索页"
  },
  "bgxxActivityGoodsExpose": {
    "screen_code": 11305,
    "screen_name": "熊猫综合专题"
  },
  "bgxxGoodsDetailGoodsExpose": {
    "screen_code": 11304,
    "screen_name": "次日达商品详情页"
  },
  "bgxxHomeStoreChoose": {
    "blockName": '页面顶部',
    "blockCode": '1130020',
    "element_name": '地址门店',
    "element_code": '1130000007',
  },
  "bgxxHomeHotBanner": {
    "element_code": 1130010001,
    "element_name": "热点banner",
    "element_content": "热点banner",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeVipGoodAddCart": {
    "element_code": 1130011001,
    "element_name": "心享会员专区加入购物车",
    "element_content": "心享会员专区加入购物车",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeOrderToSkipTips": {
    "element_code": 1130012001,
    "element_name": "自提订单提醒",
    "element_content": "自提订单提醒",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeOneBuyGoodsPopupAddCart": {
    "element_code": 1130013001,
    "element_name": "一元蔬菜加购弹窗加购",
    "element_content": "一元蔬菜加购弹窗加购",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeOneBuyGoodsPopupDelCart": {
    "element_code": 1130013002,
    "element_name": "一元蔬菜加购弹窗减购",
    "element_content": "一元蔬菜加购弹窗减购",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeOneBuyGoodsPopupClose": {
    "element_code": 1130013003,
    "element_name": "一元蔬菜弹窗关闭（返回）",
    "element_content": "一元蔬菜弹窗关闭（返回）",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeWaterFallFlowTab": {
    "element_code": 1130014001,
    "element_name": "点击瀑布流tab",
    "element_content": "点击瀑布流tab",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeWaterFallFlowAdvert": {
    "element_code": 1130014002,
    "element_name": "点击瀑布流广告位",
    "element_content": "点击瀑布流广告位",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeFloating": {
    "element_code": 1130015001,
    "element_name": "点击floating",
    "element_content": "点击floating",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxOrderDetailStorePhone": {
    "element_code": 1131100001,
    "element_name": "门店电话",
    "element_content": "门店电话",
    "screen_code": 11311,
    "screen_name": "次日达订单详情页"
  },
  "bgxxOrderDetailOpenStoreLoaction": {
    "element_code": 1131100002,
    "element_name": "导航",
    "element_content": "导航",
    "screen_code": 11311,
    "screen_name": "次日达订单详情页"
  },
  "bgxxOrderDetailShareFamily": {
    "element_code": 1131100003,
    "element_name": "分享给家人代领",
    "element_content": "分享给家人代领",
    "screen_code": 11311,
    "screen_name": "次日达订单详情页"
  },
  "bgxxOrderDetailCopyOrderId": {
    "element_code": 1131100004,
    "element_name": "复制订单编号",
    "element_content": "复制订单编号",
    "screen_code": 11311,
    "screen_name": "次日达订单详情页"
  },
  "bgxxOrderDetailInvoice": {
    "element_code": 1131100005,
    "element_name": "开具发票",
    "element_content": "开具发票",
    "screen_code": 11311,
    "screen_name": "次日达订单详情页"
  },
  "bgxxSearchGoodsPic": {
    "element_code": 1130801001,
    "element_name": "商品图",
    "element_content": "商品图",
    "screen_code": 11308,
    "screen_name": "熊猫搜索页"
  },
  "bgxxSearchGoodsAddCart": {
    "element_code": 1130801002,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 11308,
    "screen_name": "熊猫搜索页"
  },
  "bgxxSearchGoodsOpenCart": {
    "element_code": 1130801003,
    "element_name": "跳转购物车",
    "element_content": "跳转购物车",
    "screen_code": 11308,
    "screen_name": "熊猫搜索页"
  },
  "bgxxPaySuccessCheckOrder": {
    "element_code": 1130700001,
    "element_name": "查看订单",
    "element_content": "查看订单",
    "screen_code": 11307,
    "screen_name": "次日达支付成功页"
  },
  "bgxxPaySuccessToMallIndex": {
    "element_code": 1130700002,
    "element_name": "继续逛逛",
    "element_content": "继续逛逛",
    "screen_code": 11307,
    "screen_name": "次日达支付成功页"
  },
  "bgxxPaySuccessGoodsPic": {
    "element_code": 1130700003,
    "element_name": "商品图",
    "element_content": "商品图",
    "screen_code": 11307,
    "screen_name": "次日达支付成功页"
  },
  "bgxxPaySuccessGoodsAddCart": {
    "element_code": 1130700004,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 11307,
    "screen_name": "次日达支付成功页"
  },
  "bgxxPaySuccessOpenCart": {
    "element_code": 1130700005,
    "element_name": "跳转购物车",
    "element_content": "跳转购物车",
    "screen_code": 11307,
    "screen_name": "次日达支付成功页"
  },
  "bgxxPaySuccessBanner": {
    "element_code": 1130700006,
    "element_name": "banner广告位",
    "element_content": "banner广告位",
    "screen_code": 11307,
    "screen_name": "次日达支付成功页"
  },
  "bgxxCollectGoodsPic": {
    "element_code": 1130600001,
    "element_name": "商品列表",
    "element_content": "商品列表",
    "screen_code": 11306,
    "screen_name": "次日达运费凑单页"
  },
  "bgxxCollectGoodsAddCart": {
    "element_code": 1130600002,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 11306,
    "screen_name": "次日达运费凑单页"
  },
  "bgxxCollectOpenCart": {
    "element_code": 1130600003,
    "element_name": "跳转购物车",
    "element_content": "跳转购物车",
    "screen_code": 11306,
    "screen_name": "次日达运费凑单页"
  },
  "bgxxSubjectActivityOpenCart": {
    "element_code": 1130500001,
    "element_name": "悬浮购物车",
    "element_content": "悬浮购物车",
    "screen_code": 11305,
    "screen_name": "熊猫综合专题"
  },
  "bgxxSubjectActivityAddCart": {
    "element_code": 1130501001,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 11305,
    "screen_name": "熊猫综合专题"
  },
  "bgxxSubjectActivityGoodsPic": {
    "element_code": 1130501002,
    "element_name": "商品图片",
    "element_content": "商品图片",
    "screen_code": 11305,
    "screen_name": "熊猫综合专题"
  },
  "bgxxSubjectActivityUseCoupon": {
    "element_code": 1130502001,
    "element_name": "优惠券模块",
    "element_content": "优惠券模块",
    "screen_code": 11305,
    "screen_name": "熊猫综合专题"
  },
  "bgxxGoodDetailToActivityPage": {
    "element_code": 1130400008,
    "element_name": "跳转买赠活动",
    "element_content": "跳转买赠活动",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页"
  },
  "bgxxGoodDetailSavePoster": {
    "element_code": 1130401003,
    "element_name": "保存分享海报",
    "element_content": "保存分享海报",
    "screen_code": 11304,
    "screen_name": "次日达商品详情页"
  },
  "bgxxConfirmOrderChooseDeliver": {
    "element_code": 1130300001,
    "element_name": "选择配送上门方式",
    "element_content": "选择配送上门方式",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderChooseSelfTake": {
    "element_code": 1130300002,
    "element_name": "选择门店自提方式",
    "element_content": "选择门店自提方式",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderChoosePickUpStore": {
    "element_code": 1130300003,
    "element_name": "选择提货门店",
    "element_content": "选择提货门店",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderChooseReceiveAddress": {
    "element_code": 1130300004,
    "element_name": "选择收货地址",
    "element_content": "选择收货地址",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderChangePickUpStore": {
    "element_code": 1130300005,
    "element_name": "修改提货门店",
    "element_content": "修改提货门店",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderChangeReceiveAddress": {
    "element_code": 1130300006,
    "element_name": "修改收货地址",
    "element_content": "修改收货地址",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderChangeDeliverStore": {
    "element_code": 1130300007,
    "element_name": "修改配送门店",
    "element_content": "修改配送门店",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderChangeDeliverTime": {
    "element_code": 1130300008,
    "element_name": "选择送达时间",
    "element_content": "选择送达时间",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderChooseCoupon": {
    "element_code": 1130300009,
    "element_name": "选择优惠券",
    "element_content": "选择优惠券",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderBuyVip": {
    "element_code": 1130300010,
    "element_name": "心享会员开卡/续费",
    "element_content": "心享会员开卡/续费",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderVipWalletPay": {
    "element_code": 1130300011,
    "element_name": "会员钱包支付",
    "element_content": "会员钱包支付",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderWxPay": {
    "element_code": 1130300012,
    "element_name": "微信支付",
    "element_content": "微信支付",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderNowPay": {
    "element_code": 1130300013,
    "element_name": "立即支付",
    "element_content": "立即支付",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderChooseActivities": {
    "element_code": 1130300014,
    "element_name": "选择优惠活动",
    "element_content": "选择优惠活动",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderPartSend": {
    "element_code": 11303010001,
    "element_name": "参加满赠",
    "element_content": "参加满赠",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderNewCustomerDiscount": {
    "element_code": 11303010002,
    "element_name": "新客满折活动",
    "element_content": "新客满折活动",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderNotPartSend": {
    "element_code": 11303010003,
    "element_name": "不参与活动",
    "element_content": "不参与活动",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderOneBuyGoodsGiveUp": {
    "element_code": 11303020001,
    "element_name": "一元蔬菜放弃机会",
    "element_content": "一元蔬菜放弃机会",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderOneBuyGoodsCheck": {
    "element_code": 11303020002,
    "element_name": "一元蔬菜去看看（凑单）",
    "element_content": "一元蔬菜去看看（凑单）",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderOneBuyGoodsGiveUpTrue": {
    "element_code": 11303020003,
    "element_name": "直接放弃",
    "element_content": "直接放弃",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxConfirmOrderOneBuyGoodsFind": {
    "element_code": 11303020004,
    "element_name": "去看看（凑单）",
    "element_content": "去看看（凑单）",
    "screen_code": 11303,
    "screen_name": "次日达确认订单页"
  },
  "bgxxCartToMallIndex": {
    "element_code": 1130200001,
    "element_name": "去首页逛逛",
    "element_content": "去首页逛逛",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartSettlement": {
    "element_code": 1130200002,
    "element_name": "结算",
    "element_content": "结算",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartClearInvalidGoods": {
    "element_code": 1130200003,
    "element_name": "清空失效商品",
    "element_content": "清空失效商品",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartCollectBills": {
    "element_code": 1130200004,
    "element_name": "免运费去凑单（现售）",
    "element_content": "免运费去凑单（现售）",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartGoodsAddNum": {
    "element_code": 1130200005,
    "element_name": "加数量",
    "element_content": "加数量",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartGoodsDelNum": {
    "element_code": 1130200006,
    "element_name": "减数量",
    "element_content": "减数量",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartSetIsCheckAll": {
    "element_code": 1130200007,
    "element_name": "合计勾选",
    "element_content": "合计勾选",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartSetIsCheckAllNot": {
    "element_code": 1130200008,
    "element_name": "合计取消勾选",
    "element_content": "合计取消勾选",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartToSendGoods": {
    "element_code": 1130201001,
    "element_name": "买赠活动去看看",
    "element_content": "买赠活动去看看",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartActivityChange": {
    "element_code": 1130201002,
    "element_name": "买赠活动换促销",
    "element_content": "买赠活动换促销",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartToSendGoodDetail": {
    "element_code": 1130201003,
    "element_name": "买赠赠品详情页",
    "element_content": "买赠赠品详情页",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartNeedGifts": {
    "element_code": 1130202001,
    "element_name": "需要小葱",
    "element_content": "需要小葱",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartNotNeedGifts": {
    "element_code": 1130202002,
    "element_name": "不需要小葱",
    "element_content": "不需要小葱",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartNotEnoughGiftsNum": {
    "element_code": 1130202003,
    "element_name": "不足送葱弹窗提示",
    "element_content": "不足送葱弹窗提示",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartDiscountDetail": {
    "element_code": 1130203001,
    "element_name": "优惠明细",
    "element_content": "优惠明细",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxCartBuyVipCard": {
    "element_code": 1130206001,
    "element_name": "开通会员（立即省钱）",
    "element_content": "开通会员（立即省钱）",
    "screen_code": 11302,
    "screen_name": "次日达购物车页"
  },
  "bgxxHomeWaterFallGoodsClick": {
    "element_code": 1130014003,
    "element_name": "点击瀑布流商品",
    "element_content": "点击瀑布流商品",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeWaterFallGoodsClickGoods": {
    "element_code": 1130025002,
    "element_name": "点击瀑布流商品",
    "element_content": "点击瀑布流商品",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeWaterFallGoodsAdd": {
    "element_code": 1130014004,
    "element_name": "加购瀑布流商品",
    "element_content": "加购瀑布流商品",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeNewGoodsRecommendAdd": {
    "element_code": 1130016001,
    "element_name": "新品推荐商品点击",
    "element_content": "新品推荐商品点击",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeNewCustomerCardPackagedClick": {
    "element_code": 1130005003,
    "element_name": "新人券包立即使用",
    "element_content": "新人券包立即使用",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxHomeBrandClick": {
    "element_code": 1130000009,
    "element_name": "品牌说明",
    "element_content": "品牌说明",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "bgxxPaySuccessPage": {
    "element_code": 1130700007,
    "element_name": "次日达支付成功页",
    "element_content": "次日达支付成功页",
    "screen_code": 11307,
    "screen_name": "次日达支付成功页"
  },
  "bgxxToDeliveryPage": {
    "element_code": 1130000010,
    "element_name": "跳转到及时达首页",
    "element_content": "跳转到及时达首页",
    "screen_code": 11300,
    "screen_name": "次日达首页"
  },
  "categoryDeliveryAddress": {
    "element_code": 130000001,
    "element_name": "配送地址",
    "element_content": "配送地址",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categorySearch": {
    "element_code": 130000005,
    "element_name": "搜索",
    "element_content": "搜索",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryPopup": {
    "element_code": 130006001,
    "element_name": "水果外卖弹窗",
    "element_content": "水果外卖弹窗",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryPopupClose": {
    "element_code": 130006002,
    "element_name": "关闭弹窗",
    "element_content": "关闭弹窗",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryCouponClick": {
    "element_code": 130008001,
    "element_name": "优惠券",
    "element_content": "优惠券",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryCouponMoreClick": {
    "element_code": 130008002,
    "element_name": "查看更多",
    "element_content": "查看更多",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryCouponPopupClose": {
    "element_code": 130009001,
    "element_name": "关闭",
    "element_content": "关闭",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryCouponGet": {
    "element_code": 130009002,
    "element_name": "领取",
    "element_content": "领取",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryCouponToUse": {
    "element_code": 130009003,
    "element_name": "去使用",
    "element_content": "去使用",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryCouponGetAll": {
    "element_code": 130009004,
    "element_name": "一键领取",
    "element_content": "一键领取",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryAppPopupClose": {
    "element_code": 130010001,
    "element_name": "关闭",
    "element_content": "关闭",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryRedPaperPopupToUse": {
    "element_code": 130012001,
    "element_name": "去使用",
    "element_content": "去使用",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryRedPaperPopupClose": {
    "element_code": 130012002,
    "element_name": "关闭弹窗",
    "element_content": "关闭弹窗",
    "screen_code": 1300,
    "screen_name": "水果外卖"
  },
  "categoryTopicAddCart": {
    "element_code": 180100002,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 1801,
    "screen_name": "综合专题活动页"
  },
  "categoryTopicToCart": {
    "element_code": 180100003,
    "element_name": "购物车",
    "element_content": "购物车",
    "screen_code": 1801,
    "screen_name": "综合专题活动页"
  },
  "categoryTopicGetCoupon": {
    "element_code": 180100004,
    "element_name": "优惠券",
    "element_content": "优惠券",
    "screen_code": 1801,
    "screen_name": "综合专题活动页"
  },
  "categoryTopicToChoice": {
    "element_code": 180100005,
    "element_name": "加购弹起弹窗",
    "element_content": "加购弹起弹窗",
    "screen_code": 1801,
    "screen_name": "综合专题活动页"
  },
  "categoryTopicPic": {
    "element_code": 180100006,
    "element_name": "图片",
    "element_content": "图片",
    "screen_code": 1801,
    "screen_name": "综合专题活动页"
  },
  "categoryToCart": {
    "element_code": 180101006,
    "element_name": "跳转购物车",
    "element_content": "跳转购物车",
    "screen_code": 1801,
    "screen_name": "综合专题活动页"
  },
  "categoryBillDetail": {
    "element_code": 190000008,
    "element_name": "总的明细",
    "element_content": "总的明细",
    "screen_code": 1801,
    "screen_name": "综合专题活动页"
  },
  "categorySubmit": {
    "element_code": 190000009,
    "element_name": "总的一键结算",
    "element_content": "总的一键结算",
    "screen_code": 1801,
    "screen_name": "综合专题活动页"
  },
  "categoryCouponGoodsAddCart": {
    "element_code": 122300001,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 1223,
    "screen_name": "及时达优惠券适用商品列表页"
  },
  "categoryCouponGoodsToCart": {
    "element_code": 122300002,
    "element_name": "悬浮购物车",
    "element_content": "悬浮购物车",
    "screen_code": 1223,
    "screen_name": "及时达优惠券适用商品列表页"
  },
  "categoryCouponGoodsPriceRange": {
    "element_code": 122300003,
    "element_name": "顶部价格",
    "element_content": "顶部价格",
    "screen_code": 1223,
    "screen_name": "及时达优惠券适用商品列表页"
  },
  "categoryCouponGoodsChoice": {
    "element_code": 122300005,
    "element_name": "一品多规",
    "element_content": "一品多规",
    "screen_code": 1223,
    "screen_name": "及时达优惠券适用商品列表页"
  },
  "waterGoodsAddCart": {
    "element_code": 122300002,
    "element_name": "商品加购",
    "element_content": "商品加购",
    "screen_code": 1402,
    "screen_name": "红包雨领券活动页"
  },
  "waterGoodsToCart": {
    "element_code": 122300003,
    "element_name": "悬浮购物车",
    "element_content": "悬浮购物车",
    "screen_code": 1402,
    "screen_name": "红包雨领券活动页"
  },
  "waterGoodsToIndex": {
    "element_code": 122300004,
    "element_name": "跳转品类页",
    "element_content": "跳转品类页",
    "screen_code": 1402,
    "screen_name": "红包雨领券活动页"
  },
  "fightGroupsTopicImage": {
    "element_code": 110900001,
    "element_name": "拼团专题头图",
    "element_content": "拼团专题头图",
    "screen_code": 1109,
    "screen_name": "拼团综合专题页"
  },
  "fightGroupsTopicGoods": {
    "element_code": 110900002,
    "element_name": "拼团商品区域",
    "element_content": "拼团商品区域",
    "screen_code": 1109,
    "screen_name": "拼团综合专题页"
  },
  "fightGroupsTopicJoinGroup": {
    "element_code": 110900003,
    "element_name": "去开团",
    "element_content": "去开团",
    "screen_code": 1109,
    "screen_name": "拼团综合专题页"
  },
  "fightGroupsToPurchase": {
    "element_code": 110000012,
    "element_name": "马上抢",
    "element_content": "马上抢",
    "screen_code": 1100,
    "screen_name": "拼团首页"
  },
  "fightGroupsToRemind": {
    "element_code": 110000013,
    "element_name": "提醒我",
    "element_content": "提醒我",
    "screen_code": 1100,
    "screen_name": "拼团首页"
  },
  "fightGroupsToLook": {
    "element_code": 110000014,
    "element_name": "去看看",
    "element_content": "去看看",
    "screen_code": 1100,
    "screen_name": "拼团首页"
  },
  "fightGroupsSwitchCity": {
    "element_code": 110700001,
    "element_name": "城市切换",
    "element_content": "城市切换",
    "screen_code": 1107,
    "screen_name": "门店切换页"
  },
  "fightGroupsSwitchStore": {
    "element_code": 110700002,
    "element_name": "门店切换",
    "element_content": "门店切换",
    "screen_code": 1107,
    "screen_name": "门店切换页"
  },
  "fightGroupsLocation": {
    "element_code": 110700003,
    "element_name": "重新定位",
    "element_content": "重新定位",
    "screen_code": 1107,
    "screen_name": "门店切换页"
  },
  "fightGroupsStoreDetail": {
    "element_code": 110700004,
    "element_name": "门店详情",
    "element_content": "门店详情",
    "screen_code": 1107,
    "screen_name": "门店切换页"
  },
  "helpCouponListPage": {
    "element_code": 1120401001,
    "element_name": "访问助力领券",
    "element_content": "访问助力领券",
    "screen_code": 11204,
    "screen_name": "助力领券活动主页"
  },
  "helpCouponStartOrEnterActive": {
    "element_code": 1120401002,
    "element_name": "进入/发起活动",
    "element_content": "进入/发起活动",
    "screen_code": 11204,
    "screen_name": "助力领券活动主页"
  },
  "helpCouponClickInviteRecords": {
    "element_code": 1120401003,
    "element_name": "进入活动记录",
    "element_content": "进入活动记录",
    "screen_code": 11204,
    "screen_name": "助力领券活动主页"
  },
  "helpCouponDetailPage": {
    "element_code": 1120501001,
    "element_name": "转发领券",
    "element_content": "转发领券",
    "screen_code": 11205,
    "screen_name": "助力领券详情页"
  },
  "helpCouponDetailOtherActive": {
    "element_code": 1120502001,
    "element_name": "点击其他活动",
    "element_content": "点击其他活动",
    "screen_code": 11205,
    "screen_name": "助力领券详情页"
  },
  "helpCouponDetailCouponGoods": {
    "element_code": 1120503001,
    "element_name": "点击可用商品",
    "element_content": "点击可用商品",
    "screen_code": 11205,
    "screen_name": "助力领券详情页"
  },
  "helpCouponDetailCheckMoreActive": {
    "element_code": 1120504001,
    "element_name": "查看更多活动",
    "element_content": "查看更多活动",
    "screen_code": 11205,
    "screen_name": "助力领券详情页"
  },
  "helpCouponDetailCheckMoreGoods": {
    "element_code": 1120507001,
    "element_name": "进入发起人助力页",
    "element_content": "进入发起人助力页",
    "screen_code": 11205,
    "screen_name": "助力领券详情页"
  },
  "helpCouponDetailOpenAcivteRule": {
    "element_code": 1120506001,
    "element_name": "打开活动规则",
    "element_content": "打开活动规则",
    "screen_code": 11205,
    "screen_name": "助力领券详情页"
  },
  "helpCouponDetailCloseAcivteRule": {
    "element_code": 1120506003,
    "element_name": "关闭活动规则",
    "element_content": "关闭活动规则",
    "screen_code": 11205,
    "screen_name": "助力领券详情页"
  },
  "helpCouponInviteePage": {
    "element_code": 1120601001,
    "element_name": "进入助力人页面",
    "element_content": "进入助力人页面",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponInviteeHelp": {
    "element_code": 1120602001,
    "element_name": "点击助力",
    "element_content": "点击助力",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponShowCouponPopup": {
    "element_code": 1120603001,
    "element_name": "显示优惠券弹窗",
    "element_content": "显示优惠券弹窗",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponcloseCouponPopup": {
    "element_code": 1120603002,
    "element_name": "关闭优惠券弹窗",
    "element_content": "关闭优惠券弹窗",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponCouponPopupToUse": {
    "element_code": 1120603003,
    "element_name": "优惠券弹窗去使用",
    "element_content": "优惠券弹窗去使用",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponFinishStartActive": {
    "element_code": 1120604001,
    "element_name": "助力后发起活动",
    "element_content": "助力后发起活动",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponFinishToActiveList": {
    "element_code": 1120604001,
    "element_name": "助力后进去活动列表",
    "element_content": "助力后进去活动列表",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponMoreListStartActive": {
    "element_code": 1120605001,
    "element_name": "更多活动发起活动",
    "element_content": "更多活动发起活动",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponCheckMoreActive": {
    "element_code": 1120606001,
    "element_name": "查看更多活动",
    "element_content": "查看更多活动",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponInviteeOpenRule": {
    "element_code": 1120607001,
    "element_name": "打开活动规则",
    "element_content": "打开活动规则",
    "screen_code": 11206,
    "screen_name": "助力人助力页"
  },
  "helpCouponRecordEnter": {
    "element_code": 1120705001,
    "element_name": "进入活动记录",
    "element_content": "进入活动记录",
    "screen_code": 11207,
    "screen_name": "助力领券活动记录页"
  },
  "helpCouponRecordCheckAll": {
    "element_code": 1120701001,
    "element_name": "全部",
    "element_content": "全部",
    "screen_code": 11207,
    "screen_name": "助力领券活动记录页"
  },
  "helpCouponRecordCheckKeeping": {
    "element_code": 1120702001,
    "element_name": "进行中",
    "element_content": "进行中",
    "screen_code": 11207,
    "screen_name": "助力领券活动记录页"
  },
  "helpCouponRecordCheckSuccess": {
    "element_code": 1120703001,
    "element_name": "助力成功",
    "element_content": "助力成功",
    "screen_code": 11207,
    "screen_name": "助力领券活动记录页"
  },
  "helpCouponRecordCheckFail": {
    "element_code": 1120704001,
    "element_name": "助力失败",
    "element_content": "助力失败",
    "screen_code": 11207,
    "screen_name": "助力领券活动记录页"
  },
  "homeDeliveryMember": {
    "element_code": 180000005,
    "element_name": "会员码",
    "element_content": "会员码",
    "screen_code": 1800,
    "screen_name": "首页"
  },
  "homeDeliveryMemberLevel": {
    "element_code": 180000028,
    "element_name": "会员标签",
    "element_content": "会员标签",
    "screen_code": 1800,
    "screen_name": "首页"
  },
  "homeDeliveryToBgxxMember": {
    "element_code": 180000034,
    "element_name": "心享开卡",
    "element_content": "心享开卡",
    "screen_code": 1800,
    "screen_name": "首页"
  },
  "homeDeliveryToFloating": {
    "element_code": 180000035,
    "element_name": "floating",
    "element_content": "floating",
    "screen_code": 1800,
    "screen_name": "首页"
  },
  "homeDeliveryGuideToBgxx": {
    "element_code": 180005001,
    "element_name": "熊猫大鲜商品",
    "element_content": "熊猫大鲜商品",
    "screen_code": 1800,
    "screen_name": "首页"
  },
  "goodsDetailToCart": {
    "element_code": 131002004,
    "element_name": "购物车",
    "element_content": "购物车",
    "screen_code": 1310
  },
  "goodsDetailEvaluationToCart": {
    "blockName": '底部操作栏',
    "blockCode": 131015,
    "element_code": 131002004,
    "element_name": "购物车",
    "element_content": "购物车",
  },
  "cartSettlement": {
    "element_code": 190000001,
    "element_name": "结算",
    "element_content": "结算",
    "screen_code": 1900
  },
  "cartAddGoods": {
    "element_code": 190000002,
    "element_name": "加号",
    "element_content": "加号",
    "screen_code": 1900
  },
  "cartSubGoods": {
    "element_code": 190000003,
    "element_name": "减号",
    "element_content": "减号",
    "screen_code": 1900
  },
  "openXxVipCard": {
    "element_code": 190000005,
    "element_name": "心享提示条",
    "element_content": "心享提示条",
    "screen_code": 1900
  },
  "goodsDetailAddCartBtn": {
    "element_code": 131000001,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 1310,
    "screen_name": "及时达商品详情页"
  },
  "goodsDetailAddCart": {
    "element_code": 131000003,
    "element_name": "加购",
    "element_content": "加购",
    "screen_code": 1310
  },
  "goodsDetailSubCart": {
    "element_code": 131000004,
    "element_name": "减购",
    "element_content": "减购",
    "screen_code": 1310,
    "screen_name": "及时达商品详情页"
  },
  "categoryAddCart": {
    "element_code": 130003002,
    "element_name": "加购",
    "element_content": "加购",
    "screen_code": 1300
  },
  "categorySubCart": {
    "element_code": 130003003,
    "element_name": "选规格",
    "element_content": "选规格",
    "screen_code": 1300
  },
  "categoryShareAddCart": {
    "element_code": 130003004,
    "element_name": "分享加购",
    "element_content": "分享加购",
    "screen_code": 1300
  },
  "storePunchAddGoods": {
    "element_code": 121901004,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 1219
  },
  "storePunchToCarPage": {
    "element_code": 121903004,
    "element_name": "购物车",
    "element_content": "购物车",
    "screen_code": 1219
  },
  "searchGoodsAddGoods": {
    "element_code": 131100005,
    "element_name": "加号",
    "element_content": "加号",
    "screen_code": 1311
  },
  "searchGoodsSubGoods": {
    "element_code": 131100006,
    "element_name": "减号",
    "element_content": "减号",
    "screen_code": 1311
  },
  "searchGoodsChoice": {
    "element_code": 131100010,
    "element_name": "一品多规弹窗",
    "element_content": "一品多规弹窗",
    "screen_code": 1311
  },
  "searchGoodsToCarPage": {
    "element_code": 131101004,
    "element_name": "购物车",
    "element_content": "购物车",
    "screen_code": 1311
  },
  "showAfternoonTea": {
    "element_code": "180003041",
    "element_name": "展示下午茶",
    "element_content": "展示下午茶",
    "screen_code": "1800"
  },
  "showWaterfall": {
    "element_code": "180004011",
    "element_name": "查看瀑布流",
    "element_content": "查看瀑布流",
    "screen_code": "1800"
  },
  "tapWaterfallToCart": {
    "element_code": "180004014",
    "element_name": "点击瀑布流加购",
    "element_content": "点击瀑布流加购",
    "screen_code": "1800"
  },
  "tapWaterfallChoice": {
    "element_code": "180004020",
    "element_name": "一品多规弹窗",
    "element_content": "一品多规弹窗",
    "screen_code": "1800"
  },
  "offShelfRecommendCart": {
    "element_code": "131001001",
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": "1310",
    "screen_name": "及时达商品详情页"
  },
  "closeAdvertisementPopup": {
    "element_code": "180003061",
    "element_name": "广告弹窗",
    "element_content": "关闭广告弹窗",
    "screen_code": "1800",
    "screen_name": "首页"
  },
  "selectedMouthCardModeForMonth": {
    "element_code": "130202001",
    "element_name": "选中飞享月卡",
    "element_content": "选中飞享月卡",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "cancelMouthCardModeForMonth": {
    "element_code": "130202002",
    "element_name": "取消飞享月卡",
    "element_content": "取消飞享月卡",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "selectedMouthCardModeForYear": {
    "element_code": "130202003",
    "element_name": "选中轻享年卡",
    "element_content": "选中轻享年卡",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "cancelMouthCardModeForYear": {
    "element_code": "130202004",
    "element_name": "取消轻享年卡",
    "element_content": "取消轻享年卡",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "clickOpenMouthCardAtOncePopup": {
    "element_code": "130207001",
    "element_name": "立即开通",
    "element_content": "立即开通",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "notOpenMouthCardAtOncePopup": {
    "element_code": "130207002",
    "element_name": "暂不开通",
    "element_content": "暂不开通",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "closeMouthCardAtOncePopup": {
    "element_code": "130207003",
    "element_name": "关闭",
    "element_content": "关闭",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "clickSelectedMouthCardPopup": {
    "element_code": "130208001",
    "element_name": "我知道了",
    "element_content": "我知道了",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "closeSelectedMouthCardPopup": {
    "element_code": "130208002",
    "element_name": "关闭",
    "element_content": "关闭",
    "screen_code": "1301",
    "screen_name": "及时达确认订单页"
  },
  "1302_130210001": {
    "element_code": "130210001",
    "element_name": "超重默认自提",
    "element_content": "超重默认自提",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "1302_130210002": {
    "element_code": "130210002",
    "element_name": "超重自提弹窗",
    "element_content": "超重自提弹窗",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "1302_130211001": {
    "element_code": "130211001",
    "element_name": "出现随单充值模块",
    "element_content": "出现随单充值模块",
    "blockName": "随单充值模块",
    "blockCode": "11",
  },
  "1302_130211002": {
    "element_code": "130211002",
    "element_name": "选中随单充值",
    "element_content": "选中随单充值",
    "blockName": "随单充值模块",
    "blockCode": "11",
  },
  "1302_130211003": {
    "element_code": "130211003",
    "element_name": "取消随单充值",
    "element_content": "取消随单充值",
    "blockName": "随单充值模块",
    "blockCode": "11",
  },
  "1302_130210003": {
    "element_code": "130210003",
    "element_name": "超重自提弹窗支付",
    "element_content": "超重自提弹窗支付",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "1302_130210004": {
    "element_code": "130210004",
    "element_name": "超重自提弹窗关闭",
    "element_content": "超重自提弹窗关闭",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "1302_130210005": {
    "element_code": "130210005",
    "element_name": "超重切换配送提示",
    "element_content": "超重切换配送提示",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "1302_130210006": {
    "element_code": "130210006",
    "element_name": "配送超重弹窗",
    "element_content": "配送超重弹窗",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "1302_130210007": {
    "element_code": "130210007",
    "element_name": "配送超重弹窗去购物车",
    "element_content": "配送超重弹窗去购物车",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "1302_130210008": {
    "element_code": "130210008",
    "element_name": "配送超重弹窗去自提",
    "element_content": "配送超重弹窗去自提",
    "screen_code": "1302",
    "screen_name": "及时达确认订单页"
  },
  "NDBanner": {
    "element_name": "全国送轮播banner",
    "screen_code": 11203,
    "screen_name": "全国送"
  },
  "NDAddCart": {
    "element_code": 1120301001,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 11203,
    "screen_name": "全国送"
  },
  "NDCart": {
    "element_code": 1120300001,
    "element_name": "悬浮购物车",
    "element_content": "悬浮购物车",
    "screen_code": 11203,
    "screen_name": "全国送"
  },
  "bgxxShopCityListPage": {
    "title": "次日达选择城市",
    "screen_name": "次日达选择城市页",
    "screen_code": 1108
  },
  "fightGroupsHomepage": {
    "title": "拼团首页",
    "screen_name": "拼团首页",
    "screen_code": 1100
  },
  "fightGroupsGoodsDetailPage": {
    "title": "商品详情",
    "screen_name": "拼团详情页",
    "screen_code": 1101
  },
  "fightGroupsConfirmOrderPage": {
    "title": "拼团订单",
    "screen_name": "拼团确认订单页",
    "screen_code": 1102
  },
  "fightGroupsOrderDetailPage": {
    "title": "拼团订单",
    "screen_name": "拼团订单详情页",
    "screen_code": 1103
  },
  "fightGroupsOpenGroupPage": {
    "title": "支付成功",
    "screen_name": "开团页",
    "screen_code": 1104
  },
  "fightGroupsInviteFriendsPage": {
    "title": "邀请好友",
    "screen_name": "邀请好友页",
    "screen_code": 1105
  },
  "fightGroupsJoinGroupPage": {
    "title": "支付成功",
    "screen_name": "参团页",
    "screen_code": 1106
  },
  "fightGroupsExtrStorePage": {
    "title": "拼团订单",
    "screen_name": "门店切换页",
    "screen_code": 1107
  },
  "fightGroupsTopicPage": {
    "title": "拼团综合专题页",
    "screen_name": "拼团综合专题页",
    "screen_code": 1109
  },
  "userHomepage": {
    "title": "会员",
    "screen_name": "会员首页",
    "screen_code": 1200
  },
  "userModifyUserInfoPage": {
    "title": "会员修改信息",
    "screen_name": "会员修改信息页",
    "screen_code": 1201
  },
  "userMyGradePage": {
    "title": "会员俱乐部",
    "screen_name": "会员俱乐部",
    "screen_code": 1203
  },
  "userMyPrivlgPage": {
    "title": "会员特权",
    "screen_name": "会员特权页",
    "screen_code": 1203
  },
  "userPrivlgDetailPage": {
    "title": "特权详情",
    "screen_name": "特权详情页",
    "screen_code": 1204
  },
  "userBerryValuePage": {
    "title": "成长值",
    "screen_name": "成长值页",
    "screen_code": 1205
  },
  "userIntegrateDetailPage": {
    "title": "积分明细",
    "screen_name": "积分明细页",
    "screen_code": 1206
  },
  "userDepositPage": {
    "title": "账户余额",
    "screen_name": "账户余额页",
    "screen_code": 1208
  },
  "userSelfServicePage": {
    "title": "自助服务",
    "screen_name": "自助服务页",
    "screen_code": 1209
  },
  "userChangeCardPage": {
    "title": "自助换卡",
    "screen_name": "自助换卡页",
    "screen_code": 1210
  },
  "userBarcodePage": {
    "title": "优惠码详情页",
    "screen_name": "优惠码详情页",
    "screen_code": 1214
  },
  "userMyAwardPage": {
    "title": "签到奖励页",
    "screen_name": "签到奖励页",
    "screen_code": 1218
  },
  "userInvoiceManagePage": {
    "title": "发票管理",
    "screen_name": "发票管理页",
    "screen_code": 1305
  },
  "userAddInvoicePage": {
    "title": "新增发票",
    "screen_name": "新增发票页",
    "screen_code": 1306
  },
  "userEditInvoicePage": {
    "title": "编辑发票页",
    "screen_name": "编辑发票页",
    "screen_code": 1306
  },
  "userSelectVoucherPage": {
    "title": "选择代金券页",
    "screen_name": "选择代金券页",
    "screen_code": 1226
  },
  "xxshopHomepage": {
    "title": "次日达首页",
    "screen_name": "次日达首页",
    "screen_code": 11300
  },
  "categorySearchPage": {
    "title": "搜索",
    "screen_name": "搜索地址页",
    "screen_code": 1216
  },
  "addGoodsLayerDetail": {
    "title": "浏览多规格详情",
    "screen_name": "及时达品类页",
    "screen_code": 11301
  },
  "categoryConfirmOrderPage": {
    "title": "确认订单",
    "screen_name": "及时达确认订单页",
    "screen_code": 1302
  },
  "categoryPaySuccessPage": {
    "title": "支付成功",
    "screen_name": "及时达支付成功页",
    "screen_code": 1304
  },
  "xxshopSubjectActivitylPage": {
    "title": "次日达综合专题页",
    "screen_name": "次日达综合专题页",
    "screen_code": 11305
  },
  "categoryTopicPage": {
    "title": "活动专题",
    "screen_name": "及时达专题活动页",
    "screen_code": 1801
  },
  "categoryShopCartPage": {
    "title": "购物车页",
    "screen_name": "购物车页",
    "screen_code": 1900
  },
  "redEnvelopeHomepage": {
    "title": "拆红包首页",
    "screen_name": "拆红包首页",
    "screen_code": 1501
  },
  "redEnvelopeOpenPage": {
    "title": "拆红包页",
    "screen_name": "拆红包页",
    "screen_code": 1502
  },
  "storeOrderListPage": {
    "title": "门店订单列表页",
    "screen_name": "门店订单列表页",
    "screen_code": 1600
  },
  "storeAddOrderPage": {
    "title": "添加小票号页",
    "screen_name": "添加小票号页",
    "screen_code": 1601
  },
  "storeRefundGoodsListPage": {
    "title": "商品不满意页",
    "screen_name": "商品不满意页",
    "screen_code": 1606
  },
  "storeGoodsUnsatisfyPage": {
    "title": "申请退款页面",
    "screen_name": "申请退款页面",
    "screen_code": 1607
  },
  "chainsGoodsDetailPage": {
    "title": "接龙商品详情页",
    "screen_name": "接龙商品详情页",
    "screen_code": 11001
  },
  "chainsConfirmOrderPage": {
    "title": "接龙确认订单页",
    "screen_name": "接龙确认订单页",
    "screen_code": 11002
  },
  "chainsOrderDetailPage": {
    "title": "接龙订单详情页",
    "screen_name": "接龙订单详情页",
    "screen_code": 11003
  },
  "h5CommonPage": {
    "title": "小程序h5页",
    "screen_name": "小程序h5页",
    "screen_code": 11401
  },
  "noSignalCommonPage": {
    "title": "断网缺省页",
    "screen_name": "断网缺省页",
    "screen_code": 11004
  },
  "xxshopCategoryPage": {
    "title": "次日达分类页",
    "screen_name": "次日达分类页",
    "screen_code": 11301
  },
  "xxshopCategoryShopCartPage": {
    "title": "次日达购物车页",
    "screen_name": "次日达购物车页",
    "screen_code": 11302
  },
  "xxshopConfirmOrder": {
    "title": "次日达确认订单页",
    "screen_name": "次日达确认订单页",
    "screen_code": 11303
  },
  "sharePagePageShow": {
    "title": " 分享",
    "screen_name": "分享",
    "screen_code": "11102"
  },
  "shareToFriend": {
    "element_code": "1110200001",
    "element_name": "分享给好友",
    "element_content": "分享给好友",
    "screen_code": "11102"
  },
  "sendToPoster": {
    "element_code": "1110200002",
    "element_name": "发海报",
    "element_content": "发海报",
    "screen_code": "11102"
  },
  "customerAllOrder": {
    "element_code": 120007001,
    "element_name": "全部订单",
    "element_content": "全部订单",
    "screen_code": 1200,
    "screen_name": "会员首页"
  },
  "customerCategoryOrder": {
    "element_code": 120007002,
    "element_name": "水果外卖",
    "element_content": "水果外卖",
    "screen_code": 1200,
    "screen_name": "会员首页"
  },
  "customerXxshopOrder": {
    "element_code": 120007003,
    "element_name": "精选食材",
    "element_content": "精选食材",
    "screen_code": 1200,
    "screen_name": "会员首页"
  },
  "customerFightGroupsOrder": {
    "element_code": 120007004,
    "element_name": "拼团",
    "element_content": "拼团",
    "screen_code": 1200,
    "screen_name": "会员首页"
  },
  "customerStoreOrder": {
    "element_code": 120007005,
    "element_name": "门店订单",
    "element_content": "门店订单",
    "screen_code": 1200,
    "screen_name": "会员首页"
  },
  "customerEvaluate": {
    "element_code": 120007006,
    "element_name": "评价",
    "element_content": "评价",
    "screen_code": 1200,
    "screen_name": "会员首页"
  },
  '1200_120007009': {
    element_code: 120007009,
    element_name: '收礼订单',
    element_content: '收礼订单',
    screen_code: 1200,
    screen_name: '会员首页',
    blockCode: '07',
    blockName: '我的订单',
  },
  "customerNationalDeliveryOrder": {
    "element_code": 120007007,
    "element_name": "全国送",
    "element_content": "全国送",
    "screen_code": 1200,
    "screen_name": "会员首页"
  },
  "customerRelayOrder": {
    "element_code": 120007008,
    "element_name": "接龙订单",
    "element_content": "接龙订单",
    "screen_code": 1200,
    "screen_name": "会员首页"
  },
  "appDownloadGuide": {
    "element_code": 120010001,
    "element_name": "下载引导",
    "element_content": "下载引导",
    "screen_code": 1200,
    "screen_name": "会员首页"
  },
  "categoryOrderTab": {
    "element_code": 140000001,
    "element_name": "及时达订单",
    "element_content": "及时达订单",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "fightGroupsOrderTab": {
    "element_code": 140000002,
    "element_name": "拼团订单",
    "element_content": "拼团订单",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "storeOrderTab": {
    "element_code": 140000003,
    "element_name": "门店订单",
    "element_content": "门店订单",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "xxshopOrderTab": {
    "element_code": 140000004,
    "element_name": "精选食材订单",
    "element_content": "精选食材订单",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "chainsOrderTab": {
    "element_code": 140000005,
    "element_name": "接龙订单",
    "element_content": "接龙订单",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "categoryOrderListToDetail": {
    "element_code": 140001001,
    "element_name": "订单详情",
    "element_content": "订单详情",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "fightGroupsOrderListToDetail": {
    "element_code": 140002001,
    "element_name": "订单详情",
    "element_content": "订单详情",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "storeOrderListToDetail": {
    "element_code": 140003001,
    "element_name": "订单详情",
    "element_content": "订单详情",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "xxshopOrderListToDetail": {
    "element_code": 140004001,
    "element_name": "订单详情",
    "element_content": "订单详情",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "xxshopOrderListToPay": {
    "element_code": 140004002,
    "element_name": "立即付款",
    "element_content": "立即付款",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "xxshopOrderListToCancel": {
    "element_code": 140004003,
    "element_name": "取消订单",
    "element_content": "取消订单",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "xxshopOrderListRePurchase": {
    "element_code": 140004004,
    "element_name": "重新购买",
    "element_content": "重新购买",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "xxshopOrderListOneMore": {
    "element_code": 140004005,
    "element_name": "再来一单",
    "element_content": "再来一单",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "chainsOrderListToDetail": {
    "element_code": 140005001,
    "element_name": "订单详情",
    "element_content": "订单详情",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "chainsOrderListToContactStore": {
    "element_code": 140005002,
    "element_name": "联系店家",
    "element_content": "联系店家",
    "screen_code": 1400,
    "screen_name": "订单列表页"
  },
  "timelyOrderListToReturn": {
    "element_code": "140001007",
    "element_name": "不好吃瞬间退款",
    "element_content": "不好吃瞬间退款",
    "screen_code": "1400",
    "screen_name": "订单列表页"
  },
  "fightGroupOrderListToReturn": {
    "element_code": "140002005",
    "element_name": "申请退款",
    "element_content": "申请退款",
    "screen_code": "1400",
    "screen_name": "订单列表页"
  },
  "userCouponSubscribe": {
    "element_code": "120701001",
    "element_name": "点击开启提醒",
    "element_content": "点击开启提醒",
    "screen_code": "1207",
    "screen_name": "优惠券页"
  },
  "userCancelCollectionStore": {
    "element_code": "1151601001",
    "element_name": "取消收藏",
    "element_content": "取消收藏",
    "screen_code": "1516",
    "screen_name": "收藏门店页"
  },
  "collectStoreFromAdressList": {
    "element_code": "130100003",
    "element_name": "收藏门店",
    "element_content": "收藏门店",
    "screen_code": "1301",
    "screen_name": "地址选择页"
  },
  "cancelCollectStoreFromAdressList": {
    "element_code": "130100004",
    "element_name": "取消收藏门店",
    "element_content": "取消收藏门店",
    "screen_code": "1301",
    "screen_name": "地址选择页"
  },
  "collectOrderGoodsAddCart": {
    "element_code": 131200002,
    "element_name": "加入购物车",
    "element_content": "加入购物车",
    "screen_code": 1312,
    "screen_name": "凑单页"
  },
  "collectOrderGoodsToShopCart": {
    "element_code": 131200003,
    "element_name": "购物车",
    "element_content": "购物车",
    "screen_code": 1312,
    "screen_name": "凑单页"
  },
  "collectOrderGoodsTop": {
    "element_code": 131200004,
    "element_name": "顶部",
    "element_content": "顶部",
    "screen_code": 1312,
    "screen_name": "凑单页"
  },
  "exchangeCollectGoodsAddCart": {
    "element_code": 1131300001,
    "element_name": "换购商品加购",
    "element_content": "换购商品加购",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectGoodsCheck": {
    "element_code": 1131300002,
    "element_name": "换购商品查看",
    "element_content": "换购商品查看",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectGoodslistAddCart": {
    "element_code": 1131301001,
    "element_name": "换购商品加购",
    "element_content": "换购商品加购",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectGoodslistCheck": {
    "element_code": 1131301002,
    "element_name": "换购商品查看",
    "element_content": "换购商品查看",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectSectionAll": {
    "element_code": 1131301003,
    "element_name": "价格区间切换（全部）",
    "element_content": "价格区间切换（全部）",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectSectionOne": {
    "element_code": 1131301004,
    "element_name": "价格区间切换（0-10）",
    "element_content": "价格区间切换（0-10）",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectSectionTwo": {
    "element_code": 1131301005,
    "element_name": "价格区间切换（10-30）",
    "element_content": "价格区间切换（10-30）",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectSectionFour": {
    "element_code": 1131301006,
    "element_name": "价格区间切换（30-50）",
    "element_content": "价格区间切换（30-50）",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectSectionFive": {
    "element_code": 1131301006,
    "element_name": "价格区间切换（50以上）",
    "element_content": "价格区间切换（50以上）",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectSectionGoodsAddCart": {
    "element_code": 1131301007,
    "element_name": "换购商品加购",
    "element_content": "换购商品加购",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "exchangeCollectSectionGoodsCheck": {
    "element_code": 1131301008,
    "element_name": "换购商品查看",
    "element_content": "换购商品查看",
    "screen_code": 11313,
    "screen_name": "次日达换购活动页"
  },
  "confrimOrderClickMonthCardBenefits":{
    "element_code":130202010,
    "element_name":"月卡更多权益",
    "element_content":"月卡更多权益",
    "screen_code":1302,
    "screen_name":"及时达确认订单页"
  },
  "confrimOrderClickYearCardBenefits":{
    "element_code":130202011,
    "element_name":"年卡更多权益",
    "element_content":"年卡更多权益",
    "screen_code":1302,
    "screen_name":"及时达确认订单页"
  },
  "fruitMetroAllCate":{
    "element_code":180003026,
    "element_name":"及时达全部分类metro",
    "element_content":"及时达全部分类metro",
  },
  "freshMetroAllCate":{
    "element_code":1130002021,
    "element_name":"次日达全部分类",
    "element_content":"次日达全部分类",
  },
  "1900_190000006": {
    "element_code": 190000006,
    "element_name": "去逛逛",
    "element_content": "去逛逛",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190000007": {
    "element_code": 190000007,
    "element_name": "总的全选",
    "element_content": "总的全选",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190000008": {
    "element_code": 190000008,
    "element_name": "总的明细",
    "element_content": "总的明细",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190000009": {
    "element_code": 190000009,
    "element_name": "总的一键结算",
    "element_content": "总的一键结算",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001001": {
    "element_code": 190001001,
    "element_name": "及时达全选",
    "element_content": "及时达全选",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001002": {
    "element_code": 190001002,
    "element_name": "及时达优惠券",
    "element_content": "及时达优惠券",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001003": {
    "element_code": 190001003,
    "element_name": "及时达清空购物车",
    "element_content": "及时达清空购物车",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001004": {
    "element_code": 190001004,
    "element_name": "及时达去看看",
    "element_content": "及时达去看看",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001005": {
    "element_code": 190001005,
    "element_name": "及时达运费去凑单",
    "element_content": "及时达运费去凑单",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001006": {
    "element_code": 190001006,
    "element_name": "及时达优惠券去凑单",
    "element_content": "及时达优惠券去凑单",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001007": {
    "element_code": 190001007,
    "element_name": "及时达运费提示",
    "element_content": "及时达运费提示",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001008": {
    "element_code": 190001008,
    "element_name": "及时达勾选商品",
    "element_content": "及时达勾选商品",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001009": {
    "element_code": 190001009,
    "element_name": "及时达取消勾选商品",
    "element_content": "及时达取消勾选商品",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001010": {
    "element_code": 190001010,
    "element_name": "及时达加购",
    "element_content": "及时达加购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001011": {
    "element_code": 190001011,
    "element_name": "及时达减购",
    "element_content": "及时达减购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001012": {
    "element_code": 190001012,
    "element_name": "及时达修改数量",
    "element_content": "及时达修改数量",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001013": {
    "element_code": 190001013,
    "element_name": "及时达换规格",
    "element_content": "及时达换规格",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001014": {
    "element_code": 190001014,
    "element_name": "及时达查看商品详情",
    "element_content": "及时达查看商品详情",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001015": {
    "element_code": 190001015,
    "element_name": "及时达清空失效",
    "element_content": "及时达清空失效",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001016": {
    "element_code": 190001016,
    "element_name": "及时达明细",
    "element_content": "及时达明细",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190001017": {
    "element_code": 190001017,
    "element_name": "及时达结算",
    "element_content": "及时达结算",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002001": {
    "element_code": 190002001,
    "element_name": "全国送全选",
    "element_content": "全国送全选",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002002": {
    "element_code": 190002002,
    "element_name": "全国送清空购物车",
    "element_content": "全国送清空购物车",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002003": {
    "element_code": 190002003,
    "element_name": "全国送勾选商品",
    "element_content": "全国送勾选商品",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002004": {
    "element_code": 190002004,
    "element_name": "全国送取消勾选商品",
    "element_content": "全国送取消勾选商品",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002005": {
    "element_code": 190002005,
    "element_name": "全国送加购",
    "element_content": "全国送加购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002006": {
    "element_code": 190002006,
    "element_name": "全国送减购",
    "element_content": "全国送减购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002007": {
    "element_code": 190002007,
    "element_name": "全国送修改数量",
    "element_content": "全国送修改数量",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002008": {
    "element_code": 190002008,
    "element_name": "全国送查看商品详情",
    "element_content": "全国送查看商品详情",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002009": {
    "element_code": 190002009,
    "element_name": "全国送清空失效",
    "element_content": "全国送清空失效",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190002010": {
    "element_code": 190002010,
    "element_name": "全国送结算",
    "element_content": "全国送结算",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003001": {
    "element_code": 190003001,
    "element_name": "次日达全选",
    "element_content": "次日达全选",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003002": {
    "element_code": 190003002,
    "element_name": "次日达清空购物车",
    "element_content": "次日达清空购物车",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003003": {
    "element_code": 190003003,
    "element_name": "次日达运费去凑单",
    "element_content": "次日达运费去凑单",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003004": {
    "element_code": 190003004,
    "element_name": "次日达运费提示",
    "element_content": "次日达运费提示",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003005": {
    "element_code": 190003005,
    "element_name": "次日达勾选商品",
    "element_content": "次日达勾选商品",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003006": {
    "element_code": 190003006,
    "element_name": "次日达取消勾选商品",
    "element_content": "次日达取消勾选商品",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003007": {
    "element_code": 190003007,
    "element_name": "次日达加购",
    "element_content": "次日达加购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003008": {
    "element_code": 190003008,
    "element_name": "次日达减购",
    "element_content": "次日达减购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003009": {
    "element_code": 190003009,
    "element_name": "次日达修改数量",
    "element_content": "次日达修改数量",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003010": {
    "element_code": 190003010,
    "element_name": "次日达查看商品详情",
    "element_content": "次日达查看商品详情",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003011": {
    "element_code": 190003011,
    "element_name": "次日达清空失效",
    "element_content": "次日达清空失效",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003012": {
    "element_code": 190003012,
    "element_name": "次日达满赠换促销",
    "element_content": "次日达满赠换促销",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003013": {
    "element_code": 190003013,
    "element_name": "次日达满赠去看看",
    "element_content": "次日达满赠去看看",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003014": {
    "element_code": 190003014,
    "element_name": "次日达满赠去凑单",
    "element_content": "次日达满赠去凑单",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003015": {
    "element_code": 190003015,
    "element_name": "次日达赠品详情",
    "element_content": "次日达赠品详情",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003016": {
    "element_code": 190003016,
    "element_name": "次日达明细",
    "element_content": "次日达明细",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003017": {
    "element_code": 190003017,
    "element_name": "次日达结算",
    "element_content": "次日达结算",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190003018": {
    "element_code": 190003018,
    "element_name": "次日达去换购",
    "element_content": "次日达去换购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190005001": {
    "element_code": 190005001,
    "element_name": "换购品去凑单",
    "element_content": "换购品去凑单",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190005002": {
    "element_code": 190005002,
    "element_name": "换购商品加购",
    "element_content": "换购商品加购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190005003": {
    "element_code": 190005003,
    "element_name": "换购品查看详情",
    "element_content": "换购品查看详情",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190005004": {
    "element_code": 190005004,
    "element_name": "换购品加数量",
    "element_content": "换购品加数量",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190005005": {
    "element_code": 190005005,
    "element_name": "换购品减数量",
    "element_content": "换购品减数量",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190005006": {
    "element_code": 190005006,
    "element_name": "换购品修改数量",
    "element_content": "换购品修改数量",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006001": {
    "element_code": 190006001,
    "element_name": "次日达预售全选",
    "element_content": "次日达预售全选",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006002": {
    "element_code": 190006002,
    "element_name": "次日达预售清空购物车",
    "element_content": "次日达预售清空购物车",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006003": {
    "element_code": 190006003,
    "element_name": "次日达预售运费去凑单",
    "element_content": "次日达预售运费去凑单",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006004": {
    "element_code": 190006004,
    "element_name": "次日达预售运费提示",
    "element_content": "次日达预售运费提示",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006005": {
    "element_code": 190006005,
    "element_name": "次日达预售勾选商品",
    "element_content": "次日达预售勾选商品",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006006": {
    "element_code": 190006006,
    "element_name": "次日达预售取消勾选商品",
    "element_content": "次日达预售取消勾选商品",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006007": {
    "element_code": 190006007,
    "element_name": "次日达预售加购",
    "element_content": "次日达预售加购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006008": {
    "element_code": 190006008,
    "element_name": "次日达预售减购",
    "element_content": "次日达预售减购",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006009": {
    "element_code": 190006009,
    "element_name": "次日达预售修改数量",
    "element_content": "次日达预售修改数量",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006010": {
    "element_code": 190006010,
    "element_name": "次日达预售查看商品详情",
    "element_content": "次日达预售查看商品详情",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006011": {
    "element_code": 190006011,
    "element_name": "次日达预售清空失效",
    "element_content": "次日达预售清空失效",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190006012": {
    "element_code": 190006012,
    "element_name": "次日达预售结算",
    "element_content": "次日达预售结算",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190007001": {
    "element_code": 190007001,
    "element_name": "及时达确认清空",
    "element_content": "及时达确认清空",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190007002": {
    "element_code": 190007002,
    "element_name": "及时达取消清空",
    "element_content": "及时达取消清空",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190008001": {
    "element_code": 190008001,
    "element_name": "全国送确认清空",
    "element_content": "全国送确认清空",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190008002": {
    "element_code": 190008002,
    "element_name": "全国送取消清空",
    "element_content": "全国送取消清空",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190009001": {
    "element_code": 190009001,
    "element_name": "次日达确认清空",
    "element_content": "次日达确认清空",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190009002": {
    "element_code": 190009002,
    "element_name": "次日达取消清空",
    "element_content": "次日达取消清空",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190010001": {
    "element_code": 190010001,
    "element_name": "次日达预售确认清空",
    "element_content": "次日达预售确认清空",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190010002": {
    "element_code": 190010002,
    "element_name": "次日达预售取消清空",
    "element_content": "次日达预售取消清空",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190011001": {
    "element_code": 190011001,
    "element_name": "及时达去结算",
    "element_content": "及时达去结算",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190011002": {
    "element_code": 190011002,
    "element_name": "全国送去结算",
    "element_content": "全国送去结算",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190011003": {
    "element_code": 190011003,
    "element_name": "次日达去结算",
    "element_content": "次日达去结算",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1900_190011004": {
    "element_code": 190011004,
    "element_name": "关闭一键结算弹窗",
    "element_content": "关闭一键结算弹窗",
    "screen_code": 1900,
    "screen_name": "购物车页",
  },
  "1800_1130000011": {
    "element_code": 1130000011,
    "element_name": "次日达券包",
    "element_content": "次日达券包",
    "screen_code": 11300,
    "screen_name": "次日达首页",
  },
  "1300_130000006": {
    "element_code": 130000006,
    "element_name": "返回",
    "screen_code": 1300,
    "screen_name": "水果外卖(及时达品类页)",
  },
  "1300_130000007": {
    "element_code": 130000007,
    "element_name": "去购物车结算",
    "screen_code": 1300,
    "screen_name": "水果外卖(及时达品类页)",
  },
  "11301_1130100003": {
    "element_code": 1130100003,
    "element_name": "返回",
    "screen_code": 11301,
    "screen_name": "品类页",
  },
  "11301_1130100004": {
    "element_code": 1130100004,
    "element_name": "去购物车结算",
    "screen_code": 11301,
    "screen_name": "品类页",
  },
  "11517_1151700001": {
    "element_code": 1151700001,
    "element_name": "最高可退",
    "element_content": "最高可退",
    "screen_code": 11517
  },
  "11517_1151700002": {
    "element_code": 1151700002,
    "element_name": "提交",
    "element_content": "提交",
    "screen_code": 11517
  },
  "11517_1151701001": {
    "element_code": 1151701001,
    "element_name": "退至会员钱包",
    "element_content": "退至会员钱包",
    "screen_code": 11517,
    "blockName": '退至钱包弹窗',
    "blockCode": '01'
  },
  "11517_1151701002": {
    "element_code": 1151701002,
    "element_name": "选择其他途径",
    "element_content": "选择其他途径",
    "screen_code": 11517,
    "blockName": '退至钱包弹窗',
    "blockCode": '01'
  },
  "11517_1151702001": {
    "element_code": 1151702001,
    "element_name": "确定原路退还",
    "element_content": "确定原路退还",
    "screen_code": 11517,
    "blockName": '原路退弹窗',
    "blockCode": '02'
  },
  "11517_1151702002": {
    "element_code": 1151702002,
    "element_name": "取消原路退还",
    "element_content": "取消原路退还",
    "screen_code": 11517,
    "blockName": '原路退弹窗',
    "blockCode": '02'
  },
  "11518_1151800001": {
    "element_code": 1151800001,
    "element_name": "查看退款记录",
    "element_content": "查看退款记录",
    "screen_code": 11518
  },
  "11518_1151800002": {
    "element_code": 1151800002,
    "element_name": "逛逛商城",
    "element_content": "逛逛商城",
    "screen_code": 11518
  },
  "11518_1151800003": {
    "element_code": 1151800003,
    "element_name": "查看详情",
    "element_content": "查看详情",
    "screen_code": 11518
  },
  "11519_1151900001": {
    "element_code": 1151900001,
    "element_name": "退款记录",
    "element_content": "退款记录",
    "screen_code": 11519
  },
  "11407_1140700001": {
    "element_code": 1140700001,
    "element_name": "最高可退",
    "element_content": "最高可退",
    "screen_code": 11407
  },
  "11407_1140700002": {
    "element_code": 1140700002,
    "element_name": "提交",
    "element_content": "提交",
    "screen_code": 11407
  },
  "11407_1140800002": {
    "element_code": 1140800002,
    "element_name": "逛逛商城",
    "element_content": "逛逛商城",
    "screen_code": 11408
  },
  "11407_1140800003": {
    "element_code": 1140800003,
    "element_name": "查看详情",
    "element_content": "查看详情",
    "screen_code": 11408
  },
  "11407_1140900001": {
    "element_code": 1140900001,
    "element_name": "退款记录",
    "element_content": "退款记录",
    "screen_code": 11409
  },
  "11519_1151900002": {
    "element_code": 1151900002,
    "element_name": "三无退货记录",
    "element_content": "三无退货记录",
    "screen_code": 11519
  },
  "11519_1151900003": {
    "element_code": 1151900003,
    "element_name": "重新申请",
    "element_content": "重新申请",
    "screen_code": 11519
  },
  "1311_131100011": {
    blockName: '',
    blockCode: '131100',
    "element_code": 131100011,
    "element_name": "切换为宫格模式",
    "element_content": "切换为宫格模式",
    "screen_code": 1311,
    "screen_name": "搜索页"
  },
  "1311_131100012": {
    blockName: '',
    blockCode: '131100',
    "element_code": 131100012,
    "element_name": "切换为列表模式",
    "element_content": "切换为列表模式",
    "screen_code": 1311,
    "screen_name": "搜索页"
  },
  "11410": {
    "title": "duibaPay",
    "screen_name": "duibaPay",
    "screen_code": 11410
  },
  "11411": {
    "title": "duibaRedirect",
    "screen_name": "duibaRedirect",
    "screen_code": 11411
  },
  "1810_181001001": {
    "element_code": 181001001,
    "element_name": "登录",
    "element_content": "登录",
    "blockName": '会员头像区域',
    "blockCode": 181001,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181001002": {
    "element_code": 181001002,
    "element_name": "头像",
    "element_content": "头像",
    "blockName": '会员头像区域',
    "blockCode": 181001,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181001003": {
    "element_code": 181001003,
    "element_name": "普/银/金/钻会员标签",
    "element_content": "普/银/金/钻会员标签",
    "blockName": '会员头像区域',
    "blockCode": 181001,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181001004": {
    "element_code": 181001004,
    "element_name": "果粉会员标签",
    "element_content": "果粉会员标签",
    "blockName": '会员头像区域',
    "blockCode": 181001,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181001005": {
    "element_code": 181001005,
    "element_name": "心享等级",
    "element_content": "心享等级",
    "blockName": '会员头像区域',
    "blockCode": 181001,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181001006": {
    "element_code": 181001006,
    "element_name": "开通记录",
    "element_content": "开通记录",
    "blockName": '会员头像区域',
    "blockCode": 181001,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181001007": {
    "element_code": 181001007,
    "element_name": "节省记录",
    "element_content": "节省记录",
    "blockName": '会员头像区域',
    "blockCode": 181001,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181002001": {
    "element_code": 181002001,
    "element_name": "心享开通卡片",
    "element_content": "心享开通卡片",
    "blockName": '心享权益模块',
    "blockCode": 181002,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181002002": {
    "element_code": 181002002,
    "element_name": "心享续费卡片",
    "element_content": "心享续费卡片",
    "blockName": '心享权益模块',
    "blockCode": 181002,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181002003": {
    "element_code": 181002003,
    "element_name": "立即续费按钮",
    "element_content": "立即续费按钮",
    "blockName": '心享权益模块',
    "blockCode": 181002,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181002011": {
    "element_code": 181002011,
    "element_name": '心享权益卡片',
    "element_content": '心享权益卡片',
    "blockName": '心享权益模块',
    "blockCode": 181002,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181008001": {
    "element_code": 181008001,
    "element_name": '等级权益卡片',
    "element_content": '等级权益卡片',
    "blockName": '等级权益模块',
    "blockCode": 181008,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181008002": {
    "element_code": 181008002,
    "element_name": '立即开通',
    "element_content": '立即开通',
    "blockName": '等级权益模块',
    "blockCode": 181008,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181008003": {
    "element_code": 181008003,
    "element_name": '立即续费',
    "element_content": '立即续费',
    "blockName": '等级权益模块',
    "blockCode": 181008,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181009001": {
    "element_code": 181009001,
    "element_name": '我知道了',
    "element_content": '我知道了',
    "blockName": '初始化弹窗',
    "blockCode": 181009,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181010001": {
    "element_code": 181010001,
    "element_name": '我知道了',
    "element_content": '我知道了',
    "blockName": '升级弹窗',
    "blockCode": 181010,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181011001": {
    "element_code": 181011001,
    "element_name": '查看果粒值',
    "element_content": '查看果粒值',
    "blockName": '降级弹窗',
    "blockCode": 181011,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181011002": {
    "element_code": 181011002,
    "element_name": '我知道了',
    "element_content": '我知道了',
    "blockName": '降级弹窗',
    "blockCode": 181011,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181003001": {
    "element_code": 181003001,
    "element_name": "积分商城",
    "element_content": "积分商城",
    "blockName": '会员码模块',
    "blockCode": 181003,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181003002": {
    "element_code": 181003002,
    "element_name": "券包",
    "element_content": "券包",
    "blockName": '会员码模块',
    "blockCode": 181003,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181003003": {
    "element_code": 181003003,
    "element_name": "钱包/充值",
    "element_content": "钱包/充值",
    "blockName": '会员码模块',
    "blockCode": 181003,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181003004": {
    "element_code": 181003004,
    "element_name": "微信支付",
    "element_content": "微信支付",
    "blockName": '会员码模块',
    "blockCode": 181003,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181003007": {
    "element_code": 181003007,
    "element_name": "收起会员码",
    "element_content": "收起会员码",
    "blockName": '会员码模块',
    "blockCode": 181003,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181003008": {
    "element_code": 181003008,
    "element_name": "展开会员码",
    "element_content": "展开会员码",
    "blockName": '会员码模块',
    "blockCode": 181003,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181004001": {
    "element_code": 181004001,
    "element_name": "查看全部",
    "element_content": "查看全部",
    "blockName": '本月心享券包模块',
    "blockCode": 181004,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181006001": {
    "element_code": 181006001,
    "element_name": "点击社群入群卡片",
    "element_content": "点击社群入群卡片",
    "blockName": '社群模块',
    "blockCode": 181006,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1810_181006002": {
    "element_code": 181006002,
    "element_name": "点击banner运营位",
    "element_content": "点击banner运营位",
    "blockName": '社群模块',
    "blockCode": 181006,
    "screen_code": 1810,
    "screen_name": "会员中心页"
  },
  "1300_130013001": {
    "element_code": 130013001,
    "element_name": "购物车",
    "element_content": "购物车",
    "screen_code": 1300,
    "screen_name": "及时达品类页",
  },
  "1300_130013002": {
    "element_code": 130013002,
    "element_name": "结算",
    "element_content": "结算",
    "screen_code": 1300,
    "screen_name": "及时达品类页",
  },
  "1300_130014001": {
    "element_code": 130014001,
    "element_name": "及时达结算",
    "element_content": "及时达结算",
    "screen_code": 1300,
    "screen_name": "及时达品类页",
  },
  "1300_130014002": {
    "element_code": 130014002,
    "element_name": "全国送结算",
    "element_content": "全国送结算",
    "screen_code": 1300,
    "screen_name": "及时达品类页",
  },
  "1300_130015001": {
    "element_code": 130015001,
    "element_name": "及时达去结算",
    "element_content": "及时达去结算",
    "screen_code": 1300,
    "screen_name": "及时达品类页",
  },
  "1300_130015002": {
    "element_code": 130015002,
    "element_name": "全国送去结算",
    "element_content": "全国送去结算",
    "screen_code": 1300,
    "screen_name": "及时达品类页",
  },

  "11301_1130106001": {
    "element_code": 1130106001,
    "element_name": "购物车",
    "element_content": "购物车",
    "screen_code": 11301,
    "screen_name": "次日达品类页",
  },
  "11301_1130106002": {
    "element_code": 1130106002,
    "element_name": "结算",
    "element_content": "结算",
    "screen_code": 11301,
    "screen_name": "次日达品类页",
  },
  "11301_1130107001": {
    "element_code": 1130107001,
    "element_name": "次日达结算",
    "element_content": "次日达结算",
    "screen_code": 11301,
    "screen_name": "次日达品类页",
  },
  "11301_1130108001": {
    "element_code": 1130108001,
    "element_name": "次日达去结算",
    "element_content": "次日达去结算",
    "screen_code": 11301,
    "screen_name": "次日达品类页",
  },
  "1200_120000001": {
    "element_code": 120000001,
    "element_name": '会员信息',
    "element_content": '会员信息',
    "screen_code": 1200
  },
  "1200_120000005": {
    "element_code": 120000005,
    "element_name": "优惠券",
    "element_content": "优惠券",
    "screen_code": 1200
  },
  "1200_120000006": {
    "element_code": 120000006,
    "element_name": "钱包",
    "element_content": "钱包",
    "screen_code": 1200
  },
  "1200_120000012": {
    "element_code": 120000012,
    "element_name": "福利中心",
    "element_content": "福利中心",
    "screen_code": 1200
  },
  "1200_120004002": {
    "element_code": 120004002,
    "element_name": "任务一",
    "element_content": "任务一",
    "screen_code": 1200
  },
  "1200_120004003": {
    "element_code": 120004003,
    "element_name": "任务二",
    "element_content": "任务二",
    "screen_code": 1200
  },
  "1200_120004001": {
    "element_code": 120004001,
    "element_name": "领取积分",
    "element_content": "领取积分",
    "screen_code": 1200
  },
  "1200_120001002": {
    "element_code": 120001002,
    "element_name": "关闭",
    "element_content": "关闭",
    "screen_code": 1200
  },
  "1200_120001001": {
    "element_code": 120001001,
    "element_name": "点击弹窗",
    "element_content": "点击弹窗",
    "screen_code": 1200
  },
  "1200_120000007": {
    "element_code": 120000007,
    "element_name": "礼品卡",
    "element_content": "礼品卡",
    "screen_code": 1200
  },
  "1200_120000019": {
    "element_code": 120000019,
    "element_name": "试吃中心",
    "element_content": "试吃中心",
    "screen_code": 1200
  },
  "1200_120000020": {
    "element_code": 120000020,
    "element_name": "企业团购",
    "element_content": "企业团购",
    "screen_code": 1200
  },
  "1200_120000021": {
    "element_code": 120000021,
    "element_name": "加盟申请",
    "element_content": "加盟申请",
    "screen_code": 1200
  },
  "1200_120003001": {
    "element_code": 120003001,
    "element_name": "续费通知",
    "element_content": "续费通知",
    "screen_code": 1200
  },
  "1200_120003002": {
    "element_code": 120003002,
    "element_name": "积分通知",
    "element_content": "积分通知",
    "screen_code": 1200
  },
  "1200_120003003": {
    "element_code": 120003003,
    "element_name": "任务通知",
    "element_content": "任务通知",
    "screen_code": 1200
  },
  "1200_120000008": {
    "element_code": 120000008,
    "element_name": "百果园果礼商城",
    "element_content": "百果园果礼商城",
    "screen_code": 1200
  },
  "1200_120000004": {
    "element_code": 120000004,
    "element_name": "积分",
    "element_content": "积分",
    "screen_code": 1200
  },
  "1200_120005005": {
    "element_code": 120005005,
    "element_name": "更多",
    "element_content": "更多",
    "screen_code": 1200
  },
  "11606_1160601001": {
    "screen_code": 11606,
    "screen_name": "三无退货实名认证",
    "blockName": '协议弹窗',
    "blockCode": 1160601,
    "element_code": 1160601001,
    "element_name": "同意协议",
    "element_content": "同意协议"
  },
  "11606_1160601002": {
    "screen_code": 11606,
    "screen_name": "三无退货实名认证",
    "blockName": '协议弹窗',
    "blockCode": 1160601,
    "element_code": 1160601002,
    "element_name": "不同意协议",
    "element_content": "不同意协议"
  },
  "11606_1160602001": {
    "screen_code": 11606,
    "screen_name": "三无退货实名认证",
    "blockName": '提交认证',
    "blockCode": 1160602,
    "element_code": 1160602001,
    "element_name": "联系门店换货",
    "element_content": "联系门店换货"
  },
  "11606_1160602002": {
    "screen_code": 11606,
    "screen_name": "三无退货实名认证",
    "blockName": '提交认证',
    "blockCode": 1160602,
    "element_code": 1160602002,
    "element_name": "提交认证",
    "element_content": "提交认证"
  },
  "11607_1160701001": {
    "screen_code": 11607,
    "screen_name": "实名认证/换绑结果",
    "blockName": '实名认证成功',
    "blockCode": 1160701,
    "element_code": 1160701001,
    "element_name": "继续退款",
    "element_content": "继续退款"
  },
  "11607_1160702001": {
    "screen_code": 11607,
    "screen_name": "实名认证/换绑结果",
    "blockName": '实名认证失败',
    "blockCode": 1160702,
    "element_code": 1160702001,
    "element_name": "联系门店换货",
    "element_content": "联系门店换货"
  },
  "11607_1160702002": {
    "screen_code": 11607,
    "screen_name": "实名认证/换绑结果",
    "blockName": '实名认证失败',
    "blockCode": 1160702,
    "element_code": 1160702002,
    "element_name": "申请换绑",
    "element_content": "申请换绑"
  },
  "11607_1160702003": {
    "screen_code": 11607,
    "screen_name": "实名认证/换绑结果",
    "blockName": '实名认证失败',
    "blockCode": 1160702,
    "element_code": 1160702003,
    "element_name": "更换实名信息",
    "element_content": "更换实名信息"
  },
  "11607_1160702004": {
    "screen_code": 11607,
    "screen_name": "实名认证/换绑结果",
    "blockName": '实名认证失败',
    "blockCode": 1160702,
    "element_code": 1160702004,
    "element_name": "重新认证",
    "element_content": "重新认证"
  },
  "11607_1160703001": {
    "screen_code": 11607,
    "screen_name": "实名认证/换绑结果",
    "blockName": '申请换绑中',
    "blockCode": 1160703,
    "element_code": 1160703001,
    "element_name": "联系门店换货",
    "element_content": "联系门店换货"
  },
  "11607_1160703002": {
    "screen_code": 11607,
    "screen_name": "实名认证/换绑结果",
    "blockName": '申请换绑中',
    "blockCode": 1160703,
    "element_code": 1160703002,
    "element_name": "联系客服",
    "element_content": "联系客服"
  },
  "11607_1160704001": {
    "screen_code": 11607,
    "screen_name": "实名认证/换绑结果",
    "blockName": '申请换绑失败',
    "blockCode": 1160704,
    "element_code": 1160704001,
    "element_name": "联系门店换货",
    "element_content": "联系门店换货"
  },
  "11607_1160704002": {
    "screen_code": 11607,
    "screen_name": "实名认证/换绑结果",
    "blockName": '申请换绑失败',
    "blockCode": 1160704,
    "element_code": 1160704002,
    "element_name": "重新提交",
    "element_content": "重新提交"
  },
  "11608_1160800001": {
    "screen_code": 11608,
    "screen_name": "三无退货提交换绑",
    "blockCode": 1160800,
    "element_code": 1160800001,
    "element_name": "提交换绑",
    "element_content": "提交换绑"
  },
  "11608_1160800002": {
    "screen_code": 11608,
    "screen_name": "三无退货提交换绑",
    "blockCode": 1160800,
    "element_code": 1160800002,
    "element_name": "联系门店换货",
    "element_content": "联系门店换货"
  },
  "1900_190000010": {
    "element_code": 190000010,
    "element_name": "心享提示条",
    "element_content": "心享提示条"
  },
  "11609_1160900001": {
    "element_code": 1160900001,
    "element_name": "果粉价提示条",
    "element_content": "果粉价提示条"
  },
  "1800_180026001": {
    "blockName": "接龙模块",
    "blockCode": 180026,
    "element_code": 180026001,
    "element_name": "接龙模块",
    "element_content": "接龙模块"
  },
  '11611_1161100001': {
    'element_code': 1161100001,
    'element_name': '查看定金记录（输入状态）'
  },
  '11611_1161100002': {
    'element_code': 1161100002,
    'element_name': '查看定金记录（支付成功状态）'
  },
  '11611_1161100003': {
    'element_code': 1161100003,
    'element_name': '确认支付'
  },
  '11619_1161900001': {
    'element_code': 1161900001,
    'element_name': '复制订单号',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900004': {
    'element_code': 1161900004,
    'element_name': '点击转赠',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900003': {
    'element_code': 1161900003,
    'element_name': '点击充值',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900017': {
    'element_code': 1161900017,
    'element_name': '开票按钮',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900018': {
    'element_code': 1161900018,
    'element_name': '查看发票按钮',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900005': {
    'element_code': 1161900005,
    'element_name': '赠送中按钮',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900006': {
    'element_code': 1161900006,
    'element_name': '赠送中弹窗',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900008': {
    'element_code': 1161900008,
    'element_name': '已使用按钮',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900007': {
    'element_code': 1161900009,
    'element_name': '已使用弹窗',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900011': {
    'element_code': 1161900011,
    'element_name': '已赠出按钮',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900012': {
    'element_code': 1161900012,
    'element_name': '已赠出弹窗',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900014': {
    'element_code': 1161900014,
    'element_name': '已退款按钮',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161900015': {
    'element_code': 1161900015,
    'element_name': '已退款弹窗',
    'blockName': '默认',
    'blockCode': 1161900,
  },
  '11619_1161901001': {
    'element_code': 1161901001,
    'element_name': '继续充值（全部充值成功）',
    'blockName': '礼品卡充值结果弹窗',
    'blockCode': 1161901,
  },
  '11619_1161901002': {
    'element_code': 1161901002,
    'element_name': '查看余额（全部充值成功）',
    'blockName': '礼品卡充值结果弹窗',
    'blockCode': 1161901,
  },
  '11619_1161901003': {
    'element_code': 1161901003,
    'element_name': '继续充值（部分充值成功）',
    'blockName': '礼品卡充值结果弹窗',
    'blockCode': 1161901,
  },
  '11619_1161901004': {
    'element_code': 1161901004,
    'element_name': '查看余额（部分充值成功）',
    'blockName': '礼品卡充值结果弹窗',
    'blockCode': 1161901,
  },
  '11619_1161901005': {
    'element_code': 1161900015,
    'element_name': '取消（全部充值失败）',
    'blockName': '礼品卡充值结果弹窗',
    'blockCode': 1161901,
  },
  '11619_1161901006': {
    'element_code': 1161901006,
    'element_name': '联系客服（全部充值失败）',
    'blockName': '礼品卡充值结果弹窗',
    'blockCode': 1161901,
  },
  '1161901_1161901': {
    'blockName': '礼品卡充值结果弹窗',
    'blockCode': 1161901,
  },
  '1302_130200013': {
    'element_code': 130200013,
    'element_name': '配送上门',
  },
  '1302_130200014': {
    'element_code': 130200014,
    'element_name': '门店自提',
  },
}
