// bgxxUser/pages/orderDetail/index.js
const generateOrderInfo = require('./generateOrderInfo')
const coordtransform = require('../../../utils/coordUtil.js')
const common = require('../../../source/js/common').commonObj
import { refundPathEnum } from '../../../source/const/order';
const wxbarcode = require.async('../../../sourceSubPackage/barcode/index.js');
import deliveryTimeUtils from '../../../utils/deliveryTime'
const sensors = require('../../../utils/report/sensors')
const orderOperMixin = require('../../../mixins/bgxx/orderOperMixin')
const util = require('../../../utils/util')
const systemInfo = wx.getStorageSync('systemInfo')
const isIphoneX = systemInfo.model.indexOf('iPhone X') !== -1 ? true : false;
const config = require('../../../utils/config')
const app = getApp()
import { getStoreBusinessTime } from '../../../utils/util'
const orderBtn = require('../../../common/orderBtn')
const navigateToH5Mixin = require('../../../mixins/navigateToH5Mixin')

let globalPickupCode = ''

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // bg_detail_top: 'https://resource.pagoda.com.cn/group1/M21/56/A7/CmiLkGELnn2AEY3eAAHbxOLcyyk670.png',
    bg_detail_top: 'https://resource.pagoda.com.cn/group1/M21/59/3A/CmiLkGEbY-qAShbdAABRsmb5LF8090.png',
    isSuperVip: false, // 是否是心享会员
    hasPayPopup: false, // 是否弹出支付弹窗 默认为false
    hasChanelPopup: false, // 是否弹出取消弹窗 默认为false
    orderStatusInfo: () => { }, // 订单状态信息
    orderStoreInfo: () => { }, //自提门店信息
    pickUpOrderInfo: () => { }, // 自提信息
    orderPriceInfo: () => { }, // 订单价格信息
    orderInfoAndTime: () => { }, // 订单信息和时间信息
    payType: 0,
    balanceInfo: { // 钱包余额
      count: 0,
      enough: true
    },
    isShowInvoice: false, // 是否显示发票按钮
    // isShowOrderDesc: true,
    // isShowOrderTime: false,
    isIphoneX,
    stopCount: false,
    showDeliveryOrder: ['WAIT_PICKUP', 'STOCKING', 'PAYED', 'OUTBOUNDED'], // 所有需要显示提货码等信息的订单状态（待自提，配送中，已付款，已出仓）
    deliveryCanvasImage: '', // 提货码二维码canvas对应的图片
    deliveryWay: '1', //配送方式 1–门店自提  2–非门店自提(配送上门)
    receiverAddressInfo: () => { }, // 收货人地址信息
    hasConfirmPopup: false, // 是否弹出确认收货弹窗 默认为false
    selectDeliveryTime: false, // 重新选择配送时间
    orderGoodsType: {
      'F': '次日达',
      'G': '预售',
      'H': '周期购',
      'I': '次日达', // 现售补单
      'J': '预售', // 预售补单
    },
    showDeliveryInfo: false, // 是否显示提货时间，提货码等信息
    refundInfo: {}, // 三无退货信息
    orderCancelIsShow: false,
    showShareBtn: false,
    showSharePop: false,
    shareImage: '',
    shareCanvasHeight: '',
    // 订单状态信息
    orderStatusList: [],
    navBarBgColor: '', // 导航栏背景颜色
    navBarColor: '#fff', // 导航栏字体颜色
    backFilter: 1, // filter brightness
    showActivityAmount:false, //是否展示一元购优惠金额
    packAmountTips: '',   //包装费说明
    showCostBox: false,   //费用说明弹窗
    costBoxTitle: {       //底部弹窗组件头部样式
      size: '36rpx',
      height: '128rpx',
      bold: 'bold'
    },
    showCancelTips:false,  //是否显示取消订单失败提示
    showPopTip:false,
    animationData:null,
    leftDistance:0,
    toastDistanceBottom:'80rpx',
    //  所有需要显示他人代领按钮的订单状态（已付款、拣货中、已出仓、待配送、配送中,交易成功），
    showShareFriendOrder: ['PAYED','STOCKING','OUTBOUNDED','WAIT_DELIVERY','SENDING','TRADE_SUCCESS'],

    //  左侧按钮列表
    leftBtnList: [],

    showDeliveryPoster:false,//是否显示他人代领按钮,
    BGXX_PIC_DOMAIN: config.baseUrl.PAGODA_PIC_DOMAIN,
    showVipDetail: false, // 心享会员节省明细弹窗
    preGoodsList: [],
    customPickerVisible: false, // 自定义Picker是否展示
    deliveryTimePickerSeleted: [],
    deliveryTimePickerArray: [
      [],
      []
    ],
    goodsStorageTypeObj:{}, // 商品存储类型
    /**此订单是否可开票 */
    canInvoice: false,
    /**此订单是否果币支付 */
    isGBPay: false
  },
  _data:{
    isSelectTime: false,
    deliveryTimeMap: {},
    onSalePicker: [], // 现货，子单只会有一个
    preSalePicker: {}, // 预售，子单会有多个，序号记录
    currentSubOrderSelected: 'onSale', // 当前选择的子单，现售：'onSale';预售： 'preSale_0'
    deliveryLimitList: [],
    goodsImageLocalUrl:{}//商品列表头图的本地图片
  },
  mixins: [orderOperMixin, navigateToH5Mixin],
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('options', options);
    // 入口有 确认订单/订单列表/支付成功
    const isPay = options.isPay !== 'false'
    // 从参数中获取时为字符串
    const { selectTime =  false } = options
    this._data.isSelectTime = selectTime === 'true'
    this.setData({
      orderID: options.orderID,
      isPay: isPay,
      // selectDeliveryTime: selectTime === 'true'
    })
    // this.queryElement()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (!app.checkSignInsStatus()) return app.showSignInModal()
    const height = wx.getStorageSync('systemInfo').windowHeight
    const user = wx.getStorageSync('user')
    const { superVipStatus } = app.globalData
    this.setData({
      height: height * 2 - 100,
      user,
      isSuperVip: superVipStatus !== 'C',
      stopCount: false
    })
    // 页面刷新加载订单详情数据 分以支付和未支付
    this.getOrderDetailInfo(this.data.orderID, this.data.isPay)
    // 页面浏览事件
    sensors.pageScreenView()
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    wx.stopPullDownRefresh();
    this.getOrderDetailInfo(this.data.orderID, this.data.isPay)
  },
  onHide() {
    this.setData({
      stopCount: true
    })
  },
  onUnload() {
    this.setData({
      stopCount: true
    })
  },
  // 用了节流反而效果不好
  onPageScroll: function (ev) {
    this.setNavBarStyle(ev)
  },
  setNavBarStyle (ev) {

    const { scrollTop } = ev
    const { navBarHeight } = this.data
    const ratio = Math.min(scrollTop / (navBarHeight/2), 1)
    if(this.data.ratio === ratio) return
    if (ratio <= 0) {
      // 没有滚动距离
      this.setData({
        ratio,
        navBarBgColor: '',
        navBarColor: '#fff',
        backFilter: 1
      })
    } else {
      this.setData({
        ratio,
        navBarBgColor: `rgba(255,255,255, ${ratio})`,
        navBarColor: '#222222',
        backFilter: 1 - ratio
      })
    }
  },
  // 组装非预售商品数据
  getGoodsList(spot = [], aItem, systemTime) {
    if (!spot.length) return ''
    return {
      deliveryTime: aItem.deliveryTime,
      goodsOrderID: aItem.goodsOrderID,
      deliverySection: aItem.deliverySection.split(' ')[0].split('/')[0].concat(' ', aItem.deliverySection.split(' ')[1]),
      list: spot,
      orderSubType: aItem.orderSubType,
      deliveryTimeRange: [ // 配送时间选择范围
        [deliveryTimeUtils.getPickerShowTime(aItem.superVipUpdateDeliveryTime, systemTime)],
        deliveryTimeUtils.getTimePointList(aItem.store.openingTime)
      ]
    }
  },
  // 点击展示订单流状态
  showStatusList () {
    this.setData({showTimeLine: true})
  },
  /**
   * 获取订单详情数据，公共属性处理
   * @param  {} orderID
   * @param  {} isPay=true
   */
  getOrderDetailInfo(orderID, isPay = true) {
    const that = this
    app.api.bgxxGetOrderDetailInfo(isPay, this.data.user.userID, orderID).then(res => {
      let data = res.data,
        orderStatusInfo, // 订单状态信息
        orderStoreInfo, //自提门店信息
        pickUpOrderInfo, // 自提信息
        orderPriceInfo, // 订单价格信息
        orderInfoAndTime, // 订单信息和时间信息
        receiverAddressInfo, // 收货地址信息
        goodsList = {}, // 商品信息
        preGoodsList = [], // 预定商品信息
        payType = {
          'WX_MINI': '微信支付',
          'gb': '账户余额',
          'WX_SVIP_MIX_APP': '微信支付',
        }
      // let showDeliveryOrder = this.data.showDeliveryOrder
      // 次日达预售订单超时未自提也展示核销码
      // if (data.orderSubType === 'G') {
      //   showDeliveryOrder.push('MAYBE_TAKEN')
      // }
      const {
        deliveryTime,
        deliverySection,
        deliveryTimeBegin,
        deliveryTimeEnd
      } = (() => {
        let time = ''
        let section = ''
        let goodsOrderResult = {}
        // 同时有预售和现售则不展示预约时间
        if ((data.goodsOrderList && data.goodsOrderList.length < 2) || data.goodsList) {
          const result = data.goodsOrderList ? data.goodsOrderList[0] : data
          goodsOrderResult = result
          time = deliveryTimeUtils.stringDateTransferCharacter(result.deliveryTime ? result.deliveryTime.replace(/^\w+-/, '') : '')
          section = deliveryTimeUtils.stringDateTransferCharacter(result.deliverySection ? result.deliverySection.replace(/[\u4e00-\u9fa5]+/g, '').split(' ')[0].split('/')[0] : '')
        }
        return {
          deliveryTime: time,
          deliverySection: section,
          deliveryTimeEnd: goodsOrderResult.deliveryTimeEnd,
          deliveryTimeBegin: goodsOrderResult.deliveryTimeBegin,
        }
      })()
      // 对标题进行修改
      const title = (() => {
        if (data.titleInfo.title) {
          return data.titleInfo.title === '已自提' ? '交易成功' : data.titleInfo.title
        } else {
          return ''
        }
      })()
      orderStatusInfo = {
        orderStatus: title,
        // showStatusIcon: orderStatusIcon[data.titleInfo.title],
        // orderDesc: data.titleInfo.subTitle,
        orderFlowState: data.orderFlowState,
        orderSubType: data.orderSubType || '',
        deliveryTimeBegin: deliveryTimeBegin,
        deliveryTimeEnd: deliveryTimeEnd,
        isShowCancelButton:data.isShowCancelButton ||false//是否展示取消订单按钮
      }
      // 未支付时在data.goodsOrderList[0]才有goodsOrderStatus字段
      const { goodsOrderStatus } = data.goodsOrderList ? data.goodsOrderList[0] : data;
      Object.assign(
        orderStatusInfo,
        generateOrderInfo.genStatusAndDesc(data.titleInfo, goodsOrderStatus, data.deliveryWay),
        {
          // 不需要年份
          deliveryTime,
          deliverySection
        }
      )
      let vipFreeValue = 0
      if (data.freeInfoList.length > 0) {
        data.freeInfoList.forEach(item => {
          vipFreeValue += Number(item.freeValue)
        })
      }

      let { newVipDiscountAmount, payAmount, couponList, totalOneBuyActivityPrice='', totalBargainGoodsCoupon} = data
      newVipDiscountAmount = +newVipDiscountAmount ? (parseFloat(newVipDiscountAmount) / 100).toFixed(2) : ''
      couponList.forEach(coupon => {
        const { couponValue } = coupon
        coupon.couponValue = +couponValue ? (parseFloat(couponValue) / 100).toFixed(2) : ''
      })
      //一元购优惠金额
      totalOneBuyActivityPrice = +totalOneBuyActivityPrice ? (parseFloat(totalOneBuyActivityPrice) / 100).toFixed(2) : ''
      // 特价活动优惠金额 判断是否有超出购买数量的，如果有超出的则会展示该字段
      let arrGoodsList = []
      // 如果不是待支付订单 则取值goodsList
      if (data.goodsList && data.goodsList.length) {
        arrGoodsList = data.goodsList
      } else {
        // 如果是待支付订单 则遍历所有的商品判断，此时可能未拆单 所以需要遍历所有的商品
        data.goodsOrderList.forEach(item => arrGoodsList.push(...item.goodsList))
      }
      totalBargainGoodsCoupon = this.isBuyOutLimit(arrGoodsList) ? (parseFloat(totalBargainGoodsCoupon) / 100).toFixed(2) : 0
      orderPriceInfo = {
        freeValue: that.data.isSuperVip ? Number((vipFreeValue / 100).toFixed(2)) : '0',
        freeInfoList: data.freeInfoList || [],
        payAmount: +(parseFloat(payAmount) / 100).toFixed(2),
        weightCount: data.weightCount / 1000,
        newVipDiscountAmount,
        couponList,
        totalOneBuyActivityPrice,
        // 特价活动优惠的金额
        totalBargainGoodsCoupon,
        packAmount: (data.packAmount / 100).toFixed(2)   //包装费
      }
      this.setData({
        packAmountTips: data.packAmountTips    //包装费说明
      })
      // 保留接口返回的金额（单位分）
      that.initPayAmount = data.payAmount
      orderInfoAndTime = {
        orderNum: data.goodsOrderNum,
        createTime: data.createTime || '',
        payTime: data.payTime || '',
        pickedUpTime: data.pickedUpTime || '',
        channelTime: data.channelTime || '',
        closeTime: data.lastUpdate || '',
        outboundTime: data.outboundTime || '',
        arriveStoreTime: data.arriveStoreTime || '',
        actualDeliveryTime: data.actualDeliveryTime || '',
        confirmTime: data.confirmTime || ''
      }


      const {
        /**此订单可开票 */
        canInvoice,
        isGBPay
      } = (function() {
        const { channel, invoice, isAllGift, orderFlowState, payAmount, totalRefundAmount } = data

        return {
          canInvoice : (invoice && invoice.enableOpenInvoice === 'Y' && invoice.hasInvoiced === 'N') &&
            isAllGift !== 'Y' &&
            orderFlowState === 'TRADE_SUCCESS' &&
            (payAmount - totalRefundAmount > 0)
          ,
          isGBPay: channel === 'gb'
        }
      })()

      const orderInfo = Object.assign(data, { isPay, canInvoice })


      const { leftBtnList, rightBtnList } = orderBtn.genBtnList.call(this, {
        orderInfo,
        orderType: '次日达',
        orderStatus: data.titleInfo.title,
        isDetail: true
      })
      this.setData({
        leftBtnList,
        btnList: rightBtnList
      })
      if (isPay) {
        // 已支付订单
        this.setData({
          channel:data.channel,
          deliveryWay: data.deliveryWay,
          isAllGift: data.isAllGift,
          isRecurOrder: data.isRecurOrder
        })
        goodsList = {
          list: data.goodsList,
          deliveryTime: data.deliveryTimes,
          orderSubType: data.orderSubType
        }
        orderPriceInfo.totalAmount = (data.goodsTotalAmount / 100).toFixed(2)
        const { freight = 0, subFreight= 0, wholeFreight = 0 } = data
        //freight = +freight + +subFreight
        orderPriceInfo.freight = (wholeFreight / 100).toFixed(2)

        orderStoreInfo = {
          storeName: data.store.storeName,
          address: data.store.address,
          openingTime: data.store.openingTime,
          storePhone: data.store.storePhone,
          lat: data.store.lat,
          lon: data.store.lon,
          startTime:data.store.startTime||'',
          endTime:data.store.endTime||''
        }
        receiverAddressInfo = {
          userName: data.receiverAddress.userName,
          phoneNumber: data.receiverAddress.phoneNumber,
          address: this.getFullAdress(data.receiverAddress),
        }

        // 是否有每期详情
        const isEachDetails = data.isEachDetails

        // 是否能投诉
        const isAllowComplaint = data.isAllowComplaint
        // 是否有退款
        const isAllowRefund = data.isAllowRefund

        // 三无退货信息
        const { refundAmount = 0, refundStatus } = data
        const amount = +(refundAmount / 100).toFixed(2)

        const refundTips = (function() {
          const isOriginPath = data.refundPath === refundPathEnum.O
          const RERUND_TIPS = {
            // 已投诉
            1: () => '您的退款申请已受理，客服会尽快为您处理',
            // 退款中
            2: () => isOriginPath ?
              `您申请的退款金额￥${amount}将在1-3个工作日内退至您的微信支付账户` :
              '您的退款申请已受理，客服会尽快为您处理',
            // 退款成功
            3: () => isOriginPath ?
              `您申请的退款金额￥${amount}已原路退回至您的微信支付账户` :
              `您申请的退款金额￥${amount}已退回至您的钱包余额`,
            // 退款失败
            4: () => '您申请的退款失败'
          }
          const fn = RERUND_TIPS[refundStatus]

          return fn ? fn() : ''
        })()

        //缺货退款信息
        const { doubleRefundGoodsCount = 0, doubleRefundAmount = 0, doubleRefundToastContent = '' } = data
        // 是否开过发票
        this.setData({
          refundTips,
          isAllowRefund,
          isAllowComplaint,
          doubleRefundAmount: Number(doubleRefundAmount), // 缺货双倍退金额
          doubleRefundGoodsCount, //缺货双倍退商品种类数量
          doubleRefundToastContent  //缺货双倍退弹窗提示内容
        })

        switch (data.orderFlowState) {
          // 交易取消取消/已自提
          case 'CANCELED': case 'PICKED_UP':
          {
            break
          }
          // 交易关闭
          case 'CLOSED':
          {
            orderInfoAndTime.closeTime = data.lastUpdate
            break
          }
          // 已付款、拣货中、待自提 配送中 已出仓 超时未自提PAYED、STOCKING、WAIT_PICKUP SENDING OUTBOUNDED
          case 'PAYED': case 'STOCKING': case 'WAIT_PICKUP': case 'SENDING': case 'OUTBOUNDED':
          {
            // let channel = data.orderFlowState === 'PAYED' && data.channel
            const channel = data.channel
            if (this.data.showDeliveryOrder.indexOf(data.orderFlowState) !== -1) {
              const code = /\S{5}/.test(data.takeCode) && data.takeCode.replace(/\s/g, '').replace(/(.{4})/g, '$1 ')
              pickUpOrderInfo = {
                pickUpTime: data.deliverySection.split(' ')[0].split('/')[0].concat(' ', data.deliverySection.split(' ')[1]),
                takeCode: code,
                takeOrderDesc: data.takeOrderDesc,
                serialNumber: data.serialNumber
              }
              let failAgainRequest = false
              globalPickupCode = data.takeCode
              const getCanvasImage = () => {
                wxbarcode.then(module => module.barcode('barcode', data.takeCode, 600, 180, () => {
                  setTimeout(() => {
                    if (data.takeCode !== globalPickupCode) return
                    wx.canvasToTempFilePath({
                      canvasId: 'barcode',
                      success: (res) => {
                        that.setData({
                          showShareBtn: true,
                          deliveryCanvasImage: res.tempFilePath
                        })
                      },
                      fail: () => {
                        // 华为手机偶现条形码加载失败，如果失败，重新绘制一次
                        console.log('code fail')
                        that.setData({
                          showShareBtn: true
                        })
                        if (!failAgainRequest) {
                          failAgainRequest = true
                          getCanvasImage()
                        }
                      }
                    }, this)
                  }, 1000)
                }))
              }
              getCanvasImage()
              this.setData({
                pickUpOrderInfo
              })
            }

            this.setData({ channel })
            break
          }
        }

        this.setData({
          isEachDetails,
          goodsOrderID: data.goodsOrderID,
          invoice: data.invoice,
          goodsOrderNum: data.goodsOrderNum
        })
      } else {
        this.collectDeliveryTime(data.goodsOrderList) // 待产品确认是否详情需要展示冷冻冷藏标签 先保留
        // 未付款订单
        const list = data.goodsOrderList
        let spot = [],
          preSale = [],
          aItem
        for (const i in list) {
          const subType = list[i].orderSubType
          if (subType === 'F' || subType === 'I') {
            spot.push(...list[i].goodsList)
            aItem = list[i]
          } else {
            preSale.push(list[i])
          }
        }
        goodsList = this.getGoodsList(spot, aItem, res.systemTime)
        preGoodsList = preSale
        // 预售没有周期购
        preGoodsList.length > 0 && preGoodsList.forEach(item => {
          item.deliveryTimeRange = [
            [deliveryTimeUtils.getPickerShowTime(item.deliveryTime, res.systemTime)],
            deliveryTimeUtils.getTimePointList(item.store.openingTime)
          ]
        })
        this.setData({
          deliveryWay: list[0].deliveryWay
        })
        const { store: storeinfo, receiverAddress} = list[0]
        const {storeName, address, openingTime, storePhone, lat, lon,startTime='',endTime=''} = storeinfo
        orderStoreInfo = { storeName, address, openingTime, storePhone, lat, lon,startTime,endTime}
        receiverAddressInfo = {
          userName: receiverAddress.userName,
          phoneNumber: receiverAddress.phoneNumber,
          address: this.getFullAdress(receiverAddress)
        }
        orderPriceInfo.totalAmount = (data.goodsOrderAmount / 100).toFixed(2)
        const { totalFreight = 0, subFreight= 0, wholeFreight = 0 } = data
        //totalFreight = +totalFreight + +subFreight
        orderPriceInfo.freight = (wholeFreight / 100).toFixed(2)

        const enoughPay = parseFloat(data.mainBalance) >= parseFloat(data.payAmount)
        // 若余额不足 则设置默认支付方式为微信支付
        if (!enoughPay) {
          this.setData({ payType: 1 })
        }
        const payType = enoughPay ? 0 : 1
        orderPriceInfo.balanceInfo = {
          count: +parseFloat(data.mainBalance / 100).toFixed(2),
          enough: enoughPay
        }

        if (data.titleInfo.title === '已取消') {
          orderInfoAndTime.channelTime = data.lastUpdate
        }

        if (data.titleInfo.title === '待付款') {
          // 计算倒计时
          const [date, time] = data.timeExpire.split(' ')
          const [year, month, day] = date.split('-')
          // const [h, m, s] = time.split(':')

          const timeStamp = new Date(`${year}/${month}/${day} ${time}`).getTime()
          const conutTome = timeStamp - res.systemTime
          if (conutTome <= 0 && data.isCanceled === 'N') {
            // 前端判断已取消，但是后端定时任务两分钟才跑一次，当后端还没有取消的时候，前后端存在数据不一致，前端展示为已超时取消
            orderStatusInfo = {
              orderStatus: '已取消',
              statusIcon: generateOrderInfo.orderStatusIcon['已取消'],
              orderDesc: '超时未支付'
            }
            common.showModal('提示', '超时未支付,已为您取消订单', false, '', '', () => {
              this.setData({
                selectDeliveryTime: false
              })
            })
          } else {
            // 倒计时，超时未支付则重新进入此页面
            this.countDown(conutTome)
          }
        }



        // preGoodsList无论有没有均赋值，防止后续出错
        this.setData({
          timeExpire: data.timeExpire,
          payOrderNo: data.payOrderNum,
          payType,
          preGoodsList
        })
      }
      const orderDetail = data
      Object.assign(data, {
        goodsOrderStatus: goodsOrderStatus
      })
      // 营业时间
      Object.assign(orderStoreInfo, {
        storeBusinessTime:getStoreBusinessTime({
          startTime:orderStoreInfo.startTime||'',
          endTime:orderStoreInfo.endTime||'',
          openingTime:orderStoreInfo.openingTime||'',
        })
      })
      this.setData({
        payChannel: payType[data.channel],
        orderDetail,
        orderStatusInfo,
        orderStoreInfo,
        goodsList,
        orderPriceInfo,
        orderInfoAndTime,
        receiverAddressInfo,
        payOrderID: data.payOrderID,
        orderStatusList: generateOrderInfo.genOrderStatusList(data),
        canInvoice,
        isGBPay
      })

      //是否展示一元购活动金额(//有一元购活动信息&&商品享受一元购活动数量!==0 && 商品购买数量>商品享受一元购活动数量)
      const list = util.getObjectValue(this.data,'goodsList.list',[]);
      const showActivityAmount = list.filter(item=>item.activityInfo&&item.activityInfo.enjoyOneBuyNum!==0).some(res => Number(res.count)>Number(res.activityInfo.enjoyOneBuyNum))

      let showActivityAmountPre = false
      //判断预售商品是否一元购超限
      if(this.data.preGoodsList){
        const preList = this.data.preGoodsList;
        let arr = []
        preList.forEach(item=>{
          arr = arr.concat(item.goodsList)
        })
        showActivityAmountPre = arr.filter(item=>item.activityInfo&&item.activityInfo.enjoyOneBuyNum!==0).some(res => Number(res.count)>Number(res.activityInfo.enjoyOneBuyNum))
      }
      console.log(showActivityAmount,showActivityAmountPre)
      this.setData({
        showActivityAmount:showActivityAmount||showActivityAmountPre
      })

      const { showDeliveryOrder,showShareFriendOrder } = this.data
      const deliveryWay = Number(this.data.deliveryWay)
      const showDeliveryInfo = showDeliveryOrder.indexOf(orderStatusInfo.orderFlowState) !== -1 && this.data.pickUpOrderInfo && deliveryWay === 1
      // 是否显示他人代领按钮
      const showDeliveryPoster = showShareFriendOrder.indexOf(orderStatusInfo.orderFlowState) !== -1 &&  this.data.pickUpOrderInfo && deliveryWay === 2
      this.setData({
        showDeliveryInfo,
        showDeliveryPoster
      })
      this.getOrderCount()
      this._data.isSelectTime && this.deliveryTimeIsLimit({ showModal: false })
    }).catch (err => {
      console.log('err', err);
      common.showModal('提示', '请求错误，请稍后重试', false, '', '', () => {
        wx.navigateBack({ delta: 1 })
      })
    })
  },

  // 获取商品数量总计: 已支付时全部商品在goodsList中，未支付时在goodsList 和 preGoodsList中
  getOrderCount() {
    const { goodsList = {}, isPay, preGoodsList = [] } = this.data
    const list = goodsList.list || []
    let totalGoods = [...list]
    if (!isPay && preGoodsList.length) {
      preGoodsList.forEach(good => {
        totalGoods = [...totalGoods, ...good.goodsList]
      })
    }
    const totalCount = totalGoods.reduce((sum, cur) => {
      sum += +cur.count
      return sum
    }, 0)
    this.setData({totalCount})
  },

  getFullAdress(address) {
    const gisAddress = address.gisAddress
    function getParams(param) {
      return gisAddress.indexOf(param) !== -1 ? '' : param
    }
    return getParams(address.cityName) + getParams(address.gisDistrict) + gisAddress + address.blockNum
  },

  // 打开门店定位
  openStoreLoaction() {
    sensors.track('MPClick', 'bgxxOrderDetailOpenStoreLoaction')
    const lnglat = coordtransform.bd09togcj02(parseFloat(this.data.orderStoreInfo.lon), parseFloat(this.data.orderStoreInfo.lat))
    wx.openLocation({
      latitude: lnglat[1],
      longitude: lnglat[0],
      name: this.data.orderStoreInfo.storeName,
      address: this.data.orderStoreInfo.address
    })
  },
  // 去每期详情
  toinstallmentDetail() {
    wx.navigateTo({
      url: `../installmentDetail/index?goodsOrderID=${this.data.goodsOrderID}`
    })
  },
  // 联系门店
  connectStore() {
    sensors.track('MPClick', 'bgxxOrderDetailStorePhone')
    wx.makePhoneCall({
      phoneNumber: this.data.orderStoreInfo.storePhone,
    })
  },

  // 复制订单编号
  copyOrderId(e) {
    sensors.track('MPClick', 'bgxxOrderDetailCopyOrderId')
    wx.setClipboardData({
      data: e.currentTarget.dataset.orderid,
      success(res) {
        wx.showToast({
          title: '复制成功',
          icon: 'none'
        })
      }
    })
  },

  // 打开支付弹窗
  payImmediatelyBtnHandle() {
    this.setPayType()
    if (this.data.selectDeliveryTime) {
      if (!this.checkNullObj(this.data.goodsList) && !this.data.goodsList.showDeliveryTime) return wx.showToast({
        title: '请选择送达时间',
        icon: 'none'
      })
      // 无周期购，预售时间不能更改
      // const selectedpreGoodsList = this.data.preGoodsList.filter(item => {
      //   return item.orderSubType === 'H'
      // })
      // const selectCycleTime = this.data.preGoodsList.every(i => {
      //   return i.showDeliveryTime && i.showDeliveryTime !== ''
      // })

      // if (!selectCycleTime) return wx.showToast({
      //   title: '请选择送达时间',
      //   icon: 'none'
      // })
    }
    this.setData({
      hasPayPopup: true
    })
  },

  /**
   * 设置默认选中支付方式
   */
  setPayType() {
    const {
      balanceInfo: {
        enough = -1
      } = {}
    } = this.data.orderPriceInfo || {}
    if (enough !== -1) {
      this.setData({
        payType: enough ? 0 : 1
      })
    }
  },

  // 关闭支付弹窗
  closePayPopup() {
    this.setData({
      hasPayPopup: false
    })
  },

  /**
   * 取消订单按钮点击
   */
  cancelBtnHandle() {
    this.popModal({
      currentTarget: {
        dataset: {
          modalflag: 'chanel'
        }
      }
    })
  },

  /**
   * 确认收货按钮点击
   */
  confirmReceiveBtnHandle() {
    let modalflag

    //  自提单
    if (this.data.deliveryWay === '1') {
      modalflag = 'pickup'
    }
    //  配送单
    else {
      modalflag = 'confirm'
    }
    this.popModal({
      currentTarget: {
        dataset: {
          modalflag
        }
      }
    })
  },

  openRefundDialog() {
    this.setData({
      refundInfoModalVisible: true
    })
  },

  refundInfoModalConfirm() {
    this.setData({
      refundInfoModalVisible: false
    })
  },

  // popModal 打开弹窗
  popModal(e) {
    const { modalflag } = e.currentTarget.dataset
    if (modalflag === 'chanel' && !!this.data.isPay) {
      // 已付款取消订单
      this.setData({
        orderCancelIsShow: true
      })
    } else {
      this.operDialog(modalflag, true)
    }
  },
  // 关闭弹窗
  closePopup(e) {
    const { modalflag } = e.currentTarget.dataset
    this.operDialog(modalflag, false)
  },
  // 操作弹窗
  operDialog(type, oper) {
    const DIALOG_TYPE = {
      chanel: 'hasChanelPopup',
      confirm: 'hasConfirmPopup',
      pickup: 'hasPickedUp',
      refund: 'hasRefundPopup'
    }
    const key = DIALOG_TYPE[type]
    key && this.setData({ [key]: oper})
  },
  // 对象是否为空
  checkNullObj(obj) {
    return Object.keys(obj).length === 0
  },
  // 支付按钮
  payBtn() {
    if (!this.data.selectDeliveryTime) {
      this.payHandler()
      return true
    }
    // 处理现售商品超过截单事件的情况
    if (!this.checkNullObj(this.data.goodsList) && !this.data.goodsList.showDeliveryTime) return wx.showToast({
      title: '请选择送达时间',
      icon: 'none'
    })
    // 不会有周期购
    // const selectedpreGoodsList = this.data.preGoodsList.filter(item => {
    //   return item.orderSubType === 'H'
    // })
    // const selectCycleTime = selectedpreGoodsList.every(i => {
    //   return i.showDeliveryTime && i.showDeliveryTime !== ''
    // })
    // if (!selectCycleTime) return wx.showToast({
    //   title: '请选择首次送达时间',
    //   icon: 'none'
    // })

    const getDeliveryTime = (deliveryTime) => {
      const time = deliveryTime.split(' ')
      return {
        deliveryTimeBegin: `${time[0]} ${time[1].split('-')[0]}:00`,
        deliveryTimeEnd: `${time[0]} ${time[1].split('-')[1]}:00`
      }
    }

    // const cycleDeliveryTimeList = selectedpreGoodsList.map(item => {
    //   return {
    //     ...getDeliveryTime(item.deliveryTime),
    //     goodsOrderId: item.goodsOrderID
    //   }
    // })

    const params = {
      // cycleDeliveryTimeList: [],
      payOrderID: this.data.payOrderID,
      customerID: this.data.user.userID,
      deliveryTimeInfo: this.data.goodsList.deliveryTime ? getDeliveryTime(this.data.goodsList.deliveryTime) : null,
    }
    // 更新未支付订单配送时间范围
    app.api.bgxxUpdateDeliveryTime(params).then(res => {
      this.payHandler()
    }).catch(err => this.handleError(err) )
  },
  // 支付处理
  payHandler() {
    const { timeExpire, payType, payOrderNo, payOrderID} = this.data
    const payInfo = this.getPayInfo(timeExpire)
    const params = {
      payOrderNum: payOrderNo,
      payOrderID,
      payAmount: this.initPayAmount,
      ...payInfo
    }
    // console.log('支付信息参数：', params)

    // 0: 余额支付, 1: 微信支付
    const payWay = this.data.payType === 0 ? 'payByGB' : 'payByWechat'
    sensors.safeGuardSensor('pay') // 防黑产埋点
    // (params, sucCallback, failCallback
    this[payWay](params, '', err => {
      if (err.errorCode === 59999) {
        this.deliveryTimeIsLimit({ description: err.description, showModal: true })
        return
      }
      // 56010: 心享配送订单预计送达时间错误，请重新选择
      // 56011 自提订单超过截单时间支付，跳转详情页/刷新详情页
      if (err.errorCode === 56010 || err.errorCode === 56011) {
        common.showModal('提示', err.description, false)
        const selectTime = err.errorCode === 56010
        // 如果是自提订单刷新订单自提时间
        !selectTime && this.getOrderDetailInfo(this.data.orderID, this.data.isPay)
        this.setData({
          hasPayPopup: false,
          selectDeliveryTime: selectTime
        })
        return false
      }
      common.showModal('提示', '支付失败，请重试', false)
      return false
    })
  },
  // 取消未支付订单
  chanelNoPayOrder() {
    this.setData({
      hasChanelPopup: false
    })
    this.chanelNoPayOrderRequest(this.data.user.userID, this.data.payOrderID, () => {
        wx.redirectTo({
          url: `./index?orderID=${this.data.payOrderID}&&isPay=false`,
        })
      }, (err) => {
        common.showModal('提示', err.description, false)
      }
    )
  },
  // 点击确认收货
  confirmPickUp() {
    this.setData({
      hasPickedUp: false
    })
    app.api.bgxxConfirmPickUp(this.data.user.userID, this.data.goodsOrderNum).then(res => {
      wx.redirectTo({
        url: `./index?orderID=${this.data.goodsOrderID}`
      })
    }).catch(err => this.handleError(err) )
  },
  // 确认提货
  confirmOrder() {
    this.setData({ hasConfirmPopup: false })
    app.api.bgxxConfirmReceipt(this.data.user.userID, this.data.goodsOrderNum).then(res => {
      wx.redirectTo({
        url: `./index?orderID=${this.data.goodsOrderID}`,
      })
    }).catch(err => this.handleError(err) )
  },
  // 取消已支付订单
  async orderPayOrder(e) {
    const that = this
    this.setData({ hasChanelPopup: false })
    // 询问用户是否接收订阅消息，只有在用户点击以及支付成功回调中调起，退款通知
    app.requestSubscribeMessage({
      tmplIds: ['jLaaeFpXwM90AMJrCpS63A4A1tBH3zT7tVHleo621gE'],
    },() => {
      that.refundOrder(e)
    })
  },
  refundOrder(e) {
    const { cancelID = '', cancelReason = '' } = e.detail || {}
    const refundChannel = this.data.channel.indexOf('gb') > -1 ? 1 : 2
    const payParams = {
      type: 3,
      customerId: this.data.user.userID,
      goodsOrderId: this.data.goodsOrderID,
      refundChannel: refundChannel,
      refundFee: parseInt(this.data.orderPriceInfo.payAmount * 100),
      requester: 1,
      cancelID,
      subReason: cancelReason
    }
    app.api.bgxxRefundOrder(payParams).then(res => {
      wx.showToast({
        title: '取消订单成功',
        icon: 'none',
        duration: 500
      })
      setTimeout(() => {
        wx.redirectTo({
          url: `./index?orderID=${this.data.goodsOrderID}`,
        })
      }, 500)
    }).catch(err => {
      this.onOrderCancelClosed()
      if(err.errorCode === 55439){
        this.setData({
          showCancelTips:true
        })
      }else{
        common.showModal('提示', err.description, false)
      }

    })
  },

  /**
   * 次日达删除订单
   */
  async deleteBtnHandle() {
    const orderDetail = this.data.orderDetail
    const showModalRes = await app.showModalPromise({
      title: '确认删除订单吗？',
      content: '订单删除后您无法查看该笔订单，也无法对该笔订单开具发票和评价。',
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消'
    })
    //  弹窗取消，此处可以进行行为上报
    if (!showModalRes) {
      return
    }

    const {
      user: { userID } = {}
    } = this.data


    const data = {
      /**
       * 因为后端没有返回该订单的订单类型，所以
       * orderType此处写死为50，对应的是serverless中的常量类型
       */
      orderType: 50,
      customerID: userID,
      //  订单详情分为支付与未支付两个接口。而且订单id字段与取值方式完全不一样
      orderNoList: this.data.isPay ? [orderDetail.goodsOrderID] : orderDetail.goodsOrderList.map(order => order.goodsOrderID)
    }

    try {
      await app.api.updateUserHide(data);

      const pages = getCurrentPages();
      const orederListPageName = 'userB/pages/orderList/index';
      const prevPage = pages[pages.length - 2]
      //  上个页面为订单列表页
      if (prevPage.route === orederListPageName) {
        prevPage.setData({ mix_updateOrder_singleUpdateDelete: true })
        wx.navigateBack();
      } else {
        // 去订单列表页
        wx.redirectTo({
          url: `/${orederListPageName}?type=E`
        });
      }
    } catch (error) {
      wx.showModal({
        content: '订单删除失败'
      })
    }
  },

  /**
   * 分发按钮点击事件
   * @param {*} event
   */
  handleBtn(event) {
    const type = event.target.dataset.type
    this[`${type}Handle`]()
  },

  /**
   * 重新购买按钮点击
   */
  buyAgainBtnHandle() {
    const { isPay, goodsOrderID, payOrderID, goodsList, preGoodsList = [] } = this.data
    const { list = [] } = goodsList || {}
    const { goodsList:preList = [] } = preGoodsList[0] || {}
    // type类型：1为需要根据旧电商商品id来查询商品编码，2为不需要
    this.bgxxBuyAgain({ isPay, goodsOrderID, payOrderID, goodsList: list.concat(preList), type: 2 })
  },

  /**
   * 再来一单按钮点击
   */
  buyMoreBtnHandle: app.subProtocolValid('shop', function () {
    this.buyAgainBtnHandle()
  }),
  /**
   * 开具发票按钮点击
   */
  invoiceBtnHandle() {
    //  旧逻辑中，开具发票和发票详情一律进入此方法
    this.invoice()
  },
  /**
   * 发票详情按钮点击
   */
  invoiceDetailBtnHandle() {
    //  旧逻辑中，开具发票和发票详情一律进入此方法
    this.invoice()
  },
  // 发票
  invoice() {
    sensors.track('MPClick', 'bgxxOrderDetailInvoice')
    const channel = this.data.orderDetail.channel
    if(channel === 'gb') {
      common.showModal('温馨提示', '暂不支持会员钱包支付订单开票, 可前往在线客服获取开票说明', true, '在线客服', '取消',  res => {
        if (res.confirm === true) {
          app.toOnlineService({ queStr: '发票服务' })
        }
      })
      return
    }
    const invoiceinfo = this.data.invoice.hasInvoiced === 'Y' ? 'invoiceDetail' : 'applyInvoice'
    const order = this.data.invoice.hasInvoiced === 'Y' ? '' : 'orderDetail'
    const {totalRefundAmount,payAmount} = this.data.orderDetail
    const newAmount = totalRefundAmount ?  payAmount - totalRefundAmount:payAmount
    const url = `/bgxxUser/pages/invoice/${invoiceinfo}/${invoiceinfo}?orderID=${this.data.goodsOrderID}&fromPage=${order}&invoiceAmount=${(newAmount / 100).toFixed(2)}`
    wx.navigateTo({ url })
  },

  // 倒计时
  countDown(conutTome) {
    const that = this
    if (this.data.stopCount) return true
    if (conutTome >= 0) {
      let min, second
      min = Math.floor(conutTome / 1000 / 60 % 60);
      second = Math.floor(conutTome / 1000 % 60);
      if (min < 10) {
        min = `0${min}`
      }
      if (second < 10) second = `0${second}`
      that.setData({
        min,
        second
      })
      conutTome = conutTome - 1000;
      setTimeout(() => {
        that.countDown(conutTome)
      }, 999)
    } else if (conutTome < 0) {
      common.showModal('提示', '超时未支付,已为您取消订单', false, '', '', () => {
        this.setData({
          stopCount: true,
          selectDeliveryTime: false
        })
        wx.redirectTo({
          url: `/bgxxUser/pages/orderDetail/index?orderID=${that.data.orderID}&isPay=${that.data.isPay}`,
        })
      })
    }
  },

  /**
   * 不好就退按钮点击
   */
  returnGoodsBtnHandle: app.subProtocolValid('shop', function () {
    wx.navigateTo({
      url: `/bgxxUser/pages/complaints/refundGoods/index?complaintType=1&goodsOrderID=${this.data.goodsOrderID}&goodsOrderNum=${this.data.goodsOrderNum}&contact=${this.data.orderStoreInfo.storePhone}`
    })
  }),
  navigate({detail}) {
    wx.navigateTo({
      url: `/bgxxShop/pages/goodDetail/index?toDetail=${JSON.stringify({ goodsID: detail.item.goodsID })}`
    })
  },
  /**
   * 选择配送时间
   */
  pickerOnSaleDelivery (e) {
    const { item: deliveryTimeRange } = e.currentTarget.dataset
    const { onSalePicker } = this._data
    const parent = this.genPickerTimeParentArray(deliveryTimeRange)
    const children = this.genPickerTimeChildrenArray(deliveryTimeRange)

    // 第一列默认第一项，第二列默认不选择
    const deliveryTimePickerSeleted = onSalePicker.length ? onSalePicker : [0, null]
    this.setData({
      deliveryTimePickerSeleted,
      customPickerVisible: true,
      deliveryTimePickerArray: [parent, children]
    })
    this._data.currentSubOrderSelected = 'onSale'
  },
  pickerPreSaleDelivery (e) {
    const { item: deliveryTimeRange, index } = e.currentTarget.dataset
    const { preSalePicker } = this._data
    const parent = this.genPickerTimeParentArray(deliveryTimeRange)
    const children = this.genPickerTimeChildrenArray(deliveryTimeRange)
    // 第一列默认第一项，第二列默认不选择
    const deliveryTimePickerSeleted = (preSalePicker[`preSale_${index}`] && preSalePicker[`preSale_${index}`].length) ? preSalePicker[`preSale_${index}`] : [0, null]
    this.setData({
      deliveryTimePickerSeleted,
      customPickerVisible: true,
      deliveryTimePickerArray: [parent, children]
    })
    this._data.currentSubOrderSelected = `preSale_${index}`
  },
  /**
   * @desc 匹配自定义Picker组件字段
   */
  genPickerTimeParentArray (deliveryTimeRange) {
    const parentPickerArray = deliveryTimeRange[0]
    return parentPickerArray.map(parent => {
      return {
        splitDate: parent.showTime
      }
    })
  },
  /**
   * @desc 匹配自定义Picker组件字段
   * @param {Array<Array<object>, Array<object>>} deliveryTimeRange
   * @param {number} currentParent 默认为1
   */
  genPickerTimeChildrenArray (deliveryTimeRange, currentParent = 0) {
    const limitTimeList = this._data.deliveryLimitList.reduce((acc, cur) => {
      if (!cur.isFull) return acc
      // "2022-04-16 09:00:00"
      acc.push(cur.date)
      return acc
    }, [])
    const [ parentPickerArray, childPikcerArray ] = deliveryTimeRange
    return childPikcerArray.map(child => {
      // "09:00-10:00" => "09:00:00"
      let startTime = `${child.showTime.split('-')[0]}:00`
      // 组装成"2022-04-16 09:00:00"
      startTime = `${parentPickerArray[currentParent].time} ${startTime}`
      const disabled = limitTimeList.includes(startTime)
      return {
        dateDesc: child.showTime,
        labelType: disabled ? 'CIRIDA_DELIVERY_IS_FULL' : '',
        disabled
      }
    })
  },

  /**
   * 改变Picker日期触发。现货、预售类型可选日期只会有一天，所以不用改变时间
   */
  handleCustomerPickerChangeDate () {},

  /**
   * 现售只会有一个子单,预售可能会有多个子单，每个子单都可以单独选择时间
   * @param {*} e
   */
  handleCustomPickerPick (e) {
    const { detail: { current } } = e
    let deliveryTimeRange = []
    const { currentSubOrderSelected } = this._data
    const [saleTag, index] = currentSubOrderSelected.split('_')
    const isOnSale = saleTag === 'onSale'

    let deliveryTimeKey = ''
    let showDeliveryTimeKey = ''
    if (isOnSale) {
      deliveryTimeKey = 'goodsList.deliveryTime'
      showDeliveryTimeKey = 'goodsList.showDeliveryTime'
      deliveryTimeRange = this.data.goodsList.deliveryTimeRange
      this._data.onSalePicker = current
    }
    // else {
    //   deliveryTimeKey = `preGoodsList[${index}].deliveryTime`,
    //   showDeliveryTimeKey = `preGoodsList[${index}].showDeliveryTime`
    //   deliveryTimeRange = this.data.preGoodsList[index].deliveryTimeRange
    //   this._data.preSalePicker[`preSale_${index}`] = current
    // }

    const time = deliveryTimeRange[0][current[0]].time,
      point = deliveryTimeRange[1][current[1]].showTime

    // 记录选择的Picker索引

    this.setData({
      [showDeliveryTimeKey]: `预计${time.split('-')[1]}-${time.split('-')[2]} ${point}送达`,
      [deliveryTimeKey]: time + ' ' + point,
      customPickerVisible: false
    })

  },
  /**
   * 申请售后按钮点击
   */
  salesServiceBtnHandle() {
    const { goodsOrderID, goodsOrderNum, orderDetail = {}, goodsList = {} } = this.data
    const channel = orderDetail.channel || ''
    const list = goodsList.list || []
    const goodsName = list.map(item => item.goodsName).join(',')
    const goodsId = list.map(item => item.goodsID).join(',')
    const sensorData = { goodsName, goodsId}
    this.toPostSale(this.initPayAmount, goodsOrderID, goodsOrderNum, channel, sensorData) // 防黑产埋点
  },
  // 前往售后详情
  goRefundDetail() {
    const { goodsOrderID } = this.data
    wx.navigateTo({
      url: `/bgxxUser/pages/complaints/complaintsDetail/index?goodsOrderID=${goodsOrderID}`,
    })
  },
  onOrderCancelSelected(e) {
    this.orderPayOrder(e)
  },
  onOrderCancelClosed() {
    this.setData({
      orderCancelIsShow: false
    })
  },
  // 前往交易明细页面
  goBaldetail () {
    wx.navigateTo({
      url: '/userA/pages/baldetail/index'
    })
  },
  /**
   * 他人代领按钮点击
   */
  otherReceiveBtnHandle() {
    if (this.data.showDeliveryInfo && this.data.showShareBtn) {
      this.showShareHandle({
        currentTarget: { dataset: { type: '1' } }
      })
    } else if(this.data.showDeliveryPoster) {
      this.showShareHandle({
        currentTarget: { dataset: { type: '2' } }
      })
    }
  },
  showShareHandle(e) {
    const { type } = e.currentTarget.dataset
    sensors.track('MPClick', 'bgxxOrderDetailShareFamily')
    if (!wx.showShareImageMenu) {
      this.setData({
        showSharePop: true
      })
    }
    type === '1'&& this.createShareImg()
    type === '2'&& this.createShareImgToFriend()
  },
  /**
   * 他人代领——分享给家人代领
   */
  createShareImg() {
    const subStr = (value, maxLen = 15) => {
      if (value.length <= maxLen) {
        return value
      }
      return `${value.substr(0, maxLen)}...`
    }
    wx.showLoading({
      title: '图片正在生成'
    })
    const { storeName, address, openingTime, storePhone,storeBusinessTime } = this.data.orderStoreInfo
    const { pickUpTime, takeCode, serialNumber } = this.data.pickUpOrderInfo

    const canvasCtx = wx.createCanvasContext('shareCanvas')
    canvasCtx.scale(2, 2)
    const ctxWidth = 550 / 2

    const tipHeight = 16;//增加购物袋提示的高度
    const goodsInitTop = 350+tipHeight, goodsInstance = 10, goodsHeight = 15 // 第一个商品绘制top，商品上下之间的间距，一行商品展示高度
    let lastGoodsTop = 0 // 最后一个商品绘制top
    if (this.data.goodsList.list.length > 0) {
      lastGoodsTop = goodsInitTop + goodsInstance * (this.data.goodsList.list.length - 1) + goodsHeight * (this.data.goodsList.list.length - 1)
    }
    const iconHeight = 32, iconTop = lastGoodsTop + goodsHeight  // 三无退icon高度及top
    this.setData({
      shareCanvasHeight: (iconTop + iconHeight + 5) * 2
    })

    // 绘制白色背景
    canvasCtx.setFillStyle('#ffffff')
    canvasCtx.fillRect(0, 0, ctxWidth, this.data.shareCanvasHeight / 2)

    // 绘制顶部背景说明
    canvasCtx.drawImage('../../source/images/order_detail_share.png', 0, 0, ctxWidth, 66)

    // 提货门店信息
    canvasCtx.setFillStyle('#222222')
    canvasCtx.setFontSize(12)
    canvasCtx.fillText(subStr(storeName, 16), 10, 80)
    canvasCtx.setFillStyle('#000000')
    canvasCtx.setFontSize(10)
    canvasCtx.fillText(subStr(address, 20), 10, 102)
    canvasCtx.setFontSize(10)
    canvasCtx.setFillStyle('#555555')
    canvasCtx.fillText('门店营业时间：', 10, 123)
    canvasCtx.setFillStyle('#222222')
    canvasCtx.fillText(`${storeBusinessTime}`, 85, 123)
    canvasCtx.setFillStyle('#555555')
    canvasCtx.fillText('门店联系电话：', 10, 145)
    canvasCtx.setFillStyle('#222222')
    canvasCtx.fillText(`${storePhone}`, 85, 145)

    // 背景分割线
    canvasCtx.drawImage('../../source/images/bg_colorline.png', 0, 163, ctxWidth, 1)

    // 提货码信息
    canvasCtx.setFillStyle('#222222')
    canvasCtx.setTextAlign('center')
    canvasCtx.setFontSize(12)
    canvasCtx.fillText(`提货时间: ${pickUpTime}`, ctxWidth / 2, 188)

    //购物袋提示
    canvasCtx.setFillStyle('#008C3D')
    canvasCtx.setTextAlign('center')
    canvasCtx.setFontSize(8)
    canvasCtx.fillText('助力环保，自提请您自备购物袋，或到店购买可降解塑料袋。', ctxWidth / 2, 188+tipHeight)

    const codeWidth = 255 // 条形码宽度
    const codeLeft = (ctxWidth - codeWidth) / 2 // 条形码绘制左边距离
    canvasCtx.drawImage(this.data.deliveryCanvasImage, codeLeft, 199+tipHeight, codeWidth, 60)
    canvasCtx.setTextAlign('left')
    canvasCtx.setFontSize(11)
    canvasCtx.setFillStyle('#222222')
    canvasCtx.fillText(`提货码：${takeCode}`, codeLeft + 5, 278+tipHeight)
    canvasCtx.fillText(`流水号：${serialNumber}`, ctxWidth - canvasCtx.measureText(`流水号：${serialNumber}`).width - (codeLeft + 5), 278+tipHeight)

    // 分割线
    canvasCtx.moveTo(0, 298+tipHeight)
    canvasCtx.lineTo(ctxWidth, 298+tipHeight)
    canvasCtx.setStrokeStyle('#F2F2F2')
    canvasCtx.stroke()

    // 商品清单
    canvasCtx.setFillStyle('#222222')
    canvasCtx.setFontSize(13)
    canvasCtx.fillText('商品信息', 10, 320+tipHeight)
    canvasCtx.fillText(`共 ${this.data.totalCount} 件商品`, ctxWidth - canvasCtx.measureText(`共 ${this.data.totalCount} 件商品`).width - 10, 320+tipHeight)

    if (this.data.goodsList.list.length > 0) {
      this.data.goodsList.list.forEach((item, index) => {
        const currTop = goodsInitTop + goodsInstance * index + goodsHeight * index
        canvasCtx.setFontSize(12)
        canvasCtx.fillText(subStr(item.goodsName, 16), 10, currTop)
        canvasCtx.fillText(`x${item.count}`, ctxWidth - canvasCtx.measureText(`x${item.count}`).width - 10, currTop)
      })
    }

    // 三无退icon
    const iconWidth = ctxWidth
    canvasCtx.drawImage('../../source/images/order_detail_slogan.png', (ctxWidth - iconWidth) / 2, iconTop, iconWidth, iconHeight)

    canvasCtx.draw()

    const that = this
    // canvas 画布转为图片
    setTimeout(function () {
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: ctxWidth * 2,
        height: that.data.shareCanvasHeight,
        quality: 1,
        destWidth: ctxWidth * 2,
        destHeight: that.data.shareCanvasHeight,
        canvasId: 'shareCanvas',
        success: function (res) {
          wx.hideLoading()
          if (!wx.showShareImageMenu) {
            that.setData({
              shareImage: res.tempFilePath
            })
          } else {
            wx.showShareImageMenu({
              path: res.tempFilePath,
              fail:(err)=>{
                if(err.errMsg !=='showShareImageMenu:fail cancel'){
                  common.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了', '', function (res) {
                    if (res.confirm) {
                      wx.openSetting()
                    }
                  })
                }
              }
            })
          }
        },
        fail: function () {
          wx.hideLoading()
        }
      })
    }, 300)
  },
  /**
   * 他人代领——分享给朋友
   */
  createShareImgToFriend() {
    wx.showLoading({
      title: '图片正在生成'
    })
    // 先将网络图片缓存到本地
    const p = []
    this.data.goodsList.list.forEach((item,index)=>{
      // 如果有缓存图片则不在进行缓存操作
      if(!item.localHeadPic){
        p[index] = this.getCanvasImage(this.data.BGXX_PIC_DOMAIN + item.headPic,item.goodsID)
      }
    })
    Promise.all(p).then(() => {
      this.drawPoster()
    }).catch( () => {
      wx.showToast({
        icon:'none',
        title: '图片生成失败，请稍后重试',
      })
    })
  },
  drawPoster(){
    const subStr = (value, maxLen = 15) => {
      if (value.length <= maxLen) {
        return value
      }
      return `${value.substr(0, maxLen)}...`
    }
    const { storeName, storePhone } = this.data.orderStoreInfo //门店地址，电话
    const { deliverySection,deliveryTimeBegin='',deliveryTimeEnd='' } = this.data.orderStatusInfo //配送时间
    const canvasCtx = wx.createCanvasContext('shareCanvas')
    canvasCtx.scale(2, 2)
    const ctxWidth = 550 / 2
    const addressWidth = 220 //地址换行高度
    const goodsInitTop = canvasCtx.measureText(this.data.receiverAddressInfo.address).width/addressWidth > 1 ? 211:196 //第一个商品绘制top
    const goodsInstance = 10, goodsHeight = 60 //商品上下之间的间距，一行商品展示高度
    const goodsNum = this.data.goodsList.list.length //商品数量
    let lastGoodsTop = 0 // 最后一个商品绘制top
    if (goodsNum > 0) {
      lastGoodsTop = goodsInitTop + goodsInstance * (goodsNum - 1) + goodsHeight * (goodsNum - 1)
    }
    // 计算订单信息部分的高度 start
    const couponList = this.data.orderPriceInfo.couponList //优惠券信息
    lastGoodsTop += 32 //订单信息栏高度
    const orderInfoHeight = 18 //订单信息每条高度
    lastGoodsTop += orderInfoHeight*2 //商品总额+实付金额高度
    if(Number(this.data.deliveryWay) === 2 && this.data.orderPriceInfo.packAmount > 0 ){// 包装费
      lastGoodsTop += orderInfoHeight
    }
    if(Number(this.data.deliveryWay) === 2){// 配送费
      lastGoodsTop += orderInfoHeight
    }
    if(this.data.orderPriceInfo.newVipDiscountAmount){//新客瞒折
      lastGoodsTop += orderInfoHeight
    }
    if(couponList.length>0){//优惠券信息
      lastGoodsTop += orderInfoHeight *couponList.length
    }
    if(this.data.showActivityAmount && this.data.orderPriceInfo.totalOneBuyActivityPrice){//一元购优惠金额
      lastGoodsTop += orderInfoHeight
    }
    // 计算订单信息部分的高度 end
    const iconHeight = 32, iconTop = lastGoodsTop + goodsHeight  // 三无退icon高度及top
    this.setData({
      shareCanvasHeight: (iconTop + iconHeight + 16) * 2
    })

    // 绘制白色背景
    canvasCtx.setFillStyle('#ffffff')
    canvasCtx.fillRect(0, 0, ctxWidth, this.data.shareCanvasHeight / 2)

    // 绘制顶部背景说明
    canvasCtx.drawImage('../../source/images/order_detail_share.png', 0, 0, ctxWidth, 66)

    // 提货门店信息
    canvasCtx.setFillStyle('#222222')
    canvasCtx.setFontSize(12)
    canvasCtx.font = 'normal bold 12px Arial, sans-serif'
    canvasCtx.fillText(subStr(storeName, 16), 10, 80)
    canvasCtx.font = 'normal 400 10px Arial, sans-serif'
    canvasCtx.setFillStyle('#000000')
    canvasCtx.setFontSize(10)
    // 收货地址是否换行
    let offsetHiehgt = this.drawArticle(canvasCtx, this.data.receiverAddressInfo.address, addressWidth, 10, 100)
    canvasCtx.setFontSize(10)
    canvasCtx.setFillStyle('#555555')
    offsetHiehgt += 18
    canvasCtx.fillText('配送预约时间：', 10, offsetHiehgt)
    canvasCtx.setFillStyle('#222222')
    const sectionTime = `${deliverySection} ${this.formatDeliveryTime(deliveryTimeBegin)}-${this.formatDeliveryTime(deliveryTimeEnd)}`
    canvasCtx.fillText(sectionTime, 78, offsetHiehgt)
    canvasCtx.setFillStyle('#555555')
    offsetHiehgt += 18
    canvasCtx.fillText('门店联系电话：', 10, offsetHiehgt)
    canvasCtx.setFillStyle('#222222')
    canvasCtx.fillText(`${storePhone}`, 78, offsetHiehgt)

    // 背景分割线
    offsetHiehgt += 16
    canvasCtx.drawImage('../../source/images/bg_colorline.png', 0, offsetHiehgt, ctxWidth, 1)

    // 商品清单
    canvasCtx.setFillStyle('#222222')
    canvasCtx.setFontSize(10)
    offsetHiehgt += 20
    canvasCtx.font = 'normal bold 10px Arial, sans-serif'
    canvasCtx.fillText('商品信息', 10, offsetHiehgt)
    canvasCtx.font = 'normal 400 10px Arial, sans-serif'

    // 分割线
    offsetHiehgt += 12
    canvasCtx.beginPath()
    canvasCtx.setLineWidth(0.5)
    canvasCtx.moveTo(0, offsetHiehgt)
    canvasCtx.lineTo(ctxWidth, offsetHiehgt)
    canvasCtx.setStrokeStyle('#F2F2F2')
    canvasCtx.stroke()
    // 绘制商品信息
    if (goodsNum > 0) {
      this.data.goodsList.list.forEach((item, index) => {
        const isOneBuy = item.activityInfo && (Number(item.count) === Number(item.activityInfo.enjoyOneBuyNum))//是否一元购
        const isGift = item.isGift === 'Y'//是否赠品
        offsetHiehgt = goodsInitTop + goodsInstance * index + goodsHeight * index
        // 商品名
        let giftWdith = 0
        if(isGift){//赠品加上赠品标签
          giftWdith = 20
          canvasCtx.drawImage('../../source/images/icon_gift.png', 67, offsetHiehgt, 18, 9)
        }
        canvasCtx.setFillStyle('#222222')
        canvasCtx.setFontSize(10)
        canvasCtx.fillText(subStr(item.goodsName, 15), 67+giftWdith, offsetHiehgt+8)
        // 副标题
        canvasCtx.setFillStyle('#666666')
        canvasCtx.setFontSize(8)
        canvasCtx.fillText(subStr(item.spec||'', 15), 67, offsetHiehgt+22)
        // 实际交易价
        let totalPrice
        // 一元购
        if(isOneBuy){
          totalPrice = util.formatPrice(item.activityInfo.price*item.activityInfo.enjoyOneBuyNum)
        }
        // 一元购
        if(!isOneBuy){
          totalPrice = util.formatPrice(((item.bidPrice || Number(item.bidPrice) === 0) ? item.bidPrice : item.price) * item.count)
        }
        // 赠品
        if(isGift){
          totalPrice = 0
        }
        canvasCtx.setTextAlign('right')
        canvasCtx.setFillStyle('#000000')
        canvasCtx.setFontSize(10)
        canvasCtx.fillText(`￥${totalPrice}`, ctxWidth - 10, offsetHiehgt+8)
        // 划线价 (一元购 || 赠品)
        let originPrice = ''
        if(isOneBuy||isGift){
          // 原价
          originPrice = util.formatPrice(((item.bidPrice || Number(item.bidPrice) === 0) ? item.bidPrice : item.price) * item.count)
          canvasCtx.setFillStyle('#666666')
          canvasCtx.setFontSize(8)
          canvasCtx.fillText(`￥${originPrice}`, ctxWidth - 10, offsetHiehgt+22)
          // 下划线
          canvasCtx.beginPath()
          canvasCtx.moveTo(ctxWidth-canvasCtx.measureText(`x${originPrice}`).width-12, offsetHiehgt+19)
          canvasCtx.lineTo(ctxWidth-8, offsetHiehgt+19)
          canvasCtx.setStrokeStyle('#666666')
          canvasCtx.stroke()
        }
        // 重量
        const isAddWeight = this.getWeightField(item)
        if(isAddWeight){
          const weight = (item.weight*item.count) / 1000
          const addHeight = !originPrice ? 22 : 34
          canvasCtx.setFillStyle('#666666')
          canvasCtx.setFontSize(8)
          canvasCtx.fillText(`共${weight}kg`, ctxWidth - 10, offsetHiehgt+addHeight)
        }
        // 商品单价&数量
        canvasCtx.setTextAlign('left')
        const singlePrice = util.formatPrice((item.bidPrice || Number(item.bidPrice) === 0) ? item.bidPrice : item.price)
        const singleStr =   `单价：￥${singlePrice}  数量： ${item.count}`
        canvasCtx.setFillStyle('#888888')
        canvasCtx.setFontSize(8)
        canvasCtx.fillText(singleStr, 67, offsetHiehgt+45)
        // 商品图
        canvasCtx.drawImage(item.localHeadPic||'', 10, offsetHiehgt, 50, 50)
        // 分割线
        canvasCtx.beginPath()
        canvasCtx.setLineWidth(0.5)
        canvasCtx.moveTo(0, offsetHiehgt+goodsHeight)
        canvasCtx.lineTo(ctxWidth, offsetHiehgt+goodsHeight)
        canvasCtx.setStrokeStyle('#f2f2f2')
        canvasCtx.stroke()
      })
    }
    // 订单信息
    canvasCtx.setTextAlign('left')
    offsetHiehgt += goodsHeight //加上最后一列的高度
    canvasCtx.setFillStyle('#222222')
    canvasCtx.setFontSize(10)
    offsetHiehgt += 18
    canvasCtx.font = 'normal bold 10px Arial, sans-serif'
    canvasCtx.fillText('订单信息', 10, offsetHiehgt)
    canvasCtx.font = 'normal 400 10px Arial, sans-serif'
    offsetHiehgt += 12
    canvasCtx.moveTo(0, offsetHiehgt)
    canvasCtx.lineTo(ctxWidth, offsetHiehgt)
    canvasCtx.setStrokeStyle('#F2F2F2')
    canvasCtx.stroke()
    // 商品总额
    offsetHiehgt += 18
    canvasCtx.setFillStyle('#222222')
    canvasCtx.setFontSize(10)
    canvasCtx.fillText('商品总额', 12, offsetHiehgt)
    const totalAmount = `￥${this.data.orderPriceInfo.totalAmount}`
    canvasCtx.fillText(totalAmount, ctxWidth - canvasCtx.measureText(totalAmount).width - 10, offsetHiehgt+2)
    // 包装费
    if(this.data.orderPriceInfo.packAmount > 0 ){
      offsetHiehgt += 18
      canvasCtx.fillText('包装费', 12, offsetHiehgt)
      const packAmount = `￥${this.data.orderPriceInfo.packAmount}`
      canvasCtx.fillText(packAmount, ctxWidth - canvasCtx.measureText(packAmount).width - 10, offsetHiehgt+2)
    }
    // 配送费
    if(Number(this.data.deliveryWay) === 2){
      offsetHiehgt += 18
      canvasCtx.fillText(`配送费(总重${this.data.orderPriceInfo.weightCount}kg)`, 12, offsetHiehgt)
      const freight = `￥${this.data.orderPriceInfo.freight}`
      canvasCtx.fillText(freight, ctxWidth - canvasCtx.measureText(freight).width - 10, offsetHiehgt+2)
    }
    // 优惠券
    if(couponList.length>0){
      couponList.forEach(item=>{
        offsetHiehgt += 18
        canvasCtx.fillText(item.couponName, 12, offsetHiehgt)
        canvasCtx.fillText(`-￥${item.couponValue}`, ctxWidth - canvasCtx.measureText(`-￥${item.couponValue}`).width - 10, offsetHiehgt+2)
      })
    }
    // 优惠金额(新客满折)
    if(this.data.orderPriceInfo.newVipDiscountAmount){
      offsetHiehgt += 18
      canvasCtx.fillText('新客满折', 12, offsetHiehgt)
      const newVipDiscountAmount = `-￥${this.data.orderPriceInfo.newVipDiscountAmount}`
      canvasCtx.fillText(newVipDiscountAmount, ctxWidth - canvasCtx.measureText(newVipDiscountAmount).width - 10, offsetHiehgt+2)
    }
    // 一元购优惠金额
    if(this.data.showActivityAmount && this.data.orderPriceInfo.totalOneBuyActivityPrice){
      offsetHiehgt += 18
      canvasCtx.fillText('优惠活动', 12, offsetHiehgt)
      const totalOneBuyActivityPrice = `-￥${this.data.orderPriceInfo.totalOneBuyActivityPrice}`
      canvasCtx.fillText(totalOneBuyActivityPrice, ctxWidth - canvasCtx.measureText(totalOneBuyActivityPrice).width - 10, offsetHiehgt+2)
    }

    // 实际付款
    offsetHiehgt += 18
    const payAmount = `￥${this.data.orderPriceInfo.payAmount}`
    canvasCtx.fillText('实付:', ctxWidth - canvasCtx.measureText(payAmount).width-canvasCtx.measureText('实付:').width - 14, offsetHiehgt+2)
    canvasCtx.setFontSize(11)
    canvasCtx.setFillStyle('#FD6E82')
    canvasCtx.fillText(payAmount, ctxWidth - canvasCtx.measureText(payAmount).width - 10, offsetHiehgt+2)
    // 三无退icon
    offsetHiehgt += 16
    const iconWidth = ctxWidth
    canvasCtx.drawImage('../../source/images/order_detail_slogan.png', 0, offsetHiehgt, iconWidth, iconHeight)

    canvasCtx.draw()

    const that = this
    // canvas 画布转为图片
    setTimeout(function () {
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: ctxWidth * 2,
        height: that.data.shareCanvasHeight,
        quality: 1,
        destWidth: ctxWidth * 2,
        destHeight: that.data.shareCanvasHeight,
        canvasId: 'shareCanvas',
        success: function (res) {
          wx.hideLoading()
          if (!wx.showShareImageMenu) {
            that.setData({
              shareImage: res.tempFilePath
            })
          } else {
            wx.showShareImageMenu({
              path: res.tempFilePath,
              fail:(err)=>{
                if(err.errMsg !=='showShareImageMenu:fail cancel'){
                  common.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了', '', function (res) {
                    if (res.confirm) {
                      wx.openSetting()
                    }
                  })
                }
              }
            })
          }
        },
        fail: function () {
          wx.hideLoading()
        }
      })
    }, 300)

  },
  // 处理重量字段判断-降低圈复杂度
  getWeightField(item){
    return !(item.isGift === 'Y' || (item.activityInfo && item.isGift === 'N' && Number(item.count) === Number(item.activityInfo.enjoyOneBuyNum)))
  },
  // 处理时间，获取时分秒
  formatDeliveryTime(date){
    if(!date) return ''
    var arr = date.split(' ')
    var time = arr[1] || ''
    if (time.length === 8) time = time.slice(0,-3)
    return time
  },
  // 绘制文字自动换行
  drawArticle(ctx, item, width, x, y) {
    const chr = item.split('')
    let temp = ''
    const row = []
    // 超过设置宽度换行
    for (let a = 0; a < chr.length; a++) {
      if (ctx.measureText(temp).width < width) {
        temp += chr[a]
      } else {
        a--
        row.push(temp)
        temp = ''
      }
    }
    row.push(temp)
    // 超过两行显示...
    if(row.length>2){
      row[1] = `${row[1]}...`
    }
    for (var b = 0; b < row.length; b++) {
      // 控制最多显示2行
      if (b < 2) {
        ctx.fillText(row[b], x, y + b * 15);
      }
    }
    const _len = row.length > 2 ? 2 : row.length
    return y + (_len - 1) * 15
  },
  // 获取图片的本地路径
  getCanvasImage(url,id) {
    const that = this
    const list = that.data.goodsList.list
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: url, //请求的网络图片路径
        success: function (res) {
          const newArr = list.map(item=>{
            if(item.goodsID===id){
              item.localHeadPic = res.path
            }
            return item
          })
          that.setData({
            'goodsList.list':newArr
          })
          resolve(res.path)
        },
        fail: function () {
          reject('')
        }
      })
    })
  },
  closeSharePop() {
    this.setData({
      showSharePop: false
    })
  },
  saveSharePoster() {
    if (this.data.shareImage) {
      const that = this
      wx.showLoading({
        title: '正在保存图片',
        mask: true
      })
      wx.saveImageToPhotosAlbum({
        filePath: this.data.shareImage,
        success: () => {
          wx.hideLoading()
          wx.showToast({
            title: '保存成功，快去分享吧~',
            icon: 'none'
          })
          that.closeSharePop()
        },
        fail: () => {
          wx.hideLoading()
          common.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了', '', function (res) {
            if (res.confirm) {
              wx.openSetting()
            }
          })
        }
      })
    }
  },
  preventEvent() {},
  // 子组件事件：获取自定义导航栏高度
  getNavBarHeight (ev) {
    // 这里的高度单位是px
    this.setData({
      navBarHeight: ev.detail
    })
  },
  /**
   * 展示（关闭）费用弹窗说明
   */
  showCostTips(){
    this.setData({
      showCostBox: !this.data.showCostBox
    })
  },
  /**
   * 取消订单失败说明
   */
  showCancelTips(e){
    const type = e.currentTarget.dataset.type
    if(Number(type)===1){
      wx.makePhoneCall({
        phoneNumber: '400-1811-212',
        complete:()=>{
          this.setData({
            showCancelTips:false
          })
        }
      })
    }else{
      this.setData({
        showCancelTips:false
      })
      this.getOrderDetailInfo(this.data.orderID, this.data.isPay)
    }
  },
  startShowPop(){
    this.queryElement()
    if(this.data.showPopTip)return
    this.setData({
      showPopTip:true
    })
    setTimeout(()=>{
      this.setData({
        showPopTip:false
      })
    },2000)
    // let animate =  wx.createAnimation();
    //  animate.opacity(1).step()
    //  animate.opacity(0).step({
    //   delay:5000,
    //   duration:10,
    //   timingFunction:'ease-out'
    //  })
    //   this.setData({
    //     animationData:animate.export()
    //   })
  },
  queryElement(){
    const query = wx.createSelectorQuery(this)
        query.select('#invoiceBtn').boundingClientRect(res=>{
          // if(!res){return};
          const {top,left,width} = res;
          console.log('res')
          console.log(res,top,left,width)
          console.log('res')
          // if( !!top || !!left|| !!width)return
          this.setData({
            leftDistance:left+width/3 + 'px',
            toastDistanceBottom: `${app.globalData.screenHeight - top}px`
          })
        }).exec()
  },
  /**
   * 展示（关闭）心享会员节省明细弹窗
   */
  showVipDetail() {
    this.setData({
      showVipDetail: !this.data.showVipDetail
    })
  },
  /**
   *
   * @returns { Array<string> } 每个子单的提货日期
   */
  filterDeliveryTime () {
    // 接口返回的delivertTime字段在组装数据过程中，被覆盖了，所以有两种形式
    // "2022-04-28"
    // "2022-04-30 11:00-12:00"
    // 入参需要的是 "2022-04-28"，所以split
    // const beginTakeTimeList = this.data.preGoodsList.reduce((acc, cur) => {
    //   acc.push(cur.deliveryTime.split(' ')[0])
    //   return acc
    // }, [])
    const beginTakeTimeList = []
    this.data.goodsList.deliveryTime && beginTakeTimeList.push(this.data.goodsList.deliveryTime.split(' ')[0])
    return [...new Set(beginTakeTimeList)]
  },
  async getDeliveryLimitInfo () {
    try {
      const beginTakeTimeList = this.filterDeliveryTime()
      const { selectStoreInfo } = wx.getStorageSync('bgxxSelectLocateInfo') || {}
      const { storeID } = selectStoreInfo || {}
      if (!storeID) return
      const { data: deliveryLimitList } = await app.api.bgxxQueryOrderLimitInfo({
        storeID,
        beginTakeTimeList
      })
      this._data.deliveryLimitList = deliveryLimitList
    } catch (error) {
      this._data.deliveryLimitList = []
      console.log('getDeliveryLimitInfo error', error);
    }
  },
  /**
   * @desc 清空超出截单时间的子单的选项，并返回超出截单时间的第一个子单列表
   * 筛选 goodsList.deliveryTime 和 preGoodsList[i].deliveryTime =>  "2022-10-31 10:00-11:00"
   * deliveryLimitList => {count: 0, date: "2022-10-31 15:00:00", isFull: 0, limit: 10}  isFull: 1（已约满）
   */
  resetSelectedTime () {

    const { deliveryLimitList, deliveryTimeMap } = this._data
    const { goodsList, preGoodsList } = this.data
    // deliveryTimeBegin

    let firstToPicker = ''
    // 没有截单时间信息，不操作
    if (!deliveryLimitList.length) return
    // 有现售
    if (goodsList && goodsList.list.length) {
      const onSaleDeliveryTime = deliveryTimeMap.onSale
      console.log('onSaleDeliveryTime', onSaleDeliveryTime);
      const { isFull } = (deliveryLimitList.find(el => el.date === onSaleDeliveryTime) || { isFull: 0 })
      if (isFull) {
        // 清空对应的时间选项
        const key1 = 'goodsList.deliveryTime'
        const key2 = 'goodsList.showDeliveryTime'
        this.setData({
          [key1]: '',
          [key2]: ''
        })
        this._data.onSalePicker = []
        firstToPicker = 'onSale'
      }
    }

    // 预售，无论是否到达阈值，都不清空预售的时间选项和弹出关于预售的时间弹窗。因为预售不支持修改
    if (!firstToPicker) return
    // 预售不弹窗
    if (firstToPicker === 'onSale') {
      this.setData({
        selectDeliveryTime: true
      })
      this.pickerOnSaleDelivery({ currentTarget: { dataset: { item: this.data.goodsList.deliveryTimeRange } } })
    }
    // firstToPicker === 'onSale' && this.pickerOnSaleDelivery({ currentTarget: { dataset: { item: this.data.goodsList.deliveryTimeRange } } })
  },
  async deliveryTimeIsLimit ({ showModal = false, description = '' }) {
    const _this = this
    this.setData({
      hasPayPopup: false
    })
    // 当前时间段已约满
    if (showModal) {
      wx.showModal({
        title: '',
        content: description,
        showCancel: false,
        confirmText: '知道了',
        async success(res) {
          if (res.confirm) {
            await _this.getDeliveryLimitInfo()
            _this.resetSelectedTime()
          }
        }
      })
      return
    }
    // 不用提示弹窗
    await this.getDeliveryLimitInfo()
    this.resetSelectedTime()
  },
  collectDeliveryTime (goodsOrderList) {
    // deliveryTimeMap
    const { deliveryTimeMap } = this._data
    if (!goodsOrderList) return
    if (!goodsOrderList.length) return
    goodsOrderList.forEach(subOrder => {
      const { orderSubType, deliveryTimeBegin, goodsOrderID } = subOrder
      if (orderSubType === 'F') {
        deliveryTimeMap['onSale'] = deliveryTimeBegin
      }
      if (orderSubType === 'G') {
        if (!deliveryTimeMap['preSale']) {
          deliveryTimeMap['preSale'] = {}
        }
        Object.assign(deliveryTimeMap['preSale'], { [goodsOrderID]: deliveryTimeBegin })
      }
    })
  },
  /**
   * 判断订单列表里是否存在有参与特价活动的商品是否有超出限购数量（超出部分未享受特价优惠的）
   * @param {Array<{[key: String]: any, count: Number, specialActivityInfo: {activityCount: String}}>} goodsOrderList 订单列表
   * @returns {Boolean} 是否享受优惠
   */
  isBuyOutLimit(goodsOrderList) {
    // 格式不匹配的情况
    if (!Array.isArray(goodsOrderList) || !goodsOrderList.length) {
      return false
    }
    let isOver = false
    for (let i = 0; i < goodsOrderList.length; i++) {
      const orderItem = goodsOrderList[i]
      // 如果订单项有特价活动
      if (orderItem && orderItem.specialActivityInfo && String(orderItem.specialActivityInfo.activityCount) !== '') {
        // 有超过限购 则终止循环
        if (orderItem.count > orderItem.specialActivityInfo.activityCount) {
          isOver = true
          break
        }
      }
    }
    return isOver
  },
  /**
   * 跳转到心享会员首页
   */
  jumpVipPage() {
    this.navigateToCommonH5Page({
      currentTarget: {
        dataset: {
          urltype: '7'
        }
      }
    })
  }
})
