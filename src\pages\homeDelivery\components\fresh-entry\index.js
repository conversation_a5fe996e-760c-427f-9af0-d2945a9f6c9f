const config = require('../../../../utils/config')
const act = require('../../../../utils/activity')
const sensors = require('../../../../utils/report/sensors')
const { getFreshEntry } = require('./fetch')
const { homeDeliveryStorageCache } = require('~/utils/storageCache')

/**
 * 首页焦点图模块
 */
Component({
  properties: {
    userInfo: {
      type: Object,
      value: {
        customerID: -1
      }
    },
    addressInfo: {
      type: Object,
      value: {
        cityID: -1,
        storeID: -1,
        cityCode: '',
        storeCode: '',
        deliveryCenterCode: '',
        supportBToCService: false
      }
    },
  },
  data: {
    /**图片域名 */
    picDomain: config.baseUrl.PAGODA_PIC_DOMAIN,

    /**是否展示图片 */
    showFocusImage: false,
    /**次日达广告位入口图 */
    focusPicUrl: homeDeliveryStorageCache.getCache('focusPicUrl', ''),
    /**次日达广告位配置 */
    freshConfig: homeDeliveryStorageCache.getCache('freshConfig', {}),
  },

  observers: {
    /**
     * 展示焦点图后，监听组件是否出现在视图内
     * @param {*} isShow
     * @returns
     */
    showFocusImage(isShow) {
      if (!isShow) {
        return
      }

      if (this._data.initedRect) {
        return
      }

      wx.nextTick(() => {
        this.imageObserver()
        this._data.initedRect = true
      })
    }
  },

  methods: {
    /**
     * 获取次日达入口数据
     */
    async init() {
      this._data = {
        initedRect: false
      }

      const { userInfo, addressInfo } = this.data
      const freshConfig = await getFreshEntry(userInfo, addressInfo)

      this.initImage(freshConfig)
      homeDeliveryStorageCache.setCache('focusPicUrl', this.data.focusPicUrl)
      homeDeliveryStorageCache.setCache('freshConfig', this.data.freshConfig)
    },

    /**
     * 初始化
     * @param {*} freshConfig
     * @returns
     */
    initImage(freshConfig) {
      //  当前城市无次日达入口广告位配置
      if (!Object.keys(freshConfig).length) {
        this.setData({
          showFocusImage: false,
          focusPicUrl: '',
          freshConfig: {},
        })
        return
      }

      const { picDomain } = this.data
      const { picUrl } = freshConfig

      const focusPicUrl = `${picDomain}${picUrl}`
      const showFocusImage = Boolean(picUrl)

      this.setData({
        focusPicUrl,
        freshConfig,
        showFocusImage,
      })
    },

    /**
     * 点击次日达入口跳转
     */
    clickFreshImage() {
      const freshConfig = this.data.freshConfig
      const {
        bannerId = '',
        bannerName = '',
      } = freshConfig

      act.toActivityPage(freshConfig)

      sensors.adClick({
        banner_id: String(bannerId),
        banner_name: bannerName,
        Position: 0,
        element_code: 180022001,
      })
    },

    /**
     * 监听次日达入口图出现在视窗内
     */
    imageObserver() {
      this._data.facusImageObserver && this._data.facusImageObserver.disconnect()
      const facusImageObserver = wx.createIntersectionObserver(this)
      facusImageObserver.relativeToViewport().observe('#fresh-entry', res => {
        // 焦点图曝光埋点
        if (res.intersectionRatio !== 0) {
          const {
            bannerId = '',
            bannerName = '',
          } = this.data

          sensors.adExposure({
            banner_id: bannerId,
            banner_name: bannerName,
            Position: 0,
            element_code: 180022001,
          })
        }
      })
      this._data.facusImageObserver = facusImageObserver
    },
  }
})
