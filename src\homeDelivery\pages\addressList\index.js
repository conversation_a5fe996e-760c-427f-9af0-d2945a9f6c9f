let coordtransform = require('../../../utils/coordUtil')
let commonObj = require('../../../source/js/common').commonObj;
const { addressLabels, defaultCityInfo } = require('../../../utils/config')
const conFnService = require('../../../utils/services/conFnService')
const locateService = require('../../../utils/services/locate')
const { LOCATE_STORAGE_ENUM } = locateService
const sensors = require('../../../utils/report/sensors')
import { debounce, setStoreListInfo } from '../../../utils/util'
import { createStoreBindings } from 'mobx-miniprogram-bindings'
import locateStore from '../../../stores/module/locate'
import wxappMap from '../../../service/wxappMap'
import { boolean2YN } from '../../../utils/YNBoolean';
import { clickReport } from '../../../utils/report/sensors'

const callbackSucc = (data) => ({ isSuccess: true, data })
const callbackFail = (error) => ({ isSuccess: false, data: error })

const IS_RECEIVEING_ADDRESS = 'IS_RECEIVEING_ADDRESS'

const ONLY_SUPPORT_TIMELY = 'ONLY_SUPPORT_TIMELY'
const ONLY_SUPPORT_TAKE = 'ONLY_SUPPORT_TAKE'
const genSelectStoreTips = ({ storeName, supportType, notSupportType }) => {
  const tipsMap = {
    ONLY_SUPPORT_TIMELY: '配送',
    ONLY_SUPPORT_TAKE: '自提'
  }
  return `${storeName}仅支持${tipsMap[supportType]}，是否需要切换到其他支持${tipsMap[notSupportType]}的门店？`
}
let app = getApp();
Page({
  data: {
    tipsWord: '',
    showCustomConfirmModal: false,
    showSearchList: false, //地址联想列表
    from: '', // user: 会员中心; timely: 及时达首页; firmOrder: 及时达确认订单
    currAddr: {},
    cityName: '',
    cityID: '',
    noAddre: false,
    isIphoneX: app.globalData.isIphoneX,
    addressList: [], // 收货地址
    storeArr: [],
    chooseStore: '',
    isLoading: true, // 一进来是地址加载中的状态
    isBack: false, // 是否要返回上一页
    mantle: false, //输入框聚焦
  },
  _data: {
    showCustomModalResolve: null,
    reLocateTimelyCity: null, // 重新定位后的定位信息
    isClearInput: false,
    scrollFlag: false, // 监听滚动flag
    prevTimeStamp: 0 // 更多地址跳转时间戳
  },
  onLoad(options) {
    const timelyCity = wx.getStorageSync('timelyCity') || { locateCity: {} }
    const locateCity = timelyCity.locateCity || {}
    const homeDeliveryAddr = options.homeDeliveryObj ? JSON.parse(options.homeDeliveryObj) : {};
    const { location = {}, address, cityName= '' } = wx.getStorageSync('userCurrLoca') || {}
    const { cityName: defaultCityName, cityID: defaultCityID} = defaultCityInfo
    const title = locateStore.useDefault ? '无定位地址' : (homeDeliveryAddr.locaDetailInfo || address || '定位失败，未获取到定位信息')
    this.setData({
      currAddr: {
        title,
        lon: homeDeliveryAddr.lon || location.lon,
        lat: homeDeliveryAddr.lat || location.lat
      },
      cityName: locateCity.cityName || timelyCity.cityName || cityName || defaultCityName,
      cityID: locateCity.cityID || timelyCity.cityID || defaultCityID,
    })
    this.data.chooseStore = timelyCity.storeID
    this.storeBindings = createStoreBindings(this, {
      store: locateStore,
      fields: {
        useDefaultAddress: (store) => store.useDefault
      }
    });
  },
  onUnload() {
    this.storeBindings.destroyStoreBindings()
  },
  onShow() {
    // 浏览页面上报神策
    sensors.pageScreenView()
    this.getFrequentlyList()
    this.storeList()
  },

  /**
   * @description 从地址管理页回来直接返回上一页
   */
  checkIsBack () {
    if (this.data.isBack) {
      this.setData({
        scrollLeft: 0  // 设置常用地址栏滚动到最左边
      })
      setTimeout(() => {
        wx.navigateBack({ delta: 1 })
      }, 1000)
    }
  },

  /**
   * @description 获取常用收货地址列表，同时根据标签名获取标签背景色和字体色
   */
  getFrequentlyList() {
    const { userID } = wx.getStorageSync('user') || {}
    if (!userID) return
    const selectedAddress = wx.getStorageSync('selectedAddress') || {}
    const addressId = selectedAddress.addressId
    app.api.getFrequentlyList({customerID: userID}).then( (res) => {
      const resData = res.data || []
      resData.forEach( (item, index) => {
        addressLabels.forEach( label => {
          if (item.label === label.labelName) {
            item.labelObj = label
          }
        })
        item.isDefault = String(item.addressId) === String(addressId) ? selectedAddress.isDefault : 'N'
        if (item.isDefault === 'Y') {
          resData.splice(index, 1)
          resData.unshift(item)
        }
      })
      this.setData({
        addressList: [...resData].slice(0,10),
        hasAddressMore: resData.length > 2
      })
    }).catch( conFnService.failFunction )
  },
  // 重新定位
  locate: app.subProtocolValid('shop', async function() {
    if (app.globalData.reportSensors) {
      clickReport({
        blockName: '',
        blockCode: '00',
        element_name: '重新定位',
        element_code: '130100001',
      })
    }
    try {
      const locationInfo = await locateService.getGeoLocation()
      await locateService.getCityName(locationInfo.latitude, locationInfo.longitude)
      const { location: { lng, lat }, address, cityName = '' } = wx.getStorageSync('userCurrLoca') || {}
      const currLocation = {
        title: address,
        lon: lng,
        lat,
        cityName
      }
      this.setLocation(currLocation)
    } catch (error) {
      // const isShowLocateAuthModal = wx.getStorageSync(LOCATE_STORAGE_ENUM.HAS_SHOW_LOCATE_AUTH_MODAL)
      // if (isShowLocateAuthModal && !app.globalData.userLocationAuth) {
      //   const res = await app.showModalPromise({
      //     content: '需先开启定位权限方可定位当前地址。（若您已开启定位权限但仍无法正常使用，请尝试打开【微信】定位）',
      //     showCancel: true,
      //     cancelText: '取消',
      //     confirmText: '去开启',
      //   })
      //   if (res) {
      //     wx.openSetting({
      //       success() {}
      //     })
      //   }
      // }
      locateService.handleWXLocationErr(error.errMsg)
    }
  }),
  /**
   * @param {*} location title: 地址名称 lon: 经度  lat: 纬度  cityName: 城市名
   * @description 根据选择的值设置位置信息，同时检查城市服务
   */
  setLocation(location = {}) {
    const bdlocation = coordtransform.gcj02tobd09(location.lon, location.lat)
    console.log(bdlocation)
    this.setData({
      currAddr: {
        title: location.title,
        lon: bdlocation[0],
        lat: bdlocation[1]
      },
      cityName: location.cityName || this.data.cityName
    })
    wx.removeStorageSync('selectedAddress') // 重新定位时置空
    this.checkCity({ isMatchAddress: false })
  },

  /**
   * @param {Object} [options]
   * @param {string} options.selectType
   * @param {number} options.receiveAddressID
   * @param {boolean} options.isMatchAddress
   * @returns
   */
  async checkCity (options) {
    locateStore.changeUseDefault({ useDefault: false })

    const { selectType, receiveAddressID, isMatchAddress = true } = options || {}

    // 检查城市服务
    const { isSuccess, data } = await this.handleFruitCityServiceInfo({ receiveAddressID, isMatchAddress })
    if (!isSuccess) return
    const { timelyCity, resReceivingAddressInfo, resCityInfo, resStoreList, noCityServiceOrNoStore = false } = data
    // 暂存timelyCity
    this._data.reLocateTimelyCity = timelyCity
    // 是选择的收货地址
    const isReceivingAddress = selectType === IS_RECEIVEING_ADDRESS


    // 设置自提信息
    locateService.setUserCurrLocaNearSelfTakeStore({ storeList: resStoreList, city: resCityInfo })

    // 设置收货地址
    this.setSelectedAddress({ timelyCity, resReceivingAddressInfo, isReceivingAddress, receiveAddressID })

    // 无b2c服务并且无门店服务，才提示无门店，其余情况不提示
    if (!app.globalData.hasStoreService && !timelyCity.supportBToCService) {
      console.log('无b2c服务并且无门店服务，才提示无门店，其余情况不提示');
      wx.showToast({ title: '当前地址无门店', icon: 'none', duration: 2000 })
      this.setData({
        storeArr: []
      })
      // 页面不跳转
      return this.locateSuccCallback({ timelyCity, back: false })
    }

    // 无城市服务或者无门店
    if (noCityServiceOrNoStore) {
      console.log('无城市服务或者无门店');
      return this.locateSuccCallback({ timelyCity })
    }
    // 有门店
    if (!noCityServiceOrNoStore) {
      console.log('有门店');
      // 检查门店是否自提或配送，点击【换个门店】返回true
      const { isSuccess } = await this.checkStoreInfo(timelyCity.storeInfo)
      // 点击【换个门店】，停留在当前页面，且刷新门店列表
      if (isSuccess) {
        this.storeList({ timelyCityOption: timelyCity })
        return
      }
      // 只有同意才跳转页面
      this.locateSuccCallback({ timelyCity })
    }
  },
  locateSuccCallback ({ timelyCity, back = true }) {
    app.changeTimelyCityInfo(timelyCity, { isFreshPageGoods: false })
    // 选择了切换地址，则首页需要更新数据
    app.event.emit('refreshPageGoods')
    back && wx.navigateBack({
      delta: 1
    })
  },
  /**
   * @desc 请求checkCity接口并返回相关信息。
   * @param { Object } options
   * @param {number} options.receiveAddressID
   * @param {boolean} options.isMatchAddress
   * @returns { Promise<{isSuccess: boolean, data: undefined | object}> }
   */
  async handleFruitCityServiceInfo ({ receiveAddressID, isMatchAddress }) {
    // 入参
    const { userID } = wx.getStorageSync('user') || {}
    const { cityName, currAddr = {} } = this.data
    const param = {
      cityName,
      lat: currAddr.lat,
      lon: currAddr.lon,
      address: currAddr.title,
      isMatchAddress: boolean2YN(isMatchAddress),
    }
    userID && (param.customerID = userID)
    receiveAddressID && (param.receiveAddressID = receiveAddressID)
    console.log(param)
    const cityInfo = await this.fetchCheckCityInfo(param)
    // 检查城市服务失败
    if (!cityInfo) return callbackFail()
    let {
      city = {},
      address: resReceivingAddressInfo, // 接口返回的定位地址附近的收货地址
    } = cityInfo
    const {
      storeList = [],
      locateCity,
    } = cityInfo
    resReceivingAddressInfo = resReceivingAddressInfo || {}
    city = city || {}
    const { cityCanService } = locateService.updateCityServerStatus(cityInfo)

    // 无城市服务或者无门店
    const noCityServiceOrNoStore = !cityCanService || storeList.length < 1
    if (noCityServiceOrNoStore) {
      return callbackSucc({
        timelyCity: locateService.getTimelyCity({
          city,
          address: param,
          locateCity,
        }),
        noCityServiceOrNoStore,
        resCityInfo: city,
        resStoreList: storeList
      })
    }

    // 有门店
    const { store: storeInfo, cityInfo: cityAfterCheck } = locateService.storeInfo2City(storeList[0], city)
    city.cityID !== cityAfterCheck.cityID && locateService.updateCityServerStatus(cityAfterCheck)
    return callbackSucc({
      timelyCity: locateService.getTimelyCity({
        city: cityAfterCheck,
        store: storeInfo,
        address: param,
        locateCity,
      }),
      resReceivingAddressInfo,
      resCityInfo: cityAfterCheck,
      resStoreList: storeList
    })
  },
  /**
   * @param {object} options
   * @param {object} options.timelyCity
   * @param {object} options.resReceivingAddressInfo 命中收货地址时响应里的address字段有数据l，否则address为nul
   * @param {boolean} options.isReceivingAddress 是否选择了收货地址
   * @param {number | undefined} options.receiveAddressID 收货地址id
   */
  setSelectedAddress ({ timelyCity, resReceivingAddressInfo, isReceivingAddress, receiveAddressID }) {
    // 是否选择收货地址
    // 1. 是
    // 1.1 无门店时，需要设置selectAddress缓存，在收货地址列表中匹配
    // 1.2 有门店时
    // 1.2.1 接口有返回收货地址信息，需要将收货地址信息合并到timelyCity，再设置缓存
    // 1.2.2 接口无返回收货地址信息，移除selectAddress缓存
    // 2. 否
    // 2.1 无门店时，移除selectAddress缓存
    // 2.2 有门店时，移除selectAddress缓存

    // 没有选择收货地址时，不管有无门店，都需要清空selectedAddress缓存
    if (!isReceivingAddress) return wx.removeStorageSync('selectedAddress')
    let selectAddress = null
    const hasStore = !!timelyCity.storeCode
    // 有收货地址
    if (!hasStore) { // 无门店，筛选常用收货地址列表里的
      selectAddress = this.data.addressList.filter(item => String(item.addressId) === String(receiveAddressID))[0] || {}

    } else { // 有门店
      // 有收货地址信息
      if (resReceivingAddressInfo && resReceivingAddressInfo.addressId) {
        // 将响应返回的lat,lon,address信息覆盖到timelyCity上
        Object.assign(timelyCity, {
          lat: resReceivingAddressInfo.lat,
          lon: resReceivingAddressInfo.lon,
          address: resReceivingAddressInfo.gisAddress.replace(/\s+/g, "") || resReceivingAddressInfo.address || '', // 去掉地址中的空格
        })
        selectAddress = resReceivingAddressInfo

        // v3.6.5 为了兼容微信导入gisAddress为空的情况
        if (!selectAddress.gisAddress || !selectAddress.gisAddress.trim()) {
          selectAddress.gisAddress = resReceivingAddressInfo.address || ''
        }
      } else { // 无收货地址信息
        return wx.removeStorageSync('selectedAddress')
      }
    }
    // 缓存
    selectAddress.isDefault = 'Y'
    wx.setStorageSync('selectedAddress', selectAddress)
  },
  /**
   * @desc 有门店时，检查门店信息，是否支持配送、自提；Y表示是，N表示否
   * @param {object} storeInfo
   * @returns { Promise<{isSuccess: boolean, data: undefined | object}> }
   */
  async checkStoreInfo (storeInfo) {
    const { isTimelySupport, isSupportTake, shortName } = storeInfo

    // 都支持，直接返回
    if (isTimelySupport === 'Y' && isSupportTake === 'Y') return callbackFail()
    const supportType = isTimelySupport === 'Y' ? ONLY_SUPPORT_TIMELY : ONLY_SUPPORT_TAKE
    const notSupportType = isSupportTake === 'Y' ? ONLY_SUPPORT_TIMELY : ONLY_SUPPORT_TAKE
    const tipsWord = genSelectStoreTips({ storeName: shortName, supportType, notSupportType })
    this.setData({
      tipsWord,
      showCustomConfirmModal: true
    })
    const modalRes = await new Promise(resolve => this._data.showCustomModalResolve = resolve)

    return modalRes ? callbackSucc() : callbackFail()
  },
  /**
   *
   * @param {object} param 入参
   * @returns { undefined | { city: object, address: object, storeList: Array<object>, hasCityService: string, supportSuperVip: string, supportBToCService: string, supportSuperVipShop: string} }
   */
  async fetchCheckCityInfo (param) {
    try {
      const res = await app.api.checkCity(param)
      return res.data || {}
    } catch (error) {
      console.log('fetchCheckCityInfo', error);
      wx.showToast({
        title: `暂时获取不到定位信息，请稍后再试~`,
        icon: 'none',
        duration: 2000
      })
      return
    }
  },
  /**
   * @param {addressId} 选择收获地址时才有此参数
   *
   */
  setStorageAddress (addressId) {
    let selectAddress = this.data.addressList.filter( item => String(item.addressId) === String(addressId))[0] || {}
    selectAddress.isDefault = 'Y'

    wx.setStorageSync('selectedAddress', selectAddress)
  },
  // 新增地址
  addAddress: app.subProtocolValid('shop', function() {
    if (app.checkSignInsStatus()) {
      // 用户已登录
      if (app.globalData.reportSensors) {
        clickReport({
          blockName: '',
          blockCode: '00',
          element_name: '新增地址',
          element_code: '130100002',
        })
      }
      wx.navigateTo({
        url: `/bgxxUser/pages/address/addAddress/index?from=homeDelivery`,
      })
    } else {
      app.signIn()
    }
  }),
  /**
   * @param {*} e
   * @description 根据输入值查找地址
   */
  bindInputTap(e) {
    let value = e.detail.value
    if (this._data.isClearInput) {
      return
    }
    this.setData({
      inputValue: value
    })

    this.qqmapsdkSelect(this.data.inputValue)
  },
  qqmapsdkSelect: debounce( async function(value) {
    const res = await wxappMap.getSuggestion({
      keyword: value,
      region: this.data.cityName,
    })
    //  替换原title内容并将输入搜索的关键字给予高亮样式
    res.data.forEach((item)=>{
      item.showTitle = item.title.replace(value,`<span style="color:#00A34F;">${value}</span>`)
    })
    this.setData({
      searchAddressList: res.data,
      showSearchList: !!value,
      mantle: !value,
    })
  }, 300),
  // 点击搜索出来的地址
  selectSearchAddress(e) {
    console.log('selectSearchAddress');
    wx.reportAnalytics('searchresult_click')
    let { title, lon, lat } = e.currentTarget.dataset
    this.setData({
      showSearchList: false
    })
    let location = {
      title,
      lon,
      lat
    }
    this.setLocation(location)
  },
  // 清空输入地址
  clearSearchAddress() {
    this.setData({
      inputValue: '',
      showSearchList: false,
      mantle:false,
    })
    this._data.isClearInput = true
  },
  //聚焦显示删除叉叉
  bindFocusHandler(e) {
    wx.reportAnalytics('searchstore')
    this.setData({
      closeShowFlag: true,
      mantle: true
    })
    this._data.isClearInput = false

    clickReport({
      blockName: '',
      blockCode: '03',
      element_name: '搜索框',
      element_code: '130103002',
    })
  },
  //失焦隐藏删除叉叉
  bindBlurHandler(e) {
    this.setData({
      closeShowFlag: false
    })
  },
  // 选择收货地址
  selectAddress(e) {
    const { lon, lat, addressId, cityId, cityName, gisAddress} = e.currentTarget.dataset
    this.setData({
      currAddr: {
        title: gisAddress,
        lat: lat,
        lon: lon
      },
      cityID: cityId,
      cityName,
    })
    this.checkCity({
      selectType: IS_RECEIVEING_ADDRESS,
      receiveAddressID: addressId
    })

    clickReport({
      blockName: '',
      blockCode: '02',
      element_name: '常用地址',
      element_code: '130102001',
    })
  },

  /**
   * 选择城市
   */
  selectCity() {
    wx.navigateTo({
      url: `/bgxxShop/pages/cityList/index?cityId=${this.data.cityID}`
    })

    clickReport({
      blockName: '',
      blockCode: '03',
      element_name: '城市',
      element_code: '130103001',
    })
  },

  // 调用前n页的方法
  invokeLastPageMethod(n,methodName, ...args) {
    let pages = getCurrentPages();
    let idx = pages.length - 1 - n;
    let prevPage = pages[idx];

    if(prevPage[methodName]){
      return prevPage[methodName](...args)
    };
  },
  /**
   * @description 选择城市页调用的方法
   * @param {*} city
   */
  refreshCityInfo (city = {}) {
    console.log(city)
    this.setData({
      cityID: city.cityID,
      cityName: city.cityName,
      // searchAddressList: [],
      // showSearchList: false
    })
  },
  // 附近门店列表数据请求
  storeList(options) {
    const { timelyCityOption } = options || {}
    const that = this
    const timelyCity = timelyCityOption ? timelyCityOption : (wx.getStorageSync('timelyCity') || {});
    const params = {
      lon: timelyCity.lon || -1, // 经度
      lat: timelyCity.lat || -1, // 纬度
      cityCode: timelyCity.cityCode,
      isNeedAddDemoteStoreFun: boolean2YN(true),
      isNeedSaleCount: boolean2YN(true),
      isNeedScore: boolean2YN(true),
    }
    if (!params.cityCode) {
      return
    }
    const { userID } = wx.getStorageSync('user') || {}
    userID && (params.customerID = userID)
    const selectedAddress = wx.getStorageSync('selectedAddress') || {}
    const addressId = selectedAddress.addressId
    addressId && (params.receiveAddressID = addressId)

    app.api.getFruitNearByStores(params).then( ({ data }) => {
      data = data || []
      let chooseIdx = data.findIndex(v => {
        return String(v.storeID) === String(that.data.chooseStore)
      })
      if (chooseIdx !== 0 && chooseIdx !== -1) {
        const store = data.splice(chooseIdx, 1)
        data.unshift(store[0])
        chooseIdx = 0
      }

      setStoreListInfo(data)

      that.setData({
        storeArr: data,
        storeIndex: chooseIdx,
        isLoading: false
      })

    }).catch(res => {
      commonObj.showModal('提示', res.errorMsg, false, '我知道了')
    })
  },
  //去门店详情页
  toStoreDetail(e) {
    let that = this
    let i = e.currentTarget.dataset.index
    console.log(i)
    let txLocation = coordtransform.bd09togcj02(that.data.storeArr[i].lon, that.data.storeArr[i].lat)
    let curStore = that.data.storeArr[i] || {}
    let { salesCount, storeName, phone, distance, address, openingTime, credentialUrl, scoreNumber,startTime='',endTime='', storeCode } = curStore
    console.log('curStore', curStore)
    let pageInfo = {
      salesCount,
      storeName,
      storePhone: phone,
      lon: txLocation[0],
      lat: txLocation[1],
      distance,
      address,
      openingTime,
      credentialUrl,
      scoreNumber,
      startTime,
      endTime,
      storeCode
    }
    wx.navigateTo({
      url: '../storeDetail/index?storeInfo=' + encodeURIComponent(JSON.stringify(pageInfo))
    })
  },
  goIndex (e) {
    const that = this
    const idx = e.currentTarget.dataset.idx
    const selectStore = that.data.storeArr[idx] || {}
    const timelyCity = this._data.reLocateTimelyCity ? this._data.reLocateTimelyCity : wx.getStorageSync('timelyCity')
    that.setData({
      storeIndex: idx
    })
    // city传空对象,主要是想直接通过storeInfo2City取出selectStore里的city信息
    const { store, cityInfo } = locateService.storeInfo2City(selectStore, {})
    const cityChange = timelyCity.cityID  !== cityInfo.cityID
    cityChange && locateService.updateCityServerStatus(cityInfo)
    app.changeTimelyCityInfo(locateService.getTimelyCity({
      city: cityInfo,
      store: selectStore,
      // 主要是想用timelyCity里的lat,lon,address
      address: timelyCity,
    }, { keepLocate: true }), { isFreshPageGoods: cityChange || locateStore.useDefault || Number(store.storeID) !== Number(this.data.chooseStore) })
    setTimeout(function(){
      wx.navigateBack()
    }, 1000)
  },

  // 检查是否有更多地址（超过10个才展示更多地址）
  checkHasAddressMore () {
    return this.data.hasAddressMore
  },

  // 跳转地址管理页
  navigateToAddress () {
    const { addressId = -1 } = wx.getStorageSync("selectedAddress") || {}
    wx.navigateTo({
      url: `/bgxxUser/pages/address/addressList/index?from=homeAddressList&addressId=${addressId}`
    })

    clickReport({
      blockName: '',
      blockCode: '02',
      element_name: '更多地址',
      element_code: '130102002',
    })
  },
  navigateToCollectStore() {
    if (!app.checkSignInsStatus()) {
      app.signIn()
      return
    }
    wx.navigateTo({
      url: '/homeDelivery/pages/collectStore/index'
    })
    clickReport({
      blockName: '点击已收藏门店',
      blockCode: '01',
      element_name: '点击已收藏门店',
      element_code: '130100006',
    })
  },
  handleChoice (event) {
    const selectConfirm = event.detail === 'confirm'
    this.setData({
      showCustomConfirmModal: false
    })
    this._data.showCustomModalResolve(selectConfirm)
  },
  //  点击蒙层取消蒙层展示
  greyCoating(){
    this.setData({
      mantle:false,
    })
  },
  onCollectSucess(e) {
    // 这个逻辑不会触发了已经
    const selectStore = e.detail
    let timelyCity = this._data.reLocateTimelyCity ? this._data.reLocateTimelyCity : wx.getStorageSync('timelyCity')
    app.changeTimelyCityInfo({
      ...timelyCity,
      cityID: selectStore.cityID,
      storeID: selectStore.storeID,
      storeCode: selectStore.storeCode,
      storeName: selectStore.storeName,
      storeInfo: selectStore
    }, { isFreshPageGoods: locateStore.useDefault || Number(selectStore.storeID) !== Number(this.data.chooseStore) })
    setTimeout(function(){
      wx.navigateBack()
    }, 1000)
  }
});
