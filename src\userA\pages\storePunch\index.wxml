<import src="/pages/template/index.wxml" />
<view class="contain" wx:if="{{!isLoading}}">
  <user-protocol />
  <no-store
    wx:if="{{storeSignInStatus === '0' && !nearbyStore.storeID}}"
    type="2"
    icon-nostore-class="default-img"
    noStoreContent="您附近10公里范围内没有百果园门店哦\n先去到一家门店在打卡吧~">
  </no-store>
  <view class="main" wx:else>
    <view class="store-info">
      <view class="award-tips">门店打卡奖励 <text class="score">{{storeRewardIntegral}}</text> 积分</view>
      <!-- 未打卡 -->
      <view wx:if="{{storeSignInStatus === '0'}}">
        <view class="store-addr">
          <text class="store-tips">距您最近的门店</text>
          <view class="flex align-c" catchtap='toStoreMapPage'>
            <image class="store-icon" src="../../source/image/icon_add_bule.png"></image>
            <text class="store-name">{{nearbyStore.storeName}}</text>
            <image class="direction-icon" src="../../source/image/icon_right_12.png"></image>
          </view>
        </view>
        <!-- 允许打卡 -->
        <form bindsubmit="submitFormId" report-submit='true' wx:if="{{isAllowSignIn === 'Y'}}">
          <button class="punch-btn" bindtap='punchCard' form-type="submit">打卡</button>
        </form>
        <!-- 不允许打卡 -->
        <view class="distance" wx:else>
          距您 {{currentDistance}}{{lessKilo ? 'm' : 'km'}}，100m内可打卡
          <image class="refresh" src="../../source/image/icon_refresh.png" bindtap='nearbyStoreLoc'></image>
        </view>
      </view>
      <!-- 已打卡 -->
      <view wx:else>
        <view class="store-addr">
          <text class="store-tips">今日打卡门店</text>
          <image class="store-icon" src="../../source/image/icon_add_bule.png"></image>
          <text class="store-name">{{todaySignInStore.storeName}}</text>
          <image class="direction-icon" src="../../source/image/icon_right_12.png"></image>
        </view>
        <view class="punch-btn has-punch">今日已打卡</view>
      </view>
    </view>
    <!-- 热门商品推荐 -->
    <view class="hot-info" wx:if='{{goodsList.length > 1}}'>
      <view class="h-title">
        <text>门店热销水果</text>
        <view class="more" bindtap='skipToDelivery'>更多<image class="icon-20" src="../../source/image/icon_right_20.png"></image> </view>
      </view>
      <view class="hot-goods-box">
        <block wx:for="{{goodsList}}" wx:key="goodsSn">
          <common-goods
            id="goods_{{item.goodsSn}}"
            class="recommend-goods"
            goodsObj="{{item}}"
            isShowCount="{{true}}"
            componentRow="double"
            bind:wxsAnimation="handlewxsAnimation"
            bind:updateCount="updateCartCount"
            bind:toShowChoiceLayer="toShowChoiceLayer"
            addSensorskey="storePunchAddGoods"
          ></common-goods>
        </block>
      </view>
    </view>

    <!-- 购物车结算栏 -->
    <cart-popup orderType="{{ ORDER_TYPE.TIMELY }}" />
    <!-- 购物车结算栏 -->
  </view>

  <!-- 弹窗 -->
  <view class="model" wx:if='{{showAwardPop}}'>
    <view class="model-610">
      <view class="pop-title">打卡成功</view>
      <view class="pop-mid">
          <image class="jifen" src="../../source/image/<EMAIL>"></image>
          <view>积分+{{storeCode}}</view>
      </view>
      <form class="pop-bottom" bindsubmit="submitFormId" report-submit='true'>
        <button class="btn-receive" data-type="storeAccept" catchtap="closeModelPop" form-type="submit">收下奖励</button>
      </form>
    </view>
  </view>

  <!-- 未开户门店定位 -->
    <!-- <view class="model" wx:if="{{userLocateClose}}">
      <view class="model-610" >
        <view class="pop-title"> Oops! </view>
        <view class="pop-mid">
          <view class="store-tips-none">您没有打开GPS定位，无法找</view>
          <view>到相应门店，去开启定位吧</view>
        </view>
        <view class="pop-bottom">
          <view class="btn-receive" data-type="openLocate" catchtap="closeModelPop">知道啦</view>
        </view>
      </view>
    </view> -->
</view>

<!-- 购物车动画 -->
<!-- <template is="add-to-cart-animation" data="{{...addToCartData}}"></template> -->
<!--cart-animation-ball id="goodsCartAnimationBall"></cart-animation-ball -->

<!-- 蒙层 -->
<view class="prevent-screen" hidden="{{!prevent}}"></view>
<!-- sku加购弹层 -->
<add-goods-layer
  showLayer="{{showLayer}}"
  goodsInfo="{{goodsInfo}}"
  bind:updateCount="updateCartCount"
  bind:updateCarList="getCarList"
></add-goods-layer>


<common-loading />