const commonApi = require('../../../api/common.api')

import { observable, action } from 'mobx-miniprogram'
import {parallelPromise} from '../../../utils/promise'
import { clientLogin } from '../../../service/userService'


const protocol_version = 'protocol_version_cache'
const show_protocol_date = 'show_protocol_date'

/**用户隐私协议版本号 */
let appVersion = ''
let checkDate = null
/**本次冷启动是否已检查过 */
let isCheckProtocol = false

/**@type { 'firstProtocol' } 当前更新协议类型 */
let updateProtocolType = ''


export const actionType = {
  /**
   * 默认城市定位
   */
  default: 'default',
  /**
   * 通过授权获取定位
   */
  checkAuth: 'checkAuth',
  /**
   * 检查百果园协议
   */
  checkPagodaProtocol: 'checkPagodaProtocol',
  /**
   * 只在应用冷启动时执行，后续都不执行(因为定位逻辑放在onshow)
   */
  noAction: 'noAction'
}

const protocolPromise = {}

function cacheProtocol(){
  wx.setStorage({ key: protocol_version, data: appVersion })
  wx.setStorage({ key: show_protocol_date, data: checkDate })
}

export const pagodaProtocolStore = observable({
  show: false,
  /**
   * @type {'firstProtocol'}
   */
  updateProtocolType: '',
  toggleShow: action(function (bool) {
    this.show = bool
  }),
  changeProtocolType: action(function (type) {
    this.updateProtocolType = type
  })
})


export const wxPrivacyProtocolStore = observable({
  show: false,
  hasAgreed: false,
  toggleShow: action(function (bool) {
    this.show = bool
  }),
  changePrivacyAgreement: action(function(bool){
    this.hasAgreed = bool
  })
})

export function closeProtocol(detail) {
  cacheProtocol()
  protocolPromise.resolve && protocolPromise.resolve(detail)
  pagodaProtocolStore.toggleShow(false)
}

export async function getProtocolVersion(){
  try {
    const versionResult = await commonApi.getProtocolVersion()
    const data = versionResult.data || {}
    // const data = {
    //   "app": "1.0.2",
    //   "recommend": "1.0.4",
    //   "prepaid_card": "1.0.0",
    //   "unmanned_retail": "1.0.0",
    //   "zfb": "1.0.0",
    //   "vegReturnGoods": "1.0.0"
    // }
    return data
  } catch (error) {
    console.log('error', error);
    return {}
  }
}

/**
 * 检查是否需要展示协议
 * @returns { Promise<{ show: boolean, protocol: Promise<{ agree: boolean }> }> }
 */
export async function checkIsShowProtocol() {
  updateProtocolType = ''

  const cacheAppVersion = wx.getStorageSync(protocol_version)
  const showProtocolDate = wx.getStorageSync(show_protocol_date)
  const date = new Date()
  const failResult = { show: false }
  checkDate = `${date.getFullYear()}${date.getMonth()}${date.getDate()}`
  console.log('checkDate', checkDate)

  const isAllLatest = cacheAppVersion
  // 所有协议均已缓存 && 当天检查过就不再检查 || 本次冷启动已经打开过协议弹窗
  if (isAllLatest && showProtocolDate === checkDate || isCheckProtocol) {
    return failResult
  }

  try {
    if (protocolPromise.checking) {
      /**检查中，需要将检查类型重新赋值回检查中的类型 */
      updateProtocolType = protocolPromise.checking.updateProtocolType
      return protocolPromise.checking.promise
    }
    Object.assign(protocolPromise, {
      checking: (function() {
        let checkingResolve
        const checkingPromise = new Promise((resolve) => {
          checkingResolve = resolve
        })
        return {
          updateProtocolType: '',
          resolve: checkingResolve,
          promise: checkingPromise
        }
      })()
    })
    const data = await getProtocolVersion()
    if (!data.app) {
      protocolPromise.checking && protocolPromise.checking.resolve(failResult)
      return failResult
    }
    // 接口取到的协议版本
    appVersion = data.app

    //  存在登录回调标记，说明用户已进行微信/手机号登录，无需再次确认协议弹窗
    if (wx.getStorageSync('loginSuccessSetProtocol')) {
      wx.setStorageSync('loginSuccessSetProtocol', '')
      cacheProtocol()
      protocolPromise.checking && protocolPromise.checking.resolve(failResult)
      return failResult
    }

    // 协议不更新不打开
    const showProtocol = (function() {
      if (cacheAppVersion !== appVersion) {
        updateProtocolType = 'firstProtocol'
      }

      //  记录检查类型
      protocolPromise.checking.updateProtocolType = updateProtocolType
      pagodaProtocolStore.changeProtocolType(updateProtocolType)
      return Boolean(updateProtocolType)
    })()
    // 防止协议未更新时，重新进入小程序重新请求接口
    showProtocol || wx.setStorage({ key: show_protocol_date, data: checkDate })
    const result = {
      show: showProtocol,
      protocolType: showProtocol ? updateProtocolType : ''
    }
    if (showProtocol) {
      const protocol = {}
      result.protocol = protocol.promise = new Promise(resolve => (protocol.resolve = function(result) {
        protocolPromise.checking = void 0
        resolve(result)
      }))
      Object.assign(protocolPromise, protocol)
      isCheckProtocol = true
    }
    protocolPromise.checking && protocolPromise.checking.resolve(result)
    return result
  } catch (error) {
    console.log('getProtocolVersion', error);
  }
  protocolPromise.checking && protocolPromise.checking.resolve(failResult)
  return failResult
}

/**
 * 在应用冷启动时，若未缓存过百果园隐私协议，则默认同意且缓存，并发起静默登录
 */
export async function appLaunchCacheProtocol(){
  const cacheAppVersion = wx.getStorageSync(protocol_version)
  const isAllCache = cacheAppVersion
  if (isAllCache) return
  const data = await getProtocolVersion()
  if (data.app) {
    // 接口取到的协议版本
    appVersion = data.app
    const date = new Date()
    checkDate = `${date.getFullYear()}${date.getMonth()}${date.getDate()}`
    cacheProtocol()
  }
  await clientLogin()
}

export const appLaunchCacheProtocolParallel = parallelPromise(appLaunchCacheProtocol)

export const checkIsShowProtocolParallel = parallelPromise(checkIsShowProtocol)

/**
 * return actionType
 */
export async function checkPrivacyPopup (){
  try {
    const res = await wxPrivacyController.appLaunchCheckSetting()
    return res
  } catch (error) {
    console.log('wxPrivacyController err', error);
    return actionType.default
  }
}
export const checkPrivacyPopupParallel = parallelPromise(checkPrivacyPopup)

/**
 * 项目中隐私协议弹窗触发时机
 * 1. 启动时展示
 * 2. 当调用了隐私api时
 */
class WXPrivacyController{
  constructor(){
    this.launchInitPromise = {
      resolve: null,
      reject: null
    }
    this.privacyHandler = null
    this.privacyResolves = new Set()
    this.hasLaunchCheck = false
    this.initWatchApiHandler()
  }
  agreePrivacy () {
    wxPrivacyProtocolStore.toggleShow(false)
    const {resolve} = this.launchInitPromise
    resolve && resolve()
    wxPrivacyProtocolStore.changePrivacyAgreement(true)
    if (this.privacyResolves.size) {
      console.log('this.privacyResolves', this.privacyResolves);
      this.privacyResolves.forEach(resolve => {
        resolve({
          event: 'agree',
          buttonId: 'agree-btn'
        })
      })
      this.privacyResolves.clear()
    }
  }
  disAgreePrivacy () {
    wxPrivacyProtocolStore.toggleShow(false)
    const {reject} = this.launchInitPromise
    reject && reject()
    if (this.privacyResolves.size) {
      this.privacyResolves.forEach(resolve => {
        resolve({
          event: 'disagree',
        })
      })
      this.privacyResolves.clear()
    }
  }
  /** 应用初始化 */
  /**
   * 应用初始化时检查隐私设置
   * resolve：需要授权
   * reject：不需要授权
   */
  appLaunchCheckSetting () {
    if (this.hasLaunchCheck) {
      return Promise.resolve(actionType.noAction)
    }
    // 因为webview无法打开授权弹窗，所以这里不能走下面的逻辑。
    // 获取当前页面栈
    // const pages = getCurrentPages();
    // // 获取当前页面
    // const currentPage = pages[pages.length - 1];
    // if (currentPage.route === 'h5/pages/commonh5/index') {
    //   return Promise.resolve(actionType.noAction)
    // }
    return new Promise((resolve, reject) => {
      if (wx.getPrivacySetting) {
        wx.getPrivacySetting({
          success: res => {
            this.hasLaunchCheck = true
            console.log('wx.getPrivacySetting res', res);
            if (res.needAuthorization) {
              // 需要授权，展示弹窗
              this._handleSetting({show: true, cb: () => {
                this.launchInitPromise = {
                  resolve: () => {
                    resolve(actionType.checkAuth)
                  },
                  reject: () => {
                    reject()
                  }
                }
              }})
            } else{
              // 不需要授权，不展示弹窗
              wxPrivacyProtocolStore.changePrivacyAgreement(true)
              this._handleSetting({show: false, cb: () => {
                resolve(actionType.checkPagodaProtocol)
              }})
            }
          },
          fail: (err) => {
            console.log('wx.getPrivacySetting err', err);
            this._handleSetting({show: false, cb: () => {
              reject()
            }})
          },
        })
      } else {
        console.log('no support wx.getPrivacySetting');
        // 低版本基础库不支持 wx.getPrivacySetting 接口，隐私接口可以直接调用
        // 因此直接设置为同意授权
        wxPrivacyProtocolStore.changePrivacyAgreement(true)
        this._handleSetting({show: false, cb: () => {
          // 此时执行正常的流程
          resolve(actionType.checkPagodaProtocol)
        }})
      }
    })

  }
  _handleSetting({show, cb}) {
    wxPrivacyProtocolStore.toggleShow(show)
    cb && cb()
  }
  /** 应用初始化 */
  /** 监听隐私api */
  watchApiAuth() {
    if (wx.onNeedPrivacyAuthorization) {
      wx.onNeedPrivacyAuthorization(resolve => {
        console.log('onNeedPrivacyAuthorization');
        if (typeof this.privacyHandler === 'function') {
          this.privacyHandler(resolve)
        }
      })
    }
  }
  initWatchApiHandler() {
    this.privacyHandler = resolve => {
      if (!wxPrivacyProtocolStore.show) {
        console.log('toggleShow');
        wxPrivacyProtocolStore.toggleShow(true)
      }
      this.privacyResolves.add(resolve)
    }
  }
  /** 监听隐私api调用 */

}
export const wxPrivacyController = new WXPrivacyController()
