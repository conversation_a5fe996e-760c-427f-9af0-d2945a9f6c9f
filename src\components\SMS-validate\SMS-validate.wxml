<!--components/SMS-validate.wxml-->

<view class="sms-container">
  <view class="sms-title">请输入验证码</view>
  <view class="sms-desc">系统检测到您的下单设备发生了变更，为了保证钱包安全，请输入您尾号
    <text class="sms-mobile">{{mobileSuffix}}</text>
    的手机收到的4位短信验证码。
  </view>

  <view class="sms-input {{isValidateError ? 'sms-input-error' : ''}}">
    <input class="sms-ipt" type="number" value="{{SMSCode}}" placeholder="请输入验证码"  placeholder-style="color:#C8C9CC" bindinput="inputCode" maxlength="4" cursor-spacing="100" focus="{{isFocus}}" />
    <image class="sms-clear-circle-btn {{SMSCode ? '' : 'sms-clear-circle-btn-hidden'}}" src="/source/images/icon-delete.png" catchtap="clearBtnClick"></image>
    <text class="sms-send {{disabledSendBtn ? 'sms-disabled' : ''}}" catchtap="sendCode">{{sendText}}</text>
  </view>

  <view class="sms-btn">
    <view class="button {{SMSCode && SMSCode.length === 4 ? 'button-active' : ''}}" catchtap="validateCode">确定</view>
  </view>
</view>
