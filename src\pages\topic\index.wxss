/* homeDelivery/pages/activity/index.wxss */

@import "/pages/template/index";
.flex {
  display: flex;
}

.gray {
  color: #666;
}

.red {
  color: #f16650;
}

.f36 {
  font-size: 36rpx;
}
.topic-container {
  position: relative;
}
/* 图片类  */
.bottom-box {
  height: 64rpx;
  background-color: #fff;
}
.topic-img-box,
.product-img-box {
  font-size: 0;
  position: relative;
  margin-top: -1rpx;
}
.topic-img-box-item{
  position: relative;
}
.topic-img-box image{
  /* width: 100%; */
}

/* 优惠券形态被领取 */
.icon_received {
  width: 110rpx;
  height:110rpx;
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  z-index:999;
}
.coupon_received {
  opacity: 0.5;
  background:rgba(238,238,238,1);
}

/* 单排商品 */
.single-goods-gap-box {
  /* padding-top: 24rpx; */
}
/* 连续两个商品模块（单排，双排，三排），padding-top置为0 */
.single-goods-gap-box + .single-goods-gap-box,
.single-goods-gap-box + .sv-anchor + .single-goods-gap-box,
.goods-gap-box + .single-goods-gap-box,
.goods-gap-box + .sv-anchor + .single-goods-gap-box {
  /* padding-top: 0; */
}

/* 双排商品 */
.goods-gap-box{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 24rpx 0;
}
/* 连续两个商品模块（单排，双排，三排），padding-top置为0 */
.goods-gap-box + .goods-gap-box,
.goods-gap-box + .sv-anchor + .goods-gap-box,
.single-goods-gap-box + .goods-gap-box,
.single-goods-gap-box + .sv-anchor + .goods-gap-box {
  /* padding-top: 0; */
}
.goods-gap{
  padding: 10rpx 0;
}
.topic-item-goods,
.price-module-box,
.top-module-box,
.video-module-box {
  margin-bottom: 20rpx;
}
/*商品样式  */

.topic-item {
  color: #333;
}

.btn-cart {
  height: 40rpx;
  width: 40rpx;
  opacity: 1;
  padding: 22rpx 12rpx 22rpx 12rpx;
}

.count {
  text-align: center;
  width: 40rpx;
  font-size: 28rpx;
  color: #414955;
}

.nogoods {
  opacity: 0;
  pointer-events: none;
}

/*通用  */

.price-info {
  align-items: baseline;
}

.price-info .int,
.price-info .decimal {
  color: #ff9f06;
  font-size: 28rpx;
  margin-right: -0.15em;
}

.price-info .decimal {
  font-size: 28rpx;
}

.price-info .price-text-original {
  margin-left: 12rpx;
  color: #666;
  font-size: 24rpx;
  text-decoration: line-through;
}

.sale-out {
  font-size: 28rpx;
  color: #999;
}

/*购物车*/

.mask {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  /* height: 100vh; 有蒙层，无height属性则无蒙层 */
  background-color: rgba(0, 0, 0, 0.5);
}

.mask-true {
  height: 100vh;
  /* 有蒙层，无height属性则无蒙层 */
  z-index: 1111;
}

.shopcart {
  position: fixed;
  bottom: 0;
  width: 100%;
}
.shopcart-left {
  width: 100%;
  display: flex;
  flex-direction: row;
}
.cart {
  width: 100rpx;
  height: 100rpx;
}

.totalprice {
  flex-direction: column;
  justify-content: center;
  padding-left: 10rpx;
}

.shopcart .price {
  font-size: 28rpx;
  margin-right: 40rpx;
}

.title {
  color: #212121;
  font-size: 28rpx;
}

.clear {
  display: flex;
  align-items: center;
  height: 70rpx;
  font-size: 28rpx;
  color: #666;
}

.trash {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
}

.carriage {
  color: #999;
  font-size: 20rpx;
  line-height: 28rpx;
}

.settle {
  width: 260rpx;
  line-height: 98rpx;
  color: #fff;
  background-color: #ffa006;
  text-align: center;
  font-size: 32rpx;
}

.nosettle {
  color: #fff;
  background-color: #b9b9b9;
}

.shopcart-list {
  background-color: #fff;
  max-height: 390rpx;
}

.shopcart-item {
  display: flex;
  align-items: center;
  height: 130rpx;
  border-top: 1rpx solid #f2f2f2;
}

.count {
  text-align: center;
  width: 64rpx;
  font-size: 28rpx;
  color: #414955;
}

.nogoods {
  opacity: 0;
  pointer-events: none;
  transition: all 0.5s;
}

.price {
  display: flex;
  position: relative;
  align-items: baseline;
  font-size: 36rpx;
  color: #ff9f06;
}

.allPrice {
  display: flex;
  position: relative;
  align-items: baseline;
  font-size: 38rpx;
  color: #ff9f06;
}

.show-cart {
  padding-bottom: 98rpx;
}


.goods-item {
  display: flex;
  flex-direction: row;
  font-size: 26rpx;
  color: #666;
  margin: 24rpx 24rpx;
}

.symbel {
  margin: 0 -0.15em;
}

.nocoupon {
  flex: 1;
}

.nocoupon > .txt {
  border: 1rpx solid #bababa;
  border-radius: 16rpx;
  width: 94rpx;
  height: 32rpx;
  line-height: 30rpx;
  font-size: 19rpx;
  color: #999;
  text-align: center;
}

/*微信卡券*/
.coupon-container{
  position: relative;
  text-align: center;
  padding: 30rpx 0;
  height: 352rpx;
}
.coupon-title{
  height:50rpx;
  line-height:50rpx;
  padding: 0 40rpx;
  margin-bottom: 26rpx;
  font-size:36rpx;
  color:rgba(0,0,0,1);
}
.coupon-subtitle{
  height:84rpx;
  padding: 0 40rpx;
  font-size:30rpx;
  color:rgba(136,136,136,1);
  line-height:42rpx;
}
.coupon-btns{
  display: flex;
  margin: 58rpx 0 0 0;
  font-size:30rpx;
  border-top: 1rpx solid #E5E5E5;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
}
.coupon-btn{
  flex: 1;
  height: 98rpx;
  line-height: 98rpx;
  color: #000000;
  font-size:36rpx;
}
.coupon-btn--primary{
  color:#008C3C;
  border-left: 1rpx solid #E5E5E5;
}

.p-bottom24{
  padding-bottom: 24rpx;
}
.type-padding-hor{
  padding: 24rpx 0;
}
.type-padding-ori{
  padding: 0 24rpx;
}
.type-margin-hor{
  margin-bottom: 20rpx;
}
.type-margin-ori{
  margin-left: 24rpx;
  margin-right: 24rpx;
  /* flex: 1; */
  width:calc(100vw - 48rpx);
}

.fight-group-wrapper {
  border-radius: 12rpx;
  overflow: hidden;
}

.fight-group-item{
  width: 702rpx;
}

#ob-reference{
  position: fixed;
  top: 0;
  width: 100%;
  height: 10rpx;
}

.sv-anchor{
  position: relative;
  height: 0rpx;
}

.inner-anchor{
  position: absolute;
  width: 100%;
  opacity: 0;
  /* top: -110rpx; */
  z-index: -1;
}
.start-anchor{
  height: 100rpx;
  bottom: 0;
}
.end-anchor{
  height: 1rpx;
  bottom: 115rpx;
}

/* 拼团子组件内样式 */
.good-wrap-row{
  padding: 21rpx 19rpx 31rpx 12rpx;
  justify-content: space-between;
}

#fixNav{
  position: fixed;
  width: 100%;
  top: 0;
  overflow: hidden;
}
.no-data{flex:1;display:flex;flex-direction:column;justify-content: space-between;text-align: center;}
.icon-no-data {
  width: 441rpx;
  height: 309rpx;
  margin: 0px auto;
  margin-top: 155rpx;
}
.l-t{
  font-size:30rpx;
  font-weight:400;
  color:rgba(51,51,51,1);
  margin-top: 12rpx;
  text-align: center;
}

.back-btn{width:280rpx;height:88rpx;line-height: 88rpx;text-align: center;color:#fff;font-size: 30rpx;background-color:#008C3C;border-radius: 44rpx;margin:40rpx auto;}

.scroll-view-blank {
  position: absolute;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #f7f7f7;
  z-index: 1000;
}
.scroll-view-blank image {
  width: 100%;
}
/* 顶部导航条 */
.nav-back{
  width: 44rpx;
  height: 44rpx;
}

.nav-back image{
  width: 100%;
  height: 100%;
}
.topic-topgoods {
  position: relative;
}
.topic-topgoods .topic-topgoods-icon {
  position: absolute;
  top: 16rpx;
  left: 40rpx;
  width: 51rpx;
  height: 70rpx;
  z-index: 1;
}
.topic-topgoods-img  .topic-topgoods-icon {
  top: 130rpx;
}
.video-top-anchor,.video-bottom-anchor {
 height: 1rpx;
}
.video-module-box {
  margin-left: 24rpx;
  margin-right: 24rpx;
}