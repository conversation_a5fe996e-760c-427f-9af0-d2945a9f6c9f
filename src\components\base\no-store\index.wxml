<view
  wx:if="{{type === 1}}"
  style="{{ customMargin ? 'margin: unset' : '' }}"
  class="no-result">
  <image
    style="{{ customMargin ? 'margin: unset' : '' }}"
    class="icon-citynostore"
    src='https://resource.pagoda.com.cn/dsxcx/images/34667cdd39b460118b997b76aca2bdea.png'></image>
  <!-- <view class='txt-min'>该地址附近暂无百果园门店~</view> -->
  <view class='txt-middle'>已有{{callCount}}人召唤百果园来这里开店~</view>
  <view class="btn-group">
    <button class="{{disable ? 'call-btn off': 'call-btn'}} btn-group-item" bindtap='callBGY' disabled='{{disable}}'>我也来召唤</button>
    <button class="btn-group-item btn-gray" bindtap='navigateselfExtractStore'>换个地址</button>
  </view>
</view>

<view wx:if="{{type === 2}}" class="no-result">
  <image class="icon-nostore icon-nostore-class" src='https://resource.pagoda.com.cn/dsxcx/images/e309da62bb0b6a506afd8157de79cdc0.png'></image>
  <text wx:if="{{noStoreContent}}" class='txt-nostore' style="font-size: {{contentSize}}rpx; color: {{contentColor}};line-height: {{contentLineHeight}}rpx">{{noStoreContent}}</text>
  <slot></slot>
</view>
