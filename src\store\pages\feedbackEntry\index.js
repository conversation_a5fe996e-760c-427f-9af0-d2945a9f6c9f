import { getAppConfig } from '~/service/wxappSetting'
import sensors from '../../../utils/report/sensors'
import { pageTransfer } from '~/utils/route/pageTransfer'

const commonObj = require('../../../source/js/common').commonObj
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 意见反馈的问题类型及对应的子问题
    feedbackType: [],
    // 开始时间
    startTime: '08:00',
    // 结束时间
    endTime: '23:00'
  },
  _data: {
    // 是否在线客服
    isOnline: false,
    // 服务器时间
    systemTime: void 0
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function () {
    await this.getFeedbackType()
    this.setCustomerServiceOnline()
  },
  onShow: function () {
    // 浏览页面上报神策
    sensors.pageScreenView()
  },

  /**
   * 根据当前时间设置客服是否在线
   */
  async setCustomerServiceOnline () {
    const appConfig = await getAppConfig()
    // 默认时间为8:00 - 23:00
    let { startTime, endTime } = { startTime: '08:00', endTime: '23:00' }
    // 如果有配置客服在线时间，那么使用配置的时间
    if (appConfig && appConfig.customerServiceHours) {
      startTime = appConfig.customerServiceHours.startTime
      endTime = appConfig.customerServiceHours.endTime
    }
    
    const now = new Date(this._data.systemTime)
    const nowHours = now.getHours()
    const nowMinutes = now.getMinutes()
    const nowSeconds = now.getSeconds()

    const startTimeArr = startTime.split(':')
    const endTimeArr = endTime.split(':')
    const startTimeHours = parseInt(startTimeArr[0])
    const startTimeMinutes = parseInt(startTimeArr[1]) || 0
    const startTimeSeconds = parseInt(startTimeArr[2]) || 0

    const endTimeHours = parseInt(endTimeArr[0])
    const endTimeMinutes = parseInt(endTimeArr[1]) || 0
    const endTimeSeconds = parseInt(endTimeArr[2]) || 0

    // 比较当前时间是否在客服营业时间内
    if (nowHours > startTimeHours || (nowHours === startTimeHours && nowMinutes > startTimeMinutes) || (nowHours === startTimeHours && nowMinutes === startTimeMinutes && nowSeconds >= startTimeSeconds)) {
      if (nowHours < endTimeHours || (nowHours === endTimeHours && nowMinutes < endTimeMinutes) || (nowHours === endTimeHours && nowMinutes === endTimeMinutes && nowSeconds <= endTimeSeconds)) {
        this._data.isOnline = true
      }
    }
    this.setData({
      startTime,
      endTime
    })
  },

  /**
   * 获取意见反馈的问题类型及对应的子问题
   */
  async getFeedbackType () {
    try {
      const data =  await app.api.getFeedbackConfig2({
        code: 'customer_nogood'
      })
      const feedbackType = data.data || []
      this._data.systemTime = data.systemTime
      this.setData({
        feedbackType: feedbackType.filter(item => item.status === 1).map(item => {
          return {
            ...item,
            detailValueList: (item.detailValueList || []).filter(child => child.status === 1)
          }
        })
      })
    } catch (error) {
      commonObj.showModal('提示', '抱歉，暂时无法选择反馈类型。如需反馈，请联系客服：************', true, '我知道了', '联系客服', res => {
        if (res.confirm) {
          wx.navigateBack()
        } else if (res.cancel) {
          wx.makePhoneCall({
            phoneNumber: '************'
          })
        }
      })
    }
  },

  /**
   * 跳转反馈页面
   */
  navigateToFeedback (e) {
    const { item, index } = e.currentTarget.dataset
    if (!item) return

    // 如果支持转客服，那么直接跳转客服
    if (item.value.includes('4') && this._data.isOnline) {
      // 增加埋点
      sensors.clickReport({
        element_content: `${item.name}（在线客服）`,
        element_name: `${item.name}（在线客服）`,
        element_code: 1162300002 + index * 2,
      })
      this.toOnlineService()
      return
    }
    // 增加埋点
    sensors.clickReport({
      element_content: `${item.name}（填写反馈）`,
      element_name: `${item.name}（填写反馈）`,
      element_code: 1162300002 + index * 2 + 1,
    })
    pageTransfer.send(item)
    // 其他情况直接跳转问卷
    wx.navigateTo({
      url: '/store/pages/feedbackPage/index'
    })
  },
  /**
   * @description 跳转去客服页面
   */
  toOnlineService() {
    const { userID } = wx.getStorageSync('user')
    let url = ''
    // 小程序意见反馈页，点击商品问题大类，直接转人工客服；人工客服地址：
    url = `https://kfyw.pagoda.com.cn/webchatbot/h5chat.html?sysNum=1638265220559&sourceId=126&lang=zh_CN&memberId=${userID}`
    // APP意见反馈页，点击商品问题大类，直接转人工客服；人工客服地址：
    // url = 'https://kfyw.pagoda.com.cn/webchatbot/h5chat.html?sysNum=1638265220559&sourceId=127&lang=zh_CN'
    
    wx.navigateTo({
      url: `/h5/pages/commonLink/index?pageUrl=${encodeURIComponent(url)}`
    })
  },
  /**
   * @description 跳转去机器人客服页面
   */
  toOfflineService() {
    // 增加埋点
    sensors.clickReport({
      element_content: '联系在线客服',
      element_name: '联系在线客服',
      element_code: '1162300001',
    })
    app.toOnlineService({ needShopCartGoods: true })
  }
})
