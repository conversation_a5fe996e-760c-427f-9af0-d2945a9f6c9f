/**
 * 本页面是及时达分享卡片、海报 拆分出来独立功能的文件 后续涉及到卡片、海报分享的修改在本文件修改
 * 多行注释表示是共有方法 单行注释表示该类私有方法，暂未对外暴露
 */
import { getWxCodeTempFilePath }  from '../../../service/weChat'

import { getSaleReportInfo }  from '../../../utils/report/getSaleReportInfo'
import { goodsLevelFormat, isLowGoodsLevel } from '../../../utils/format'
import userStore from '~/stores/userStore'
import config from '../../../utils/config'
import { checkUserIsDistributor, isDistributorFromCache } from '../../../service/userService'
import COSHelper from '~/utils/services/COSHelper'
import { COS_GLOBAL } from '~/source/const/cosConfig'
import { getHttpFullUrl } from '~/utils/file'
import { SALE_TYPE_ENUM } from '~/utils/goods/enum'

// 商品图片常量
const { TIMELY_SHARE_CARD,
  B2C_SHARE_CARD,
  TIMELY_SHARE_POSTER,
  TIMELY_TIPS_ICON,
  B2C_TIPS_ICON
} = require('../../../source/const/goodsImage')

const util = require('../../../utils/util')
const { fillText, drawVipIcon, drawGoodsPicRadius } = require('../../../utils/services/canvasUtil')
const { createCanvasImage, strokeRoundRect, fillText: fillTextCard, fillRoundRect, measureText } = require('../../../utils/services/2DCanvasUtil')
const { generateShareAttr, ShareScene, ShareWay } = require('../../../utils/report/setup')
const sensors = require('../../../utils/report/sensors')
const getDistributorsQRCode = require.async('../../../sourceSubPackage/qrcode/distributorsQRCode')

// 由于模板图是https的资源，无法直接在canvas里面绘制，这里保存的是封面图模板可直接绘制的对象
// 分享卡片模板缓存图 及时达 全国送
let deliveryCardCover = null
let b2cCardCover = null
// 分享海报模板缓存图
let posterCover = null
// 分享卡片绘制状态
let shareCardPromise = null
let shareCardPromiseReject = null
/**@type COSHelper | null */
let cosHelperDSXCX = null
// spu背景分享图缓存数据
const shareCardCache = {
  // 过期时间戳
  expireTime: 0,
  // spu背景分享图缓存数据
  goodsShareImgList: {}
}

function instanceCOSHelper() {
  return new COSHelper({
    authSource: 'wxapp',
    cosType: 'global'
  })
}

const app = getApp()

export default class ShareCard {
  /**
   * 构造函数 接受商详页传入的data
   * @param {Object} that 页面操作的this对象
   */
  constructor(that) {
    this.that = that
    this.initDefaultData()
  }

  /**
   * 初始化默认数据
   */
  initDefaultData() {
    const data = {
      // 海报临时地址 tempFilePath 用于显示界面中的canvas
      shareImage: '',
      // 是否是iPhone机型
      isIphone: app.globalData.isIphone
    }
    const _data = {
      // 存储分享海报临时路径 格式： {[goodsSn]: tempFilePath}
      posterPic: {},
      // 存储下载好的图片临时路径缓存对象
      posterImage: {}
    }
    Object.assign(this.that.data, data)
    Object.assign(this.that._data, _data)
  }
  // 是否携带分享门店,携带isCarryStore=N进入小程序的，都不携带分享门店
  checkIsCarryShareStore () {
    const { query } = app.globalData.globalLaunchOptions || {}
    const { isCarryStore = 'Y' } = query || {}
    return isCarryStore !== 'N'
  }
  /**
   * 用户分享卡片事件
   */
  onShareAppMessage() {
    const that = this.that
    const { storeID = 0 , storeName = '', cityID= ''  } = wx.getStorageSync('timelyCity') || {}
    const { goodsSn, goodsName, abbreviation, specDesc, headPic, takeawayAttr } = that.data.goodsObj
    const shareObj = this.getShareReportData()
    wx.reportAnalytics('share_success')
    checkUserIsDistributor().then(isRecommender => {
      sensors.track('MPShare', { ...shareObj, isRecommender: Number(isRecommender) })
    })
    sensors.track('MPClick', {
      element_code: 131003001,
      element_name: '分享给好友',
      element_content: '分享给好友',
      screen_code: '1310',
      screen_name: '及时达商品详情页',
      SKU_ID: goodsSn,
      SKU_Name: goodsName || ''
    })

    const isSetStore = this.checkIsCarryShareStore()

    const storeText = isSetStore ? `&storeID=${storeID}&storeName=${storeName}` : ''
    const beforeText = goodsLevelFormat(abbreviation || goodsName)
    return {
      title: beforeText + (that.data.showDesc ? goodsLevelFormat(specDesc) : ''),
      // 这个路径和参数会在app.js中判断是否为及时达商品详情分享
      // 如果要改变分享路径，需要在app.js中更改
      path: '/homeDelivery/pages/category/index?homeDeliveryObj=' + JSON.stringify({ goodsSn, takeawayAttr}) + storeText +'&cityID=' + cityID + '&takeawayAttr=' + takeawayAttr + '&shareObj=' + JSON.stringify(shareObj),
      imageUrl: that._data.sharePic.hasOwnProperty(goodsSn) ? that._data.sharePic[goodsSn] : headPic
    }
  }

  /**
   * 用户分享朋友圈事件
   */
  onShareTimeline () {
    const that = this.that
    const { storeID = 0 , storeName = '' } = wx.getStorageSync('timelyCity') || {}
    const { userID } = wx.getStorageSync('user') || {}
    const { goodsSn, goodsName, specDesc, takeawayAttr, headPic } = that.data.goodsObj
    const formatGoodsName = goodsLevelFormat(goodsName)

    const shareObj = {
      ...generateShareAttr(),
      mp_shareTitle: formatGoodsName,
      screen_name: '及时达商品详情页',
      screen_code: '1310',
      SKU_ID: goodsSn,
      SKU_Name: formatGoodsName || '',
      storeID,
      storeName
    }
    return {
      title: formatGoodsName + (that.data.showDesc ? goodsLevelFormat(specDesc) : ''),
      query: [
        'homeDeliveryObj=' + JSON.stringify({ goodsSn, takeawayAttr, storeID: that.data.storeID }),
        'shareObj=' + JSON.stringify(shareObj),
        userID && this.checkIsCarryShareStore() && isDistributorFromCache() ? `distributor=${userID}` : ''
      ].filter(Boolean).join('&'),
      imageUrl: that._data.sharePic.hasOwnProperty(goodsSn) ? that._data.sharePic[goodsSn] : headPic
    }
  }

  /**
   * 绘制分享卡片 一个方法搞定
   * @param {Object} goodsObj 商品详情信息
   */
  async drawSharePic(goodsObj) {
    const self = this
    const that = this.that
    const { goodsSn, takeawayAttr, saleType } = goodsObj

    // 性能监控：开始时间
    const startTime = Date.now()
    console.log(`[ShareCard Performance] drawSharePic开始 - goodsSn: ${goodsSn}, 时间: ${startTime}`)

    // 判断是否是特价
    const hasActPrice = [
      that.data.priceDisplayTypeEnum.activityPrice_new,
      that.data.priceDisplayTypeEnum.activityPrice_store,
      that.data.priceDisplayTypeEnum.activityPrice_operation
    ].includes(that.data.priceDisplayType)

    // 如果是及时达 && 标准份 && 不是特价 则走自定义分享卡片模式
    if (takeawayAttr === '及时达' && saleType === SALE_TYPE_ENUM.标准份 && !hasActPrice){
      const customShareStartTime = Date.now()
      console.log(`[ShareCard Performance] 开始获取自定义分享背景 - 耗时: ${customShareStartTime - startTime}ms`)

      const shareCardImg = await this.getCustomShareBG(goodsObj.associationList)

      const customShareEndTime = Date.now()
      console.log(`[ShareCard Performance] 获取自定义分享背景完成 - 耗时: ${customShareEndTime - customShareStartTime}ms`)

      // 如果已经销毁
      if (that._data.hasClickBackBtn) {
        return
      }
      // 如果是自定义分享卡片模式
      if (shareCardImg) {
        const customBgImg = getHttpFullUrl(that.data.picUrl, shareCardImg)
        that.setData({
          customBgImg
        })
        // 如果已经销毁
        if (that._data.hasClickBackBtn) {
          return
        }
        // 上一次未绘制完 直接reject
        if (shareCardPromiseReject) {
          shareCardPromiseReject()
          shareCardPromise = null
        }

        const generateStartTime = Date.now()
        console.log(`[ShareCard Performance] 开始生成自定义分享卡片 - 耗时: ${generateStartTime - startTime}ms`)

        const res = await new Promise((resolve, reject) => {
          // 是否有生成成功过，如果有则0延迟生成下一张
          const hasSuccessBuild = Object.keys(that._data.sharePic).length > 0
          // 开始绘制 - 优化：减少延迟时间
          this.generateShareCard(hasSuccessBuild ? 100 : 1000)
          shareCardPromise = resolve
          shareCardPromiseReject = reject
        })

        const generateEndTime = Date.now()
        console.log(`[ShareCard Performance] 自定义分享卡片生成完成 - 耗时: ${generateEndTime - generateStartTime}ms, 总耗时: ${generateEndTime - startTime}ms`)

        // 如果已经销毁
        if (that._data.hasClickBackBtn) {
          return
        }
        that._data.sharePic = Object.assign(that._data.sharePic, {
          [goodsSn]: res.shareImage
        })
        return
      }
    }
    // 这里是默认的分享卡片模式
    const defaultShareStartTime = Date.now()
    console.log(`[ShareCard Performance] 开始默认分享卡片模式 - 耗时: ${defaultShareStartTime - startTime}ms`)

    return new Promise((resolve, reject) => {
      try {
        const query = wx.createSelectorQuery()
        query.select('#picCanvas').fields({ node: true, size: true }).exec(async (res) => {
          if(res[0] && res[0].node){
            const canvasInitTime = Date.now()
            console.log(`[ShareCard Performance] Canvas初始化完成 - 耗时: ${canvasInitTime - defaultShareStartTime}ms`)

            /**
             * @type {HTMLCanvasElement}
             */
            const canvas = res[0].node
            const canvasCtx = canvas.getContext('2d')
            canvas.width = 992
            canvas.height = 794

            let { storeName = '' } = wx.getStorageSync('timelyCity') || {}
            let { activityPrice, memberPrice, retailPrice, headPic, takeawayAttr } = goodsObj
            // 是否有果粉价
            const hasFruitFansPrice = userStore.isFruitFansGray && (memberPrice <= retailPrice)
            // 是否有零售价的文本
            const hasRetailPriceLabel = userStore.isFruitFansGray && (memberPrice < retailPrice)
            activityPrice = util.formatPrice(activityPrice)
            memberPrice = util.formatPrice(memberPrice)
            retailPrice = util.formatPrice(retailPrice)

            /**
             * 是否是全国送的情况
             */
            const isB2C = takeawayAttr === 'B2C'

            // 先放大2倍画布
            canvasCtx.scale(2, 2)
            // 绘制内容区
            canvasCtx.fillStyle = '#FFFFFF'
            canvasCtx.fillRect(0, 0, 496, 397)

            // 绘制背景
            const bgImageStartTime = Date.now()
            const bgPath = isB2C ? B2C_SHARE_CARD : TIMELY_SHARE_CARD
            const bgImageUrl = await self.getCoverBgImg(canvas, bgPath, isB2C ? 'b2cCard' : 'deliveryCard')
            const bgImageEndTime = Date.now()
            console.log(`[ShareCard Performance] 背景图加载完成 - 耗时: ${bgImageEndTime - bgImageStartTime}ms`)

            canvasCtx.drawImage(bgImageUrl, 0, 0, 496, 397)

            // 绘制商品图片
            const headPicStartTime = Date.now()
            // 头图需要对尺寸进行优化，增加参数&imageView2/3/w/496
            const connectCharacter = headPic.indexOf('?') === -1 ? '?' : '&';
            headPic += connectCharacter + 'imageView2/3/w/496'

            const navHeadUrl = await createCanvasImage(canvas, headPic)
            const headPicEndTime = Date.now()
            console.log(`[ShareCard Performance] 商品图片加载完成 - 耗时: ${headPicEndTime - headPicStartTime}ms`)

            canvasCtx.drawImage(navHeadUrl, 12, 44, 248, 248)

            // 绘制门店
            const isDrawStore = this.checkIsCarryShareStore()
            if (takeawayAttr === '及时达' && storeName && isDrawStore) {
              storeName = `百果园(${storeName})`
              const marginLeft = 15
              const marginRight = 10
              const maxWidth = 496 - marginLeft - marginRight
              const storeNameW = measureText(canvasCtx, storeName, '20px')
              // 门店名称是否超出一屏
              const isOverFlow = storeNameW >= maxWidth
              const width = (isOverFlow ? maxWidth : storeNameW) + marginLeft + marginRight
              const cornerRadius = { bottomRight: 0, bottomLeft: 19, topLeft: 0, topRight: 0 } // 圆角矩形的四个圆角的半径
              fillRoundRect(canvasCtx, 496 - width, 0, width, 35, 8, '#E5F6EC', cornerRadius)
              fillTextCard(canvasCtx, {
                text: storeName,
                x: 496 - width + marginLeft,
                y: 28,
                fontSize: '20px',
                fillStyle: '#06B24C',
                textBaseline: 'bottom',
                width: maxWidth,
                MaxLineNumber: 1
              })
            }
            // 绘制商品价格
            const middlePointX = 371 // 价格在x轴居中的位置

            // 绘制抢购价|特价 的标题
            // 默认是抢购价(b2c)
            let labelPriceTitle = '抢购价'
            // 判断是否是特价
            switch (that.data.priceDisplayType) {
              case that.data.priceDisplayTypeEnum.activityPrice_new:
                labelPriceTitle = '新人价'
                break
              case that.data.priceDisplayTypeEnum.activityPrice_store:
              case that.data.priceDisplayTypeEnum.activityPrice_operation:
                labelPriceTitle = '限时特价'
                break
              default:
                labelPriceTitle = !isB2C && hasFruitFansPrice ? '果粉价' : '抢购价'
                break
            }

            const labelPriceTitleW = measureText(canvasCtx, labelPriceTitle, '24px')
            fillTextCard(canvasCtx, {
              text: labelPriceTitle,
              x: middlePointX - labelPriceTitleW / 2,
              y: 75,
              fontSize: '24px',
              fillStyle: '#222222',
              textBaseline: 'top',
              MaxLineNumber: 1
            })

            const salePrice = activityPrice || memberPrice
            // 获取最大价格￥的宽度 结果-10是因为符号绘制的比较宽 这里就处理了一下
            const rmbSignW = measureText(canvasCtx, '￥', '32px') - 10
            // 获取最大价格具体值的宽度
            const priceTextW = measureText(canvasCtx, salePrice, '52px')
            // 计算x轴
            const firstPriceX = middlePointX - (rmbSignW + priceTextW) / 2

            // 绘制人民币符号
            fillTextCard(canvasCtx, {
              text: '¥',
              x: firstPriceX,
              y: 122,
              fontWeight: '500',
              fontSize: '32px',
              fillStyle: '#222222',
              textBaseline: 'top'
            })
            // 绘制价格
            fillTextCard(canvasCtx, {
              text: salePrice,
              x: firstPriceX + rmbSignW,
              y: 108,
              fontWeight: '500',
              fontSize: '52px',
              fillStyle: '#222222',
              textBaseline: 'top'
            })

            // 特价商品处理：绘制划线价
            if (activityPrice) {
              const lineThroughPrice = userStore.isFruitFansGray ? retailPrice : memberPrice
              // 计算￥符号宽度会导致多10px
              const linePriceW = measureText(canvasCtx, `￥${lineThroughPrice}`, '24px') - 10
              const linePriceX = middlePointX - linePriceW / 2
              const linePriceY = 160
              // 计算y坐标
              const lineY = linePriceY + 12
              fillTextCard(canvasCtx, {
                text: `¥${lineThroughPrice}`,
                x: linePriceX,
                y: linePriceY,
                fontWeight: '400',
                fontSize: '24px',
                fillStyle: '#CCCCCC',
                textBaseline: 'top'
              })

              // 绘制删除线
              canvasCtx.beginPath()
              canvasCtx.lineWidth = 2
              canvasCtx.strokeStyle = '#CCCCCC'
              canvasCtx.moveTo(linePriceX - 1, lineY)
              canvasCtx.lineTo(linePriceX + linePriceW + 1, lineY)
              canvasCtx.closePath()
              canvasCtx.stroke()
            } else if (hasRetailPriceLabel) {
              // 要展示零售价
              // 计算￥符号宽度会导致多10px
              const linePriceW = measureText(canvasCtx, `零售价¥${retailPrice}`, '24px') - 10
              const linePriceX = middlePointX - linePriceW / 2
              const linePriceY = 162

              fillTextCard(canvasCtx, {
                text: `零售价¥${retailPrice}`,
                x: linePriceX,
                y: linePriceY,
                fontWeight: '400',
                fontSize: '24px',
                fillStyle: '#888888',
                textBaseline: 'top'
              })
            }
            // 恢复画布大小（勿删，解决 OPPO手机使用同一个画布多次绘制分享卡片时，画布大小不会重置）
            canvasCtx.scale(0.5, 0.5)

            const canvasToFileStartTime = Date.now()
            console.log(`[ShareCard Performance] Canvas绘制完成，开始转换为临时文件 - 耗时: ${canvasToFileStartTime - defaultShareStartTime}ms`)

            // 优化：减少延迟时间，iPhone不需要延迟，安卓减少延迟
            const delay = that.data.isIphone ? 0 : 50
            setTimeout(() => {
              wx.canvasToTempFilePath({
                canvas: canvas,
                x: 0,
                y: 0,
                width: 992,
                height: 794,
                quality: 0.8, // 优化：降低质量以提升性能
                destWidth: 992,
                destHeight: 794,
                canvasId: 'picCanvas',
                success: res => {
                  const canvasToFileEndTime = Date.now()
                  console.log(`[ShareCard Performance] Canvas转换为临时文件完成 - 耗时: ${canvasToFileEndTime - canvasToFileStartTime}ms`)
                  console.log(`[ShareCard Performance] 默认分享卡片总耗时: ${canvasToFileEndTime - startTime}ms`)

                  that._data.sharePic = Object.assign(that._data.sharePic, {
                    [goodsSn]: res.tempFilePath
                  })
                  resolve()
                },
                fail: err => {
                  console.error('[ShareCard Performance] Canvas转换失败:', err)
                  reject(err)
                },
                finally: () => wx.hideLoading()
              })
            }, delay)
          }
        })
      } catch (err) {
        console.log('drawSharePic', err)
        reject()
      }
    })
  }
  onShareCardGenerated(e) {
    if (this.that.data.goodsObj.goodsSn !== e.detail.goodsSn || !shareCardPromise) {
      return
    }
    shareCardPromise(e.detail)
  }

  /**
   * 绘制分享海报 canvas图
   */
  async drawPoster () {
    const self = this
    const that = this.that
    // 如果卡片没加载完 不让加载海报 不然容易出奇奇怪怪的问题
    // if (!that.data.canUseSharePic) {
    //   return
    // }
    const { goodsSn } = that.data.goodsObj || {}
    const { posterPic = {} } = that._data
    // 若海报已经生成过了，不用重新生成
    if (posterPic.hasOwnProperty(goodsSn)) {
      this.showSharePoster(posterPic[goodsSn])
      return
    }
    wx.showLoading({
      title: '正在生成海报',
    })
    try {
      // const { storeName } = wx.getStorageSync('timelyCity') || {}
      let { activityPrice, memberPrice, retailPrice, heartPrice, goodsName, abbreviation, specDesc, subtitle: goodsWeight, takeawayAttr, isSupportDisparityRefund, effectOrderGoodsNumber, vipNineFiveDiscount, goodsLevel } = that.data.goodsObj || {}
      // 是否有果粉价
      const hasFruitFansPrice = userStore.isFruitFansGray && (memberPrice <= retailPrice)
      // 是否有零售价的文本
      const hasRetailPriceLabel = userStore.isFruitFansGray && (memberPrice < retailPrice)
      activityPrice = util.formatPrice(activityPrice)
      memberPrice = util.formatPrice(memberPrice)
      retailPrice = util.formatPrice(retailPrice)
      heartPrice = util.formatPrice(heartPrice)
      // 是否是全国送
      const isB2C = takeawayAttr === 'B2C'

      // canvas绘制文字和图片
      const canvasCtx = wx.createCanvasContext('posterCanvas')
      canvasCtx.scale(2, 2)

      // 获取生成海报的图片资源
      await this.getPosterImagePath()
      // 图片资源
      const { bgImagePath, nativeImagePath, codeImagePath, TIMELY_TIPS_ICON, B2C_TIPS_ICON } = that._data.posterImage || {}

      const marginLeft = 32
      // 绘制背景图 750*1304 这里缩放一半
      const WIDTH = 375
      const HEIGHT = 652
      canvasCtx.drawImage(bgImagePath, 0, 0, WIDTH, HEIGHT)
      // 绘制商品图片
      // canvasCtx.drawImage(nativeImagePath, 19, 58, 338, 338)
      drawGoodsPicRadius({canvasCtx, imagePath: nativeImagePath})

      // 绘制商品类型
      const tipsIcon = isB2C ? B2C_TIPS_ICON : TIMELY_TIPS_ICON
      canvasCtx.drawImage(tipsIcon, 28, 67, 60, 24)

      //绘制商品标题
      const maxWidth = 160
      let fillTop = 461
      const showGoodsLevel = (function () {
        if (goodsLevel && isLowGoodsLevel(goodsLevel)) {
          return ''
        }

        return goodsLevel ? (goodsLevel + '-') : ''
      })()
      const showGoodsName = goodsLevelFormat(goodsName)

      const showSpecDesc = (that.data.showDesc && specDesc) ? (' ' + goodsLevelFormat(specDesc)) : ''
      fillTop = fillText(canvasCtx, {
        text: showGoodsLevel + showGoodsName + showSpecDesc,
        x: marginLeft,
        y: fillTop,
        width: maxWidth,
        MaxLineNumber: 2,
        fontSize: 18,
        bolder: true,
        color: '#222222',
        lineHeight: 23
      })
      fillTop += 23 + 4 // 23 字体行高
      // 绘制小标题
      fillText(canvasCtx, {
        text: goodsWeight,
        x: marginLeft,
        y: fillTop,
        width: maxWidth,
        MaxLineNumber: 2,
        fontSize: 12,
        color: '#888888'
      })

      // 绘制第一个价格：特价/售价
      let priceTextX = marginLeft
      // 如果是特价 则坐标会偏上 否则 会偏下
      const priceTextY = 411
      const salePrice = activityPrice || memberPrice
      // 如果展示果粉价 则价格有一些偏移量，具体展示位：果粉价:¥价格
      let offsetX = 0
      // 如果有果粉价 则没有特价
      if (hasFruitFansPrice && !activityPrice) {
        // 绘制果粉标题
        fillText(canvasCtx, {
          text: '果粉价',
          x: priceTextX,
          y: priceTextY + 8,
          fontSize: 13,
          bolder: true,
          color: '#FF7387'
        })
        offsetX += measureText(canvasCtx, '果粉价', '13px') + 1
      }
      // 绘制价格
      fillText(canvasCtx, {
        text: '¥',
        x: priceTextX + offsetX,
        y: priceTextY + 6,
        fontSize: 16,
        bolder: true,
        color: '#FF7387'
      })
      // 保存零售价的x坐标
      const retailPriceTextX = priceTextX
      priceTextX += measureText(canvasCtx, `${hasFruitFansPrice && !activityPrice ? '果粉价' : ''}¥`, '13px')
      // 绘制特价 || 果粉价
      fillText(canvasCtx, {
        text: salePrice,
        x: priceTextX + 4,
        y: priceTextY - 3,
        fontSize: 26,
        bolder: true,
        color: '#FF7387'
      })
      priceTextX += measureText(canvasCtx, salePrice, '26px') + 7.5

      // 绘制第二个价格：划线价/心享价 绘制特价标签，特价icon 及 活动门店
      if (activityPrice) {
        // 划线价（原售价）
        fillText(canvasCtx, {
          text: `¥${userStore.isFruitFansGray ? retailPrice : memberPrice}`,
          x: priceTextX,
          y: priceTextY + 7,
          fontSize: 15,
          color: '#888888',
          lineThrough: true
        })
      } else if (hasRetailPriceLabel) {
        // 要展示零售价 则绘制零售价
        fillText(canvasCtx, {
          text: '零售价',
          x: retailPriceTextX,
          y: priceTextY + 29,
          fontSize: 10,
          color: '#888888'
        })
        fillText(canvasCtx, {
          text: `¥${retailPrice}`,
          x: retailPriceTextX + measureText(canvasCtx, '零售价', '10px') + 1,
          y: priceTextY + 28,
          fontSize: 12,
          color: '#888888'
        })
      }
      // 无特价，有心享价：绘制心享价
      if (!activityPrice && heartPrice) {
        const heartPriceY = priceTextY + 5
        const heartPriceX = priceTextX + 2
        // 非灰度需要判断 否则直接心享95折
        const text = !userStore.isFruitFansGray ? (!vipNineFiveDiscount ? `¥${heartPrice}` : '心享95折') : '心享95折'
        // 绘制心享价或者心享95折
        drawVipIcon({canvasCtx, text, x: heartPriceX, y: heartPriceY, height: 17})
      }

      // 绘制小程序码
      canvasCtx.drawImage(codeImagePath, 228, 401, 120, 120)

      // 绘制codeText字
      fillText(canvasCtx, {
        text: '长按图片去购买',
        x: 228 + 16,
        y: 401 + 120 + 10,
        color: '#888888',
        fontSize: 12,
        lineHeight: 10,
        MaxLineNumber: 1
      })

      canvasCtx.setTextBaseline('top')
      // canvas 画布转为图片
      canvasCtx.draw(false, function () {
        // 判断绘制方法，判断手机类型 因为安卓手机在某种情况下会导致绘图失败，没有按照原设定参数绘制
        setTimeout(() => {
          wx.canvasToTempFilePath({
            x: 0,
            y: 0,
            width: 750,
            height: 1304,
            quality: .4,
            destWidth: 750 * 1.5,
            destHeight: 1304 * 1.5,
            canvasId: 'posterCanvas',
            success: function (res) {
              console.log(`isPhone: ${that.data.isIphone}, drawposter: ${res.tempFilePath}`)
              that._data.posterPic = Object.assign(that._data.posterPic, {
                [goodsSn]: res.tempFilePath
              })
              self.showSharePoster(res.tempFilePath)
              wx.hideLoading()
            },
            fail: function (res) {
              wx.hideLoading()
              wx.showLoading({
                title: '生成海报出错啦，请稍后重试',
              })
            }
          })
        }, that.data.isIphone ? 0 : 100)
      })
    } catch (error) {
      console.error(error)
      wx.hideLoading()
      wx.showLoading({
        title: '生成海报出错啦，请稍后重试',
      })
    }
  }

  /**
   * 分享弹窗点击事件
   * @param {Object} e 事件
   */
  showModel(e) {
    const self = this
    const that = this.that
    if (that.data.fromShareTimeLine) {
      wx.showToast({
        title: '请前往小程序使用完整服务',
        icon: 'none'
      })
      return
    }

    if (!util.isEmpty(that.data.goodsObj)) {
      const type = e.currentTarget.dataset.type
      if(type === 'share'){
        that.setData({ isShare : true})
        self.utilShare()
        if (app.globalData.reportSensors) {

          sensors.clickReport({
            blockName: '底部操作栏',
            blockCode: '131015',
            element_name: '分享',
            element_code: 131000007,
          })
        }
      } else if (type === 'thumbnail'){
        that.setData({ isShare : false})
        app.subProtocolValid('shop', function() {
          if (!wx.showShareImageMenu) {
            that.setData({ isThumbnail: true })
          }
          self.utilThumbnail()
          // 生成海报
          self.drawPoster()

          if (app.globalData.reportSensors) {

            sensors.clickReport({
              blockName: '底部操作栏',
              blockCode: '131015',
              element_name: '发朋友圈',
              element_code: 131003002,
            })
          }
        })()
      }
    }
  }
  /**
   * 关闭分享弹窗事件
   * @param {Object} e 事件
   */
  closeModel(e) {
    wx.hideLoading()
    var that = this.that
    if (app.globalData.reportSensors) {
      const { goodsSn, goodsName } = that.data.goodsObj
      app.sensors.track('MPClick', {
        element_code: 131003003,
        element_name: '取消',
        element_content: '取消',
        screen_code: '1310',
        screen_name: '及时达商品详情页',
        SKU_ID: goodsSn,
        SKU_Name: goodsName || ''
      })
    }
    var type = e.currentTarget.dataset.type
    if (type === 'share') {
      that.setData({ isShare: false })
    } else if (type === 'thumbnail') {
      that.setData({ isThumbnail: false })
    }
  }

  getShareReportData() {
    const that = this.that
    const { goodsSn, goodsName, takeawayAttr } = that.data.goodsObj
    const { storeID = 0 , storeName = '', storeCode  } = wx.getStorageSync('timelyCity') || {}
    const { goods_label = [] } = getSaleReportInfo({goodsObj: that.data.goodsObj})
    const formatGoodsName = goodsLevelFormat(goodsName)

    return {
      ...generateShareAttr(),
      shareScene: {
        B2C: ShareScene.全国送商品,
        及时达: ShareScene.及时达商品,
      }[takeawayAttr] || ShareScene.及时达商品,
      mp_shareTitle: formatGoodsName,
      screen_name: '及时达商品详情页',
      screen_code: '1310',
      SKU_ID: goodsSn,
      SKU_Name: formatGoodsName || '',
      storeID,
      storeName,
      storeNum: storeCode || '',
      goods_label
    }
  }

  // 展示商品生成的海报
  showSharePoster(path, forceSharePopup) {
    if (forceSharePopup || !wx.showShareImageMenu) {
      this.that.setData({
        shareImage: path,
        isThumbnail: true,
      })
    } else {
      wx.showShareImageMenu({
        path: path,
        style: 'v2',
        success: () => {
          checkUserIsDistributor().then(isRecommender => {
            sensors.track('MPShare', {
              ...this.getShareReportData(),
              isRecommender: Number(isRecommender),
              shareWay: ShareWay.分享海报,
            })
          })
        },
        fail: async (err) => {
          if (err.errMsg === 'showShareImageMenu:fail cancel') {
            return
          }
          if (err.errMsg === 'showShareImageMenu:fail no permission' && util.checkIsQy()) {
            // 如果是企微因为权限问题失败,使用旧版的sharePopup
            this.showSharePoster(path, true)
            return
          } else if (err.errMsg === 'showShareImageMenu:fail forbidden' && isDistributorFromCache()) {
            // 暂时不支持团长分佣二维码分享到朋友圈
            // 使用微信自带的toast
            // app.showModalPromise({
            //   content: '暂不支持分享到朋友圈',
            //   showCancel: false,
            //   confirmText: '我知道了',
            // })
            return
          }
          const res = await app.showModalPromise({
            content: '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试',
            showCancel: false,
            confirmText: '我知道了',
          })
          if (res) wx.openSetting()
        }
      })
    }
  }

  // 获取生成海报的图片的本地路径
  getPosterImagePath () {
    const that = this.that
    return new Promise((resolve, reject) => {
      const p = []
      const { headPic, goodsSn, takeawayAttr } = that.data.goodsObj || {}
      const { posterPic = {}, posterImage } = that._data
      // 是否是全国送
      const isB2C = takeawayAttr === 'B2C'

      // 若海报已经生成过了，不用重新生成
      if (posterPic.hasOwnProperty(goodsSn)) {
        that.setData({
          shareImage: posterPic[goodsSn]
        })
        wx.hideLoading()
        return
      }

      // 生成二维码
      const p1 = this.getCode()
      p.push(p1)

      // 获取商品头图（若已经获取过，不用重新获取）
      if (!this.checkStorageImage('nativeImagePath', posterImage)) {
        const p2 = this.getImageInfo(headPic, 'nativeImagePath')
        p.push(p2)
      }

      // 获取海报背景图（若已经获取过，不用重新获取）
      if (!this.checkStorageImage('bgImagePath', posterImage)) {
        // 有缓存则优先取缓存
        if (posterCover) {
          this.that._data.posterImage = Object.assign(this.that._data.posterImage, {
            bgImagePath: posterCover
          })
        } else {
          const p3 = this.getImageInfo(TIMELY_SHARE_POSTER, 'bgImagePath')
          p.push(p3)
        }
      }

      // 获取及时达icon（若已经获取过，不用重新获取）
      if (!isB2C && !this.checkStorageImage('TIMELY_TIPS_ICON', posterImage)) {
        const p4 = this.getImageInfo(TIMELY_TIPS_ICON, 'TIMELY_TIPS_ICON')
        p.push(p4)
      }

      // 获取全国送icon（若已经获取过，不用重新获取）
      if (isB2C && !this.checkStorageImage('B2C_TIPS_ICON', posterImage)) {
        const p4 = this.getImageInfo(B2C_TIPS_ICON, 'B2C_TIPS_ICON')
        p.push(p4)
      }

      Promise.all(p).then(res => {
        resolve ()
      }).catch((err) => {
        console.error(err)
        reject()
      })
    })
  }

  // 检查 storage 缓存中是否存储了 storageKey对应的图片的本地路径
  checkStorageImage (storageKey, storage) {
    return storage.hasOwnProperty(storageKey)
  }

  // 将商品图缓存
  getImageInfo(netUrl, storageKeyUrl) {
    const self = this
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: netUrl,    //请求的网络图片路径
        success: function (res) {
          //请求成功后将会生成一个本地路径即res.path,然后将该路径缓存到storageKeyUrl关键字中
          self.storageImage(res.path, storageKeyUrl)
          resolve()
        },
        fail: res => reject()
      })
    })
  }

  // 将获取到的图片的本地路径存储到 posterImage
  storageImage(path, key) {
    this.that._data.posterImage = Object.assign(this.that._data.posterImage, {
      [key]: path
    })
    // 如果是及时达分享海报封面  则需要缓存下来
    if (key === 'bgImagePath') {
      posterCover = path
    }
  }

  async getDistributorsQRCode({ userID, storeName, storeID, cityID }) {
    const goodsObj = this.that.data.goodsObj
    const { goodsSn, takeawayAttr, goodsName } = goodsObj || {}
    const { goods_label = [] } = getSaleReportInfo({ goodsObj })
    // copy(`q=${encodeURIComponent(`https://dskhd-web.dsxcx.tencent-test.pagoda.com.cn/#/commission/shareLanding?distributor=${/* 团长用户id,写在单引号里👉 */''}&goodsSn=${/* 及时达商品sku,写在单引号里👉 */''}&takeawayAttr=及时达&storeName=${/* 门店名称,写在单引号里👉 */''}&storeID=${/* 门店id,写在单引号里👉 */''}&cityID=${/* 城市id,写在单引号里👉 */''}`)}`)
    return getDistributorsQRCode.then(({ getDistributorsQRCode }) => getDistributorsQRCode({
      canvasId: 'QRCodeCanvas',
      query: Object.assign({
        distributor: userID,
        goodsSn,
        takeawayAttr,
        storeName,
        storeID,
        cityID,
        goodsName,
      }, goods_label.length ? { goods_label } : {}),
    }))
  }

  // 生成二维码
  async getCode() {
    const { storeID = 0, storeName, cityID = '' } = wx.getStorageSync('timelyCity') || {}
    const { userID = '' } = wx.getStorageSync('user') || {}
    const { goodsObj } = this.that.data
    const { goodsSn, takeawayAttr } = goodsObj || {}

    // B2C商品分享跳转到商品详情页
    const isB2C = takeawayAttr === 'B2C'
    const isCarryStore = this.checkIsCarryShareStore()

    const needDistributorShare = await (userID && isCarryStore ? checkUserIsDistributor() : Promise.resolve(false))
    const posterUrlPromise = needDistributorShare
      ? this.getDistributorsQRCode({
        userID,
        storeName,
        storeID,
        cityID,
      })
      : getWxCodeTempFilePath({
        page: isB2C ? 'pages/homeDelivery/index' : 'pages/category/index',
        // 在app.js的checkNeedOptionStore会对这串字符做校验
        scene: [
          'SGD',
          goodsSn,
          takeawayAttr === '及时达' ? '' : takeawayAttr,
          isB2C || !isCarryStore ? '' : storeID,
          isB2C ? '' : cityID,
        ].join('@'),
        width: 200,
        isHyaline: false
      })
    const posterUrl = await posterUrlPromise
    return this.storageImage(posterUrl, 'codeImagePath')
  }

  /**
   * 海报保存到本地
   */
  savePoster() {
    var that = this.that
    if (app.globalData.reportSensors) {
      const { goodsName, goodsSn } = that.data.goodsObj
      app.sensors.track('MPClick', {
        element_code: 131003004,
        element_name: '保存图片',
        element_content: '保存图片',
        screen_code: '1310',
        screen_name: '及时达商品详情页',
        SKU_ID: goodsSn,
        SKU_Name: goodsName || ''
      })
    }
    wx.showLoading({
      title: '正在保存图片'
    })
    wx.saveImageToPhotosAlbum({
      filePath: this.that.data.shareImage,
      success: (res) => {
        wx.hideLoading()
        wx.showToast({ title: '保存成功，快去分享吧~', icon: 'none' })
      },
      fail: (err) => {
        wx.hideLoading()
        console.error(err)
        commonObj.showModal('提示', '保存图片出错啦，请检查是否授权小程序访问手机相册，并在授权后重试', false, '我知道了','',function(res){
          if (res.confirm) {
            wx.openSetting()
          }
        })
      }
    })
  }
  // 分享弹框
  utilShare (){
    var that =  this.that
    var animation = wx.createAnimation({
      duration:200,
      timingFunction:'linear',
      delay:0
    })
    that.animation = animation
    animation.translateY(-158).step()
    that.setData({animationShare:animation.export()})

    setTimeout(function(){
      that.setData({animationShare:animation})
    }.bind(that),400)
  }
  // 缩略图弹框
  utilThumbnail () {
    var that = this.that
    var animation = wx.createAnimation({
      duration: 200,
      timingFunction: 'linear',
      delay: 0
    })
    that.animation = animation
    animation.translateY(-parseInt((that.data.modelHeight/2))).step()
    that.setData({ animationImage: animation.export() })

    setTimeout(function () {
      that.setData({ animationImage: animation })
    }.bind(that), 300)
  }

  /**
   * private方法 获取分享卡片背景图缓存
   * @param {HTMLCanvasElement} canvas 要绘制的canvas
   * @param {String} netUrl 网络地址
   * @param {'deliveryCard' | 'b2cCard' | 'poster'} coverType 封面模板类型
   */
  async getCoverBgImg(canvas, netUrl, coverType) {
    const cacheStartTime = Date.now()

    if (coverType === 'deliveryCard' && deliveryCardCover) {
      console.log(`[ShareCard Performance] 使用缓存的及时达背景图 - 耗时: ${Date.now() - cacheStartTime}ms`)
      return Promise.resolve(deliveryCardCover)
    }
    if (coverType === 'b2cCard' && b2cCardCover) {
      console.log(`[ShareCard Performance] 使用缓存的B2C背景图 - 耗时: ${Date.now() - cacheStartTime}ms`)
      return Promise.resolve(b2cCardCover)
    }

    console.log(`[ShareCard Performance] 开始加载${coverType}背景图: ${netUrl}`)
    const img = await createCanvasImage(canvas, netUrl)
    const loadEndTime = Date.now()
    console.log(`[ShareCard Performance] ${coverType}背景图加载完成 - 耗时: ${loadEndTime - cacheStartTime}ms`)

    if (coverType === 'deliveryCard') {
      deliveryCardCover = img
    }
    if (coverType === 'b2cCard') {
      b2cCardCover = img
    }
    return img
  }
  /**
   * 用户截屏事件
   */
  async captureScreenEvent() {
    this.drawPoster()
  }

  // 生成分享图
  async generateShareCard(delay = 1000) {
    const generateStartTime = Date.now()
    console.log(`[ShareCard Performance] generateShareCard开始 - 延迟: ${delay}ms`)

    const that = this.that
    const saveData = {}
    // 获取门店名称
    const { storeName = '' } = wx.getStorageSync('timelyCity') || {}
    if (storeName) {
      saveData.goodsObj = {
        ...that.data.goodsObj,
        storeName: `百果园(${storeName})`
      }
    }
    // 判断是否是特价
    switch (that.data.priceDisplayType) {
      case that.data.priceDisplayTypeEnum.activityPrice_new:
        saveData.priceType = 'newUser'
        break
      case that.data.priceDisplayTypeEnum.activityPrice_store:
      case that.data.priceDisplayTypeEnum.activityPrice_operation:
        saveData.priceType = 'special'
        break
      default:
        saveData.priceType = 'normal'
        break
    }

    // 更新信息
    const setDataStartTime = Date.now()
    that.setData(saveData)
    console.log(`[ShareCard Performance] setData完成 - 耗时: ${Date.now() - setDataStartTime}ms`)

    const goodsSn = that.data.goodsObj.goodsSn

    // 优化：减少延迟时间，使用更精确的延迟控制
    const optimizedDelay = Math.min(delay, 800) // 最大延迟不超过800ms
    setTimeout(() => {
      const componentStartTime = Date.now()
      console.log(`[ShareCard Performance] 延迟等待完成，开始查找组件 - 实际延迟: ${componentStartTime - generateStartTime}ms`)

      const shareCardGenerator = that.selectComponent('#shareCardGenerator')
      // 防止切换sku后才开始绘制上一次的图
      if (shareCardGenerator && that.data.goodsObj.goodsSn === goodsSn) {
        console.log('[ShareCard Performance] 开始调用组件生成分享卡片')
        shareCardGenerator.generateShareCard()
      } else {
        console.log(`[ShareCard Performance] 组件未找到或商品已切换 - 组件存在: ${!!shareCardGenerator}, 商品匹配: ${that.data.goodsObj.goodsSn === goodsSn}`)
      }
    }, optimizedDelay)
  }

  /**
   * 获取自定义分享背景图
   */
  async getCustomShareBG(associationList = []) {
    const customBgStartTime = Date.now()
    console.log('[ShareCard Performance] 开始获取自定义分享背景图')

    if (associationList.length === 0) {
      console.log(`[ShareCard Performance] 无关联商品列表，返回空 - 耗时: ${Date.now() - customBgStartTime}ms`)
      return ''
    }

    // 投入品过滤耗材（basicGoodsSn 以 5 开头的是耗材）
    const associationListNew = (associationList || []).filter(item => /^(?!5)/.test(item.basicGoodsSn))
    const spuNumber = associationListNew[0]?.basicGoodsSn || ''
    // 过滤99开头的耗材 用于判断是否多投入品
    const isMultiSpu = associationListNew.filter(item => /^(?!99)/.test(item.basicGoodsSn)).length > 1

    // 如果没有spuNumber 或者 是多投入品 则返回空
    if (!spuNumber || isMultiSpu) {
      console.log(`[ShareCard Performance] 无有效SPU或多投入品，返回空 - spuNumber: ${spuNumber}, isMultiSpu: ${isMultiSpu}, 耗时: ${Date.now() - customBgStartTime}ms`)
      return ''
    }

    // 如果没有过期
    if (shareCardCache.expireTime > Date.now()) {
      const cachedResult = shareCardCache.goodsShareImgList[spuNumber] || ''
      console.log(`[ShareCard Performance] 使用缓存的自定义背景图 - spuNumber: ${spuNumber}, 有缓存: ${!!cachedResult}, 耗时: ${Date.now() - customBgStartTime}ms`)
      return cachedResult
    }

    // 查询COS
    const cosStartTime = Date.now()
    console.log('[ShareCard Performance] 开始查询COS自定义背景图配置')

    if (!cosHelperDSXCX) {
      cosHelperDSXCX = instanceCOSHelper()
    }
    let objSpuShareBGImg = {}
    try {
      const objCosRes = await cosHelperDSXCX.getObject('goodsSharePictures/goodsSharePictures.json')
      const cosEndTime = Date.now()
      console.log(`[ShareCard Performance] COS查询完成 - 耗时: ${cosEndTime - cosStartTime}ms`)

      if (objCosRes && objCosRes.Body) {
        objSpuShareBGImg = JSON.parse(objCosRes.Body || '{}')
      }
    } catch (e) {
      console.error(`[ShareCard Performance] 读取/解析COS异常 - 耗时: ${Date.now() - cosStartTime}ms`, e)
    }

    if (!objSpuShareBGImg || !objSpuShareBGImg.goodsShareImgList || !Object.keys(objSpuShareBGImg.goodsShareImgList).length) {
      console.log(`[ShareCard Performance] COS无有效配置，返回空 - 耗时: ${Date.now() - customBgStartTime}ms`)
      return ''
    }

    shareCardCache.goodsShareImgList = objSpuShareBGImg.goodsShareImgList || {}
    // 过期时间为5分钟
    shareCardCache.expireTime = Date.now() + 1000 * 60 * 5

    const result = shareCardCache.goodsShareImgList[spuNumber] || ''
    console.log(`[ShareCard Performance] 自定义分享背景图获取完成 - spuNumber: ${spuNumber}, 有结果: ${!!result}, 总耗时: ${Date.now() - customBgStartTime}ms`)
    return result
  }
}

