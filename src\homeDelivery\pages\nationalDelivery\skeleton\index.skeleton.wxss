/*
此文件为开发者工具生成，生成时间: 2021/11/9 上午9:55:18

在 /Users/<USER>/djh-project/pagoda/stash2/wxapp/homeDelivery/pages/nationalDelivery/index.wxss 中引入样式
```
@import "./index.skeleton.wxss";
```

更多详细信息可以参考文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/skeleton.html
*/
.sk-transparent {
    color: transparent !important;
  }
.sk-text-13-6364-799 {
    background-image: linear-gradient(transparent 13.6364%, #EEEEEE 0%, #EEEEEE 86.3636%, transparent 0%) !important;
    background-size: 100% 44.0000rpx;
    position: relative !important;
  }
.sk-text {
    background-origin: content-box !important;
    background-clip: content-box !important;
    background-color: transparent !important;
    color: transparent !important;
    background-repeat: repeat-y !important;
  }
.sk-text-16-6667-552 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 30.0000rpx;
    position: relative !important;
  }
.sk-text-14-2857-975 {
    background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
    background-size: 100% 30.8000rpx;
    position: relative !important;
  }
.sk-text-0-0000-593 {
    background-image: linear-gradient(transparent 0.0000%, #EEEEEE 0%, #EEEEEE 100.0000%, transparent 0%) !important;
    background-size: 100% 32.0000rpx;
    position: relative !important;
  }
.sk-text-13-6364-501 {
    background-image: linear-gradient(transparent 13.6364%, #EEEEEE 0%, #EEEEEE 86.3636%, transparent 0%) !important;
    background-size: 100% 44.0000rpx;
    position: relative !important;
  }
.sk-text-16-6667-5 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 30.0000rpx;
    position: relative !important;
  }
.sk-text-14-2857-304 {
    background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
    background-size: 100% 30.8000rpx;
    position: relative !important;
  }
.sk-text-0-0000-833 {
    background-image: linear-gradient(transparent 0.0000%, #EEEEEE 0%, #EEEEEE 100.0000%, transparent 0%) !important;
    background-size: 100% 32.0000rpx;
    position: relative !important;
  }
.sk-text-13-6364-798 {
    background-image: linear-gradient(transparent 13.6364%, #EEEEEE 0%, #EEEEEE 86.3636%, transparent 0%) !important;
    background-size: 100% 44.0000rpx;
    position: relative !important;
  }
.sk-text-16-6667-50 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 30.0000rpx;
    position: relative !important;
  }
.sk-text-14-2857-907 {
    background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
    background-size: 100% 30.8000rpx;
    position: relative !important;
  }
.sk-text-0-0000-692 {
    background-image: linear-gradient(transparent 0.0000%, #EEEEEE 0%, #EEEEEE 100.0000%, transparent 0%) !important;
    background-size: 100% 32.0000rpx;
    position: relative !important;
  }
.sk-text-13-6364-994 {
    background-image: linear-gradient(transparent 13.6364%, #EEEEEE 0%, #EEEEEE 86.3636%, transparent 0%) !important;
    background-size: 100% 44.0000rpx;
    position: relative !important;
  }
.sk-text-16-6667-94 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 30.0000rpx;
    position: relative !important;
  }
.sk-text-14-2857-682 {
    background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
    background-size: 100% 30.8000rpx;
    position: relative !important;
  }
.sk-text-0-0000-525 {
    background-image: linear-gradient(transparent 0.0000%, #EEEEEE 0%, #EEEEEE 100.0000%, transparent 0%) !important;
    background-size: 100% 32.0000rpx;
    position: relative !important;
  }
.sk-text-13-6364-171 {
    background-image: linear-gradient(transparent 13.6364%, #EEEEEE 0%, #EEEEEE 86.3636%, transparent 0%) !important;
    background-size: 100% 44.0000rpx;
    position: relative !important;
  }
.sk-text-16-6667-33 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 30.0000rpx;
    position: relative !important;
  }
.sk-text-14-2857-191 {
    background-image: linear-gradient(transparent 14.2857%, #EEEEEE 0%, #EEEEEE 85.7143%, transparent 0%) !important;
    background-size: 100% 30.8000rpx;
    position: relative !important;
  }
.sk-text-0-0000-867 {
    background-image: linear-gradient(transparent 0.0000%, #EEEEEE 0%, #EEEEEE 100.0000%, transparent 0%) !important;
    background-size: 100% 32.0000rpx;
    position: relative !important;
  }
.sk-text-16-6667-957 {
    background-image: linear-gradient(transparent 16.6667%, #EEEEEE 0%, #EEEEEE 83.3333%, transparent 0%) !important;
    background-size: 100% 30.0000rpx;
    position: absolute !important;
  }
.sk-image {
    background: #F5F5F5 !important;
  }
.sk-rect {
    border-radius: 0;
  }
.sk-pseudo::before, .sk-pseudo::after {
      background: #EFEFEF !important;
      background-image: none !important;
      color: transparent !important;
      border-color: transparent !important;
    }
.sk-pseudo-rect::before, .sk-pseudo-rect::after {
      border-radius: 0 !important;
    }
.sk-pseudo-circle::before, .sk-pseudo-circle::after {
      border-radius: 50% !important;
    }
.sk-container {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: transparent;
  }
 
    @keyframes flush {
      0% {
        left: -100%;
      }
      50% {
        left: 0;
      }
      100% {
        left: 100%;
      }
    }
    .sk-loading {
      animation: flush 2s linear infinite;
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background: linear-gradient(to left, 
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, .85) 50%,
        rgba(255, 255, 255, 0) 100%
      )
    }
  
