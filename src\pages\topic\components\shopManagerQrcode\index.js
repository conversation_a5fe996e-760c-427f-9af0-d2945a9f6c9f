import { getManagerQrCode } from './service/index'
import config from '../../../../utils/config'
Component({
  properties: {
    picUrl: {
      type: String,
      value: ''
    }
  },
  lifetimes: {
    async attached() {
      const url = await getManagerQrCode()
      this.setData({
        qrcodeUrl: url,
        domain: config.baseUrl.PAGODA_PIC_DOMAIN
      })
    }
  },
  data: {
    qrcodeUrl: '',
    domain: ''
  },
  methods: {

  }
})

