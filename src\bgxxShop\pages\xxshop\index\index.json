{"usingComponents": {"noservice": "../../../../components/bgxx/noCityService/index", "goods-preview": "../../../../componentsSubPackage/bgxx/goodsPreview/goodsPreview", "main-page": "../../../../components/base/main-page/index", "refresher-nav-bar": "../components/refresher-nav-bar/index", "sticky-nav-bar": "../components/sticky-nav-bar/index", "bounce-tips": "../../../../componentsSubPackage/bgxx/bounce-tips", "dialog": "../components/dialog/dialog", "pagoda-search-box": "../components/pagoda-search-box/index", "slide-layer": "../../../../components/slideLayer/index", "pagoda-split-banner": "../../../../componentsSubPackage/pagoda-split-banner/index", "pagoda-card-package": "../../../../componentsSubPackage/pagoda-card-package/index", "pagoda-banner-swiper": "../components/pagoda-banner-swiper/pagoda-banner-swiper", "pagoda-metro": "../../../../components/bgxx/pagoda-metro/pagoda-metro", "floating": "../../../../pages/homeDelivery/components/floating/index", "waterfallGoods": "../components/waterfallGoods/index", "waterfall-tabbar": "../components/waterfallGoods/waterfallTabbar/index", "dialog-coupon": "../components/dialog-coupon/index", "cart-popup": "../../../../componentsSubPackage/shopCart-popup/index", "showcase-banner": "../components/showcase-banner", "waist-banner": "../components/waist-banner", "address-tips": "../components/address-tips"}, "componentPlaceholder": {"pagoda-split-banner": "view", "pagoda-card-package": "view", "bounce-tips": "view", "cart-popup": "view", "goods-preview": "view"}, "disableScroll": true, "onReachBottomDistance": 100, "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "white", "navigationStyle": "custom"}