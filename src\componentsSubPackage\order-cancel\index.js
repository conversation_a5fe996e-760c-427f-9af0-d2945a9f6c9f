// components/order/order-cancel/index.js
var common = require('../../source/js/common');
const util = require('../../utils/util');
let app = getApp();
const radioColor = {
  wxMiniOrder: "#008C3C",
  bgxxOrder: "#FF1F3A"
}
Component({
  properties: {
    // 使用订单来源，默认百果园+订单取消使用 素生鲜订单取消使用传 bgxxOrder , 全国送订单取 b2cOrder
    orderSource: {
      type: String,
      value: 'wxMiniOrder'
    },
    orderID: {
      type: String,
      value: ''
    },
    showedCancelTitle: {
      type: String,
      value: ''
    }
  },
  data: {
    pageIsReady: true, // 页面是否加载完成
    popupIsShow: true,  // 弹窗是否显示

    cancelReasonList: [], // 原因列表
    cancelID: null, // 用户取消订单原因ID
    cancelReason: null, // 用户取消订单原因
    canceIsValid: false, // 表单是否检验通过

    textareaIsShow: false, // 输入框获是否显示
    textareaIsFocus: false, // 输入框获取焦点
    textareaValue: '', // 输入框内容
    textareaMaxlength: 100, // 输入框内容长度
    radioColor: '#008C3D'
  },
  lifetimes: {
    attached: function () {
      this.textareaIsFirstShow = true
      // this.setData({
      //   radioColor: radioColor[this.data.orderSource]
      // })
      this.orderCancelReasonList()
    }
  },
  methods: {
    /**
     * @description - 判断是否登录
     */
    hasLogin () {
      return app.checkSignInsStatus()
    },
    /**
     * @description - 请求数据报错
     */
    showModal(errorMsg) {
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 3000
      })
      // commonObj.showModal('提示', errorMsg, false, '我知道了')
    },
    /**
     * @description - 获取用户取消订单原因列表 v2.4.0
     */
    orderCancelReasonList() {
      let vm = this
      if (!vm.hasLogin()) {
        return
      }
      wx.showLoading({
        title: '加载中'
      })
      const requestPromise = this.getCancelReasonList(this.data.orderSource)
      requestPromise.then((res) => {
        wx.hideLoading()
        let { errorCode, errorMsg, data: cancelReasonList } = res
        // 数据报错
        if (Number(errorCode) !== 0) {
          vm.showModal(errorMsg);
          return
        }
        // 设置原因列表数据
        vm.setData({
          cancelReasonList,
          pageIsReady: true
        })
      }).catch((res) => {
        wx.hideLoading()
        vm.showModal(res.description || '系统异常，请稍后再试')
      })
    },
    /**
     * @desc 返回获取取消订单原因列表的接口
     * @param {String} orderType 订单类型
     */
    getCancelReasonList (orderType) {
      const fetchApi = {
        //  及时达
        wxMiniOrder: 'orderCancelReasonList',
        //  次日达
        bgxxOrder: 'bgxxGetOrderCancelReason',
        //  全国送
        b2cOrder: 'getB2CCancelReasonList'
      }
      const fetchData = {
        bgxxOrder: { orderID: this.data.orderID},
        b2cOrder: {
          customerID: (wx.getStorageSync('user') || {}).userID
        }
      }
      return app.api[fetchApi[orderType]](fetchData[orderType] || {})
    },
    /**
     * @description - 验证用户表单
     * @returns {boolean}
     */
    formValidate (cancelID, cancelReason, textareaValue) {
      let vm = this
      let result = {errorCode: 1, errorMsg: ''} // 1默认成功, 0失败
      let textareaIsShow = vm.userDoInput(cancelID)
      // 用户手动输入
      if (textareaIsShow) {
        if (!textareaValue || textareaValue.length < 3) {
          result.errorCode = 0
          result.errorMsg = '请输入3个字以上'
        }
        return result
      }
      // 选择原因-用户未选择
      result.errorCode = Number((Boolean(cancelReason) && Boolean(cancelID)))
      return result
    },
    /**
     * @description - 是否用户手动输入
     * @param {string} - 取消原因
     * @returns {boolean}
     */
    userDoInput (cancelID) {
      return cancelID === 99
    },
    /**
     * @description - 当textarea键盘输入时，触发 input 事件
     * @param {object} e - 原生事件
     */
    onTextAreaInput (e) {
      let vm = this
      let {value: textareaValue} = e.detail
      if (!!textareaValue) {
        // 首位空格
        textareaValue = textareaValue.replace(/(^\s*)|(\s*$)/g, "")
        // 表情符号
        textareaValue = textareaValue.replace(/[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/ig, "")
      }
      let {cancelID, cancelReason} = vm.data
      let formValidation = vm.formValidate(cancelID, cancelReason, textareaValue)
      this.setData({
        textareaValue,
        canceIsValid: Boolean(formValidation.errorCode)
      })
    },
    /**
     * @description - 当radio选中项发生改变时触发 change 事件
     * @param {object} e - 原生事件
     */
    onRadioChange (e) {
      let vm = this, textareaIsFocus, textareaIsShow
      let {cancelreason: cancelReason, cancelid: cancelID } = e.currentTarget.dataset
      let {textareaValue, cancelID: _cancelID} = vm.data
      if (_cancelID && _cancelID === cancelID) {
        return
      }
      let formValidation = vm.formValidate(cancelID, cancelReason, textareaValue)
      textareaIsFocus = textareaIsShow = vm.userDoInput(cancelID)
      let textareaView = textareaIsShow ? 'textareaView' : ''
      // 1. 先打开输入框区域(textareaIsShow),
      //    显示view标签(默认)
      vm.setData({
        cancelID,
        cancelReason,
        textareaIsShow,
        canceIsValid: Boolean(formValidation.errorCode),
        textareaView
      })
      if (textareaIsShow) {
        wx.nextTick(() => {
          this.setData({textareaIsFocus: true})
        })
      }
    },
    /**
     * @description - 点击"确认取消"
     */
    onConfirmCancel: util.debounce(function() {
      let vm = this
      let {cancelReason, cancelID, textareaValue} = vm.data
      let formValidation = vm.formValidate(cancelID, cancelReason, textareaValue)
      // 验证通过
      if (!!formValidation.errorCode) {
        // “其他” 选项需要赋值用户填写内容；其他默认原来选项文字
        cancelReason = vm.userDoInput(cancelID) ? textareaValue : cancelReason
        vm.triggerEvent('select', {cancelReason, cancelID})
        return
      }
      // 验证失败
      formValidation.errorMsg && vm.showModal(formValidation.errorMsg)
    }, 300),
    /**
     * @description - 点击"确认取消"
     */
    onCloseCancel () {
      this.triggerEvent('close')
    }
  }
})
