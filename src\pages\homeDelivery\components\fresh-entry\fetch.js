const app = getApp();

/**
 * @desc 次日达腰部入口，获取次日达广告位
 */
export async function getFreshEntry(userInfo, addressInfo) {
  const { customerID } = userInfo || {}
  const { cityID, storeID } = addressInfo || {}

  let result = {}

  if (!cityID || cityID === -1) {
    return result
  }
  const params = {
    customerID: String(customerID || -1),
    cityID: Number(cityID),
    storeID: Number(storeID || -1),
    isOldSize: false,
  }

  try {
    const res = await app.api.getFreshEntry(params)
    if (res.data) {
      const { type, value, imageUrl } = res.data
      const freshEntryObj = {
        openType: type,
        openValue: value,
        picUrl: imageUrl,
        name: '次日达入口'
      }
      result = freshEntryObj
    }
  } catch (error) {
    console.info('getFreshEntry error', error)
  }
  return result
}
