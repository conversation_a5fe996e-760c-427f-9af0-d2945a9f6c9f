
/* 收货信息 start */
.receive-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 32rpx 24rpx 40rpx;
  background-color: #FFFfff;
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
}
.receive-info-label{
  font-size: 28rpx;
  color: #222222;
  line-height: 40rpx;
  margin-right: 24rpx;
}
.receive-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.logistics-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #222222;
}
.logistics-order-no {
  margin-left: 32rpx;
}
.logistics-company {
  flex-shrink: 0;
}
.logistics-company,
.logistics-order-no{
  font-weight: bold;
}
.btn-copy {
  margin-left: 8rpx;
  width: 72rpx;
  /* height: 28rpx; */
  text-align: center;
  line-height: 26rpx;
  font-size: 20rpx;
  color: #555555;
  border: 1rpx solid rgba(85, 85, 85, .5);
  border-radius: 14rpx;
}
.receive-address,
.receive-time {
  font-size: 26rpx;
  color: #888888;
}
/* 收货信息 end */

/* 物流进度 start */
.logistics-detail {
  min-height: 100%;
  padding: 33rpx 24rpx 0 24rpx;
  background-color: #FFFfff;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}
.logistics-progress {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 0 0 36rpx 76rpx;
}
.logistics-progress::before {
  content: '';
  position: absolute;
  top: 20rpx;
  left: 26rpx;
  width: 16rpx;
  height: 16rpx;
  transform: translate(-50%, -50%);
  background-color: #DDDDDD;
  border-radius: 50%;
  z-index: 2;
}
.logistics-progress.active .logistics-msg{
  color: #222222;
}
.logistics-progress.active::before {
  width: 52rpx;
  height: 52rpx;
  background-image: url('https://resource.pagoda.com.cn/group1/M0F/34/CA/CmiLlF_EnOeAfqcdAAAEe09eB2s087.png');
  background-size: 100% 100%;
}
.logistics-progress::after {
  content: '';
  position: absolute;
  top: 20rpx;
  left: 26rpx;
  width: 1rpx;
  height: 100%;
  background-color: #ECEEF5;
}
.logistics-progress.active::after {
  background-color: #CCCCCC;
}
.logistics-progress:last-child::after {
  display: none;
}
.logistics-msg {
  font-size: 30rpx;
  color: #858996;
}
.logistics-time {
  font-size: 24rpx;
  color: #858996;
}
/* 物流进度 end */

/* 暂无物流进度 start */
.no-logistics {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 72rpx;
  width: 100%;
  font-size: 30rpx;
  color: #888888;
}
.no-logistics-img {
  width: 400rpx;
  height: 400rpx;
}
/* 暂无物流进度 end */

/* 包裹内商品 start */
.package-goods {
  display: flex;
  align-items: center;
  padding: 24rpx 24rpx 0;
  width: 100%;
  height: 112rpx;
  background-color: #fff;
}
.package-goods-title {
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #000000;
  flex-shrink: 0;
}
.package-goods-box {
  padding-left: 20rpx;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
}
.package-goods-img {
  margin-left: -20rpx;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
}
/* 包裹内商品 end */
