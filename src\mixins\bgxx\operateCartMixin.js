/**
 * 心享买好菜，专题页，凑单页，支付成功页相关加入购物车及加入动画，banner跳转等公共方法集合
 */
import {
    jumpH5Vip
} from '../../utils/services/jumpBgxxVip'
import {successCode, freshErrorCode } from '../../source/const/cartCode'
import util from '../../utils/util.js'
import cartCount from './cartCount'
const app = getApp()
const config = require('../../utils/config')
const { toActivityPage } = require('../../utils/activity')
import storeBinding from './storeBinding'
import specialStore from '../../stores/module/special'
import shopCartStore from '../../stores/shopCartStore'
import { freshCartStore } from '../../stores/module/freshCart'
import { ORDER_TYPE } from '../../source/const/order'
import { freshAddSuccessReport } from '../../utils/report/getSaleReportInfo'
import { allTrue, defaultVal, getStorage } from '../../utils/cyclomatic'
/**
 * 心享专享弹窗
 * @param { string } description 弹窗内容
 * @returns { boolean } 是否弹窗或者是否需要登录
 */
function vipModal(description) {
    const isLogin = app.checkSignInsStatus( {})
    const needVip = isLogin && app.globalData.superVipStatus === 'C'
    needVip && wx.showModal({
        title: '',
        content: description || '此活动仅限心享会员才可参加哦~',
        cancelColor: '#666666',
        confirmText: '立即开通',
        confirmColor: config.themeColorConfig.fruit,
        success(res) {
            if (res.confirm) {
                jumpH5Vip()
            }
        }
    })
    isLogin || app.signIn()
    return needVip || !isLogin
}

module.exports = {
    data: {
      cartCount: 0,
      searchAdConfig: { // 搜索框广告语
          isDefault: true,
          text: ''
      },
      needCartAnimate: false
    },
    ...cartCount,
    mixins:[storeBinding],
    /**
     * 加减购物车
     * beforHttp: 发送http
     * @param {Object} detail 商品卡片组件回调参数 {event, goodsData}
     * @param {Function} success 添加成功回调参数
     */
    addCart({
      detail,
      currentTarget
    }, data) {
        const { success = () => {}, beforeRequest = '', fail, complete } = data || {}
        const {
            event,
            goodsData,
            type = '1',
            oneParams = {}
        } = detail
        this.moduleIndex = currentTarget ? currentTarget.dataset.moduleIndex || -1 : -1
        if (this.checkAddCartValid(goodsData, type)) {
          this.requestAddCar({
              goodsData,
              event,
              success,
              type,
              beforeRequest,
              oneParams,
              fail,
              complete
          })
        }
    },
    requestAddCar: util.throttle(function(params) {
      this.setCartGoods(params)
    }, 200),

    onAddCartSuccess({
        tipCode,
        tip,
        success,
        goodsData,
        type,
        res,
        duration,
        cartCount,
    }) {
        // 能正常加购 但需做提示
        if (tipCode) {
            wx.showToast({
                title: tip,
                icon: 'none',
                duration
            })
        }
        success && setTimeout(() => {
            success.call(this, {
                goodsID: goodsData.id,
                type,
                params: util.deepClone(res.data)
            })
        }, duration)
        if (Number(type) === 1) {
            // 不用设置购物车动画了
            // if (this.setData) {
            //   this.setData({
            //       'movingBallInfo.pic': findValue([goodsData.recommendPic, goodsData.headPic], goodsData.mainImgUrl)
            //   })
            // }
            this.setCount(cartCount)
            if (!this.data.needCartAnimate) {
            // 不用设置购物车动画了
            //     this.startAnimation(event)
            // } else {
                tipCode || setTimeout(function() {
                  wx.nextTick(function() {
                    wx.showToast({
                      title: '加入购物车成功',
                      icon: 'none',
                      duration: 1500
                    })
                  })
                }, 0)
            }
            freshAddSuccessReport({ goodsObj: goodsData, count: 1, goodsActivityObj: this.data.goodsActivityObj })
        }
    },

    async onAddCartError({
        tipCode,
        tip,
        fail,
        duration,
    }) {
        if (Number(tipCode) === 35821) {
            wx.showModal({
                title: '',
                content: tip,
                showCancel: false,
                confirmColor: config.themeColorConfig.vegetables,
                confirmText: '知道了',
                duration
            })
        } else if(Number(tipCode) === 35809){ // 商品下架 重新刷一下购物车  新客不能加购新客商品
            const { cartCount = 0 } = await app.loadShopCart()
            this.setCount(cartCount)
             wx.showToast({
                title: tip,
                icon: 'none',
                duration
            })
        } else {
            wx.showToast({
                title: tip,
                icon: 'none',
                duration
            })
        }
        fail && setTimeout(() => {
            fail.call(this, {
                errorCode: tipCode,
                description: tip
            })
        }, duration)
    },

    async setCartGoods(params) {
        const {
            goodsData,
            // event,
            success,
            type = 1,
            beforeRequest,
            fail,
            complete
        } = params
        let _complete = function() {}
        try {
            const {
                customerID,
                bgxxCityInfo: {
                    cityID = '',
                    storeID = 0,
                    storeCode,
                    cityCode,
                    deliveryCenterCode
                } = {}
            } = app.globalData
            const activityCode = specialStore.getActiviyCode({ goodsId: goodsData.id, specialInfo: goodsData.specialInfo })
            const paramsData = {
                cartUuId: app.getOperateCartUnId(),
                customerID: defaultVal(customerID, -1),
                cityID,
                storeID: defaultVal(storeID, '-1'),
                storeCode,
                type,
                goodsInfo: {
                    goodsID: goodsData.id,
                    activityCode
                },
                count: 1,
                cityCode,
                deliveryCenterCode
            }
            if (!allTrue(cityID, cityCode)) {
                // 由于及时达，次日达定位检查城市服务是分开逻辑，
                // 如果次日达加购时没有城市，则用及时达的城市（主要是避免有些及时达页面出现次日达商品，无法正常加购）
                const { cityID, deliveryCenterCode, cityCode } = getStorage('timelyCity')
                Object.assign(paramsData, {
                  cityID,
                  deliveryCenterCode,
                  cityCode
                })
            }
            beforeRequest && beforeRequest.call(this, goodsData)
            const res = await app.api.setBgxxCartCount(paramsData)
            shopCartStore.setTopCart({ freshGoods: goodsData })
            if (!res.data) {
                return
            }
            freshCartStore.resultHandler(util.deepClone(res))
            const {
                tipCode = -1, cartCount = 0, tip = ''
            } = defaultVal(res.data, { tipCode: -1, tip: '网络错误' })
            const refreshPage = freshErrorCode.includes(tipCode)
            const addSuccess = successCode.includes(tipCode)
            const duration = tipCode ? 1500 : 0
            specialStore.updateEndStatus({ tipCode, activityCode })
            if (addSuccess) {
                this.onAddCartSuccess({
                    tipCode,
                    tip,
                    success,
                    goodsData,
                    type,
                    res,
                    duration,
                    cartCount,
                })
            } else {
                await this.onAddCartError({
                    tipCode,
                    tip,
                    fail,
                    duration,
                })
            }
            if(Number(tipCode) !== 35809){ // 非下架情况下刷新数据 防止下架情况下刷新导致角标会闪现 35809商品下架
                app.updateFreshGoodsCartList(res.data)
                this.setCount(cartCount)
            }
            if(tipCode === 35822){ // 非下架情况下刷新数据 35822 新客加购无法在加购新客商品需
                const { cartCount = 0 } = await app.loadShopCart()
                this.setCount(cartCount)
                wx.showToast({
                  title: tip,
                  icon: 'none',
                  duration
                })
            }
            complete && (_complete = () => {
                setTimeout(() => {
                    complete.call(this, {
                        addSuccess,
                        errorCode: tipCode,
                        description: tip,
                        refreshPage
                    })
                }, duration)
            })
        } catch (e) {
            if (e && e.errorCode === 35832) {
                vipModal(e.description)
            } else {
                app.apiErrorDialog(e)
            }
            fail && fail.call(this, e)
            complete && (_complete = complete.bind(this, e))
        }
        _complete()
    },
    /**
     * 心享专享商品添加购物车合法校验
     * @param {Object} goodsData 商品数据
     */
    checkAddCartValid({ buyerType, isNewCustomerAvailable, isNewExclusive }, type = 1) {
      const isAddCart = Number(type) === 1
      // 只在加购时校验
      if (!isAddCart) { return true }
      // 新客专享商品校验
      if ((isNewCustomerAvailable || isNewExclusive === 'Y') && !app.checkSignInsStatus()) { app.signIn(); return false }
      // 校验心享vip
      if (buyerType === 'V') { return !vipModal('此商品仅限心享会员才可购买哦~') }
      return true
    },
    /**
     * 后台配置商品不同场景跳转处理
     * @param {Object} data 商品数据
     * business	：0 所有， 1 次日达，2 及时达
     */
    configBannerToSkip: app.subProtocolValid({
        type: 'memberService' ,
        conditionFn (data) {
            const { openType, openValue } = data

            //  仅自定义页面进行校验
            if (Number(openType) === 17) {
                //  自定义页面只有外部页面 或 签到页进行校验
                return openValue.includes('http') || ['/userA/pages/checkIn/index'].includes(openValue)
            }
        }
    }, function (data, signInFlag) {
        const {
            openType = '',
            openValue = '',
            passthrough = []
        } = data
        if (openValue === 'allCate') {
            wx.navigateTo({
                url: '/bgxxShop/pages/category/index'
            })
            return
        }
        let url = ''
        switch (Number(openType)) {
            case 65: // 新中台品类
            {
                url = `/bgxxShop/pages/category/index?categoryCode=${openValue}`
                break;
            }
            case 3: // 商品详情
            {
                app.globalData.prePageName = 'goodDetail'
                url = `/bgxxShop/pages/goodDetail/index?toDetail=${JSON.stringify({goodsID: openValue})}`
                break;
            }
            case 63: // 商品详情
            {
                app.globalData.prePageName = 'goodDetail'
                url = `/bgxxShop/pages/goodDetail/index?toDetail=${JSON.stringify({goodsSn: openValue})}`
                break;
            }
            case 17: // 自定义页面
            {
                if (openValue.indexOf("http") > -1) {
                    let pageParam = {
                        pageUrl: encodeURIComponent(openValue)
                    }
                    // if (!app.checkSignInsStatus( {})) {
                    //     app.signIn()
                    //     if (signInFlag) {
                    //         this.data.signInFlag = true
                    //         this.data.defineUrl = `/h5/pages/bgxxSkipH5/index?pageUrl=${encodeURIComponent(openValue)}`
                    //     }
                    //     return
                    // }
                    // url = `/h5/pages/bgxxSkipH5/index?pageUrl=${encodeURIComponent(openValue)}`
                    url = `/h5/pages/commonh5/index?pageParam=${JSON.stringify(pageParam)}`
                } else {
                    url = openValue
                }
                app.globalData.prePageName = 'commonLink'
                break;
            }
            case 19:
            case 52: // 综合专题
            {
                app.globalData.prePageName = 'subjectActivity'
                url = `/bgxxShop/pages/subjectActivity/index?activityID=${openValue}`
                break;
            }
            case 20: {
                toActivityPage(data)
                return
            }
            case 68: // 及时达配置综合专题
            {
                app.globalData.prePageName = 'subjectActivity'
                const businessType = util.getObjectValue(passthrough[0],'business') // 2为及时达 1 为次日达
                url = businessType === 2 ? `/pages/topic/index?homeDeliveryObj=${JSON.stringify({activityID:openValue })}`:`/bgxxShop/pages/subjectActivity/index?activityID=${openValue}&isFromHomeDelivery=true`
                break;
            }
            case 1:
            case 13: // 账户充值页
            {
                if (!app.checkSignInsStatus( {})) {
                    app.signIn()
                    return
                }
                app.globalData.prePageName = 'deposit'
                url = '/userA/pages/deposit/index'
                break;
            }
            case 7: // 开通心享
            {
                jumpH5Vip()
                return
            }
            case 8:
            case 35: // 跳转其他小程序
            {
                if (data.miniProgram === 'wx1f9ea355b47256dd') {
                    // 跳转当前小程序
                    url = openValue
                    break;
                } else {
                    const params = {
                        appId: data.miniProgram,
                    };
                    !!openValue && Object.assign(params, {
                        path: openValue
                    })
                    wx.navigateToMiniProgram(params);
                    return
                }
            }
            case 64: // 跳首页
            {
                url = '/pages/xxshop/index/index'
                break;
            }
        };
        if (!!url) {
            //跳转品类页
            if(Number(openType)===58){
                wx.navigateTo({
                  url
                })
                return
            }
            if (config.tabBarPage.includes(url)) {
                wx.switchTab({
                  url
                })
            } else {
                wx.navigateTo({
                    url
                })
            }
        }
    }),
    /**
     * 设置当前商品状态为不可添加
     * @param {Object} data { list, id, errorCode }
     * @param {String} prefixKey 设置data前缀
     */
    setDisebledGoods(data, prefixKey) {
        const {
            list,
            id,
            errorCode
        } = data
        const {
            goodsList = [],
                goodsOutputList = []
        } = list[this.moduleIndex]
        const listMap = {
            goodsList: goodsList,
            goodsOutputList: goodsOutputList
        }
        const listKey = goodsList && goodsList.length > 0 ? 'goodsList' : 'goodsOutputList'
        for (let j = 0, num = listMap[listKey].length; j < num; j++) {
            if (listMap[listKey][j].id === id) {
                const key = `${prefixKey}[${this.moduleIndex}].${listKey}[${j}]`
                const dataObj = list[this.moduleIndex][listKey][j]
                switch (errorCode) {
                    case 35804:
                        {
                            // 达到限购
                            this.setData({
                                [key]: {
                                    ...dataObj,
                                    residualEffectNum: 0
                                }
                            })
                            break
                        }
                    case 35809:
                        {
                            // 下架
                            this.setData({
                                [key]: {
                                    ...dataObj,
                                    shelfStatus: 'D'
                                }
                            })
                            break
                        }
                    default:
                        {
                            // 售罄
                            this.setData({
                                [key]: {
                                    ...dataObj,
                                    stockNum: 0
                                }
                            })
                        }
                }
                return
            }
        }
    },
    /**
     * 进入购物车页面
     */
    enterCartPage() {
        app.globalData.prePageName = 'cart'
        // 原次日达购物车改为及时达购物车
        wx.switchTab({
          url: '/pages/shopCart/index'
        })
    },
    /**
     * 获取搜索框广告语
     *@params  'orgCode':机构编号
     *  @params  `type`:'类型：1首页banner，2首页metro，3首页品牌栏，4热点banner，5切分banner，6首页推荐广告位，7首页floating，8首页弹窗，9付款成功页，10搜索框广告语，11热搜广告词',
     *  @params  `orgLevel`:'机构级别:0全国，1大区，2配送中心，3城市，4门店',
     *  @params  `orgType`:'机构来源类型：1自营+生鲜; 2心享电商',
     *  @params  `userType`: '用户类型：0 所有用户(默认值)，1 新用户，2 老用户'
     *  @params  `business`: 业务：0 所有， 1 次日达，2 及时达
     *  @params  `clientType`: 客户端：1百果园+，2百果园APP
     */
    async getBgxxSearchAd() {
      let searchAdConfig = {
          isDefault: true,
          text: '请输入您要搜索的商品'
      }
      console.log('app.globalData',app.globalData)
      const {
          bgxxCityInfo: {
            cityCode = '',
            storeCode
          } = {},
          customerID
      } = app.globalData
      const params = {
        organizationCode:cityCode,
        customerID: customerID || -1,
        storeCode
      }
      if (!!cityCode) {
          try {
              const res = await app.api.getBgxxSearchAd(params)
              if (!!res.data && !!res.data.msg) {
                  searchAdConfig = {
                      ...searchAdConfig,
                      isDefault: false,
                      text: res.data.msg
                  }
              }
          } catch (error) {}
      }
      this.setData({
          searchAdConfig
      })
  },
    async locateNoContentTip() {
        const res = await app.showModalPromise({
            content: `${this.data.currentView === 'noLocationAuth' ? "定位权限未开启" : "当前城市未开通服务"}`,
            confirmText: '回首页',
        })
        if (res) {
            wx.navigateTo({ url: '/pages/xxshop/index/index' });
        }
    }
}
