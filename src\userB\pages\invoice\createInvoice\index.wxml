<wxs module="filter" src="../../../../utils/common.wxs"></wxs>
<wxs module="fn">
  module.exports = {
    /**
     * 获取抬头输入框placeholder
     */
    getTitlePlaceholder: function(invoiceTypeIsCompany) {
      return invoiceTypeIsCompany ? '请输入完整公司名称' : '请输入抬头名称'
    },

    /**
     * 按钮禁止提交样式
     * formData无法以对象的形式传入，对象的形式传入属性值变化不会触发响应式更新（太坑了）
     */
    submitClassName: function(buyerInvoiceTitle, buyerTaxCode, buyerEmail, invoiceTypeIsCompany, isAgreeProtocal) {
      var invoicePass = invoiceTypeIsCompany ? buyerTaxCode : true

      if(!(buyerInvoiceTitle && invoicePass && buyerEmail && isAgreeProtocal)) {
        return 'submit-btn--disabled'
      }
    }
  }
</wxs>

<user-protocol />
<!-- 发票详情 -->
<view class="section">
  <form-cell title="发票金额" showRequireTip isStrongLabel>
    <view class="invoice-amount">
      <view class="amount">
        <text>{{ filter.formatPrice(drawableAmount) }}</text>元
      </view>
      <view wx:if="{{ isSingleOrder }}" class="operate" bindtap="toInvoiceDetail">
        <text>查看详情</text>
        <image src="https://resource.pagoda.com.cn/0/*****************/e5e633f8bd2b3ce37d24fe84a60c03bb.png" />
      </view>
    </view>
  </form-cell>

  <form-cell title="抬头类型" showRequireTip isStrongLabel>
    <view class="title-type-group">
      <view
        class="{{ invoiceTypeIsCompany ? 'title-type-item--isChecked' : '' }}"
        data-type="{{ TITLE_ENUM.company }}"
        bindtap="chooseInvoiceType">企业单位</view>
      <view
        class="{{ invoiceTypeIsCompany ? '' : 'title-type-item--isChecked' }}"
        data-type="{{ TITLE_ENUM.single }}"
        bindtap="chooseInvoiceType">个人/非企业单位</view>
    </view>
  </form-cell>

  <view class="title-row">
    <form-cell title="发票抬头" showRequireTip isStrongLabel>
      <view style="display: flex">
        <view class="input-wrap">
          <input
            type="text"
            placeholder="{{ fn.getTitlePlaceholder(invoiceTypeIsCompany) }}"
            placeholder-class="placeholder-style"
            value="{{ formData.buyerInvoiceTitle }}"
            bindinput="titleChange"
            data-field="buyerInvoiceTitle"
            maxlength="{{ invoiceTypeIsCompany ? '35' : '30' }}"
          />

          <image
            wx:if="{{ formData.buyerInvoiceTitle }}"
            bindtap="clearHandle"
            data-field="buyerInvoiceTitle"
            src="./bg_close_addr.png" />
        </view>

        <view class="choose-wechat-invoice" bindtap="chooseWechatInvoice">
          <image class="wechat-icon" src="https://resource.pagoda.com.cn/0/35681663929516978/e5e633f8bd2b3ce37d24fe84a60c03bb.png" />
          <text>微信导入</text>
          <image class="arrow" src="https://resource.pagoda.com.cn/0/*****************/e5e633f8bd2b3ce37d24fe84a60c03bb.png" />
        </view>
      </view>
    </form-cell>

    <view wx:if="{{ showCompanyList }}" class="company-list">
      <view class="company-content">
        <view wx:for="{{ titleCompanyList }}"
              wx:for-item="company"
              wx:key="company"
              class="company-item"
              bindtap="chooseCompany"
              data-company="{{ company }}"
              >
          <view class="company-item__title">
            {{ company.title }}
          </view>
          <view class="company-item__taxCode">
            {{ company.taxCode }}
          </view>
        </view>
      </view>
      <view class="company-list-close" bindtap="closeCompanyList">
        <text>关闭</text>
      </view>
    </view>
  </view>

  <form-cell wx:if="{{ invoiceTypeIsCompany }}" title="公司税号" showRequireTip isStrongLabel>
    <view class="input-wrap">
      <input
        type="text"
        placeholder="请输入纳税人识别号"
        placeholder-class="placeholder-style"
        value="{{ formData.buyerTaxCode }}"
        bindinput="inputChangeHandle"
        data-field="buyerTaxCode"
        maxlength="20"
        />

      <image
        wx:if="{{ formData.buyerTaxCode }}"
        bindtap="clearHandle"
        data-field="buyerTaxCode"
        src="./bg_close_addr.png" />
    </view>
  </form-cell>

  <form-cell title="电子邮件" showRequireTip isStrongLabel>
    <view class="input-wrap">
      <input
        type="text"
        placeholder="用于向您发送电子发票"
        placeholder-class="placeholder-style"
        value="{{ formData.buyerEmail }}"
        bindinput="inputChangeHandle"
        data-field="buyerEmail" />

      <image
        wx:if="{{ formData.buyerEmail }}"
        bindtap="clearHandle"
        data-field="buyerEmail"
        src="./bg_close_addr.png" />
    </view>
  </form-cell>

  <form-cell wx:if="{{ isSingleOrder && orderType !== ORDER_TYPE.GIFTCARD }}">
    <view slot="title">
      <view class="wechat-cardBag">
        <image class="wechat-icon" src="https://resource.pagoda.com.cn/0/35681663929516978/e5e633f8bd2b3ce37d24fe84a60c03bb.png" />
        <text style="font-weight: bold;">微信卡包</text>
        <image src="{{ GOODSIMAGE.QUESTION_ICON }}" class="question" bindtap="showWxAuthCardBagTip" />

        <view>
          <view class="question-tip" wx:if="{{ showQuestionTip }}" bindtap="hideQuestionTip">
            <view>
              完成微信卡包授权后，发票开具成功
            </view>
            <view>
              后将直接插入至个人微信卡包
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="operate" bindtap="authorizeWxCardBag">
      <block wx:if="{{ isAuthWxCardBag }}">
        <text>已授权</text>
      </block>
      <block wx:else>
        <text class="auth">去授权</text>
        <image src="https://resource.pagoda.com.cn/0/*****************/e5e633f8bd2b3ce37d24fe84a60c03bb.png" />
      </block>
    </view>
  </form-cell>

  <block wx:if="{{ showMore }}">
    <form-cell title="联系地址" isStrongLabel>
      <view class="input-wrap">
        <input
          type="text"
          placeholder="填写联系地址"
          placeholder-class="placeholder-style"
          value="{{ formData.buyerCompanyAddress }}"
          bindinput="inputChangeHandle"
          data-field="buyerCompanyAddress"
          maxlength="30" />

        <image
          wx:if="{{ formData.buyerCompanyAddress }}"
          bindtap="clearHandle"
          data-field="buyerCompanyAddress"
          src="./bg_close_addr.png" />
      </view>
    </form-cell>

    <form-cell title="联系电话" isStrongLabel>
      <view class="input-wrap">
        <input
          type="number"
          placeholder="填写联系电话"
          placeholder-class="placeholder-style"
          value="{{ formData.buyerCompanyPhone }}"
          bindinput="inputChangeHandle"
          data-field="buyerCompanyPhone"
          maxlength="15" />

        <image
          wx:if="{{ formData.buyerCompanyPhone }}"
          bindtap="clearHandle"
          data-field="buyerCompanyPhone"
          src="./bg_close_addr.png" />
      </view>
    </form-cell>

    <form-cell title="开户行" isStrongLabel>
      <view class="input-wrap">
        <input
          type="text"
          placeholder="填写开户行"
          placeholder-class="placeholder-style"
          value="{{ formData.buyerBankName }}"
          bindinput="inputChangeHandle"
          data-field="buyerBankName"
          maxlength="20"
          />

        <image
          wx:if="{{ formData.buyerBankName }}"
          bindtap="clearHandle"
          data-field="buyerBankName"
          src="./bg_close_addr.png" />
      </view>
    </form-cell>

    <form-cell title="银行账号" isStrongLabel>
      <view class="input-wrap">
        <input
          type="number"
          placeholder="填写银行账号"
          placeholder-class="placeholder-style"
          value="{{ formData.buyerBankAccount }}"
          bindinput="inputChangeHandle"
          data-field="buyerBankAccount"
          maxlength="50"
          />

        <image
          wx:if="{{ formData.buyerBankAccount }}"
          bindtap="clearHandle"
          data-field="buyerBankAccount"
          src="./bg_close_addr.png" />
      </view>
    </form-cell>

    <form-cell title="备注说明" isStrongLabel>
      <view class="input-wrap">
        <input
          type="text"
          placeholder="填写备注说明"
          placeholder-class="placeholder-style"
          value="{{ formData.buyerRemark }}"
          bindinput="inputChangeHandle"
          data-field="buyerRemark"
          maxlength="80"
          />

        <image
          wx:if="{{ formData.buyerRemark }}"
          bindtap="clearHandle"
          data-field="buyerRemark"
          src="./bg_close_addr.png" />
      </view>
    </form-cell>

    <form-cell title="">
      <view class="operate" bindtap="previewExample">
        <text>查看示例</text>
        <image src="https://resource.pagoda.com.cn/0/*****************/e5e633f8bd2b3ce37d24fe84a60c03bb.png" />
      </view>
    </form-cell>
  </block>

  <view class="toggle-showMore {{ showMore ? 'toggle-showMore--open' : '' }}" bindtap="toggleShowMore">
    <text>{{ showMore ? '收起' : '更多内容(选填)' }}</text>
    <image src="https://resource.pagoda.com.cn/0/37641663052705994/fd456406745d816a45cae554c788e754.png" />
  </view>
</view>
<!-- 发票详情 -->

<view class="footer-height safe-area-inset-bottom">
  <!-- 提交 -->
  <view class="footer-wrap">
    <view class="agree-box">
      <radio-check
        checked="{{ isAgreeProtocal }}"
        size="30rpx"
        catchtap="agreeHandle" />
      <view class="agree-box-text">
        我已阅读并同意
      </view>
      <view
        class="agree-box-protocol"
        catchtap="navigateToPage">《发票服务个人信息授权声明》</view>
    </view>

    <view class="footer safe-area-inset-bottom">
      <view
        class="submit-btn {{ fn.submitClassName(formData.buyerInvoiceTitle, formData.buyerTaxCode, formData.buyerEmail, invoiceTypeIsCompany, isAgreeProtocal) }}"
        bindtap="beforeSubmit">
        提交
      </view>
    </view>
  </view>
  <!-- 提交 -->
</view>


<invoice-confirm-modal
  showModal="{{ showInvoiceConfirmModal }}"
  formList="{{ invoiceConfirmModalFormList }}"
  bindconfirm="invoiceConfirmModalSubmit"
  bindclose="closeInvoiceConfirmModal"
  />

<request-subscribe title="打开提醒，获得开票结果通知" show="{{ subscribe.show }}" tmpl-ids="{{ subscribe.tmplIds }}" bind:close="onSubscribeClose" />

<common-loading />

<confirm-modal
  confirmText="我知道了"
  isShowConfirmModal="{{ showConfirmModal }}"
  showCancel="{{ false }}"
  bindconfirm="modalConfirm">
  <view class="content" slot="content">
    本订单商品由<text>“深圳百果园实业(集团)股份有限公司914403007152447549”</text>销售，开具企业发票不可使用与销售方相同的企业作为开票抬头，请更换其他抬头或类型。
  </view>
</confirm-modal>
