// homeDelivery/pages/activity/index.js
import fruitCartStore from '~/stores/module/fruitCart'
import { tickEntryMixins } from '~/mixins/ticketEntry'
import { createStoreBindings } from 'mobx-miniprogram-bindings'
import { freshCartStore } from '~/stores/module/freshCart'
import { ORDER_TYPE } from '~/source/const/order'
import { checkUserIsDistributor, isDistributorFromCache } from '../../service/userService'
var commonObj = require('~/source/js/common.js').commonObj
const app = getApp()
const cartMinxins = require('~/mixins/cartMixin')
const locateMixin = require('~/mixins/locateMixin')
const vegetablesLocateMixin = require('~/mixins/vegetablesLocateMixin')
const checkInsertCoupon = require('~/mixins/checkInsertCoupon')
const sensors = require('~/utils/report/sensors')
const { debounce, throttle } = require('~/utils/util')
const { generateShareAttr, ShareScene } = require('~/utils/report/setup')
const activity = require('~/utils/activity')
const { tabBarPage } = require('~/utils/config')
const log = require('~/utils/log.js')
import { getOnlinePriceGoodsList } from '~/service/fruitGoods'
import { getWindowInfo } from '~/utils/wx/system'
// const {
//   FruitGoods
// } = require('~/service/fruitGoods')
const {
  FruitSubject
} = require('~/service/fruitSubject')


let obAnchor = null
Page({
  mixins: [cartMinxins, locateMixin, vegetablesLocateMixin, checkInsertCoupon, tickEntryMixins],
  /**
   * 页面的初始数据
   */
  data: {
    ORDER_TYPE,
    componentFeature: {
      2: 'single',
      3: 'double',
      6: 'treble'
    },
    activityID: 0,
    cityID: -1,
    storeID: -1,
    isShowList: false,
    isClearShop: false,
    topicList: [],
    picUrl: commonObj.PAGODA_PIC_DOMAIN,
    shareInfo: {},
    cartFlag: false,
    couponIsShow: false, // 微信支付券弹窗是否显示
    couponSending: false, // 发券中
    coupon: {}, // 卡券数据{send_coupon_merchant, send_coupon_merchant, sign}
    mainViews: { // 页面缺省页可能展示不同，可自由配置,勿删
      noLocationAuth: 'noLocationAuth',
      noStore: 'content',
      noCity: 'noStore',
      content: 'content'
    },
    currentView: '',
    zhuantiID: '',
    zhuantiName: '',
    componentClass: 'recommend-goods', // 混入中使用
    fromShareTimeLine: false,
    activityObj: {},
    // 最多只显示12个
    navList: [],
    // 页面样式属性
    pageConfig: {},
    scrollViewHeight: '',
    toView: 'z',
    clickToScroll: false,
    // top,scrollId
    scrollInfo: {
      scrollId: 0
    },
    showFixNav: false,
    showCartSubmit: false,
    couponModuleMap: {},
    hasData: true,
    modulePriority: '',
    isShowBlank: false,
    isScrollView: true,
    isScrollNav: false,
    videoList: [], // 存储视频信息
    currentPlayingVideo: null // 当前播放的视频ID
  },
  _data: {
    pageProtocol: true,
    isReportPageShow: false,
    systemTime: Date.now(),
    isInitSetSelectNav: false,
    screenIsChange: false,
    isResise: false
  },
  onReady() {
    setTimeout(() => {
      this.initObserver()
    },3000)
  },
  onResize() {
    this._data.isResise = true
    setTimeout(() => {
      this._data.isResise = false
    }, 500)
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options = {}) {
    console.log(options)
    fruitCartStore.initStore({ app })
    this.storeBindings = createStoreBindings(this, {
      store: fruitCartStore,
      actions: []
    })
    // 隐藏分享按钮
    wx.hideShareMenu()
    this.setData({
      topicList: [], options, modulePriority: options.modulePriority,
      // isShowBlank: options.modulePriority ? true: false
      activityType: options.activityType, // 线上专享特价专用，用来区分活动类型
    })
    // 获取页面容器高度
    this.getWindowHeight()
    if (options && (options.homeDeliveryObj || options.paySuccessObj || options.bindCardObj)) {
      var activityID = 0
      if (options.homeDeliveryObj) {
        activityID = options && JSON.parse(options.homeDeliveryObj).activityID
      } else if (options.paySuccessObj) {
        activityID = options && JSON.parse(options.paySuccessObj).activityID
      } else if (options.bindCardObj) {
        activityID = options && JSON.parse(options.bindCardObj).activityID
      }
      this.setData({ activityID: String(activityID).trim() })
    } else if (options && options.activityID){
      this.setData({ activityID: String(options.activityID).trim() })
    }
    if (app.globalData.scene === 1154) this.setData({ fromShareTimeLine: true })
    console.log(`---${activityID}---`)
  },

  onUnload: function (){
    obAnchor && obAnchor.disConnect()
    this.storeBindings.destroyStoreBindings()
    this.videoObserver && this.videoObserver.disconnect();
  },

  onHide: function () {
    // obAnchor在每次请求数据之后进行监听赋值
    // 在每次onshow时会定位，定位后请求数据，所以onHide时需要清除
    obAnchor && obAnchor.disConnect()
    this._data.isReportPageShow = false
    this.setData({
      modulePriority: ''
    })
  },
  async getWindowHeight() {
    const { windowHeight } = await getWindowInfo()
    this.setData({scrollViewHeight: windowHeight})
  },
  checkContent() {
    return this.data.currentView === 'content'
  },

  bgxxLocateReady: throttle(function() {
    this.checkContent() && freshCartStore.getCartData()
  }, 1),

  // 定位完成触发的方法
  onLocateReady({ onlyShowData = false } = {}) {
    if (this.checkContent()) {
      this.fetchData()
    }
    if (onlyShowData){
      return
    }
    // 在购物车结算条会获取及时达购物车数据
    // fruitCartStore.setPageParams()
    // fruitCartStore.getCartData()
  },
  async getCouponModule() {
    const { userID } = wx.getStorageSync('user') || {}
    const { unionid } = wx.getStorageSync('wxSnsInfo') || {}
    const { deliveryCenterCode } = wx.getStorageSync('timelyCity') || {}
    const params = {
      customerID: userID || -1,
      unionid,
      activityID: Number(this.data.activityID),
      deliveryCenterCode
    }
    try {
      const result = await app.api.getSubjectCouponModule(params)
      this._data.systemTime = result.systemTime
      const couponModuleMap = {}
      result.data.couponModuleList.forEach( item => {
        couponModuleMap[item.moduleNumber] = [item]
      })
      return couponModuleMap
    } catch(err) {
      return {}
    }
  },
  async getSubjectDetail() {
    const fruitSubject = new FruitSubject({ activityID: this.data.activityID, isNeedMerge: true })
    const [ subjectDetail, onlinePriceGoodsList ] = await Promise.all([
      fruitSubject.getDetailWithPreRequest(),
      this.getOnlinePriceGoods()
    ])
    return { subjectDetail, onlinePriceGoodsList }
  },
  async getOnlinePriceGoods() {
     // 判断是特价活动需要查特价商品
     if (this.data.activityType !== 'onlinePrice') {
      return []
     }
    const { storeCode, deliveryCenterCode, cityCode } = wx.getStorageSync('timelyCity') || {}
    return getOnlinePriceGoodsList({
      storeCode, 
      deliveryCenterCode, 
      cityCode, 
      isNeedMerge: false,
      isFliterNewGoods: true,
    })
  },
  /**
   * 获取专题数据
   */
  async fetchData() {
    wx.showLoading({
      title: '加载中',
    })
    const that = this
    // const { windowWidth: screenW = 750 } = wx.getStorageSync('systemInfo') || {};
    try {
      const  { subjectDetail = {}, onlinePriceGoodsList = [] } = await this.getSubjectDetail()
        const {
          pageTitle,
          activityID: zhuantiID = 0,
          name: zhuantiName = '',
          togetherModuleList: topicList = [],
          pageConfig = {
            backgroundColor: '#000',  //页面背景色
            navigationBarColor: '#fff', //导航栏背景色
            navigationButtonColor: '#00C587', //导航按钮色
            navigationDefaultColor: '#000', //导航字体默认色
            navigationSelectedColor: '#fff' //导航字体选中色
          }
        } = subjectDetail

        that.setData({
          pageConfig,
          zhuantiID,
          zhuantiName,
          activityObj: {
            activityID: zhuantiID,
            activityName: zhuantiName
          },
        })
        that.setPageTitle({
          title: pageTitle || zhuantiName,
          topicList: topicList
        })
        let navList = []
        // let navItemIdx = 0
        let showCartSubmit = false
        // 商品处理
        // 三排数据要进行处理
        const videoList = []
        topicList.forEach((topic) => {
          topic.id = `scroll-${topic.modulePriority}`
          const topicType = topic.type
          // 有商品模块才展示购物车条

          const checkIsShowCart = () => {
            if (showCartSubmit) {
              return showCartSubmit
            }
            const { activityPicList = [] } = topic;
            if (activityPicList && activityPicList.length > 0) {
              const { goodsTemplate } = activityPicList[0]
              if (!showCartSubmit && goodsTemplate) {
                return true
              }
            }
            if (['2','3','6','8'].includes(topicType)) {
              return topic?.goodsList.length
            }
            if (topicType === '9') {
              return topic?.goodsList.length
            }
          }
          // type=1 图片
          // if (topicType === '1') {
            // topic.activityPicList.forEach(ac => {
              // [{"w":225,"x":0,"h":64,"index":0,"y":0}]
              // if (ac.hotZone && JSON.parse(ac.hotZone)[0]) {
              //   ac.hotZoneStyle = JSON.parse(ac.hotZone)[0]
              //   const {w, h, x, y} = ac.hotZoneStyle
              //   const {maxWidth} = ac
              //   Object.assign(ac.hotZoneStyle, {
              //     w: screenW*(w/maxWidth),
              //     h: screenW*(h/maxWidth),
              //     x: screenW*(x/maxWidth),
              //     y: screenW*(y/maxWidth)
              //   })
              // }
              // 把goodsInfo里的数据拿出来
              // Object.assign(ac, ac.goodsInfo)
            // })
          // }
          // 导航栏会在楼层前面
          // 导航栏
          if (topicType === '999') {
            topic.navigationList.forEach((nav) => {
              nav.id = `scroll-${nav.priority}`
              nav.picUrl = nav.pic ? this.data.picUrl + nav.pic : ''
            })
            navList = topic.navigationList
          }
          // 导航楼层模板添加id
          if (topicType === '7') {
            const { priority } = topic.navigationList[0] || {}
            navList.forEach(nav => {
              if (nav.priority === priority) {
                topic.id = nav.id
              }
            })
          }
          // 三排商品时，增加空的数据补足成3的倍数
          // if (topicType === '6') {
          //   const listL = topic.goodsList.length
          //   const addL = listL % 3 ? 3 - listL % 3 : 0
          //   const array = Array.from({length: addL}, () => {
          //     return {
          //       isAdd: true
          //     }
          //   })
          //   topic.goodsList.push(...array)
          // }
          // 处理特价模块
          if (topicType === '8') {
            topic.goodsList = onlinePriceGoodsList
          }
          
          if (topicType === '10') {
            topic.videoIndex = videoList.length
            videoList.push({
              id: topic.id,
            })
          }
          showCartSubmit = checkIsShowCart()
        })
        // 处理售罄商品
        const {
          topicSelloutGoodsMap,
          topicShowList,
        } = this.handleSellOutList(topicList)
        const hasData = this.checkHasData(topicShowList, topicSelloutGoodsMap)
        that.setData({
          videoList,
          navList,
          topicList: topicShowList,
          showCartSubmit,
          hasData: hasData,
        })
        // 解决自动滚动导致监听交叉不触发问题
        if (!this._data.isInitSetSelectNav) {
          const scrollId = this.getSelectNav(navList)
          this.setData({
            scrollInfo: {
              scrollId: scrollId || 0
            }
          })
        }
        this._data.isInitSetSelectNav = true
        // wx.nextTick(() => {
          /**
           * uniapp-H5上缺陷，页面初始化后会马上执行createIntersectionObserver的回调
           * 导致默认设置了最后一个导航
           * 此处做延时放开处理
           */
          this.setData({
            clickToScroll: true
          })
        // wx.nextTick(() => {
        //   this.data.modulePriority && this.setData({
        //     toView: `scroll-${this.data.modulePriority}`,
        //   })
        // })
        setTimeout(() => {
          this.data.modulePriority && this.setData({
            toView: `scroll-${this.data.modulePriority}`,
          })
        }, 300);
        wx.hideLoading()
          setTimeout(() => {

            this.observerAnchor()
            this.setData({
              topicSelloutGoodsMap
            })
            // setData售罄商品
            // for( const key in topicSelloutGoodsMap) {
            //   const setKey = `topicList[${key}].sellOutList`
            //   const obj = {}
            //   obj[setKey] = topicSelloutGoodsMap[key] || []
            //   this.setData(obj)
            // }

          }, 300)
          // setTimeout(() => {
          //   console.log('time3', Date.now() - time2)
          //   this.data.isShowBlank && this.setData({
          //     isShowBlank: false,
          //   })
          // }, 400)
        // })

        that.setShareData({
          isShareOpen: subjectDetail.isShareOpen,
          title: subjectDetail.shareMajor,
          imageUrl: subjectDetail.cardSharePic
        })
        // 无任何配置(包括图片,商品,卡券等)则展示错误提示
      topicList.length || this.showErrorDialog()

        // 上报神策数据
        this.pageShowReport({ zhuantiID, zhuantiName})
    } catch(err) {
      wx.hideLoading()
      console.error(err)
      this.showErrorDialog()
      log.error('及时达综合专题报错', err)
    }
  },
  /**
   * @description
   * @param {*} topicList
   */
  getSelectNav(navList) {
    const { modulePriority } = this.data
    if (!modulePriority) return
    const selectNav = navList.filter( item => item.priority <= modulePriority)
    if (!selectNav.length) {
      return
    }
    return selectNav[selectNav.length - 1].priority
  },
  checkHasData(topicList = [], topicSelloutGoodsMap) {
    return topicList.some( (module,index) => {
      const { type } = module;
      switch (type) {
        case '1':
          if (module.activityPicList.length) {
            return true
          }
          break;
        case '2':
        case '3':
        case '8':
          var selloutGoodsList = topicSelloutGoodsMap[index] || [];
          if (module.showList.length || selloutGoodsList.length) {
            return true
          }
          break;
        case '4':
          if (module.couponList.length) {
            return true
          }
          break;
        case '9': 
          if (module.goodsList.length) {
            return true
          }
        break;
        case '999':
          if (module.navigationList.length > 2) {
            return true
          }
          break;
        case '10':
          return true
        default:
          break;
      }
    })
  },
  // 页面浏览上报埋点
  async pageShowReport({ zhuantiID, zhuantiName}) {
    if (!this._data.isReportPageShow) {
      //  上报页面浏览事件
      sensors.pageScreenView({
        $url_query: JSON.parse(JSON.stringify(this.data.options)),
        activity_ID: zhuantiID || '',
        activity_Name: zhuantiName || '',
      })
      this._data.isReportPageShow = true
    }
  },
  showErrorDialog() {
    commonObj.showModal('提示', '活动太火爆了，休息一下再试试吧', false, '我知道了', '', () => {
      wx.navigateBack({
        delta: 1,
        fail() {
          wx.switchTab({ url: '/pages/homeDelivery/index' });
        }
      });
    })
  },
  handleTapCoupon: throttle(function (e) {
    const item = e.currentTarget.dataset.item
    this.bannerJumpHandle(item)
  }, 1000),
  bannerJumpHandle(options) {
    const { openType } = options
    switch (String(openType)) {
      // 跳转商品详情，需要单独处理
      case '30':
      {
        this.toReceiveCopon(options)
        break;
      }
      default:
      {
        activity.toActivityPage({...options})
        break;
      }
    }
  },
  /**
   * @description 领取优惠券
   * @param {*} options
   */
  async toReceiveCopon(options) {
    if (!app.checkSignInsStatus()) {
      app.signIn()
      return
    }
    const { moduleNumber } = options
    const { userID } = wx.getStorageSync('user') || {}
    const { unionid } = wx.getStorageSync('wxSnsInfo') || {}
    const params = {
      activityID: Number(this.data.activityID),
      customerID: userID,
      moduleNumber,
      unionid: unionid
    }
    const result = await app.api.receiveCouponBySubject(params)
    const { receiveStatus, message } = result.data
    if (receiveStatus !== 'S') {
      this.showToast(message)
      return
    }
    this.showToast(message)
    // 领券成功刷新优惠券模块
    this.refreshCouponModule(moduleNumber)
    sensors.track('MPClick', 'categoryTopicGetCoupon', {
      activity_ID: this.data.activityID || '',
      activity_Name: this.data.zhuantiName || '',
    })
  },
  async refreshCouponModule(curModuleNumber) {
    const couponModuleMap = await this.getCouponModule()
    const { topicList } = this.data
    let setIndex = 0, setCoponList = [];
    topicList.forEach( (item, index) => {
      const {
        type,
        activityCouponList = []
      } = item
      if (type === '4') { // 优惠券模块
        const curActivityCouponList = activityCouponList.filter( item => {
          return couponModuleMap[item.moduleNumber]
        })
        const { moduleNumber } = curActivityCouponList[0] || {}
        if (curModuleNumber === moduleNumber) {
          setIndex = index
          setCoponList = couponModuleMap[moduleNumber]
        }
      }
    })
    const key = `topicList[${setIndex}].couponList`
    const obj = {}
    obj[key] = setCoponList
    this.setData(obj)
  },

  /**
   * 页面跳转
   */
  goPage(value) {
    const {
      opentype: openType,
      openvalue: openValue,
      obj: pageObj,
      isget: isGet,
      isbanner: isBanner,
      subopen: subOpenType,
      idx,
      module,
      modulenumber: moduleNumber,
      goodsSn,
      takeawayAttr
    } = value
    const that = this
    if (this.data.fromShareTimeLine) return

    switch (openType) {
      case '1':
      {
        // 果币充值页
        // 登录判断
        if (app.checkSignInsStatus()) {
          // 用户已登录
          wx.navigateTo({
            url: '/userA/pages/deposit/index'
          })
        } else {
          app.signIn()
        }
        break;
      }
      case '3':
      {
        // 商品详情页
        sensors.track('MPClick', 'categoryTopicPic', {
          activity_ID: that.data.activityID || '',
          activity_Name: that.data.zhuantiName || '',
        })
        const homeDeliveryObj = {
          takeawayAttr
        }
        //  62 全国送  3 及时达
        if (openType === '62') {
          homeDeliveryObj.goodsSn = goodsSn
        } else {
          homeDeliveryObj.goodsID = openValue
        }
        wx.navigateTo({
          url: '/homeDelivery/pages/goodsDetail/index?homeDeliveryObj=' + JSON.stringify(homeDeliveryObj)
        })
        break;
      }
      case '30':
      {
        sensors.track('MPClick', 'categoryTopicGetCoupon', {
          activity_ID: that.data.activityID || '',
          activity_Name: that.data.zhuantiName || '',
        })
        // 点击banner不需要校验登录状态
        if (isBanner) return activity.toActivityPage({openType: subOpenType, openValue})
        if (app.checkSignInsStatus()) {
          this.showTicketSubscribe().then(() => {
            // 用户已登录
            if (isGet === 'Y') {
              activity.toActivityPage({openType: subOpenType, openValue})
            } else {
              const customerID = wx.getStorageSync('user') && wx.getStorageSync('user').userID || -1
              const params = {
                activityID: that.data.activityID,
                customerID,
                moduleNumber,
                isWeixinCard: "T",
                type: "4"
              }
              app.api.getCouponInTopic(customerID, params).then(res => {
                const data = res.data
                // 领取失败

                if (data.receiveStatus !== 'S') {
                  that.showToast(data.message)
                  return
                }
                // 领取成功
                const couponIdx = this.data.topicList.findIndex(topic => topic.modulePriority === module)
                const isGeted = `topicList[${couponIdx}].couponList[${idx}].isGeted`
                that.setData({
                  [isGeted]: 'Y'
                })
                // 是否可以插入微信卡包
                const { success, cardCoupon = {} } = that.checkIsInsertCoupon(data.wxminiCardCoupon)
                if (!success) {
                  that.showToast(data.message)
                  return
                }
                // 需要插入微信卡包
                that.couponOpen(cardCoupon)
              })
            }
          })
        } else {
          app.signIn()
        }
        break;
      }
      case '35':
      {
        if (!pageObj.miniProgram) {
          return
        }
        wx.navigateToMiniProgram({
          path: pageObj.openValue,
          appId: pageObj.miniProgram
        })
        break;
      }
      default:
      {
        activity.toActivityPage({openType, openValue, ...pageObj})
        break;
      }
    }
  },

  navigateToPage(url) {
    if (this.data.fromShareTimeLine) return
    const pageUrl = `/${url}`
    if (tabBarPage.includes(pageUrl)) {
      wx.switchTab({
        url: pageUrl
      })
    } else {
      wx.navigateTo({
        url: pageUrl
      })
    }
  },

  eventprevent() {
    // console.log("mask 显示隐藏事件，阻止冒泡这个空方法不能删")
  },

  // 显示/隐藏 反馈库存弹窗
  checkInventory() {
    this.setData({
      isShowOrderMask: false
    })
    if (this.data.shopCart.length === 0) {
      this.clearShopCart()
    }
  },

  /**
   * 设置标题
   */
  async setPageTitle({
    title = '',
    topicList = []
  }) {
    // 只有第一张是图片，才不设置标题
    const { type } = topicList[0] || {}
    const { modulePriority } = this.data
    this._data.navBarTitle = title
    this.setData({
      isScrollNav: type === '1'
    })
    if (!modulePriority && type === '1') {
      return
    }
    this.setData({
      navBarBgColor: 'rgba(255,255,255)',
      navBarColor: '#222222',
      navBarTitle: title,
      backFilter: 0,
    })
  },
  /**
   * 设置分享信息
   */
  async setShareData(obj) {
    const shareInfo = {}
    if (['', 'Y'].includes(obj.isShareOpen)) {
      wx.showShareMenu({
        withShareTicket: false,
        menus: ['shareAppMessage', 'shareTimeline']
      })
      // 使用后台配置的分享
      shareInfo.title = obj.title
      shareInfo.imageUrl = this.data.picUrl + obj.imageUrl
      this.setData({ shareInfo: shareInfo })
    }
  },

  getReportShareParams() {
    const { version = '' } =  wx.getStorageSync('systemInfo') || {};
    const { userID = '' } = wx.getStorageSync('user') || {}
    const { zhuantiID, zhuantiName, shareInfo } = this.data
    const { storeID = 0, storeName = '', storeInfo = {} } = wx.getStorageSync('timelyCity') || {}
    return {
      ...generateShareAttr(),
      shareScene: ShareScene.及时达综合专题页,
      version,
      userID,
      mp_shareTitle: shareInfo.title,
      activity_ID: zhuantiID,
      activity_Name: zhuantiName,
      screen_name: '及时达专题活动页',
      screen_code: '1801',
      storeID,
      storeName,
      storeNum: storeInfo.storeCode || ''
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const shareInfo = this.data.shareInfo || {}
    wx.reportAnalytics('share_success')
    const shareObj = this.getReportShareParams()
    checkUserIsDistributor().then(isRecommender => {
      sensors.track('MPShare', {
        ...shareObj,
        isRecommender: Number(isRecommender),
      })
    })
    return {
      title: shareInfo.title,
      // 这个路径和参数会在app.js中判断是否为及时达综合专题分享
      // 如果要改变分享路径，需要在app.js中更改
      path: `/pages/topic/index?homeDeliveryObj=${JSON.stringify({ activityID: this.data.activityID })}&shareObj=${JSON.stringify(shareObj)}`,
      imageUrl: shareInfo.imageUrl
    }
  },

  checkNeedDistributor() {
    const { userID } = wx.getStorageSync('user') || {}
    const { query } = app.globalData.globalLaunchOptions || {}
    const { isCarryStore = 'Y' } = query || {}
    const needDistributor = isCarryStore !== 'N'
    return {
      userID,
      needDistributor: needDistributor && userID && isDistributorFromCache(),
    }
  },

  /**
  * 用户分享朋友圈
  */
  onShareTimeline() {
    const shareInfo = this.data.shareInfo
    wx.reportAnalytics('share_success')
    sensors.track('MPShare', this.getReportShareParams())
    const { userID, needDistributor, } = this.checkNeedDistributor()
    const shareObj = this.getReportShareParams()
    checkUserIsDistributor().then(isRecommender => {
      sensors.track('MPShare', {
        ...shareObj,
        isRecommender: Number(isRecommender),
      })
    })
    return {
      title: shareInfo.title,
      path: '/pages/topic/index',
      query: [
        `activityID=${this.data.activityID}`,
        needDistributor ? `distributor=${userID}` : '',
        `shareObj=${JSON.stringify(shareObj)}`
      ].filter(Boolean).join('&'),
      // imageUrl: shareInfo.imageUrl,
    }
  },
  /**
   * @description 消息弹窗
   * @param {string} - msg 消息文本
   */
  showToast(msg) {
    if (!msg) return
    wx.showToast({
      title: msg,
      icon: "none",
      duration: 2500
    })
  },
  /**
   * @description - 点击确定插入卡包
   */
  onCouponComfirmBtnTap: function ({ detail }) {
    const { errcode, send_coupon_result = [] } = detail
    const errorTxt = '未插入微信卡包'
    // 插件返回信息在params.detail
    console.log('>>>>>>>>>detail', detail)
    // 插入卡包失败
    if (errcode !== 'OK') {
      this.showToast(errorTxt)
      return
    }
    // 发券结果
    const isgetCouponErr = send_coupon_result.every(item => {
      return item.code !== 'SUCCESS' && item.code !== 'DUPREQUEST'
    })
    if (isgetCouponErr) {
      this.showToast(errorTxt)
    } else {
      this.showToast("领取成功！")
    }
  },
  /**
   * @description - 准备插卡
   */
  couponOpen(cardCoupon) {
    try {
      console.log(">>>>>>>coupon:", JSON.stringify(cardCoupon))
      this.setData({
        coupon: cardCoupon,
        couponIsShow: true,
      })
    } catch (error) { }
  },
  /**
   * @description - 取消插卡
   */
  couponClose() {
    this.setData({
      coupon: {},
      couponIsShow: false,
    })
  },
  // 商品倒计时触发
  handleCountComplete: throttle(function () {
    this.fetchData()
  }),


  /**
   * @desc 监听导航楼层位置，使导航cover按钮跳转至对应位置
   */
  observerAnchor () {
    // .relativeTo('#fixNav') 对选定元素作为参考
    // .relativeToViewport() 对可视区作为参考
    obAnchor = wx.createIntersectionObserver(this, {
      observeAll: true
    }).
    relativeTo('#ob-reference').observe('.inner-anchor-nav', res => {
      // relativeTo('#fixNav') 只监听高度很小的元素，所以每次触发的只有一个元素
      // 在点击跳转时不触发
      if (this.data.clickToScroll) return
      this.setScrollInfo(res)
    })
  },
  stickyNav (value) {
    const { id } = value.detail
    this.setData({
      clickToScroll: true,
      // scrollInfo: {scrollId: `scroll-${id}`},
      scrollInfo: {scrollId: id},
      toView: `scroll-${id}`
    })
  },
  noStickyNavHide (bool) {
    setTimeout(() => {
      this.setData({showFixNav: bool.detail})
    }, 60)
  },
  isScroll(e) {
    // 点击跳转时，页面滚动不触发observerAnchor的回调, 通过clickToScroll控制
    // 页面滚动时，不触发seletNav done
    // bindDragEnd无效，只能用bindscroll加防抖触发
    if (this._data.isResise) {
      return
    }
    setTimeout(() => {
      this.data.clickToScroll && this.setData({
        clickToScroll: false
      })
    },100)
    this.setNavBarStyle(e.detail.scrollTop)

    this.setData({ isScrolling: true });
    clearTimeout(this.data.scrollTimer);
    const scrollTimer = setTimeout(() => {
      this.setData({ isScrolling: false });
      // 查找第一个完整可见的视频
      this.findBestVideoToPlay();
    }, 500);
    
    this.setData({ scrollTimer });
  },
  setScrollInfo: debounce(function () {
    const { dataset: {startid: startId, endid: endId}} = arguments[0]
    this.setData({
      scrollInfo: {
        scrollId: typeof startId !== 'undefined' ? startId : endId
      }
    })
    console.log('scrollId', this.data.scrollInfo);
  }, 100),

  clickPic (e) {
    console.log('clickPic', e.detail);
    this.goPage(e.detail)
  },
  /**
   * 加载数据列表
   * @param {Object} list 商品列表
   */
  handleSellOutList(list) {
      try{
        const processGoodsList = (list) => {
          return list.map(el => {
            el.sellList = [] //在售商品列表
            el.sellOutList = [] //售罄商品列表
            el.showList = [] //展示商品列表
            el.showListStatus = false //是否显示展开售罄商品按钮 false/不展示 true/展示
            el.buttonStatus = false //按钮状态 false/展开 true/收起
            el.hideListNum = 0 //隐藏商品数
            const { goodsList } = el
            if(goodsList && goodsList.length){
              el.goodsList.forEach(good => {
                // 在售商品
                (good.allSkuStockNum ? el.sellList : el.sellOutList).push(good)
              })
            }
            return el
        })
      }
      const tranfList = processGoodsList(list || [])
      const topicSelloutGoodsMap = {}
      const topicShowList = tranfList.map((res, index) =>{
          let {showListStatus,showList,hideListNum,} = res
          const { sellOutList, type, sellList } = res
          if (type === '9') { return res }
          // 单排 && 售罄商品数量＞1
          if(type === '2' || type === '8'){
            showListStatus = sellOutList.length > 1;
            showList = showListStatus ? sellList.concat(sellOutList[0]) : sellList.concat(sellOutList);
            if (showListStatus) {
              hideListNum = sellOutList.length-1
              sellOutList.splice(0, 1)
            }
          } else if(type === '3'){
            if (sellOutList.length >= 2) {
              const isOddSellList = sellList.length % 2 === 1;
              showListStatus = isOddSellList || sellOutList.length !== 2
              showList = isOddSellList ? sellList.concat(sellOutList[0]) : sellList.concat(sellOutList.slice(0, 2));
              hideListNum = isOddSellList ? sellOutList.length - 1 : sellOutList.length - 2;
              if (isOddSellList) sellOutList.splice(0, 1);
              else sellOutList.splice(0, 2);
            } else {
              showListStatus = false;
              showList = [...sellList, ...sellOutList];
            }
          }
          // 三排  在售商品总数为3的倍数时，且楼层内售罄商品>3
          // else if(type === '6' && sellOutList.length > 3 && sellList.length % 3 === 0){
          //     showListStatus = true
          //     showList = sellList.concat(sellOutList.slice(0,3))
          //     hideListNum = sellOutList.filter((item)=>{return !item.isAdd}).length - 3
          //     sellOutList.splice(0,3)
          // }
          // 三排  在售商品总数为3的倍数+1时，且楼层内售罄商品>2
          // else if(type === '6' && sellOutList.length > 2 && sellList.length % 3 === 1){
          //     showListStatus = true
          //     showList = sellList.concat(sellOutList.slice(0,2))
          //     hideListNum = sellOutList.filter((item)=>{return !item.isAdd}).length - 2
          //     sellOutList.splice(0,2)
          // }
          // 三排 在售商品总数为3的倍数+2时，且楼层内售罄商品>1
          // else if(type === '6' && sellOutList.length > 1 && sellList.length % 3 === 2){
          //   showListStatus = true
          //   showList = sellList.concat(sellOutList.slice(0,1))
          //   // 去掉补足的部分
          //   hideListNum = sellOutList.filter((item)=>{return !item.isAdd}).length - 1
          //   sellOutList.splice(0,1)
          // }else {
          else {
            showListStatus = false
            showList = sellList.concat(sellOutList)
          }
          delete res.goodsList
          // 记录售罄商品，单独setData
          if (sellOutList.length) {
            topicSelloutGoodsMap[index] = sellOutList
          }
          return {...res,showListStatus,showList,hideListNum,sellOutList:[],sellList: [],type}
      })
      return {
        topicShowList,
        topicSelloutGoodsMap
      }
      }catch(e){
        console.log(e)
        //TODO handle the exception
        return {
          topicShowList: [],
          topicSelloutGoodsMap: {}
        }
      }

    },
    backPage() {
      wx.switchTab({
        url: '/pages/homeDelivery/index',
      })
    },
    setNavBarStyle (scrollTop) {
      if (!this.data.isScrollNav) {
        return 
      }
      const { navBarHeight = 0 } = this.data
      const ratio = Math.min(scrollTop / (navBarHeight/2), 1)
      if(this._data.ratio === ratio) return
      this._data.ratio = ratio
      if (ratio > 1) {
        return
      }
      if (ratio <= 0.2) {
        // 没有滚动距离
        this.setData({
          navBarBgColor: 'transparent',
          navBarColor: '#fff',
          backFilter: 1,
          navBarTitle: '',
        })
      } else {
        this.setData({
          navBarBgColor: `rgba(255,255,255, ${ratio})`,
          navBarColor: '#222222',
          backFilter: 1 - ratio,
          navBarTitle: this._data.navBarTitle,
        })
      }
    },
    // 子组件事件：获取自定义导航栏高度
    getNavBarHeight (ev) {
      // 这里的高度单位是px
      this.setData({
        navBarHeight: ev.detail
      })
    },
      // 初始化交叉观察器
  initObserver() {
    this.videoObserver = wx.createIntersectionObserver(this, {
      thresholds: [0.1, 0.5, 0.9, 1],
      observeAll: true
    });
    
    // this.observer.relativeToViewport({ top: 150, bottom: 150 }).observe('.video-module', res => {
    this.videoObserver.relativeTo('.scroll-box', { top: -90, bottom: -20 }).observe('.video-module', res => {
      if (this._data.screenIsChange) {
        return
      }
      const { dataset } = res;
      const { priority } = dataset;
      if (!res.intersectionRatio) { return }
      const index = priority
      const visibility = res.intersectionRatio;
      const key = `videoList[${index}].visibility`;
      const positionKey = `videoList[${index}].position`;
      this.setData({ 
        [key]: visibility ,
        [positionKey]: res.boundingClientRect.top
      });
      // 滑动中不处理新播放
      if (this.data.isScrolling) return;

      this.findBestVideoToPlay();
    });
  },

  // 查找最合适播放的视频
  findBestVideoToPlay() {
    const { videoList, currentPlayingVideo } = this.data;
    let firstVisibleIndex = -1;
    
    // 按顺序遍历视频（从上到下）
    for (let i = 0; i < videoList.length; i++) {
      const video = videoList[i];
      
      // 要求视频完整可见（90%以上在可视区内）
      if (video.visibility >= 0.9) {
        firstVisibleIndex = i;
        break; // 找到第一个就停止
      }
    }
    
    // 找到新视频且不是当前播放的
    if (firstVisibleIndex !== -1 && firstVisibleIndex !== currentPlayingVideo) {
      this.switchVideo(firstVisibleIndex);
    }
  },

  // 切换播放视频
  switchVideo(newIndex) {
    this.setData({
      currentPlayingVideo: newIndex
    })
  },
  handleVideoManualPlay(e) {
    const { videoId } = e.detail;
    // 更新当前播放的视频
    this.setData({ 
      currentPlayingVideo: videoId 
    });
  },
  handleScreenChange(e) {
    const { fullScreen } = e.detail
    if (fullScreen) {
      this._data.screenIsChange = true
    } else {
      setTimeout(() => {
        this._data.screenIsChange = false
      },1000)
    }
  }
})
