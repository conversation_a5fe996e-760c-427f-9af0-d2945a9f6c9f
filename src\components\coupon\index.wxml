<!--components/coupon/index.wxml-->
<!-- 定义方法来模拟computed -->
<wxs module="moduleFn">
/**
  * 是否是 没有门槛的运费券
  */
var isNoLimitTransparentCoupon = function(coupon) {
  if (!coupon) {
    return false
  }
  return coupon.couponWay === '5' && !Number(coupon.limitValue)
}
/**
  * 是否是仅配送
  */
var isOnlyDelivery = function(coupon) {
  if (!coupon) {
    return false
  }
  return coupon.isSupportDelivery === 'Y' && (!coupon.isSupportTake || coupon.isSupportTake === 'N')
}
/**
  * 是否是仅自提
  */
var isOnlyTake = function(coupon) {
  if (!coupon) {
    return false
  }
  return (
    coupon.isSupportTake === 'Y' && (!coupon.isSupportDelivery || coupon.isSupportDelivery === 'N')
  )
}
/**
  * 当前的券是否有限制 或者 是否是已过期且需要展示去续费/开通
  */
var isHideBorderRedius = function(coupon, type) {
  if (!coupon) {
    return false
  }
  return coupon.unAvailableReason || (coupon.isCanUse === 'N' && type === 'normal')
}
/**
  * 去掉时间字符串后面的时分秒
  */
var subsTime = function(strTime) {
  if (strTime && strTime.length > 10) {
    return strTime.substring(0, 10)
  }
  return strTime
}
module.exports.isNoLimitTransparentCoupon = isNoLimitTransparentCoupon
module.exports.isOnlyDelivery = isOnlyDelivery
module.exports.isOnlyTake = isOnlyTake
module.exports.isHideBorderRedius = isHideBorderRedius
module.exports.subsTime = subsTime
</wxs>
<view wx:if="{{coupon}}" class="coupon {{color}}">
  <!-- 中间区域如果是代金券类型 则增加代金券专用class -->
  <view class="body {{!isFold ? 'fold-shadow' : ''}} {{moduleFn.isHideBorderRedius(coupon, type) ? '' : 'bottom-redius'}} {{coupon.couponWay === '6' ? 'voucher' : ''}} {{ isExchangeCard ? 'exchange-body' : '' }}">
    <view wx:if="{{ isExchangeCard }}" class="{{type === 'disabled' || coupon.unAvailableReason ? 'coupon-gray' : ''}}">
    </view>
    <view class="coupon-top {{type === 'disabled' || coupon.unAvailableReason ? 'coupon-gray' : ''}}">
      <!-- 头部信息栏 如果是代金券/兑换卡 这里将会隐藏 代金券没有左侧样式 里面的关于代金券的逻辑是可以去掉的 -->
      <view wx:if="{{coupon.couponWay !== '6'}}" class="header {{coupon.isOnlyVip === 'Y' ? 'vip-gold' : ''}} {{coupon.couponWay === '5' ? 'freight-discount' : ''}} {{ isExchangeCard ? 'card' : '' }}">
        <!-- 如果是兑换卡 -->
        <view wx:if="{{isExchangeCard}}">
          <image class="exchange-card-img" src="{{EXCHANGE_CARD}}" mode="widthFix"></image>
          <!-- 价格 -->
          <view class="exchange-card-text-bg {{coupon.isOnlyVip === 'Y' ? 'vip-text-bg' : '' }}">
            <view>{{ coupon.couponValueStr }}</view>元兑换
          </view>
          <!-- 价格 -->
        </view>
        <!-- 不是兑换卡 -->
        <block wx:else>
          <view>
            <!-- 1.满减，3.立减，2.满折，4.立折，5.免运(有门槛的只能抵扣对应金额，所以展示￥) 6.代金券 -->
            <text class="name-decorate" wx:if="{{coupon.couponWay === '1' || coupon.couponWay === '3' || coupon.couponWay === '6' || (coupon.couponWay === '5' && Number(coupon.limitValue))}}">￥</text>
            <text class="name {{moduleFn.isNoLimitTransparentCoupon(coupon) ? 'small' : ''}}">{{
                moduleFn.isNoLimitTransparentCoupon(coupon) ? '免运' : couponValue
              }}</text>
            <text class="name-decorate" wx:if="{{coupon.couponWay === '2' || coupon.couponWay === '4'}}">折</text>
          </view>
          <view class="append one-line" wx:if="{{!moduleFn.isNoLimitTransparentCoupon(coupon)}}">{{coupon.limitValueStr}}</view>
        </block>
      </view>
      <view class="content">
        <view class="title-info one-line">
          <!-- 标题栏 -->
          <view class="title-box">
            <!-- 如果是代金券 -->
            <view wx:if="{{coupon.couponWay === '6'}}" class="voucher-title one-line">
              <text class="voucher-amount">{{ couponValue }}</text><text>元代金券</text>
            </view>
            <!-- 如果不是代金券 -->
            <view wx:else class="title {{coupon.isOnlyVip === 'Y' ? 'title-vip-gold' : ''}} {{ isExchangeCard ? 'exchange-name' : '' }}">
              <image class="vip-sign" wx:if="{{coupon.isOnlyVip === 'Y'}}" src="{{VIP_RADIUS_ICON}}"></image>
              <text class="title-name one-line">{{coupon.couponName}}</text>
            </view>
            <view class="limit-tag">
              <!-- 如果是代金券 则在此显示门槛 -->
              <view class="condition" wx:if="{{coupon.couponWay === '6'}}">{{coupon.limitValueStr}}</view>
              <view class="type-str label-fix-border" wx:if="{{coupon.couponTypeStr}}">{{coupon.couponTypeStr}}</view>
              <view
                wx:if="{{moduleFn.isOnlyDelivery(coupon) || moduleFn.isOnlyTake(coupon)}}"
                class="type-str label-fix-border">
                  {{ moduleFn.isOnlyDelivery(coupon) ? '配送专享' : '自提专享' }}
              </view>
            </view>
            <view class="coupon-bottom">
              <view class="left">
                <view class="expire-time">
                  <text class="limit-time pre" wx:if="{{coupon.effectTimeStr}}">{{coupon.effectTimeStr}}</text>
                  <text class="limit-time" wx:elif="{{type !== 'disabled' && coupon.limitTimeStr}}">{{coupon.limitTimeStr}}</text>
                  <text class="period">有效期至 {{moduleFn.subsTime(coupon.expireTimeText || coupon.expireTime)}}</text>
                </view>
                <!-- 详细信息栏 -->
                <view class="more {{isFold ? 'fold' : ''}}" wx:if="{{hasMore}}" bindtap="toggleMore">
                  详细说明
                  <image class="arrow" src="{{FOLD_ICON}}"></image>
                </view>
              </view>
            </view>
          </view>
          <!-- 这里增加如果有选择框的情况 则需要增加左边距 -->
          <view class="right-icon {{type === 'select' && coupon.isCanUse !== 'N' ? 'left-gap' : ''}}">
            <!-- 最佳优惠券标签 -->
            <image wx:if="{{orderBestCouponCode === coupon.couponCode}}" mode="aspectFill" class="order-optimal" src="https://resource.pagoda.com.cn/dsxcx/images/d9a99258845c040621b956342dd534f7.png"></image>
            <!-- 已使用/已过期 -->
            <block wx:if="{{type === 'disabled'}}">
              <image class="stamp" wx:if="{{coupon.couponStatus === '2'}}" src="https://resource.pagoda.com.cn/group1/M21/65/29/CmiLkGF5BJCAEhHpAAAUcJlzcZk524.png"></image>
              <image class="stamp" wx:if="{{coupon.couponStatus === '3'}}" src="https://resource.pagoda.com.cn/group1/M21/65/29/CmiLkGF5BHGARNA6AAAUqssN-5Q862.png"></image>
            </block>
            <!-- 其它情况 -->
            <block wx:if="{{type === 'select' && coupon.isCanUse !== 'N'}}">
              <radio-check checked="{{coupon.isSelected === 1}}" size="40rpx" bindchange="change" useScene="{{color == 'light' ? 'vegetables' : 'fruit'}}"></radio-check>
            </block>
          </view>
        </view>
        <view wx:if="{{ type === 'normal' }}" class="right">
          <view class="btns" wx:if="{{(coupon.isCanUse !== 'N' || coupon.isAppDownloadCoupon === 'Y')}}">
            <!--app下载引导券-->
            <view wx:if="{{coupon.isAppDownloadCoupon === 'Y'}}" class="btn" catchtap="appUse">App可用</view>
            <!-- 页面来源 src 为 列表页水果外卖且类型为门店和通用时，展示核销码 -->
            <block wx:else>
              <view wx:if="{{showToUse}}" class="btn flex-c-c {{coupon.effectTimeStr ? 'private' : ''}} {{coupon.isOnlyVip === 'Y' ? 'btn-gold' : ''}}" bindtap="toUsePage">
                {{ coupon.effectTimeStr ? '去看看' : (isExchangeCard ? '去兑换' : '去使用') }}
              </view>
            </block>
          </view>
          <view wx:elif="{{coupon.isOnlyVip === 'Y' && coupon.vipPayType === 1}}" class="btn flex-c-c btn-gold" bindtap="navigateOpenPage">
            去开通
          </view>
          <view wx:elif="{{coupon.isOnlyVip === 'Y' && coupon.vipPayType === 2}}" class="btn flex-c-c btn-gold" bindtap="navigateRenewPage">
            去续费
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 使用说明 -->
  <view wx:if="{{!isFold}}" class="use-desc {{moduleFn.isHideBorderRedius(coupon, type) ? '' : 'bottom-redius'}}">
    <!-- 如果是兑换卡 -->
    <text wx:if="{{ coupon.ruleDescription }}">{{ coupon.ruleDescription }}</text>
    <block wx:else>
      <view wx:for="{{coupon.useableCondition}}" wx:key="index">
        <text wx:if="{{coupon.useableCondition.length > 1}}">{{index+1}}.</text>{{item}}
      </view>
    </block>
  </view>
  <view class="footer">
    <view class="unAvailable-reason" wx:if="{{!!coupon.unAvailableReason}}">
      <image src="/source/images/icon_remind_circle_red.png" />
      <view>{{coupon.unAvailableReason}}</view>
    </view>
    <view class="btns" wx:elif="{{coupon.isCanUse === 'N' && type === 'normal'}}">
      <view class="open-renewal" wx:if="{{coupon.vipPayType === 2}}">
        <view class="vip-tips">您的心享会员已过期，请重新开通后使用</view>
        <view class="vip-btn" bindtap="navigateRenewPage">
          续费<image class="arrow-right" src="/source/images/icon_20_right_orang.png" />
        </view>
      </view>
      <view class="open-renewal" wx:if="{{coupon.vipPayType === 1}}">
        <view class="vip-tips">您尚未成为心享会员，请开通后使用</view>
        <view class="vip-btn" bindtap="navigateOpenPage">
          开通<image class="arrow-right" src="/source/images/icon_20_right_orang.png" />
        </view>
      </view>
    </view>
  </view>
</view>
