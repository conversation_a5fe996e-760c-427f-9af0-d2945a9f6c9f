const { recentBuy } = require('../../../service/userService');

const commonObj = require('../../../source/js/common').commonObj;
const app = getApp();
// 不同订单对应页面提示
const orderTipMap = {
  groupOrder: '支付成功，正在为您生成订单...',
  timelyOrder: '正在查询支付结果...',
  deposit: '支付成功，正在为您充值...'
}
Page({
  data: {
    waitText: '',
  },
  _data: {
    checkTimer: -1,
    times: 3,
    waitParams: {}
  },
  onLoad(options) {
    if (!options || !options.orderDetailObj) {
      return;
    }
    this.setInitData(options.orderDetailObj);
  },
  /**
   * 设置初始页面参数
   */
  setInitData(params) {
    const waitParams = JSON.parse(params)
    this._data.waitParams = waitParams;
    // 随单充响应太慢了,改成10次
    this._data.times = waitParams.isRecharge ? 10 : 3
    const { orderType = '' } = this._data.waitParams;
    this.setData({
      waitText: orderTipMap[orderType],
    });
    this.checkOrder();
  },
  checkOrder() {
    if (this._data.times <= 0) {
      this.handleOrderFail();
      return;
    }
    const { orderType = '' } = this._data.waitParams;
    if (orderType === 'timelyOrder' || orderType === 'deposit') {
      this.checkTimePayResult();
      return;
    }
    this.checkGroupDepositOrder();
  },
  /**
   * 拼团充值检查支付结果
   */
  checkGroupDepositOrder() {
    const that = this;
    const { customerID, paymentOrderID } = this._data.waitParams || {};
    const reqObj = {
      url: '/api/v1/order/checkPayStatus/' + customerID + '/' + paymentOrderID,
    };
    commonObj.requestData(
      reqObj,
      function (res) {
        const { data } = res.data || {};
        const { status = '' } = data || {};
        that.handleReqResult(status);
      },
      function () {
        that.handleReqResult();
      }
    );
  },
  /**
   * 及时达检查支付结果（切换到中台）
   */
  async checkTimePayResult() {
    const that = this;
    const { payNos, paymentOrderID: orderNo } = this._data.waitParams || {};
    try {
      const fruitSku = recentBuy.getRecentBuySku()
      const { userID } = wx.getStorageSync('user') || { userID: '' }
      const res = await app.api.checkPayStatusCombine({ payNos, orderNo, fruitSku, customerID: userID });
      const { status = '' } = res.data;
      that.handleReqResult(status);
    } catch (error) {
      that.handleReqResult();
    }
  },
  /**
   * 处理请求结果
   */
  handleReqResult(status = '') {
    if (status === 'SUCCESS') {
      this.handlePaySuccess();
    } else {
      this._data.times--;
      this._data.checkTimer = setTimeout(() => {
        this.checkOrder();
      }, 1000);
    }
  },
  /**
   * 处理检查支付成功
   */
  handlePaySuccess() {
    const {
      paymentOrderID,
      orderType,
      groupId,
      needShowCode,
      storeCode,
      storeName,
      storePhone,
      isTimelyOrder,
      subOrderNo,
      selectMonthCard,
      isMix = false,
      deliveryWay,
      timelyGoodsIsOverWeight,
      isRecharge,
    } = this._data.waitParams || {};
    if (orderType === 'deposit') {
      app.event.emit('depositSuccess');
      wx.redirectTo({
        url: '/userA/pages/deposit/index?from=充值&status=成功',
      });
    } else if (orderType === 'groupOrder') {
      wx.redirectTo({
        url: '/fightGroups/pages/paySuccess/index?payOrderIDorGoodsOrderID=' + paymentOrderID + '&fromPTorderDetail=' + true + '&groupId=' + groupId+`&needShowCode=${needShowCode}`
      })
    } else {
      wx.redirectTo({
        url: `/homeDelivery/pages/paySuccess/index?${
          [
            `paySuccessObj=${paymentOrderID}`,
            `needShowCode=${needShowCode}`,
            `storeCode=${storeCode}`,
            `storeName=${storeName}`,
            `storePhone=${storePhone}`,
            `subOrderNo=${subOrderNo || ''}`,
            `selectMonthCard=${Number(selectMonthCard)}`,
            `isMix=${isMix}`,
            `isTimelyOrder=${isTimelyOrder}`,
            `deliveryWay=${deliveryWay}`,
            `timelyGoodsIsOverWeight=${timelyGoodsIsOverWeight}`,
            `isRecharge=${isRecharge}`,
          ].join('&')
        }`
      })
    }
  },
  /**
   * 处理检查支付失败
   */
  handleOrderFail() {
    const { customerID, paymentOrderID, orderType, groupId, payNos } =
      this._data.waitParams || {};

    if (orderType === 'deposit') {
      wx.redirectTo({
        url: '/userA/pages/deposit/index?from=充值&status=待支付',
      });
      return;
    }
    const toObj = {
      customerId: customerID,
      paymentOrderId: paymentOrderID,
      fromTo: orderType === 'timelyOrder' ? '及时达' : '拼团',
    };
    if (orderType === 'timelyOrder') {
      toObj.payNos = payNos
    }
    if (orderType === 'groupOrder') {
      toObj.groupId = groupId;
    }
    wx.redirectTo({
      url: `/userA/pages/payError/index?toObj=` + JSON.stringify(toObj),
    });
  },
  onHide() {
    this.clearPage();
  },
  onUnload() {
    this.clearPage();
  },
  clearPage() {
    clearTimeout(this._data.checkTimer);
  },
});
