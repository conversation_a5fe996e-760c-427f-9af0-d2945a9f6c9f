// const sensors = require('../../../utils/report/sensors')
const util = require('../../../utils/util')
import { COUPON_DEFAULT } from '../../../source/const/goodsImage'
const app = getApp()
const sensors = require('../../../utils/report/sensors')

Page({
  data: {
    // 可用列表
    availableList: [],
    // 不可用列表
    unavailableList: [],
    // 优惠券缺省图
    noCouponImage: COUPON_DEFAULT,
    payAmount: 0  // 未使用代金券的支付金额
  },
  onShow() {
    // 浏览页面上报神策
    // sensors.pageShow('userSelectCouponPage')
    if (app.checkSignInsStatus()) {
      this.setInitCouponData()
    } else {
      app.showSignInModal()
    }
    
    sensors.pageShow('userSelectVoucherPage')
  },
  /**
   * 设置初始优惠券数据
   */
  setInitCouponData() {
    const eventChannel = this.getOpenerEventChannel()
    eventChannel.on('voucherInfo', (data) => {
      const { 
        voucherCouponInfo: { availableList = [], unavailableList = [], selectedCouponList = [] } = {}, 
        payAmount
      } = data || {}
      if (selectedCouponList.length) {
        availableList.forEach((item) => {
          // 可能选择的项是对象 则取couponCode属性 否则直接取值
          const isNeedCheck = selectedCouponList.find(
            selectItem => (typeof selectItem === 'object' ? selectItem.couponCode : selectItem) === item.couponCode
          )
          item.isSelected = isNeedCheck ? 1 : 0
        })
      } else {
        availableList.forEach((item) => {
          // 默认都不选择
          item.isSelected = 0
        })
      }
      const permuteInfo = [
        ...util.permute(['A', 'I', 'W']),
        ...util.permute(['A', 'I']),
        ...util.permute(['A', 'W']),
        'A',
      ]

      this.setData({
        payAmount, // 未使用代金券的支付金额
        availableList,
        unavailableList: util.filterChannelSeparation(unavailableList, 'channelSeparation', permuteInfo)
      })
    })
  },

  /**
   * 确定使用按钮 返回确认订单页
   */
  couponPipe() {
    const { availableList } = this.data
    const selectedCouponList = availableList.filter((item) => !!item.isSelected)
    // 删除选择的属性再交出去
    selectedCouponList.forEach(item => delete item.isSelected)
    this.backConfirmOrder(selectedCouponList)
    app.sensors.track('MPClick', {
      element_code: 122601002,
      element_name: '确认使用',
      element_id: '',
      element_content: '确认使用',
      screen_code: '1226',
    });
  },

  /**
   * 不使用优惠券
   */
  noUseCoupon() {
    this.backConfirmOrder([])
    
    app.sensors.track('MPClick', {
      element_code: 122601001,
      element_name: '暂不使用',
      element_id: '',
      element_content: '暂不使用',
      screen_code: '1226',
    });
  },

  /**
   * 传参到调用者 并返回上一页
   * @param {Array<any>} selectedCouponList 选择的代金券
   */
  backConfirmOrder(selectedCouponList) {
    const eventChannel = this.getOpenerEventChannel()
    // 通过emit的方式进行触发 将子页面/目标页面中的数据传递给当前页面
    eventChannel.emit('confirmSelectVouchers', { data: selectedCouponList })
    wx.navigateBack()
  },

  /**
   * 取消选中，设置选中
   */
  selectChange(e) {
    const { couponCode, isSelected, couponMoney = 0 } = e.detail || {}
    const { 
      availableList,
      payAmount = 0
    } = this.data
    const chooseVoucherAmount = availableList.reduce((total, item) => {
      return total + (item.isSelected ? item.couponMoney || 0 : 0)
    }, 0)
    // 选中的时候判断，如果已选中代金券+当前代金券大于非代金券支付金额则toast提示
    if (!isSelected && (chooseVoucherAmount + couponMoney > payAmount)) {
      wx.showToast({
        title: '代金券总金额不可高于订单应付金额哦~',
        icon: 'none'
      })
      return
    }
    // 获取点击索引
    const index = availableList.findIndex((item) => String(item.couponCode) === String(couponCode))
    // 有 取反
    if (index !== -1) {
      this.setData({
        [`availableList[${index}].isSelected`]: isSelected === 1 ? 0 : 1,
      })
    }
    app.sensors.track('MPClick', {
      element_code: isSelected ? '122601004' : '122601003',
      element_name: isSelected ? '代金券取消' : '代金券勾选',
      element_id: '',
      element_content: isSelected ? '代金券取消' : '代金券勾选',
      screen_code: '1302',
    });
  }
})
