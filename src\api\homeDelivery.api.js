const request = require('../utils/request')
const config = require('../utils/config')
import userStore from '~/stores/userStore'
import { getCustomerInfo } from '../source/js/requestData/activityPrice'

let waterfallTabCache

const api = {
  // 加减购物车商品
  changeCartGoods(params) {
    params.goodsDetailIsGetCache = false
    return request.post({ url:`/wxapp/cart/fruit/v1/changeCount`, data: params, isLoading: false})
  },
  // 同步购物车
  syncCart (params) {
    if (!params.cityCode && !params.storeCode) {
      return new Promise( resolve => { resolve() })
    }
    return request.post({ url:`/wxapp/cart/fruit/v1/syncCart`, data: params})
  },
  // 获取购物车页面的运费配置
  calFee(params) {
    return request.post({ url:`/wxapp/city/v1/getCityFreight`, data: params,isLoading: false})
  },
  // 获取及时达首页顶部功能区背景图
  getHomepagePic ({ cityID, userID, storeID }) {
    return request.get({ url: `/api/v2/homePage/picConfig/${cityID}/${userID}/${storeID}`, isLoading: false })
  },
  // 获取首页猜你喜欢的商品
  getLikedGoods ({ cityID , userID, storeID}) {
    return request.get({ url: `/api/v2/goods/guess/like/${userID}/${cityID}/${storeID}`, isLoading: false })
  },
  // 获取心享食材商品销量前十商品
  getHeartGoods ({ customerID, cityID, storeID }) {
    return request.get({ url: `/api/v1/wxmini/superVip/recommend/goods/${customerID}/${cityID}/${storeID}`, isLoading: false })
  },
  // 获取首页广告为商品和图片
  getAdvertGoods ({ customerID, cityID, storeID, pageNum, pageSize }) {
    return request.get({ url: `/api/v2/homePage/advert/goods/list/${customerID}/${cityID}/${storeID}/${pageNum}/${pageSize}`, isLoading: false})
  },
  // 获取品类
  getCategoryList ({ data, isLoading = false }) {
    return request.post({ url:`/dskhd/api/fruit/category/getCategoryList`, data, isLoading})
  },
  // 获取最近购买
  getFruitRecentBuy({data, isLoading = false}) {
    // http://localhost:3001/draft_exc
    return request.post({ url:`/dskhd/api/fruit/category/getRecentBuyList`, data, isLoading})

  },
  // 获取品类banner
  getCategoryBannerList (data) {
    return request.post({ url: `/wxapp/category/fruit/v1/getBanner`, data, isLoading: false })
  },
  // 获取商品
  getGoodsList ({data, loading = false}) {
    return request.post({ url: `/wxapp/goods/fruit/v3/getCategoryGoods`, data, isLoading: loading })
  },
   // 购物车去结算
  cartSettlement(p,data) {
    return request.post({ url:`/api/v1/stock/check/${p.customerID}/${p.cityID}/${p.storeID}`, data})
  },
  // 再来一单
  repeatCart(p,data) {
    return request.post({ url:`/api/v2/cart/repeatOrder/${p.customerID}/${p.cityID}/${p.storeID}` ,data})
  },
  // 获取门店打卡详情
  getStorePunchInfo (data = {}) {
    return request.post({ url: `/api/v2/wxmini/signIn/store/detail`, data })
  },
  // 获取门店商品
  getStoreGoods(data = {}) {
    return request.post({ url: `/dskhd/api/fruit/goods/v1/getStoreShelvesGoodsList`, data, isLoading: false })
  },
  // 门店打卡
  goPunchCard (customerID, storeID){
    return request.get({ url: `/api/v1/wxmini/signIn/store/${customerID}/${storeID}` })
  },
  // 获取 签到提醒开关
  getCheckStatus(customerID){
    return request.get({ url: `/api/v1/wxmini/signIn/notifySwitch/${customerID}` })
  },
  // 设置 签到提醒开关
  changePunchSwitch (data = {}){
    return request.post({ url: `/api/v1/wxmini/signIn/notifySwitch/set`,data })
  },
  // 门店列表页
  getStoreList(data = {}){
    return request.post({ url:`/api/v1/city/listStore`, data})
  },
  // 获取凑单商品列表
  getOrderGapGoods(data = {}, isLoading){
    return request.post({ url:`/dskhd/api/fruit/goods/v1/getCartAddOrderGoodsList`, data, isLoading})
  },
  /**
   * 获取品类页商品列表
   * @param {*} param0
   * @returns
   */
  getCategoryGoodsList({ data = {}, loading = false }) {
    return request.post( {url:`/dskhd/api/fruit/goods/v1/getCategoryGoodsList`, data, isLoading: loading})
  },
  getSearchHistory(cityID){
    return request.get({ url: `/api/v1/keyword/${cityID}`, isLoading: false})
  },
  // 三无退款页面
  // 1、查看是否同意三无协议
  checkRefundRule (customerID){
    return request.get({ url: `/api/v1/customer/isAgree/${customerID}`})
  },
  // 2、设置是否同意三无协议
  setRefundRule (data = {}){
    return request.post({ url:`/api/v1/customer/refundAgreement/set`, data})
  },
  // 三无退货任务是否做完 v2.4.1
  checkFinishTask(customerID){
    return request.get({ url:`/api/v1/customer/understandNoReasonReturn/${customerID}`})
  },
  // 用户取消订单原因列表 v2.4.0
  orderCancelReasonList(data = {}){
    return request.post({ url:`${config.baseUrl.PAGODA_DSN_DOMAIN}/api/v1/order/cancel/reason/list`, data})
  },
  // 取消订单 - 未支付 v2.4.0
  orderCancelUnpaid({customerID, payOrderID, requester}){
    return request.post({ url:`${config.baseUrl.PAGODA_EMS_DOMAIN}/wxmini/api/v1/order/cancel/unpaid/${customerID}/${payOrderID}/${requester}`})
  },
  // 取消订单 - 已支付(超时取消订单) v2.4.0
  orderRefundPaid(data = {}){
    let timelyCity = wx.getStorageSync('timelyCity') || {}
    let header = {
      gpslocation: JSON.stringify({
        lat: timelyCity.lat || '',
        lon: timelyCity.lon || ''
      })
    }
    return request.post({ url:`${config.baseUrl.PAGODA_EMS_DOMAIN}/wxmini/api/v1/order/refund/paid`, data, header })
  },
  // 获取会员页banner图
  getBannerList ({ customerID, cityID, storeID, pageSource}) {
    // return request.get({ url: `/api/v1/getBannerList/${customerID}/${cityID}/${storeID}/${pageSource}`, isLoading: false })
    return request.get({ url: `/api/v2/banner/${customerID}/${cityID}/${storeID}/member`, isLoading: false })
  },
  // 获取支付成功页banner图
  getPaySuccessBannerList ({ customerID, cityID, storeID }) {
    return request.get({ url: `/api/v2/banner/${customerID}/${cityID}/${storeID}/wxminiPaySuccess`, isLoading: false })
  },
  // 获取会员页积分专区
  getIntegralShopList () {
    return request.get({ url: `/api/v1/customer/integralShopList`, isLoading: false })
  },
  // 获取会员页商品列表
  getSpecialGoodsList ({ cityID, storeID, customerID}) {
    // return request.get({ url: `/api/v2/homePage/advert/goods/list/${customerID}/${cityID}/${storeID}/${pageNum}/${pageSize}`, isLoading: false})
    return request.get({ url: `/api/v1/findSpecialGoodsList/${customerID}/${cityID}/${storeID}`, isLoading: false})
  },
  // 实时获取用户是否为新用户
   getIsNewUser ({ customerID }) {
    return request.get({ url: `/api/v1/customer/isNew/${customerID}`, isLoading: false })
  },
  // 1元购商品首页
  getFissionActivityGoods ({ cityID, storeID, customerID }) {
    return request.get({ url: `/api/v1/fissionActivity/homePage/${cityID}/${storeID}/${customerID}`, isLoading: false })
  },
  // 领取裂变活动优惠券
  getCouponReceive (data = {}) {
    return request.post({ url: `/api/v1/fissionActivity/coupon/receive`, data })
  },
  // 查询红包活动
  getRedEnvelopeStatus (data) {
    return request.get({ url: `/api/v1/wxmini/groupRedEnvelope/checkActivityStatus/${data.customerID}`, isLoading: false })
  },
  // 检查城市是否支持心享服务
  isSupportVip (cityID) {
    return request.get({ url: `/api/v1/city/superVip/support/${cityID}`, isLoading: false })
  },
  /**
   * @description 获取首页弹窗
   * @param { any } data
   */
  getHomepageDialog (data) {
    return request.post({ url: `/wxapp/homePage/v1/getHomeLaunchDialog`, data, isLoading: false })
  },
  // 获取会员页弹窗
  getMemberpageModal (data) {
    return request.get({ url: `/api/v1/memberPage/launchDialog/${data.customerID}/${data.cityID}/${data.storeID}`, isLoading: false })
  },
  // 获取购物车和确认订单页小黄条提示
  getBubble (data) {
    return request.post({ url: `/dskhd/api/fruit/home/<USER>/getNotice`, data, isLoading: false })
  },
  // 获取购物车凑单优惠券列表
  getCartCouponList (data) {
    return request.post({ url: `/dskhd/api/member/coupon/v1/getCartCouponList`, data, isLoading: false })
  },
  // 查询购物车
  queryCart(data, isLoading = false) {
    return request.post({ url: `/wxapp/cart/fruit/v1/getCartList`, data, isLoading })
  },
  // 删除商品
  delCartGoods(data) {
    return request.post({ url: `/wxapp/cart/fruit/v1/deleteCart`, data })
  },
  /**
   * 清空购物车
   */
  clearCart(data){
    return request.post({ url: `/wxapp/cart/fruit/v1/clearCart`, data })
  },
  // 切换规格服务
  switchSpecService(data) {
    return request.post({ url: `/wxapp/cart/fruit/v1/switchCartList`, data })
  },
  getCategoryIdByGoodsId(data) {
    return request.get({ url: `/wxapp/category/fruit/v1/getCategoryByGoodsId`, data})
  },
  // 订单提醒
  getOrderInfo(data) {
    return request.post({ url: `/wxapp/order/v3/getOrderRemind`, data, isLoading: false })
  },
  // 获取直播 floating
  getFloating(data) {
    return request.post({ url: `/wxapp/homePage/v1/getFloating`, data, isLoading: false })
  },
  // 获取spu商品头图
  getSpuHeadPicList(data) {
    return request.post({ url: `/wxapp/order/v1/getSpuHeadPicList`, data})
  },
  // 获取水果外卖优惠券专区用户可以领取的优惠券
  getActivityCanReceiveCoupons(data) {
    return request.post({ url: `/wxapp/category/fruit/v1/getActivityCanReceiveCoupons`, data, isLoading: false})
  },
  // 领取活动优惠券
  receiveActivityCoupons(data) {
    return request.post({ url: `/wxapp/category/fruit/v1/receiveActivityCoupons`, data})
  },
  // 首页心享会员栏
  getBgxxVipTips(data) {
    return request.post({ url: `/wxapp/member/v2/getBgxxVipTips`, data})
  },
  // 获取用户账户下百果园+小程序可以使用的券(已废弃)
  getWxminiCanUseCoupons(data) {
    return request.post({ url: `/wxapp/category/fruit/v1/getWxminiCanUseCoupons`, data, isLoading: false})
  },
  // 获取全国送B2C商品
  getB2CGoods(data, isLoading = false) {
    return request.post({ url: `/dskhd/api/b2c/goods/v1/getB2cGoodsList`, data, isLoading})
  },
  // 用户各个状态下订单数量统计
  getOrderStatusCount(data) {
    return request.post({ url: `/wxapp/order/v1/getOrderCount`, data, isLoading: false})
  },
  /**
   * 搜索商品列表
   * @param {*} data
   * @returns
   */
  searchGoodsList(data) {
    return request.post({ url: `/wxapp/goods/v3/searchGoods`, data, isLoading: false, mask: true })
  },
  /**
   * 搜索推荐商品，整合及时达与次日达
   * @param {*} data
   * @returns
   */
  getRecommenGoods(data) {
    return request.post({ url: `/wxapp/goods/v1/recommenGoods`, data, isLoading: false, mask: true })
  },
  /**
   * 查询搜索商品联想列表
   * @returns
   */
  getSimilarWords(data) {
    return request.post({ url: '/wxapp/goods/v1/similarWords', data, isLoading: false, mask: true })
  },
  /**
   * 订单结算，果粉价版本
   * @param {*} data
   * @returns
   */
  orderSettleGrayScale(data) {
    // 结算接口限流报错不重试
    let isVip = getCustomerInfo().IS_VIP_CUSTOMER
    // 会员生成的虚拟身份唯一码
    if (data.uniqCode) {
      isVip = true
    }
    // 为了兼容勾选月卡时用户视为虚拟会员的场景，请求头的用户信息在这里单独设置
    const userHeaderInfo = {
      'x-customerId': data.customerID,
      'is-fruit-fans-gray': String(userStore.isFruitFansGray),
      'is-fruit-fans': String(userStore.isFruitFans),
      'is-vip': String(isVip)
    }
    return request.post({ 
      url: `/wxapp/trade/fruit/v7/settle`, 
      data, 
      isLoading: true, 
      mask: true, 
      tryCount: 999,
      header: {
        'x-userinfo': JSON.stringify(userHeaderInfo)
      }
    })
  },
  // 订单结算
  orderSettle(data) {
    return request.post({ url: `/wxapp/trade/fruit/v4/settle`, data, isLoading: true, mask: true})
  },
  // 获取商品详情
  getGoodsDetail (data) {
    return request.post({url: `/wxapp/goods/fruit/v1/getGoodsDetail`, data, isLoading: false})
  },
  // 获取订单列表
  getOrderList (data) {
    return request.post({url: `/wxapp/order/v4/getOrderList`, data, isLoading: false })
  },
  // 检查支付结果
  checkPayStatus(data) {
    return request.post({url: `/wxapp/trade/fruit/v1/checkPayResult`, data, isLoading: false})
  },

  /**
   * 查询随单充值活动数据
   * @param {*} data 
   * @returns 
   */
  getOrderInlineRechargeData(data) {
    return request.post({
      url: `/dskhd/api/member/account/v1/getOrderInlineRecharge`,
      data,
      isLoading: false,
    })
  },

  // 交易订单合并接口
  checkPayStatusCombine(data) {
    // http://localhost:3001/draft_exc
    return request.post({url: `/dskhd/api/trade/v1/checkPayResult`, data, isLoading: false})
  },
  // 提交订单，果粉价版本
  submitOrderGrayScale(data, loginOutBackHomePage = false, loginOutErrorHandle) {
    let isVip = getCustomerInfo().IS_VIP_CUSTOMER
    // 会员生成的虚拟身份唯一码
    if (data.uniqCode) {
      isVip = true
    }
    // 为了兼容勾选月卡时用户视为虚拟会员的场景，请求头的用户信息在这里单独设置
    const userHeaderInfo = {
      'x-customerId': data.customerID,
      'is-fruit-fans-gray': String(userStore.isFruitFansGray),
      'is-fruit-fans': String(userStore.isFruitFans),
      'is-vip': String(isVip)
    }
    return request.post({
      url: `/wxapp/trade/fruit/v7/orderSubmit`, 
      data, 
      isLoading: false, 
      loginOutBackHomePage, 
      tryCount: 999,
      loginOutErrorHandle,
      header: {
        'x-userinfo': JSON.stringify(userHeaderInfo)
      }
    })
  },
  // 提交订单
  submitOrder(data, loginOutBackHomePage = false) {
    return request.post({url: `/wxapp/trade/fruit/v4/orderSubmit`, data, isLoading: false, loginOutBackHomePage})
  },
  // 订单支付
  orderPay(data) {
    return request.post({url: `/wxapp/trade/fruit/v1/pay`, data})
  },
  // 订单交易合并后的支付接口
  orderPayCombine(data) {
    return request.post({url: `/wxapp/trade/fruit/v5/pay`, data, encryptFlag: true, encryptType: 'token' })
  },
  // 0元支付
  orderZeroPay(data) {
    return request.post({url: `/wxapp/trade/fruit/v1/zeroPay`, data, encryptFlag: true, encryptType: 'token' })
  },
  // 获取付款成功页数据
  getPaySuccessInfo(data) {
    return request.post({url: `/wxapp/trade/fruit/v2/paySuccessInfo`, data, mask: true})
  },
  // 获取付款成功页数据,订单交易合并版本
  getPaySuccessInfoCombine(data) {
    return request.post({url: `/wxapp/trade/fruit/v3/paySuccessInfo`, data, mask: true})
  },
  // 获取付款成功页弹窗
  paySuccessPopup(data) {
    return request.post({url: `/wxapp/trade/fruit/v1/paySuccessPopup`, data, isLoading: false})
  },
  // 取消已支付订单
  cancelPaidOrder (data) {
    return request.post({url: `/wxapp/order/v2/cancelPaidOrder`, data})
  },
  // 取消未支付订单
  cancelUnpaidOrder (data) {
    return request.post({url: `/wxapp/order/v2/cancelUnpaidOrder`, data})
  },
  // 及时达订单再来一单
  repeatOrder (data) {
    return request.post({url: `/wxapp/cart/fruit/v1/repeatOrder`, data})
  },
  // 获取订单详情（及时达，试吃，全国送）
  getOrderDetail(data, isLoading) {
    return request.post({ url: `/wxapp/order/v4/getOrderDetail`, data, isLoading })
  },
  //  获取拼团详情
  //  http://wiki.pagoda.com.cn/pages/viewpage.action?pageId=2590489
  getScrabbleDoughDetails(customerID,goodsOrderID) {
    return request.get({ url: `${config.baseUrl.PAGODA_APP_DOMAIN}/api/v1/order/detail/${customerID}/${goodsOrderID}/N` });
  },
  //  获取及时达物流详情
  getTimelyDeliveryInfo(data) {
    return request.post({ url: `/wxapp/order/v1/getTimelyDeliveryInfo`, data, isLoading: false})
  },
  //  删除订单
  updateUserHide(data) {
    return request.post({ url: `/wxapp/order/v1/updateUserHide`, data, isLoading: false})
  },
  // 确认全国送物流收货
  confirmB2CExpress(data) {
    return request.post({ url: `/wxapp/order/v2/confirmExpress`, data, isLoading: false})
  },
  // 获取三无退款预警弹窗
  getRefundWarning(data) {
    return request.post({ url: `/wxapp/order/v1/getRefundWarning`, data, isLoading: false})
  },
  getExpressDetail(data) {
    return request.post({ url: `/wxapp/order/v2/getExpressDetail`, data, isLoading: false})
  },
  getBaseGoodsInfoByGoodsSnList(data) {
    return request.post({ url: `/wxapp/goods/fruit/v1/getBaseGoodsInfoByGoodsSnList`, data, isLoading: false})
  },
  // b2c订单取消原因列表
  getB2CCancelReasonList(data) {
    return request.post({ url: `/wxapp/order/v1/b2cCancelReasonList`, data, isLoading: false})
  },
  // 旧电商 三无退 (拼团)
  orderRefundOnline({params,customerID,goodsOrderID}){
    return  request.post({url:`${config.baseUrl.PAGODA_APP_DOMAIN}/api/v1/order/refund/${customerID}/${goodsOrderID}`,data:params})
  },
  // 旧电商 三无退 (门店)
  orderRefundOffline({params,customerID,orderTicket}){
    return  request.post({url:`${config.baseUrl.PAGODA_APP_DOMAIN}/api/v1/offlineStore/refund/${customerID}/${orderTicket}`,data:params})
  },
  // serverless 及时达、全国送、接龙耗材三无退原因
  getConsumableReason(){
    return request.post({url:`/wxapp/order/v1/getConsumableReason`})
  },
  // 中台 三五退（及时达，b2c）
  selfOperateOderRefund(data){
    return request.post({
      url:`/dskhd/api/order/refund/v1/selfOperateOderRefund`,
      data,
      isLoading:false,

      encryptFlag: true,
      encryptType: 'token'
    })
  },
  // 中台 查询退款情况详情
  getReFundResult(data){
    return request.post({
      url:`/wxapp/order/v4/getReFundResult`,
      data,
      isLoading:false
    })
  },
  // 敏感词校验
  sensitiveCheck(data){
    return request.post({url:`/wxapp/content/v2/checkSensitiveInfo`, data, isLoading:false})
  },
  // 三无退预警弹窗
  selfOperatedRefundWarning(data){
    return request.post({url:`/wxapp/trade/fruit/v2/selfOperatedRefundWarning`,data,isLoading:false})
  },
  // 三无退短信上行校验
  getSmsMsg(data){
    return request.post({ url:`/dskhd/api/order/refund/v1/getSmsMsg`, data, isLoading: true })
  },
  // 获取短信上行结果查询
  getSmsSendStatus(data){
    return request.post({ url:`/dskhd/api/order/refund/v1/getSmsSendStatus`, data, isLoading: true })
  },
  // 获取首页metro列表
  getHomePageMetroList (data) {
    return request.post({ url: `/wxapp/homePage/v3/getHomePageMetroList`, data, isLoading: false})
  },
  getHomePageDocument (data) {
    return request.post({ url: `/wxapp/homePage/v1/getHomePageDocument`, data, isLoading: false})
  },
  // 获取首页瀑布流tabbar
  async getWaterfallTab (data) {
    waterfallTabCache = await request.post({ url: `/wxapp/homePage/v1/getWaterfallTab`, data, isLoading: false })
    return waterfallTabCache
  },
  /**
   * 获取及时达首页瀑布流缓存结果
   * @returns
   */
  getWaterfallTabPromise () {
    if (waterfallTabCache) {
      return waterfallTabCache
    } else {
      const { userID } = wx.getStorageSync('user') || {}
      const { cityID, storeID, cityCode, supportBToCService = false, } = wx.getStorageSync('timelyCity') || {}

      const params = {
        customerID: userID || -1,
        cityID,
        storeID: storeID || -1,
        cityCode: cityCode || "",
        supportBToCService
      }

      return api.getWaterfallTab(params)
    }
  },
  /** 获取spu的erp分类信息 */
  getSPUERPCategory(data) {
    return request.post({ url: '/dskhd/api/fruit/goods/v1/getSPUERPCategory', data, isLoading: false })
  },
  // 获取首页瀑布流商品
  getWaterFallFlowGoodsList (data) {
    return request.post({ url: '/dskhd/api/fruit/goods/v1/getWaterfallFlowGoodsList', data, isLoading: false })
  },
  // 获取限时特价活动列表
  getLimitedTimeSpecialActivitiesList (data) {
    return request.post({ url: `/wxapp/activity/limitedTimeSpecialActivities/v1/getActivitiesList`, data, isLoading: false })
  },
  // 获取限时特价的商品详情列表
  getLimitedTimeSpecialActivityGoodsDetailList (data) {
    return request.post({ url: `/dskhd/api/fruit/goods/v1/getLimitTimeActivityPriceGoodsList`, data, isLoading: false })
  },

  /**
   * 获取及时达首页大促背景图
   * @param {*} data
   * @returns
   */
  getBackgroundAd(data) {
    return request.post({
      url: `/dskhd/api/fruit/home/<USER>/getBackgroundAd`,
      data,
      isLoading: false
    })
  },

  // 获取腰部焦点广告
  getHomeMiddleAd (data) {
    return request.post({ url: `/dskhd/api/fruit/home/<USER>/getMiddleAd`, data, isLoading: false })
  },
  // 获取招牌橱窗数据
  getSignboardShow(data) {
    return request.post({ url: `/dskhd/api/fruit/home/<USER>/getSignboardShow`, data, isLoading: false })
  },
  checkSensitiveInfo (data) {
    return request.post({ url: `/wxapp/content/v1/checkSensitiveInfo`, data, isLoading: true })
  },
  getWaterfulGoodsIdList(data) {
    return request.post({ url: `/wxapp/goods/fruit/v1/getWaterfulGoodsIdList`, data, isLoading: false })
  },
  // 获取及时达附近门店
  getFruitNearByStores (data, { isLoading = true } = {}) {
    return request.post({ url: '/dskhd/api/store/v1/getFruitNearbyStoreList', data: Object.assign(data, {
      lat: String(data.lat),
      lon: String(data.lon),
    }), isLoading }).then(result => {
      const resultData = result.data || []
      resultData.forEach(store => {
        /**增加属性：门店距离，不带单位 */
        store.distanceNum = store.distance
        store.distanceStr && (store.distance = store.distanceStr)
      })

      return result
    })
  },
  /**
   * 获取及时达附近有货门店列表
   * @param {*} data
   * @returns
   */
  getNearbyHasFruitStoreList(data) {
    return request.post({ url: '/dskhd/api/store/v1/getNearbyHasFruitStoreList', data, isLoading: false })
  },
  // 批量获取订单信息 目前用户查询混合单子单信息上报有数埋点
  getSubOrderList (data) {
    return request.post({ url: `/wxapp/order/v1/getSubOrderInfo`, data, isLoading: false })
  },
  // 获取次日达入口
  getFreshEntry(data) {
    return request.post({ url: `/dskhd/api/fruit/home/<USER>/getFreshEntryAd`, data, isLoading: false })
  },
  /**
   * 获取切分banner广告位
   * @param {*} data 
   * @returns 
   */
  getSplitBannerAd(data) {
    return request.post({
      url: `/dskhd/api/fruit/home/<USER>/getSplitBannerAd`,
      data,
      isLoading: false,
    })
  },
  // 查询商品详情列表
  getGoodsDetailList(data, isLoading = false) {
    return request.post({ url: `/wxapp/goods/fruit/v1/getGoodsDetailList`, data, isLoading })
  },
  // 查询商品动态信息(特价、限购、库存)
  getGoodsDynamicInfoList(data, isLoading = false) {
    return request.post({ url: `/wxapp/goods/fruit/v1/getGoodsDynamicInfoList`, data, isLoading })
  },
  // 查询门店订单
  // 获取门店订单列表
  getOfflineOrderList(data) {
    return request
      .post({ url: '/wxapp/order/v4/offlineOrderList', data, isLoading: true })
      .catch(() => ({ data: { orderList: [], headPicList: [] } }))
  },
  getLiveOrRecord(data, isLoading = false) {
    return request.post({ url: '/dskhd/api/order/storeRecod/v1/getLiveOrRecord', data, isLoading })
  },
  getGoodsListByTime(data) {
    return request.post({ url: '/dskhd/api/fruit/goods/v1/getGoodsListByTime', data, isLoading: false, timeout: 2000 })
  },
  // 获取三无退总体退款比例是否超过管理台配置比例
  threeRefundRatioCheck(data) {
    return request.post({ url: '/dskhd/api/order/refund/v1/threeRefundRatioCheck', data, isLoading: false })
  },
  // 查询会员当前实名信息（会员ID）
  getVerifyStatus(data) {
    return request.post({ url: '/dskhd/api/member/account/v1/getVerifyStatus', data, isLoading: false })
  },

  /**
   * 查询新客退款检查
   * @param {*} data
   * @returns
   */
  firstRefundCheck(data) {
    return request.post({ url: '/dskhd/api/order/refund/v1/firstRefundCheck', data, isLoading: false })
  },

  // 根据身份证号码获取对应绑定会员认证信息结果（身份证号码）
  getVerifyByIdentityCardNum(data) {
    return request.post({ url: '/dskhd/api/member/account/v1/getVerifyByIdentityCardNum', data, isLoading: false })
  },
  // 获取最新一条会员实名切换审核单
  getVerifyChangeRecord(data) {
    return request.post({ url: '/dskhd/api/member/account/v1/getVerifyChangeRecord', data, isLoading: false })
  },
  // 会员申请实名认证/改绑（二合一）
  applyMemberVerify(data) {
    return request.post({ url: '/dskhd/api/member/account/v1/applyMemberVerify', data, isLoading: false })
  },
  // 上报订单买点
  reportOrder(data) {
    return request.post({ url: '/dskhd/api/report/orderDeviceInfo', data, isLoading: false })
  },
  getPriceActivityGoodsRequest(data) {
    return request.post({ url: `/wxapp/goods/fruit/v1/getActivityPriceGoods`, data, isLoading: false })
  },
  // 获取新人专享活动商品列表
  newActGoodsList(data) {
    return request.post({ url: '/wxapp/goods/fruit/v1/newActGoodsList', data, isLoading: false })
  }
}


module.exports = api
