/**
 * 分享卡片性能优化工具类
 * 提供图片预加载、缓存管理、并发控制等功能
 */

// 图片缓存对象
const imageCache = new Map()
// 正在加载的图片Promise缓存
const loadingPromises = new Map()

class ShareCardOptimizer {
  constructor() {
    this.maxCacheSize = 50 // 最大缓存图片数量
    this.cacheExpireTime = 30 * 60 * 1000 // 缓存过期时间：30分钟
  }

  /**
   * 预加载图片并缓存
   * @param {string} url 图片URL
   * @param {string} cacheKey 缓存键名
   * @returns {Promise<string>} 返回图片本地路径
   */
  async preloadImage(url, cacheKey = url) {
    const startTime = Date.now()
    console.log(`[ShareCard Optimizer] 开始预加载图片: ${cacheKey}`)

    // 检查缓存
    const cached = this.getFromCache(cacheKey)
    if (cached) {
      console.log(`[ShareCard Optimizer] 使用缓存图片: ${cacheKey} - 耗时: ${Date.now() - startTime}ms`)
      return cached
    }

    // 检查是否正在加载
    if (loadingPromises.has(cacheKey)) {
      console.log(`[ShareCard Optimizer] 图片正在加载中，等待完成: ${cacheKey}`)
      return await loadingPromises.get(cacheKey)
    }

    // 开始加载图片
    const loadPromise = this.loadImageWithRetry(url, 3)
    loadingPromises.set(cacheKey, loadPromise)

    try {
      const imagePath = await loadPromise
      const endTime = Date.now()
      console.log(`[ShareCard Optimizer] 图片加载完成: ${cacheKey} - 耗时: ${endTime - startTime}ms`)
      
      // 缓存图片
      this.setToCache(cacheKey, imagePath)
      return imagePath
    } catch (error) {
      console.error(`[ShareCard Optimizer] 图片加载失败: ${cacheKey}`, error)
      throw error
    } finally {
      loadingPromises.delete(cacheKey)
    }
  }

  /**
   * 带重试的图片加载
   * @param {string} url 图片URL
   * @param {number} maxRetries 最大重试次数
   * @returns {Promise<string>}
   */
  async loadImageWithRetry(url, maxRetries = 3) {
    let lastError
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        const result = await this.loadSingleImage(url)
        if (i > 0) {
          console.log(`[ShareCard Optimizer] 图片重试加载成功: ${url}, 重试次数: ${i}`)
        }
        return result
      } catch (error) {
        lastError = error
        console.warn(`[ShareCard Optimizer] 图片加载失败，重试 ${i + 1}/${maxRetries}: ${url}`, error)
        
        if (i < maxRetries - 1) {
          // 等待一段时间后重试
          await this.delay(Math.pow(2, i) * 1000) // 指数退避
        }
      }
    }
    
    throw lastError
  }

  /**
   * 加载单个图片
   * @param {string} url 图片URL
   * @returns {Promise<string>}
   */
  loadSingleImage(url) {
    return new Promise((resolve, reject) => {
      wx.getImageInfo({
        src: url,
        success: (res) => resolve(res.path),
        fail: reject
      })
    })
  }

  /**
   * 并发预加载多个图片
   * @param {Array<{url: string, cacheKey?: string}>} imageList 图片列表
   * @param {number} concurrency 并发数量
   * @returns {Promise<Array<string>>}
   */
  async preloadImagesWithConcurrency(imageList, concurrency = 3) {
    const startTime = Date.now()
    console.log(`[ShareCard Optimizer] 开始并发预加载 ${imageList.length} 个图片，并发数: ${concurrency}`)

    const results = []
    const executing = []

    for (const imageInfo of imageList) {
      const { url, cacheKey = url } = imageInfo
      
      const promise = this.preloadImage(url, cacheKey).then(
        (result) => ({ status: 'fulfilled', value: result, cacheKey }),
        (error) => ({ status: 'rejected', reason: error, cacheKey })
      )

      results.push(promise)

      if (results.length >= concurrency) {
        executing.push(promise)
        if (executing.length >= concurrency) {
          await Promise.race(executing)
          executing.splice(executing.findIndex(p => p === promise), 1)
        }
      }
    }

    const allResults = await Promise.all(results)
    const endTime = Date.now()
    
    const successful = allResults.filter(r => r.status === 'fulfilled')
    const failed = allResults.filter(r => r.status === 'rejected')
    
    console.log(`[ShareCard Optimizer] 并发预加载完成 - 成功: ${successful.length}, 失败: ${failed.length}, 总耗时: ${endTime - startTime}ms`)
    
    return allResults
  }

  /**
   * 从缓存获取图片
   * @param {string} cacheKey 缓存键名
   * @returns {string|null}
   */
  getFromCache(cacheKey) {
    const cached = imageCache.get(cacheKey)
    if (!cached) return null

    // 检查是否过期
    if (Date.now() > cached.expireTime) {
      imageCache.delete(cacheKey)
      return null
    }

    return cached.path
  }

  /**
   * 设置图片到缓存
   * @param {string} cacheKey 缓存键名
   * @param {string} imagePath 图片路径
   */
  setToCache(cacheKey, imagePath) {
    // 检查缓存大小，如果超过限制则清理最旧的缓存
    if (imageCache.size >= this.maxCacheSize) {
      this.cleanOldCache()
    }

    imageCache.set(cacheKey, {
      path: imagePath,
      expireTime: Date.now() + this.cacheExpireTime,
      createTime: Date.now()
    })
  }

  /**
   * 清理过期缓存
   */
  cleanOldCache() {
    const now = Date.now()
    const entries = Array.from(imageCache.entries())
    
    // 按创建时间排序，删除最旧的缓存
    entries.sort((a, b) => a[1].createTime - b[1].createTime)
    
    const toDelete = Math.ceil(this.maxCacheSize * 0.3) // 删除30%的缓存
    for (let i = 0; i < toDelete && i < entries.length; i++) {
      imageCache.delete(entries[i][0])
    }
    
    console.log(`[ShareCard Optimizer] 清理了 ${toDelete} 个旧缓存`)
  }

  /**
   * 清空所有缓存
   */
  clearCache() {
    imageCache.clear()
    loadingPromises.clear()
    console.log(`[ShareCard Optimizer] 已清空所有缓存`)
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      cacheSize: imageCache.size,
      loadingCount: loadingPromises.size,
      maxCacheSize: this.maxCacheSize
    }
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 导出单例
export default new ShareCardOptimizer()
